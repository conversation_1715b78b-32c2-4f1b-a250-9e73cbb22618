# ----------------------------------------
# WARNING - this is not a script. 
#		Run the commands one by one.
# ----------------------------------------

# ----------------------------------------
# CA private key, certificate and truststore
# ----------------------------------------
## generate CA private key to sign certificates with
openssl genrsa -aes256 -out caPrivate.key 2048
## generate CA certificate
openssl req -x509 -new -nodes -key caPrivate.key -sha256 -days 1024 -out CA.crt
## add to truststore
keytool -import -file CA.crt -alias caCert -keystore truststore.jks

# ----------------------------------------
# client private key, certificate and keystore -> in this example for OPI
# ----------------------------------------
## generate client private key
openssl genrsa -aes256 -out opi_private.key 2048
## create a CSR
openssl req -new -key opi_private.key -out opi.csr
## create a certificate from the CSR
openssl x509 -req -in opi.csr -CA CA.crt -CAkey caPrivate.key -CAcreateserial -out opi.crt -days 365 -sha256
## add certificate to a keystore
openssl pkcs12 -export -in opi.crt -inkey opi_private.key -certfile opi.crt -out opi_keystore.p12

# ----------------------------------------
# SIMILAR - client private key, certificate and keystore for reporting service, if you want different certificates
# ----------------------------------------
## generate client private key
openssl genrsa -aes256 -out report_private.key 2048
## create a CSR
openssl req -new -key report_private.key -out report.csr
## create a certificate from the CSR
openssl x509 -req -in report.csr -CA CA.crt -CAkey caPrivate.key -CAcreateserial -out report.crt -days 365 -sha256
## add certificate to a keystore
openssl pkcs12 -export -in report.crt -inkey report_private.key -certfile report.crt -out report_keystore.p12
