"db_access", "db.access", "1", "DB access"
"isattacker", "isattacker", "0", "Is attacker"
"updater_registered", "updater.registered", "0", "Updater is registered"
"batcher_enabled", "batcher.enabled", "1", "Batcher is enabled"
"queuehandler_enabled", "queuehandler.enabled", "1", "Queue handler is enabled"
"pollthread_enabled", "pollThread.enabled", "1", "Pollthread is enabled"
"scheduler_enabled", "scheduler.enabled", "1", "Scheduler is enabled"
"reporter_enabled", "reporter.enabled", "1", "Reporter is enabled"
"outscan_enabled", "outscan.enabled", "1", "Outscan is enabled"
"networkmonitor_enabled", "networkmonitor.enabled", "1", "Network monitor is enabled"
"tasks_enabled", "tasks.enabled", "0", "Tasks are enabled"
"agent_update_enabled", "agent.update.enabled", "0", "Agent updates are enabled"
"ldap_enabled", "ldap.enabled", "0", "LDAP is enabled"
"ldap_user_rolesenabled", "ldap.user.rolesenabled", "0", "LDAP user role is enabled"
"ldap_ssl", "ldap.ssl", "1", "LDAP ssl enabled"
"ldap2_ssl", "ldap2.ssl", "1", "LDAP2 ssl is enabled"
"hiab_enabled", "hiab.enabled", "1", "HIAB is enabled"
"hiab_syslog_audit", "hiab.syslog.audit", "0", "HIAB syslog audit"
"hiab_arcsight", "hiab.arcsight", "0", "HIAB arcsight"
"hiab_tls", "hiab.tls", "0", "HIAB tls"
"hiab_allowexceptions", "hiab.allowexceptions", "1", "HIAB allows exceptions"
"hiab_backup_include_settings", "hiab.backup.include.settings", "0", "HIAB backup include settings"
"hiab_backup_ftps", "hiab.backup.ftps", "0", "HIAB backup ftps"
"hiab_backup_ftp_pasv", "hiab.backup.ftp_pasv", "1", "HIAB backup ftp passive mode"
"hiab_backup_implicit", "hiab.backup.implicit", "0", "HIAB backup is implicit"
"hiab_backup_nfs_lock", "hiab.backup.nfs_lock", "0", "HIAB backup nfs lock"
"hiab_report_ftp_pasv", "hiab.report.ftp_pasv", "1", "HIAB report ftp is passive"
"hiab_report_nfs_lock", "hiab.report.nfs_lock", "0", "HIAB report nfs lock"
"hiab_dropattacks", "hiab.dropattacks", "0", "HIAB drop attacks"
"hiab_fromincludeip", "hiab.fromincludeip", "1", "Include form ip"
"hiab_enableplanning", "hiab.enableplanning", "0", "Enable planning for HIAB"
"hiab_planningscanhistory", "hiab.planningscanhistory", "0", "Planning scan history"
"hiab_schedulingtomanagedreports", "hiab.schedulingtomanagedreports", "0", "Scheduling to managed reports"
"awsscanner_mode", "awsscanner.mode", "0", "AWS scanner mode"
"aws_scanning", "aws.scanning", "1", "AWS scanning"
"batcher_run_atstartup", "batcher.run.atstartup", "0", "Batcher runs at startup"
"enable_translation", "enable.translation", "0", "Enable translation"
"allow_ruleengine", "allow.ruleengine", "0", "Allows rule engine"
"sls_nouptodatecheck", "sls.nouptodatecheck", "0", "Scanless scan no update to check"
"oos_enabled", "oos.enabled", "0", "Oos is enabled"
"oos_disable_compliance", "oos.disable.compliance", "0", "Oos is disabled for compliance"
"oos_disable_hiab", "oos.disable.hiab", "0", "Oos is disabled for HIAB"
"oos_disable_was", "oos.disable.was", "0", "Oos is disabled for was"
"oos_disable_pci", "oos.disable.pci", "0", "Oos is disabled for PCI"
"oos_disable_poc", "oos.disable.poc", "0", "Oos is disabled for POC"
"oos_disable_services", "oos.disable.services", "0", "Oos is disabled for services"
"oos_disable_sms", "oos.disable.sms", "0", "To disable sms"
"queuehandler_allowmail", "queuehandler.allowmail", "0", "Queue handler allows mail"
"queuehandler_allowsms", "queuehandler.allowsms", "0", "Queue handler allows SMS"
"queuehandler_allowlogging", "queuehandler.allowlogging", "0", "Queue handler allows logging"
"queuehandler_allowsyslog", "queuehandler.allowsyslog", "0", "Queue handler allows syslog"
"queuehandler_allowsplunk", "queuehandler.allowsplunk", "0", "Queue handler allows splunk"
"billing_daily", "billing.daily", "0", "Daily billing"
"billing_enabled", "billing.enabled", "0", "Billing is enabled"
"report_save_xml", "report.save.xml", "0", "Save report as XML"
"report_showall", "report.showall", "0", "Show all in report"
"disablelicense", "disablelicense", "0", "disable license"
"filter_domain", "filter.domain", "0", "Filter domain"
"dev_mode", "dev.mode", "0", "Developer mode"
"dev_compliance", "dev.compliance", "0", "Developer mode compliance"
"dev_swat", "dev.swat", "0", "Developer mode swat"
"demo_machine", "demo.machine", "0", "Demo machine"
"scanless_checkruledate", "scanless.checkruledate", "1", "Scanless check rule date"
"autorules_enable", "autorules.enable", "0", "Autorules enabled"
"allow_subuser_logon", "allow.subuser.logon", "0", "Allows subusers to login"
"dev_sysout_sms", "dev.sysout.sms", "0", "Sysout SMS for developer"
"notifyupdatedrules", "notifyupdatedrules", "0", "Notify when rules are updated"
"admin_mode", "admin.mode", "0", "Admin mode"
"fetch_exploits", "fetch.exploits", "0", "Fetch exploits"
"fetch_prefset", "fetch.prefset", "0", "Fetch prefset task"
"pci_dss_qa_enabled", "pci.dss.qa.enabled", "0", "PCI dss qa is enabled"
"vulndb_update", "vulndb.update", "0", "Vulnerability DB update"
"request_debug", "request.debug", "0", "Request debug"
"no_db_migration", "no.db.migration", "0", "No DB migration"
"flyway_developer_migrations", "flyway.developer.migrations", "0", "Flyway Developer migrations"
"update_crl_list", "update.crllist", "0", "update crl list"
"updated_whitelabel_lastupdate", "whitelabel.updated.lastboot", "0", "Whitelable update last boot"
"force_payperuse", "force.payperuse", "0", "Force pay per use"
"test_mode", "test.mode", "0", "Test mode"
"fetchRulesFromOutscan", "fetchrules", "0", "Fetch rules form outscan"
"disableConfigurationReload", "disable.configuration.reload", "1", "Disable reload for configuration"
"require2faadmin", "require.2fa.admin", "1", "Require 2 factor authentication for admin"
"event_micro_service", "event.micro.service", "0", "Use event microservice"
"report_micro_service", "report.micro.service", "0", "Use report microservice"
"save_configuration", "save.configuration", "1", ""
"ignore_hiab_image_version", "ignore.hiab.image.version", "0", "Whether to ignore checking the image version when a HIAB enrolls or not"
"pin_trust", "pin.trust", "1", "Whether to pin trust or ignore trust"
