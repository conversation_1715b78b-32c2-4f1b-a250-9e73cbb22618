{"events": [{"event": "0,1,2,4,50", "hmName": "scriptname", "uiName": "_name", "syslogName": "Script Name", "timeStamp": "0", "arcsightName": "cs1", "arcsightLabel": "cs1Label", "id": "1", "example": "Apache HTTP Request Unexpected Behavior Vulnerability", "cim": "signature"}, {"event": "0,1,2,4,50", "hmName": "scriptid", "uiName": "_scriptId", "syslogName": "Script Id", "timeStamp": "0", "arcsightName": "msg", "id": "2", "example": "1000010", "cim": "signature_id"}, {"event": "0,1,2,4,50", "hmName": "host", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": "4", "example": "*************", "cim": "dest_ip"}, {"event": "0,1,2,4,50", "hmName": "hostname", "uiName": "_hostName", "syslogName": "Hostname", "timeStamp": "0", "arcsightName": "dvchost", "id": "131072", "example": "localhost", "cim": "dest_name"}, {"event": "0,1,2,4,50", "hmName": "IPORT", "uiName": "_port", "syslogName": "Port", "timeStamp": "0", "arcsightName": "spt", "id": "8192", "example": "80"}, {"event": "0,1,2,4,50", "hmName": "VCBUG", "uiName": "bugTraq", "syslogName": "BugTraq", "timeStamp": "0", "arcsightName": "cs4", "arcsightLabel": "cs4Label", "id": "16", "example": "3796", "cim": "bugtraq"}, {"event": "0,1,2,4,50", "hmName": "CVSSSCORE", "uiName": "_cvss", "syslogName": "CVSS", "timeStamp": "0", "arcsightName": "cfp1", "arcsightLabel": "cfp1Label", "id": "32", "example": "5.0", "cim": "cvss"}, {"event": "0,1,2,4,50", "hmName": "BNEW", "uiName": "_new", "syslogName": "New", "timeStamp": "0", "arcsightName": "msg", "id": "64", "example": "1"}, {"event": "0,1,2,4,50", "hmName": "VCCVE", "uiName": "CVE", "syslogName": "CVE", "timeStamp": "0", "arcsightName": "cs2", "arcsightLabel": "cs2Label", "id": "128", "example": "CVE-2002-2012", "cim": "cve"}, {"event": "0,1,2,4,50", "hmName": "VCFAMILY", "uiName": "_family", "syslogName": "Family", "timeStamp": "0", "arcsightName": "msg", "id": "256", "example": "apache", "cim": "category"}, {"event": "0,1,2,4,50", "hmName": "VCVHOST", "uiName": "_virtualHost", "syslogName": "Virtual Host", "timeStamp": "0", "arcsightName": "msg", "id": "512", "example": "localhost"}, {"event": "0,1,2,4", "hmName": "ACCEPTED", "uiName": "_accepted", "syslogName": "Accepted", "timeStamp": "0", "arcsightName": "msg", "id": "16384", "example": "1"}, {"event": "0,1,2,4", "hmName": "ACCEPTDATE", "uiName": "_acceptDate", "syslogName": "Accept Date", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "msg", "id": "8", "dep": "ACCEPTED", "example": "2012-06-14 10:00"}, {"event": "0,1,2,4", "hmName": "ACCEPTCOMMENT", "uiName": "_acceptComment", "syslogName": "Accepted Comment", "timeStamp": "0", "arcsightName": "msg", "id": "1024", "dep": "ACCEPTED", "example": "Comment"}, {"event": "0,1,2,4,50", "hmName": "DFIRSTSEEN", "uiName": "_firstSeen", "syslogName": "First Seen", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "deviceCustomDate1", "arcsightLabel": "deviceCustomDate1Label", "id": "32768", "example": "2012-04-03 10:12"}, {"event": "0,1,2,4,50", "hmName": "DLASTSEEN", "uiName": "_lastSeen", "syslogName": "Last Seen", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "deviceCustomDate2", "arcsightLabel": "deviceCustomDate2Label", "id": "2048", "example": "2012-06-13 08:12"}, {"event": "0,1,2,4,50", "hmName": "PRODUCT", "uiName": "_product", "syslogName": "Product", "timeStamp": "0", "arcsightName": "msg", "id": "4096", "example": "apache", "cim": "vendor_product"}, {"event": "0,1,2,4,50", "hmName": "HASEXPLOITS", "uiName": "_hasExploits", "syslogName": "Has Exploits", "timeStamp": "0", "arcsightName": "msg", "id": "65536", "example": "yes"}, {"event": "0,1,2,4,50", "hmName": "SCANNERNAME", "uiName": "_scannerName", "syslogName": "Scanner", "timeStamp": "0", "arcsightName": "msg", "id": "262144", "hiabOnly": "1", "example": "Local"}, {"event": "5,6", "hmName": "SCANNERNAME", "uiName": "_scannerName", "syslogName": "Scanner", "timeStamp": "0", "arcsightName": "msg", "id": "1", "hiabOnly": "1", "example": "Local"}, {"event": "5,6", "hmName": "VIRTUALHOSTS", "uiName": "_virtualHostNames", "syslogName": "VirtualHosts", "timeStamp": "0", "arcsightName": "msg", "id": "2", "example": "localhost"}, {"event": "0,1,2,4,50", "hmName": "PLATFORM", "uiName": "_platform", "syslogName": "Platform", "timeStamp": "0", "arcsightName": "cs3", "arcsightLabel": "cs3Label", "id": "524288", "example": "Linux"}, {"event": "0,1,2,4,50", "hmName": "REPORTDATE", "uiName": "_reportDate", "syslogName": "Report date", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "rt", "id": 1048576, "example": "2012-06-11 12:32"}, {"event": "5,6", "hmName": "PLATFORM", "uiName": "_platform", "syslogName": "Platform", "timeStamp": "0", "arcsightName": "msg", "id": "4", "example": "Linux"}, {"event": "5,6", "hmName": "IPADDRESS", "uiName": "_ipAddress", "syslogName": "IP", "timeStamp": "0", "arcsightName": "dvc", "id": "8", "example": "***********"}, {"event": "5,6", "hmName": "HOSTNAME", "uiName": "_hostName", "syslogName": "Hostname", "timeStamp": "0", "arcsightName": "shost", "id": "16", "example": "localhost"}, {"event": "5,6", "hmName": "MACADDRESS", "uiName": "_<PERSON><PERSON><PERSON><PERSON>", "syslogName": "<PERSON>", "timeStamp": "0", "arcsightName": "smac", "id": "32", "example": "00:11:22:33:44:55"}, {"event": "5,6", "hmName": "NETBIOS", "uiName": "_netbios", "syslogName": "Netbios", "timeStamp": "0", "arcsightName": "msg", "id": "64", "hiabOnly": "1", "example": "\\netbios"}, {"event": "5,6", "hmName": "portcount", "uiName": "_ports", "syslogName": "Ports", "timeStamp": "0", "arcsightName": "msg", "id": "128", "example": "3"}, {"event": "5", "hmName": "LOWCOUNT", "uiName": "_lowRisks", "syslogName": "Low risks", "timeStamp": "0", "arcsightName": "msg", "id": "256", "example": "10"}, {"event": "5", "hmName": "MEDIUMCOUNT", "uiName": "_mediumRisks", "syslogName": "Medium risks", "timeStamp": "0", "arcsightName": "msg", "id": "512", "example": "5"}, {"event": "5", "hmName": "HIGHCOUNT", "uiName": "_highRisks", "syslogName": "High risks", "timeStamp": "0", "arcsightName": "msg", "id": "1024", "example": "7"}, {"event": "7,8,9,10,35,51", "hmName": "host", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": "1", "example": "***********"}, {"event": "7,8,9,10,35", "hmName": "template", "uiName": "_policy", "syslogName": "Policy", "timeStamp": "0", "arcsightName": "msg", "id": "2", "example": "Normal"}, {"event": "7,8,9,10,35,51", "hmName": "starttime", "uiName": "_startTime", "syslogName": "Start Time", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "start", "id": "4", "example": "2012-06-12 12:00"}, {"event": "8,9,10,35", "hmName": "endtime", "uiName": "_endTime", "syslogName": "End Time", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "end", "id": "8", "example": "2012-06-12 12:43"}, {"event": "7,8,9,10,35", "hmName": "SCANJOB", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": "16", "example": "Web servers"}, {"event": "7,8,9,10", "hmName": "_IP", "uiName": "_ipAddress", "syslogName": "IP Address", "timeStamp": "0", "arcsightName": "dvc", "id": "32", "example": "***********"}, {"event": "7,8,9,10", "hmName": "_HOSTNAME", "uiName": "_hostName", "syslogName": "Hostname", "timeStamp": "0", "arcsightName": "shost", "id": "64", "example": "localhost"}, {"event": "7,8,9,10", "hmName": "_NETBIOS", "uiName": "_netbios", "syslogName": "Netbios", "timeStamp": "0", "arcsightName": "msg", "id": "128", "example": "\\netbios"}, {"event": 11, "hmName": "host", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 11, "hmName": "PORTLIST", "uiName": "_ports", "syslogName": "Ports", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "22,80"}, {"event": 12, "hmName": "host", "uiName": "_targets", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 12, "hmName": "PORTLIST", "uiName": "_ports", "syslogName": "Ports", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "22,80"}, {"event": 13, "hmName": "HOSTLIST", "uiName": "_targets", "syslogName": "Targets", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 14, "hmName": "HOSTLIST", "uiName": "_targets", "syslogName": "Targets", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 17, "hmName": "BACKCHANNELSTATUS", "uiName": "_status", "syslogName": "Status", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "OK"}, {"event": "20,21,22,23", "hmName": "JOBNAME", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "Web servers"}, {"event": 20, "hmName": "ALIVE_LIST_SIZE", "uiName": "_activeCount", "syslogName": "Active", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "3"}, {"event": 20, "hmName": "DEAD_LIST_SIZE", "uiName": "_inactiveCount", "syslogName": "Inactive", "timeStamp": "0", "arcsightName": "msg", "id": 4, "example": "2"}, {"event": 20, "hmName": "ADDED_LIST_SIZE", "uiName": "_addedCount", "syslogName": "Added", "timeStamp": "0", "arcsightName": "msg", "id": 8, "example": "1"}, {"event": 21, "hmName": "ALIVE_LIST", "uiName": "_listOfActive", "syslogName": "Active", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "***********,***********,***********"}, {"event": "22,48", "hmName": "DEAD_LIST", "uiName": "_listOfInactive", "syslogName": "Inactive", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "***********,***********"}, {"event": 23, "hmName": "ADDED_LIST", "uiName": "_listOfAdded", "syslogName": "Added", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "***********"}, {"event": 24, "hmName": "HOSTS", "uiName": "_listOfAdded", "syslogName": "Added", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 25, "hmName": "HOSTS", "uiName": "_listOfRemoved", "syslogName": "Removed", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": 26, "hmName": "SCANSTART", "uiName": "_scanStart", "syslogName": "<PERSON><PERSON>", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "start", "id": 1, "example": "2012-06-13 12:32"}, {"event": 26, "hmName": "SCANEND", "uiName": "_scanEnd", "syslogName": "<PERSON><PERSON>", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "end", "id": 2, "example": "2012-06-13 13:21"}, {"event": 26, "hmName": "JOBNAME", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 4, "example": "Web servers"}, {"event": 26, "hmName": "TEMPLATE", "uiName": "_policy", "syslogName": "Policy", "timeStamp": "0", "arcsightName": "msg", "id": 8, "example": "Normal"}, {"event": 30, "hmName": "LOGINUSERNAME", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "USER"}, {"event": 30, "hmName": "LOGINTIME", "uiName": "_time", "syslogName": "Time", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "msg", "id": 2, "example": "2012-06-13 12:00"}, {"event": 30, "hmName": "ORIGIN", "uiName": "_ipAddress", "syslogName": "IP Address", "timeStamp": "0", "arcsightName": "dvc", "id": 4, "example": "***********"}, {"event": 31, "hmName": "SCANNERNAME", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "Local"}, {"event": 31, "hmName": "SCANNERIP", "uiName": "_ipAddress", "syslogName": "IP Address", "timeStamp": "0", "arcsightName": "dvc", "id": 2, "example": "***********"}, {"event": 31, "hmName": "LASTCONNECTION", "uiName": "_lastConnect", "syslogName": "Last sync", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "msg", "id": 4, "example": "2012-06-03 04:12"}, {"event": 32, "hmName": "status", "uiName": "_status", "syslogName": "Status", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "OK"}, {"event": "34,45,46,47,49,53", "hmName": "SCRIPTNAME", "uiName": "_scriptName", "syslogName": "Script name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "Apache HTTP Request Unexpected Behavior Vulnerability"}, {"event": 34, "hmName": "PORT", "uiName": "_port", "syslogName": "Port", "timeStamp": "0", "arcsightName": "spt", "id": 2, "example": "80"}, {"event": 34, "hmName": "RESULT", "uiName": "_result", "syslogName": "Result", "timeStamp": "0", "arcsightName": "msg", "id": 4, "example": "Still present"}, {"event": "34,45,46,49,53", "hmName": "REPORTHOST", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": 8, "example": "***********"}, {"event": 34, "hmName": "REPORTDATE", "uiName": "_reportDate", "syslogName": "Report date", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "rt", "id": 16, "example": "2012-06-11 12:32"}, {"event": 36, "hmName": "JOBNAME", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "Web servers"}, {"event": 36, "hmName": "REPORTHOST", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": 2, "example": "***********"}, {"event": 36, "hmName": "ADDED", "uiName": "_addedVulnerabilities", "syslogName": "Added vulnerabilities", "timeStamp": "0", "arcsightName": "msg", "id": 4, "example": "Apache HTTP Request Unexpected Behavior Vulnerability"}, {"event": 38, "hmName": "VERSION", "uiName": "_version", "syslogName": "Version", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "G4_1_120"}, {"event": 38, "hmName": "DATE", "uiName": "_date", "syslogName": "Date", "timeStamp": "yyyy-MM-dd", "arcsightName": "msg", "id": 2, "example": "2012-09-11"}, {"event": "39,40,52", "hmName": "SCHEDULEJOB", "uiName": "_name", "syslogName": "Name", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "Web servers"}, {"event": "52", "hmName": "starttime", "uiName": "_startTime", "syslogName": "Start Time", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "start", "id": "2", "example": "2012-06-12 12:00"}, {"event": 42, "hmName": "FAILED", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "*********** SSH: username"}, {"event": "43,44,57,58", "hmName": "REPORTHOST", "uiName": "_target", "syslogName": "Target", "timeStamp": "0", "arcsightName": "dvc", "id": 1, "example": "***********"}, {"event": "43,44", "hmName": "PORTLIST", "uiName": "_ports", "syslogName": "Ports", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "80 tcp/http, 443 tcp/http"}, {"event": "45,46,47,49,53", "hmName": "SCRIPTID", "uiName": "_scriptId", "syslogName": "Script Id", "timeStamp": "0", "arcsightName": "msg", "id": "2", "example": "1000010"}, {"event": "45,46,47", "hmName": "COMMENT", "uiName": "_comment", "syslogName": "Comment", "timeStamp": "0", "arcsightName": "msg", "id": "4", "example": "Comment"}, {"event": "46", "hmName": "COMMENTNAME", "uiName": "_commented<PERSON>y", "syslogName": "Commented By", "timeStamp": "0", "arcsightName": "msg", "id": "16", "example": "USER"}, {"event": "45,49", "hmName": "ACCEPTEDBY", "uiName": "_acceptedBy", "syslogName": "Accepted By", "timeStamp": "0", "arcsightName": "msg", "id": "16", "example": "USER"}, {"event": "45,49", "hmName": "ACCEPTDATE", "uiName": "_acceptDate", "syslogName": "Accept Date", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "msg", "id": "32", "example": "2012-06-03 04:12"}, {"event": "45,49", "hmName": "ACCEPTEDDAYS", "uiName": "_acceptLength", "syslogName": "Accepted for number of days", "timeStamp": "0", "arcsightName": "msg", "id": "64", "example": "30"}, {"event": "49", "hmName": "ACCEPTEEXPIRES", "uiName": "_acceptanceExpires", "syslogName": "Acceptance Expires on", "timeStamp": "yyyy-MM-dd HH:mm", "arcsightName": "msg", "id": "4", "example": "2012-06-03 04:12"}, {"event": "57,58", "hmName": "POLICYLIST", "uiName": "_policies", "syslogName": "Policies", "timeStamp": "0", "arcsightName": "msg", "id": 2, "example": "80 tcp/http, 443 tcp/http"}, {"event": 59, "hmName": "percent", "uiName": "_percent", "syslogName": "Percent", "timeStamp": "0", "arcsightName": "msg", "id": 1, "example": "90"}], "fixes": [{"event": 0, "pre": "Risk:", "post": " - Information"}, {"event": 1, "pre": "Risk:", "post": " - Low"}, {"event": 2, "pre": "Risk:", "post": " - Medium"}, {"event": 4, "pre": "Risk:", "post": " - High"}, {"event": 5, "pre": "Report:", "post": " - Ready"}, {"event": 6, "pre": "Report:", "post": " - Large"}, {"event": 7, "pre": "Scan:", "post": " - Start"}, {"event": 8, "pre": "Scan:", "post": " - Timeout"}, {"event": 9, "pre": "Scan:", "post": " - Stopped"}, {"event": 10, "pre": "Scan:", "post": " - Error"}, {"event": 15, "pre": "Event:", "post": " - Update Done"}, {"event": 16, "pre": "Event:", "post": " - System Rebooted"}, {"event": 17, "pre": "Event:", "post": " - Remote Support"}, {"event": 18, "pre": "Event:", "post": " - Backup <PERSON>"}, {"event": 19, "pre": "Event:", "post": " - System Restart"}, {"event": 20, "pre": "Discovery:", "post": " - Discovery Status Notification"}, {"event": 21, "pre": "Discovery:", "post": " - Discovery Alive Target"}, {"event": 22, "pre": "Discovery:", "post": " - Discovery Dead Target"}, {"event": 23, "pre": "Discovery:", "post": " - Discovery Added Target"}, {"event": 24, "pre": "Target:", "post": " - Added"}, {"event": 25, "pre": "Target:", "post": " - Removed"}, {"event": 26, "pre": "Scan Status:", "post": ""}, {"event": 30, "pre": "User Login:", "post": ""}, {"event": 31, "pre": "Scanner Missing:", "post": ""}, {"event": 32, "pre": "Maintenance:", "post": ""}, {"event": 33, "pre": "Event:", "post": " - Update Failed"}, {"event": 34, "pre": "Verify:", "post": ""}, {"event": 35, "pre": "Scan:", "post": " - Not Reachable"}, {"event": 36, "pre": "Scan:", "post": " - Updated"}, {"event": 37, "pre": "Event:", "post": " - Backup Failed"}, {"event": 38, "pre": "Release Notes:", "post": ""}, {"event": 39, "pre": "Scan:", "post": ""}, {"event": 40, "pre": "Scan:", "post": " - Started"}, {"event": 42, "pre": "Target:", "post": " - Authentication Failed"}, {"event": 43, "pre": "Ports:", "post": " - Opened"}, {"event": 44, "pre": "Ports:", "post": " - Closed"}, {"event": 45, "pre": "Risk:", "post": " - Accepted"}, {"event": 46, "pre": "Risk:", "post": " - Comment Added"}, {"event": 47, "pre": "Risk:", "post": " - Discussion Updated"}, {"event": 48, "pre": "Discovery:", "post": " - Dead Target in Consecutive Discovery Scans"}, {"event": 49, "pre": "Risk:", "post": " - Acceptance Expiring"}, {"event": 50, "pre": "Risk:", "post": " - Exploit Available"}, {"event": 51, "pre": "Target:", "post": " - Scan Scheduled"}, {"event": 52, "pre": "Scan:", "post": " - Scheduled"}, {"event": 53, "pre": "Risk:", "post": " - Acceptance Expired"}, {"event": 57, "pre": "Compliant Target:", "post": ""}, {"event": 58, "pre": "Not Compliant Target:", "post": ""}, {"event": 59, "pre": "Disk Usage:", "post": ""}], "dblogEvents": [{"event": 26, "uiName": "_information", "dblogName": "Information", "id": "128"}, {"event": 26, "uiName": "_lowRisks", "dblogName": "Low risks", "id": "256"}, {"event": 26, "uiName": "_mediumRisks", "dblogName": "Medium risks", "id": "512"}, {"event": 26, "uiName": "_highRisks", "dblogName": "High risks", "id": "1024"}]}