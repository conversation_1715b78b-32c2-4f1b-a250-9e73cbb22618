#!/bin/bash

mkdir -p /var/log/opi /var/cache/opi /var/cache/opi/settings /var/cache/opi/xml /var/cache/opi/reports /var/cache/opi/reporting /var/updater-cache /var/cache/opi/updates /var/cache/opi/offlineupdate /var/cache/opi/offlineupdate/repository /var/updater-cache /etc/pki/tls/certs/opi /etc/pki/tls/private/opi /var/cache/wasx /var/cache/opi/yumrepo /var/cache/opi/gpgtmp /var/cache/scans /var/cache/opi/hiab /var/cache/opi/blueprints /var/cache/cloud-discovery
chown tomcat:tomcat /var/log/opi /var/cache/opi /var/cache/opi/settings /var/cache/opi/xml /var/cache/opi/reports /var/cache/opi/reporting /var/updater-cache /var/cache/opi/updates /var/cache/opi/offlineupdate /var/cache/opi/offlineupdate/repository /var/updater-cache /etc/pki/tls/certs/opi /etc/pki/tls/private/opi /var/cache/wasx /var/cache/opi/gpgtmp /var/cache/scans /var/cache/opi/hiab /var/cache/opi/blueprints /var/cache/cloud-discovery
setfacl -d -m group:tomcat:rwx /var/log/opi

if grep -q 'hiab.enabled[[:blank:]]*=[[:blank:]]*1' /etc/server.properties; then
	chown tomcat:hiabconfig /var/updater-cache
	chmod 775 /var/updater-cache

	# Remove AXIS user if exists
	if grep -q "axis" /etc/passwd; then
		/usr/sbin/userdel -r axis
	fi

	if grep -q "axis" /etc/group; then
		/usr/sbin/groupdel axis
	fi
fi

# Update the Nginx configuration on install but not update
if [ "$1" = "1" ] || ! grep -q '$remote_addr' /etc/nginx/nginx.conf; then
cat << 'EOF' > /etc/nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
	worker_connections 1024;
}

http {
		log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
											'$status $body_bytes_sent "$http_referer" '
											'"$http_user_agent" "$http_x_forwarded_for"';

		access_log  /var/log/nginx/access.log  main;

		sendfile            on;
		tcp_nopush          on;
		tcp_nodelay         on;
		keepalive_timeout   65;
		types_hash_max_size 2048;

		include             /etc/nginx/mime.types;
		default_type        application/octet-stream;

		# Load modular configuration files from the /etc/nginx/conf.d directory.
		# See http://nginx.org/en/docs/ngx_core_module.html#include
		# for more information.
		include /etc/nginx/conf.d/*.conf;
}
EOF
fi

mkdir -p /var/lib/tomcats/opi/conf/Catalina/localhost /var/lib/tomcats/opi/work/Catalina/localhost/opi
chown tomcat:tomcat /var/lib/tomcats/opi/conf/Catalina/localhost /var/lib/tomcats/opi/work/Catalina/localhost/opi
setsebool -P httpd_enable_ftp_server 1

systemctl enable nginx
systemctl enable opi
rm -f /var/lib/tomcats/opi/logs
mkdir -p /var/log/opi
chown tomcat:tomcat /var/log/opi
ln -s /var/log/opi/ /var/lib/tomcats/opi/logs

if grep -q 'hiab.enabled[[:blank:]]*=[[:blank:]]*1' /etc/server.properties; then
	usermod -a -G systemd-journal tomcat
	usermod -a -G systemd-journal support
fi
setfacl -Rnm g:systemd-journal:rx,d:g:systemd-journal:rx /var/log/journal/
if [ -d /run/log/journal ]; then
	setfacl -Rnm g:systemd-journal:rx,d:g:systemd-journal:rx /run/log/journal/
fi

mkdir -p /var/run/opi
chown tomcat:tomcat /var/run/opi
if [ ! -f  /etc/pki/tls/certs/opi/hiab.crt ]; then
	ln -s /etc/pki/tls/certs/hiab_orig.crt /etc/pki/tls/certs/opi/hiab.crt
fi
if [ ! -f  /etc/pki/tls/private/opi/hiab.key ]; then
	ln -s /etc/pki/tls/private/hiab_orig.key /etc/pki/tls/private/opi/hiab.key
fi

if [ -f  /etc/nginx/conf.d/appliance.conf ]; then
	mv /etc/nginx/conf.d/appliance.conf /etc/nginx/conf.d/90-appliance.conf
fi

setfacl -m "u:tomcat:rwx" /etc/hosts

mkdir -p /etc/opi
if [[ ! -f  /etc/opi/rootCA.key || $(md5sum /etc/opi/rootCA.key) = "286da2cc04164b6490b69da966b8a629  /etc/opi/rootCA.key" ]]; then
	openssl genrsa -out /etc/opi/rootCA.key 4096 -req -days 3650
fi

/usr/bin/createrepo /var/cache/opi/offlineupdate/repository
restorecon -R /var/cache/opi/offlineupdate/repository/

ln -sf /var/cache/yum /var/cache/opi/yumrepo/

if getent group libellum ; then
	gpasswd -a tomcat libellum
fi

if id -u scanjob ; then
	setfacl -m "u:scanjob:rwx" /var/cache/wasx
	setfacl -d -m "u:scanjob:rwx" /var/cache/wasx
	setfacl -m "u:scanjob:rwx" /var/cache/scans
	setfacl -d -m "u:scanjob:rwx" /var/cache/scans
	setfacl -m "u:scanjob:rx" /var/cache/tomcat
	setfacl -m "u:scanjob:rx" /var/cache/tomcat/temp
fi
setfacl -d -m "u:tomcat:rwx" /var/cache/scans

folders=("/usr/share/opi/resources/messages"  "/usr/share/opi/resources/json"  "/usr/share/opi/resources/email"  "/usr/share/opi/resources/label")
for folder in "${folders[@]}"; do
	if [ -d "$folder" ]; then
		setfacl -Rm "u:tomcat:rx" "$folder"
	fi
done

setfacl -Rm "u:tomcat:rwx" /var/cache/opi/reporting
setfacl -Rd -m "u:tomcat:rwx" /var/cache/opi/reporting

semodule -n -i /usr/share/selinux/packages/o24opi.pp
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/WEB-INF(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/baseschema(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/compliance(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/files(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/guides(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/images(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/migrations(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/repeatablemigrations(/.*)?"
semanage fcontext -a -t tomcat_var_lib_t "/var/lib/tomcats/opi/webapps/opi/META-INF/xml(/.*)?"
semanage fcontext -a -t bin_t "/var/lib/tomcats/opi/webapps/opi/META-INF/scripts(/.*)?"
restorecon -R /var/lib/tomcats/opi/webapps/opi/
semanage fcontext -a -t opi_config_t "/etc/opi(/.*)?"
restorecon -R /etc/opi
setsebool -P nis_enabled 1

if grep -q 'hiab.enabled[[:blank:]]*=[[:blank:]]*1' /etc/server.properties; then
	psql opsspace postgres -c "CREATE EXTENSION IF NOT EXISTS pg_trgm"
	psql opsspace postgres -c "CREATE EXTENSION IF NOT EXISTS btree_gist"
fi