#!/bin/bash

function itoa {
	#returns the dotted-decimal ascii form of an IP arg passed in integer format
	echo -n $(($(($(($((${1}/256))/256))/256))%256)).
	echo -n $(($(($((${1}/256))/256))%256)).
	echo -n $(($((${1}/256))%256)).
	echo $((${1}%256))
}

if [ -z $1 ] || [ -z $2 ] || [ -z $3 ]; then
	echo 'Usage: <user ID> <number of targets> <number of findings per target> [database] [database user]'
	exit 1
fi

userid="$1"
nrOfTargets="$2"
nrOfFindings="$3"
database="opsspace"
user="opsuser"
[ -n "$4" ] && database="$4"
[ -n "$5" ] && user="$5"

ipstart="$(itoa 167772160)"
ipend="$(itoa $((167772160+$nrOfTargets)))"

psql "$database" "${user}" -c "DELETE FROM tuserdatas WHERE ipaddress >= '$ipstart' AND ipaddress <= '$ipend' AND xuserxid=$userid"
psql "$database" "${user}" -c "DELETE from schedules WHERE userid=$userid AND name='Target creation script'"
scheduleid=$(psql "$database" "${user}" -tqc "INSERT INTO schedules(id, name, userid, maxscantimeminutes, template) VALUES (nextval('schedules_seq'), 'Target creation script', $userid, 3600, 2) RETURNING id")
scanlogid=$(psql "$database" "${user}" -tqc "INSERT INTO tscanlogs(xid, xuserxid, itype, dscanstartdate, dscanenddate, xtemplate, xsoxid) VALUES(nextval('tscanlogs_seq'), $userid, 20, NOW()-'1 day'::INTERVAL, NOW(), 2, $scheduleid) RETURNING xid")

for ((i=0; i<$nrOfTargets; i++));
do
	echo "$i"
	number="$((167772160+i))"
	ip="$(itoa number)"
	xipxid=$(psql "$database" "${user}" -tqc "INSERT INTO tuserdatas(xid, ipaddress, xuserxid) VALUES (nextval('tuserdatas_seq'), '$ip', $userid) RETURNING xid")
	psql "$database" "${user}" -c "INSERT INTO xlinkscheduleobject( xid, itype, vctarget, ipaddress, endipaddress, discoverycount) VALUES ($scheduleid, 1, '$ip', '$ip', '$ip', 1)"
	psql "$database" "${user}" -c "INSERT INTO tscanlogs(xid, xscanjobxid, xipxid, dscanstartdate, dscanenddate, xsoxid, xtemplate, xuserxid, itype, vchost) VALUES (nextval('tscanlogs_seq'), $scanlogid, $xipxid, NOW() - '1 day'::INTERVAL, NOW(), $scheduleid, 2, $userid, 0, '$ip')"
	reportid=$(psql "$database" "${user}" -tqc "INSERT INTO treportentrys(xid, xipxid, xscanlogxid, xscanjobxid, xuserxid, xtemplate, xsoxid, vctarget, ipaddress, benabled, itype) VALUES (nextval('treportentrys_seq'), $xipxid, (SELECT xid FROM tscanlogs ORDER BY xid DESC LIMIT 1), $scanlogid, $userid, 2, $scheduleid, '$ip', '$ip', 1, 0) RETURNING xid")
	psql "$database" "${user}" -c "INSERT INTO treport_vulns(xid, fk_treportentrys_xid, vcvulnid, iport, iprotocol, itype, vcvhost, xipxid) VALUES (nextval('treport_vulns_seq'), $reportid, 101010, 80, 6, 1, '$ip', $xipxid)"
	psql "$database" "${user}" -c "INSERT INTO treport_vulns(xid, fk_treportentrys_xid, vcvulnid, iport, iprotocol, itype, vcvhost, xipxid) VALUES (nextval('treport_vulns_seq'), $reportid, 101010, 443, 6, 1, '$ip', $xipxid)"
	psql "$database" "${user}" -c "INSERT INTO treport_vulns(xid, fk_treportentrys_xid, vcvulnid, iport, iprotocol, itype, vcvhost, xipxid) VALUES (nextval('treport_vulns_seq'), $reportid, 101010, 3128, 6, 1, '$ip', $xipxid)"
	psql "$database" "${user}" -c "INSERT INTO treport_vulns(xid, fk_treportentrys_xid, vcvulnid, iport, iprotocol, itype, irisk, iscvss, vcscvssvector, vcvhost, xipxid) SELECT nextval('treport_vulns_seq'), $reportid, ruleid, (ARRAY[80, 443, 3128])[CEILING(RANDOM()*3)], 6, 3, CASE WHEN cvssscore>=7.0 THEN 4 WHEN cvssscore>=4.0 THEN 2 ELSE 1 END, cvssscore * 10, cvssvector, '$ip' as vcvhost, $xipxid FROM trules WHERE NOT isscript AND NOT deleted ORDER BY RANDOM() LIMIT $nrOfFindings + (select floor(random() * 10 - 5))"
done
psql "$database" "${user}" -c "UPDATE targetscandata t SET lastreportid=(SELECT MAX(xid) FROM treportentrys WHERE xipxid=u.xid) FROM tuserdatas u WHERE t.targetid = u.xid AND u.ipaddress >= '$ipstart' AND u.ipaddress <= '$ipend' AND u.xuserxid=$userid"
