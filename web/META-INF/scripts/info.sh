#!/bin/sh

export LD_LIBRARY_PATH=/usr/local/lib:/usr/local/ssl/lib:/usr/lib
export USER=root
export LANG=en_US
export LOGNAME=root
export SHLVL=1
export HUSHLOGIN=FALSE
export JADE_HOME=/usr/local/
export SHELL=/bin/bash
export TERM=xterm
export HOME=/root
export PATH=/sbin:/bin:/usr/sbin:/usr/bin:/usr/local/bin


cd /tmp
echo -e "--[ Uptime ]-----------------------------------------------------------------------\n" >summary_info.data
uptime >>summary_info.data
echo -e "\n--[ Disk ]-------------------------------------------------------------------------\n" >>summary_info.data
df -h >> summary_info.data
echo -e "\n--[ Memory ]-----------------------------------------------------------------------\n" >>summary_info.data
free -m >>summary_info.data
echo -e "\n--[ Network ]----------------------------------------------------------------------\n" >>summary_info.data
ifconfig eth0|grep net >>summary_info.data
ifconfig eth1|grep net >>summary_info.data
echo -e "\n" >>summary_info.data
netstat -r -n >>summary_info.data
echo -e "\n" >>summary_info.data
netstat -i -n >>summary_info.data
echo -e "\n--[ kernel version ]---------------------------------------------------------------\n" >>summary_info.data
uname -r >>summary_info.data

