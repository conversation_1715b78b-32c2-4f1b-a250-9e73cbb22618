if [[ $1 == "on" ]] ; then
	sudo systemctl stop nginx
	sudo systemctl start nginx-maintenance
else
	/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/scheduleronly.sh
	sudo systemctl stop nginx-maintenance
	sudo systemctl start nginx
	sudo systemctl restart opi
	if systemctl is-active --quiet o24reportservice; then
	    sudo systemctl restart o24reportservice
    fi
	if systemctl is-active --quiet o24eventservice; then
	    sudo systemctl restart o24eventservice
    fi
	if systemctl is-active --quiet kafka; then
	    sudo systemctl restart kafka
    fi
	if systemctl is-active --quiet filebeat; then
		sudo systemctl restart filebeat;
	fi
	if systemctl is-active --quiet metricbeat; then
		sudo systemctl restart metricbeat;
	fi
fi
