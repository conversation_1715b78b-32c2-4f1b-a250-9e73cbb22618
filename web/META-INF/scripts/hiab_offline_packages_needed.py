#!/usr/bin/python
import yum, json, sys, os, shutil

# Imports a module if it exists
def importModule(module_name):
    try:
        return __import__(module_name)
    except ImportError:
        return None

tmpstdout = sys.stdout
sys.stdout = sys.stderr
#Initialize yum stuff
yb = yum.YumBase()
yb.setCacheDir(tmpdir='/tmp/', reuse=False, force=True)

for repo in yb.repos.listEnabled():
	repo.disable()

repos = importModule('repos')
if repos != None:
	repos.addRepositories(yb)
else:
	print >> sys.stderr, "No repository file, going with default ones"
	yb.add_enable_repo('offline_repo', baseurls=['file:///var/www/repo/o24-7/updates/x86_64/'])
	yb.add_enable_repo('offline_repo_2018', baseurls=['file:///var/www/repo/2018/updates/x86_64/'])

#read json supplied from hiab
inp = ""
if len(sys.argv) != 2:
	print >> sys.stderr, "usage: hiab_update_offline_needed.py [-|json-file]"
	sys.exit(1)
if sys.argv[1] == "-":
	#read it from stdin
	inp = "".join(sys.stdin.readlines())
elif os.path.exists(sys.argv[1]):
	#read it from file
	f = open(sys.argv[1], "r")
	inp = "".join(f.readlines())
	f.close()
else:
	print >> sys.stderr, "Something died in aisle one, please clean it up!"
	sys.exit(2)

#Parse the json into python variables
try:
	pkgs = [tuple(p) for p in json.loads(inp)]
except:
	print >> sys.stderr, "That wasn't json, get out of here!"
	sys.exit(3)

tmp = pkgs
pkgs = []
packagesforupdate = ['xmlapi-hiab', 'o24core', 'o24dblayer', 'o24reportexport', 'hiab-keys', 'hiab-release', 'xmlapi-hiab-scripts', 'xmlapi-dependencies', 'xmlapi-selinux', 'ui-hiab', 'o24configHiab']
for p in tmp:
    if p[0] in packagesforupdate:
        pkgs.append(p)

#Query the latest packages
newpkgs = [q for q in yb.pkgSack.returnNewestByName(patterns=[p[0] for p in pkgs])]
newpkgs2=[]
for p1 in newpkgs:
	for p2 in pkgs:
		if p1.pkgtup[0] == p2[0]:
			b1 = yb.getPackageObject(p1.pkgtup)
			b2 = yum.packages.PackageObject()
			b2.name = p2[0]
			b2.arch = p2[1]
			b2.epoch = p2[2]
			b2.version = p2[3]
			b2.release = p2[4]
			if b1.verGT(b2):
				newpkgs2.append(b1)
newpkgs = newpkgs2
newpkgs = [p.pkgtup for p in newpkgs]
packageobjects = [yb.getPackageObject(p) for p in list(set(newpkgs)-set(pkgs))]
for p in packageobjects:
    if "file://" in p.remote_url:
        shutil.copy(p.remote_url.replace("file://", ""), yb._conf.cachedir)
    else:
    	p.repo.getPackage(p,
            checkfunc=None,
            text=None,
            cache=False)
sys.stdout = tmpstdout
print yb._conf.cachedir
