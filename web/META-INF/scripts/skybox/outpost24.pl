#!/bin/env perl

#
# Processes Outpost24 export files turning them into Skybox iXML files.
#

use lib "/skybox/lib";
use lib "/skybox/lib/external";

use strict;
use XML::Twig;
use util::Helper;
use intermediate::IntermediateSecurityModel;

my $in = $ARGV[0];
my $out = $ARGV[1];

my %hosts;

my %twig_handlers;
$twig_handlers{'/main/hostlist/host'} = \&host;
$twig_handlers{'/main/detaillist/detail'} = \&vulnerability;
$twig_handlers{'/main/portlist/portlist-host'} = \&services;

my $twig = new XML::Twig(twig_handlers => {%twig_handlers});  
$twig->parsefile($in);

my $inm = new intermediate::IntermediateSecurityModel;
foreach my $host (keys %hosts) {

	debug("Host: " . $host);
	my $host_ref = $inm->AddHost(&escape($host));
	$inm->AddInterface($host_ref, &escape($hosts{$host}{'ip'}));

  	foreach my $service (@{$hosts{$host}{'services'}}) {
  		debug("Service: " . ${$service}{'port'} . " : " . ${$service}{'protocol'} . " : " . ${$service}{'service'});
		my $service_ref = $inm->AddService($host_ref, ${$service}{'service'}, ${$service}{'port'} . "/" . ${$service}{'protocol'});
  	}
  	  	
  	foreach my $vulnerability (@{$hosts{$host}{'vulnerabilities'}})	{
		if(${$vulnerability}{'cve'}) {
			debug("CVE: " . ${$vulnerability}{'cve'});
			$inm->AddVulnerability($host_ref, "CVE", ${$vulnerability}{'cve'}, "");
		}
		else {
			debug("Custom: " . ${$vulnerability}{'name'});
			$inm->AddCustomVulnerability($host_ref, "Outpost24", ${$vulnerability}{'id'}, 3525, &escape(${$vulnerability}{'name'}), "", ${$vulnerability}{'risk'}, &escape(${$vulnerability}{'description'}));
		}
  	}	
}

$inm->SetCreationTime(Helper::getCreationTime());
#$inm->Print();
$inm->Write($out);
exit(0);


sub debug
{
	my($text) = @_;
	#print $text . "\n";
}

sub escape 
{
	my($text) = @_;

	$text =~ s/\n/<br>/g;
	$text =~ s/&/&amp;/g;
	$text =~ s/</&lt;/g;
	$text =~ s/>/&gt;/g;
	$text =~ s/"/&quot;/g;
	$text =~ s/'/&#39;/g;
	
	return $text;
}

sub host {
	my($twig, $node) = @_;

	my $ip = $node->first_child_text('ip');
	my $hostname = $node->first_child_text('name');

	my $host = $ip;
	if($hostname) {
		 $host = $hostname;
	}

	if(!$host) {
		return;
	}	

	if(!$hosts{$host}) {
		$hosts{$host}{'ip'} = $ip;
		$hosts{$host}{'hostname'} = $hostname;
		$hosts{$host}{'vulnerabilities'} = [];
		$hosts{$host}{'services'} = [];
	}
		
	$twig->purge; 
}

sub vulnerability 
{
	my($twig, $node) = @_;

	my $ip = $node->first_child_text('ip');
	my $hostname = $node->first_child_text('hostname');

	my $host = $ip;
	if($hostname) {
		 $host = $hostname;
	}
	
	if(!$hosts{$host}) {
		return;
	}

	my $cve = $node->first_child('cve');
	if($cve) {
		foreach my $id ($cve->children('id')) {
			my %vulnerability;
			$vulnerability{'cve'} = $id->text;
			
			push @{$hosts{$host}{'vulnerabilities'}}, \%vulnerability;
		}	
	}
	
	if(!$cve || $cve->children == 0) {
		my $risk = $node->first_child_text('risk');
		if($risk == 0) {
			$risk = "Info";	
		}
		elsif($risk == 1) {
			$risk = "Low";	
		}
		elsif($risk == 2) {
			$risk = "Medium";	
		}
		elsif($risk == 4) {
			$risk = "High";	
		}
					
		my %vulnerability;
		$vulnerability{'cve'} = "";
		$vulnerability{'id'} = $node->first_child_text('id');
		$vulnerability{'name'} = $node->first_child_text('name');
		$vulnerability{'description'} = &clean($node->first_child_text('description') . "<br>" . $node->first_child_text('information'));
		$vulnerability{'risk'} = $risk;

		push @{$hosts{$host}{'vulnerabilities'}}, \%vulnerability;
	}

	$twig->purge; 
}

sub clean
{
	my($text) = @_;
	
	$text =~ s/<rtab>(.*?)<\/rtab>/$1<br>/ig;
	$text =~ s/<hdr>(.*?)<\/hdr>/---------------------------------------<br>$1<br>---------------------------------------<br>/ig;
	$text =~ s/<row>(.*?)<\/row>/$1<br>/ig;
	$text =~ s/<col>(.*?)<\/col>/[$1]/ig;
	$text =~ s/<(.?)rowset>//ig;
	$text =~ s/<columns>(.*)<\/columns>//ig;

	return $text;
}

sub service
{
	my($host, $node) = @_;
		
	my %service;
	$service{'port'} = $node->first_child_text('portnumber');
	$service{'protocol'} = $node->first_child_text('protocol');
	$service{'service'} = $node->first_child_text('service');

	push @{$hosts{$host}{'services'}}, \%service;	
}

sub services 
{
	my($twig, $node) = @_;
	
	my $ip = $node->first_child_text('ip');
	my $hostname = $node->first_child_text('name');

	my $host = $ip;
	if($hostname) {
		 $host = $hostname;
	}

	if(!$hosts{$host}) {
		return;
	}

	foreach my $service ($node->children('portinfo')) {
		&service($host, $service);
	}

	$twig->purge; 
}
