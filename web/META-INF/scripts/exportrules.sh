#!/bin/bash

filename="$(mktemp)"
placeholder=1970-1-1

BLUE_LIV_DATE=
DBURL=
EXPLOITS_DATE=
ADDITIONAL_FILE=
MIGRATIONS=1
PRODUCTINFO_DATE=
RULE_DATE=
PATCHSUPERSEDENCE_DATE=
SYSTEM=
COMPRESS=0

while getopts "b:l:e:f:m:p:r:t:s:z:" opt; do
	case "$opt" in
		b) BLUE_LIV_DATE="$OPTARG";;
		l) DBURL="$OPTARG";;
		e) EXPLOITS_DATE="$OPTARG";;
		f) ADDITIONAL_FILE="$OPTARG";;
		m) MIGRATIONS="$OPTARG";;
		p) PRODUCTINFO_DATE="$OPTARG";;
		r) RULE_DATE="$OPTARG";;
		t) PATCHSUPERSEDENCE_DATE="$OPTARG" ;;
		s) SYSTEM="$OPTARG" ;;
		z) COMPRESS="$OPTARG" ;;

		\?) exit 1 ;;
		:) exit 1 ;;
	esac
done

if [[ -z "${RULE_DATE}" ]] || [[ -z "${DBURL}" ]]; then
cat << EOF
  Usage:
  -r <Rules update date>
  -l <DB URL>
  -f <Additional sql file path if any>
  -m <0|1 Should include DB migrations for tables related to rules. Default: 1>
  -b <Blueliv update date. Default: 1970-1-1>
  -e <Exploits update date. Default: 1970-1-1>
  -p <ProductInfo update date. Default: 1970-1-1>
  -t <Patchsupersedence update date. Default: 1970-1-1>
  -s <all|scheduler|scanner|oos|attacker The system name rules should be exported for. This flag is used to included or exclude certain tables. Send 'all' to to include all tables.>
  -z <1|0 compress the final file. Default: 0>
EOF
	exit 1
fi

RULE_COLUMN=$(psql "${DBURL}" -t -q -c "SELECT string_agg(column_name, ',') FROM information_schema.columns WHERE table_schema='public' AND table_name='trules' AND column_name!='autorules' AND column_name!='released';")
RULEDEF_COLUMN=$(psql "${DBURL}" -t -q -c "SELECT string_agg(column_name, ',') FROM information_schema.columns WHERE table_schema='public' AND table_name='trulesdef';")
PRODUCTINFO_COLUMN=$(psql "${DBURL}" -t -q -c "SELECT string_agg(column_name, ',') FROM information_schema.columns WHERE table_schema='public' AND table_name='tproductinformation';")
PATCHSUPERSEDENCE_COLUMN=$(psql "${DBURL}" -t -q -c "SELECT string_agg(column_name, ',') FROM information_schema.columns WHERE table_schema='public' AND table_name='tpatchsupersedence' AND column_name != 'bulletinid';")

#echo $RULE_COLUMN

cat << EOF > "${filename}"
BEGIN;
SET client_encoding = utf8;
SET standard_conforming_strings = off;
SET check_function_bodies = false;
SET client_min_messages = warning;
SET escape_string_warning = off;

$([ -f "$ADDITIONAL_FILE" ] && cat "$ADDITIONAL_FILE")

COMMIT;

BEGIN;
EOF

if [[ "${MIGRATIONS}" -eq 1 ]]; then
cat << EOF >> "${filename}"
DO \$$
	BEGIN
		CREATE OR REPLACE FUNCTION addColumn(_table REGCLASS, _column TEXT, _type TEXT)
			RETURNS bool AS
		\$FUNC\$
		BEGIN
			IF EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = _table AND attname = LOWER(_column) AND NOT attisdropped) THEN
				RETURN FALSE;
			ELSE
				RAISE WARNING 'Creating % on %', _column, _table;
				EXECUTE format('ALTER TABLE %s ADD COLUMN %s %s', _table, _column, _type);
				RETURN TRUE;
			END IF;
		END
		\$FUNC\$  LANGUAGE plpgsql;

		PERFORM addColumn('trules', 'cvssV3Score', 'DECIMAL(10, 1)');
		PERFORM addColumn('trules', 'cvssV3Vector', 'VARCHAR(100)');
		PERFORM addColumn('tproductinformation', 'updatedby', 'BIGINT');
		PERFORM addColumn('tproductinformation', 'updated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('tpatchsupersedence', 'updatedby', 'BIGINT');
		PERFORM addColumn('tpatchsupersedence', 'updated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('tpatchsupersedence', 'deleted', 'BOOLEAN DEFAULT FALSE');

		CREATE SEQUENCE IF NOT EXISTS texploits_seq
			START WITH 1000
			INCREMENT BY 1
			NO MAXVALUE
			NO MINVALUE
			CACHE 1;

		PERFORM addColumn('texploits', 'approved', 'boolean');
		PERFORM addColumn('texploits', 'id', 'bigint NOT NULL DEFAULT nextval(''texploits_seq''::regclass)');
		IF EXISTS(SELECT column_default FROM information_schema.columns col WHERE col.column_default IS NOT NULL AND table_name='texploits' and column_name='id') THEN
			ALTER TABLE texploits ALTER COLUMN id DROP DEFAULT;
			ALTER TABLE texploits ADD CONSTRAINT texploits_pkey PRIMARY KEY (id);
		END IF;

		PERFORM addColumn('trules', 'readyforreview', 'BOOLEAN');
		PERFORM addColumn('trulesdef', 'hidereport', 'BOOLEAN');
		IF (SELECT addColumn('trules', 'specialNotes', 'specialNoteType[]')) THEN
			UPDATE trules SET specialNotes = array_append(ARRAY[]::specialNoteType[], specialnote) WHERE specialnote IS NOT NULL AND specialNote != 'NONE';
		END IF;
		PERFORM addColumn('trules', 'cwe', 'INTEGER');
		PERFORM addColumn('trules', 'operation', 'SMALLINT');
		PERFORM addColumn('tproductinformation', 'signaturekeys', 'TEXT[]');
		PERFORM addColumn('trules', 'cyrating', 'DECIMAL(10, 2) NOT NULL DEFAULT 1.0');
		PERFORM addColumn('trules', 'exploitprobability', 'DECIMAL(10, 2) NOT NULL DEFAULT 0');
		PERFORM addColumn('trules', 'previouscyrating', 'DECIMAL(10, 2) NOT NULL DEFAULT 1.0');
		PERFORM addColumn('trules', 'previousexploitprobability', 'DECIMAL(10, 2) NOT NULL DEFAULT 0');
		PERFORM addColumn('trules', 'cyr3conupdated', 'TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()');
		PERFORM addColumn('classifications', 'securecodewarrior', 'JSONB');
		IF EXISTS(SELECT column_name FROM information_schema.columns WHERE table_name='texploits' AND column_name='identifier' AND data_type='character varying') THEN
			ALTER TABLE texploits ALTER COLUMN identifier TYPE text;
		END IF;
		PERFORM addColumn('cyr3con', 'lastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'cyratinglastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('truleshistory', 'cyratinglastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'productid', 'INTEGER');
		PERFORM addColumn('trules', 'bluelivupdated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'bluelivscore', 'FLOAT');
		PERFORM addColumn('trules', 'bluelivdelta', 'FLOAT');
		PERFORM addColumn('trules', 'bluelivlastthreatactivity', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'bluelivmentions', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'bluelivthreatactors', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'bluelivexploits', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'farsight', 'JSONB');
		PERFORM addColumn('truleshistory', 'farsight', 'JSONB');
		UPDATE trules SET productid = (SELECT xid FROM tproductinformation WHERE product = solutionproduct) WHERE productid IS NULL;

		DROP FUNCTION addColumn(REGCLASS, TEXT, TEXT);
	END;
\$$;
EOF
fi

if [[ 'scanner' != "${SYSTEM}" ]]; then
{
	echo "DROP TABLE IF EXISTS texploitsupdate;"
	echo "CREATE TABLE texploitsupdate(type smallint, cve varchar(100), name text, created timestamp without time zone, identifier text, pack varchar(255), scriptid bigint);"
	echo "COPY texploitsupdate(type, cve, name, created, identifier, pack, scriptid) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT type, cve, name, created, identifier, pack, scriptid FROM texploits WHERE created > coalesce(nullif('${EXPLOITS_DATE}', ''), '${placeholder}')::timestamp) TO STDOUT"
	echo "\."
	echo "DELETE FROM texploits WHERE (type, coalesce(cve,''), coalesce(name,''), coalesce(identifier,''), coalesce(pack,''), coalesce(scriptid, 0)) IN (select type, coalesce(cve,''), coalesce(name,''), coalesce(identifier,''), coalesce(pack,''), coalesce(scriptid, 0) FROM texploitsupdate);"
	echo "INSERT INTO texploits(id, type, cve, name, created, identifier, pack, scriptid) SELECT nextval('texploits_seq'), type, cve, name, created, identifier, pack, scriptid FROM texploitsupdate;"
} >> "${filename}"
fi

{
	echo "DROP TABLE IF EXISTS trulesupdate;"
	echo "DROP TABLE IF EXISTS trulesdefupdate;"
	echo "CREATE TABLE trulesupdate AS SELECT * FROM trules LIMIT 0;"
	echo "COPY trulesupdate(${RULE_COLUMN}) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT ${RULE_COLUMN} from trules WHERE (updated > '${RULE_DATE}' OR bluelivupdated > nullif('${BLUE_LIV_DATE}', '')::timestamp OR (nullif('${BLUE_LIV_DATE}', '')::timestamp IS NULL AND bluelivupdated IS NOT NULL)) AND runforadmins IS DISTINCT FROM TRUE AND isscript = FALSE) TO STDOUT"
	echo "\."
	echo "CREATE TABLE trulesdefupdate AS SELECT * FROM trulesdef LIMIT 0;"
	echo "COPY trulesdefupdate(${RULEDEF_COLUMN}) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT ${RULEDEF_COLUMN} FROM trulesdef WHERE ruleid IN (SELECT ruleid FROM trules WHERE (updated > '${RULE_DATE}' OR bluelivupdated > nullif('${BLUE_LIV_DATE}', '')::timestamp OR (nullif('${BLUE_LIV_DATE}', '')::timestamp IS NULL AND bluelivupdated IS NOT NULL)) AND runforadmins IS DISTINCT FROM TRUE AND isScript = FALSE)) TO STDOUT"
	echo "\."

	echo "DELETE FROM trules WHERE ruleid IN (select ruleid FROM trulesupdate);"
	echo "INSERT INTO trules SELECT * FROM trulesupdate;"
	echo "INSERT INTO trulesdef SELECT * FROM trulesdefupdate;"

	echo "DROP TABLE IF EXISTS tpatchsupersedenceupdate;"
	echo "CREATE TABLE tpatchsupersedenceupdate AS SELECT * FROM tpatchsupersedence LIMIT 0;"
	echo "COPY tpatchsupersedenceupdate(${PATCHSUPERSEDENCE_COLUMN}) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT ${PATCHSUPERSEDENCE_COLUMN} from tpatchsupersedence WHERE updated > COALESCE(nullif('${PATCHSUPERSEDENCE_DATE}', ''), '${placeholder}')::timestamp) TO STDOUT"
	echo "\."
	echo "DELETE FROM tpatchsupersedence WHERE xid IN (select xid FROM tpatchsupersedenceupdate);"
	echo "INSERT INTO tpatchsupersedence SELECT * FROM tpatchsupersedenceupdate;"

	echo "DROP TABLE IF EXISTS tproductinformationupdate;"
	echo "CREATE TABLE tproductinformationupdate AS SELECT * FROM tproductinformation LIMIT 0;"
	echo "COPY tproductinformationupdate(${PRODUCTINFO_COLUMN}) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT ${PRODUCTINFO_COLUMN} from tproductinformation WHERE updated > COALESCE(nullif('${PRODUCTINFO_DATE}', ''), '${placeholder}')::timestamp) TO STDOUT"
	echo "\."
	echo "DELETE FROM tproductinformation WHERE xid IN (select xid FROM tproductinformationupdate);"
	echo "INSERT INTO tproductinformation SELECT * FROM tproductinformationupdate;"

	echo "DROP TABLE IF EXISTS classificationsupdate;"
	echo "CREATE TABLE classificationsupdate (cwe INTEGER, securecodewarrior JSONB);"
	echo "COPY classificationsupdate(cwe, securecodewarrior) FROM stdin;"
	psql "${DBURL}" -c "COPY (SELECT cwe, securecodewarrior FROM classifications) TO STDOUT"
	echo "\."
	echo "UPDATE classifications c SET securecodewarrior = (SELECT securecodewarrior FROM classificationsupdate u WHERE u.cwe = c.cwe);"

	if [[ 'all' == "${SYSTEM}" ]] || [[ 'scheduler' == "${SYSTEM}" ]]; then
		echo "DELETE FROM tvulxrefs;"
		echo "COPY tvulxrefs(iid, xid, vctype, vcxref) FROM stdin;"
		psql "${DBURL}" -c "COPY (SELECT iid, xid, vctype, vcxref FROM tvulxrefs) TO STDOUT"
		echo "\."
		echo "SELECT setval('tvulxrefs_seq', (SELECT MAX(xid) + 1 FROM tvulxrefs));"

		echo "DELETE FROM ttranslationvulns;"
		echo "COPY ttranslationvulns(ruleid, type, product, key, language, value) FROM stdin;"
		psql "${DBURL}" -c "COPY (SELECT ruleid, type, product, key, language, value FROM ttranslationvulns) TO STDOUT"
		echo "\."
	fi
	echo "COMMIT;"
} >> "${filename}"

if [[ "${COMPRESS}" -eq 0 ]]; then
	echo "${filename}"
else
	gzip -9 -f "${filename}"
	echo "${filename}".gz
fi
