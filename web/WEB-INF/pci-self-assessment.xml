<?xml version = '1.0' encoding = 'iso-8859-1' ?>
<PCIREQUIREMENTLIST>
	<NAME>Payment Card Industry (PCI) Data Security Standard Self-Assessment Questionnaire</NAME>
	<VERSION>1.0</VERSION>
	<RELEASE>December 2004</RELEASE>

	<REQUIREMENTLIST>
		<REQUIREMENT>
			<GROUP>1</GROUP>
			<HEADLINE>Build and Maintain a Secure Network</HEADLINE>
			<NAME>Install and maintain a firewall configuration to protect data</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>2</GROUP>
			<HEADLINE>Build and Maintain a Secure Network</HEADLINE>
			<NAME>Do not use vendor-supplied defaults for system passwords and other security parameters</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>3</GROUP>
			<HEADLINE>Protect Cardholder Data</HEADLINE>
			<NAME>Protect stored data</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>4</GROUP>
			<HEADLINE>Protect Cardholder Data</HEADLINE>
			<NAME>Encrypt transmission of cardholder data and sensitive information across public networks</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>5</GROUP>
			<HEADLINE>Maintain a Vulnerability Management Program</HEADLINE>
			<NAME>Use and regularly update anti-virus software</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>6</GROUP>
			<HEADLINE>Maintain a Vulnerability Management Program</HEADLINE>
			<NAME>Develop and maintain secure systems and applications</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>7</GROUP>
			<HEADLINE>Implement Strong Access Control Measures</HEADLINE>
			<NAME>Restrict access to data by business need-to-know</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>8</GROUP>
			<HEADLINE>Implement Strong Access Control Measures</HEADLINE>
			<NAME>Assign a unique ID to each person with computer access</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>9</GROUP>
			<HEADLINE>Implement Strong Access Control Measures</HEADLINE>
			<NAME>Restrict physical access to cardholder data</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>10</GROUP>
			<HEADLINE>Regularly Monitor and Test Networks</HEADLINE>
			<NAME>Track and monitor all access to network resources and cardholder data</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>11</GROUP>
			<HEADLINE>Regularly Monitor and Test Networks</HEADLINE>
			<NAME>Regularly test security systems and processes</NAME>
		</REQUIREMENT>
		<REQUIREMENT>
			<GROUP>12</GROUP>
			<HEADLINE>Maintain a policy that addresses information security</HEADLINE>
			<NAME>Maintain a policy that addresses information security</NAME>
		</REQUIREMENT>
	</REQUIREMENTLIST>

	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are all router, switches, wireless access points, and firewall configurations secured and do they conform to documented security standards?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>2</KEY>
		<QTEXT>If wireless technology is used, is the access to the network limited to authorized devices?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>3</KEY>
		<QTEXT>Do changes to the firewall need authorization and are the changes logged?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>4</KEY>
		<QTEXT>Is a firewall used to protect the network and limit traffic to that which is required to conduct business?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are egress and ingress filters installed on all border routers to prevent impersonation with spoofed IP addresses?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>6</KEY>
		<QTEXT>Is payment card account information stored in a database located on the internal network (not the DMZ) and protected by a firewall?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>7</KEY>
		<QTEXT>If wireless technology is used, do perimeter firewalls exist between wireless networks and the payment card environment?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>8</KEY>
		<QTEXT>Does each mobile computer with direct connectivity to the Internet have a personal firewall and anti-virus software installed?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>9</KEY>
		<QTEXT>Are Web servers located on a publicly reachable network segment separated from the internal network by a firewall (DMZ)?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>1</GROUP>
		<KEY>10</KEY>
		<QTEXT>Is the firewall configured to translate (hide) internal IP addresses, using network address translation (NAT)?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are vendor default security settings changed on production systems before taking the system into production?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>2</KEY>
		<QTEXT>Are vendor default accounts and passwords disabled or changed on production systems before putting a system into production?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>3</KEY>
		<QTEXT>If wireless technology is used, are vendor default settings changed (i.e. WEP keys, SSID, passwords, SNMP community strings, disabling SSID broadcasts)?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>4</KEY>
		<QTEXT>If wireless technology is used, is Wi-Fi Protected Access (WPA) technology implemented for encryption and authentication when WPA-capable?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are all production systems (servers and network components) hardened by removing all unnecessary services and protocols installed by the default configuration?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>2</GROUP>
		<KEY>6</KEY>
		<QTEXT>Are secure, encrypted communications used for remote administration of production systems and applications?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>1</KEY>
		<QTEXT>Is sensitive cardholder data securely disposed of when no longer needed?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>2</KEY>
		<QTEXT>Is it prohibited to store the full contents of any track from the magnetic stripe (on the back of the card, in a chip, etc.) in the database, log files, or point-of-sale products?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>3</KEY>
		<QTEXT>Is it prohibited to store the card-validation code (three-digit value printed on the signature panel of a card) in the database, log files, or point-of-sale products?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>4</KEY>
		<QTEXT>Are all but the last four digits of the account number masked when displaying cardholder data?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are account numbers (in databases, logs, files, backup media, etc.) stored securely? for example, by means of encryption or truncation?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>3</GROUP>
		<KEY>6</KEY>
		<QTEXT>Are account numbers sanitized before being logged in the audit log?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>4</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are transmissions of sensitive cardholder data encrypted over public networks through the use of SSL or other industry acceptable methods?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>4</GROUP>
		<KEY>2</KEY>
		<QTEXT>If SSL is used for transmission of sensitive cardholder data, is it using version 3.0 with 128-bit encryption?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>4</GROUP>
		<KEY>3</KEY>
		<QTEXT>If wireless technology is used, is the communication encrypted using Wi-Fi Protected Access (WPA), VPN, SSL at 128-bit, or WEP?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>4</GROUP>
		<KEY>4</KEY>
		<QTEXT>If wireless technology is used, are WEP at 128-bit and additional encryption technologies in use, and are shared WEP keys rotated quarterly?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>4</GROUP>
		<KEY>5</KEY>
		<QTEXT>Is encryption used in the transmission of account numbers via e-mail?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	
	<QUESTION>
		<GROUP>5</GROUP>
		<KEY>1</KEY>
		<QTEXT>Is there a virus scanner installed on all servers and on all workstations, and is the virus scanner regularly updated?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are development, testing, and production systems updated with the latest security-related patches released by the vendors?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>2</KEY>
		<QTEXT>Is the software and application development process based on an industry best practice and is information security included throughout the software development life cycle (SDLC) process?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>3</KEY>
		<QTEXT>If production data is used for testing and development purposes, is sensitive cardholder data sanitized before usage?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>4</KEY>
		<QTEXT>Are all changes to the production environment and applications formally authorized, planned, and logged before being implemented?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>5</KEY>
		<QTEXT>Were the guidelines commonly accepted by the security community (such as Open Web Application Security Project group (www.owasp.org)) taken into account in the development of Web applications?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>6</KEY>
		<QTEXT>When authenticating over the Internet, is the application designed to prevent malicious users from trying to determine existing user accounts?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>7</KEY>
		<QTEXT>Is sensitive cardholder data stored in cookies secured or encrypted?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>6</GROUP>
		<KEY>8</KEY>
		<QTEXT>Are controls implemented on the server side to prevent SQL injection and other bypassing of client side-input controls?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	
	<QUESTION>
		<GROUP>7</GROUP>
		<KEY>1</KEY>
		<QTEXT>Is access to payment card account numbers restricted for users on a need-to-know basis?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are all users required to authenticate using, at a minimum, a unique username and password?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>2</KEY>
		<QTEXT>If employees, administrators, or third parties access the network remotely, is remote access software (such as PCAnywhere, dial-in, or VPN) configured with a unique username and password and with encryption and other security features turned on?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>3</KEY>
		<QTEXT>Are all passwords on network devices and systems encrypted?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>4</KEY>
		<QTEXT>When an employee leaves the company, are that employee?s user accounts and passwords immediately revoked?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are all user accounts reviewed on a regular basis to ensure that malicious, out-of-date, or unknown accounts do not exist?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>6</KEY>
		<QTEXT>Are non-consumer accounts that are not used for a lengthy amount of time (inactive accounts) automatically disabled in the system after a pre-defined period?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>7</KEY>
		<QTEXT>Are accounts used by vendors for remote maintenance enabled only during the time needed?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>8</KEY>
		<QTEXT>Are group, shared, or generic accounts and passwords prohibited for non-consumer users?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>9</KEY>
		<QTEXT>Are non-consumer users required to change their passwords on a pre-defined regular basis?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>10</KEY>
		<QTEXT>Is there a password policy for non-consumer users that enforces the use of strong passwords and prevents the resubmission of previously used passwords?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>8</GROUP>
		<KEY>11</KEY>
		<QTEXT>Is there an account-lockout mechanism that blocks a malicious user from obtaining access to an account by multiple password retries or brute force?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are there multiple physical security controls (such as badges, escorts, or mantraps) in place that would prevent unauthorized individuals from gaining access to the facility?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>2</KEY>
		<QTEXT>If wireless technology is used, do you restrict access to wireless access points, wireless gateways, and wireless handheld devices?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>3</KEY>
		<QTEXT>Are equipment (such as servers, workstations, laptops, and hard drives) and media containing cardholder data physically protected against unauthorized access?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>4</KEY>
		<QTEXT>Is all cardholder data printed on paper or received by fax protected against unauthorized access?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are procedures in place to handle secure distribution and disposal of backup media and other media containing sensitive cardholder data?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>6</KEY>
		<QTEXT>Are all media devices that store cardholder data properly inventoried and securely stored?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>9</GROUP>
		<KEY>7</KEY>
		<QTEXT>Is cardholder data deleted or destroyed before it is physically disposed (for example, by shredding papers or degaussing backup media)?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>10</GROUP>
		<KEY>1</KEY>
		<QTEXT>Is all access to cardholder data, including root/administration access, logged?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>10</GROUP>
		<KEY>2</KEY>
		<QTEXT>Do access control logs contain successful and unsuccessful login attempts and access to audit logs?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>10</GROUP>
		<KEY>3</KEY>
		<QTEXT>Are all critical system clocks and times synchronized, and do logs include date and time stamp?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>10</GROUP>
		<KEY>4</KEY>
		<QTEXT>Are the firewall, router, wireless access points, and authentication server logs regularly reviewed for unauthorized traffic?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>10</GROUP>
		<KEY>5</KEY>
		<QTEXT>Are audit logs regularly backed up, secured, and retained for at least three months online and one-year offline for all critical systems?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>11</GROUP>
		<KEY>1</KEY>
		<QTEXT>If wireless technology is used, is a wireless analyzer periodically run to identify all wireless devices?</QTEXT>
		<OPTIONTYPE>YNNA</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>11</GROUP>
		<KEY>2</KEY>
		<QTEXT>Is a vulnerability scan or penetration test performed on all Internet-facing applications and systems before they go into production?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>11</GROUP>
		<KEY>3</KEY>
		<QTEXT>Is an intrusion detection or intrusion prevention system used on the network?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>11</GROUP>
		<KEY>4</KEY>
		<QTEXT>Are security alerts from the intrusion detection or intrusion prevention system (IDS/IPS) continuously monitored, and are the latest IDS/IPS signatures installed?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>

	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>1</KEY>
		<QTEXT>Are information security policies, including policies for access control, application and system development, operational, network and physical security, formally documented?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>2</KEY>
		<QTEXT>Are information security policies and other relevant security information disseminated to all system users (including vendors, contractors, and business partners)?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>3</KEY>
		<QTEXT>Are information security policies reviewed at least once a year and updated as needed?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>4</KEY>
		<QTEXT>Have the roles and responsibilities for information security been clearly defined within the company?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>5</KEY>
		<QTEXT>Is there an up-to-date information security awareness and training program in place for all system users?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>6</KEY>
		<QTEXT>Are employees required to sign an agreement verifying they have read and understood the security policies and procedures?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>7</KEY>
		<QTEXT>Is a background investigation (such as a credit- and criminal-record check, within the limits of local law) performed on all employees with access to account numbers?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>8</KEY>
		<QTEXT>Are all third parties with access to sensitive cardholder data contractually obligated to comply with card association security standards?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>9</KEY>
		<QTEXT>Is a security incident response plan formally documented and disseminated to the appropriate responsible parties?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>10</KEY>
		<QTEXT>Are security incidents reported to the person responsible for security investigation?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
	<QUESTION>
		<GROUP>12</GROUP>
		<KEY>11</KEY>
		<QTEXT>Is there an incident response team ready to be deployed in case of a cardholder data compromise?</QTEXT>
		<OPTIONTYPE>YN</OPTIONTYPE>
	</QUESTION>
</PCIREQUIREMENTLIST>
