module outpost24-eventservice 0;

# **************************************************
# Initialize policy with types and allow transition
# entry
# **************************************************

####################################################
# Require things needed for incomming transitions
####################################################
require {
	class file { entrypoint execute };
	attribute configfile, domain, entry_type, exec_type, file_type;
	role unconfined_r, system_r;
}

####################################################
# Declare types
####################################################
type o24_eventservice_t, domain;
type o24_eventservice_exec_t, file_type, exec_type, entry_type;
type o24_eventservice_conf_t, configfile, file_type, entry_type;

# TODO consider exchanging/removing standard attributes domain, file_type etc.

####################################################
# Add types to roles (RBAC)
####################################################

role unconfined_r types { o24_eventservice_t o24_eventservice_exec_t };
role system_r types { o24_eventservice_t o24_eventservice_exec_t };

# TODO consider changing/removing standard roles system, unconfined etc.

####################################################
# Make rules for these types be permissive
####################################################
permissive o24_eventservice_t;
permissive o24_eventservice_exec_t;

####################################################
# Allow transitions to this context (MAC)
####################################################

allow o24_eventservice_t o24_eventservice_exec_t : file { entrypoint execute };

# **************************************************
# Allow transitions to other contexts
# **************************************************

####################################################
# Require things needed for all outgoing transitions
####################################################

require {
	class dir { getattr search read };
	class file { entrypoint execute getattr open read };
	class process { transition noatsecure rlimitinh siginh getattr };
	class lnk_file { read };
	class fd { use };
}

####################################################
# Allow transitions from init to this context (MAC)
#
# This is needed if your context is supposed to be
# entered as a service via systemd
####################################################

optional {
	require {
		type init_t, init_exec_t;
	}
	type_transition init_t o24_eventservice_exec_t : process o24_eventservice_t;
	allow init_t o24_eventservice_t : process { transition };
	allow init_t o24_eventservice_exec_t:file { execute open read };
}

####################################################
# Allow transitions from this context (MAC)
####################################################

# You can create an attribute (type group) that other policies
# can opt their types into and write rules given that one instead,
# however, the last value of the type_transition statement
# needs to be a concrete type. If an layout where the child
# packages/policies controls what will be executed instead of
# the parent, the type_transition statement probably belongs
# in the child policy instead of the parent.
# Parent example with attributes:
#
#attribute o24_sample_trans_target;
#attribute o24_sample_trans_exec;
#allow o24_sample_t o24_sample_trans_target : process { transition };
#allow o24_sample_t o24_sample_trans_exec:file { execute open read };
#
# Child example with attributes:
#
#optional {
#	require {
#		attribute o24_sample_trans_target, o24_sample_trans_exec;
#	}
#	type_transition o24_sample_t o24_sample_trans_exec : process other_t;
#}

optional {
	require {
		type other_t, other_exec_t;
	}
	type_transition o24_eventservice_t other_exec_t : process other_t;
	allow o24_eventservice_t other_t : process { transition };
	allow o24_eventservice_t other_exec_t:file { execute open read };
}


# **************************************************
# Allow contexts needed permissions (MAC)
# **************************************************

# Make sure to run the explorative tests in as similar way as possible to how
# it will run in production to minimize what you allow, eg. don't run with a
# tty unless you need to, etc.

require {
	type sysfs_t;
	type bin_t;
	type cert_t;
	type cgroup_t;
	type devlog_t;
	type ephemeral_port_t;
	type fs_t;
	type ifconfig_exec_t;
	type initrc_tmp_t;
	type kernel_t;
	type ldconfig_exec_t;
	type net_conf_t;
	type node_t;
	type opi_config_t;
	type pam_var_run_t;
	type passwd_file_t;
	type postfix_etc_t;
	type postfix_master_t;
	type postfix_postdrop_exec_t;
	type postfix_public_t;
	type postfix_spool_t;
	type postgresql_port_t;
	type proc_net_t;
	type proc_t;
	type random_device_t;
	type sendmail_exec_t;
	type shell_exec_t;
	type smtp_port_t;
	type sudo_db_t;
	type sudo_exec_t;
	type syslogd_var_run_t;
	type tmp_t;
	type unreserved_port_t;
	type user_home_dir_t;
	class file { execute execute_no_trans };
	class sock_file { getattr write };
	class tcp_socket { accept bind connect create getattr getopt listen name_bind name_connect node_bind setopt shutdown };
	class lnk_file read;
	class filesystem getattr;
	class dir { add_name create getattr open read remove_name search write };
	class file { create ioctl lock rename setattr unlink write };
	class unix_dgram_socket { connect create getopt setopt sendto };
	class unix_stream_socket { connectto };
	class chr_file { getattr open read };
	class capability { audit_write setgid setuid sys_resource };
	class netlink_audit_socket { create nlmsg_relay };
	class netlink_route_socket { bind create getattr nlmsg_read setopt };
	class process { execmem setrlimit };
	class udp_socket { connect create getattr };
}

allow o24_eventservice_t sysfs_t:lnk_file read; # Needed to interact with network interfaces

#!!!! WARNING: 'bin_t' is a base type.
allow o24_eventservice_t bin_t:file { execute execute_no_trans };														# Execute java
allow o24_eventservice_t cgroup_t:dir search;
allow o24_eventservice_t cgroup_t:file { getattr open read };
allow o24_eventservice_t devlog_t:sock_file write;																		# Logs
allow o24_eventservice_t ephemeral_port_t:tcp_socket name_connect;
allow o24_eventservice_t fs_t:filesystem getattr;
allow o24_eventservice_t ifconfig_exec_t:file { execute execute_no_trans open read };									# Get network information
allow o24_eventservice_t initrc_tmp_t:dir { add_name getattr open read remove_name search write };						# Java performance information
allow o24_eventservice_t initrc_tmp_t:file { create unlink write };														# Java performance information

#!!!! The file '/dev/log' is mislabeled on your system.
#!!!! Fix with $ restorecon -R -v /dev/log
allow o24_eventservice_t kernel_t:unix_dgram_socket { getopt setopt sendto };											# Sendmail
allow o24_eventservice_t net_conf_t:file { getattr open read };															# /etc/resolv.conf, /etc/hosts
allow o24_eventservice_t node_t:tcp_socket node_bind;																	# TCP
allow o24_eventservice_t o24_eventservice_exec_t:file { getattr ioctl open read };										# Read jar file
allow o24_eventservice_t opi_config_t:dir search;																		# /etc/opi/hiabsetup.properties
allow o24_eventservice_t opi_config_t:file { getattr open read };														# /etc/opi/hiabsetup.properties
allow o24_eventservice_t pam_var_run_t:dir { add_name getattr write };													# tmpfs
allow o24_eventservice_t pam_var_run_t:file { create getattr lock open read write };									# /run/sudo/ts/o24event
allow o24_eventservice_t passwd_file_t:file { getattr open read };														# Sendmail seems to need this
allow o24_eventservice_t postfix_etc_t:file { getattr ioctl open read };												# Send email
allow o24_eventservice_t postfix_etc_t:dir search;

#!!!! The file '/var/spool/postfix/public/pickup' is mislabeled on your system.
#!!!! Fix with $ restorecon -R -v /var/spool/postfix/public/pickup
allow o24_eventservice_t postfix_master_t:unix_stream_socket connectto;													# Postfix
allow o24_eventservice_t postfix_postdrop_exec_t:file { execute execute_no_trans open read };							# Postfix
allow o24_eventservice_t postfix_public_t:sock_file { getattr write };
allow o24_eventservice_t postfix_spool_t:dir { add_name remove_name search write };
allow o24_eventservice_t postfix_spool_t:file { create getattr open read rename setattr write };
allow o24_eventservice_t postgresql_port_t:tcp_socket name_connect;														# Postgresql
allow o24_eventservice_t proc_net_t:file { getattr open read };															# Network information
allow o24_eventservice_t proc_t:file { getattr open read };																# Meminfo
allow o24_eventservice_t random_device_t:chr_file { getattr open read };												# /dev/random
allow o24_eventservice_t self:capability { audit_write setgid setuid sys_resource };									# sudo
allow o24_eventservice_t self:netlink_audit_socket { create nlmsg_relay };												# sudo
allow o24_eventservice_t self:netlink_route_socket { bind create getattr nlmsg_read setopt };							# Network
allow o24_eventservice_t self:process { execmem setrlimit };
allow o24_eventservice_t self:tcp_socket { accept bind connect create getattr getopt listen setopt shutdown };			# Network connect
allow o24_eventservice_t self:udp_socket { connect create getattr };													# Network connect
allow o24_eventservice_t self:unix_dgram_socket { connect create };														# UDP socket
allow o24_eventservice_t sendmail_exec_t:file { execute execute_no_trans getattr open read };							# Postfix
allow o24_eventservice_t smtp_port_t:tcp_socket name_connect;

#!!!! WARNING: 'shell_exec_t' is a base type.
allow o24_eventservice_t shell_exec_t:file execute;																		# Bash
allow o24_eventservice_t sudo_db_t:dir { getattr search };																# sudo
allow o24_eventservice_t sudo_exec_t:file { execute execute_no_trans getattr open read };								# sudo
allow o24_eventservice_t sysfs_t:dir read;

#!!!! WARNING: 'tmp_t' is a base type.
allow o24_eventservice_t tmp_t:dir { add_name create read remove_name write };											# tmp files

#!!!! WARNING: 'tmp_t' is a base type.
allow o24_eventservice_t tmp_t:file { create unlink write };															# tmp files
allow o24_eventservice_t unreserved_port_t:tcp_socket { name_bind name_connect };										# Listen to tcp
allow o24_eventservice_t user_home_dir_t:dir search;

allow o24_eventservice_t cert_t:dir search;																				# Certificates
allow o24_eventservice_t cert_t:file { getattr open read };
allow o24_eventservice_t cert_t:lnk_file read;
allow o24_eventservice_t syslogd_var_run_t:dir search;
allow o24_eventservice_t ldconfig_exec_t:file { execute execute_no_trans open read };

