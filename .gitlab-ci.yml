include:
  - project: 'outpost24/gitlab/gitlab-job-templates'
    ref: v3.0
    file:
      - '/legacy/rpm-build-steps.yaml'
      - '/workflows/java-publish-docker.yaml'

variables:
  LEGACY_PROJECT_PATH: "opi"
  BRANCH_NAME: $CI_COMMIT_REF_NAME
  GIT_URL: $CI_PROJECT_URL
  GIT_COMMIT: $CI_COMMIT_SHA
  GIT_TAG: $CI_COMMIT_TAG
  BUILD_TYPE: "docker"
  RUNNER_SIZE: "large"
  LEGACY_RUNNER_SIZE: "large"
  TRIVY_TARGET: "publish"

# Triggers the renovate-bot branch in the scan workflows gitops base repo to promote newly tagged images
trigger-on-tag-scan-workflows-gitops-base:
  extends: .trigger-downstream-on-tag
  trigger:
    project: outpost24/vm/gitops/scan-workflows-gitops-base
