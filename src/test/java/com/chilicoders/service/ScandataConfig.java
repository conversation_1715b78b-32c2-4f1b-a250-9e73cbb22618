package com.chilicoders.service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.kafka.test.core.BrokerAddress;
import org.springframework.kafka.test.rule.EmbeddedKafkaRule;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;

@EnableKafka
@Configuration
@EmbeddedKafka
public class ScandataConfig {
	@Autowired
	private ConfigurationService configService;

	public static final EmbeddedKafkaRule embeddedKafka = new EmbeddedKafkaRule(1, true, "topic").kafkaPorts(0);

	/**
	 * Create a kafka listener container.
	 *
	 * @return Listener container.
	 */
	@Bean
	@Primary
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> scanDataKafkaListenerContainerFactory() {
		if (embeddedKafka.getEmbeddedKafka().getZookeeper() == null) {
			embeddedKafka.before();
		}
		final BrokerAddress brokerAddress = embeddedKafka.getEmbeddedKafka().getBrokerAddresses()[0];
		configService.setProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap.getConfigKey(), "http://" + brokerAddress);

		final ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(scanDataConsumerFactory());
		factory.setConcurrency(10);
		factory.getContainerProperties().setAuthExceptionRetryInterval(Duration.ofSeconds(10));
		return factory;
	}

	/**
	 * Create a consumer factory.
	 *
	 * @return Consumer factory.
	 */
	@Bean
	@Primary
	public ConsumerFactory<String, String> scanDataConsumerFactory() {
		final Map<String, Object> props = new HashMap<>();
		final String kafkaBrokers = configService.getProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap);

		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
		props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, configService.getProperty(ConfigKeys.ConfigurationIntKey.report_max_poll_records));
		props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, configService.getProperty(ConfigKeys.ConfigurationIntKey.report_max_poll_interval));
		if (!kafkaBrokers.contains("localhost") &&
				!kafkaBrokers.contains("127.0.0.1") &&
				!kafkaBrokers.contains("host.docker.internal") &&
				!configService.getProperty(ConfigKeys.ConfigurationBooleanKey.kafka_disable_authentication)) {
			props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
			props.put(SaslConfigs.SASL_MECHANISM, "AWS_MSK_IAM");
			props.put(SaslConfigs.SASL_JAAS_CONFIG, "software.amazon.msk.auth.iam.IAMLoginModule required awsDebugCreds=true;");
			props.put(SaslConfigs.SASL_CLIENT_CALLBACK_HANDLER_CLASS, "software.amazon.msk.auth.iam.IAMClientCallbackHandler");
		}
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
		return new DefaultKafkaConsumerFactory<>(props);
	}
}
