package com.chilicoders.report;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import com.chilicoders.core.compliancefinding.api.ComplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.reporting.api.ReportingService;
import com.chilicoders.core.rule.RuleEngineListener;
import com.chilicoders.core.rule.api.RuleService;
import com.chilicoders.core.scandata.api.ScanPolicyService;
import com.chilicoders.core.scandata.api.ScanStatusService;
import com.chilicoders.core.scandata.api.ScanlogService;
import com.chilicoders.core.scandata.api.ScheduleService;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatus;
import com.chilicoders.core.scheduling.api.model.ScanResult;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.core.user.impl.TestUserDetails;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.ReportData;
import com.chilicoders.model.ReportDataList;
import com.chilicoders.model.ReportEntryTypes;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.Template;
import com.chilicoders.model.WasTypes;
import com.chilicoders.ruleengine.InstalledProductInfo;
import com.chilicoders.ruleengine.RuleEngine;
import com.chilicoders.ruleengine.ScanDataCollector;
import com.chilicoders.ruleengine.TestScanDataCollector;
import com.chilicoders.test.TestUtil;
import com.chilicoders.test.model.TestScanConfiguration;
import com.chilicoders.test.model.TestTarget;
import com.chilicoders.test.model.TestTargetGroup;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XMLObject;
import com.chilicoders.util.xml.XmlUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;

@ActiveProfiles("scandatatest")
public class NetsecReportImporterTest extends BaseSpringTestWithRollback {
	@Autowired
	private ConfigurationService configService;

	@Autowired
	private ReportingService reportService;

	@Autowired
	private UserService userService;

	@Autowired
	private TargetService targetService;

	@Autowired
	private IpService ipService;

	@Autowired
	private RuleService ruleService;

	@Autowired
	private ScanPolicyService scanPolicyService;

	@Autowired
	private ScheduleService scheduleService;

	@Autowired
	private ScanStatusService scanStatusService;

	@Autowired
	private ScanlogService scanlogService;

	@Autowired
	private ReportCreateService reportCreateService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SaveReportService saveReportService;

	@Autowired
	private ComplianceService complianceService;

	@Autowired
	private TransactionalNativeStatementExecutor statementExecutor;

	@Autowired
	private TestUtil testUtil;

	@Test
	public void testReportUpdate() throws Exception {
		final TestUserDetails testUser = new TestUserDetails(false);
		addUser(testUser, statementExecutor);

		final long targetId = testUtil.insertTarget(TestTarget.builder().userId(testUser.getMainUserId()).ipAddress("*******").build());
		final long scheduleId = testUtil.insertSchedule(testUser.getMainUserId(), "Test schedule");
		final long targetGroupId = testUtil.insertTargetGroup(TestTargetGroup.builder().userId(testUser.getMainUserId()).name("Test group").build());
		testUtil.insertLinkGeneric(targetGroupId, targetId);

		final ScanStatus scanstatus = new ScanStatus();
		scanstatus.setUserId(testUser.getMainUserId());
		scanstatus.setTargetId(targetId);
		scanstatus.setScheduleId(scheduleId);
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, -10);
		scanstatus.setScanEnded(cal.getTime());
		scanstatus.setScanStarted(cal.getTime());

		final List<Event> eventList = new ArrayList<>();

		ReportDataList report = new ReportDataList();
		try (final ScanDataCollector collector = new TestScanDataCollector(statementExecutor,
				new InstalledProductInfo("apache/tomcat", Protocol.TCP, 80, null, null, "7.0.25", null, null, null, false, false, null, false, false, false, null, 0, 0, null,
						null, false, null))) {

			RuleEngine.executeRules(collector, new RuleEngineListener(report, testUser.getMainUserId(), false), null, false, false, false, false, true, null, false,
					false);
		}
		report.getReportData().add(ReportData.builder().type(ReportEntryTypes.PORT.getId()).port("80").userId(testUser.getMainUserId()).build());
		final long reportId =
				NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService,
						ipService, targetService, ruleService, reportService, scanPolicyService, reportCreateService, scanstatus, report, eventList, null, new ArrayList<>());
		scanstatus.setScanEnded(new Date());
		scanstatus.setScanStarted(new Date());
		scanstatus.setScanlessReportId(reportId);
		report = new ReportDataList();
		final Set<String> patches = new HashSet<>();
		patches.add("KB967723");
		try (final TestScanDataCollector collector2 = new TestScanDataCollector(statementExecutor,
				new InstalledProductInfo("microsoft/windows", Protocol.TCP, 445, null, null, "6.0 sp1", null, patches, null, false, false, null, false, false, true, null, 0,
						0, null, null, false, null))) {
			RuleEngine.executeRules(collector2, new RuleEngineListener(report, testUser.getMainUserId(), false), null, false, false, false, false, true, null, false,
					false);
			report.getReportData().add(ReportData.builder().type(ReportEntryTypes.PORT.getId()).port("445").userId(testUser.getMainUserId()).build());
			NetsecReportImporter.updateReport(configService, reportService, userService, targetService, ipService, ruleService, scanPolicyService, scheduleService,
					reportCreateService, messageService, statementExecutor, scanstatus, report, new ArrayList<>());
		}

		assertThat(statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM treport_vulns WHERE dadded IS NOT NULL"))).isEqualTo(59);
		assertThat(eventList).hasSize(0);
	}

	@Test
	public void testWasReporting() throws SQLException, JSONException, JsonProcessingException {
		XMLObject.createNewDocument();
		final TestUserDetails testUser = new TestUserDetails(false);
		addUser(testUser, statementExecutor);

		final long targetId = testUtil.insertTarget(TestTarget.builder().userId(testUser.getMainUserId()).ipAddress("*******").build());
		final long scheduleId = testUtil.insertSchedule(testUser.getMainUserId(), "Test schedule");
		final long targetGroupId = testUtil.insertTargetGroup(TestTargetGroup.builder().userId(testUser.getMainUserId()).name("Test group").build());
		testUtil.insertLinkGeneric(targetGroupId, targetId);

		final List<Event> eventList = new ArrayList<>();

		final ScanStatus scanstatus = new ScanStatus();
		scanstatus.setUserId(testUser.getMainUserId());
		scanstatus.setTargetId(targetId);
		scanstatus.setTarget("*******");
		scanstatus.setScheduleId(scheduleId);
		scanstatus.setScanStarted(new Date());
		scanstatus.setScanEnded(new Date());

		final String xml =
				"<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><REPORTLIST><REPORT><ITYPE>1</ITYPE><IPORT>443</IPORT><XUSERXID>6</XUSERXID></REPORT><REPORT><XUSERXID>6</XUSERXID>"
						+ "<DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT>"
						+ "<VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>295843</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE>"
						+ "<VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>269510</VCVULNID>"
						+ "<ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL><CDATA>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Path&lt;/col&gt;&lt;col&gt;Count&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;"
						+ "http://www.test.com&lt;/col&gt;&lt;col&gt;1&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</CDATA></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST>"
						+ "<VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>250129</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL>"
						+ "<CDATA>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Name&lt;/col&gt;&lt;col&gt;Value&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;Crawled URIs&lt;/col&gt;&lt;col&gt;1&lt;/col&gt;&lt;/row&gt;"
						+ "&lt;/rtab&gt;</CDATA></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT>"
						+ "<VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>250204</VCVULNID><ITYPE>3</ITYPE><IPROTOCOL>6</IPROTOCOL><VCCVE>NOCVE</VCCVE><VCBUG>NOBUG</VCBUG><VCSCVSSVECTOR>(AV:N/AC:L/Au:N/C:C/I:C/A:C)</VCSCVSSVECTOR>"
						+ "<ISCVSS>100</ISCVSS><IPCICVSS>0</IPCICVSS><BPCIFAILED>1</BPCIFAILED></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT>"
						+ "<PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>WAS_REPORT</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL><CDATA>H4sIAAAAAAAAAI2RX2vCMBTFv4r0fU0tDsa4RoK5nQFNuvx"
						+ "h01dXnDDbMcO6j7+mW8T2yadzzj0J+V0Ci5/Tx+S7+jofm3qeTNMsmVT1vnk71od54mxx95AsKGgslbZrYWz0FOy2RDoF0is4vabv3n8+EtK2beqrs0/3zQlIKGCDdqU4BRKNFRukWXc5KCwV71OvUKrwDPmTwu12yEP574BEgAFIHkEEp/l9lmczIJ29jetQ+RFaJBuwLJW0KEO+OI3PDo1dIeOoTQ83HG"
						+ "g0pZIGr0+MJsy9cmZZV13cCzOFkFzIp7ADGcW4OLn6ll+iQtPPyQEAAA==</CDATA></REPORT></REPORTLIST>";
		final ReportDataList report = new XmlMapper().readValue(xml, ReportDataList.class);
		NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService, ipService,
				targetService, ruleService, reportService, scanPolicyService, reportCreateService, scanstatus, report, eventList, null, new ArrayList<>());

		assertThat(statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM twasfindings"))).isEqualTo(2);
		assertThat(eventList).hasSize(0);

		final String auxDataReportXml =
				"<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><REPORTLIST><REPORT><ITYPE>1</ITYPE><IPORT>443</IPORT><XUSERXID>6</XUSERXID></REPORT><REPORT><XUSERXID>6</XUSERXID>"
						+ "<DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT>"
						+ "<VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>295843</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE>"
						+ "<VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>269510</VCVULNID>"
						+ "<ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL><CDATA>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Path&lt;/col&gt;&lt;col&gt;Count&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;"
						+ "http://www.test.com&lt;/col&gt;&lt;col&gt;1&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</CDATA></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST>"
						+ "<VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>250129</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL>"
						+ "<CDATA>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Name&lt;/col&gt;&lt;col&gt;Value&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;Crawled URIs&lt;/col&gt;&lt;col&gt;1&lt;/col&gt;&lt;/row&gt;"
						+ "&lt;/rtab&gt;</CDATA></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT><PRODUCT>Web Application Extended Scanning</PRODUCT>"
						+ "<VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>250204</VCVULNID><ITYPE>3</ITYPE><IPROTOCOL>6</IPROTOCOL><VCCVE>NOCVE</VCCVE><VCBUG>NOBUG</VCBUG><VCSCVSSVECTOR>(AV:N/AC:L/Au:N/C:C/I:C/A:C)</VCSCVSSVECTOR>"
						+ "<ISCVSS>100</ISCVSS><IPCICVSS>0</IPCICVSS><BPCIFAILED>1</BPCIFAILED></REPORT><REPORT><XUSERXID>6</XUSERXID><DREPORTDATE>2023-03-31 08:22</DREPORTDATE><VCVHOST>www.test.com</VCVHOST><VCHOST>www.test.com</VCHOST><IPORT>80</IPORT>"
						+ "<PRODUCT>Web Application Extended Scanning</PRODUCT><VCFAMILY>was</VCFAMILY><WASFINDING>1</WASFINDING><VCVULNID>WAS_REPORT</VCVULNID><ITYPE>2</ITYPE><IPROTOCOL>6</IPROTOCOL><CDATA>H4sIAAAAAAAAAI2SUVOCQBDH3/sUDq9NHpilNcc54K0CAYdw"
						+ "lPmWxqAZ0Ah52qdPqGvUp+7l/9/d29vf7Bwe7LL31jbZlKsi1xWtrSqtJF8Ur6s81ZWYj676yoDgEAIWcteOuPQE8+cAiIZRozgOXbKsqo97hIQQ7Sopq/aiyDCqC9gDbjFKMJKG2x4Q9dBcKx4y2kSN4oDVY9CPjOLZDGhd/HUYSYATkI4EsSnp3KgdtYvRwf6PK02qMzRJdsIyZD4Hv47/XAiTGCJugUEh"
						+ "jBq400QIUcD8CI5vnGWMeEoNbhCrW9qGPK7nLNJrQcVm158IVs6ZWTKt3I7Ut97c4Q9Cq+4AyuzLtHLNnH72XMh9Jy7MIcxuvSDu7rP9ZMnXu3Hm5Or6RYwv9yF9TEX9uK5jJKfiJyMa2T61/XG9L3QWyiWjoy9w8Q1TKe3bNgIAAA==</CDATA></REPORT></REPORTLIST>";
		final ReportDataList auxDataReport = new XmlMapper().readValue(auxDataReportXml, ReportDataList.class);
		NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService, ipService,
				targetService, ruleService, reportService, scanPolicyService, reportCreateService, scanstatus, auxDataReport, eventList, null, new ArrayList<>());

		assertThat(statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM twasfindings"))).isEqualTo(4);
		assertThat(statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM twasfindings WHERE data = ARRAY['key1', 'value1', 'key2', 'value2']"))).isEqualTo(1);
		assertThat(eventList).hasSize(0);
	}

	@Test
	public void testSaveScaleReport() throws SQLException {
		final TestUserDetails testUser = new TestUserDetails(false);
		addUser(testUser, statementExecutor);

		final Integer customerId = testUtil.insertCustomer(testUser.getId());

		final Instant scanDate = Instant.now();
		final TestScanConfiguration scanConfiguration = TestScanConfiguration.builder()
				.customerId(customerId)
				.name("Test")
				.template(ScanTemplate.SCALE)
				.configuration("{\"template\": \"SCALE_SCAN\"}")
				.created(Instant.now())
				.createdBy("Son")
				.createdById(customerId)
				.updated(Instant.now())
				.updatedBy("Son")
				.updatedById(customerId)
				.build();
		final Integer scanConfigurationId = testUtil.insertScanConfiguration(scanConfiguration);

		final long scanId = statementExecutor.getLong(new NativeSqlStatement(
				"INSERT INTO scanlogs (status, template, customerid, createdbyid, started, ended, scanconfigurationid) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
				ScanLogStatus.FINISHED, ScanTemplate.SCALE, customerId, testUser.getMainUserId(), scanDate, scanDate, scanConfigurationId));

		final HashMap<String, Object> settings = new HashMap<>();
		settings.put("TARGET", "exttest.outpost24.com");

		final List<Event> eventList = new ArrayList<>();

		final ScanStatus scan = new ScanStatus();
		scan.setUserId(testUser.getMainUserId());
		scan.setTargetId(scanId);
		scan.setTarget("Test");
		scan.setService(ScanServiceType.AppsecScale);
		scan.setTemplateId(Template.AppsecScale.getId());
		scan.setSettings(XmlUtils.createParam(settings).toString());
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, -10);
		scan.setScanStarted(cal.getTime());
		scan.setScanEnded(cal.getTime());
		scan.setScanResult(ScanResult.OK);
		scan.setIpAddress("*************");

		final JSONObject match = new JSONObject();
		match.put("url", "https://exttest.outpost24.com/test");

		final ReportDataList report = new ReportDataList();
		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId("200017")
						.port("443")
						.protocol(Protocol.TCP.value)
						.matchInformation(match.toString())
						.data("This vulnerability was identified just because.")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId("219101")
						.port("443")
						.protocol(Protocol.TCP.value)
						.matchInformation(match.toString())
						.data("This vulnerability was identified just because.")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId("219101")
						.port("443")
						.protocol(Protocol.TCP.value)
						.matchInformation(match.toString())
						.data("This vulnerability was identified just because.")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.PROTOCOL.getId())
						.port("80")
						.protocol(Protocol.TCP.value)
						.data("http")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.REPORT_SETUP.getId())
						.port("WASXCRAWLEDURLS")
						.data(StringUtils.compress("Test"))
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.REPORT_SETUP.getId())
						.port("WASISSUES")
						.data("[]]")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.REPORT_SETUP.getId())
						.port("WASXPROGRESSLOG")
						.data("Test")
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId("250204")
						.port("443")
						.protocol(Protocol.TCP.value)
						.wasFinding(true)
						.build());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId("286768")
						.port("443")
						.protocol(Protocol.TCP.value)
						.wasFinding(true)
						.build());

		final XMLDoc was = new XMLDoc(SaveReportServiceImpl.ROWSET_TAG, SaveReportServiceImpl.ROW_TAG);
		was.setNewValueOf("ID", "250204");
		was.setValueOf("URL", "https://exttest.outpost24.com/test");
		was.setValueOf("METHOD", "GET");
		was.setValueOf("TYPE", WasTypes.Finding.getId());

		final XMLDoc aux = new XMLDoc(SaveReportServiceImpl.ROWSET_TAG, SaveReportServiceImpl.ROW_TAG);
		aux.setNewValueOf("KEY", "Test");
		aux.setValueOf("VALUE", "Test");

		was.setNewValueOf("ID", "286768");
		was.setValueOf("URL", "https://exttest.outpost24.com/test");
		was.setValueOf("METHOD", "POST");
		was.setValueOf("AUXDATA", StringUtils.compress(aux.toString()));
		was.setValueOf("POST", "Test");
		was.setValueOf("MATCH", "Test");
		was.setValueOf("MATCHSTART", "1");
		was.setValueOf("MATCHLENGTH", "2");
		was.setValueOf("TYPE", WasTypes.Finding.getId());

		was.setNewValueOf("ID", "286768");
		was.setValueOf("URL", "https://exttest.outpost24.com/test2");
		was.setValueOf("METHOD", "POST");
		was.setValueOf("AUXDATA", StringUtils.compress(aux.toString()));
		was.setValueOf("POST", "Test");
		was.setValueOf("MATCH", "Test");
		was.setValueOf("MATCHSTART", "1");
		was.setValueOf("MATCHLENGTH", "2");
		was.setValueOf("TYPE", WasTypes.Finding.getId());

		report.getReportData()
				.add(ReportData.builder()
						.userId(testUser.getMainUserId())
						.type(ReportEntryTypes.VULNERABILITY.getId())
						.ruleId(SaveReportServiceImpl.WAS_REPORT)
						.data(StringUtils.compress(was.toString()))
						.wasFinding(true)
						.build());

		NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService,
				ipService, targetService, ruleService, reportService, scanPolicyService, reportCreateService, scan, report, eventList, null, new ArrayList<>());

		final Long[] assetIdentifierIds =
				statementExecutor.getIdsArray(
						new NativeSqlStatement("SELECT ARRAY_AGG(id::BIGINT) AS ids FROM assetidentifiers WHERE customerid = ? AND lastscanid = ?", customerId, scanId),
						"ids");
		assertThat(assetIdentifierIds.length).isEqualTo(2);
		final Long[] assetIds =
				statementExecutor.getIdsArray(
						new NativeSqlStatement("SELECT ARRAY_AGG(id::BIGINT) AS ids FROM assets WHERE customerid = ? AND lastscanid = ?", customerId, scanId), "ids");
		assertThat(assetIds.length).isOne();
		assertThat(statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(id) FROM matches WHERE customerid = ? AND assetid = ANY(?) AND lastseen = ?", customerId,
				assetIds, scanDate))).isEqualTo(4);
		final Long[] findingIds = statementExecutor.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(id::BIGINT) AS ids FROM findings WHERE customerid = ? AND assetid = ANY(?)", customerId, assetIds), "ids");
		assertThat(findingIds.length).isEqualTo(4);

		final Map<EventObject.EventObjectIdentifier, Integer> assetCreatedObjectIds = new HashMap<>();
		assetCreatedObjectIds.put(EventObjectIdentifier.ASSET, assetIds[0].intValue());
		assetCreatedObjectIds.put(EventObjectIdentifier.SCANCONFIGURATION, scanConfigurationId);
		assetCreatedObjectIds.put(EventObjectIdentifier.SCAN, (int) scanId);
		final Map<EventObject.EventObjectIdentifier, Integer> findingCreatedObjectIds = new HashMap<>();
		findingCreatedObjectIds.put(EventObjectIdentifier.FINDING, findingIds[0].intValue());
		findingCreatedObjectIds.put(EventObjectIdentifier.SCANCONFIGURATION, scanConfigurationId);
		findingCreatedObjectIds.put(EventObjectIdentifier.SCAN, (int) scanId);
		findingCreatedObjectIds.put(EventObjectIdentifier.ASSET, assetIds[0].intValue());
		assertThat(eventList).hasSize(5).extracting("trigger").contains(Trigger.ASSET_CREATED, Trigger.FINDING_CREATED);
		assertThat(eventList).extracting("objectIds").contains(assetCreatedObjectIds, findingCreatedObjectIds);

		eventList.clear();
		NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService,
				ipService, targetService, ruleService, reportService, scanPolicyService, reportCreateService, scan, report, eventList, null, new ArrayList<>());
		assertThat(eventList).hasSize(5).extracting("trigger").contains(Trigger.FINDING_SEEN);
	}
}
