package com.chilicoders.report;

import com.chilicoders.api.EventApiInterface;
import com.chilicoders.core.compliance.api.model.ComplianceRequirementType;
import com.chilicoders.core.compliance.impl.ComplianceReport;
import com.chilicoders.core.compliance.impl.ComplianceReportRepository;
import com.chilicoders.core.compliancefinding.api.ComplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.reporting.api.ReportingService;
import com.chilicoders.core.rule.RuleEngineListener;
import com.chilicoders.core.rule.api.RuleService;
import com.chilicoders.core.scandata.api.ScanPolicyService;
import com.chilicoders.core.scandata.api.ScanStatusService;
import com.chilicoders.core.scandata.api.ScanlogService;
import com.chilicoders.core.scandata.api.model.ScanStatus;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.core.user.impl.TestUserDetails;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.model.Event;
import com.chilicoders.model.LogType;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.ReportData;
import com.chilicoders.model.ReportDataList;
import com.chilicoders.model.ReportEntryTypes;
import com.chilicoders.model.Template;
import com.chilicoders.ruleengine.InstalledProductInfo;
import com.chilicoders.ruleengine.RuleEngine;
import com.chilicoders.ruleengine.ScanDataCollector;
import com.chilicoders.ruleengine.TestScanDataCollector;
import com.chilicoders.test.TestUtil;
import com.chilicoders.test.model.TestCompliancePolicy;
import com.chilicoders.test.model.TestComplianceRequirement;
import com.chilicoders.test.model.TestScanlog;
import com.chilicoders.test.model.TestTarget;
import com.chilicoders.util.xml.ParamList;
import org.json.JSONException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.times;

@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("scandatatest")
public class ComplianceReportImporterTest extends BaseSpringTestWithRollback {
	@Autowired
	private TransactionalNativeStatementExecutor statementExecutor;

	@Autowired
	private ComplianceService complianceService;

	@Mock
	private EventApiInterface eventApi;

	@Autowired
	private ConfigurationService configService;

	@Autowired
	private TestUtil testUtil;

	@Autowired
	private TargetService targetService;

	@Autowired
	private SaveReportService saveReportService;

	@Autowired
	private UserService userService;

	@Autowired
	private ScanlogService scanlogService;

	@Autowired
	private ScanStatusService scanStatusService;

	@Autowired
	private IpService ipService;

	@Autowired
	private RuleService ruleService;

	@Autowired
	private ReportingService reportService;

	@Autowired
	private ScanPolicyService scanPolicyService;

	@Autowired
	private ReportCreateService reportCreateService;

	@Autowired
	private ComplianceReportRepository complianceReportRepository;

	@PersistenceContext
	private EntityManager entityManager;

	/**
	 * Create an event listener for a user.
	 *
	 * @param event Event to listen for.
	 * @param user User details.
	 */
	private void createEventListener(final Event.O24Event event, final UserDetails user) throws SQLException {
		statementExecutor.execute(
				new NativeSqlStatement("INSERT INTO tloggings(xid, xuserxid, recipient, xrefid, itype, bactive) VALUES (nextval('tloggings_seq'), ?, ?, ?, ?, ?)",
						user.getMainUserId(), "<EMAIL>", event.getId(), LogType.Email.ordinal(), 1));
	}

	/**
	 * Create a compliance policy that disallows a certain rule.
	 *
	 * @param name Policy name
	 * @param ruleId Rule id in the report
	 * @param user User details
	 * @return Policy id.
	 */
	private long createRuleCompliancePolicy(final String name, final long ruleId, final UserDetails user) throws JSONException {
		final TestCompliancePolicy policy = TestCompliancePolicy.builder().userId(user.getMainUserId()).description("Desc").name(name).build();
		testUtil.insertCompliancePolicy(policy);
		final TestComplianceRequirement requirement1 = TestComplianceRequirement.builder()
				.policyId(policy.getId())
				.parentId(-1)
				.name("Requirement")
				.description("Test")
				.orderId(1)
				.solution("Test")
				.requirementType(ComplianceRequirementType.REQUIREMENT.ordinal())
				.settings("<ITEMS>"
						+ "<ITEM><NAME>IGNOREVULNERABILITYAVAILABILITY</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>VULNERABILITIES</NAME><VALUE>&lt;vulnerabilities&gt;&lt;vulnerability&gt;&lt;TYPE&gt;0&lt;/TYPE&gt;&lt;RULEID"
						+ "&gt;"
						+ ruleId
						+ "&lt;/RULEID&gt;&lt;RULENAME&gt;Apache Tomcat: Chunked Request Handling Denial of Service Vulnerability&lt;/RULENAME&gt;&lt;/vulnerability&gt;&lt;/vulnerabilities&gt;</VALUE></ITEM>"
						+ "<ITEM><NAME>PATCHLEVELS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>WMIQUERIES</NAME><VALUE></VALUE></ITEM><ITEM><NAME>DBQUERIES</NAME><VALUE></VALUE></ITEM></ITEMS>")
				.build();
		testUtil.insertComplianceRequirement(requirement1);
		return policy.getId();
	}

	/**
	 * Create a compliance policy.
	 *
	 * @param user User details.
	 * @return Report id.
	 */
	public TestCompliancePolicy createCompliancePolicyTestCmdExec(final UserDetails user) throws JSONException {
		final TestCompliancePolicy compliancePolicy = TestCompliancePolicy.builder().userId(user.getMainUserId()).name("Cmd").description("Description").build();
		testUtil.insertCompliancePolicy(compliancePolicy);
		testUtil.insertComplianceRequirement(TestComplianceRequirement.builder()
				.policyId(compliancePolicy.getId())
				.name("Req1")
				.description("Description")
				.level(0)
				.settings("<ITEMS><ITEM><NAME>PORTS_POLICY</NAME><VALUE>1</VALUE></ITEM><ITEM><NAME>PORTS</NAME><VALUE></VALUE></ITEM>"
						+ "<ITEM><NAME>FILECHECKS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>FILECONTENTCHECKS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>CHECKCONFIG</NAME><VALUE></VALUE></ITEM>"
						+ "<ITEM><NAME>PASSWORDCOMPLEXITY</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>REVERSIBLEPASSWORDENCRYPTION</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSGUESTDISABLED</NAME><VALUE>false</VALUE></ITEM>"
						+ "<ITEM><NAME>WINDOWSGUESTRENAMED</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSADMINDISABLED</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSADMINRENAMED</NAME><VALUE>false</VALUE></ITEM>"
						+ "<ITEM><NAME>REQUIRELOGONTOCHANGEPASSWORD</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>FORCELOGOFFWHENHOUREXPIRE</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>LSAANONYMOUSNAMELOOKUP</NAME><VALUE>false</VALUE></ITEM>"
						+ "<ITEM><NAME>USERRIGHTCONSTRAINTS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>AUDITPOLICY</NAME><VALUE></VALUE></ITEM><ITEM><NAME>ACCOUNTCHECKS</NAME><VALUE></VALUE></ITEM>"
						+ "<ITEM><NAME>REGISTRYKEYS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>WINDOWSSERVICES_POLICY</NAME><VALUE>1</VALUE></ITEM><ITEM><NAME>WINDOWSSERVICES</NAME><VALUE></VALUE></ITEM>"
						+ "<ITEM><NAME>EXCLUDEACCEPTEDVULNERABILITIES</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>IGNOREVULNERABILITYAVAILABILITY</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>VULNERABILITIES</NAME><VALUE></VALUE></ITEM>"
						+ "<ITEM><NAME>PATCHLEVELS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>WMIQUERIES</NAME><VALUE></VALUE></ITEM><ITEM><NAME>DBQUERIES</NAME><VALUE></VALUE></ITEM></ITEMS>")
				.build());
		testUtil.insertComplianceRequirement(TestComplianceRequirement.builder().policyId(compliancePolicy.getId()).name("Req2").description("Description").level(0).build());
		testUtil.insertComplianceRequirement(TestComplianceRequirement.builder()
				.policyId(compliancePolicy.getId())
				.name("Req3")
				.description("Check ports")
				.level(0)
				.settings(
						"<ITEMS><ITEM><NAME>PORTS_POLICY</NAME><VALUE>1</VALUE></ITEM><ITEM><NAME>PORTS</NAME><VALUE>80</VALUE></ITEM><ITEM><NAME>FILECHECKS</NAME><VALUE></VALUE></ITEM>"
								+ "<ITEM><NAME>FILECONTENTCHECKS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>INSTALLEDAPPLICATIONS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>COMMANDEXECUTEREQUIREONE</NAME><VALUE>false</VALUE></ITEM>"
								+ "<ITEM><NAME>COMMANDEXECUTE</NAME><VALUE>&lt;commands&gt;&lt;cmd&gt;&lt;NAME&gt;ls /&lt;/NAME&gt;&lt;EXPECT&gt;.*root.*&lt;/EXPECT&gt;&lt;INVERT&gt;false&lt;/INVERT&gt;&lt;"
								+ "DESCRIPTION&gt;Checks for root&lt;/DESCRIPTION&gt;&lt;/cmd&gt;&lt;/commands&gt;</VALUE></ITEM><ITEM><NAME>CHECKCONFIG</NAME><VALUE></VALUE></ITEM><ITEM><NAME>PASSWORDCOMPLEXITY</NAME><VALUE>false</VALUE></ITEM>"
								+ "<ITEM><NAME>REVERSIBLEPASSWORDENCRYPTION</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSGUESTDISABLED</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSGUESTRENAMED</NAME><VALUE>false</VALUE></ITEM>"
								+ "<ITEM><NAME>WINDOWSADMINDISABLED</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>WINDOWSADMINRENAMED</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>REQUIRELOGONTOCHANGEPASSWORD</NAME><VALUE>false</VALUE></ITEM>"
								+ "<ITEM><NAME>FORCELOGOFFWHENHOUREXPIRE</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>LSAANONYMOUSNAMELOOKUP</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>USERRIGHTCONSTRAINTS</NAME><VALUE></VALUE></ITEM>"
								+ "<ITEM><NAME>AUDITPOLICY</NAME><VALUE></VALUE></ITEM><ITEM><NAME>ACCOUNTCHECKS</NAME><VALUE></VALUE></ITEM><ITEM><NAME>REGISTRYKEYS</NAME><VALUE></VALUE></ITEM>"
								+ "<ITEM><NAME>WINDOWSSERVICES_POLICY</NAME><VALUE>1</VALUE></ITEM><ITEM><NAME>WINDOWSSERVICES</NAME><VALUE></VALUE></ITEM><ITEM><NAME>EXCLUDEACCEPTEDVULNERABILITIES</NAME><VALUE>false</VALUE></ITEM>"
								+ "<ITEM><NAME>IGNOREVULNERABILITYAVAILABILITY</NAME><VALUE>false</VALUE></ITEM><ITEM><NAME>VULNERABILITIES</NAME><VALUE></VALUE></ITEM><ITEM><NAME>PATCHLEVELS</NAME><VALUE></VALUE></ITEM>"
								+ "<ITEM><NAME>WMIQUERIES</NAME><VALUE></VALUE></ITEM><ITEM><NAME>DBQUERIES</NAME><VALUE></VALUE></ITEM></ITEMS>")
				.build());
		return compliancePolicy;
	}

	/**
	 * Creates a compliance report for the main user and makes sure its available.
	 *
	 * @param user User details.
	 * @param targetId Target id.
	 */
	private void createBasicComplianceReport(final UserDetails user, final long targetId) throws Exception {
		final long scheduleId = testUtil.insertSchedule(user.getMainUserId(), "Test schedule");
		final List<com.chilicoders.event.model.Event> eventList = new ArrayList<>();
		final List<Event> eventV1List = new ArrayList<>();

		final long reportId = saveReport(user, targetId, scheduleId, eventList, eventV1List);
		assertTrue(reportId > 0);
		assertEquals(0, eventList.size());
		assertEquals(2, eventV1List.size());

		final long scanlogId = statementExecutor.getLong(new NativeSqlStatement("SELECT xid FROM tscanlogs WHERE itype = 0"));
		final long scanjobId = statementExecutor.getLong(new NativeSqlStatement("SELECT xid FROM tscanlogs WHERE itype = 20"));
		statementExecutor.execute(new NativeSqlStatement(
				"INSERT INTO tcompliancescandata(xid, scanlogxid, key, type, port, scanjobxid) VALUES (nextval('tcompliancescandata_seq'), ?, 'fact/port', 'open', 80, ?)",
				scanlogId, scanjobId));
	}

	@Test
	public void updateComplianceReport() throws Exception {
		final TestUserDetails testUser = new TestUserDetails(false);
		addUser(testUser, statementExecutor);
		configService.setProperty(ConfigKeys.ConfigurationBooleanKey.dev_mode.getConfigKey(), "false");

		final long targetId = testUtil.insertTarget(TestTarget.builder().userId(testUser.getMainUserId()).ipAddress("*******").build());
		createBasicComplianceReport(testUser, targetId);
		final TestCompliancePolicy cmdPolicy = createCompliancePolicyTestCmdExec(testUser);

		final Long scanjobId = statementExecutor.getLong(new NativeSqlStatement("SELECT xid FROM tscanlogs WHERE itype = 20"));
		final long reportId =
				ComplianceReportImporter.createReport(statementExecutor, complianceService, configService, testUser, cmdPolicy, new Long[] {targetId}, new Long[] {-1L},
						scanjobId, testUser.getMainUserId());

		entityManager.clear();
		final ComplianceReport report = complianceReportRepository.findById(reportId).orElseThrow(() -> new IllegalStateException("No report created"));
		assertThat(report.getCompliant()).isZero();
		assertThat(report.getNotCompliant()).isOne();

		final ScanStatus status = new ScanStatus();
		status.setUserId(testUser.getMainUserId());
		status.setScanJobId(scanjobId);
		status.setTargetId(statementExecutor.getLong(new NativeSqlStatement("SELECT xipxid FROM treportentrys")));

		final ParamList pl = new ParamList();
		pl.setValueOf("NAME", "COMPLIANCETYPES");
		pl.setValueOf("VALUE", "1,2,3,4,5,6,7,8,9,10,11");
		status.setSettings(pl.toString());

		final ReportData data = ReportData.builder()
				.type(ReportEntryTypes.COMPLIANCE.getId())
				.data("<ITEMLIST><ITEM><KEY>fact/compliance</KEY><TYPE>cmd/exec</TYPE><FACT_TYPE>2</FACT_TYPE><FACT>dHJ1ZQ==</FACT><PATH>ls /;.*root.*;false;</PATH><PROTO>tcp</PROTO><PORT>80</PORT></ITEM></ITEMLIST>")
				.build();
		final ReportDataList reportList = new ReportDataList();
		reportList.getReportData().add(data);
		ComplianceReportImporter.saveComplianceUpdate(statementExecutor, scanlogService, complianceService, status, reportList);

		statementExecutor.execute(new NativeSqlStatement("DELETE FROM tcompliancereports"));

		final long compliantReportId =
				ComplianceReportImporter.createReport(statementExecutor, complianceService, configService, testUser, cmdPolicy, new Long[] {targetId}, new Long[] {-1L},
						scanjobId, testUser.getMainUserId());

		entityManager.clear();
		final ComplianceReport compliantReport = complianceReportRepository.findById(compliantReportId).orElseThrow(() -> new IllegalStateException("No report created"));
		assertThat(compliantReport.getCompliant()).isOne();
		assertThat(compliantReport.getNotCompliant()).isZero();
	}

	@Test
	public void testSendCompliance() throws Exception {
		final TestUserDetails testUser = new TestUserDetails(false);
		addUser(testUser, statementExecutor);

		final List<com.chilicoders.event.model.Event> eventList = new ArrayList<>();
		final List<Event> eventV1List = new ArrayList<>();
		final long targetId = testUtil.insertTarget(TestTarget.builder().userId(testUser.getMainUserId()).ipAddress("*******").build());
		final long scheduleId = testUtil.insertSchedule(testUser.getMainUserId(), "Test schedule");

		statementExecutor.execute(new NativeSqlStatement("UPDATE tuserdatas SET compliancesenabled = -1 WHERE xid = ?", targetId));
		createEventListener(Event.O24Event.TargetCompliant, testUser);

		final long id1 = createRuleCompliancePolicy("NotCompliant", 1213470, testUser);

		final long reportId = saveReport(testUser, targetId, scheduleId, eventList, eventV1List);

		runCompliance(testUser, targetId, reportId, 1, Event.O24Event.TargetNotCompliant);
		assertEquals(0, eventList.size());
		assertEquals(2, eventV1List.size());

		statementExecutor.execute(new NativeSqlStatement("DELETE FROM tcompliancepolicies WHERE xid = ?", id1));
		createRuleCompliancePolicy("IsCompliant", 1, testUser);

		runCompliance(testUser, targetId, reportId, 2, Event.O24Event.TargetCompliant);
		assertEquals(0, eventList.size());
		assertEquals(2, eventV1List.size());
	}

	/**
	 * Save a report used for testing.
	 *
	 * @param testUser User details
	 * @param targetId Target id
	 * @param scheduleId Schedule id
	 * @param eventList Event list
	 * @param eventV1List Event V1 list
	 * @return Report id
	 */
	private long saveReport(final UserDetails testUser, final long targetId, final long scheduleId, final List<com.chilicoders.event.model.Event> eventList, final List<Event> eventV1List) throws Exception {
		final ReportDataList report = new ReportDataList();
		try (final ScanDataCollector collector = new TestScanDataCollector(statementExecutor,
				new InstalledProductInfo("apache/tomcat", Protocol.TCP, 80, null, null, "7.0.25", null, null, null, false, false, null, false, false, false, null, 0, 0, null,
						null, false, null))) {

			RuleEngine.executeRules(collector, new RuleEngineListener(report, testUser.getMainUserId(), false), null, false, false, false, false, true, null, false,
					false);
		}

		final long scanjobId = testUtil.insertScanlog(TestScanlog.builder()
				.userId(testUser.getMainUserId())
				.scanEndDate(new Date())
				.scanStartDate(new Date())
				.scheduleId(scheduleId)
				.templateId(2)
				.type(20)
				.targetId(targetId)
				.host("*******")
				.complianceScan(true)
				.build());

		final long scanlogId = testUtil.insertScanlog(TestScanlog.builder()
				.userId(testUser.getMainUserId())
				.scanEndDate(new Date())
				.scanStartDate(new Date())
				.scheduleId(scheduleId)
				.templateId(2)
				.type(0)
				.targetId(targetId)
				.host("*******")
				.complianceScan(true)
				.scanjobId(scanjobId)
				.build());

		final ScanStatus scan = new ScanStatus();
		scan.setUserId(testUser.getMainUserId());
		scan.setTargetId(targetId);
		scan.setTemplateId(Template.Normal.getId());
		scan.setScheduleId(scheduleId);
		scan.setTarget("*******");
		scan.setScanEnded(new Date());
		scan.setScanlogId(scanlogId);
		scan.setScanJobId(scanjobId);

		return NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService,
				ipService, targetService, ruleService, reportService, scanPolicyService, reportCreateService, scan, report, eventList, null, eventV1List);
	}

	/**
	 * Run compliance and check results.
	 *
	 * @param user User details
	 * @param targetId Target id
	 * @param reportId Report id
	 * @param expectedEvents Expected number of events
	 * @param expectedEvent Expected event type in the last event
	 */
	private void runCompliance(final TestUserDetails user, final long targetId, final long reportId, final int expectedEvents, final Event.O24Event expectedEvent)
			throws Exception {
		ComplianceReportImporter.sendCompliance(statementExecutor, complianceService, eventApi, configService, user, user.getMainUserId(),
				targetService.getById(user, targetId), statementExecutor.getLong(new NativeSqlStatement("SELECT xscanjobxid FROM treportentrys WHERE xid = ?", reportId)));
		final ArgumentCaptor<Event> argumentCaptor2 = ArgumentCaptor.forClass(Event.class);
		Mockito.verify(eventApi, times(expectedEvents)).handleEvent(argumentCaptor2.capture());
		final List<Event> passedArguments2 = argumentCaptor2.getAllValues();
		assertThat(passedArguments2.get(expectedEvents - 1).getEvent()).isEqualTo(expectedEvent);
		assertThat(passedArguments2.get(expectedEvents - 1).getTargetId()).isEqualTo(targetId);
	}
}
