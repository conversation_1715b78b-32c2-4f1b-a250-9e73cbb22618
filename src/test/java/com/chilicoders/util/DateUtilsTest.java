package com.chilicoders.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.Calendar;
import java.util.Date;

import org.junit.Test;

public class DateUtilsTest {

	@Test
	public void formatTimeDate() {
		final Calendar cal = Calendar.getInstance();
		cal.set(2015, 11, 24, 15, 0);

		assertEquals(null, DateUtils.formatTimeDate(null));
		assertEquals("2015-12-24 15:00", DateUtils.formatTimeDate(cal.getTime()));
	}

	@Test
	public void formatDateTimezone() {
		final Calendar cal = Calendar.getInstance();
		cal.set(2015, 11, 24, 15, 0, 0);

		assertEquals(null, DateUtils.formatDateTimezone(null, null, null));
		assertEquals("2015-12-24 15:00", DateUtils.formatDateTimezone(cal.getTime(), null, null));
		assertEquals("2015-12-24 15:00:00", DateUtils.formatDateTimezone(cal.getTime(), "yyyy-MM-dd HH:mm:ss", cal.getTimeZone().getID()));
	}

	@Test
	public void getDateFormat() {
		assertEquals("dd-MM-yyyy", DateUtils.getDateFormat("d-m-Y"));
		assertEquals("MM/dd/yyyy", DateUtils.getDateFormat("m/d/Y"));
		assertEquals("yyyy-MM-dd", DateUtils.getDateFormat(null));
	}

	@Test
	public void getTimeFormat() {
		assertEquals("hh:mm a", DateUtils.getTimeFormat("h:i a"));
		assertEquals("HH:mm", DateUtils.getTimeFormat(null));
	}

	@Test
	public void parseDateFormat() {
		assertEquals(null, DateUtils.parseDateFormat(null, null, null));
		assertEquals(null, DateUtils.parseDateFormat("x", null, null));

		final Calendar cal = Calendar.getInstance();
		final Date date = DateUtils.parseDateFormat("2015-12-24 15:00", null, cal.getTimeZone().getID());
		cal.setTime(date);
		assertEquals(24, cal.get(Calendar.DAY_OF_MONTH));
	}

	@Test
	public void findDateTimeFormat() {
		assertEquals("yyyy-MM-dd HH:mm", DateUtils.findDateTimeFormat("x"));
		assertEquals("yyyy-MM-dd HH:mm", DateUtils.findDateTimeFormat("2015-12-24 15:00"));
	}

	@Test
	public void parseTimeDate() {
		assertEquals(null, DateUtils.parseTimeDate(null));

		final Calendar cal = Calendar.getInstance();
		final Date date = DateUtils.parseTimeDate("2015-12-24 15:00");
		cal.setTime(date);
		assertEquals(24, cal.get(Calendar.DAY_OF_MONTH));
	}

	@Test
	public void formatLongTimeDate() {
		final Calendar cal = Calendar.getInstance();
		cal.set(2015, 11, 24, 15, 0, 0);

		assertEquals(null, DateUtils.formatLongTimeDate(null));
		assertEquals("2015-12-24 15:00:00", DateUtils.formatLongTimeDate(cal.getTime()));
	}

	@Test
	public void parseLongTimeDate() {
		assertEquals(null, DateUtils.parseLongTimeDate("x"));

		final Calendar cal = Calendar.getInstance();
		final Date date = DateUtils.parseLongTimeDate("2015-12-24 15:00:00");
		cal.setTime(date);
		assertEquals(24, cal.get(Calendar.DAY_OF_MONTH));
	}

	@Test
	public void formatDate() {
		final Calendar cal = Calendar.getInstance();
		cal.set(2015, 11, 24, 15, 0, 0);

		assertEquals(null, DateUtils.formatDate(null));
		assertEquals("2015-12-24", DateUtils.formatDate(cal.getTime()));
	}

	@Test
	public void parseDate() {
		assertEquals(null, DateUtils.parseDate(null));
		assertEquals(null, DateUtils.parseDate("x"));

		final Calendar cal = Calendar.getInstance();
		final Date date = DateUtils.parseDate("2015-12-24");
		cal.setTime(date);
		assertEquals(24, cal.get(Calendar.DAY_OF_MONTH));
	}

	@Test
	public void getCurrentDate() {
		assertTrue(DateUtils.getCurrentTimeDate(1).length() > 10);
		assertTrue(DateUtils.getCurrentDate().length() == 10);
		assertTrue(DateUtils.getCurrentDate("yyyy-MM-dd").length() == 10);
	}

	@Test
	public void getDateOffset() {
		assertTrue(DateUtils.getDateOffset(null, 0, true).length() == 16);
		assertTrue(DateUtils.getDateOffset("2015-12-25 15:00", "yyyy-MM-dd HH:mm", null, 1, false).length() == 16);
		assertTrue(DateUtils.getDateOffset("2015-12-26 15:00", "yyyy-MM-dd HH:mm", null, 1, false).length() == 16);
		assertTrue(DateUtils.getDateOffset("x", "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm", 1, true).length() == 16);
	}

	@Test
	public void getDateTimeOffset() {
		final Calendar cal = Calendar.getInstance();
		cal.set(2015, 11, 24, 15, 0, 0);

		final Date date = DateUtils.getDateTimeOffset(cal.getTime(), 0);
		cal.setTime(date);
		assertEquals(24, cal.get(Calendar.DAY_OF_MONTH));

		assertTrue(DateUtils.getDateTimeOffset("2015-12-24 15:00", "yyyy-MM-dd HH:mm", 0).length() == 16);
		assertTrue(DateUtils.getDateTimeOffset("x", "yyyy-MM-dd HH:mm", 0).length() == 16);
	}

	@Test
	public void getTimezone() {
		assertEquals("GMT", DateUtils.getTimezone(0));
		assertEquals("GMT+1:30", DateUtils.getTimezone(1.5));
		assertEquals("GMT-1", DateUtils.getTimezone(-1));
	}

	@Test
	public void getHighestDate() {
		final String today = DateUtils.getCurrentDate();
		final String tomorrow = DateUtils.getCurrentDate("yyyy-MM-dd", 24 * 60 * 60 * 1000);

		final Calendar cal = Calendar.getInstance();
		assertEquals(today, DateUtils.getHighestDate("yyyy-MM-dd", cal.getTime()));
		cal.add(Calendar.DAY_OF_YEAR, 1);
		assertEquals(tomorrow, DateUtils.getHighestDate("yyyy-MM-dd", cal.getTime()));
		cal.set(2015, 11, 24, 15, 0, 0);
		assertEquals(today, DateUtils.getHighestDate("yyyy-MM-dd", cal.getTime()));
		assertEquals(today, DateUtils.getHighestDate("yyyy-MM-dd", null));
	}

	@Test
	public void leftPad() {
		assertEquals("test", StringUtils.leftPad("test", "", 8));
		assertEquals("0000test", StringUtils.leftPad("test", "0", 8));
	}

}
