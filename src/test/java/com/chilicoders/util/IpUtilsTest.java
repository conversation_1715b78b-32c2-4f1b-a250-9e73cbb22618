package com.chilicoders.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import com.chilicoders.bl.ScannerBusiness;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.HostInfo;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class IpUtilsTest extends XMLAPITestBase {

	@Test
	public void ipCountTest() throws SQLException {
		assertEquals(0, IpUtils.getIpCount(getConnection(), null));
		assertEquals(0, IpUtils.getIpCount(getConnection(), ""));
		assertEquals(1, IpUtils.getIpCount(getConnection(), "127.0.0.1"));
		assertEquals(65536, IpUtils.getIpCount(getConnection(), "127.0.0.1/16"));
		assertEquals(2, IpUtils.getIpCount(getConnection(), "127.0.0.1\n\r*********"));
		assertEquals(Long.MAX_VALUE, IpUtils.getIpCount(getConnection(), "1::1/56"));
		assertEquals(Long.MAX_VALUE, IpUtils.getIpCount(getConnection(), "1::1/56\n127.0.0.1"));
		assertEquals(1, IpUtils.getIpCount(getConnection(), "*************||test-outpost24.com"));
	}

	@Test
	public void testCidr() {
		assertFalse(IpUtils.isCidr(getConnection(), null));
		assertFalse(IpUtils.isCidr(getConnection(), ""));
		assertFalse(IpUtils.isCidr(getConnection(), "127.0.0.1"));

		assertFalse(IpUtils.isCidr(getConnection(), "127.0.0.1/-1"));
		assertTrue(IpUtils.isCidr(getConnection(), "127.0.0.1/0"));
		assertTrue(IpUtils.isCidr(getConnection(), "127.0.0.1/32"));
		assertFalse(IpUtils.isCidr(getConnection(), "333.0.0.1/32"));
		assertFalse(IpUtils.isCidr(getConnection(), "127.0.0.1/33"));

		assertFalse(IpUtils.isCidr(getConnection(), "ffc0::1/-1"));
		assertTrue(IpUtils.isCidr(getConnection(), "ffc0::1/0"));
		assertTrue(IpUtils.isCidr(getConnection(), "ffc0::1/128"));
		assertFalse(IpUtils.isCidr(getConnection(), "ffc0::1/129"));

		assertFalse(IpUtils.isCidr(getConnection(), "127.0.0.1-127.0.0.10"));
		assertFalse(IpUtils.isCidr(getConnection(), "exttest.outpost24.com"));
	}

	@Test(expected = ParamValidationException.class)
	public void testMissingScanner() throws ParamValidationException, SQLException {
		assertEquals("(ipaddress = '127.0.0.1' AND scannerid=1000)",
				IpUtils.getTargetRangeSelection(getConnection(), new ArrayList<>(), "127.0.0.1<Scanner", true, -1).get(0));
	}

	@Test
	public void tooLargeRange() throws ParamValidationException, SQLException {
		IpUtils.getTargetRangeSelection(getConnection(), null, "1000::0/64", true, 0);
	}

	@Test
	public void testTargetSelection() throws ParamValidationException, SQLException, IOException {
		setupDatabaseForTest(true);

		DbObject.executeUpdate(getConnection(), "INSERT INTO tscanners (xid, name, xuserxid, ipaddress, mode, approved, inactive) VALUES (?, ?, ?, '127.0.0.1', 1, 1, 0)",
				1000, "Scanner", XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		assertEquals("(ipaddress = '127.0.0.1')", IpUtils.getTargetRangeSelection(getConnection(), null, "127.0.0.1", true, -1).get(0));

		assertEquals("(ipaddress = '127.0.0.1' AND scannerid=1000)", IpUtils.getTargetRangeSelection(getConnection(),
						ScannerBusiness.getAvailableScanners(getConnection(), LoggedOnUser.getById(LoggedOnUser.class, getConnection(), TEST_USER_ID)), "127.0.0.1<Scanner", true, -1)
				.get(0));

		assertEquals("(ipaddress >= '127.0.0.1' AND ipaddress <= '127.0.0.100') OR (ipaddress = '***********')",
				IpUtils.getTargetRangeSelection(getConnection(), null, "127.0.0.1-127.0.0.100\n***********", true, -1).get(0));

		assertEquals("(ipaddress >= '127.0.0.1' AND ipaddress <= '127.0.0.100' AND scannerid=3) OR (ipaddress = '***********' AND scannerid=3)",
				IpUtils.getTargetRangeSelection(getConnection(), null, "127.0.0.1-127.0.0.100\n***********", false, 3).get(0));

		assertEquals("(ipaddress = '127.0.0.1')", IpUtils.getTargetRangeSelection(getConnection(), null, "127.0.0.1\r\n\r\n", true, -1).get(0));
	}

	@Test
	public void testComment() throws ParamValidationException, SQLException {
		final List<HostInfo> list = IpUtils.getIpList(getConnection(), null, "127.0.0.1,********* % Comment", false, false);
		assertEquals(2, list.size());
		assertEquals("% Comment", list.get(0).getComment());
		assertEquals("% Comment", list.get(1).getComment());
	}

	@Test
	public void testInputWithSpaces() throws ParamValidationException, SQLException {
		final List<HostInfo> list = IpUtils.getIpList(getConnection(), null, "127.0.0.1, ********* % Comment", false, false);
		assertEquals(2, list.size());
		assertEquals("127.0.0.1", list.get(0).getTarget());
		assertEquals("*********", list.get(1).getTarget());
	}

	@Test
	public void testInputWithScanner() throws ParamValidationException, SQLException {
		final List<ScannerImpl> scanners = new ArrayList<>();
		final ScannerImpl scanner = new ScannerImpl();
		scanner.setId(1000);
		scanner.setName("Scanner");
		scanners.add(scanner);

		List<HostInfo> list = IpUtils.getIpList(getConnection(), scanners, "127.0.0.1<Scanner", false, false);
		assertEquals(1, list.size());
		assertEquals("127.0.0.1", list.get(0).getTarget());

		try {
			IpUtils.getIpList(getConnection(), scanners, "*********<", false, false);
		}
		catch (final ParamValidationException e) {
			assertEquals("Scanner does not exist", e.getMessage());
		}

		list = IpUtils.getIpList(getConnection(), scanners, "*********", false, false);
		assertEquals(1, list.size());
		assertEquals("*********", list.get(0).getTarget());
	}

	@Test
	public void testExclude() throws SQLException {
		final IpService ipService = ServiceProvider.getIpService(getConnection());
		assertEquals("", ipService.excludeTargets("127.0.0.1", "127.0.0.1", false));
		assertEquals("127.0.0.1-*********\nwww.google.com\n", ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.11", false));
		assertEquals("*********\n", ipService.excludeTargets("127.0.0.1-*********", "127.0.0.1", false));
		assertEquals("*********\n", ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.1\nwww.google.com", false));
		assertEquals("127.0.0.1\n*********\n", ipService.excludeTargets("127.0.0.1-*********", "*********", false));
		assertEquals("2001::1\n2001::3\n", ipService.excludeTargets("2001::1-2001::3", "2001::2", false));
		assertEquals("*********\n*********\n", ipService.excludeTargets("127.0.0.1\n*********\n*********\n127.0.0.4", "127.0.0.1\n127.0.0.4", false));
	}

	@Test
	public void testLowMax() throws SQLException {
		assertEquals("************", IpUtils.getLowIp(getConnection(), "************/31"));
		assertEquals("************", IpUtils.getMaxIp(getConnection(), "************/31"));
		assertEquals("************", IpUtils.getLowIp(getConnection(), "************-************"));
		assertEquals("************", IpUtils.getMaxIp(getConnection(), "************-************"));
		assertNull(IpUtils.getMaxIp(getConnection(), ""));
		assertNull(IpUtils.getMaxIp(getConnection(), "test.com", 0));
		assertNull(IpUtils.getLowIp(getConnection(), ""));
		assertNull(IpUtils.getLowIp(getConnection(), "test.com", 0));
	}

	@Test
	public void testValidIp() {
		assertTrue(IpUtils.isValid("***************"));
		assertTrue(IpUtils.isValid("2001:610:1a0:1016:145:48:16::"));
		assertFalse(IpUtils.isValid("20y1:610:1a0:1016:145:48:16::"));
		assertFalse(IpUtils.isValid("192.168.203.257"));
	}

	@Test
	public void testValidHostname() throws SQLException {
		assertFalse(IpUtils.isHostname(getConnection(), null));
		assertTrue(IpUtils.isHostname(getConnection(), "test.com"));
	}

	@Test
	public void testValidSNTarget() {
		assertFalse(IpUtils.isSnSysId(null));
		assertTrue(IpUtils.isSnSysId("SNTarget9d385017c611228701d22104cc95c371"));
	}

	@Test
	public void testValidAmazonTarget() {
		assertFalse(IpUtils.isInstanceId(null));
		assertTrue(IpUtils.isInstanceId("@test"));
		assertNull(IpUtils.parseInstanceId(""));
		assertEquals("test", IpUtils.parseInstanceId("@test"));
	}

	@Test
	public void testCleanIpList() throws SQLException {
		assertEquals("*************\n*************-*************1\n192.168.200.0-***************\n",
				IpUtils.cleanTargets(getConnection(), "***************\n192.168.200.04-*************1\n***************/24"));
	}

	@Test
	public void testIpValue() throws SQLException {
		assertEquals(IpUtils.getIpValue(getConnection(), "*************"), 3232286724L);
		assertTrue(IpUtils.getIpValue(getConnection(), "@test") > 0);
		final long value1 = IpUtils.getIpValue(getConnection(), "test.com");
		assertEquals(IpUtils.getIp(getConnection(), value1), "test.com");
		final long value2 = IpUtils.getIpValue(getConnection(), "*************");
		final long value3 = IpUtils.getIpValue(getConnection(), "*************");
		final Long[] ipValues = new Long[] {value2, value3};
		assertEquals("*************,*************", IpUtils.getIps(getConnection(), ipValues));
	}

	@Test
	public void testIpHostnameConversion() throws SQLException {
		boolean canResolveHostnames = true;
		try {
			InetAddress.getAllByName("exttest.outpost24.com");
		}
		catch (final UnknownHostException ex) {
			canResolveHostnames = false;
		}

		if (canResolveHostnames) {
			final IpService ipService = ServiceProvider.getIpService(getConnection());
			assertNull(ipService.convertHostnameToIp(""));
			assertEquals("127.0.0.1", ipService.convertHostnameToIp("127.0.0.1"));
			assertNull(ipService.convertHostnameToIp("invalid.outpost24.com"));
			final String ip = ipService.convertHostnameToIp("outscan.outpost24.com");
			assertNotNull(ip);

			assertNull(IpUtils.convertHostnamesToIp(getConnection(), null));
			assertNotNull(IpUtils.convertHostnamesToIp(getConnection(), "google.com\noutpost24.com"));

			assertEquals("", IpUtils.convertIpToHostname(""));

			assertNotNull(IpUtils.convertIpToHostname("2001:67c:1084:1::43"));
			assertEquals("", IpUtils.convertIpToHostname("outpost24.com"));
		}
	}

	@Test
	public void doMacLookup() throws SQLException, IOException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}
		setupDatabaseForTest(true);

		assertEquals("52:54:00:3c:17:98", IpUtils.doMacLookup("outpost24.com"));
	}

	@Test
	public void testGetHostsBetween() throws SQLException {
		assertEquals(254, IpUtils.getHostsBetween("*********", "*********").length);
		assertEquals(4, IpUtils.getHostsBetween("::1", "::4").length);
	}

	@Test
	public void testAnyIpInList() throws SQLException {
		final String iplist = "***********,***********,***********-***********0\n***********/24,testhostname.com,@instance_id";
		final IpService ipService = ServiceProvider.getIpService(getConnection());
		assertFalse(ipService.isAnyIpInList(new String[] {"***********1"}, iplist));
		assertFalse(ipService.isAnyIpInList(new String[] {"*************", "*************", "***********00", "@not_existing_instance_id", "notest.com"}, iplist));
		assertFalse(ipService.isAnyIpInList(new String[] {null, null, null, null}, iplist));
		assertFalse(ipService.isAnyIpInList(new String[] {"***********", "*************"}, iplist));
		assertFalse(ipService.isAnyIpInList(new String[] {"notest.com", "@not_existing_instance_id"}, iplist));

		assertTrue(ipService.isAnyIpInList(new String[] {"*************", "*************", "***********00", "@not_existing_instance_id", "testhostname.com"}, iplist));
		assertTrue(ipService.isAnyIpInList(new String[] {"*************", "***********00", "@instance_id"}, iplist));
		assertTrue(ipService.isAnyIpInList(new String[] {"***********", "*************", "***********00", "@not_existing_instance_id", "testhostname.com"}, iplist));
		assertTrue(ipService.isAnyIpInList(new String[] {"@not_existing_instance_id", "testhostname.com", "***********", "*************", "***********"}, iplist));
		assertTrue(ipService.isAnyIpInList(new String[] {"***********"}, iplist));
		assertTrue(ipService.isAnyIpInList(new String[] {"***********", "***********"}, iplist));
	}
}
