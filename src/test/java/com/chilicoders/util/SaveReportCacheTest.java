package com.chilicoders.util;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.ExecutionException;

import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.service.ServiceProvider;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class SaveReportCacheTest extends XMLAPITestBase {
	@Test
	public void testCache() throws SQLException, IOException, ExecutionException, SAXException {
		setupDatabaseForTest(false);
		final SaveReportCache cache = SaveReportCache.getInstance();
		final long scheduleId = createSchedule(MAINUSER_TOKEN, "3.3.3.3", 0);
		assertNull(cache.getSchedule(ServiceProvider.getSchedulingService(getConnection()), TEST_USER_ID, 99999));
		assertNotNull(cache.getSchedule(ServiceProvider.getSchedulingService(getConnection()), TEST_USER_ID, scheduleId));

		assertNotNull(cache.getUserFeatures(ServiceProvider.getUserService(getConnection()), TEST_USER_ID));
		assertNotNull(cache.getRiskChanges(ServiceProvider.getReportingService(getConnection()), TEST_USER_ID));
	}
}