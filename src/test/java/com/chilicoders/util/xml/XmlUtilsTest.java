package com.chilicoders.util.xml;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Vector;

import org.junit.Test;

public class XmlUtilsTest {

	@Test
	public void escapeXmlText() {
		assertEquals(null, XmlUtils.escapeXmlText(null));
		assertEquals("x&#15;", XmlUtils.escapeXmlText("x" + (char) 15));
		assertEquals("&amp;", XmlUtils.escapeXmlText("&"));
		assertEquals("&apos;", XmlUtils.escapeXmlText("'"));
		assertEquals("&quot;", XmlUtils.escapeXmlText("\""));
		assertEquals("&lt;", XmlUtils.escapeXmlText("<"));
		assertEquals("&gt;", XmlUtils.escapeXmlText(">"));
	}

	@Test
	public void isValidXml() {
		assertEquals(true, XmlUtils.isValidXml(""));
		assertEquals(true, XmlUtils.isValidXml("<xml></xml>"));
		assertEquals(false, XmlUtils.isValidXml("<xml>" + (char) 15 + "</xml>"));
	}

	@Test
	public void cleanXMLName() {
		assertEquals("", XmlUtils.cleanXMLName(""));
		assertEquals("test123", XmlUtils.cleanXMLName("test=123"));
	}

	@Test
	public void cleanXML() {
		assertEquals("", XmlUtils.cleanXML(""));
		assertEquals("<xml></xml>", XmlUtils.cleanXML("<xml>" + (char) 15 + "</xml>"));
		assertEquals("<xml></xml>", XmlUtils.cleanXML((char) 15 + "<xml></xml>"));
		assertEquals("<xml></xml>", XmlUtils.cleanXML("<xml></xml>" + (char) 15));
	}

	@Test
	public void testParam() {
		final HashMap<String, Object> map = new HashMap<>();
		map.put("string", "test");
		map.put("integer", Integer.valueOf(5));
		map.put("null", null);
		final HashMap<String, String> hash = new HashMap<>();
		hash.put("test", "test");
		map.put("hash", hash);
		final Hashtable<String, String> table = new Hashtable<>();
		table.put("test", "test");
		map.put("table", table);
		final Response response = new Response();
		response.setNewValueOf("test", "test");
		map.put("response", response);
		map.put("date", new Date());
		final ArrayList<String> list = new ArrayList<>();
		list.add("test");
		map.put("list", list);
		@SuppressWarnings("unchecked")
		final ArrayList<String>[] lists = (ArrayList<String>[]) new ArrayList<?>[] {list};
		map.put("lists", lists);
		map.put("strings", new String[] {"test"});
		map.put("ints", new int[] {5});
		map.put("bytes", new byte[] {0});
		final Vector<String> vector = new Vector<>();
		vector.add("test");
		map.put("vector", vector);
		@SuppressWarnings("unchecked")
		final Vector<String>[] vectors = (Vector<String>[]) new Vector<?>[] {vector};
		map.put("vectors", vectors);

		final ParamList paramList = XmlUtils.createParam(map);
		assertTrue(paramList.toString().length() > 100);

		final HashMap<String, Object> params = XmlUtils.getParams(paramList);
		assertEquals(map.get("string"), params.get("string"));
	}

	@Test
	public void getDocument() {
		assertEquals(XmlUtils.getDocument(null), null);
		assertTrue(XmlUtils.getDocument("<xml></xml>") != null);
		assertTrue(XmlUtils.getDocument("<xml></xml>", false, false) != null);
		try {
			assertTrue(XmlUtils.getDocument("", true) == null);
			fail("Error in XML");
		}
		catch (final Exception e) {
			//
		}
	}

}
