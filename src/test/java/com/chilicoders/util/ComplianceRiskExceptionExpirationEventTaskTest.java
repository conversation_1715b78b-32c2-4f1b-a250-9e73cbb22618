package com.chilicoders.util;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.xml.bind.JAXBException;

import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.CompliancePolicyType;
import com.chilicoders.model.ComplianceRequirementCategory;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.model.MatchType;
import com.chilicoders.model.Source;
import com.chilicoders.model.SubscriptionType;
import com.chilicoders.rest.models.ComplianceFinding;
import com.chilicoders.rest.models.EventSubscription;
import com.chilicoders.rest.resources.BaseResourceTest;
import com.chilicoders.rest.resources.IntegrationsResourceTest;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class ComplianceRiskExceptionExpirationEventTaskTest extends BaseResourceTest {

	private static final String WEBHOOK_CONTENT_TEMPLATE = "{"
			+ "	\"fields\": {"
			+ "		\"project\": {"
			+ "			\"key\": \"dev\""
			+ "		},"
			+ "		\"summary\":\"{{ event.trigger }} occured on {{ finding.name }} @{{ event.timestamp }}\","
			+ "		\"description\": \"{{ event.trigger }} occured on {{ finding.name }} @{{ event.timestamp }}\","
			+ "		\"issueType\": {"
			+ "			\"name\":\"bug\""
			+ "		}"
			+ "	}"
			+ "}";

	private static final String SETTINGS_TIME_BEFORE = "{\"timeBefore\": 1}";
	private long genericWebhookIntegrationId = -1;
	private Integer subscriptionId = -1;
	private Long assetId;
	private Long policyId;
	private Long requirementId;
	private Long matchId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();

		final JSONObject webhookIntegration = IntegrationsResourceTest.createIntegration(IntegrationType.WEBHOOK);
		this.genericWebhookIntegrationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO integrations (name, type, configuration, customerid, created, updated) VALUES(?, ?, ?::JSONB, ?, ?, ?) RETURNING id",
				"nameText", IntegrationType.WEBHOOK, webhookIntegration.getJSONObject("configuration").toString(), getAdminUserCustomerId(), new Date(), new Date());
		this.subscriptionId = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO eventsubscriptions (name, trigger, viewtemplateid, integrationid, integrationtype, enabled, contentconfiguration, settings, customerid, created, updated)"
				+ " VALUES(?, ?, ?, ?, ?, ?, ?::JSONB, ?::JSONB, ?, ?, ?) RETURNING id",
				"nameText", Trigger.COMPLIANCE_RISK_EXCEPTION_EXPIRATION, null, this.genericWebhookIntegrationId, IntegrationType.WEBHOOK, true, WEBHOOK_CONTENT_TEMPLATE, SETTINGS_TIME_BEFORE,
				getAdminUserCustomerId(), new Date(), new Date());
		this.assetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, customerid, source, activesubscriptiontypes) VALUES (?, ?, ?::source[], ?::subscriptiontype[]) RETURNING id",
				"Asset 1", getAdminUserCustomerId(), new Source[] {Source.SCALE}, new SubscriptionType[] {SubscriptionType.SCALE});
		this.policyId = DbObject.executeCountQuery(getConnection(), "INSERT INTO compliancepolicies (name, key, type) VALUES(?, ?, ?) RETURNING id", "Test", "test",
				CompliancePolicyType.AWS);
		this.requirementId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO compliancerequirements (policyid, nameshort, description, key, requirementid, category) VALUES(?, ?, ?, ?, ?, ?) RETURNING id",
				policyId, "Test", "Test", "test1.1", "1.1", ComplianceRequirementCategory.MONITORING_AND_LOGGING);
		this.matchId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO matches (assetid, type, firstseen, lastseen, source, subscriptiontype, createdbyid, updatedbyid, customerid) VALUES(?, ?, now(), now(), ?::source[], ?::subscriptiontype, ?, ?, ?) RETURNING id",
				assetId, MatchType.COMPLIANCE, new Source[] {Source.CLOUDSEC}, SubscriptionType.CLOUDSEC, XMLAPITestBase.ADMIN_USER_ID,
				XMLAPITestBase.ADMIN_USER_ID, getAdminUserCustomerId());
		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM eventsubscriptions WHERE customerid = ?", getAdminUserCustomerId());
		DbObject.executeUpdate(getConnection(), "DELETE FROM integrations WHERE customerid = ?", getAdminUserCustomerId());
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancefindings WHERE customerid = ?", getAdminUserCustomerId());
		DbObject.executeUpdate(getConnection(), "DELETE FROM viewtemplates WHERE customerid = ?", getAdminUserCustomerId());
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancerequirements WHERE policyId = ?", policyId);
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancepolicies WHERE id = ?", policyId);
		DbObject.executeUpdate(getConnection(), "DELETE FROM matches WHERE customerid = ?", getAdminUserCustomerId());
		getConnection().commit();
	}

	@Test
	public void testCollectFindingAcceptedExpirationEvent() throws SQLException, JAXBException {
		final int findingIdInLessThanOneDay = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO compliancefindings (assetid, requirementid, matchid, customerid, exceptionuntil) VALUES(?, ?, ?, ?, ?) RETURNING id",
				this.assetId, this.requirementId, this.matchId, getAdminUserCustomerId(), Instant.now().plus(4, ChronoUnit.HOURS));

		final int findingIdInMoreThanOneDay = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO compliancefindings (assetid, requirementid, matchid, customerid, exceptionuntil) VALUES(?, ?, ?, ?, ?) RETURNING id",
				this.assetId, this.requirementId, this.matchId, getAdminUserCustomerId(), Instant.now().plus(4, ChronoUnit.HOURS).plus(1, ChronoUnit.DAYS));
		// Check one event is actually collected
		List<Event> eventList = new ComplianceRiskExceptionExpirationEventsTask().getComplianceRiskExceptionExpirationEvents(getConnection());
		assertThat(eventList).isNotEmpty().hasSize(1);
		final Event event = eventList.get(0);
		assertThat(event.getTrigger()).isEqualTo(Trigger.COMPLIANCE_RISK_EXCEPTION_EXPIRATION);

		assertThat(event.getObjectIds()).isNotEmpty().hasSize(3);
		final Map<EventObject.EventObjectIdentifier, Integer> objectIds = event.getObjectIds();
		assertThat(objectIds.get(EventObject.EventObjectIdentifier.COMPLIANCE_FINDING)).isEqualTo(findingIdInLessThanOneDay);
		assertThat(objectIds.get(EventObject.EventObjectIdentifier.COMPLIANCE_FINDING)).isNotEqualTo(findingIdInMoreThanOneDay);
		assertThat(objectIds.get(EventObject.EventObjectIdentifier.EVENT_SUBSCRIPTION)).isEqualTo(subscriptionId);
		assertThat(objectIds.get(EventObject.EventObjectIdentifier.ASSET)).isEqualTo(this.assetId.intValue());

		final List<Object> params = new ArrayList<>();
		final StringBuilder where = new StringBuilder("deleted IS NULL");
		final List<ComplianceFinding> findings = ComplianceFinding.getObjects(ComplianceFinding.class, getConnection(), null, null, null, null, null, null, "" + findingIdInLessThanOneDay, false, true, where.toString(), params).getRight();
		assertThat(findings).isNotEmpty().hasSize(1);
		final ComplianceFinding finding = findings.get(0);

		StringUtils.concatenateFilters(where, "trigger = ?");
		params.add(Trigger.COMPLIANCE_RISK_EXCEPTION_EXPIRATION);
		final List<EventSubscription> subscriptions = EventSubscription.getObjects(EventSubscription.class, getConnection(), null, null, null, null, null, null,
				this.subscriptionId.toString(), false, true, where.toString(), params).getRight();
		assertThat(subscriptions).isNotEmpty().hasSize(1);
		final EventSubscription subscription = subscriptions.get(0);
		assertThat(subscription.getTrigger()).isEqualTo(Trigger.COMPLIANCE_RISK_EXCEPTION_EXPIRATION);
		assertThat(subscription.getLastTriggered()).isNotNull();
		assertThat(finding.getExceptionUntil()).isBefore(subscription.getLastTriggered().plus(subscription.getSettings().getTimeBefore(), ChronoUnit.DAYS));

		// Check if run a second time no new events are collected, e.g. events are created only one time.
		eventList = new ComplianceRiskExceptionExpirationEventsTask().getComplianceRiskExceptionExpirationEvents(getConnection());
		assertThat(eventList).isEmpty();

		// Create a schedule with next occurrence just after thread last run date and check a new event is collected.
		final List<EventSubscription> subscriptionsAfterSecondRun = EventSubscription.getObjects(EventSubscription.class, getConnection(), null, null, null, null, null, null,
				this.subscriptionId.toString(), false, true, where.toString(), params).getRight();
		assertThat(subscriptionsAfterSecondRun).isNotEmpty().hasSize(1);
		final EventSubscription subscriptionAfterSecondRun = subscriptionsAfterSecondRun.get(0);
		assertThat(subscriptionAfterSecondRun.getId()).isEqualTo(subscription.getId());
		assertThat(subscriptionAfterSecondRun.getLastTriggered()).isEqualTo(subscription.getLastTriggered());
	}
}
