package com.chilicoders.util;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import java.sql.SQLException;
import java.util.Optional;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.core.agents.api.AgentsApi;
import com.chilicoders.core.agents.api.AgentsService;
import com.chilicoders.core.agents.impl.AgentsServiceImpl;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.db.DbObject;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class SyncAgentVersionTaskTest extends XMLAPITestBase {
	private static final String OLD_AGENT_VERSION = "1.15.0";
	private static final String AGENT_VERSION = "1.15.1";

	@Mock
	private AgentsApi agentsApiMock;
	private AgentsService agentsService;

	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Setup before each test.
	 */
	@Before
	public void init() {
		MockitoAnnotations.openMocks(this);
		when(agentsApiMock.getAgentInstallerVersion()).thenReturn(AGENT_VERSION);
		agentsService = new AgentsServiceImpl(agentsApiMock, Optional.of(ServiceProvider.getEncryptionService()), Configuration.getConfigService(),
				Optional.of(ServiceProvider.getClavemService()));
	}

	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name());
		getConnection().commit();
	}

	@Test
	@RequireDatabaseCommits
	public void insertAgentVersionTest() throws SQLException {

		// given that there's no LAST_AGENT_VERSION stored in the datastore
		assertEquals(Long.valueOf(0),
				DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));

		// when running SyncAgentVersionTask
		new SyncAgentVersionTask(agentsService).run();

		// then the LAST_AGENT_VERSION is stored with the value corresponding to the one returned from agentApi
		assertEquals(Long.valueOf(1),
				DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));
		assertEquals(AGENT_VERSION,
				DbObject.getString(getConnection(), "SELECT value FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));
	}

	@Test
	@RequireDatabaseCommits
	public void updateAgentVersionTest() throws SQLException {

		// given that the agent version has been stored in the datastore
		ServiceProvider.getDataStoreService(getConnection()).setEntry(DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name(), OLD_AGENT_VERSION);
		getConnection().commit();
		assertEquals(OLD_AGENT_VERSION,
				DbObject.getString(getConnection(), "SELECT value FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));

		// when running SyncAgentVersionTask
		new SyncAgentVersionTask(agentsService).run();

		// then the agent version is updated with the new value
		assertEquals(Long.valueOf(1),
				DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));
		assertEquals(AGENT_VERSION,
				DbObject.getString(getConnection(), "SELECT value FROM datastore WHERE key = ?", DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name()));
	}
}
