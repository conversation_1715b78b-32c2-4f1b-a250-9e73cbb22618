package com.chilicoders.boris;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

import javax.xml.bind.JAXBException;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import com.chilicoders.app.ScanApp;
import com.chilicoders.boris.objects.Scan;
import com.chilicoders.core.authentication.generic.model.Credentials;
import com.chilicoders.core.authentication.generic.model.SMBCredential;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.scandata.api.model.PluginPreferences;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.xml.XmlUtils;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class TestCredentialsThreadTest extends XMLAPITestBase {

	@Test
	public void testCredentials() throws SQLException, IOException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}
		setupDatabaseForTest(false);

		final StringBuilder prop = new StringBuilder();
		prop.append(new PluginPreferences("ssh", "username", "text", "test").toProbeSetting() + "\r\n");
		prop.append(new PluginPreferences("ssh", "password", "text", "test").toProbeSetting() + "\r\n");
		prop.append(new PluginPreferences("ssh", "substitute user", "text", "sudo").toProbeSetting() + "\r\n");
		prop.append(new PluginPreferences("smb", "username", "text", "test").toProbeSetting() + "\r\n");
		prop.append(new PluginPreferences("smb", "password", "text", "test").toProbeSetting() + "\r\n");

		final HashMap<String, Object> settings = new HashMap<>();
		settings.put("PROP", prop.toString());

		final Scan scan = new Scan();
		scan.setTarget("192.168.201.1");
		scan.setService(ScanServiceType.TestCredentials);
		scan.setScanStartDate(new Date());
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.HOUR, 1);
		scan.setScanEnd(cal.getTime());
		assertTrue(scan.isTestCredentials());

		final OutscanThread outscanThread = new OutscanThread(ScanApp.getInstance(false));

		// SSH
		Configuration.setProperty(ConfigKeys.ConfigurationKey.sshcommands_runas, ""); // test env doesn't have scanjob user
		scan.setId(1);
		settings.put("AUTHENTICATIONTYPE", "1");
		scan.setSettings(XmlUtils.createParam(settings).toString());
		scan.setTargetType(TargetService.TARGET_TYPE_IP);
		TestCredentialsThread thread = new TestCredentialsThread(outscanThread, scan);
		thread.runThread();
		String reportFile = OutscanThread.getReportFile(scan.getId(), false, true, false);
		assertTrue(reportFile, new File(reportFile).exists());
		FileUtils.deleteQuietly(new File(reportFile));
		FileUtils.deleteQuietly(new File(TestCredentialsThread.getFilename("" + scan.getId(), scan.getScanStartDate())));

		// SMB
		scan.setId(2);
		settings.put("AUTHENTICATIONTYPE", "0");
		scan.setSettings(XmlUtils.createParam(settings).toString());
		thread = new TestCredentialsThread(outscanThread, scan);
		thread.runThread();
		reportFile = OutscanThread.getReportFile(scan.getId(), false, true, false);
		assertTrue(reportFile, new File(reportFile).exists());
		FileUtils.deleteQuietly(new File(reportFile));
		FileUtils.deleteQuietly(new File(TestCredentialsThread.getFilename("" + scan.getId(), scan.getScanStartDate())));
	}

	@Test
	public void testSMBCredentialsNewFormat() throws SQLException, IOException, JAXBException {
		setupDatabaseForTest(false);

		final Credentials credentials = new Credentials();
		credentials.addSMBCredential(new SMBCredential("", "username", "passw", null));
		final String credentialsString = MarshallingUtils.marshal(credentials);
		final StringBuilder prop = new StringBuilder();
		prop.append(new PluginPreferences("auth", "credentials", "entry", Base64.encodeBase64String(credentialsString.getBytes(UTF_8))).toProbeSetting() + "\r\n");

		final HashMap<String, Object> settings = new HashMap<>();
		settings.put("PROP", prop.toString());

		final Scan scan = new Scan();
		scan.setTarget("192.168.201.1");
		scan.setService(ScanServiceType.TestCredentials);
		scan.setScanStartDate(new Date());
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.HOUR, 1);
		scan.setScanEnd(cal.getTime());
		assertTrue(scan.isTestCredentials());

		final OutscanThread outscanThread = new OutscanThread(ScanApp.getInstance(false));
		scan.setId(2);
		settings.put("AUTHENTICATIONTYPE", "0");
		scan.setSettings(XmlUtils.createParam(settings).toString());
		final TestCredentialsThread thread = new TestCredentialsThread(outscanThread, scan);
		thread.runThread();
		final String reportFile = OutscanThread.getReportFile(scan.getId(), false, true, false);
		assertTrue(reportFile, new File(reportFile).exists());
		FileUtils.deleteQuietly(new File(reportFile));
		FileUtils.deleteQuietly(new File(TestCredentialsThread.getFilename("" + scan.getId(), scan.getScanStartDate())));
	}
}
