package com.chilicoders.bl;

import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.json.JSONException;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.app.ScanApp;
import com.chilicoders.boris.VerifyThread;
import com.chilicoders.db.DbObject;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.ReportData;
import com.chilicoders.model.ReportDataList;
import com.chilicoders.model.ReportEntryTypes;
import com.chilicoders.ruleengine.ProbeFact;
import com.chilicoders.ruleengine.RuleException;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class VerifyBusinessTest extends XMLAPITestBase {

	@Test
	@RequireDatabaseCommits
	public void startVerify() throws <PERSON><PERSON><PERSON>x<PERSON>, <PERSON>Exception, IOException, J<PERSON>NException, SAXException {
		setupDatabaseForTest(false);

		final long targetId = createTarget(MAINUSER_TOKEN, null, false);

		final long scheduleId = createSchedule(MAINUSER_TOKEN, null, -1);

		final List<ProbeFact> facts = new ArrayList<>();
		facts.add(new ProbeFact("fact/product/version", "apache/tomcat", "1.2.1", 80, Protocol.TCP));

		final ReportDataList report = new ReportDataList();
		report.getReportData().add(ReportData.builder().host("*******").userId(TEST_USER_ID).type(ReportEntryTypes.REPORT_SETUP.getId()).ruleId("PROP").data("").build());

		final long reportId = saveReport(targetId, scheduleId, facts, TEST_USER_ID, report, null);
		assertTrue(reportId > 0);
		final long scanJobId = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tscanlogs WHERE itype=20");

		doTests("STARTVERIFYDATA",
				"JSON=1&PCI=0&GROUPVALUE=*******&FETCHGROUPS=1&TARGETS=-1&SCANLOGXID="
						+ scanJobId
						+ "&TARGETGROUPS=-1&GROUPS=-1&filter[0][field]=DFIRSTSEEN&filter[0][data][type]=date&filter[0][data][comparison]=today&groupBy=VCTARGET&TYPE=1",
				new XMLAPIResults(MAINUSER_TOKEN));
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tverifyscans") > 0);

		new VerifyBusiness().scheduler(new VerifyThread(ScanApp.getInstance(), 1));
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss") > 0);
	}
}
