package com.chilicoders.bl;

import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.xml.bind.JAXBException;

import org.apache.commons.io.FileUtils;
import org.awaitility.Awaitility;
import org.flywaydb.core.api.MigrationVersion;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.bl.certificate.CsrCreationsResult;
import com.chilicoders.bl.certificate.CsrCreationsResult.CsrResultCode;
import com.chilicoders.boris.ScanStatusThread;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.AppAccess;
import com.chilicoders.db.objects.HiabStat;
import com.chilicoders.db.objects.ScanStatus;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.db.objects.SubUser;
import com.chilicoders.db.objects.User;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.rest.models.BaseDbObject;
import com.chilicoders.rest.models.CertificateCreationRequest;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.LibellumUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.xmlapi.TestResponse;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

public class ScannerBusinessTest extends XMLAPITestBase {

	static {
		initConfiguration();
	}

	/**
	 * Setup for test.
	 */
	@Before
	public void startCertificateServerMock() throws IOException {
		if (!Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);
			startLibellumMockServer(ServiceIdentity.Service.HIAB_SCHEDULER);
		}
	}

	/**
	 * cleanup
	 */
	@After
	public void cleanup() {
		stopLocalServer();
		FileUtils.deleteQuietly(new File(Configuration.getProperty(ConfigurationKey.client_csr_file)));
		FileUtils.deleteQuietly(new File(Configuration.getProperty(ConfigurationKey.client_key_file)));
	}

	@Test
	@RequireDatabaseCommits
	public void registerScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);
		startLocalServer(true);

		final User user = User.getById(User.class, getConnection(), Access.ADMIN, TEST_USER_ID);
		assertNotNull(user);

		final Map<String, String> params = new HashMap<>();
		params.put("JSON", "1");
		params.put("ACTIVATE", "1");
		params.put("NAME", "Test");
		params.put("IPADDRESS", "127.0.0.1");
		params.put("USERNAME", user.getUsername());
		params.put("MODE", "1");
		params.put("PASSWORD", "security");
		params.put("AUTHMETHOD", "1");

		Configuration.setProperty(ConfigurationKey.distributescan_path, ":" + getLocalServerPort(), false);
		final long scannerId = doTests("UPDATESCANNERDATA", params, new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(2, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xuserxid = ?", TEST_USER_ID));

		doTests("APPROVESCANNER", createParameterMap("JSON=1&XID=1"), new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));
		doTests("APPROVESCANNER", createParameterMap("JSON=1&XID=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND approved = 1", scannerId));

		// Test with token authentication
		DbObject.removeObjects(ScannerImpl.class, getConnection(), "xuserxid = ?", TEST_USER_ID);
		getConnection().commit();

		long tokenId = doTests("UPDATEAPPACCESSDATA", createParameterMap("JSON=1&XID=-1&NAME=Test&ACTIVE=1"), new XMLAPIResults(MAINUSER_TOKEN));
		assertTrue(tokenId > 0);
		AppAccess appAccess = DbObject.getByField(AppAccess.class, getConnection(), Access.ADMIN, "xid", tokenId);
		assertNotNull(appAccess);
		String token = appAccess.getTokenKey();
		assertEquals(64, token.length());

		params.remove("USERNAME");
		params.remove("PASSWORD");
		params.put("AUTHMETHOD", "2");
		params.put("ACCESSTOKEN", token);

		doTests("UPDATESCANNERDATA", params, new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(2, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xuserxid = ?", TEST_USER_ID));

		// Test with sub user ID
		DbObject.removeObjects(ScannerImpl.class, getConnection(), "xuserxid = ?", TEST_USER_ID);
		getConnection().commit();

		final String userToken = getSubuser(new String[] {});
		final SubUser subUser = SubUser.getByField(SubUser.class, getConnection(), Access.ADMIN, "VCUSERNAME", userToken);
		assertNotNull(subUser);

		tokenId = doTests("UPDATEAPPACCESSDATA", createParameterMap("JSON=1&XID=-1&NAME=Test&ACTIVE=1&XSUBUSERXID=" + subUser.getId()), new XMLAPIResults(MAINUSER_TOKEN));
		appAccess = DbObject.getByField(AppAccess.class, getConnection(), Access.ADMIN, "xid", tokenId);
		assertNotNull(appAccess);
		token = appAccess.getTokenKey();

		params.put("ACCESSTOKEN", token);

		doTests("UPDATESCANNERDATA", params, new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(2, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xuserxid = ?", TEST_USER_ID));

		// Test with non-existent sub user
		DbObject.removeObjects(ScannerImpl.class, getConnection(), "xuserxid = ?", TEST_USER_ID);
		DbObject.removeObject(SubUser.class, getConnection(), subUser.getId());
		getConnection().commit();

		doTests("UPDATESCANNERDATA", params, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied));

		// Test with IP restriction
		tokenId = doTests("UPDATEAPPACCESSDATA", createParameterMap("JSON=1&XID=-1&NAME=Test&ACTIVE=1&IPADDRESSRESTRICTION=*********"), new XMLAPIResults(MAINUSER_TOKEN));
		appAccess = DbObject.getByField(AppAccess.class, getConnection(), Access.ADMIN, "xid", tokenId);
		assertNotNull(appAccess);
		token = appAccess.getTokenKey();

		params.put("ACCESSTOKEN", token);

		doTests("UPDATESCANNERDATA", params, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied));
	}

	@Test
	public void setDefaultScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());

		doTests("UPDATESCANNERDATA", createParameterMap("JSON=1&ISDEFAULT=1&XID=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tusers WHERE xid = ? AND defaultscanner = ?", TEST_USER_ID, scannerId));
	}

	@Test
	public void testApproveScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(false);
		startLocalServer(true);

		// Insert HIAB Scheduler
		DbObject.executeUpdate(getConnection(),
				"INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES(?, ?, ?, ?)",
				TEST_USER_ID, TEST_UUID, PRODUCT_HIAB, HIAB_SCHEDULER_MODE);
		getConnection().commit();

		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=1&X509KEY=" + TEST_UUID + "&KEY=" + "someString"),
				new XMLAPIResults(MAINUSER_TOKEN, new AccountMissingValidator()));

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET hiabexternalwebapps = 2");
		getConnection().commit();

		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=1&X509KEY=" + TEST_UUID + "&KEY=" + "someString"), new XMLAPIResults(MAINUSER_TOKEN));

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET hiabexternalwebapps = 0");
		getConnection().commit();

		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=1&X509KEY=" + TEST_UUID + "&KEY=" + "someString"),
				new XMLAPIResults(MAINUSER_TOKEN, new AccountMissingValidator()));

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET xhiabexternalip = 5");
		getConnection().commit();

		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=1&X509KEY=" + TEST_UUID + "&KEY=" + "someString"),
				new XMLAPIResults(MAINUSER_TOKEN));

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET xhiabexternalip = 0");
		getConnection().commit();

		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=1&X509KEY=" + TEST_UUID + "&KEY=" + "someString"),
				new XMLAPIResults(MAINUSER_TOKEN, new AccountMissingValidator()));
	}

	private static class AccountMissingValidator implements XMLAPIResults.ResponseListener {
		@Override
		public void validate(final TestResponse response, final Map<String, String> params) throws JSONException {
			assertThat(response).isNotNull();
			final JSONObject responseData = (JSONObject) response.getJsonResult().get("data");
			assertThatJson(responseData).isObject()
					.node("errorCode")
					.isString()
					.containsOnlyDigits()
					.isEqualTo(Integer.toString(ErrorCode.InputValidationFailed.getCode()));
			assertThatJson(response.getJsonResult()).isObject().node("data").isObject().node("errorMessage").isString().contains("HIAB External");
		}
	}

	@Test
	public void updateScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());

		doTests("UPDATESCANNER", createParameterMap("JSON=1&XIDS=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND performupdate = 1", scannerId));
	}

	@Test
	public void testSetScannerTypeInOutscan() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);
		startLocalServer(true);

		// create thiabstat entries
		// Scheduler
		if (HiabStat.getByKey(getConnection(), TEST_UUID) == null) {
			DbObject.executeUpdate(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES (?, ?, ?, ?)",
					TEST_USER_ID, TEST_UUID, PRODUCT_HIAB, HIAB_SCHEDULER_MODE);
		}
		// Scanner
		if (HiabStat.getByKey(getConnection(), HIAB_IDENTIFIER) == null) {
			DbObject.executeUpdate(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES (?, ?, ?, ?)",
					TEST_USER_ID, HIAB_IDENTIFIER, PRODUCT_HIAB, HIAB_SCANNER_MODE);
		}
		getConnection().commit();

		// Approve Outscan as scheduler in HIAB
		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=0&X509KEY=" + TEST_UUID + "&KEY=" + "someStringThatIsLongerThan32Chars"),
				new XMLAPIResults(MAINUSER_TOKEN));

		final ScannerImpl scheduler =
				ScannerImpl.get(ScannerImpl.class, getConnection(), DbHelper.getSelect(ScannerImpl.class, Access.ADMIN, "uuid=?::UUID"), TEST_UUID);
		doTests("SETSCANNERTYPE", createParameterMap("JSON=1&XID=" + scheduler.getId() + "&APPSECSCALE=true"),
				new XMLAPIResults(MAINUSER_TOKEN, new WrongHiabTypeValidator()));
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND appsecscalescanner = true", scheduler.getId())).isZero();

		// Approve Outscan as scheduler in HIAB
		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=0&X509KEY=" + HIAB_IDENTIFIER + "&KEY=" + "someStringThatIsLongerThan32Chars"),
				new XMLAPIResults(MAINUSER_TOKEN));

		final ScannerImpl scanner =
				ScannerImpl.get(ScannerImpl.class, getConnection(), DbHelper.getSelect(ScannerImpl.class, Access.ADMIN, "uuid=?::UUID"), HIAB_IDENTIFIER);
		doTests("SETSCANNERTYPE", createParameterMap("JSON=1&XID=" + scanner.getId() + "&APPSECSCALE=true"),
				new XMLAPIResults(MAINUSER_TOKEN));
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND appsecscalescanner = true", scanner.getId())).isOne();
	}

	private static class WrongHiabTypeValidator implements XMLAPIResults.ResponseListener {
		@Override
		public void validate(final TestResponse response, final Map<String, String> params) throws JSONException {
			assertThat(response).isNotNull();
			final JSONObject responseData = (JSONObject) response.getJsonResult().get("data");
			assertThatJson(responseData).isObject()
					.node("errorCode")
					.isString()
					.containsOnlyDigits()
					.isEqualTo(Integer.toString(ErrorCode.InputValidationFailed.getCode()));
			assertThatJson(response.getJsonResult()).isObject().node("data").isObject().node("errorMessage").isString().contains("Only HIAB scanner");
		}
	}

	/**
	 * Verify that it is possible to set scanner type to normal for Scheduler HIAB
	 * This is a corner case that might happen if a Scheduler HIAB is set as Appsec Scale scanner before the restriction was implemented.
	 * Then it should be allowed to set scanner type to normal.
	 */
	@Test
	public void testSetAsNormalTypeInOutscan() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);
		startLocalServer(true);

		// create thiabstat entries
		// Scheduler
		if (HiabStat.getByKey(getConnection(), TEST_UUID) == null) {
			DbObject.executeUpdate(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES (?, ?, ?, ?)",
					TEST_USER_ID, TEST_UUID, PRODUCT_HIAB, HIAB_SCHEDULER_MODE);
		}
		getConnection().commit();

		// Approve Outscan as scheduler in HIAB
		doTests("OUTSCANSCANNER", createParameterMap("JSON=1&ASSCANNER=0&X509KEY=" + TEST_UUID + "&KEY=" + "someStringThatIsLongerThan32Chars"),
				new XMLAPIResults(MAINUSER_TOKEN));

		final ScannerImpl scheduler =
				ScannerImpl.get(ScannerImpl.class, getConnection(), DbHelper.getSelect(ScannerImpl.class, Access.ADMIN, "uuid=?::UUID"), TEST_UUID);
		// Fake set as AppSec scanner
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET appsecscalescanner = true WHERE xid = ?", scheduler.getId());
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND appsecscalescanner = true", scheduler.getId())).isOne();

		doTests("SETSCANNERTYPE", createParameterMap("JSON=1&XID=" + scheduler.getId() + "&APPSECSCALE=false"),
				new XMLAPIResults(MAINUSER_TOKEN));
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND appsecscalescanner = true", scheduler.getId())).isZero();
	}

	@Test
	public void testSetScannerTypeInHiab() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());

		doTests("SETSCANNERTYPE", createParameterMap("JSON=1&XID=" + scannerId + "&APPSECSCALE=true"), new XMLAPIResults(MAINUSER_TOKEN));
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND appsecscalescanner = true", scannerId)).isOne();
	}

	@Test
	@RequireDatabaseCommits
	public void sendSynchronizationFromScheduler() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());
		final long schedulerId = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tscanners WHERE xuserxid = ? AND mode = 0", TEST_USER_ID);

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET key = (SELECT key FROM tscanners WHERE xid = ?), approved = 1 WHERE xid = ?", scannerId, schedulerId);
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET key = '' WHERE xid = ?", scannerId);
		getConnection().commit();

		startScan(scannerId);
		final Long scanId = DbObject.getLong(getConnection(), "SELECT xid FROM tscanstatuss WHERE scannerid = ? LIMIT 1", scannerId);

		new ScannerBusiness().scheduler(new ScanStatusThread(0, null), false, false);
		Awaitility.await().atMost(Duration.ofSeconds(5)).until(() -> DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss") == 2);
		Awaitility.await()
				.atMost(Duration.ofSeconds(5))
				.until(() -> DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE xid = ? AND scansent = 1", scanId) == 1);

		doTests("STOPSCAN", createParameterMap("JSON=1&XID=" + scanId), new XMLAPIResults(MAINUSER_TOKEN));
		Awaitility.await()
				.atMost(Duration.ofSeconds(5))
				.until(() -> DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE xid = ? AND bsync = 1", scanId) == 1);

		new ScannerBusiness().scheduler(new ScanStatusThread(0, null), false, false);
		Awaitility.await()
				.atMost(Duration.ofSeconds(5))
				.until(() -> DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE xid = ? AND bsync = 0", scanId) == 1);
	}

	/**
	 * Scanner sends certificate request in sync to online scheduler. Scheduler gets certificate from Outscan and sends in response.
	 */
	@Test
	@RequireDatabaseCommits
	public void testScannerGetCertificateFromOnlineScheduler() throws Exception {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		/* TODO: BGV - fix this test for gitlab migration
		setupDatabaseForTest(true);
		startLocalServer(true);
		setOutscanUrlToJerseyRestUrl();
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		final Connection connection = getConnection();
		createScannerPushToScheduler(connection);

		assertThat(shouldRenewCertificate()).isTrue();

		new ScannerBusiness().scheduler(new ScanStatusThread(0, null), true, false);

		assertThat(shouldRenewCertificate()).isFalse();*/
	}

	/**
	 * Scanner sends sync request to scheduler. Scheduler has renewed certificate for scanner in DB. Scheduler sends the certificate to scanner.
	 */
	@Test
	@RequireDatabaseCommits
	public void testScannerGetCertificateFromOfflineScheduler() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);
		startLocalServer(true);
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		Configuration.setProperty(ConfigurationKey.outscan_url, "offline");
		ScannerImpl scanner = createScannerPushToScheduler(getConnection());
		final Connection connection = getConnection();

		ScannerImpl.executeUpdate(connection, "UPDATE tscanners SET certificaterequest = NULL, certificate = ? WHERE xid = ? ",
				validCertificate(ServiceIdentity.Service.HIAB_SCHEDULER), scanner.getId());
		connection.commit();

		assertThat(shouldRenewCertificate()).isTrue();

		new ScannerBusiness().scheduler(new ScanStatusThread(0, null), true, false);

		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
		assertThat(shouldRenewCertificate()).isFalse();
		assertThat(scanner.getCertificate()).isNull();

		final ScannerImpl localScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), ScannerBusiness.LOCAL_SCANNER);
		assertThat(localScanner.getCertificateRequest()).isNull();
	}

	/**
	 * This test verifies the certificate request is stored with local scanner in database and used for next sync call.
	 */
	@Test
	public void testCertificateRequestIsStoredWithLocalScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);
		setOutscanUrlToJerseyRestUrl();
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		final ScannerBusiness scannerBusiness = new ScannerBusiness();
		final ScanStatusThread schedulerThread = new ScanStatusThread(0, null);
		final Connection connection = getConnection();
		createScannerPushToScheduler(connection);

		Configuration.setProperty(ConfigurationKey.outscan_url, "offline");

		scannerBusiness.scheduler(schedulerThread, true, false);
		ScannerImpl scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), ScannerBusiness.LOCAL_SCANNER);
		final String certificateRequest = scanner.getCertificateRequest();

		scannerBusiness.scheduler(schedulerThread, true, false);
		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), ScannerBusiness.LOCAL_SCANNER);
		assertThat(certificateRequest).isEqualTo(scanner.getCertificateRequest()); // verify certificate request is not changed.
	}

	/**
	 * Offline Scheduler to scanner: Scheduler sends sync request and scanner wants to renew certificate.
	 * scanner sends certificate request in response. Scheduler stores request in the database.
	 */
	@Test
	@RequireDatabaseCommits
	public void testSchedulerReceivesCertificateRequestInSyncResponse() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);
		startLocalServer(true);
		Configuration.setProperty(ConfigurationKey.outscan_url, "offline");
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		final ScannerBusiness scannerBusiness = new ScannerBusiness();
		final ScanStatusThread schedulerThread = new ScanStatusThread(0, null);
		final Connection connection = getConnection();
		final ScannerImpl scanner = createScannerPollingToScheduler(connection);

		scannerBusiness.scheduler(schedulerThread, false, false);
		Awaitility.await().atMost(Duration.ofSeconds(10)).until(() -> {
			final ScannerImpl checkScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
			return checkScanner.getCertificateRequest() != null;
		});
	}

	/**
	 * Test that scheduler has already got renewed certificate from Outscan and sends in sync request.
	 */
	@Test
	@RequireDatabaseCommits
	public void testSchedulerSendsCertificateInSyncRequest() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);
		Configuration.setProperty(ConfigurationKey.outscan_url, "offline");
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		final Connection connection = getConnection();

		final ScannerImpl scanner = createScannerPollingToScheduler(connection);
		ScannerImpl.executeUpdate(connection, "UPDATE tscanners SET certificaterequest = NULL, certificate = ? WHERE xid = ? ",
				validCertificate(ServiceIdentity.Service.HIAB_SCHEDULER), scanner.getId());
		connection.commit();

		new ScannerBusiness().scheduler(new ScanStatusThread(0, null), false, false);
		Awaitility.await().atMost(Duration.ofSeconds(10)).until(() -> {
			final ScannerImpl checkScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
			return checkScanner.getCertificateRequest() == null && checkScanner.getCertificate() == null && !shouldRenewCertificate();
		});

		final ScannerImpl localScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), ScannerBusiness.LOCAL_SCANNER);
		assertThat(localScanner.getCertificateRequest()).isNull();
	}

	/**
	 * Scheduler is online, in sync from scheduler response, scheduler receives
	 * certificate request. Scheduler get certificate from Outscan and saves in the
	 * DB which is sent to scanner in next sync request.
	 */
	@Test
	@RequireDatabaseCommits
	public void testRenewScannerCertificateFromOnlineSchedulerInPushingMode() throws Exception {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		/* TODO: BGV - fix this test for gitlab migration
		setupDatabaseForTest(true);
		startLocalServer(true);
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		setOutscanUrlToJerseyRestUrl();
		final ScannerBusiness scannerBusiness = new ScannerBusiness();
		final ScanStatusThread schedulerThread = new ScanStatusThread(0, null);
		final Connection connection = getConnection();
		ScannerImpl scanner = createScannerPollingToScheduler(connection);

		scannerBusiness.scheduler(schedulerThread, false, false);

		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
		final long scannerId = scanner.getId();
		Awaitility.await().atMost(Duration.ofSeconds(30)).until(() -> {
			final ScannerImpl checkScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scannerId);
			return checkScanner.getCertificate() != null && checkScanner.getCertificateRequest() == null;
		});

		// sync again to send the certificate
		scannerBusiness.scheduler(schedulerThread, false, false);

		Awaitility.await().atMost(Duration.ofSeconds(30)).until(() -> {
			final ScannerImpl checkScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scannerId);
			return checkScanner.getCertificate() == null && checkScanner.getCertificateRequest() == null;
		});*/
	}

	@Test
	@RequireDatabaseCommits
	public void testRenewScannerCertificateForRevokedHiab() throws Exception {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		/* TODO: BGV - fix this test for gitlab migration
		setupDatabaseForTest(true);
		startLocalServer(true);
		makeHiabSchedulerHasExpiredCertificate(getConnection());
		setOutscanUrlToJerseyRestUrl();
		final ScannerBusiness scannerBusiness = new ScannerBusiness();
		final ScanStatusThread schedulerThread = new ScanStatusThread(0, null);
		final Connection connection = getConnection();
		final ScannerImpl scanner = createScannerPollingToScheduler(connection);

		final CsrCreationsResult csrResult = LibellumUtils.createCsr();
		final CertificateCreationRequest certificateRequest = CertificateCreationRequest.create(csrResult);

		certificateRequest.setKey(String.valueOf(scanner.getId()));
		scanner.setCertificateRequest(MarshallingUtils.marshal(certificateRequest));
		DbObject.executeUpdate(connection, "UPDATE tscanners set certificate = null, certificaterequest = ?::jsonb", MarshallingUtils.marshal(certificateRequest));
		DbObject.executeUpdate(connection, "UPDATE thiabstats set revoked = true");
		connection.commit();
		scannerBusiness.scheduler(schedulerThread, false, false);

		Awaitility.await().atMost(Duration.ofSeconds(10)).until(() -> {
			final ScannerImpl checkScanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
			return checkScanner.isRevoked();
		});
		DbObject.execute(getConnection(), "DELETE FROM tscanners WHERE xid = ?", scanner.getId());
		getConnection().commit();*/
	}

	@Test
	public void testScannersLastSyncWasMoreThanCertificateRenewalTask() throws Exception {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);
		final Connection connection = getConnection();
		ScannerImpl scanner = createScannerPollingToScheduler(connection);

		DbObject.executeUpdate(connection, "UPDATE tscanners SET lastconnection = NULL where xid = ?", scanner.getId());
		connection.commit();

		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
		assertThat(ScannerBusiness.lastSyncWasMoreThanCertificateRenewalCheckInterval(scanner)).isTrue();

		final long certificateRenewalInterval = Duration.parse(Configuration.getProperty(ConfigurationKey.certificate_renewal_task_interval)).toDays();
		DbObject.executeUpdate(connection, "UPDATE tscanners SET lastconnection = NOW() - INTERVAL '" + (certificateRenewalInterval - 1) + " DAY' where xid = ?",
				scanner.getId());
		connection.commit();

		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
		assertThat(ScannerBusiness.lastSyncWasMoreThanCertificateRenewalCheckInterval(scanner)).isFalse();

		DbObject.executeUpdate(connection, "UPDATE tscanners SET lastconnection = NOW() - INTERVAL '" + (certificateRenewalInterval + 1) + " DAY' where xid = ?",
				scanner.getId());
		connection.commit();

		scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scanner.getId());
		assertThat(ScannerBusiness.lastSyncWasMoreThanCertificateRenewalCheckInterval(scanner)).isTrue();
	}

	@Test
	public void sendScanners() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());

		doTests("ASSIGNSCANNERTOGROUP", createParameterMap("JSON=1&XID=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));
		final long groupId = doTests("ASSIGNSCANNERTOGROUP", createParameterMap("JSON=1&XID=" + scannerId + "&GROUPNAME=Test"), new XMLAPIResults(MAINUSER_TOKEN));

		doTests("SCANNERDATA", createParameterMap("XID=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN, "XID", "" + scannerId));
		doTests("SCANNERDATA", createParameterMap("SCANNERS=1&GROUPS=1"), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(3, "SCANNER")));
		doTests("SCANNERDATA", createParameterMap("SCANNERS=1"), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2, "SCANNER")));
		doTests("SCANNERDATA", createParameterMap("GROUPS=1"), new XMLAPIResults(MAINUSER_TOKEN, "XID", "" + groupId));
		doTests("SCANNERDATA", createParameterMap("MODE=1"), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2, "SCANNER")));
		doTests("SCANNERDATA", createParameterMap("MODE=1&EMPTYGROUPS=1&SORTGROUPS=1"), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2, "SCANNER")));

		doTests("ASSIGNSCANNERTOGROUP", createParameterMap("JSON=1&XID=" + scannerId + "&GROUPXID=0"), new XMLAPIResults(MAINUSER_TOKEN));
	}

	@Test
	@RequireDatabaseCommits
	public void removeScanner() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}
		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());
		final long schedulerId = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tscanners WHERE xuserxid = ? AND mode = 0", TEST_USER_ID);

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET key = (SELECT key FROM tscanners WHERE xid = ?), approved = 1, inactive = 0 WHERE xid = ?", scannerId,
				schedulerId);
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET key = '' WHERE xid = ?", scannerId);
		getConnection().commit();

		createTarget(MAINUSER_TOKEN, "3.3.3.3", false, scannerId);

		doTests("UPDATESCHEDULEDATA", createParameterMap("JSON=1&XID=-1&GROUPLIST=-1&NAME=Test&SCANMODE=1&SCANNERID=" + scannerId), new XMLAPIResults(MAINUSER_TOKEN));

		doTests("REMOVESCANNERDATA", createParameterMap("JSON=1&XID=" + scannerId + "&SCANNERID=0"), new XMLAPIResults(MAINUSER_TOKEN));

		Awaitility.await()
				.atMost(2, TimeUnit.SECONDS)
				.pollInterval(Duration.ofMillis(500))
				.until(() -> DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ? AND deleted = 1", scannerId) == 1);

		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanners WHERE xid = ?", schedulerId));
	}

	@Test
	public void testScannersNeedNewCertificate() throws Exception {
		setupDatabaseForTest(true);

		final Connection connection = getConnection();
		final CertificateCreationRequest certificateRequest = CertificateCreationRequest.builder()
				.certificateSigningRequest("FAKE_REQUEST")
				.build();

		DbObject.executeUpdate(connection, "UPDATE tscanners set certificaterequest = ?::jsonb", MarshallingUtils.marshal(certificateRequest));
		connection.commit();
		assertThat(ScannerBusiness.getScannersNeedNewCertificates(connection).isEmpty()).isFalse();

		DbObject.executeUpdate(connection, "UPDATE tscanners set certificaterequest = null");
		connection.commit();
		assertThat(ScannerBusiness.getScannersNeedNewCertificates(connection).isEmpty()).isTrue();
	}

	@Test
	public void testRevokingOldScanners() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);
		startLocalServer(true);

		final String fakeSchedulerUUID = "e8ac16fc-fdb5-4e56-bf6c";
		final String fakeScannerUUID = "e8ac16fc-fdb5-4e56-ba6f";

		// create thiabstat entries
		if (HiabStat.getByKey(getConnection(), fakeSchedulerUUID) == null) {
			DbObject.executeUpdate(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES (?, ?, ?, ?)", TEST_USER_ID, fakeSchedulerUUID, "hiab",
					HIAB_SCHEDULER_MODE);
		}
		if (HiabStat.getByKey(getConnection(), fakeScannerUUID) == null) {
			DbObject.executeUpdate(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES (?, ?, ?, ?)", TEST_USER_ID, fakeScannerUUID, "hiab",
					HIAB_SCANNER_MODE);
		}
		getConnection().commit();

		final HiabStat scheduler = HiabStat.getByKey(getConnection(), fakeSchedulerUUID);
		final HiabStat scanner = HiabStat.getByKey(getConnection(), fakeScannerUUID);

		// verify that nothing is revoked at this point
		assertThat(scheduler.isRevoked()).isFalse();
		assertThat(scanner.isRevoked()).isFalse();

		final Map<String, String> requestParams = new HashMap<>();
		requestParams.put("JSON", "1");

		// test sending an invalid scheduler UUID when revoking a scanner
		requestParams.put("UUID", "INVALID-SCHEDULER-UUID");
		requestParams.put("SCANNERUUID", fakeScannerUUID);
		doTests("REVOKESCANNER", requestParams, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));

		// test revoking a scheduler
		requestParams.put("UUID", fakeSchedulerUUID);
		requestParams.put("SCANNERUUID", fakeSchedulerUUID);
		doTests("REVOKESCANNER", requestParams, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));

		// test revoking a scanner
		requestParams.put("SCANNERUUID", fakeScannerUUID);
		doTests("REVOKESCANNER", requestParams, new XMLAPIResults(MAINUSER_TOKEN));

		// verify revocation states
		assertThat(HiabStat.getBoolean(getConnection(), "SELECT revoked FROM thiabstats WHERE id = ?", scanner.getId()) != null).isTrue();
		assertThat(HiabStat.getBoolean(getConnection(), "SELECT revoked FROM thiabstats WHERE id = ? AND uuid = ?", scheduler.getId(), scheduler.getUuid()))
				.isFalse();

		// cleanup
		DbObject.removeObjects(HiabStat.class, getConnection(), "uuid = ANY(?) AND xuserxid = ?", new String[] {fakeSchedulerUUID, "DELETED_" + fakeScannerUUID},
				TEST_USER_ID);
		getConnection().commit();
	}

	@Test
	public void testSchedulerShouldIgnoreScannerTypeIfRunningWorkflow() throws SQLException, IOException, JSONException, SAXException, JAXBException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}

		setupDatabaseForTest(true);
		startLocalServer(true);

		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());
		final long customerId = DbObject.executeCountQuery(getConnection(), "SELECT id FROM customers WHERE userid = ?", TEST_USER_ID);
		final long workflowId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO workflows (name, configurations, customerid, createdbyid) VALUES(?, ?::jsonb, ?, ?) RETURNING id",
				"Test1", "[]", customerId, BaseDbObject.SYSTEM_USER);
		final long scanlogId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanlogs (workflowid, customerid) VALUES(?, ?) RETURNING id", workflowId, customerId);

		final ScanStatus scanStatus = new ScanStatus();
		scanStatus.setScannerId(scannerId);
		scanStatus.setService(ScanServiceType.Netsec);
		scanStatus.setTargetId(scanlogId);
		scanStatus.save(getConnection());
		getConnection().commit();
		final Long scanId = DbObject.getLong(getConnection(), "SELECT xid FROM tscanstatuss WHERE scannerid = ? LIMIT 1", scannerId);

		final List<TestData> testDatas = new ArrayList<>();
		// Should have result with normal scanner
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.CLOUDSEC).appsecScanner(false).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.NETWORK_SCAN).appsecScanner(false).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.DOCKER_SCAN).appsecScanner(false).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.DOCKER_DISCOVERY).appsecScanner(false).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.NETWORK_DISCOVERY).appsecScanner(false).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.CLOUD_DISCOVERY).appsecScanner(false).expectResult(true).build());

		// Should have no result with appsec scanner
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.CLOUDSEC).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.NETWORK_SCAN).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.DOCKER_SCAN).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.DOCKER_DISCOVERY).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.NETWORK_DISCOVERY).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.CLOUD_DISCOVERY).appsecScanner(true).expectResult(false).build());

		// Should have result with appsec scanner if workflow
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.CLOUDSEC).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.NETWORK_SCAN).appsecScanner(true).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.DOCKER_SCAN).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.DOCKER_DISCOVERY).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.NETWORK_DISCOVERY).appsecScanner(true).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.CLOUD_DISCOVERY).appsecScanner(true).expectResult(true).build());

		// Scale scan should sync to Scale Scanner if it's part of workflow or not
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.AppsecScale).appsecScanner(true).expectResult(true).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.AppsecScale).appsecScanner(false).expectResult(true).build());

		// Scale scan should NOT sync to Normal Scanner if it's NOT part of workflow & vice versa
		testDatas.add(TestData.builder().workflow(false).serviceType(ScanServiceType.AppsecScale).appsecScanner(false).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.AppsecScale).appsecScanner(false).expectResult(true).build());

		// Should have no result if no portal scan types and as targetid may accidentally point to a workflow id
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.WasX).appsecScanner(false).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.LuaCheck).appsecScanner(false).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.AmazonDiscovery).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.AgentDiscovery).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.LdapDiscovery).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.Netsec).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.NetsecDiscovery).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.TestCredentials).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.Lookup).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.Was).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.WasDiscovery).appsecScanner(true).expectResult(false).build());
		testDatas.add(TestData.builder().workflow(true).serviceType(ScanServiceType.PAC_SCAN).appsecScanner(true).expectResult(false).build());

		for (final TestData testData : testDatas) {
			DbObject.executeUpdate(getConnection(), "UPDATE scanlogs SET workflowid =? WHERE id =?", testData.isWorkflow() ? workflowId : null, scanlogId);
			DbObject.executeUpdate(getConnection(), "UPDATE tscanstatuss SET workflowid =?, vcservice =? WHERE xid =?", testData.isWorkflow() ? workflowId : null,
					testData.serviceType.toString(), scanId);
			DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET appsecscalescanner =? WHERE xid =?", testData.isAppsecScanner(), scannerId);
			getConnection().commit();

			final ScannerImpl scanner = ScannerImpl.get(ScannerImpl.class, getConnection(), DbHelper.getSelect(ScannerImpl.class, Access.ADMIN, "xid=?"), scannerId);
			final XMLDoc resultScans = ScannerBusiness.buildSchedulerData(getConnection(), scanner, new HashMap<>(), 50, true);
			final String ids = resultScans.getValueListOf("XID");

			assertThat(!ids.isEmpty()).as("Test data failed with settings: " + MarshallingUtils.marshal(testData)).isEqualTo(testData.expectResult);
		}
	}

	@Test
	public void testDatabaseVersions() throws SQLException, IOException, JSONException, SAXException {
		final MigrationVersion emptyVersion = MigrationVersion.fromVersion(null);

		assertThat(emptyVersion.equals(MigrationVersion.fromVersion("359.1"))).isFalse();
		assertThat(emptyVersion.isNewerThan("359.1")).isFalse();

		assertThat(MigrationVersion.fromVersion("359.1").equals(MigrationVersion.fromVersion("359.1"))).isTrue();
		assertThat(MigrationVersion.fromVersion("359.1").isNewerThan("359.0.1111.11111.333333")).isTrue();
		assertThat(MigrationVersion.fromVersion("359.1").isNewerThan("359.0.1")).isTrue();
		assertThat(MigrationVersion.fromVersion("359.1").isNewerThan("359.0")).isTrue();
		assertThat(MigrationVersion.fromVersion("359.1").isNewerThan("359")).isTrue();

		assertThat(MigrationVersion.fromVersion("359.1").isAtLeast("359.0.1")).isTrue();
		assertThat(MigrationVersion.fromVersion("359.1").isAtLeast("359")).isTrue();

		assertThat(MigrationVersion.fromVersion("359").isNewerThan("359.1")).isFalse();
	}

	@Builder
	@ToString
	@Getter
	@Setter
	@NoArgsConstructor
	@AllArgsConstructor
	private static class TestData {
		boolean workflow;
		ScanServiceType serviceType;
		boolean appsecScanner;
		boolean expectResult;
	}

	/**
	 * Creates scanner the is pushing to the scheduler.
	 *
	 * @param connection the database connection.
	 * @return the created scanner object
	 */
	private ScannerImpl createScannerPushToScheduler(final Connection connection) throws SQLException, IOException, SAXException {
		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());
		final long schedulerId = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tscanners WHERE xuserxid = ? AND mode = 0", TEST_USER_ID);

		final ScannerImpl scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scannerId);
		final ScannerImpl scheduler = ScannerImpl.getById(ScannerImpl.class, getConnection(), schedulerId);
		final String scannerIpAddress = scanner.getIpAddress();

		scanner.setIpAddress(scheduler.getIpAddress());
		scanner.setPolling(false);
		scanner.save(connection);

		scheduler.setPolling(true);
		scheduler.setInactive(false);
		scheduler.setIpAddress(scannerIpAddress);
		scheduler.save(connection);
		connection.commit();
		return scanner;
	}

	/**
	 * Creates scanner the is polling to the scheduler.
	 * Also starts a scan.
	 *
	 * @param connection the database connection.
	 * @return the created scanner object
	 */

	private ScannerImpl createScannerPollingToScheduler(final Connection connection) throws SQLException, IOException, SAXException {
		final long scannerId = getScanner(MAINUSER_TOKEN, TEST_USER_ID, getLocalServerPort());
		final long schedulerId = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tscanners WHERE xuserxid = ? AND mode = 0", TEST_USER_ID);
		final ScannerImpl scanner = ScannerImpl.getById(ScannerImpl.class, getConnection(), scannerId);
		final ScannerImpl scheduler = ScannerImpl.getById(ScannerImpl.class, getConnection(), schedulerId);

		scheduler.setApproved(true);
		scheduler.setKey(scanner.getKey());
		scheduler.save(connection);
		scanner.setKey("");
		connection.commit();

		startScan(scanner.getId());

		return scanner;
	}

	/**
	 * Creates target, updates schedule and starts scan.
	 *
	 * @param scannerId The scannerId for the target.
	 */
	private void startScan(final long scannerId) throws IOException, SAXException {
		createTarget(MAINUSER_TOKEN, "3.3.3.3", false, scannerId);
		final long scheduleId = doTests("UPDATESCHEDULEDATA", createParameterMap("JSON=1&XID=-1&GROUPLIST=-1&NAME=Test"), new XMLAPIResults(MAINUSER_TOKEN));
		doTests("STARTSCAN", createParameterMap("JSON=1&XID=" + scheduleId), new XMLAPIResults(MAINUSER_TOKEN));
	}

	/**
	 * creates a CSR and check if certificate should be renewed or not
	 *
	 * @return true if certificate should be renewed.
	 */
	private boolean shouldRenewCertificate() throws IOException {
		final CsrCreationsResult csrResult = LibellumUtils.createCsr();
		return csrResult.getResultCode() == CsrResultCode.SUCCESS;
	}
}
