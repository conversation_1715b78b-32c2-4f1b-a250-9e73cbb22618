package com.chilicoders.bl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.json.JSONException;
import org.junit.Before;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.app.RequestFile;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.reporting.api.model.ReportStatus;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.FindingImpl;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.model.DownloadEntryType;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.ruleengine.RuleException;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.StringUtils;
import com.chilicoders.xmlapi.TestResponse;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class ReportExportBusinessTest extends XMLAPITestBase {

	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Delete data from old tests.
	 */
	@Before
	public void clearData() throws SQLException {
		XmlAble.executeUpdate("DELETE FROM downloadentries");
	}

	@Test
	public void testImportReport() throws SQLException, IOException, JSONException, SAXException, RuleException {
		setupDatabaseForTest(true);

		final Map<String, String> map = new HashMap<>();
		map.put("SCHEDULENAME", "Test Import");
		map.put("SCANNERNAME", "Test Scanner");
		map.put("TARGETGROUPNAME", "Test Group");

		final ArrayList<RequestFile> files = new ArrayList<>();

		doTests("IMPORTREPORT", map, files, new XMLAPIResults(NOACCESS_TOKEN, ErrorCode.AccessDenied));

		doTests("IMPORTREPORT", map, files, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));

		final String data = StringUtils.readTextFile(testPath + "vulnerability_import.xml");
		files.add(new RequestFile("FILECONTENT", "report.xml", data.getBytes(StandardCharsets.UTF_8)));

		doTests("IMPORTREPORT", map, files, new XMLAPIResults(MAINUSER_TOKEN));

		assertThat(FindingImpl.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM treport_vulns")).isEqualTo(4);
	}

	@Test
	public void testCache() throws SQLException, JSONException, IOException, SAXException, RuleException {
		setupDatabaseForTest(false);

		DbObject.executeCountQuery(getConnection(), "INSERT INTO downloadentries (userid, subuserid, type, status) VALUES(?, ?, ?, ?) RETURNING id",
				XMLAPITestBase.TEST_USER_ID, -1, DownloadEntryType.REPORT, ReportStatus.DONE.getStatus());
		getConnection().commit();

		doTests("SENDEXPORTCACHEDATA", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
		doTests("CLEARDOWNLOADCACHE", "", new XMLAPIResults(LIMITEDTARGETS_TOKEN));
		doTests("SENDEXPORTCACHEDATA", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
		doTests("SENDEXPORTCACHEDATA", "", new XMLAPIResults(ADMIN_TOKEN, XMLAPIResults.getCountListener(0)));
		doTests("SENDEXPORTCACHEDATA", "", new XMLAPIResults(LIMITEDTARGETS_TOKEN, XMLAPIResults.getCountListener(0)));
		doTests("CLEARDOWNLOADCACHE", "", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("SENDEXPORTCACHEDATA", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0)));
	}

	@Test
	public void testUpdatePackageDownload() throws IOException, SQLException {
		if (Configuration.isKubernetesEnabled()) {
			return;
		}
		setupDatabaseForTest(false);
		final String content = "update package";
		final String key = "adfef06a-2f60-45c8-b113-55fb5841f5dc";
		final boolean current = Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.reject_non_hiab_requests);
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.reject_non_hiab_requests, true, false);
		try {
			final File file = File.createTempFile("update", ".crt");
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), UTF_8)) {
				writer.write(content);
			}
			DbObject.executeCountQuery(getConnection(),
					"INSERT INTO downloadentries (userid, subuserid, filename, readablename, key, type, status) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
					XMLAPITestBase.ADMIN_USER_ID, -1, file.getPath(), "update.crt", key,
					DownloadEntryType.UPDATE_PACKAGE, ReportStatus.DONE.getStatus());
			getConnection().commit();

			final Map<String, String> downloadMap = new HashMap<>();
			downloadMap.put("KEY", key);
			final TestResponse response = performRequest("DOWNLOADEXPORT", ADMIN_TOKEN, downloadMap);
			assertThat(new String(response.getResultData(), UTF_8)).isEqualTo(content);
		}
		finally {
			Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.reject_non_hiab_requests, current, false);
		}
	}
}
