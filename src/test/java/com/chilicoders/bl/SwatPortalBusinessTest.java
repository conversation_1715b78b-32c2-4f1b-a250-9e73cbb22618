package com.chilicoders.bl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;

import org.json.JSONException;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.db.DbObject;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.SwatStatus;
import com.chilicoders.model.Template;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class SwatPortalBusinessTest extends XMLAPITestBase {

	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Create swat user.
	 *
	 * @param roles User roles
	 */
	private void setupUsers(final String roles) throws SQLException {
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET xroles = ? WHERE xid = ?", roles, ADMIN_USER_ID);
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET web = true WHERE xid = ?", TEST_USER_ID);
		getConnection().commit();
	}

	@Test
	public void updateFinding() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);
		startEmailServer();

		setupUsers("admin");

		final Long applicationId = doTests("WASUPDATESCHEDULE", createParameterMap("CUSTOMERID=" + TEST_USER_ID + "&JSON=1&NAME=Test App"), new XMLAPIResults(ADMIN_TOKEN));

		final long findingId = doTests("WASCREATEFINDING",
				createParameterMap("RULEID=250204&PORT=80&JSON=1&SCHEDULEID=" + applicationId + "&FINDINGTYPE=0&DATAURI=http://www.test.com&DATAMETHOD=GET"),
				new XMLAPIResults(ADMIN_TOKEN));

		final long wasFindingId = DbObject.getLong(getConnection(), "SELECT xid FROM twasfindings WHERE findingxid = ?", findingId);

		// Update finding data on finding that don't exist
		String params = "JSON=1&UPDATEDATA=1&FINDINGID=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InputValidationFailed),
				new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied));

		// Update was finding data
		assertEquals(DbObject.getString(getConnection(), "SELECT method FROM twasfindings WHERE xid = ?", wasFindingId), "GET");
		params = "JSON=1&UPDATEDATA=1&FINDINGID=" + findingId + "&DATA=test&XID = " + wasFindingId + "&METHOD=POST";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals(DbObject.getString(getConnection(), "SELECT method FROM twasfindings WHERE xid = ?", wasFindingId), "POST");

		// Add new was findings
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM twasfindings") == 1);
		params = "JSON=1&UPDATEDATA=1&FINDINGID=" + findingId + "&DATA=test&URL=http://www.test.com/test";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM twasfindings") == 2);

		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.Draft.getId());

		// Update finding set status Verification
		params = "JSON=1&FINDINGID=" + findingId + "&SUBMITVERIFICATION=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.Verification.getId());

		// Update finding set status QA
		params = "JSON=1&FINDINGID=" + findingId + "&SUBMITQA=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.QA.getId());

		// Update finding set status DraftRejectedQA
		params = "JSON=1&FINDINGID=" + findingId + "&MARKASDRAFT=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.DraftRejectedQA.getId());

		// Update finding set status QA from DraftRejectedQA
		params = "JSON=1&FINDINGID=" + findingId + "&SUBMITQA=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.QA.getId());

		// Update finding set status Ready
		params = "JSON=1&FINDINGID=" + findingId + "&MARKREADY=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.Ready.getId());

		// Update finding set status Published
		params = "JSON=1&FINDINGID=" + findingId + "&PUBLISHFINDING=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT swatstatus FROM treport_vulns WHERE xid = ?", findingId), (long) SwatStatus.Published.getId());

		// Update finding set fixed
		params = "JSON=1&FINDINGID=" + findingId + "&MARKFIXED=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT fixed FROM treport_vulns WHERE xid = ?", findingId), 1);

		// Update finding set unfixed
		params = "JSON=1&FINDINGID=" + findingId + "&UNMARKFIXED=1";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals((long) DbObject.getLong(getConnection(), "SELECT fixed FROM treport_vulns WHERE xid = ?", findingId), 0);

		// Update finding set as reviewed
		params = "JSON=1&FINDINGID=" + findingId + "&MARKASREVIEWED=1&DATE=2016-01-01 11:11:00";
		doTests("WASUPDATEFINDING", createParameterMap(params), new XMLAPIResults(ADMIN_TOKEN));
		assertEquals(DbObject.getString(getConnection(), "SELECT dlastseen::text FROM treport_vulns WHERE xid = ?", findingId), "2016-01-01 11:11:00");
	}

	@Test
	public void removeFinding() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);

		setupUsers("admin");

		final Long applicationId = doTests("WASUPDATESCHEDULE", createParameterMap("CUSTOMERID=" + TEST_USER_ID + "&JSON=1&NAME=Test App"), new XMLAPIResults(ADMIN_TOKEN));

		final long findingId =
				doTests("WASCREATEFINDING", createParameterMap("RULEID=250204&PORT=80&JSON=1&SCHEDULEID=" + applicationId + "&FINDINGTYPE=0&DATAURI=http://www.test.com"),
						new XMLAPIResults(ADMIN_TOKEN));

		final long wasFindingId = DbObject.getLong(getConnection(), "SELECT xid FROM twasfindings WHERE findingxid = ?", findingId);

		// Delete was finding that don't exist
		doTests("WASREMOVEFINDING", createParameterMap("WASFINDINGID=1"), new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied));

		// Delete finding that don't exist
		doTests("WASREMOVEFINDING", createParameterMap("FINDINGID=1"), new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InputValidationFailed));

		// Delete was finding
		doTests("WASREMOVEFINDING", createParameterMap("WASFINDINGID=" + wasFindingId), new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM twasfindings WHERE xid = ?", wasFindingId) == 0);

		// Delete finding
		doTests("WASREMOVEFINDING", createParameterMap("FINDINGID=" + findingId), new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM vreportfinding WHERE xid = ?", findingId) == 0);
	}

	@Test
	public void createFinding() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);

		setupUsers("admin");

		final Long applicationId = doTests("WASUPDATESCHEDULE", createParameterMap("CUSTOMERID=" + TEST_USER_ID + "&JSON=1&NAME=Test App"), new XMLAPIResults(ADMIN_TOKEN));

		final HashMap<String, String> settings = new HashMap<>();
		settings.put("JSON", "1");

		// Create finding with missing parameters
		doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InputValidationFailed), new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied));

		settings.put("RULEID", "250204");
		settings.put("PORT", "80");

		// Create finding with schedule that don't exist
		settings.put("SCHEDULEID", "1");
		doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InternalServerError));

		settings.put("SCHEDULEID", "" + applicationId);

		// Create finding with rule that don't exist
		settings.put("RULEID", "1");
		doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InternalServerError));

		settings.put("RULEID", "250204");
		settings.put("NAME", "");
		settings.put("RECREATIONFLOW", "test");
		settings.put("EXPLANATION", "test");

		// Create finding with cdata
		settings.put("FINDINGTYPE", "1");
		settings.put("GATHEREDINFORMATION", "test");
		long findingId = doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(findingId > 0);

		settings.put("FINDINGTYPE", "0");
		settings.put("DATAURI", "http://www.test.com");

		// Create finding with urls, high risk
		settings.put("CVSSVECTOR", "(AV:N/AC:L/Au:N/C:C/I:C/A:C)");
		findingId = doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(findingId > 0);

		// Create finding with urls, medium risk
		settings.put("CVSSVECTOR", "(AV:N/AC:L/Au:N/C:P/I:N/A:N)");
		findingId = doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN));

		// Create finding with urls, low risk
		settings.put("CVSSVECTOR", "(AV:L/AC:L/Au:N/C:P/I:N/A:N)");
		findingId = doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(findingId > 0);

		// Add url to existing finding
		settings.put("FINDINGID", "" + findingId);
		settings.put("DATAURI", "http://www.test2.com");
		findingId = doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN));
		assertTrue(findingId > 0);

		// Create finding with missing treportentrys
		DbObject.executeUpdate(getConnection(), "DELETE FROM treportentrys WHERE xtemplate=? AND xsoxid=?", Template.Swat.getId(), applicationId);
		getConnection().commit();

		doTests("WASCREATEFINDING", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InternalServerError));
	}

	@Test
	public void deleteSchedule() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);

		setupUsers("admin");

		final Long applicationId = doTests("WASUPDATESCHEDULE", createParameterMap("CUSTOMERID=" + TEST_USER_ID + "&JSON=1&NAME=Test App"), new XMLAPIResults(ADMIN_TOKEN));

		// Delete without customerid
		doTests("WASDELETESCHEDULES", createParameterMap("ID=" + applicationId), new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied),
				new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InputValidationFailed));

		// Delete schedule
		doTests("WASDELETESCHEDULES", createParameterMap("ID=" + applicationId + "&CUSTOMERID=" + TEST_USER_ID), new XMLAPIResults(ADMIN_TOKEN));
	}

	@Test
	public void updateSchedule() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);

		setupUsers("admin");

		final HashMap<String, String> settings = new HashMap<>();
		settings.put("PARENT", "-1");
		settings.put("STATUS", "0");
		settings.put("JSON", "1");

		// Create schedule with missing parent
		doTests("WASUPDATESCHEDULE", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InputValidationFailed));

		settings.put("PARENT", "1");

		// Create schedule with parent that don't exist
		doTests("WASUPDATESCHEDULE", settings, new XMLAPIResults(ADMIN_TOKEN, ErrorCode.InternalServerError));

		settings.put("NAME", "Test App");
		settings.put("CUSTOMERID", "" + TEST_USER_ID);
		settings.put("PARENT", "-1");

		// Create schedule (application)
		final Long applicationId = doTests("WASUPDATESCHEDULE", settings, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.AccessDenied), new XMLAPIResults(ADMIN_TOKEN));

		settings.put("NAME", "Test Instance");
		settings.put("PARENT", "" + applicationId);

		// Create schedule (instance)
		final Long instanceId = doTests("WASUPDATESCHEDULE", settings, new XMLAPIResults(ADMIN_TOKEN));

		settings.remove("PARENT");
		settings.put("ID", "" + instanceId);

		// Update schedule
		doTests("WASUPDATESCHEDULE", settings, new XMLAPIResults(ADMIN_TOKEN));
	}

	@Test
	public void sendReportTargets() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(false);

		setupUsers("admin");

		final Long applicationId = doTests("WASUPDATESCHEDULE", createParameterMap("CUSTOMERID=" + TEST_USER_ID + "&JSON=1&NAME=Test App"), new XMLAPIResults(ADMIN_TOKEN));

		final long findingId =
				doTests("WASCREATEFINDING", createParameterMap("RULEID=250204&PORT=80&JSON=1&SCHEDULEID=" + applicationId + "&FINDINGTYPE=0&DATAURI=http://www.test.com"),
						new XMLAPIResults(ADMIN_TOKEN));

		doTests("REPORTTARGETDATA", createParameterMap("WEB=1&TARGETS=" + applicationId + ",&REPORTUSER=" + TEST_USER_ID),
				new XMLAPIResults(ADMIN_TOKEN, "XID", "" + findingId));
	}
}
