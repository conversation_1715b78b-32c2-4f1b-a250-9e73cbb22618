package com.chilicoders.bl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Objects.requireNonNull;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.spec.InvalidKeySpecException;
import java.sql.SQLException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipInputStream;

import org.apache.commons.io.FileUtils;
import org.apache.sshd.scp.server.ScpCommandFactory;
import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.auth.pubkey.AcceptAllPublickeyAuthenticator;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.apache.sshd.server.shell.ProcessShellFactory;
import org.assertj.core.api.Assertions;
import org.awaitility.Awaitility;
import org.bouncycastle.openssl.PEMParser;
import org.json.JSONException;
import org.junit.After;
import org.junit.Ignore;
import org.junit.Test;
import org.mockftpserver.fake.FakeFtpServer;
import org.mockftpserver.fake.UserAccount;
import org.mockftpserver.fake.filesystem.DirectoryEntry;
import org.mockftpserver.fake.filesystem.FileSystem;
import org.mockftpserver.fake.filesystem.UnixFakeFileSystem;
import org.xml.sax.SAXException;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.app.RequestFile;
import com.chilicoders.app.XMLAPI;
import com.chilicoders.boris.hiab.Backup;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.encryption.api.exception.EncryptionException;
import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.hiab.update.UpdateUtil;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.rest.models.CertificateCreationRequest;
import com.chilicoders.rest.models.Terms;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.HMACKey;
import com.chilicoders.util.StringUtils;
import com.chilicoders.xmlapi.TestResponse;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;
import com.google.common.collect.ImmutableMap;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class HiabToolsBusinessTest extends XMLAPITestBase {
	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * cleanup
	 */
	@After
	public void cleanup() {
		FileUtils.deleteQuietly(new File(Configuration.getProperty(ConfigurationKey.client_csr_file)));
		FileUtils.deleteQuietly(new File(Configuration.getProperty(ConfigurationKey.client_key_file)));
		stopLocalServer();
	}

	@Test
	public void testCreateCSR() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(true);

		final XMLAPIResults.ResponseListener listener = (response, params) -> {
			final ZipInputStream is = new ZipInputStream(new ByteArrayInputStream(response.getOs()));
			int count = 0;
			while (is.getNextEntry() != null) {
				count++;
			}
			assertEquals(2, count);
		};

		doTests("CREATECSR", "COMMONNAME=JUNIT&COUNTRY=SE", new XMLAPIResults(MAINUSER_TOKEN, listener));
	}

	@Test
	public void testDownloadKey() throws SQLException, IOException, JSONException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);
		createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);
		startLibellumMockServer(ServiceIdentity.Service.HIAB_SCHEDULER);

		final TestResponse response = performRequest("HIABDOWNLOADKEY", MAINUSER_TOKEN);
		assertTrue(response.getContentType().contains("application/octet-stream"));

		int counter = 0;
		try (final PEMParser pemParser = new PEMParser(new StringReader(response.getResult()))) {
			while (pemParser.readPemObject() != null) {
				counter++;
			}
		}
		assertTrue(counter >= 6);
	}

	@Test
	public void testHiabServerStatus() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		doTests("HIABSERVERSTATUS", "JSON=1", new XMLAPIResults(MAINUSER_TOKEN));
	}

	@Test
	public void testHiabSettingBackup() throws IOException, JSONException, SAXException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);
		createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);

		final List<String> expectedFiles = Stream.of(HiabToolsBusiness.SETTINGS_BACKUP_FILES).filter(file -> Files.isReadable(Paths.get(file))).collect(Collectors.toList());
		expectedFiles.add("/network.xml");
		final String encryptionKey = XmlAble.getString("SELECT backupencryptionkey FROM customers WHERE userid = ?", UserBusiness.HIAB_USER_ID);
		doTests("HIABSETTINGBACKUP", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getZipCheckListener(encryptionKey, false, expectedFiles.toArray(new String[0]))));

		final Path tmpFile = Files.createTempFile("settingsBackup", ".zip.clvm");
		final TestResponse response = performRequest("HIABSETTINGBACKUP", MAINUSER_TOKEN);
		Files.write(tmpFile, response.getResultData());
		final RequestFile requestFile = new RequestFile("CONTENT", tmpFile.toString(), null);
		doTests("HIABSETTINGUPLOADBACKUP", new HashMap<>(), new ArrayList<>(Collections.singletonList(requestFile)), new XMLAPIResults(MAINUSER_TOKEN));
	}

	@Test
	public void testHiabBackupData() throws IOException, JSONException, SAXException, SQLException {
		setupDatabaseForTest(true);

		doTests("HIABBACKUPDATA", "", new XMLAPIResults(MAINUSER_TOKEN, "BACKUPFTP_FREQUENCY", "1"));
	}

	@Test
	public void testHiabSettingsData() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(true);

		doTests("HIABSETUPDATA", "REPORT=1", new XMLAPIResults(MAINUSER_TOKEN, "REPORTFTP_CONNECTMETHOD", "1"));
		doTests("UPDATEHIABSETUPDATA", "UPDATE_FREQUENCY=2", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("UPDATEHIABSETUPDATA", "REPORT=1&REPORTSCP_PORT=23", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("HIABSETUPDATA", "", new XMLAPIResults(MAINUSER_TOKEN, "UPDATE_FREQUENCY", "2"));
		doTests("HIABSETUPDATA", "REPORT=1", new XMLAPIResults(MAINUSER_TOKEN, "REPORTSCP_PORT", "23"));
		doTests("UPDATEHIABSETUPDATA", "UPDATE_NEXTDATE=1977-03-10 10:00", new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));
	}

	@Test
	public void testShutdown() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		doTests("HIABSHUTDOWN", "", new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM bashcommands WHERE command='shutdown -1'"));
	}

	@Test
	public void testReboot() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		doTests("HIABREBOOT", "", new XMLAPIResults(MAINUSER_TOKEN));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM bashcommands WHERE command='reboot -1'"));
	}

	@Test
	public void testSummaryInfo() throws IOException, JSONException, SAXException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		doTests("HIABINFO", "", new XMLAPIResults(MAINUSER_TOKEN, (response, params) -> assertTrue(response.getResult().contains("Free memory"))));
	}

	@Test
	public void testGetInformation() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(true);

		doTests("UPDATESTATUS", "", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("CERTIFICATEINFORMATION", "", new XMLAPIResults(MAINUSER_TOKEN));
	}

	@Test
	@RequireDatabaseCommits
	public void testDownloadLogs() throws Exception {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);
		createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);

		final TestResponse downloadResponse = performRequest("DOWNLOADLOGS", MAINUSER_TOKEN, ImmutableMap.of("JSON", "1"));
		final String key = downloadResponse.getJsonResult().getJSONObject("data").getString("key");
		Awaitility.await()
				.atMost(10, TimeUnit.SECONDS)
				.until(() -> performRequest("SENDEXPORTCACHEDATA", MAINUSER_TOKEN).getXmlNodeValue("STATUS", "KEY", key).equals("Complete"));
		doTests("DOWNLOADEXPORT", "KEY=" + key, new XMLAPIResults(MAINUSER_TOKEN, (response, params) -> {
			try {
				ServiceProvider.getEncryptionService()
						.decrypt(EncryptionDecryptionRequest.builder()
								.input(new ByteArrayInputStream(response.getResultData()))
								.output(new ByteArrayOutputStream())
								.unsigned(true)
								.build());
			}
			catch (final EncryptionException ex) {
				throw new RuntimeException(ex);
			}
		}));
	}

	@Test
	public void testUploadUpdatePackageByMainUser() throws SQLException, JSONException, IOException, SAXException, EncryptionException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		runUploadUpdatePackageTest(MAINUSER_TOKEN);

		final Long keyValue = DbObject.getLong(getConnection(), "SELECT COALESCE((SELECT value::integer FROM datastore WHERE key = ?), -1)",
				DataStoreEntry.DataStoreEntryKeys.HIAB_UPDATER_SUBUSER_ID);
		assertThat(keyValue).isEqualTo(-1);
	}

	@Test
	public void testUploadUpdatePackageBySuperUSer() throws SQLException, JSONException, IOException, SAXException, EncryptionException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		runUploadUpdatePackageTest(SUPERUSER_TOKEN);

		Awaitility.await()
				.atMost(10, TimeUnit.SECONDS)
				.until(() -> DbObject.getLong(getConnection(), "SELECT COALESCE((SELECT value::integer FROM datastore WHERE key = ?), -1)",
						DataStoreEntry.DataStoreEntryKeys.HIAB_UPDATER_SUBUSER_ID) > 0);
	}

	@Test
	public void testUploadUpdatePackageBySubUSer() throws SQLException, JSONException, IOException, SAXException, EncryptionException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		runUploadUpdatePackageTest(LIMITEDTARGETS_TOKEN);

		Awaitility.await()
				.atMost(10, TimeUnit.SECONDS)
				.until(() -> DbObject.getLong(getConnection(), "SELECT COALESCE((SELECT value::integer FROM datastore WHERE key = ?), -1)",
						DataStoreEntry.DataStoreEntryKeys.HIAB_UPDATER_SUBUSER_ID) > 0);
	}

	/**
	 * Runs upload update package user given user's token
	 *
	 * @param token Access token of the user
	 */
	private void runUploadUpdatePackageTest(final String token) throws SQLException, JSONException, IOException, SAXException, EncryptionException {
		setupDatabaseForTest(true);
		createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);

		HiabToolsBusiness.updateRunning = false;
		final File scannerUpdate = new File(Configuration.getProperty(ConfigurationKey.scanner_update_file));
		FileUtils.deleteQuietly(scannerUpdate);

		final File tmp = File.createTempFile("tmp", ".upload");
		Files.write(Paths.get(tmp.getAbsolutePath()), "CONTENT".getBytes(UTF_8));

		final File encrypted = File.createTempFile("tmp", ".upload.gpg");
		try {
			try (final FileOutputStream outputStream = new FileOutputStream(encrypted)) {
				ServiceProvider.getEncryptionService().encrypt(EncryptionDecryptionRequest.builder().input(tmp.toPath()).output(outputStream).build());
			}

			final ArrayList<RequestFile> validFiles = new ArrayList<>();
			validFiles.add(new RequestFile("CONTENT", encrypted.getAbsolutePath(), null));
			doTests("HIABUPLOADUPDATEPACKAGE", new HashMap<>(), validFiles, new XMLAPIResults(token));
			Awaitility.await().atMost(3, TimeUnit.SECONDS).until(scannerUpdate::exists);

			assertTrue(scannerUpdate.exists());
			final String updateFile = FileUtils.readFileToString(scannerUpdate, UTF_8).split(" ")[0];
			assertEquals("CONTENT", FileUtils.readFileToString(new File(updateFile), UTF_8));
			assertTrue(HiabToolsBusiness.updateRunning);
			HiabToolsBusiness.updateRunning = false;
		}
		finally {
			FileUtils.deleteQuietly(tmp);
			FileUtils.deleteQuietly(encrypted);
			FileUtils.deleteQuietly(scannerUpdate);
		}
	}

	@Test
	public void testHiabTest() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		// The json flag is only needed for the testframework to treat the result as json
		doTests("HIABTEST", "TYPE=PING&TESTIP=*******&JSON=1", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("HIABTEST", "TYPE=TRACEROUTE&TESTIP=*******&JSON=1", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("HIABTEST", "TYPE=TCPTRACEROUTE&TESTIP=*******&TESTPORT=80&JSON=1", new XMLAPIResults(MAINUSER_TOKEN));
	}

	@Test
	public void testFailedEnrollViaOutscan() throws SQLException, IOException, JSONException, SAXException, InvalidKeySpecException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		try {
			startLocalServer(true);
			HMACKey.getPublicKey();
			Configuration.setProperty(ConfigurationKey.outscan_url, "https://localhost:" + getLocalServerPort());
			doTests("ENROLL", "USERNAME=JUNITTEST&PASSWORD=security2", new XMLAPIResults(MAINUSER_TOKEN));
			Awaitility.await().atMost(30, TimeUnit.SECONDS).until(() -> XMLAPI.isEnrollFailed());
			assertEquals(0, XmlAble.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM thiabstats WHERE xuserxid=?", TEST_USER_ID));
			assertFalse(new File(Configuration.getProperty(ConfigurationKey.enrolled_file)).exists());
			assertTrue(XMLAPI.isEnrollFailed());
		}
		finally {
			stopLocalServer();
		}
	}

	@Test
	@Ignore
	public void testEnrollWithSubuserViaOutscan() throws SQLException, IOException, JSONException, SAXException, InvalidKeySpecException {
		setupDatabaseForTest(true);

		try {
			XMLAPI.setEnrollFailed(false);
			HMACKey.getPublicKey();
			startLocalServer(true);
			FileUtils.deleteQuietly(new File(ServiceProvider.getLibellumConfig().getClientCertificatePath()));
			Configuration.setProperty(ConfigurationKey.outscan_url, "https://localhost:" + getLocalServerPort());

			doTests("ENROLL", "USERNAME=SUBUSER_ALLACCESS&PASSWORD=security", new XMLAPIResults(MAINUSER_TOKEN));
			Awaitility.await().atMost(30, TimeUnit.SECONDS).until(() -> new File(Configuration.getProperty(ConfigurationKey.enrolled_file)).exists());
			assertEquals(1, XmlAble.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM thiabstats WHERE xuserxid=?", TEST_USER_ID));
			assertFalse(XMLAPI.isEnrollFailed());
		}
		finally {
			stopLocalServer();
		}
	}

	@Test
	@Ignore
	public void testEnrollViaOutscan() throws SQLException, IOException, JSONException, SAXException, InvalidKeySpecException {
		setupDatabaseForTest(true);

		try {
			XMLAPI.setEnrollFailed(false);
			HMACKey.getPublicKey();
			startLocalServer(true);
			FileUtils.deleteQuietly(new File(ServiceProvider.getLibellumConfig().getClientCertificatePath()));

			Configuration.setProperty(ConfigurationKey.outscan_url, "https://localhost:" + getLocalServerPort());
			doTests("ENROLL", "USERNAME=JUNITTEST&PASSWORD=security", new XMLAPIResults(MAINUSER_TOKEN));
			Awaitility.await().atMost(30, TimeUnit.SECONDS).until(() -> new File(Configuration.getProperty(ConfigurationKey.enrolled_file)).exists());
			assertEquals(1, XmlAble.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM thiabstats WHERE xuserxid=?", TEST_USER_ID));
			assertFalse(XMLAPI.isEnrollFailed());
		}
		finally {
			stopLocalServer();
		}
	}

	@Test
	@SuppressFBWarnings(value = "RU_INVOKE_RUN")
	public void testBackchannel() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(true);

		final File f = new File("/tmp/rs_active");
		if (f.exists()) {
			FileUtils.deleteQuietly(f);
		}

		doTests("HIABBACKCHANNEL", "", new XMLAPIResults(MAINUSER_TOKEN, "BACKCHANNEL", "0"));
		doTests("UPDATEHIABBACKCHANNEL", "ENABLE=1", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("HIABBACKCHANNEL", "", new XMLAPIResults(MAINUSER_TOKEN, "BACKCHANNEL", "1"));
		doTests("UPDATEHIABBACKCHANNEL", "ENABLE=0", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("HIABBACKCHANNEL", "", new XMLAPIResults(MAINUSER_TOKEN, "BACKCHANNEL", "0"));
	}

	@Test
	@SuppressFBWarnings(value = "RU_INVOKE_RUN")
	public void testBackupImportFtp() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);

		final FakeFtpServer ftp = new FakeFtpServer();
		ftp.setServerControlPort(0);

		final FileSystem fs = new UnixFakeFileSystem();
		fs.add(new DirectoryEntry("/"));
		ftp.setFileSystem(fs);

		final UserAccount ua = new UserAccount("username", "password", "/");
		ftp.addUserAccount(ua);

		ftp.start();

		try {
			final Map<String, String> map = new HashMap<>();
			map.put("JSON", "1");
			map.put("BACKUPFTP_NEXTDATE", "2018-01-01");
			map.put("BACKUPFTP", "localhost");
			map.put("BACKUPFTP_PORT", "" + ftp.getServerControlPort());
			map.put("BACKUPFTP_NAME", "username");
			map.put("BACKUPFTP_PWD", "password");
			map.put("BACKUPFTP_DIR", "/");
			map.put("BACKUPFTP_CONNECTMETHOD", "1");

			doTests("UPDATEHIABBACKUPDATA", map, new XMLAPIResults(MAINUSER_TOKEN));

			// Backup

			final File backupPath = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_sql_export_path));
			FileUtils.deleteQuietly(backupPath);
			final File localBackupFile = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_localbackup_file));
			FileUtils.deleteQuietly(localBackupFile);

			final Backup backup = new Backup((String) null);
			backup.run();

			assertTrue(localBackupFile.exists());

			// Import

			doTests("HIABLISTIMPORTS", createParameterMap(""), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1, "IMPORT")));

			final File importPath = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_sql_import_path));
			FileUtils.deleteQuietly(importPath);

			@SuppressWarnings("unchecked")
			final List<String> files = fs.listNames("/");
			assertEquals(1, files.size());

			final String importFile = files.get(0);

			final Backup imp = new Backup(importFile);
			imp.run();

			assertTrue(new File(importPath.getPath() + File.separator + importFile).exists());
			assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(xid) FROM tusers WHERE xid = ?", UserBusiness.HIAB_USER_ID));

			DbObject.executeUpdate(getConnection(), "DELETE FROM tusers WHERE xid = ?", UserBusiness.HIAB_USER_ID);
			getConnection().commit();

			FileUtils.deleteQuietly(backupPath);
			FileUtils.deleteQuietly(localBackupFile);
			FileUtils.deleteQuietly(importPath);
		}
		finally {
			ftp.stop();
		}

		Configuration.setProperty(ConfigurationKey.hiab_backup_ftp, "");
	}

	@Test
	@SuppressFBWarnings(value = "RU_INVOKE_RUN")
	public void testBackupImportScp() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);

		final File scpDir = new File("/tmp/scp");
		FileUtils.deleteQuietly(scpDir);
		FileUtils.forceMkdir(scpDir);

		try (final SshServer sshd = SshServer.setUpDefaultServer()) {
			sshd.setHost("localhost");
			sshd.setPort(0);
			sshd.setKeyPairProvider(new SimpleGeneratorHostKeyProvider(new File(scpDir.getAbsolutePath() + "/test.ser").toPath()));
			sshd.setPublickeyAuthenticator(AcceptAllPublickeyAuthenticator.INSTANCE);
			sshd.setPasswordAuthenticator((username, password, session) -> true);
			final ScpCommandFactory factory = new ScpCommandFactory();
			factory.setDelegateCommandFactory((channel, command) -> new ProcessShellFactory(command, command.split(" ")).createShell(channel));
			sshd.setCommandFactory(factory);

			sshd.start();

			final Map<String, String> map = new HashMap<>();
			map.put("JSON", "1");
			map.put("BACKUPSCP", "localhost");
			map.put("BACKUPSCP_PORT", "" + sshd.getPort());
			map.put("BACKUPSCP_NAME", "username");
			map.put("BACKUPSCP_PWD", "password");
			map.put("BACKUPSCP_DIR", scpDir.getAbsolutePath());

			doTests("UPDATEHIABBACKUPDATA", map, new XMLAPIResults(MAINUSER_TOKEN));

			// Backup

			final File backupPath = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_sql_export_path));
			FileUtils.deleteQuietly(backupPath);
			final File localBackupFile = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_localbackup_file));
			FileUtils.deleteQuietly(localBackupFile);

			final Backup backup = new Backup((String) null);
			backup.run();

			assertTrue(localBackupFile.exists());

			// Import

			doTests("HIABLISTIMPORTS", createParameterMap(""), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2, "IMPORT")));

			final File importPath = new File(Configuration.getProperty(ConfigurationKey.hiab_dump_sql_import_path));
			FileUtils.deleteQuietly(importPath);

			String importFile = null;
			final File[] files = scpDir.listFiles();
			if (files != null) {
				for (final File file : files) {
					if (file.getName().startsWith("dumpData")) {
						importFile = file.getName();
					}
				}
			}
			assertFalse(StringUtils.isEmpty(importFile));

			final Backup imp = new Backup(importFile);
			imp.run();

			assertTrue(new File(importPath.getPath() + File.separator + importFile).exists());
			assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(xid) FROM tusers WHERE xid = ?", UserBusiness.HIAB_USER_ID));

			DbObject.executeUpdate(getConnection(), "DELETE FROM tusers WHERE xid = ?", UserBusiness.HIAB_USER_ID);
			getConnection().commit();

			FileUtils.deleteQuietly(backupPath);
			FileUtils.deleteQuietly(localBackupFile);
			FileUtils.deleteQuietly(importPath);
		}

		Configuration.setProperty(ConfigurationKey.hiab_backup_scp, "");
		FileUtils.deleteQuietly(scpDir);
	}

	@Test
	@Ignore
	public void testOfflineEnrollment() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(true);

		FileUtils.deleteQuietly(new File(ServiceProvider.getLibellumConfig().getClientCertificatePath()));
		final File enrollmentKey = new File(Configuration.getProperty(ConfigurationKey.update_package_path) + "offlineenroll.key");
		FileUtils.deleteQuietly(enrollmentKey);

		TestResponse response = performRequest("HIABENROLLMENTKEY", MAINUSER_TOKEN, createMap(createParameterMap("JSON=1")));
		final String key = response.getJsonResult().getJSONObject("data").getString("enrollmentkey");
		assertFalse(key.isEmpty());
		assertTrue(enrollmentKey.exists());

		setHiab(false);

		final Map<String, String> map = new HashMap<>();
		map.put("JSON", "1");
		map.put("ENROLLMENTKEY", key);

		response = performRequest("HIABDOWNLOADENROLLMENTPACKAGE", MAINUSER_TOKEN, map);
		final String enrollmentFile = response.getResult();
		assertTrue(enrollmentFile.length() > 1000);
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(id) FROM thiabstats WHERE xuserxid = ?", TEST_USER_ID));

		setHiab(true);

		map.clear();
		final File scannerEnroll = new File(Configuration.getProperty(ConfigurationKey.scanner_enroll_file));
		FileUtils.deleteQuietly(scannerEnroll);

		final ArrayList<RequestFile> files = new ArrayList<>();
		files.add(new RequestFile("CONTENT", "enroll", enrollmentFile.getBytes(UTF_8)));

		doTests("HIABUPLOADENROLLMENTPACKAGE", map, files, new XMLAPIResults(MAINUSER_TOKEN));
		Awaitility.await().atMost(1, TimeUnit.SECONDS).until(scannerEnroll::exists);

		assertTrue(scannerEnroll.exists());
		FileUtils.deleteQuietly(scannerEnroll);
		FileUtils.deleteQuietly(enrollmentKey);
	}

	@Test
	public void testAddCertificateInfoForOnlyHiabs() {

		// need to add test for new key

		/*setExpiredCertificate();
		final long creatorId = 1111;
		final boolean isForHiab = true;

		final String downloadKeyString = StringUtils.readTextFile(testPath + "downloadkey.txt");
		final String[] downloadKey = HiabToolsBusiness.extractKeyInfo(downloadKeyString);
		assertThat(downloadKey.length).isEqualTo(CERTIFICATE_RENEWAL_DATA_INDEX + 1);

		downloadKeyString = HiabToolsBusiness.generateHiabDownloadKey(getConnection(), creatorId, !isForHiab);
		downloadKey = HiabToolsBusiness.extractKeyInfo(downloadKeyString);

		assertThat(downloadKey.length).isEqualTo(CERTIFICATE_RENEWAL_DATA_INDEX);*/
	}

	@Test
	public void testScannerCertificateRequestHasScannerId() throws Exception {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		final long scanner1Id = XMLAPITestBase.createScannerWithCertificateRequest(getConnection());
		final long scanner2Id = XMLAPITestBase.createScannerWithCertificateRequest(getConnection());

		final List<CertificateCreationRequest> certificateList = UpdateUtil.createCertificateRequestList(getConnection());

		assertThat(certificateList.stream().anyMatch(data -> data.getScannerId() == scanner1Id)).isTrue();
		assertThat(certificateList.stream().anyMatch(data -> data.getScannerId() == scanner2Id)).isTrue();
	}

	@Test
	public void testUpdatePolicyPeriod() {
		// normal and boundary cases
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(10, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isTrue();
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(90, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isTrue();
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(91, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isFalse();
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(400, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isFalse();

		// HIAB-somehow-updated-in-the-future case
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().plus(10, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isTrue();

		// property change case
		Configuration.setProperty(ConfigurationKey.update_policy_grace_duration, "P35D");
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(35, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isTrue();
		Assertions.assertThat(UpdateUtil.isUpdateAllowed(Instant.now().minus(36, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS))).isFalse();
		// reset property
		Configuration.setProperty(ConfigurationKey.update_policy_grace_duration, "P90D");
	}

	@Test
	public void testSqlTerms() throws SQLException, IOException {
		setupDatabaseForTest(false);

		DbObject.executeUpdate(getConnection(), "DELETE FROM terms");
		final Long templateId = DbObject.getLong(getConnection(), "INSERT INTO termstemplates(name, content) VALUES ('template1', 'content1') RETURNING id");
		assertThat(templateId).isNotNull();
		DbObject.executeUpdate(getConnection(),
				"INSERT INTO terms(mainusermustaccept, allusersmustaccept, termstemplateid, customerid) VALUES (true, false, ?, (SELECT id FROM customers WHERE userid = ?))",
				templateId, TEST_USER_ID);
		DbObject.executeUpdate(getConnection(),
				"INSERT INTO terms(mainusermustaccept, allusersmustaccept, termstemplateid, customerid) VALUES (false, true, ?, (SELECT id FROM customers WHERE userid = ?))",
				templateId, TEST_USER_ID);
		final String sqlFile = HiabToolsBusiness.createClientInfoSqlFile(getConnection(), TEST_USER_ID, "fakekey");
		final String sql = FileUtils.readFileToString(new File(sqlFile), UTF_8);
		DbObject.execute(getConnection(), sql);
		final String termsUpdate = requireNonNull(DbObject.getString(getConnection(), "SELECT * FROM termsupdate")).replaceAll("COMMIT;", "");
		DbObject.execute(getConnection(), termsUpdate);
		final List<Terms> terms =
				Terms.fetchObjects(Terms.class, getConnection(), Access.ADMIN, "customerid = (SELECT id FROM customers WHERE userid = ?)", UserBusiness.HIAB_USER_ID);
		assertEquals(2, terms.size());
		assertEquals((Long) 2L, DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM terms"));
	}
}
