package com.chilicoders.bl;

import static com.chilicoders.cache.ConnectionCache.PROPERTY_PREFIX;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assume.assumeFalse;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chilicoders.core.configuration.common.ConfigKeys;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.json.JSONException;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.xml.sax.SAXException;
import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.db.DbObject;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.xmlapi.TestResponse;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class UpdaterBusinessTest extends XMLAPITestBase {
	private static Date SAMPLE_DATE;
	private static final long SAMPLE_PATCH_ID = 9990L;
	private static final long SAMPLE_INFO_ID = 9991L;
	private static final long SAMPLE_CYR3CON_ID = 9992L;

	private static final List<String> SAMPLE_DATAS = Arrays.asList("9990\tmicrosoft/windows\tKB4484099\tKB4486696",
			"9991\tmy product/my product\tmy product");
	private static double LOCAL_PATCH;

	/**
	 * Initialize once before running all tests
	 */
	@BeforeClass
	public static void init() {
		startLocalServer(true);
		setOutscanUrlToJerseyRestUrl();

		final Calendar cal = Calendar.getInstance();
		cal.set(2019, Calendar.DECEMBER, 31);
		SAMPLE_DATE = cal.getTime();

		final FluentConfiguration config = Flyway.configure();
		config.dataSource(Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.jdbc_url, PROPERTY_PREFIX), Configuration.getPropertyWithOptionalPrefix(
				ConfigurationKey.db_username, PROPERTY_PREFIX), Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.db_password, PROPERTY_PREFIX));
		config.table("schema_version");
		final Flyway flyway = config.load();
		LOCAL_PATCH = StringUtils.getDecimalValue(flyway.info().current().getVersion().toString());
	}

	@AfterClass
	public static void cleanup() {
		stopLocalServer();
	}

	@Test
	public void testTrigger_WhenInsertWithUpdatedField_InputUpdatedFiledShouldBeSet() throws SQLException {
		try {
			insertSampleData(SAMPLE_DATE, false);
			verifyUpdatedField(SAMPLE_DATE);
		}
		finally {
			removeInsertedData(false);
		}
	}

	@Test
	public void testTrigger_WhenInsertWithoutUpdatedField_UpdatedFiledShouldBeSetToNow() throws SQLException {
		try {
			insertSampleData(null, false);
			verifyUpdatedField(new Date());
		}
		finally {
			removeInsertedData(false);
		}
	}

	@Test
	public void testTrigger_WhenUpdateWithUpdatedField_InputUpdatedFiledShouldNotBeSetButUsingNow() throws SQLException {
		try {
			final Calendar cal = Calendar.getInstance();
			cal.set(2020, Calendar.MAY, 28);
			final Date tempDate = cal.getTime();

			insertSampleData(tempDate, false);

			DbObject.executeUpdate(getConnection(), "UPDATE tpatchsupersedence SET updated = '2020-05-28' WHERE xid = ?", SAMPLE_PATCH_ID);
			DbObject.executeUpdate(getConnection(), "UPDATE tproductinformation SET updated = '2020-05-28' WHERE xid = ?", SAMPLE_INFO_ID);
			getConnection().commit();

			verifyUpdatedField(new Date());
		}
		finally {
			removeInsertedData(false);
		}
	}

	/**
	 * Insert sample data for testing
	 *
	 * @param updatedDate input for updated field
	 * @param hasCyr3con include inserting data for cyr3con table or not
	 */
	private void insertSampleData(final Date updatedDate, final boolean hasCyr3con) throws SQLException {
		final long patchCount =
				DbObject.executeUpdate(getConnection(), "INSERT INTO tpatchsupersedence(xid, product, patch, supersededby, updated) VALUES(?, ?, ?, ?, ?::DATE)",
						SAMPLE_PATCH_ID, "microsoft/windows", "KB4484099", "KB4486696", DateUtils.formatDate(updatedDate));
		final long infoCount = DbObject.executeUpdate(getConnection(), "INSERT INTO tproductinformation(xid, product, name, updated) VALUES(?, ?, ?, ?::DATE)",
				SAMPLE_INFO_ID, "my product/my product", "my product", DateUtils.formatDate(updatedDate));
		if (hasCyr3con) {
			final long cyr3conCount = DbObject.executeUpdate(getConnection(), "INSERT INTO cyr3con(id, cve, cyrating, " +
					"exploitprobability, updated) VALUES (?, 'CVE-1999-0015', 38.46, 1.000, ?::DATE)", SAMPLE_CYR3CON_ID, SAMPLE_DATE);
			Assert.assertEquals(1, cyr3conCount);
		}
		getConnection().commit();
		Assert.assertEquals(1, patchCount);
		Assert.assertEquals(1, infoCount);
	}

	/**
	 * Verify updated field with expected date
	 *
	 * @param expectedDate expected date for verify
	 */
	private void verifyUpdatedField(final Date expectedDate) throws SQLException {
		final Date patchDate = (Date) DbObject.getObject(getConnection(), "SELECT updated FROM tpatchsupersedence " +
				"WHERE xid = ?", SAMPLE_PATCH_ID);
		final Date infoDate = (Date) DbObject.getObject(getConnection(), "SELECT updated FROM tproductinformation " +
				"WHERE xid = ?", SAMPLE_INFO_ID);

		Assert.assertEquals(DateUtils.formatDate(expectedDate), DateUtils.formatDate(patchDate));
		Assert.assertEquals(DateUtils.formatDate(expectedDate), DateUtils.formatDate(infoDate));
	}

	/**
	 * Cleanup inserted data
	 *
	 * @param hasCyr3con include removing inserted data in cyr3con table or not
	 */
	private void removeInsertedData(final boolean hasCyr3con) throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM tpatchsupersedence WHERE xid = ?", SAMPLE_PATCH_ID);
		DbObject.executeUpdate(getConnection(), "DELETE FROM tproductinformation WHERE xid = ?", SAMPLE_INFO_ID);
		if (hasCyr3con) {
			DbObject.executeUpdate(getConnection(), "DELETE FROM cyr3con WHERE id = ?", SAMPLE_CYR3CON_ID);
		}
		getConnection().commit();
	}

	@Test
	@RequireDatabaseCommits
	public void testHiabUpdateRequest_WhenNotInputDateParams_ShouldContainExpectedRecords() throws SQLException, IOException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(false);
		prepareApplianceData();
		try {
			insertSampleData(SAMPLE_DATE, true);

			doTests("OUTSCAN_UPDATEHIAB", buildSampleParams(null), new XMLAPIResults(MAINUSER_TOKEN, (response, params) -> {
				final String resultData = new String(response.getResultData(), UTF_8);
				assertBeginAndEndOfResultData(resultData);
				SAMPLE_DATAS.forEach(x -> assertTrue(resultData.contains(x)));
			}));
		}
		finally {
			removeInsertedData(true);
		}
	}

	@Test
	@RequireDatabaseCommits
	public void testHiabUpdateRequest_WhenInputDateParamsAreNow_ShouldNotContainExpectedRecords() throws SQLException, IOException, SAXException {
		assumeFalse(Configuration.isKubernetesEnabled());
		setupDatabaseForTest(false);
		prepareApplianceData();
		try {
			insertSampleData(SAMPLE_DATE, true);

			doTests("OUTSCAN_UPDATEHIAB", buildSampleParams(DateUtils.getCurrentDate()), new XMLAPIResults(MAINUSER_TOKEN, (response, params) -> {
				final String resultData = new String(response.getResultData(), UTF_8);
				assertBeginAndEndOfResultData(resultData);
				SAMPLE_DATAS.forEach(x -> assertFalse(resultData.contains(x)));
			}));
		}
		finally {
			removeInsertedData(true);
		}
	}

	@Test
	@RequireDatabaseCommits
	public void testHiabUpdateRequest_WhenDateParamsBeforeUpdatedDate_ShouldContainExpectedRecord() throws SQLException, IOException, SAXException {
		assumeFalse(Configuration.isKubernetesEnabled());
		setupDatabaseForTest(false);
		prepareApplianceData();
		try {
			insertSampleData(SAMPLE_DATE, true);
			final Calendar cal = Calendar.getInstance();
			cal.set(2018, Calendar.DECEMBER, 31);

			doTests("OUTSCAN_UPDATEHIAB", buildSampleParams(DateUtils.formatTimeDate(cal.getTime())), new XMLAPIResults(MAINUSER_TOKEN, (response, params) -> {
				final String resultData = new String(response.getResultData(), UTF_8);
				assertBeginAndEndOfResultData(resultData);
				SAMPLE_DATAS.forEach(x -> assertTrue(resultData.contains(x)));
			}));
		}
		finally {
			removeInsertedData(true);
		}
	}

	/**
	 * Assert the beginning and the end of Result Data
	 *
	 * @param resultData result data
	 */
	private void assertBeginAndEndOfResultData(final String resultData) {
		assertTrue(resultData.startsWith("BEGIN;"));
		assertTrue(resultData.endsWith("COMMIT;\n"));
	}

	/**
	 * Build parameters map for OUTSCAN_UPDATEHIAB
	 *
	 * @param dateParam date parameter for filter on updated field
	 * @return Customer Id
	 */
	private Map<String, String> buildSampleParams(final String dateParam) {
		final Map<String, String> params = new HashMap<>();
		params.put("SUBJECTKEYID", TEST_UUID);
		params.put("LASTPATCH", "" + LOCAL_PATCH);
		params.put("LASTUPDATE", DateUtils.getCurrentDate());
		params.put("VERSION", "1.1");
		params.put("UIVERSION", "1.2");
		params.put("SCANNERVERSION", "1.3");
		params.put("RULESVERSION", DateUtils.getCurrentDate());
		params.put("RULEDATE", DateUtils.getCurrentDate());
		params.put("EXPLOITDATE", DateUtils.getCurrentDate());
		params.put("PATCHSUPERSEDENCEDATE", dateParam);
		params.put("PRODUCTINFORMATIONDATE", dateParam);
		return params;
	}

	/**
	 * Insert hiab, customer and appliance record to Database for testing
	 *
	 * @return Customer Id
	 */
	private Long prepareApplianceData() throws SQLException {
		final Long hiabId =
				DbObject.getLong(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES(?, ?, ?, 1) RETURNING id", TEST_USER_ID, TEST_UUID,
						"hiab");
		final Long customerId = DbObject.getLong(getConnection(), "SELECT id FROM customers WHERE userid = ?", XMLAPITestBase.TEST_USER_ID);
		DbObject.executeUpdate(getConnection(), "INSERT INTO appliances(\"primary\", virtual, hiabid, customerid, uuid) VALUES (?, ?, ?, ?, ?)", true, true, hiabId,
				customerId, TEST_UUID);
		getConnection().commit();

		return customerId;
	}

	@Test
	public void testHiabUpdateRequest() throws SQLException, IOException, JSONException, SAXException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		setupDatabaseForTest(false);
		final Long customerId = prepareApplianceData();
		Assert.assertNull(DbObject.getString(getConnection(), "SELECT version FROM thiabstats WHERE uuid=?", TEST_UUID));

		doTests("OUTSCAN_UPDATEHIAB", buildSampleParams(null), new XMLAPIResults(MAINUSER_TOKEN,
				(response, params) -> assertBeginAndEndOfResultData(new String(response.getResultData(), UTF_8))));

		Assert.assertEquals(true, DbObject.getBoolean(getConnection(), "SELECT NOW() - '1 minute'::INTERVAL < dupdate " +
				"FROM thiabstats WHERE uuid=?", TEST_UUID));
		Assert.assertEquals("1.1", DbObject.getString(getConnection(), "SELECT version FROM thiabstats WHERE uuid=?", TEST_UUID));

		// Scanner update via scheduler
		final Long scannerHiabId =
				DbObject.getLong(getConnection(), "INSERT INTO thiabstats(xuserxid, uuid, product, scheduler) VALUES(?, ?, ?, 0) RETURNING id", TEST_USER_ID,
						TEST_UUID + "-scanner", "hiab");
		DbObject.executeUpdate(getConnection(), "INSERT INTO appliances(\"primary\", virtual, hiabid, customerid, uuid) VALUES (?, ?, ?, ?, ?)", true, true, scannerHiabId,
				customerId, TEST_UUID + "-scanner");
		getConnection().commit();

		final Map<String, String> parameters = buildSampleParams(null);
		parameters.put("SCANNERKEYID", TEST_UUID + "-scanner");
		parameters.put("SCANNERUPDATE", "1");
		parameters.put("VERSION", "1.4");

		doTests("OUTSCAN_UPDATEHIAB", parameters, new XMLAPIResults(MAINUSER_TOKEN));

		Assert.assertEquals("1.1", DbObject.getString(getConnection(), "SELECT version FROM thiabstats WHERE uuid=?", TEST_UUID));
		Assert.assertEquals("1.4", DbObject.getString(getConnection(), "SELECT version FROM thiabstats WHERE uuid=?", TEST_UUID + "-scanner"));

		final Map<String, String> params = buildSampleParams(null);
		// Wrong subject key id
		params.put("SUBJECTKEYID", "wrongid");
		doTests("OUTSCAN_UPDATEHIAB", params, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));

		// wrong mac test
		params.put("MAC", "mac_value");
		doTests("OUTSCAN_UPDATEHIAB", params, new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));

		// Empty param test
		doTests("OUTSCAN_UPDATEHIAB", "", new XMLAPIResults(MAINUSER_TOKEN, ErrorCode.InputValidationFailed));
	}

	@Test
	public void testOnlineEnrollmentWithMissingImageVersion() throws SQLException, IOException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);

		final Map<String, String> params = new HashMap<>();
		params.put("JSON", "1");
		params.put("CERTIFICATEREQUESTS", "{}");
		params.put("ENROLLMENTKEY", "{}");
		final TestResponse response = performRequest("OUTSCAN_ENROLLHIAB", MAINUSER_TOKEN, params);
		assertThat(response.isJsonSuccess()).isFalse();
		assertThat(response.getJsonErrorCode()).isEqualTo(500);
		assertThat(response.getJsonErrorMessage()).contains(getMessage("_GET_LATEST_IMAGE"));
	}

	@Test
	public void testOnlineEnrollmentWithInvalidImageVersion() throws SQLException, IOException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		setupDatabaseForTest(true);

		final Map<String, String> params = new HashMap<>();
		params.put("JSON", "1");
		params.put("CERTIFICATEREQUESTS", "{}");
		params.put("ENROLLMENTKEY", "{}");
		params.put("HIABIMAGEVERSION", "invalid");
		final TestResponse response = performRequest("OUTSCAN_ENROLLHIAB", MAINUSER_TOKEN, params);
		assertThat(response.isJsonSuccess()).isFalse();
		assertThat(response.getJsonErrorCode()).isEqualTo(500);
		assertThat(response.getJsonErrorMessage()).contains(getMessage("_GET_LATEST_IMAGE"));
	}
}
