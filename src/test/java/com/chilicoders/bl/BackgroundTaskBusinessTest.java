package com.chilicoders.bl;

import static org.mockito.Mockito.when;

import java.io.IOException;
import java.sql.SQLException;

import org.json.JSONException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.xml.sax.SAXException;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.SubUser;
import com.chilicoders.task.BackgroundTaskService;
import com.chilicoders.task.BackgroundTaskType;
import com.chilicoders.task.impl.BackgroundTaskServiceFactory;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class BackgroundTaskBusinessTest extends XMLAPITestBase {

	@Mock
	private UserDetails user;

	@Mock
	private UserDetails subuser;

	private String subuserToken;
	private BackgroundTaskService taskService;

	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Set up for UTs. Init mocks, db test setup, initialize service being tested.
	 */
	@Before
	public void init() throws SQLException, IOException {
		setupDatabaseForTest(false);
		MockitoAnnotations.openMocks(this);

		when(user.getMainUserId()).thenReturn(TEST_USER_ID);
		when(user.getSubUserId()).thenReturn(-1L);

		subuserToken = getSubuser(new String[] {});
		final SubUser subUser = SubUser.getByField(SubUser.class, getConnection(), Access.ADMIN, "VCUSERNAME", subuserToken);
		when(subuser.getMainUserId()).thenReturn(TEST_USER_ID);
		when(subuser.getSubUserId()).thenReturn(subUser.getId());

		taskService = BackgroundTaskServiceFactory.newBackgroundTaskService(getConnection());
	}

	@Test
	public void testSendBackgroundTasks() throws SQLException, JSONException, IOException, SAXException {
		taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
		taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2)));
	}

	@Test
	public void testSendBackgroundTaksSubuserToken() throws SQLException, JSONException, IOException, SAXException {
		final String subuserToken = getSubuser(new String[] {});
		final SubUser subUser = SubUser.getByField(SubUser.class, getConnection(), Access.ADMIN, "VCUSERNAME", subuserToken);
		when(subuser.getSubUserId()).thenReturn(subUser.getId());

		taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(1)));
		taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(2)));
	}

	@Test
	public void testSendBackgroundTaksSubuserDoesNotGetMainUserTasks() throws SQLException, JSONException, IOException, SAXException {
		taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(2)));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(0)));
	}

	@Test
	public void testSendBackgroundTaksMainUserDoesNotGetSubUserTasks() throws SQLException, JSONException, IOException, SAXException {
		taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0)));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(2)));
	}

	@Test
	public void testClearBackgroundTasks() throws SQLException, JSONException, IOException, SAXException {
		final long taskId = taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
		taskService.markTaskDone(taskId);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0)));
	}

	@Test
	public void testClearBackgroundTasksSubuser() throws SQLException, JSONException, IOException, SAXException {
		final long taskId = taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(subuserToken));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(1)));
		taskService.markTaskDone(taskId);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(subuserToken));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(0)));
	}

	@Test
	public void testClearBackgroundTasksSubuserDoesNotClearMainUserTasks() throws SQLException, JSONException, IOException, SAXException {
		final long taskId = taskService.startTask(user, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
		taskService.markTaskDone(taskId);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(subuserToken));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1)));
	}

	@Test
	public void testClearBackgroundTasksMainUserDoesNotClearSubUserTasks() throws SQLException, JSONException, IOException, SAXException {
		final long taskId = taskService.startTask(subuser, BackgroundTaskType.COMPLIANCE_REPORT);
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(1)));
		taskService.markTaskDone(taskId);
		doTests("CLEARBACKGROUNDTASKS", "", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("BACKGROUNDTASKS", "", new XMLAPIResults(subuserToken, XMLAPIResults.getCountListener(1)));
	}
}
