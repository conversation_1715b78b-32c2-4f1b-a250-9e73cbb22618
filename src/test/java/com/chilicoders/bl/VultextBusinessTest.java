package com.chilicoders.bl;

import java.io.IOException;
import java.sql.SQLException;

import org.json.JSONException;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.util.Configuration;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class VultextBusinessTest extends XMLAPITestBase {
	static {
		XMLAPITestBase.initConfiguration();
	}

	@Test
	public void testScriptData() throws SQLException, IOException, JSONException, SAXException {
		setupDatabaseForTest(true);
		final int maxLimit = Configuration.getProperty(ConfigKeys.ConfigurationIntKey.xml_api_max_limit);

		doTests("SCRIPTDATA", "limit=50", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(50, "SCRIPT")));
		doTests("SCRIPTDATA", "limit=" + (maxLimit * 2), new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0, (maxLimit * 2))));
	}

	@Test
	public void testScriptComments() throws JSONException, IOException, SAXException, SQLException {
		setupDatabaseForTest(true);

		doTests("UPDATESCRIPTCOMMENT", "SCRIPTID=1217363&COMMENT=Comment", new XMLAPIResults(MAINUSER_TOKEN));
		doTests("SCRIPTCOMMENTDATA", "SCRIPTID=1217363", new XMLAPIResults(MAINUSER_TOKEN, "COMMENT", "Comment"));
		final long id = XmlAble.executeCountQuery(getConnection(), "SELECT xid FROM tvultextscomments ORDER BY xid DESC LIMIT 1");
		doTests("UPDATESCRIPTCOMMENT", "COMMENT=Changed&XID=" + id, new XMLAPIResults(MAINUSER_TOKEN));
		doTests("SCRIPTCOMMENTDATA", "SCRIPTID=1217363", new XMLAPIResults(MAINUSER_TOKEN, "COMMENT", "Changed"));

		doTests("SCRIPTCOMMENTDATA", "SCRIPTID=1217363", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1, "SCRIPT")));
		doTests("REMOVESCRIPTCOMMENT", "XID=" + id, new XMLAPIResults(MAINUSER_TOKEN));
		doTests("SCRIPTCOMMENTDATA", "SCRIPTID=1217363", new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0, "SCRIPT")));
	}
}
