package com.chilicoders.bl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.json.JSONException;
import org.junit.Assert;
import org.junit.Test;
import org.xml.sax.SAXException;

import com.chilicoders.app.RequestFile;
import com.chilicoders.db.DbObject;
import com.chilicoders.model.Protocol;
import com.chilicoders.ruleengine.ProbeFact;
import com.chilicoders.ruleengine.RuleException;
import com.chilicoders.xmlapi.TestResponse;
import com.chilicoders.xmlapi.XMLAPIResults;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class DisputeBusinessTest extends XMLAPITestBase {
	@Test
	public void basicOperations() throws <PERSON><PERSON><PERSON>x<PERSON>, IOEx<PERSON>, J<PERSON>NException, SAXException, RuleException {
		setupDatabaseForTest(false);
		final long targetId = createTarget(MAINUSER_TOKEN, null, false);

		final long scheduleId = createSchedule(MAINUSER_TOKEN, null, -1);

		final List<ProbeFact> facts = new ArrayList<>();
		facts.add(new ProbeFact("fact/product/version", "apache/tomcat", "1.2.1", 80, Protocol.TCP));
		final long reportId = saveReport(targetId, scheduleId, facts, TEST_USER_ID, null, null);
		assertTrue(reportId > 0);

		final long findingId = DbObject.executeCountQuery(getConnection(), "SELECT MAX(xid) FROM treport_vulns WHERE itype IN (2,3)");
		assertTrue(findingId > 0);

		final ArrayList<RequestFile> files = new ArrayList<>();
		files.add(new RequestFile("CONTENT", "file.file", "testdata".getBytes(UTF_8)));

		doTests("UPDATEDISPUTEFILEDATA", createMap(createParameterMap("FINDINGID=" + findingId)), files, new XMLAPIResults(MAINUSER_TOKEN));
		final long id = DbObject.executeCountQuery(getConnection(), "SELECT xid FROM treport_disputes");
		doTests("DISPUTEFILEDATA", "FINDINGID=" + findingId, new XMLAPIResults(MAINUSER_TOKEN, "NAME", "file.file"));
		doTests("GETDISPUTEFILE", "FINDINGID=" + findingId + "&XID=" + id, new XMLAPIResults(MAINUSER_TOKEN, new XMLAPIResults.ResponseListener() {
			@Override
			public void validate(final TestResponse response, final Map<String, String> params) throws IOException, SAXException, JSONException {
				Assert.assertEquals("testdata", response.getResult());
			}
		}));
		doTests("DISPUTEFILEDATA", "FINDINGID=" + findingId, new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(1, DisputeFileBusiness.ROW_TAG)));
		doTests("REMOVEDISPUTEFILEDATA", "XID=" + id, new XMLAPIResults(MAINUSER_TOKEN));
		doTests("DISPUTEFILEDATA", "FINDINGID=" + findingId, new XMLAPIResults(MAINUSER_TOKEN, XMLAPIResults.getCountListener(0, DisputeFileBusiness.ROW_TAG)));
	}
}
