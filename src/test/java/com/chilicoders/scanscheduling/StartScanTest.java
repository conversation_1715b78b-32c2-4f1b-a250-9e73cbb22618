package com.chilicoders.scanscheduling;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;

import com.chilicoders.core.scheduling.api.ScanSchedulingService;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.test.TestUtil;
import com.chilicoders.test.model.TestReportEntry;
import com.chilicoders.test.model.TestScanlog;
import com.chilicoders.test.model.TestSchedule;
import com.chilicoders.test.model.TestTarget;

@ActiveProfiles("schedulingtest")
@ContextConfiguration(classes = {TestConfig.class})
public class StartScanTest extends BaseSpringTestWithRollback {
	@Autowired
	private ScanSchedulingService scanSchedulingService;

	@Autowired
	private NativeStatementExecutor statementExecutor;

	@Autowired
	private TestUtil testUtil;

	@Test
	public void testSLSFailedNotifications() throws SQLException {
		testUtil.insertUser(12000, "test");
		final TestTarget target = TestTarget.builder().userId(12000).ipAddress("*******").build();
		final long targetId = testUtil.insertTarget(target);
		final TestSchedule schedule = TestSchedule.builder().userId(12000).name("Test schedule").scanless(true).build();
		testUtil.insertSchedule(schedule);
		final TestScanlog scanjob = TestScanlog.builder().userId(12000).type(20).scheduleId(schedule.getId()).build();
		testUtil.insertScanlog(scanjob);
		final TestScanlog scanlog = TestScanlog.builder().userId(12000).scheduleId(schedule.getId()).scanjobId(scanjob.getId()).targetId(targetId).build();
		testUtil.insertScanlog(scanlog);
		final TestReportEntry reportEntry = TestReportEntry.builder().userId(12000).scanlogId(scanlog.getId()).targetId(targetId).build();
		testUtil.insertReportEntry(reportEntry);
		statementExecutor.execute(new NativeSqlStatement("INSERT INTO trules(ruleid, name, family, description, cvssvector, created, updated) VALUES"
				+ "(12000, 'Name', 'fam', 'Description', '', NOW(), NOW() + '1 day'::interval)"));
		assertThat(statementExecutor.getDate(new NativeSqlStatement("SELECT slsnotified FROM schedules WHERE id = ?", schedule.getId()))).isNull();
		scanSchedulingService.handleScanlessScan();
		assertThat(statementExecutor.getDate(new NativeSqlStatement("SELECT slsnotified FROM schedules WHERE id = ?", schedule.getId()))).isNotNull();
	}
}
