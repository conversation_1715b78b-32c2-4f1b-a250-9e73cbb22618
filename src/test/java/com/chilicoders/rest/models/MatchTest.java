package com.chilicoders.rest.models;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.json.JSONObject;
import org.junit.Test;

import com.chilicoders.model.MatchType;

public class MatchTest {

	private static final int ANY_ID = 123;

	@Test
	public void testNestsServiceInMatch() {
		final Service service = new Service("http", 80, Service.Protocol.TCP, "example.com", "http");
		final Match match = new Match(ANY_ID, ANY_ID, ANY_ID, service, MatchType.GATHEREDINFORMATION, new JSONObject());

		// Match JSON now contains Service under the "service" key.
		assertTrue(match.getMatchJson().has("service"));

		final JSONObject expected = new JSONObject();
		expected.put("service", service.toJson());
		assertEquals(expected.toString(), match.getMatchJson().toString());

		// Match exposes Service via a getter.
		assertEquals(service, match.getService());
	}
}
