package com.chilicoders.rest.models;

import java.sql.SQLException;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.chilicoders.core.subscriptions.api.model.Subscription;
import com.chilicoders.model.SubscriptionType;
import com.chilicoders.service.ServiceProvider;
import org.assertj.core.api.Assertions;
import org.joda.time.Duration;
import org.joda.time.Instant;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.Source;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.resources.BaseResourceTest;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.xmlapi.XMLAPITestBase;

import static com.chilicoders.xmlapi.XMLAPITestBase.startSubscriptionServiceMockServer;
import static com.chilicoders.xmlapi.XMLAPITestBase.stopLocalServer;

public class ScopingInformationTest extends BaseResourceTest {

	private static final String ANY_HOST = "anyhost.com";
	private static final String ANY_CLOUD_INSTANCE = "a-clound-instance";
	private static final String TARGET_SOLUTION = "targetSolution";
	private static final String TARGET_START_DATE = "targetStartDate";
	private static final String ASSETS_IDENTIFIER_IDS = "assetIdentifierIds";

	private Integer assetIdentifierId1;
	private Integer assetIdentifierId2;
	private Long customerId;
	private String customerUuid;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();
		customerId = DbObject.getLong(getConnection(), "SELECT id FROM customers WHERE userid = ?", XMLAPITestBase.ADMIN_USER_ID);
		customerUuid = DbObject.getString(getConnection(), "SELECT uuid::TEXT FROM customers WHERE userid = ?", XMLAPITestBase.ADMIN_USER_ID);
		DbObject.executeUpdate(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?)",
				ANY_HOST, AssetIdentifierType.HOSTNAME,
				new Source[] {Source.SWAT}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(), customerId);
		DbObject.executeUpdate(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?)",
				ANY_CLOUD_INSTANCE,
				AssetIdentifierType.AWS_INSTANCE_ID, new Source[] {Source.SCOUT}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(),
				customerId);
		getConnection().commit();
		assetIdentifierId1 = AssetIdentifier.get(AssetIdentifier.class, getConnection(), "SELECT id FROM assetidentifiers WHERE name = ? ", ANY_HOST).getId();
		assetIdentifierId2 = AssetIdentifier.get(AssetIdentifier.class, getConnection(), "SELECT id FROM assetidentifiers WHERE name = ? ", ANY_CLOUD_INSTANCE).getId();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM assetidentifiers");
		getConnection().commit();
		stopLocalServer();
	}

	/**
	 * In case of SNAPSHOT if assets are more than available subscriptions.
	 */
	@Test(expected = InputValidationException.class)
	public void testThrowsValidationExceptionForSnapshot() throws Exception {
		final Subscription subscription = new Subscription();
		subscription.setUuid("test");
		final Subscription.Associations associations = new Subscription.Associations();
		associations.setAssetGroups(new String[] {"1"});
		subscription.setAssociations(associations);
		subscription.setType(SubscriptionType.SWAT);
		subscription.setActiveUntil(new Date().toInstant().plus(365, ChronoUnit.DAYS));
		subscription.setActivatedAt(new Date().toInstant().minus(10, ChronoUnit.DAYS));
		final JSONArray subscriptions = new JSONArray(Arrays.asList(subscription));
		startSubscriptionServiceMockServer(subscriptions.toString(), null);

		final JSONObject scopingJson = createScopingJson();
		scopingJson.put(TARGET_SOLUTION, "Appsec Snapshot");
		final ScopingInformation scopingInformation = MarshallingUtils.unmarshal(ScopingInformation.class, scopingJson.toString());

		scopingInformation.validateSnapshotSubscriptionSize(ServiceProvider.getSubscriptionService(getConnection()), customerUuid);
	}

	@Test
	public void testCanGetAssetNames() throws Exception {
		final JSONObject scopingJson = createScopingJson();
		final ScopingInformation scopingInformation = MarshallingUtils.unmarshal(ScopingInformation.class, scopingJson.toString());
		final List<String> assetNames = Arrays.asList(scopingInformation.getAssetIdentifierNames(getConnection()));

		Assertions.assertThat(assetNames.size()).isEqualTo(2);
		Assertions.assertThat(assetNames).contains(ANY_HOST);
		Assertions.assertThat(assetNames).contains(ANY_CLOUD_INSTANCE);
	}

	/**
	 * Creates JSON object with scoping information.
	 *
	 * @return JSON object with scoping information.
	 */
	private JSONObject createScopingJson() {
		final JSONObject postJson = new JSONObject();
		postJson.put(TARGET_SOLUTION, "Appsec Assure");
		postJson.put(TARGET_START_DATE, Instant.now().plus(Duration.standardMinutes(1)));
		postJson.put(ASSETS_IDENTIFIER_IDS, new JSONArray("[ " + assetIdentifierId1 + "," + assetIdentifierId2 + "]"));

		return postJson;
	}
}
