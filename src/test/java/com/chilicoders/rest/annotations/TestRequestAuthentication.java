package com.chilicoders.rest.annotations;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.Test;

public class TestRequestAuthentication {
	@Test
	public void testThatServiceAccountsEnumWorkAsExpected() {
		for (final RequestAuthentication.ServiceAccounts sa : RequestAuthentication.ServiceAccounts.values()) {
			final String asString = sa.toString();
			assertThat(sa).isEqualTo(RequestAuthentication.ServiceAccounts.fromString(asString));
		}
	}

	@Test
	public void testThatServiceAccountsExistsWorkAsExpected() {
		for (final RequestAuthentication.ServiceAccounts sa : RequestAuthentication.ServiceAccounts.values()) {
			final String asString = sa.toString();
			assertThat(RequestAuthentication.ServiceAccounts.exists(asString)).isTrue();
		}
		assertThat(RequestAuthentication.ServiceAccounts.exists("really-not-a-service-account")).isFalse();
	}
}
