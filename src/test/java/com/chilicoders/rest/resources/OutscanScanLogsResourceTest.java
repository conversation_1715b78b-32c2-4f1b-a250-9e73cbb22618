package com.chilicoders.rest.resources;

import static org.junit.Assert.assertEquals;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.ScanLog;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.ScanTypes;
import com.chilicoders.model.Template;
import com.chilicoders.rest.models.OutscanScanLog;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class OutscanScanLogsResourceTest extends BaseResourceTest {

	private long targetId;
	private long scanJobId;
	private long scanLogId;
	private long reportId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();

		this.targetId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tuserdatas (xid, xuserxid, ipaddress) VALUES (nextval('tuserdatas_seq'), ?, ?::INET) RETURNING xid",
						XMLAPITestBase.TEST_USER_ID, "**********");
		this.scanJobId = ScanLog.getNewId(ScanLog.class, getConnection());
		DbObject.executeCountQuery(getConnection(), "INSERT INTO tscanlogs (xid, xuserxid, itype, xscanjobxid) VALUES (?, ?, ?, ?) RETURNING xid", this.scanJobId,
				XMLAPITestBase.TEST_USER_ID, ScanTypes.ScheduleJobStopped.getId(), this.scanJobId);
		this.scanLogId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tscanlogs (xid, xuserxid, itype, xscanjobxid, xipxid, xtemplate) VALUES (nextval('tscanlogs_seq'), ?, ?, ?, ?, ?) "
						+ "RETURNING xid", XMLAPITestBase.TEST_USER_ID, ScanTypes.Ok.getId(), this.scanJobId, this.targetId, Template.Normal.getId());
		this.reportId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO treportentrys (xid, xuserxid, xipxid, itype, xscanlogxid, xscanjobxid, xtemplate, dreportdate, vctarget) "
						+ "VALUES (nextval('treportentrys_seq'), ?, ?, ?, ?, ?, ?, ?, ?) RETURNING xid",
				XMLAPITestBase.TEST_USER_ID, this.targetId, 0, this.scanLogId, this.scanJobId, Template.Normal.getId(), new Date(), "**********");
		DbObject.executeCountQuery(getConnection(),
				"INSERT INTO treport_vulns (xid, fk_treportentrys_xid, xipxid, itype, iport, irisk, iprotocol, vcvulnid, cdata, iscvss, vcscvssvector, servicename, dfirstseen, dlastseen, vcvhost) "
						+ "VALUES (nextval('treport_vulns_seq'), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING xid",
				this.reportId, -1, 3, 80, 4, Protocol.TCP.value, 250210, null, 75, "(AV:N/AC:L/Au:N/C:P/I:P/A:P)", "http", new Date(), new Date(), "test.com");
		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM tuserdatas WHERE xid = ?", this.targetId);
		DbObject.executeUpdate(getConnection(), "DELETE FROM treportentrys WHERE xid = ?", this.reportId);
		DbObject.executeUpdate(getConnection(), "DELETE FROM tscanlogs WHERE xscanjobxid = ?", this.scanJobId);
		getConnection().commit();
	}

	@Test
	public void testGetScanLogHeader() {
		final String totalCount = head(OutscanScanLogsResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertEquals("1", totalCount);
	}

	@Test
	public void testGetScanLogList() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,target");

		final JSONArray jsonArray = getListFromResource(OutscanScanLogsResource.PATH, queryParams, MAINUSER_TOKEN);
		assertEquals(1, jsonArray.length());
	}

	@Test
	public void testGetScanLog() {
		final Map<String, String> queryParams = new HashMap<>();

		Response response = get(OutscanScanLogsResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		response = get(OutscanScanLogsResource.PATH + "/" + this.scanLogId, Status.OK, MAINUSER_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.scanLogId, jsonObject.getInt("id"));
		assertEquals(OutscanScanLog.Status.Ok.name(), jsonObject.getString("status"));
	}

	@Test
	public void testGetFindingListScanLog() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,name");

		final JSONArray jsonArray = getListFromResource(OutscanScanLogsResource.PATH + "/" + this.scanLogId + "/findings", queryParams, MAINUSER_TOKEN);
		assertEquals(1, jsonArray.length());
	}

}
