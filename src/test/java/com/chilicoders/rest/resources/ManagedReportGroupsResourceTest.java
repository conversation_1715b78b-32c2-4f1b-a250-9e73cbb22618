package com.chilicoders.rest.resources;

import static org.junit.Assert.assertEquals;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class ManagedReportGroupsResourceTest extends BaseResourceTest {

	private long groupId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();
		this.groupId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tmanagedreportgroups (xid, xuserxid, name) VALUES(nextval('tmanagedreportgroup_seq'), ?, ?) RETURNING xid", XMLAPITestBase.TEST_USER_ID, "Test");
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET managedserviceslimited = 1 WHERE xid = ?", XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM tmanagedreportgroups WHERE xid = ?", this.groupId);
		getConnection().commit();
	}

	@Test
	public void testGetManagedReportGroupHeader() {
		final String totalCount = head(ManagedReportGroupsResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertEquals("3", totalCount);
	}

	@Test
	public void testGetManagedReportGroupList() throws SQLException {
		JSONArray jsonArray = getListFromResource(ManagedReportGroupsResource.PATH, "id,name", MAINUSER_TOKEN);
		assertEquals(3, jsonArray.length());

		final long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUBUSER_NOACCESS");
		final String subUserToken = AuthResource.createToken(XMLAPITestBase.TEST_USER_ID, subUserId, null, false, null);
		jsonArray = getListFromResource(ManagedReportGroupsResource.PATH, "id,name", subUserToken);
		assertEquals(2, jsonArray.length());
	}

	@Test
	public void testGetManagedReportGroup() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,name");

		// Get group that does not exist
		Response response = get(ManagedReportGroupsResource.PATH + "/-2", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get group
		response = get(ManagedReportGroupsResource.PATH + "/" + this.groupId, Status.OK, MAINUSER_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.groupId, jsonObject.getInt("id"));
	}

}
