package com.chilicoders.rest.resources;

import static com.chilicoders.xmlapi.XMLAPITestBase.TEST_USER_ID;
import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;

import javax.ws.rs.core.Response;

import org.json.JSONArray;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.db.DbObject;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class AboutResourceTest extends BaseResourceTest {
	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Setup before each test.
	 */
	@Before
	public void init() {
		setupDatabase();
	}

	@Test
	public void testGetAgentVersionWithAgentNotEnabled() {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}

		Configuration.setProperty(ConfigKeys.ConfigurationKey.agent_api_url, "agent_api_url");
		final Response response = get(AboutResource.PATH, Response.Status.OK, MAINUSER_TOKEN);
		final JSONArray versionArray = new JSONArray(response.readEntity(String.class));
		assertThat(versionArray).isNotNull().hasSize(2);
		assertThat(versionArray.getJSONObject(0).get("version")).isEqualTo("");
	}

	@Test
	public void testGetAgentVersionWithAgentUriNotDefined() throws SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET agentsenabled = true WHERE xid = ?", TEST_USER_ID);
		getConnection().commit();

		final Response response = get(AboutResource.PATH, Response.Status.OK, MAINUSER_TOKEN);
		final JSONArray versionArray = new JSONArray(response.readEntity(String.class));
		assertThat(versionArray).isNotNull().hasSize(2);
		assertThat(versionArray.getJSONObject(0).get("version")).isEqualTo("");
	}

	@Test
	public void testGetCorrectAgentAndRulesVersions() throws SQLException {
		Configuration.setProperty(ConfigKeys.ConfigurationKey.agent_api_url, "agent_api_url");
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET agentsenabled = true WHERE xid = ?", TEST_USER_ID);
		ServiceProvider.getDataStoreService(getConnection()).setEntry(DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name(), "1.31.0");
		getConnection().commit();

		final Response response = get(AboutResource.PATH, Response.Status.OK, MAINUSER_TOKEN);
		final JSONArray versionArray = new JSONArray(response.readEntity(String.class));
		assertThat(versionArray).isNotNull().hasSize(2);
		assertThat(versionArray.getJSONObject(0).get("software")).isEqualTo("AGENT");
		assertThat(versionArray.getJSONObject(0).get("version")).isEqualTo("1.31.0");
		assertThat(versionArray.getJSONObject(1).get("software")).isEqualTo("RULES");
		assertThat(versionArray.getJSONObject(1).get("version")).isEqualTo("2017-05-16 09:11");
	}

	/**
	 * Teardown after each test.
	 */
	@After
	public void teardown() throws SQLException {
		Configuration.setProperty(ConfigKeys.ConfigurationKey.agent_api_url, "");

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET agentsenabled = false WHERE xid = ?", TEST_USER_ID);
		ServiceProvider.getDataStoreService(getConnection()).removeEntry(DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name());
		getConnection().commit();
	}
}
