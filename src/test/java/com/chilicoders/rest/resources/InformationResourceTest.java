package com.chilicoders.rest.resources;

import static com.chilicoders.xmlapi.XMLAPITestBase.TEST_USER_ID;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.IOException;
import java.sql.SQLException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBException;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.model.InformationPortEncryptionType;
import com.chilicoders.model.InformationServiceType;
import com.chilicoders.model.InformationStatus;
import com.chilicoders.model.InformationType;
import com.chilicoders.model.MatchType;
import com.chilicoders.model.Source;
import com.chilicoders.model.SubscriptionType;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.Service.Protocol;
import com.chilicoders.rest.models.SupportStatus;
import com.chilicoders.rest.models.Match.PortMatch;
import com.chilicoders.rest.models.Match.ProductMatch;
import com.chilicoders.rest.models.Match.ServiceMatch;
import com.chilicoders.util.MarshallingUtils;

public class InformationResourceTest extends BaseResourceTest {
	private long assetId1;

	private long assetId2;

	private long informationId1;

	private long informationId2;

	private long informationId3;

	private long informationId4;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException, IOException, JAXBException {
		setupDatabase();
		setSubUserTokens();

		this.assetId1 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, customerid) VALUES (?, ?::source[], ?, ?) RETURNING id",
				"Asset 1", new Source[] {Source.NETSEC}, TEST_USER_ID, getTestUserCustomerId());

		this.assetId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, customerid) VALUES (?, ?::source[], ?, ?) RETURNING id",
				"Asset 2", new Source[] {Source.NETSEC}, TEST_USER_ID, getTestUserCustomerId());

		final ProductMatch productMatch1 = new ProductMatch();
		productMatch1.setType(MatchType.PRODUCT);
		productMatch1.setProduct("nginx/nginx");
		productMatch1.setVersion("1.12.2");
		productMatch1.setEol(Instant.parse("2017-01-31T00:00:00Z"));

		final long matchId1 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO matches (assetid, type, firstseen, lastseen, source, subscriptiontype, match, createdbyid, customerid) "
						+ "VALUES(?, ?, now(), now(), ?::source[], ?::subscriptiontype, ?::JSONB, ?, ?) RETURNING id",
				this.assetId1, MatchType.PRODUCT, new Source[] {Source.NETSEC}, SubscriptionType.NETSEC, MarshallingUtils.marshal(productMatch1),
				TEST_USER_ID, getTestUserCustomerId());

		this.informationId1 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO information (assetid, type, createdbyid, customerid) VALUES(?, ?, ?, ?) RETURNING id",
				this.assetId1, InformationType.PRODUCT, TEST_USER_ID, getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO match_information (matchid, informationid) VALUES (?, ?)", matchId1, this.informationId1);

		final ProductMatch productMatch2 = new ProductMatch();
		productMatch2.setType(MatchType.PRODUCT);
		productMatch2.setProduct("openssh/openssh");
		productMatch2.setVersion("7.4");

		final long matchId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO matches (assetid, type, firstseen, lastseen, source, subscriptiontype, match, createdbyid, customerid) "
						+ "VALUES(?, ?, now(), now(), ?::source[], ?::subscriptiontype, ?::JSONB, ?, ?) RETURNING id",
				this.assetId2, MatchType.PRODUCT, new Source[] {Source.NETSEC}, SubscriptionType.NETSEC, MarshallingUtils.marshal(productMatch2),
				TEST_USER_ID, getTestUserCustomerId());

		this.informationId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO information (assetid, type, createdbyid, customerid) VALUES(?, ?, ?, ?) RETURNING id",
				this.assetId2, InformationType.PRODUCT, TEST_USER_ID, getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO match_information (matchid, informationid) VALUES (?, ?)", matchId2, this.informationId2);

		final PortMatch portMatch1 = new PortMatch();
		portMatch1.setType(MatchType.PORT);
		portMatch1.setPort(22);
		portMatch1.setProtocol(Protocol.TCP);
		portMatch1.setServiceName("ssh");
		portMatch1.setBanner("ssh");
		portMatch1.setEncaps(false);
		portMatch1.setLayer("layer");

		final PortMatch.Encryption encryption1 = new PortMatch.Encryption();
		encryption1.setType(InformationPortEncryptionType.SSH_CIPHER);
		encryption1.setValue("cipher1");

		final PortMatch.Encryption encryption2 = new PortMatch.Encryption();
		encryption2.setType(InformationPortEncryptionType.SSH_VERSION);
		encryption2.setValue("2.0");

		final PortMatch.Encryption encryption3 = new PortMatch.Encryption();
		encryption3.setType(InformationPortEncryptionType.SSH_KEX);
		encryption3.setValue("kex");

		portMatch1.setEncryption(new PortMatch.Encryption[] {encryption1, encryption2, encryption3});

		final long matchId3 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO matches (assetid, type, firstseen, lastseen, source, subscriptiontype, match, createdbyid, customerid) "
						+ "VALUES(?, ?, now(), now(), ?::source[], ?::subscriptiontype, ?::JSONB, ?, ?) RETURNING id",
				this.assetId2, MatchType.PORT, new Source[] {Source.NETSEC}, SubscriptionType.NETSEC, MarshallingUtils.marshal(portMatch1),
				TEST_USER_ID, getTestUserCustomerId());

		this.informationId3 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO information (assetid, type, createdbyid, customerid) VALUES(?, ?, ?, ?) RETURNING id",
				this.assetId2, InformationType.PORT, TEST_USER_ID, getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO match_information (matchid, informationid) VALUES (?, ?)", matchId3, this.informationId3);

		final ServiceMatch serviceMatch1 = new ServiceMatch();
		serviceMatch1.setType(MatchType.SERVICE);
		serviceMatch1.setName("TestService");
		serviceMatch1.setServiceType(InformationServiceType.WINDOWS);
		serviceMatch1.setStatus("RUNNING");
		serviceMatch1.setConfiguration("MANUAL");

		final long matchId4 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO matches (assetid, type, firstseen, lastseen, source, subscriptiontype, match, createdbyid, customerid) "
						+ "VALUES(?, ?, now(), now(), ?::source[], ?::subscriptiontype, ?::JSONB, ?, ?) RETURNING id",
				this.assetId2, MatchType.SERVICE, new Source[] {Source.NETSEC}, SubscriptionType.NETSEC, MarshallingUtils.marshal(serviceMatch1),
				TEST_USER_ID, getTestUserCustomerId());

		this.informationId4 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO information (assetid, type, createdbyid, customerid) VALUES(?, ?, ?, ?) RETURNING id",
				this.assetId2, InformationType.SERVICE, TEST_USER_ID, getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO match_information (matchid, informationid) VALUES (?, ?)", matchId4, this.informationId4);

		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM assetidentifiers");
		DbObject.executeUpdate(getConnection(), "DELETE FROM assets");
		DbObject.executeUpdate(getConnection(), "DELETE FROM matches");
		DbObject.executeUpdate(getConnection(), "DELETE FROM information");
		DbObject.executeUpdate(getConnection(), "DELETE FROM tags");
		DbObject.executeUpdate(getConnection(), "DELETE FROM comments");
		getConnection().commit();
	}

	@Test
	public void testGetInformationHeaderForProducts() {
		final String totalCount = head(InformationResource.PATH + "/" + InformationResource.PATH_PRODUCTS, new HashMap<>(), MAINUSER_TOKEN);
		assertThat(totalCount).isEqualTo("2");
	}

	@Test
	public void testGetInformationListForProducts() {
		final JSONArray jsonArray = getListFromResource(InformationResource.PATH + "/" + InformationResource.PATH_PRODUCTS, "id,matches", MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(2);
	}

	@Test
	public void testGetInformationForProducts() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,type,matches");

		// Get information that does not exist
		Response response = get(InformationResource.PATH + "/" + InformationResource.PATH_PRODUCTS + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Get information
		response = get(InformationResource.PATH + "/" + InformationResource.PATH_PRODUCTS + "/" + this.informationId1, Status.OK, MAINUSER_TOKEN, queryParams);

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getInt("id")).isEqualTo(this.informationId1);
	}

	@Test
	public void testGetInformationHeaderForPorts() {
		final String totalCount = head(InformationResource.PATH + "/" + InformationResource.PATH_PORTS, new HashMap<>(), MAINUSER_TOKEN);
		assertThat(totalCount).isEqualTo("1");
	}

	@Test
	public void testGetInformationListForPorts() {
		final JSONArray jsonArray = getListFromResource(InformationResource.PATH + "/" + InformationResource.PATH_PORTS, "id,matches", MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(1);
	}

	@Test
	public void testGetInformationForPorts() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,type,matches");

		// Get information that does not exist
		Response response = get(InformationResource.PATH + "/" + InformationResource.PATH_PORTS + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Get information
		response = get(InformationResource.PATH + "/" + InformationResource.PATH_PORTS + "/" + this.informationId3, Status.OK, MAINUSER_TOKEN, queryParams);

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getInt("id")).isEqualTo(this.informationId3);
	}

	@Test
	public void testGetInformationHeaderForServices() {
		final String totalCount = head(InformationResource.PATH + "/" + InformationResource.PATH_SERVICES, new HashMap<>(), MAINUSER_TOKEN);
		assertThat(totalCount).isEqualTo("1");
	}

	@Test
	public void testGetInformationListForServices() {
		final JSONArray jsonArray = getListFromResource(InformationResource.PATH + "/" + InformationResource.PATH_SERVICES, "id,matches", MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(1);
	}

	@Test
	public void testGetInformationForServices() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,type,matches");

		// Get information that does not exist
		Response response = get(InformationResource.PATH + "/" + InformationResource.PATH_SERVICES + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Get information
		response = get(InformationResource.PATH + "/" + InformationResource.PATH_SERVICES + "/" + this.informationId4, Status.OK, MAINUSER_TOKEN, queryParams);

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getInt("id")).isEqualTo(this.informationId4);
	}

	@Test
	public void testGetInformationHeader() {
		final String totalCount = head(InformationResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertThat(totalCount).isEqualTo("4");
	}

	@Test
	public void testGetInformationList() {
		JSONArray jsonArray = getListFromResource(InformationResource.PATH, "id,matches", MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(4);

		final Map<String, String> queryParams = new HashMap<>();

		// Test product filter for product information
		queryParams.put("fields", "id");
		queryParams.put("filter", createFilter("matches.product", "nginx"));

		jsonArray = getListFromResource(InformationResource.PATH, queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray.length()).isEqualTo(1);

		// Test encryption filter for ports information
		queryParams.put("fields", "id");
		queryParams.put("filter", createFilter("matches.encryption", InformationPortEncryptionType.SSH_KEX + ":kex"));

		jsonArray = getListFromResource(InformationResource.PATH, queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray.length()).isEqualTo(1);

		// Test name filter for service information
		queryParams.put("fields", "id");
		queryParams.put("filter", createFilter("matches.name", "TestService"));

		jsonArray = getListFromResource(InformationResource.PATH, queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray.length()).isEqualTo(1);
	}

	@Test
	public void testGetInformation() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,type,matches");

		// Get information that does not exist
		Response response = get(InformationResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Get information
		response = get(InformationResource.PATH + "/" + this.informationId1, Status.OK, MAINUSER_TOKEN, queryParams);

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getInt("id")).isEqualTo(this.informationId1);
	}

	@Test
	public void testGetInformationLimitedResources() throws SQLException {
		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id",
				"Test", "Test", getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, this.assetId1);

		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.FINDINGS_VIEW.getId()});
		setResourceGroup(getAllAccessSubUserId(), ResourceType.ASSET, new Integer[] {(int) tagId});
		getConnection().commit();

		// List information as subuser with limited access
		final Response response = get(InformationResource.PATH, Status.OK, getAllAccessSubUserToken());
		final JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		assertThat(jsonArray).hasSize(1);
	}

	@Test
	public void testMarkAndUnmarkFalsePositive() throws IOException {
		final JSONObject falsePositiveData = new JSONObject();
		falsePositiveData.put("falsePositiveComment", "Comment");

		// Mark false positive not found
		Response response = post(InformationResource.PATH + "/0/mark-false-positive", Status.NOT_FOUND, MAINUSER_TOKEN, falsePositiveData.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		response = get(InformationResource.PATH + "/" + this.informationId1, Status.OK, MAINUSER_TOKEN);
		JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonObject).node("status").isEqualTo(InformationStatus.PRESENT.name());
		assertThatJson(jsonObject).node("falsePositive").isAbsent();
		assertThatJson(jsonObject).node("falsePositiveComment").isAbsent();

		// Mark false positive
		post(InformationResource.PATH + "/" + this.informationId1 + "/mark-false-positive", Status.NO_CONTENT, MAINUSER_TOKEN, falsePositiveData.toString());

		response = get(InformationResource.PATH + "/" + this.informationId1, Status.OK, MAINUSER_TOKEN);
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonObject).node("status").isEqualTo(InformationStatus.FALSE_POSITIVE.name());
		assertThatJson(jsonObject).node("falsePositive").isNotNull();
		assertThatJson(jsonObject).node("falsePositiveComment").isNotNull();
		assertThatJson(jsonObject).node("commentsCount").isEqualTo(1);

		// Unmark false positive not found
		response = post(InformationResource.PATH + "/0/reset-status", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Unmark false positive
		post(InformationResource.PATH + "/" + this.informationId1 + "/reset-status", Status.NO_CONTENT, MAINUSER_TOKEN, "");

		response = get(InformationResource.PATH + "/" + this.informationId1, Status.OK, MAINUSER_TOKEN);
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonObject).node("status").isEqualTo(InformationStatus.PRESENT.name());
		assertThatJson(jsonObject).node("falsePositive").isAbsent();
		assertThatJson(jsonObject).node("falsePositiveComment").isAbsent();
	}

	@Test
	public void testMarkAndUnmarkFalsePositiveBulk() throws IOException, SQLException {
		final JSONObject falsePositiveData = new JSONObject();
		falsePositiveData.put("falsePositiveComment", "Comment");

		final Map<String, String> queryParams = new HashMap<>();

		// Mark false positive not found
		queryParams.put("filter", createFilter("id", "0"));
		Response response = post(InformationResource.PATH + "/mark-false-positive", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, falsePositiveData.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Mark false positive
		queryParams.put("filter", createFilter("type", InformationType.PRODUCT.name()));
		post(InformationResource.PATH + "/mark-false-positive", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, falsePositiveData.toString());

		long informationCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM information WHERE type = ? AND status = ?",
				InformationType.PRODUCT, InformationStatus.FALSE_POSITIVE);
		assertThat(informationCount).isEqualTo(2);

		// Unmark false positive not found
		queryParams.put("filter", createFilter("id", "0"));
		response = post(InformationResource.PATH + "/reset-status", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Unmark false positive
		queryParams.put("filter", createFilter("type", InformationType.PRODUCT.name()));
		post(InformationResource.PATH + "/reset-status", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, "");

		informationCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM information WHERE type = ? AND status = ?",
				InformationType.PRODUCT, InformationStatus.PRESENT);
		assertThat(informationCount).isEqualTo(2);
	}

	@Test
	public void testTags() throws SQLException {
		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id",
				"Test", "Test", getTestUserCustomerId());
		getConnection().commit();

		// Add tags with missing information
		Response response = put(InformationResource.PATH + "/0/tags", Status.NOT_FOUND, MAINUSER_TOKEN, new JSONArray(new Integer[] {0}).toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Add tags with missing tag
		response = put(InformationResource.PATH + "/" + this.informationId1 + "/tags", Status.NOT_FOUND, MAINUSER_TOKEN, new JSONArray(new Integer[] {0}).toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_TAG_ID_NOT_FOUND"));

		// Add tags
		put(InformationResource.PATH + "/" + this.informationId1 + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, new JSONArray(new Integer[] {(int) tagId}).toString());
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_information WHERE tagid = ? AND informationid = ?",
				tagId, this.informationId1)).isEqualTo(1);

		// Remove tags
		put(InformationResource.PATH + "/" + this.informationId1 + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, new JSONArray(new Integer[0]).toString());
		assertThat(DbObject.executeCountQuery(getConnection(),
				"SELECT COUNT(*) FROM tag_information WHERE tagid = ? AND informationid = ?", tagId, this.informationId1)).isEqualTo(0);

		// Add link
		put(InformationResource.PATH + "/" + this.informationId2 + "/tags/" + tagId, Status.CREATED, MAINUSER_TOKEN, "");
		assertThat(DbObject.executeCountQuery(getConnection(),
				"SELECT COUNT(*) FROM tag_information WHERE tagid = ? AND informationid = ?", tagId, this.informationId2)).isEqualTo(1);

		// Remove link
		delete(InformationResource.PATH + "/" + this.informationId2 + "/tags/" + tagId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertThat(DbObject.executeCountQuery(getConnection(),
				"SELECT COUNT(*) FROM tag_information WHERE tagid = ? AND informationid = ?", tagId, this.informationId2)).isEqualTo(0);
	}

	@Test
	public void testTagsBulk() throws SQLException {
		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id",
				"Test", "Test", getTestUserCustomerId());
		getConnection().commit();

		final Map<String, String> queryParams = new HashMap<>();

		// Add tags with missing information
		queryParams.put("filter", createFilter("id", "0"));
		final Response response = put(InformationResource.PATH + "/tags", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, new JSONArray(new Integer[] {(int) tagId}).toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Add tags
		queryParams.put("filter", createFilter("type", InformationType.PRODUCT.name()));
		put(InformationResource.PATH + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, new JSONArray(new Integer[] {(int) tagId}).toString());

		long tagCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_information WHERE tagid = ?", tagId);
		assertThat(tagCount).isEqualTo(2);

		// Remove tags
		put(InformationResource.PATH + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, new JSONArray(new Integer[0]).toString());
		tagCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_information WHERE tagid = ?", tagId);
		assertThat(tagCount).isEqualTo(0);

		// Add link
		put(InformationResource.PATH + "/tags/" + tagId, Status.CREATED, MAINUSER_TOKEN, queryParams, "");
		tagCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_information WHERE tagid = ?", tagId);
		assertThat(tagCount).isEqualTo(2);

		// Remove link
		delete(InformationResource.PATH + "/tags/" + tagId, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams);
		tagCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_information WHERE tagid = ?", tagId);
		assertThat(tagCount).isEqualTo(0);
	}

	@Test
	public void testComments() throws SQLException {
		final JSONObject customerComment = new JSONObject();
		customerComment.put("comment", "Test");
		customerComment.put("supportStatus", SupportStatus.NOT_APPLICABLE);

		// Create comment
		Response response = postWithResult(InformationResource.PATH + "/" + this.informationId1 + "/comments", Status.CREATED, MAINUSER_TOKEN, customerComment.toString());
		final long customerCommentId = getCreatedId(response);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,comment");

		// Get comments
		JSONArray jsonArray = getListFromResource(InformationResource.PATH + "/" + this.informationId1 + "/comments", queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(1);

		// Get information with comment that does not exist
		response = get(InformationResource.PATH + "/" + this.informationId1 + "/comments/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Get comment
		response = get(InformationResource.PATH + "/" + this.informationId1 + "/comments/" + customerCommentId, Status.OK, MAINUSER_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getInt("id")).isEqualTo(customerCommentId);

		// Delete comment
		delete(InformationResource.PATH + "/" + this.informationId1 + "/comments/" + customerCommentId, Status.NO_CONTENT, MAINUSER_TOKEN);

		queryParams.put("filter", createFilter("deleted", JSONObject.NULL));
		jsonArray = getListFromResource(InformationResource.PATH + "/" + this.informationId1 + "/comments", queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray).isEmpty();

		// Create comments in bulk
		queryParams.put("filter", createFilter("id", this.informationId1));
		response = postWithResult(InformationResource.PATH + "/comments", Status.CREATED, MAINUSER_TOKEN, customerComment.toString());

		queryParams.put("filter", createFilter("deleted", JSONObject.NULL));
		jsonArray = getListFromResource(InformationResource.PATH + "/" + this.informationId1 + "/comments", queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray).hasSize(1);

		// Delete comment in bulk
		delete(InformationResource.PATH + "/comments", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams);

		queryParams.put("filter", createFilter("deleted", JSONObject.NULL));
		jsonArray = getListFromResource(InformationResource.PATH + "/" + this.informationId1 + "/comments", queryParams, MAINUSER_TOKEN);
		assertThat(jsonArray).isEmpty();
	}
}
