package com.chilicoders.rest.resources;

import static org.junit.Assert.assertEquals;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.core.user.api.model.FarsightProduct;
import com.chilicoders.db.DbObject;
import com.chilicoders.model.ExploitType;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class ExploitsResourceTest extends BaseResourceTest {

	private long exploitId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();

		this.exploitId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO texploits (id, name, type, cve, created, identifier, pack, approved) VALUES (?, ?, ?, ?, ?, ?, ?, true) RETURNING id",
				1, "TestExploit1", ExploitType.CORE.ordinal(), "TestCVE-2017-14079", new Date(), "TestExploit1Identifier", "TestPack");
		DbObject.executeUpdate(getConnection(), "INSERT INTO texploits (id, name, type, cve, created, identifier, pack, approved) VALUES (?, ?, ?, ?, ?, ?, ?, true)",
				2, "TestExploit2", ExploitType.D_SQUARE.ordinal(), "TestCVE-2018-14089", new Date(), "TestExploit1Identifier", "TestPack");
		DbObject.executeUpdate(getConnection(), "INSERT INTO texploits (id, name, type, cve, created, identifier, pack, approved) VALUES (?, ?, ?, ?, ?, ?, ?, true)",
				3, "TestExploit3", ExploitType.SNORT.ordinal(), "TestCVE-2019-14099", new Date(), "TestExploit1Identifier", "TestPack");
		DbObject.executeUpdate(getConnection(), "INSERT INTO texploits (id, name, type, cve, created, identifier, pack, approved) VALUES (?, ?, ?, ?, ?, ?, ?, true)",
				4, "TestExploit4", ExploitType.FARSIGHT.ordinal(), "TestCVE-2019-14090", new Date(), "TestExploit1Identifier", "TestPack");
		DbObject.executeUpdate(getConnection(), "INSERT INTO texploits (id, name, type, cve, created, identifier, pack, approved) VALUES (?, ?, ?, ?, ?, ?, ?, true)",
				5, "TestExploit5", ExploitType.UNKNOWN.ordinal(), "TestCVE-2019-14075", new Date(), "TestExploit1Identifier", "TestPack");
		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM texploits");
		getConnection().commit();
	}

	@Test
	public void testGetExploitHeader() {
		final String totalCount = head(ExploitsResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertEquals("4", totalCount);
	}

	@Test
	public void testGetExploitList() throws SQLException {
		JSONArray jsonArray = getListFromResource(ExploitsResource.PATH, "id,source,cve,identifier,scriptid", MAINUSER_TOKEN);
		assertEquals(4, jsonArray.length());

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET farsightproducts = ? WHERE xid = ?", new String[] {FarsightProduct.NETSEC.name().toLowerCase()},
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		jsonArray = getListFromResource(ExploitsResource.PATH, "id,source,cve,identifier,scriptid", MAINUSER_TOKEN);
		assertEquals(4, jsonArray.length());
	}

	@Test
	public void testGetExploit() {
		// Get exploit that does not exist
		Response response = get(ExploitsResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get exploit
		response = get(ExploitsResource.PATH + "/" + this.exploitId, Status.OK, MAINUSER_TOKEN);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.exploitId, jsonObject.getInt("id"));
		assertEquals("Core Security", jsonObject.getString("source"));
	}
}
