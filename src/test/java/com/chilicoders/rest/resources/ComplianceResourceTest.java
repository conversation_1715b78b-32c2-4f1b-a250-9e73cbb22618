package com.chilicoders.rest.resources;

import static com.chilicoders.xmlapi.XMLAPITestBase.ADMIN_USER_ID;
import static java.nio.charset.StandardCharsets.UTF_8;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.json;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.time.Instant;
import java.time.Period;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.DatatypeConverter;
import javax.xml.bind.JAXBException;

import org.apache.commons.io.FileUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.MultiPart;
import org.glassfish.jersey.media.multipart.MultiPartFeature;
import org.glassfish.jersey.media.multipart.file.FileDataBodyPart;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.api.reportexport.ReportExportResponse;
import com.chilicoders.api.reportexport.ReportExportResponse.ExportResult;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.reporting.api.model.ReportFormat;
import com.chilicoders.core.reporting.api.model.ReportLevel;
import com.chilicoders.core.reporting.impl.ReportingServiceImpl;
import com.chilicoders.core.reporting.impl.jpa.entity.ReportDownloadEntry;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.CompliancePolicyType;
import com.chilicoders.model.ComplianceRequirementCategory;
import com.chilicoders.model.MatchType;
import com.chilicoders.model.Source;
import com.chilicoders.model.SubscriptionType;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.model.ViewType;
import com.chilicoders.report.ReportExportServiceImpl;
import com.chilicoders.rest.models.CompliancePolicy;
import com.chilicoders.rest.models.ComplianceReport;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.StringUtils;

public class ComplianceResourceTest extends BaseResourceTest {

	private long assetId;

	private long assetIdentifierId;

	private long policyId;

	private long requirementId;

	private long findingId;

	private long viewTemplateId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();
		setSubUserTokens();

		this.assetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"ap-northeast-1", AssetIdentifierType.AWS_REGION, new Source[] {Source.CLOUDSEC}, ADMIN_USER_ID, ADMIN_USER_ID, new Date(),
				new Date(), getAdminUserCustomerId());
		this.assetId = DbObject.executeCountQuery(getConnection(), "INSERT INTO assets (name, customerid, source) VALUES (?, ?, ?::source[]) RETURNING id",
				"Asset 1", getAdminUserCustomerId(), new Source[] {Source.CLOUDSEC});
		this.policyId = DbObject.executeCountQuery(getConnection(), "INSERT INTO compliancepolicies (name, key, type) VALUES(?, ?, ?) RETURNING id", "Test", "test",
				CompliancePolicyType.AWS);
		this.requirementId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO compliancerequirements (policyid, nameshort, description, key, requirementid, category) VALUES(?, ?, ?, ?, ?, ?) RETURNING id",
				this.policyId, "Test", "Test", "test1.1", "1.1", ComplianceRequirementCategory.MONITORING_AND_LOGGING);
		final long matchId = DbObject.executeCountQuery(getConnection(), "INSERT INTO matches (assetid, type, " +
						"firstseen, lastseen, source, subscriptiontype, createdbyid, updatedbyid, customerid) VALUES(?, ?, now(), now()," +
						" ?::source[], ?::subscriptiontype, ?, ?, ?) RETURNING id",
				this.assetId, MatchType.COMPLIANCE, new Source[] {Source.CLOUDSEC}, SubscriptionType.CLOUDSEC,
				ADMIN_USER_ID, ADMIN_USER_ID, getAdminUserCustomerId());
		this.findingId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO compliancefindings (assetid, requirementid, matchid, customerid) VALUES(?, ?, ?, ?) RETURNING id",
				this.assetId, this.requirementId, matchId, getAdminUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES (?, ?, now(), now())",
				this.assetId, this.assetIdentifierId);

		this.viewTemplateId = DbObject.executeCountQuery(getConnection(), "SELECT id FROM viewtemplates WHERE system AND type = ? LIMIT 1", ViewType.COMPLIANCE);

		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM assetidentifiers");
		DbObject.executeUpdate(getConnection(), "DELETE FROM assets");
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancepolicies");
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancerequirements");
		DbObject.executeUpdate(getConnection(), "DELETE FROM matches");
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancefindings");
		DbObject.executeUpdate(getConnection(), "DELETE FROM tags");
		DbObject.executeUpdate(getConnection(), "DELETE FROM downloadentries");
		final File[] files = new File(Configuration.getProperty(ConfigurationKey.report_tmp_folder)).listFiles();
		if (files != null) {
			for (final File f : files) {
				FileUtils.deleteQuietly(f);
			}
		}
		getConnection().commit();
	}

	@Test
	public void testGetCompliancePolicy() {
		// Get policy head
		final String totalCount = head(ComplianceResource.PATH + "/policies", new HashMap<>(), ADMIN_TOKEN);
		assertEquals("1", totalCount);

		// Get policy list
		final JSONArray jsonArray = getListFromResource(ComplianceResource.PATH + "/policies", "id,name", ADMIN_TOKEN);
		assertEquals(1, jsonArray.length());

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,name");

		// Get policy that does not exist
		Response response = get(ComplianceResource.PATH + "/policies/0", Status.NOT_FOUND, ADMIN_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get policy
		response = get(ComplianceResource.PATH + "/policies/" + this.policyId, Status.OK, ADMIN_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.policyId, jsonObject.getInt("id"));
	}

	@Test
	public void testUpdateCompliancePolicy() throws IOException, SQLException, NoSuchAlgorithmException {
		if (!Configuration.isKubernetesEnabled()) {
			return;
		}
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_ignore_tokens, true, false);
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_ignore_certs, true, false);
		try {
			final File awsPolicy = File.createTempFile("aws-policy", "");
			final String awsContent = "policy:\n" +
					"    id: aws\n" +
					"    name: aws-name\n" +
					"    type: AWS\n" +
					"    description: aws-description";
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(awsPolicy.toPath()), UTF_8)) {
				writer.write(awsContent);
			}
			final File azurePolicy = File.createTempFile("azure-policy", "");
			final String azureContent = "policy:\n" +
					"    id: azure\n" +
					"    name: azure-name\n" +
					"    type: AZURE\n" +
					"    description: azure-description";
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(azurePolicy.toPath()), UTF_8)) {
				writer.write(azureContent);
			}
			getClient().register(MultiPartFeature.class);
			final MultiPart multiPart = new MultiPart(MediaType.MULTIPART_FORM_DATA_TYPE);
			multiPart.bodyPart(new FormDataBodyPart("sum_aws-policy", StringUtils.md5sum(awsContent).toLowerCase()));
			multiPart.bodyPart(new FormDataBodyPart("sum_azure-policy", StringUtils.md5sum(azureContent).toLowerCase()));
			multiPart.bodyPart(new FileDataBodyPart("aws-policy", awsPolicy));
			multiPart.bodyPart(new FileDataBodyPart("azure-policy", azurePolicy));

			final Response response = put(ComplianceResource.PATH + "/policies", Status.OK, MAINUSER_TOKEN, Entity.entity(multiPart, multiPart.getMediaType()));
			assertThat(response.getStatus()).isEqualTo(Status.OK.getStatusCode());

			final long policiesCount = DbObject.executeCountQuery(getConnection(), "SELECT COUNT(id) FROM compliancepolicies WHERE (key='aws' or key='azure') and checksum IS NOT NULL");
			assertThat(policiesCount).isEqualTo(2);

			// test no changes in the compliance policies
			final String combinedChecksum = xorHexStrings(StringUtils.md5sum(awsContent), StringUtils.md5sum(azureContent));
			final Map<String, Object> headers = new HashMap<>();
			headers.put("If-None-Match", combinedChecksum.toLowerCase());
			putWithHeaders(ComplianceResource.PATH + "/policies", Status.PRECONDITION_FAILED, MAINUSER_TOKEN, headers, Entity.entity(multiPart, multiPart.getMediaType()));

			// test update a compliance policy
			final MessageDigest mdDigest = MessageDigest.getInstance("MD5");
			final String updatedAzureContent = "policy:\n" +
					"    id: azure\n" +
					"    name: azure-name\n" +
					"    type: AZURE\n" +
					"    description: updated-azure-description";
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(azurePolicy.toPath()), UTF_8)) {
				writer.write(updatedAzureContent);
			}
			final MultiPart updatedMultiPart = new MultiPart(MediaType.MULTIPART_FORM_DATA_TYPE);
			updatedMultiPart.bodyPart(new FormDataBodyPart("sum_aws-policy", StringUtils.md5sum(awsContent).toLowerCase()));
			updatedMultiPart.bodyPart(new FormDataBodyPart("sum_azure-policy", StringUtils.md5sum(updatedAzureContent).toLowerCase()));
			updatedMultiPart.bodyPart(new FileDataBodyPart("aws-policy", awsPolicy));
			updatedMultiPart.bodyPart(new FileDataBodyPart("azure-policy", azurePolicy));
			final String updatedCombinedChecksum = xorHexStrings(StringUtils.md5sum(awsContent), StringUtils.md5sum(updatedAzureContent));
			final Map<String, Object> updatedHeaders = new HashMap<>();
			updatedHeaders.put("If-None-Match", updatedCombinedChecksum.toLowerCase());
			putWithHeaders(ComplianceResource.PATH + "/policies", Status.OK, MAINUSER_TOKEN, updatedHeaders, Entity.entity(updatedMultiPart, updatedMultiPart.getMediaType()));

			List<Map<String, Object>> policies = CompliancePolicy.getGenericObjects(getConnection(), "SELECT * FROM compliancepolicies WHERE (key='aws' or key='azure') and checksum IS NOT NULL");
			assertThat(policies).hasSize(2);
			for (final Map<String, Object> policy: policies) {
				if (policy.get("key").equals("aws")) {
					assertThat(policy).containsEntry("description", "aws-description");
					assertThat(policy).containsEntry("checksum", mdDigest.digest(awsContent.getBytes(UTF_8)));
				}
				else if (policy.get("key").equals("azure")) {
					assertThat(policy).containsEntry("description", "updated-azure-description");
					assertThat(policy).containsEntry("checksum", mdDigest.digest(updatedAzureContent.getBytes(UTF_8)));
				}
			}

			// test delete a compliance policy
			final File aws1Policy = File.createTempFile("aws-policy", "");
			final String aws1Content = "policy:\n" +
					"    id: aws1\n" +
					"    name: aws-name\n" +
					"    type: AWS\n" +
					"    description: aws-description";
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(aws1Policy.toPath()), UTF_8)) {
				writer.write(aws1Content);
			}
			final MultiPart multiPartForPolicyRemoval = new MultiPart(MediaType.MULTIPART_FORM_DATA_TYPE);
			multiPartForPolicyRemoval.bodyPart(new FormDataBodyPart("sum_aws-policy-1", StringUtils.md5sum(aws1Content).toLowerCase()));
			multiPartForPolicyRemoval.bodyPart(new FileDataBodyPart("aws-policy-1", aws1Policy));
			final Map<String, Object> headersForPolicyRemoval = new HashMap<>();
			headersForPolicyRemoval.put("If-None-Match", StringUtils.md5sum(awsContent).toLowerCase());
			putWithHeaders(ComplianceResource.PATH + "/policies", Status.OK, MAINUSER_TOKEN, headersForPolicyRemoval, Entity.entity(multiPartForPolicyRemoval, multiPartForPolicyRemoval.getMediaType()));

			policies = CompliancePolicy.getGenericObjects(getConnection(), "SELECT * FROM compliancepolicies WHERE (key='aws' or key='azure' or key='aws1')");
			assertThat(policies).hasSize(3);
			for (final Map<String, Object> policy: policies) {
				if (policy.get("key").equals("aws")) {
					assertThat(policy).containsEntry("description", "aws-description");
					assertThat(policy).containsEntry("checksum", mdDigest.digest(awsContent.getBytes(UTF_8)));
					assertThat(policy.get("deleted")).isNotNull();
				}
				else if (policy.get("key").equals("azure")) {
					assertThat(policy).containsEntry("description", "updated-azure-description");
					assertThat(policy).containsEntry("checksum", mdDigest.digest(updatedAzureContent.getBytes(UTF_8)));
					assertThat(policy.get("deleted")).isNotNull();
				}
				if (policy.get("key").equals("aws1")) {
					assertThat(policy).containsEntry("description", "aws-description");
					assertThat(policy).containsEntry("checksum", mdDigest.digest(aws1Content.getBytes(UTF_8)));
					assertThat(policy.get("deleted")).isNull();
				}
			}

			// test force update compliance policies when If-None-Match header not presents
			final String forceUpdateAWSContent = "policy:\n" +
					"    id: aws\n" +
					"    name: aws-name\n" +
					"    type: AWS\n" +
					"    description: force-update-aws-description";
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(awsPolicy.toPath()), UTF_8)) {
				writer.write(forceUpdateAWSContent);
			}
			final MultiPart multiPartForPolicyForceUpdate = new MultiPart(MediaType.MULTIPART_FORM_DATA_TYPE);
			multiPartForPolicyForceUpdate.bodyPart(new FormDataBodyPart("sum_aws-policy-1", StringUtils.md5sum(awsContent).toLowerCase()));
			multiPartForPolicyForceUpdate.bodyPart(new FileDataBodyPart("aws-policy-1", awsPolicy));
			put(ComplianceResource.PATH + "/policies", Status.OK, MAINUSER_TOKEN, Entity.entity(multiPartForPolicyForceUpdate, multiPartForPolicyForceUpdate.getMediaType()));
			policies = CompliancePolicy.getGenericObjects(getConnection(), "SELECT * FROM compliancepolicies WHERE deleted is NULL");
			assertThat(policies).hasSize(1);
			assertThat(policies.get(0)).containsEntry("key", "aws");
			assertThat(policies.get(0)).containsEntry("description", "force-update-aws-description");
		}
		finally {
			Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_ignore_tokens, false, false);
			Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_ignore_certs, false, false);
		}
	}

	@Test
	public void testGetComplianceRequirement() {
		// Get requirement head
		final String totalCount = head(ComplianceResource.PATH + "/requirements", new HashMap<>(), ADMIN_TOKEN);
		assertEquals("1", totalCount);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,nameShort,category");

		// Get requirement list
		final JSONArray jsonArray = getListFromResource(ComplianceResource.PATH + "/requirements", queryParams, ADMIN_TOKEN);
		assertEquals(1, jsonArray.length());

		// Get requirement that does not exist
		Response response = get(ComplianceResource.PATH + "/requirements/0", Status.NOT_FOUND, ADMIN_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get requirement
		response = get(ComplianceResource.PATH + "/requirements/" + this.requirementId, Status.OK, ADMIN_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.requirementId, jsonObject.getInt("id"));
		assertEquals(ComplianceRequirementCategory.MONITORING_AND_LOGGING,
				jsonObject.getEnum(ComplianceRequirementCategory.class, "category"));
	}

	@Test
	public void testGetComplianceFinding() throws SQLException {
		// Get finding head
		final String totalCount = head(ComplianceResource.PATH + "/findings", new HashMap<>(), ADMIN_TOKEN);
		assertEquals("1", totalCount);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("assetId", "" + this.assetId);
		queryParams.put("fields", "id,nameShort");

		// Get finding list
		JSONArray jsonArray = getListFromResource(ComplianceResource.PATH + "/findings", queryParams, ADMIN_TOKEN);
		assertEquals(1, jsonArray.length());

		queryParams.remove("assetId");

		// Get finding that does not exist
		Response response = get(ComplianceResource.PATH + "/findings/0", Status.NOT_FOUND, ADMIN_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get finding
		response = get(ComplianceResource.PATH + "/findings/" + this.findingId, Status.OK, ADMIN_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.findingId, jsonObject.getInt("id"));

		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test",
				getAdminUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, this.assetId);
		setUserRole(getAdminSuperUserId(), new Integer[] {UserRolePermission.COMPLIANCE_VIEW.getId()});
		setResourceGroup(getAdminSuperUserId(), ResourceType.ASSET, new Integer[] {(int) tagId});
		getConnection().commit();

		// List findings as subuser with limited access
		response = get(ComplianceResource.PATH + "/findings", Status.OK, getAdminSuperUserToken());
		jsonArray = new JSONArray(response.readEntity(String.class));
		assertEquals(1, jsonArray.length());
	}

	@Test
	public void testMarkFalsePositive() throws SQLException {
		final JSONObject data = new JSONObject();
		data.put("falsePositiveComment", "Test");

		// Mark not found
		Response response = post(ComplianceResource.PATH + "/findings/0/mark-false-positive", Status.NOT_FOUND, ADMIN_TOKEN, data.toString());
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Mark
		post(ComplianceResource.PATH + "/findings/" + this.findingId + "/mark-false-positive", Status.NO_CONTENT, ADMIN_TOKEN, data.toString());
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM compliancefindings WHERE id = ? AND falsepositive IS NOT NULL", this.findingId));

		// Unmark not found
		response = post(ComplianceResource.PATH + "/findings/0/unmark-false-positive", Status.NOT_FOUND, ADMIN_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Unmark
		post(ComplianceResource.PATH + "/findings/" + this.findingId + "/unmark-false-positive", Status.NO_CONTENT, ADMIN_TOKEN, "");
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM compliancefindings WHERE id = ? AND falsepositive IS NULL", this.findingId));
	}

	@Test
	public void testMarkException() throws SQLException {
		final JSONObject data = new JSONObject();
		data.put("exceptionUntil", Instant.now().plus(Period.ofDays(5)));
		data.put("exceptionComment", "Test");

		// Mark not found
		Response response = post(ComplianceResource.PATH + "/findings/0/mark-exception", Status.NOT_FOUND, ADMIN_TOKEN, data.toString());
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Mark
		post(ComplianceResource.PATH + "/findings/" + this.findingId + "/mark-exception", Status.NO_CONTENT, ADMIN_TOKEN, data.toString());
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM compliancefindings WHERE id = ? AND exception IS NOT NULL", this.findingId));

		// Unmark not found
		response = post(ComplianceResource.PATH + "/findings/0/unmark-exception", Status.NOT_FOUND, ADMIN_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Unmark
		post(ComplianceResource.PATH + "/findings/" + this.findingId + "/unmark-exception", Status.NO_CONTENT, ADMIN_TOKEN, "");
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM compliancefindings WHERE id = ? AND exception IS NULL", this.findingId));
	}

	@Test
	public void testTagLink() throws SQLException {
		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test",
				getAdminUserCustomerId());
		getConnection().commit();

		// Add link with missing finding
		Response response = put(ComplianceResource.PATH + "/findings/0/tags/0", Status.NOT_FOUND, ADMIN_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link with missing tag
		response = put(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags/0", Status.NOT_FOUND, ADMIN_TOKEN, "");
		assertEquals(getMessage("_TAG_ID_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove not linked tag
		response = delete(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags/" + tagId, Status.NOT_FOUND, ADMIN_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link
		response = put(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags/" + tagId, Status.CREATED, ADMIN_TOKEN, "");
		assertEquals(tagId, getCreatedId(response));
		assertEquals(1,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_compliancefinding WHERE tagid = ? AND compliancefindingid = ?", tagId, this.findingId));

		// Remove link with missing finding
		response = delete(ComplianceResource.PATH + "/findings/0/tags/0", Status.NOT_FOUND, ADMIN_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link with missing tag
		response = delete(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags/0", Status.NOT_FOUND, ADMIN_TOKEN);
		assertEquals(getMessage("_TAG_ID_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link
		delete(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags/" + tagId, Status.NO_CONTENT, ADMIN_TOKEN);
		assertEquals(0,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_compliancefinding WHERE tagid = ? AND compliancefindingid = ?", tagId, this.findingId));

		// Edit set of linked tags
		final Integer[] firstTagSet = createSetOfTags(3, getAdminUserCustomerId(), "first");
		JSONArray payload = new JSONArray(firstTagSet);
		put(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags", Status.NO_CONTENT, ADMIN_TOKEN, payload.toString());
		assertEquals(3,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_compliancefinding WHERE compliancefindingid = ? AND tagid = ANY(?)", this.findingId,
						firstTagSet));

		final Integer[] secondTagSet = createSetOfTags(3, getAdminUserCustomerId(), "second");
		payload = new JSONArray(secondTagSet);
		put(ComplianceResource.PATH + "/findings/" + this.findingId + "/tags", Status.NO_CONTENT, ADMIN_TOKEN, payload.toString());
		assertEquals(3,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_compliancefinding WHERE compliancefindingid = ? AND tagid = ANY(?)", this.findingId,
						secondTagSet));
		assertEquals(0,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_compliancefinding WHERE compliancefindingid = ? AND tagid = ANY(?)", this.findingId,
						firstTagSet));
	}

	@Test
	public void testRequestComplianceReport() throws SQLException, JAXBException, ParamValidationException {
		final String downloadKey = UUID.randomUUID().toString();

		final ReturnResultParameters returnResultParameters = new ReturnResultParameters();
		returnResultParameters.setReturnResult(true);

		final ComplianceReport report = new ComplianceReport();
		report.setName("Compliance");
		report.setLevel(ReportLevel.Detailed);
		report.setAssetIds(new Integer[] {(int)this.assetId});
		report.setViewTemplateId((int)this.viewTemplateId);
		report.setFormat(ReportFormat.PDF);

		final BaseLoggedOnUser baseLoggedOnUser = mock(BaseLoggedOnUser.class);

		final ComplianceResource complianceResource = spy(new ComplianceResource());
		doReturn(baseLoggedOnUser).when(complianceResource).getUser();
		doReturn(getConnection()).when(complianceResource).getConnection();

		final ReportExportResponse reportExportResponse = new ReportExportResponse();
		reportExportResponse.setResultCode(ExportResult.STARTED);
		reportExportResponse.setReportDownloadEntryId(1L);

		// With the removal of the report service as a xmlapi dependency, we need to remove the actual call to
		// the reportservice microservice and replacing it with a mock of the reportExportService
		final ReportExportServiceImpl reportExportService = spy(new ReportExportServiceImpl(null, null));
		doReturn(reportExportResponse).when(reportExportService).exportReport(any());

		final ReportDownloadEntry reportEntry = new ReportDownloadEntry();
		reportEntry.setKey(downloadKey);

		final ReportingServiceImpl reportingService = spy(new ReportingServiceImpl(null, null, null, null, null, null, null, null));
		doReturn(reportEntry).when(reportingService).getReportDownloadEntry(any(), anyLong());

		try (final MockedStatic<ServiceProvider> serviceProvider = Mockito.mockStatic(ServiceProvider.class)) {
			serviceProvider.when(() -> ServiceProvider.getReportExportService(any())).thenReturn(reportExportService);
			serviceProvider.when(() -> ServiceProvider.getReportingService(any())).thenReturn(reportingService);
			serviceProvider.when(ServiceProvider::getReportExportValidator).thenCallRealMethod();
			serviceProvider.when(ServiceProvider::getMetricsService).thenCallRealMethod();

			final Response response = complianceResource.requestComplianceReport(report, returnResultParameters);
			final JSONObject json = new JSONObject((String)response.getEntity());
			assertThat(json.getString("key")).isEqualTo(downloadKey);
		}
	}

	/**
	 * Verify that tags are inherited from asset groups and assets to compliance finding
	 *
	 * <p>There are two asset groups, Parent and Child. Both have the same tag.
	 * There is an asset, which belongs to Child asset group.
	 * The asset has two tags. The same tag as the asset groups and its own tag.
	 * The finding has its own tag.
	 *
	 * <p>GET the finding and verify that one instance of the asset group tags, the asset tags and the finding's own tag are included in the response.
	 *
	 * <p>DELETE the Parent asset group, which also deletes the Child asset group, and their tag links.
	 *
	 * <p>GET the finding and verify that the asset tag and the finding's own tag is presented.
	 *
	 * <p>DELETE all tags previously created
	 *
	 * <p>GET the finding and verify that the tag parameter is not included in the response.
	 */
	@Test
	@RequireDatabaseCommits
	public void testComplianceFindingTags() {
		// Create parent asset group
		final JSONObject parentJson = new JSONObject()
				.put("name", "Parent")
				.put("source", new JSONArray(new String[] {Source.SCALE.name()}));

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		Response response = post(AssetGroupsResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, parentJson.toString());
		JSONObject createdAssetGroup = new JSONObject(response.readEntity(String.class));
		final long parentId = createdAssetGroup.getLong("id");

		assertThatJson(createdAssetGroup)
				.isObject()
				.containsEntry("name", "Parent");
		assertThatJson(createdAssetGroup).node("path").isArray().containsExactly(parentId);

		// Create child asset group
		final JSONObject childJson = new JSONObject()
				.put("name", "Child")
				.put("source", new JSONArray(new String[] {Source.SCALE.name()}))
				.put("parentId", parentId);

		response = post(AssetGroupsResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, childJson.toString());
		createdAssetGroup = new JSONObject(response.readEntity(String.class));
		final long childId = createdAssetGroup.getLong("id");

		assertThatJson(createdAssetGroup)
				.isObject()
				.containsEntry("name", "Child");
		assertThatJson(createdAssetGroup).node("path").isArray().containsExactly(parentId, childId);

		// Create assetgroup tag
		final JSONObject assetGroupTag = new JSONObject()
				.put("key", "TestKey1")
				.put("value", "TestValue1");

		response = post(TagsResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, assetGroupTag.toString());
		JSONObject createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey1")
				.containsEntry("value", "TestValue1");
		final long assetGroupTagId = createdTag.getLong("id");

		// Add tag link to child asset group
		response = put(AssetGroupsResource.PATH + "/" + childId + "/tags/" + assetGroupTagId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(assetGroupTagId);

		// Add tag link to parent asset group
		response = put(AssetGroupsResource.PATH + "/" + parentId + "/tags/" + assetGroupTagId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(assetGroupTagId);

		// Create asset tag
		final JSONObject assetTag = new JSONObject()
				.put("key", "TestKey2")
				.put("value", "TestValue2");

		response = post(TagsResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, assetTag.toString());
		createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey2")
				.containsEntry("value", "TestValue2");
		final long assetTagId = createdTag.getLong("id");

		// Add tag links to asset
		response = put(AssetsResource.PATH + "/" + this.assetId + "/tags/" + assetTagId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(assetTagId);
		response = put(AssetsResource.PATH + "/" + this.assetId + "/tags/" + assetGroupTagId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(assetGroupTagId);

		// Add link to asset group
		response = put(AssetGroupsResource.PATH + "/" + childId + "/assets/" + this.assetId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(this.assetId);

		// Create finding tag
		final JSONObject findingTag = new JSONObject()
				.put("key", "TestKey3")
				.put("value", "TestValue3");

		response = post(TagsResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, findingTag.toString());
		createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey3")
				.containsEntry("value", "TestValue3");
		final long findingTagId = createdTag.getLong("id");

		// Add tag link to finding
		response = put(ComplianceResource.PATH + "/findings/" + findingId + "/tags/" + findingTagId, Status.CREATED, ADMIN_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(findingTagId);

		// Get finding
		queryParams.clear();
		queryParams.put("fields", "id,name,tags");

		response = get(ComplianceResource.PATH + "/findings/" + findingId, Status.OK, ADMIN_TOKEN, queryParams);
		JSONObject responseJson = new JSONObject(response.readEntity(String.class));
		final JSONObject expectedAssetGroupTag = new JSONObject(assetGroupTag.toString())
				.put("id", assetGroupTagId)
				.put("inherited", true);
		final JSONObject expectedAssetTag = new JSONObject(expectedAssetGroupTag.toString());
		final JSONObject expectedAssetTag2 = new JSONObject(assetTag.toString())
				.put("id", assetTagId)
				.put("inherited", true);
		final JSONObject expectedFindingTag = new JSONObject(findingTag.toString())
				.put("id", findingTagId)
				.put("inherited", false);

		// Expect tags from asset groups, asset and own tag. Since the tag information is identical on both parent and child asset groups,
		// only one instance of the asset group tag is presented on the finding level
		assertThatJson(responseJson).node("tags")
				.isArray()
				.containsExactlyInAnyOrder(json(expectedFindingTag.toString()), json(expectedAssetTag.toString()), json(expectedAssetTag2.toString()),
						json(expectedAssetGroupTag.toString()));

		// Delete child asset group
		response = delete(AssetGroupsResource.PATH + "/" + childId, Status.NO_CONTENT, ADMIN_TOKEN);

		response = get(ComplianceResource.PATH + "/findings/" + findingId, Status.OK, ADMIN_TOKEN, queryParams);
		responseJson = new JSONObject(response.readEntity(String.class));

		assertThatJson(responseJson).node("tags")
				.isArray()
				.containsExactlyInAnyOrder(json(expectedFindingTag.toString()), json(expectedAssetTag.toString()), json(expectedAssetTag2.toString()));

		// Delete parent asset group
		response = delete(AssetGroupsResource.PATH + "/" + parentId, Status.NO_CONTENT, ADMIN_TOKEN);

		// Delete tags
		response = delete(TagsResource.PATH + "/" + assetGroupTagId, Status.NO_CONTENT, ADMIN_TOKEN);
		response = delete(TagsResource.PATH + "/" + assetTagId, Status.NO_CONTENT, ADMIN_TOKEN);
		response = delete(TagsResource.PATH + "/" + findingTagId, Status.NO_CONTENT, ADMIN_TOKEN);

		response = get(ComplianceResource.PATH + "/findings/" + findingId, Status.OK, ADMIN_TOKEN, queryParams);
		responseJson = new JSONObject(response.readEntity(String.class));

		assertThatJson(responseJson).node("tags").isAbsent();
	}

	/**
	 * XOR operator on 2 hex strings.
	 *
	 * @param hex1 The first hex string.
	 * @param hex2 The second hex string.
	 * @return The result hex string.
	 */
	public static String xorHexStrings(final String hex1, final String hex2) {
		if ((StringUtils.isEmpty(hex1) || StringUtils.isEmpty(hex2)) || hex1.length() != hex2.length()) {
			throw new IllegalArgumentException("Hex strings must be of the same length");
		}
		final byte[] bytes1 = DatatypeConverter.parseHexBinary(hex1);
		final byte[] bytes2 = DatatypeConverter.parseHexBinary(hex2);
		final byte[] resultBytes = new byte[bytes1.length];
		for (int i = 0; i < bytes1.length; i++) {
			resultBytes[i] = (byte) (bytes1[i] ^ bytes2[i]);
		}
		return DatatypeConverter.printHexBinary(resultBytes);
	}
}
