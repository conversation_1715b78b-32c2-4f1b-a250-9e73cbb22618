package com.chilicoders.rest.resources;

import static com.chilicoders.rest.resources.BaseResource.BASE_PATH;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static org.assertj.core.api.Assertions.assertThat;

import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.Form;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBException;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Test;

import com.chilicoders.core.user.api.model.TwoFactorType;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.PasswordPolicy;
import com.chilicoders.db.objects.ValidationToken;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.models.ErrorMessage;
import com.chilicoders.rest.models.Tag;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.Pgp;
import com.chilicoders.util.PgpTest;
import com.chilicoders.util.StringUtils;
import com.chilicoders.xmlapi.XMLAPITestBase;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class SubUsersResourceTest extends BaseResourceTest {

	private static final String ALLOWED_CUSTOMER_TAGS = "allowedCustomerTags";
	private static final String ALLOWED_CUSTOMER_TAG_IDS = "allowedCustomerTagIds";

	@Test
	public void testGetSubUserHeader() {
		final String totalCount = head(SubUsersResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertThat(totalCount).isEqualTo("4");
	}

	@Test
	public void testGetSubUserList() {
		setupDatabase();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,lastName");

		// List all subusers
		final Response response = get(SubUsersResource.PATH, Status.OK, MAINUSER_TOKEN, queryParams);
		final JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		assertThat(jsonArray.length()).isEqualTo(4);
	}

	@Test
	public void testGetSubUser() throws SQLException {
		setupDatabase();
		setSubUserTokens();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,lastName,username");

		// Get subuser that don't exist
		Response response = get(SubUsersResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.USERS_VIEW.getId()});
		getConnection().commit();

		// Get subuser as subuser
		response = get(SubUsersResource.PATH + "/" + getAllAccessSubUserId(), Status.OK, getAllAccessSubUserToken(), queryParams);
		JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getLong("id")).isEqualTo(getAllAccessSubUserId());

		// Get subuser as subuser without access to users
		DbObject.executeUpdate(getConnection(), "UPDATE tusergroups SET bsubadmin = 0 WHERE vcname = ?", "All rights");
		getConnection().commit();

		response = get(SubUsersResource.PATH + "/" + getAllAccessSubUserId(), Status.OK, getAllAccessSubUserToken(), queryParams);
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getString("username")).isEqualTo("SUBUSER_ALLACCESS");
	}

	@Test
	public void testGetUserRoles() throws SQLException {
		setupDatabase();

		final String requesterdFields = "id,username,roles";
		final Map<String, String> queryParams = ImmutableMap.of("fields", requesterdFields);

		// Verify roles field is absent
		Response response = get(SubUsersResource.PATH + "/" + getAllAccessSubUserId(), Status.OK, MAINUSER_TOKEN, queryParams);
		JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getLong("id")).isEqualTo(getAllAccessSubUserId());
		assertThatJson(jsonObject).node("roles").isAbsent();

		// Set user role on test user
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET xroles = ? WHERE xid = ?", "TestRole", XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Verify that main user does see roles field on sub user
		response = get(SubUsersResource.PATH + "/" + getAllAccessSubUserId(), Status.OK, MAINUSER_TOKEN, queryParams);
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getLong("id")).isEqualTo(getAllAccessSubUserId());
		assertThat(jsonObject.getString("roles")).isEqualTo("TestRole");
		assertThat(jsonObject.keySet()).containsExactlyInAnyOrderElementsOf(Arrays.asList(requesterdFields.split(",")));

		// Verify that sub user does see roles field on sub user
		setSubUserTokens();
		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.USERS_VIEW.getId()});
		getConnection().commit();
		response = get(SubUsersResource.PATH + "/" + getAllAccessSubUserId(), Status.OK, getAllAccessSubUserToken(), ImmutableMap.of());
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getLong("id")).isEqualTo(getAllAccessSubUserId());
		assertThat(jsonObject.getString("roles")).isEqualTo("TestRole");
	}

	@Test
	public void testGetCurrentSubUser() {
		setupDatabase();
		setSubUserTokens();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,lastName");

		// Get current subuser
		final Response response = get(SubUsersResource.PATH + "/me", Status.OK, getSuperUserToken(), queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getLong("id")).isEqualTo(getSuperUserId());

		// Get current user
		get(SubUsersResource.PATH + "/me", Status.TEMPORARY_REDIRECT, MAINUSER_TOKEN, queryParams);
	}

	@Test
	public void testPatchSubUser() throws SQLException {
		setupDatabase();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,lastName");
		queryParams.put("return-result", "true");

		final JSONObject postJson = new JSONObject();
		postJson.put("lastName", "Test");

		// Patch subuser that don't exist
		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");
		Response response = patch(SubUsersResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, postJson.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Patch subuser
		response = patch(SubUsersResource.PATH + "/" + subUserId, Status.OK, MAINUSER_TOKEN, queryParams, postJson.toString());
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getString("lastName")).isEqualTo("Test");

		// Patch subuser and don't return result
		queryParams.remove("return-result");
		patch(SubUsersResource.PATH + "/" + subUserId, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, postJson.toString());
	}

	@Test
	public void testPatchSubUserSms() throws SQLException {
		setupDatabase();

		// Prepare data
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final JSONObject postJson = new JSONObject();
		postJson.put("twoFactorType", "SMS");
		postJson.put("mobilePhone", "8412345678");

		// Patch subuser
		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");
		final Response response = patch(SubUsersResource.PATH + "/" + subUserId, Status.OK, MAINUSER_TOKEN, queryParams, postJson.toString());
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getString("twoFactorType")).isEqualTo(TwoFactorType.SMS.name());
	}

	@Test
	public void testPatchSubUserEmptyInputs() throws SQLException {
		setupDatabase();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final JSONObject data = new JSONObject();
		data.put("firstName", "");
		data.put("lastName", "");
		data.put("username", "");
		data.put("email", "");

		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");
		final Response response = patch(SubUsersResource.PATH + "/" + subUserId, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		final ErrorMessage error = getErrorResponse(response);
		assertThat(error.getMessage()).isEqualTo(getError(ErrorCode.InputValidationFailed));
		assertThat(error.getDetails()).containsExactlyInAnyOrder("firstName must not be empty", "lastName must not be empty", "username must not be empty",
				"email  is not a valid email");
	}

	@Test
	public void testPatchSubUserAsSubUser() throws SQLException {
		setupDatabase();
		setSubUserTokens();

		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");

		// without required permissions
		Response response = patch(SubUsersResource.PATH + "/" + subUserId, Status.FORBIDDEN, getAllAccessSubUserToken(), queryParams, data.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_MISSING_PERMISSIONS"));

		// add required permissions to the sub-user
		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.USERS_VIEW_AND_MANAGE.getId()});
		getConnection().commit();

		response = patch(SubUsersResource.PATH + "/" + subUserId, Status.OK, getAllAccessSubUserToken(), queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));
	}

	@Test
	public void testPatchCurrentSubUser() {
		setupDatabase();
		setSubUserTokens();

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,lastName");
		queryParams.put("return-result", "true");

		final JSONObject postJson = new JSONObject();
		postJson.put("lastName", "Test");

		// Patch current subuser
		final Response response = patch(SubUsersResource.PATH + "/me", Status.OK, getSuperUserToken(), queryParams, postJson.toString());
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getString("lastName")).isEqualTo("Test");

		// Patch current user
		patch(SubUsersResource.PATH + "/me", Status.TEMPORARY_REDIRECT, MAINUSER_TOKEN, queryParams, postJson.toString());
	}

	@Test
	public void testPatchCurrentSubUserSms() throws SQLException {
		setupDatabase();
		setSubUserTokens();

		// Send SMS code
		final JSONObject smsJson = new JSONObject();
		smsJson.put("phoneNumber", "8412345678");
		post(AuthResource.PATH + "/send-2fa-sms", Status.NO_CONTENT, getSuperUserToken(), smsJson.toString());

		// Prepare data
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");
		final ValidationToken token = ValidationToken.getByField(ValidationToken.class, getConnection(), Access.ADMIN, "XSUBUSERXID", subUserId);
		final JSONObject postJson = new JSONObject();
		postJson.put("twoFactorType", "SMS");
		postJson.put("mobilePhone", "8412345678");
		postJson.put("twoFactorCode", token.getId());

		// Patch subuser
		final Response response = patch(SubUsersResource.PATH + "/me", Status.OK, getSuperUserToken(), queryParams, postJson.toString());
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertThat(jsonObject.getString("twoFactorType")).isEqualTo(TwoFactorType.SMS.name());
	}

	@Test
	public void testPatchCurrentSubUserSmsInvalidCode() {
		setupDatabase();
		setSubUserTokens();

		// Prepare data
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final JSONObject postJson = new JSONObject();
		postJson.put("twoFactorType", "SMS");
		postJson.put("mobilePhone", "8412345678");
		postJson.put("twoFactorCode", "invalid_code");

		// Patch subuser
		final Response response = patch(SubUsersResource.PATH + "/me", Status.BAD_REQUEST, getSuperUserToken(), queryParams, postJson.toString());
		final ErrorMessage error = getErrorResponse(response);
		assertThat(error.getMessage()).isEqualTo(getMessage("_ERROR_VALIDATE_CODE"));
	}

	@Test
	public void testAllowedCustomerTagsAdmin() throws SQLException, JAXBException {
		setupDatabase();
		setSubUserTokens();

		final Map<String, String> queryParams = Maps.newHashMap();
		queryParams.put("fields", "id,lastName," + ALLOWED_CUSTOMER_TAG_IDS + "," + ALLOWED_CUSTOMER_TAGS);
		queryParams.put("return-result", "true");

		// Get admin subuser
		// Verify that no allowed customer tags exist
		Response response = get(SubUsersResource.PATH + "/me", Status.OK, getAllAccessSubUserToken(), queryParams);
		JSONObject jsonSubUser = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonSubUser).node("id").isEqualTo(getAllAccessSubUserId());
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isAbsent();
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isAbsent();

		// Create TestTag1
		final Map<String, String> postQueryParams = ImmutableMap.of("return-result", "true");
		final JSONObject tag1Data = new JSONObject();
		tag1Data.put("key", "TestTag1");
		tag1Data.put("value", "TestValue1");
		response = post(TagsResource.PATH, Status.CREATED, ADMIN_TOKEN, postQueryParams, tag1Data.toString());
		final JSONObject tag1Json = new JSONObject(response.readEntity(String.class));
		final Tag tag1 = MarshallingUtils.unmarshal(Tag.class, tag1Json.toString());

		// Assign TestTag1, as allowedCustomerTags to GhostLabs sub user1
		final JSONArray tagIdArray = new JSONArray();
		tagIdArray.put(tag1.getId());
		final JSONObject patchJson = new JSONObject();
		patchJson.put(ALLOWED_CUSTOMER_TAG_IDS, tagIdArray);
		patch(SubUsersResource.PATH + "/" + getAdminSuperUserId(), Status.FORBIDDEN, ADMIN_TOKEN, ImmutableMap.of(), patchJson.toString());

		// Get admin subuser
		// Verify that no allowed customer tags exist
		response = get(SubUsersResource.PATH + "/me", Status.OK, getAdminSuperUserToken(), queryParams);
		jsonSubUser = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonSubUser).node("id").isEqualTo(getAdminSuperUserId());
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isAbsent();
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isAbsent();
	}

	@Test
	public void testAllowedCustomerTags() throws SQLException, JAXBException {
		setupDatabase();
		setSubUserTokens();
		setGhostLabsUserTokens();

		final Map<String, String> queryParams = Maps.newHashMap();
		queryParams.put("fields", "id,lastName," + ALLOWED_CUSTOMER_TAG_IDS + "," + ALLOWED_CUSTOMER_TAGS);
		queryParams.put("return-result", "true");

		// Get current subuser
		// Verify that no allowed customer tags exist
		Response response = get(SubUsersResource.PATH + "/me", Status.OK, ghostLabsSubUser1Token, queryParams);
		JSONObject jsonSubUser = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonSubUser).node("id").isEqualTo(ghostLabsSubUser1Id);
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isAbsent();
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isAbsent();

		// Get GhostLabs subuser
		// Verify that no allowed customer tags exist, because not set
		response = get(SubUsersResource.PATH + "/" + ghostLabsSubUser1Id, Status.OK, ghostLabsMainUserToken, queryParams);
		jsonSubUser = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonSubUser).node("id").isEqualTo(ghostLabsSubUser1Id);
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isAbsent();
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isAbsent();

		// Create TestTag1
		final Map<String, String> postQueryParams = ImmutableMap.of("return-result", "true");
		final JSONObject tag1Data = new JSONObject();
		tag1Data.put("key", "TestTag1");
		tag1Data.put("value", "TestValue1");
		response = post(TagsResource.PATH, Status.CREATED, ghostLabsMainUserToken, postQueryParams, tag1Data.toString());
		final JSONObject tag1Json = new JSONObject(response.readEntity(String.class));
		final Tag tag1 = MarshallingUtils.unmarshal(Tag.class, tag1Json.toString());

		// Assign TestTag1, as allowedCustomerTags to GhostLabs sub user1
		JSONArray tagIdArray = new JSONArray();
		tagIdArray.put(tag1.getId());
		JSONObject patchJson = new JSONObject();
		patchJson.put(ALLOWED_CUSTOMER_TAG_IDS, tagIdArray);
		patch(SubUsersResource.PATH + "/" + ghostLabsSubUser1Id, Status.NO_CONTENT, ghostLabsMainUserToken, ImmutableMap.of(), patchJson.toString());

		// Get current subuser with sub user token
		// Verify that no allowed customer tags exist
		response = get(SubUsersResource.PATH + "/me", Status.OK, ghostLabsSubUser1Token, queryParams);
		jsonSubUser = new JSONObject(response.readEntity(String.class));
		assertThatJson(jsonSubUser).node("id").isEqualTo(ghostLabsSubUser1Id);
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isArray().extracting("id").containsExactly(tag1.getId());
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isAbsent();

		// Get GhostLabs subuser with main user token
		// Verify that assigned allowed customer tag is present
		response = get(SubUsersResource.PATH + "/" + ghostLabsSubUser1Id, Status.OK, ghostLabsMainUserToken, queryParams);
		String responseEntity = response.readEntity(String.class);
		jsonSubUser = new JSONObject(responseEntity);
		assertThatJson(jsonSubUser).node("id").isEqualTo(ghostLabsSubUser1Id);
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAGS).isArray().extracting("id").containsExactly(tag1.getId());
		assertThatJson(jsonSubUser).node(ALLOWED_CUSTOMER_TAG_IDS).isEqualTo(tagIdArray);

		// Get GhostLabs subusers with main user token
		// Verify that both sub users are returned
		response = get(SubUsersResource.PATH, Status.OK, ghostLabsMainUserToken, queryParams);
		responseEntity = response.readEntity(String.class);
		JSONArray jsonSubUsers = new JSONArray(responseEntity);
		assertThatJson(jsonSubUsers).isArray().extracting("id").containsExactlyInAnyOrder(ghostLabsSubUser1Id, ghostLabsSubUser2Id);

		// Verify that filtering in allowedcustomertags returns the expected sub users
		// Get GhostLabs subusers with main user token filtering on TestTag1
		// Verify that sub user1 is returned
		queryParams.put("filter", "[{\"field\":\"allowedCustomerTags\",\"value\":\"TestTag1\",\"comparison\":\"eq\"}]");
		response = get(SubUsersResource.PATH, Status.OK, ghostLabsMainUserToken, queryParams);
		responseEntity = response.readEntity(String.class);
		jsonSubUsers = new JSONArray(responseEntity);
		assertThatJson(jsonSubUsers).isArray().extracting("id").containsExactly(ghostLabsSubUser1Id);

		// Create TestTag2
		final JSONObject tag2Data = new JSONObject();
		tag2Data.put("key", "TestTag2");
		tag2Data.put("value", "TestValue2");
		response = post(TagsResource.PATH, Status.CREATED, ghostLabsMainUserToken, postQueryParams, tag2Data.toString());
		final JSONObject tag2Json = new JSONObject(response.readEntity(String.class));
		final Tag tag2 = MarshallingUtils.unmarshal(Tag.class, tag2Json.toString());

		// Get GhostLabs subusers with main user token filtering on TestTag2
		// Verify that no sub users are returned
		queryParams.put("filter", "[{\"field\":\"allowedCustomerTags\",\"value\":\"TestTag2\",\"comparison\":\"eq\"}]");
		response = get(SubUsersResource.PATH, Status.OK, ghostLabsMainUserToken, queryParams);
		responseEntity = response.readEntity(String.class);
		jsonSubUsers = new JSONArray(responseEntity);
		assertThatJson(jsonSubUsers).isArray().isEmpty();

		// Assign TestTag2, as allowedCustomerTags to GhostLabs sub user2
		tagIdArray = new JSONArray();
		tagIdArray.put(tag2.getId());
		patchJson = new JSONObject();
		patchJson.put(ALLOWED_CUSTOMER_TAG_IDS, tagIdArray);
		patch(SubUsersResource.PATH + "/" + ghostLabsSubUser2Id, Status.NO_CONTENT, ghostLabsMainUserToken, ImmutableMap.of(), patchJson.toString());

		// Get GhostLabs subusers with main user token filtering on TestTag2
		// Verify that sub user2 is returned
		queryParams.put("filter", "[{\"field\":\"allowedCustomerTags\",\"value\":\"TestTag2\",\"comparison\":\"eq\"}]");
		response = get(SubUsersResource.PATH, Status.OK, ghostLabsMainUserToken, queryParams);
		responseEntity = response.readEntity(String.class);
		jsonSubUsers = new JSONArray(responseEntity);
		assertThatJson(jsonSubUsers).isArray().extracting("id").containsExactly(ghostLabsSubUser2Id);

		// Assign TestTag1 and TestTag2, as allowedCustomerTags to GhostLabs sub user2
		tagIdArray = new JSONArray();
		tagIdArray.put(tag1.getId());
		tagIdArray.put(tag2.getId());
		patchJson = new JSONObject();
		patchJson.put(ALLOWED_CUSTOMER_TAG_IDS, tagIdArray);
		patch(SubUsersResource.PATH + "/" + ghostLabsSubUser2Id, Status.NO_CONTENT, ghostLabsMainUserToken, ImmutableMap.of(), patchJson.toString());

		// Get GhostLabs subusers with main user token filtering on TestTag1
		// Verify that both sub users are returned
		queryParams.put("filter", "[{\"field\":\"allowedCustomerTags\",\"value\":\"TestValue1\",\"comparison\":\"eq\"}]");
		response = get(SubUsersResource.PATH, Status.OK, ghostLabsMainUserToken, queryParams);
		responseEntity = response.readEntity(String.class);
		jsonSubUsers = new JSONArray(responseEntity);
		assertThatJson(jsonSubUsers).isArray().extracting("id").containsExactlyInAnyOrder(ghostLabsSubUser1Id, ghostLabsSubUser2Id);
	}

	@Test
	public void testTagLink() throws SQLException {
		setupDatabase();

		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		getConnection().commit();

		final long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");

		// Add link with missing user
		Response response = put(SubUsersResource.PATH + "/0/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Add link with missing tag
		response = put(SubUsersResource.PATH + "/" + subUserId + "/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_TAG_ID_NOT_FOUND"));

		// Remove not linked tag
		response = delete(SubUsersResource.PATH + "/" + subUserId + "/tags/" + tagId, Status.NOT_FOUND, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Add link
		response = put(SubUsersResource.PATH + "/" + subUserId + "/tags/" + tagId, Status.CREATED, MAINUSER_TOKEN, "");
		assertThat(getCreatedId(response)).isEqualTo(tagId);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_user WHERE tagid = ? AND userid = ?", tagId, -subUserId)).isOne();

		// Remove link with missing user
		response = delete(SubUsersResource.PATH + "/0/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Remove link with missing tag
		response = delete(SubUsersResource.PATH + "/" + subUserId + "/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_TAG_ID_NOT_FOUND"));

		// Remove link
		delete(SubUsersResource.PATH + "/" + subUserId + "/tags/" + tagId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_user WHERE tagid = ? AND userid = ?", tagId, -subUserId)).isZero();

		// Edit set of linked tags
		final Integer[] firstTagSet = createSetOfTags(3, getTestUserCustomerId(), "first");
		JSONArray payload = new JSONArray(firstTagSet);
		put(SubUsersResource.PATH + "/" + subUserId + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, payload.toString());
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_user WHERE userid = ? AND tagid = ANY(?)", -subUserId, firstTagSet)).isEqualTo(3);

		final Integer[] secondTagSet = createSetOfTags(3, getTestUserCustomerId(), "second");
		payload = new JSONArray(secondTagSet);
		put(SubUsersResource.PATH + "/" + subUserId + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, payload.toString());
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_user WHERE userid = ? AND tagid = ANY(?)", -subUserId, secondTagSet)).isEqualTo(3);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_user WHERE userid = ? AND tagid = ANY(?)", -subUserId, firstTagSet)).isZero();
	}

	@Test
	public void testPostSubUser() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("username", "Test1");
		data.put("email", "<EMAIL>");
		data.put("twoFactorType", "NONE");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));
		assertThat(jsonObject.getString("emailEncryptionKey")).isEqualTo(Pgp.UNENCRYPTED_KEY);
		assertThat(jsonObject.getString("twoFactorType")).isEqualTo(TwoFactorType.NONE.name());
	}

	@Test
	public void testPostSubUserValidPGPKey() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("username", "Test1");
		data.put("email", "<EMAIL>");
		data.put("pgpPublicKey", PgpTest.userKeyPublic);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));
		assertThat(jsonObject.getString("emailEncryptionKey")).isEqualTo(Pgp.SHARED_KEY);
		assertThat(jsonObject.getString("pgpPublicKey")).isEqualTo(PgpTest.userKeyPublic);
	}

	@Test
	public void testPostSubUserInvalidPGPKey() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("username", "Test1");
		data.put("email", "<EMAIL>");
		data.put("pgpPublicKey", "invalid_key");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		final ErrorMessage error = getErrorResponse(response);
		assertThat(error.getMessage()).isEqualTo(getMessage("_INVALID_PGP_KEY"));
	}

	@Test
	public void testPostSubUserSms() {
		setupDatabase();

		// Prepare data
		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("username", "Test1");
		data.put("email", "<EMAIL>");
		data.put("mobilePhone", "8412345678");
		data.put("twoFactorType", "SMS");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		// Create user
		final Response response = post(SubUsersResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));
		assertThat(jsonObject.getString("twoFactorType")).isEqualTo(TwoFactorType.SMS.name());
	}

	@Test
	public void testPostSubUserEmptyInputs() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("firstName", "");
		data.put("lastName", "");
		data.put("username", "");
		data.put("email", "");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		final ErrorMessage error = getErrorResponse(response);
		assertThat(error.getMessage()).isEqualTo(getError(ErrorCode.InputValidationFailed));
		assertThat(error.getDetails()).containsExactlyInAnyOrder("firstName must not be empty", "lastName must not be empty", "username must not be empty",
				"email  is not a valid email");
	}

	@Test
	public void testPostSubUserInvalidUsername() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("username", "Test");
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("email", "<EMAIL>");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		// valid
		Response response = post(SubUsersResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));

		// already existing
		response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getError(ErrorCode.UsernameAlreadyExist));

		// empty
		data.put("username", "");
		response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		assertThat(getErrorResponse(response).getDetails()).hasToString("[username must not be empty]");

		// too short
		data.put("username", "Te");
		response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getError(ErrorCode.UsernameTooShort));
	}

	@Test
	public void testPostSubUserTooLongInputs() {
		final JSONObject data = new JSONObject();
		final String longInput = StringUtils.repeat("a", 101);
		data.put("firstName", longInput);
		data.put("lastName", longInput);
		data.put("username", longInput);
		data.put("email", "<EMAIL>");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, data.toString());
		final ErrorMessage error = getErrorResponse(response);
		assertThat(error.getMessage()).isEqualTo(getError(ErrorCode.InputValidationFailed));
		assertThat(error.getDetails()).containsExactlyInAnyOrder("firstName size must be between 0 and 100", "lastName size must be between 0 and 100",
				"username size must be between 0 and 64");
	}

	@Test
	public void testPostSubUserSetSubParentId() {
		setupDatabase();

		final JSONObject data = new JSONObject();
		data.put("username", "Test");
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("email", "<EMAIL>");
		data.put("subParentId", 1000);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response = post(SubUsersResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getInt("subParentId")).isEqualTo(-1);
	}

	@Test
	public void testPostSubUserAsSubUser() throws SQLException {
		setupDatabase();
		setSubUserTokens();

		final JSONObject data = new JSONObject();
		data.put("firstName", "Test");
		data.put("lastName", "Test");
		data.put("username", "Test1");
		data.put("email", "<EMAIL>");

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		// without required permissions
		Response response = post(SubUsersResource.PATH, Status.FORBIDDEN, getAllAccessSubUserToken(), queryParams, data.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_MISSING_PERMISSIONS"));

		// add required permissions to the sub-user
		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.USERS_VIEW_AND_MANAGE.getId()});
		getConnection().commit();

		response = post(SubUsersResource.PATH, Status.CREATED, getAllAccessSubUserToken(), queryParams, data.toString());
		final JSONObject jsonObject = new JSONObject((response.readEntity(String.class)));
		assertThat(jsonObject.getString("firstName")).isEqualTo(data.getString("firstName"));
	}

	@Test
	@SuppressFBWarnings(value = "DMI_HARDCODED_ABSOLUTE_FILENAME", justification = "Intentionally using hardcoded absolute pathname")
	public void testChangePasswordForSubUser() {
		setupDatabase();
		setSubUserTokens();
		final String subUserToken = getAllAccessSubUserToken();
		final String username = "SUBUSER_ALLACCESS";
		final String newPassword = "testtesttest123!";

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id");
		queryParams.put("return-result", "true");

		final JSONObject data = new JSONObject();

		// old password not provided
		data.put("password", newPassword);

		Response response = post(SubUsersResource.PATH + "/me/change-password", Status.BAD_REQUEST, subUserToken, queryParams, data.toString());

		assertThat(getErrorResponse(response).getMessage())
				.isEqualTo("Validation of input failed.");
		assertLogin(username, newPassword, Status.UNAUTHORIZED);

		// wrong old password
		data.put("oldPassword", "test");
		data.put("password", newPassword);

		response = post(SubUsersResource.PATH + "/me/change-password", Status.BAD_REQUEST, subUserToken, queryParams, data.toString());

		assertThat(getErrorResponse(response).getMessage())
				.isEqualTo(getMessage("_INVALID_OLD_PASSWORD"));
		assertLogin(username, newPassword, Status.UNAUTHORIZED);

		// too short password
		data.put("oldPassword", "security");
		data.put("password", "test");

		response = post(SubUsersResource.PATH + "/me/change-password", Status.BAD_REQUEST, subUserToken, queryParams, data.toString());

		assertThat(getErrorResponse(response).getMessage())
				.isEqualTo(getError(ErrorCode.PasswordTooShort));
		assertLogin(username, newPassword, Status.UNAUTHORIZED);

		// without required special character
		data.put("oldPassword", "security");
		data.put("password", "testtesttest123");

		response = post(SubUsersResource.PATH + "/me/change-password", Status.BAD_REQUEST, subUserToken, queryParams, data.toString());

		assertThat(getErrorResponse(response).getMessage())
				.isEqualTo(String.format(getMessage("_MUST_CONTAIN_SPECIAL"), new PasswordPolicy().getEnforceSpecial()));
		assertLogin(username, newPassword, Status.UNAUTHORIZED);

		// valid password
		data.put("oldPassword", "security");
		data.put("password", newPassword);

		post(SubUsersResource.PATH + "/me/change-password", Status.OK, subUserToken, queryParams, data.toString());

		assertLogin(username, newPassword, Status.OK);

		// with non subuser should redirect
		data.put("oldPassword", "security");
		data.put("password", newPassword);

		response = post(SubUsersResource.PATH + "/me/change-password", Status.TEMPORARY_REDIRECT, MAINUSER_TOKEN, queryParams, data.toString());

		final String redirectPath = Paths.get(BASE_PATH, UsersResource.PATH, "me", "change-password").toString();
		assertThat(response.getLocation().getPath()).isEqualTo(redirectPath);
	}

	/**
	 * @param userName User name
	 * @param password password
	 * @param status HTTP status to assert when logging in with credentials
	 */
	private void assertLogin(final String userName, final String password, final Status status) {
		final Form form = new Form();
		form.param("username", userName);
		form.param("password", password);
		post(AuthResource.PATH + "/login", status, null, null, Entity.form(form));
	}

	@Test
	public void testDeleteSubUser() throws SQLException {
		setupDatabase();

		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");

		// Non-existing
		final Response response = delete(SubUsersResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));

		// Existing
		delete(SubUsersResource.PATH + "/" + subUserId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tsubusers WHERE xid = ?", subUserId)).isZero();
	}

	@Test
	public void testDeleteSubUserAsSubUser() throws SQLException {
		setupDatabase();
		setSubUserTokens();
		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");

		// Without required permission
		final Response response = delete(SubUsersResource.PATH + "/" + subUserId, Status.FORBIDDEN, getAllAccessSubUserToken());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_MISSING_PERMISSIONS"));

		// Add required permissions to the sub-user
		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.USERS_VIEW_AND_MANAGE.getId()});
		getConnection().commit();

		delete(SubUsersResource.PATH + "/" + subUserId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tsubusers WHERE xid = ?", subUserId)).isZero();
	}

	@Test
	public void testDeleteSubUserAsDifferentMainUser() throws SQLException {
		setupDatabase();

		final Long subUserId = DbObject.getLong(getConnection(), "SELECT xid FROM tsubusers WHERE vcusername = ?", "SUPERUSER");
		delete(SubUsersResource.PATH + "/" + subUserId, Status.NOT_FOUND, SWAT_USER_TOKEN);
	}
}
