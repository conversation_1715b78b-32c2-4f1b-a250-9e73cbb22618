package com.chilicoders.rest.resources;

import static com.chilicoders.xmlapi.XMLAPITestBase.getLocalServerPort;
import static com.chilicoders.xmlapi.XMLAPITestBase.startLocalServer;
import static com.chilicoders.xmlapi.XMLAPITestBase.stopLocalServer;
import static java.nio.charset.StandardCharsets.UTF_8;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.json;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.awaitility.Awaitility.await;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.net.MalformedURLException;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;

import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBException;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.entity.BasicHttpEntity;
import org.apache.http.impl.DefaultBHttpServerConnection;
import org.apache.http.message.BasicHttpResponse;
import org.assertj.core.util.Arrays;
import org.eclipse.persistence.exceptions.BeanValidationException;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.agents.impl.RequireDatabaseCommits;
import com.chilicoders.app.ScanApp;
import com.chilicoders.bl.ScanStatusBusiness;
import com.chilicoders.bl.ScannerBusiness;
import com.chilicoders.boris.DiscoveryThread;
import com.chilicoders.boris.OutscanThread;
import com.chilicoders.boris.ScanStatusThread;
import com.chilicoders.boris.objects.Scan;
import com.chilicoders.boris.objects.ScanService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.core.scanconfiguration.model.AgentScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudsecScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerDiscoveryScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkScanConfigurationTemplate;
import com.chilicoders.core.scandata.api.ScanSettingKeys;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.db.objects.ScanStatus;
import com.chilicoders.db.objects.User;
import com.chilicoders.event.model.Event;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.AccountType;
import com.chilicoders.model.AssetIdentifierInterface.DockerImageProperties;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.CloudDiscoveryAwsConfigurationTemplate;
import com.chilicoders.model.CloudDiscoveryAzureConfigurationTemplate;
import com.chilicoders.model.CloudDiscoveryType;
import com.chilicoders.model.CompliancePolicyType;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.IntegrationInterface.CyberArkIntegrationConfiguration;
import com.chilicoders.model.IntegrationInterface.DelineaIntegrationConfiguration;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.Source;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.exceptions.BaseRestException;
import com.chilicoders.rest.exceptions.CustomResponseStatus;
import com.chilicoders.rest.models.Account;
import com.chilicoders.rest.models.BaseDbObject;
import com.chilicoders.rest.models.CompliancePolicy;
import com.chilicoders.rest.models.Credential;
import com.chilicoders.rest.models.CredentialClassType;
import com.chilicoders.rest.models.Integration;
import com.chilicoders.rest.models.NetworkDiscoveryProtocol;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.ScanConfiguration;
import com.chilicoders.scan.StartScanService;
import com.chilicoders.scan.impl.StartScanRequest;
import com.chilicoders.service.ScanDataService;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;
import com.chilicoders.service.dao.ScannerDaoImpl;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.workflow.ScanQueue;
import com.chilicoders.xmlapi.XMLAPITestBase;
import com.chilicoders.xmlapi.XMLAPITestBase.LocalServerCallback;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;

public class ScanConfigurationsResourceTest extends BaseResourceTest {
	private static final String BASIC_AUTH = "basic-auth";

	private static final String NAME = "name";

	private static final String TEMPLATE = "template";

	private static final String CONFIGURATION = "configuration";

	private static final String CUSTOMER_ID = "customerId";

	private static final String SCANNER_ID = "scannerId";

	private static final String TIMEOUT = "timeout";

	private static final String SEEDS = "seeds";

	private static final String NSECONDS = "nseconds";

	private static final String FUZZING = "fuzzing";

	private static final String UPDATED_CONFIG_NAME = "UpdatedConfig";

	private static final String CLIENT_CERTIFICATE_AUTH = "client-certificate-auth";

	private static final String ABORT_PATTERN = "abort-pattern";

	private static final String MUST_MATCH = "must-match";

	private static final String CANNOT_MATCH = "cannot-match";

	private static final String ALLOWED_DOMAINS = "allowed-domains";

	private static final String ADDR_BLACKLIST = "addr-blacklist";

	private static final String HOST_MAP = "host-map";

	private static final String INFRASTRUCTURE_SCAN = "infrastructure-scan";

	private static final String ASSET_IDENTIFIER_IDS = "assetIdentifierIds";

	private static final String ASSET_IDS = "assetIds";

	private static final String ACCOUNT = "accountId";

	private static final String SPA_CRAWLING = "spa-crawling";

	private static final long THIRTEEN_HOURS_IN_SECONDS = Duration.ofHours(13).getSeconds();

	private static final long TWENTY_SIX_HOURS_IN_SECONDS = Duration.ofHours(26).getSeconds();

	private long assetIdentifierId;

	private long assetIdentifierId2;

	private long awsAssetIdentifierId;

	private long scanConfigurationScaleId;

	private long scanConfigurationCloudsecId;

	private long scheduleId;

	private long assetId;

	private long customPolicy1;

	private long customPolicy2;

	private long customPolicy3;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();

		DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tags (key, value, customerid, createdby, updatedby) VALUES (?, ?, ?, ?, ?) RETURNING id",
				"tag1", "val1", XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());
		DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tags (key, value, customerid, createdby, updatedby) VALUES (?, ?, ?, ?, ?) RETURNING id",
				"tag2", "val2", XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());

		setSubUserTokens();

		this.scanConfigurationScaleId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"TestConfig", ScanTemplate.SCALE, createScaleConfiguration().toString(), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		this.scanConfigurationCloudsecId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"TestConfig", ScanTemplate.CLOUDSEC, createScaleConfiguration().toString(), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		this.assetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"TestAssetIdentifier", AssetIdentifierType.IP, new Source[] {Source.SCALE}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(),
				getTestUserCustomerId());
		this.assetIdentifierId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"**************", AssetIdentifierType.IP, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(),
				getTestUserCustomerId());
		this.awsAssetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"a-clound-instance", AssetIdentifierType.AWS_INSTANCE_ID, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				new Date(), getTestUserCustomerId());
		this.scheduleId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO genericschedules (name, startsfrom, customerid, createdbyid, updatedbyid) VALUES(?, ?::timestamp with time zone, ?, ?, ?) RETURNING id",
				"Test", "2018-12-14T15:52:00.000Z", getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		this.assetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, updatedbyid, customerid) VALUES(?, ?::source[], ?, ?, ?) RETURNING id",
				"TestAsset", new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, getTestUserCustomerId());

		final String scanPolicyQuery = "INSERT INTO scanpolicies (name, settings, system, createdbyid, updatedbyid, customerid) VALUES (?, ?::JSONB, ?, ?, ?, ?) RETURNING id";
		this.customPolicy1 = DbObject.executeCountQuery(getConnection(), scanPolicyQuery,
				"Son Policy 1", "{\"safeOnly\":true, \"speed\" :\"Fast\", \"useCustomCredentials\":true }", false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID,
				getTestUserCustomerId());
		this.customPolicy2 = DbObject.executeCountQuery(getConnection(), scanPolicyQuery,
				"Son Policy 2", "{\"tryDefaultCredentials\":false, \"speed\":\"Very Fast\", \"useCustomCredentials" +
						"\":false }", false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());
		this.customPolicy3 = DbObject.executeCountQuery(getConnection(), scanPolicyQuery,
				"My Policy 3", "{\"trustedCasPem\":\"-----BEGIN CERTIFICATE-----\\nMIIEczCCA1ugAwIBAgIBADANBgkqhkiG9w0BAQQFAD\"," +
						"\"virtualHostsHostName\":true, \"useCustomCredentials\":true }",
				false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());

		setUserRole(getSuperUserId(), new Integer[] {UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE.getId()});
		setResourceGroup(getSuperUserId(), ResourceType.SCANCONFIGURATION, null);

		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfiguration_assetidentifier");
		DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfigurations");
		DbObject.executeUpdate(getConnection(), "DELETE FROM assetidentifiers");
		DbObject.executeUpdate(getConnection(), "DELETE FROM assets");
		DbObject.executeUpdate(getConnection(), "DELETE FROM genericschedules");
		DbObject.executeUpdate(getConnection(), "DELETE FROM tags");
		DbObject.executeUpdate(getConnection(), "DELETE FROM compliancepolicies");
		DbObject.executeUpdate(getConnection(), "DELETE FROM scanpolicies WHERE NOT system");
		DbObject.executeUpdate(getConnection(), "DELETE FROM accounts");
		DbObject.executeUpdate(getConnection(), "DELETE FROM credentials");
		DbObject.executeUpdate(getConnection(), "DELETE FROM integrations");
		DbObject.executeUpdate(getConnection(), "DELETE FROM scanlogs");
		DbObject.executeUpdate(getConnection(), "DELETE FROM tscanstatuss");
		getConnection().commit();
	}

	@Test
	public void testGetScanConfigurationHeader() {
		final String totalCount = head(ScanConfigurationsResource.PATH, new HashMap<>(), MAINUSER_TOKEN);
		assertEquals("2", totalCount);
	}

	@Test
	public void testGetScanConfigurationList() {
		final JSONArray jsonArray = getListFromResource(ScanConfigurationsResource.PATH, "id,name", MAINUSER_TOKEN);
		assertTrue(jsonArray.length() > 1);
	}

	@Test
	public void testGetScanConfigurationsLimitedResources() throws SQLException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_scanconfiguration (tagid, scanconfigurationid) VALUES (?, ?)", tagId, this.scanConfigurationScaleId);
		setUserRole(getAllAccessSubUserId(), new Integer[] {UserRolePermission.SCAN_CONFIGURATIONS_VIEW.getId()});
		setResourceGroup(getAllAccessSubUserId(), ResourceType.SCANCONFIGURATION, new Integer[] {(int) tagId});
		getConnection().commit();

		// List scanconfigs as subuser with limited access
		final Response response = get(ScanConfigurationsResource.PATH, Status.OK, getAllAccessSubUserToken());
		final JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		assertEquals(1, jsonArray.length());
	}

	@Test
	public void testGetScanConfigurations() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,name");

		// Get scan configuration that does not exist
		Response response = get(ScanConfigurationsResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get scan configuration
		response = get(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId, Status.OK, MAINUSER_TOKEN, queryParams);

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.scanConfigurationScaleId, jsonObject.getInt("id"));
	}

	@Test
	public void testPostScanConfigurationSuccessRequest() {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();

		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());

		assertTrue(getCreatedId(response) > 0);
		assertNull(response.readEntity(ScanConfiguration.class));
	}

	@Test
	public void testPostCloudDiscoveryScanConfigurationSuccessRequest() throws SQLException {
		final String updateNetsecFeatureQuery = "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?";
		DbObject.execute(getConnection(), updateNetsecFeatureQuery, getTestUserCustomerId());

		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, createCloudDiscoveryScanConfigurationData().toString());

		assertTrue(getCreatedId(response) > 0);
		assertNull(response.readEntity(ScanConfiguration.class));
	}

	@Test
	public void testPostAndStartCloudDiscoveryScanConfigurationWithAppsecScaleScanner() throws SQLException {
		final String updateNetsecFeatureQuery = "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?";
		DbObject.execute(getConnection(), updateNetsecFeatureQuery, getTestUserCustomerId());

		// Post Cloud discovery scan configuration with appsec scale scanner should return 403
		final long scannerId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tscanners(xid, xuserxid, name, approved, ipaddress, inactive, appsecscalescanner, isoutpost) VALUES (NEXTVAL('tscanners_seq'), ?, 'Scanner', 1, '127.0.0.1', 0, true, 1) RETURNING xid",
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();
		final JSONObject scanConfigurationJson = createCloudDiscoveryScanConfigurationData();
		scanConfigurationJson.put(SCANNER_ID, scannerId);
		Response response = post(ScanConfigurationsResource.PATH, Status.FORBIDDEN, MAINUSER_TOKEN, scanConfigurationJson.toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo(getMessage("_NOT_ALLOWED_SCANNER_APPSEC_HIAB"));

		// Post Cloud discovery scan configuration with NOT appsec scale scanner should return 201
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET appsecscalescanner = false WHERE xid = ?", scannerId);
		getConnection().commit();
		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
		final long configId = getCreatedId(response);
		assertThat(configId).isGreaterThan(0);
		assertThat(response.readEntity(ScanConfiguration.class)).isNull();

		// Patch Cloud discovery scan configuration with appsec scale scanner should return 403
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET appsecscalescanner = true WHERE xid = ?", scannerId);
		getConnection().commit();
		response = patch(ScanConfigurationsResource.PATH + "/" + configId, Status.FORBIDDEN, MAINUSER_TOKEN, scanConfigurationJson.toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo(getMessage("_NOT_ALLOWED_SCANNER_APPSEC_HIAB"));
	}

	@Test
	public void testPostNetworkDiscoveryIpv4ScanConfigurationSuccessRequest() throws SQLException {
		final String updateNetsecFeatureQuery = "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?";
		DbObject.execute(getConnection(), updateNetsecFeatureQuery, getTestUserCustomerId());

		final String targets = "**********\n*********-**********\n*********/24\n10.15.0.0,*********, *********,example.com, example2.com";
		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());

		assertTrue(getCreatedId(response) > 0);
		assertNull(response.readEntity(ScanConfiguration.class));
	}

	@Test
	public void testPostNetworkDiscoveryIpv4ScanConfigurationBadRequest() throws SQLException {
		final String updateNetsecFeatureQuery = "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?";
		DbObject.execute(getConnection(), updateNetsecFeatureQuery, getTestUserCustomerId());

		String targets = "********* -**********\n*********/24";
		Response response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");

		targets = "*********-**********\n*********/p24";
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");

		targets = "*********-**********,*********/24";
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");

		targets = "192.15.20.4, *********/24";
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");

		targets = "*********.45 - **********\\n*********/24";
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");

		targets = "*********.45 % comment";
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData(targets).toString());
		assertThat(getErrorResponse(response)).isNotNull().extracting("message").isNotNull().isEqualTo("Validation of input failed.");
	}

	@Test
	public void testPostScanConfigurationWithoutResourcePermissions() throws SQLException {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();

		setUserRole(getNoAccessSubUserId(), new Integer[] {UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE.getId()});
		getConnection().commit();

		final Response response = post(ScanConfigurationsResource.PATH, Status.FORBIDDEN, getNoAccessSubUserToken(), scanConfigurationJson.toString());
		assertEquals(getMessage("_NOT_ALLOWED_CREATE_RESOURCE"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPostScanConfigurationRequestWithScanner() throws SQLException {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		scanConfigurationJson.put(SCANNER_ID, ScannerBusiness.EXTERNAL_SCANNER_ID);

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET global = true WHERE xid = ?", ScannerBusiness.EXTERNAL_SCANNER_ID);
		getConnection().commit();

		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
		final long scanConfigurationId = getCreatedId(response);
		assertTrue(scanConfigurationId > 0);
		assertNull(response.readEntity(ScanConfiguration.class));

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET global = false WHERE xid = ?", ScannerBusiness.EXTERNAL_SCANNER_ID);
		getConnection().commit();

		final long scannerId = DbObject.getLong(getConnection(), "SELECT scannerid FROM scanconfigurations WHERE id = ?", scanConfigurationId);
		assertEquals(ScannerBusiness.EXTERNAL_SCANNER_ID, scannerId);
	}

	@Test
	public void testPostScanConfigurationTimeout() throws SQLException {
		final JSONObject networkScanConfiguration = createNetworkScanConfiguration();
		final JSONObject networkScanConfigurationData = createScanConfigurationData(networkScanConfiguration, ScanTemplate.NETWORK_SCAN);
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET global = true WHERE xid = ?", ScannerBusiness.EXTERNAL_SCANNER_ID);

		// Should fail if running from OUTSCAN
		networkScanConfiguration.put(TIMEOUT, THIRTEEN_HOURS_IN_SECONDS);
		networkScanConfigurationData.put(CONFIGURATION, networkScanConfiguration);
		networkScanConfigurationData.put(SCANNER_ID, ScannerBusiness.EXTERNAL_SCANNER_ID);
		getConnection().commit();
		Response response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, networkScanConfigurationData.toString());
		JSONObject jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.get("message")).isEqualTo(getMessage("_TIMELIMIT_OUT_OF_BOUNDS"));

		// Should not allow more than 24h
		networkScanConfiguration.put(TIMEOUT, TWENTY_SIX_HOURS_IN_SECONDS);
		networkScanConfigurationData.put(CONFIGURATION, networkScanConfiguration);
		response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, networkScanConfigurationData.toString());
		jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.get("message")).isEqualTo(getError(ErrorCode.InputValidationFailed));

		// Should be successful if running from HIAB
		networkScanConfiguration.put(TIMEOUT, THIRTEEN_HOURS_IN_SECONDS);
		networkScanConfigurationData.put(CONFIGURATION, networkScanConfiguration);
		final long scannerId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tscanners(xid, xuserxid, name, approved, ipaddress, inactive, appsecscalescanner, isoutpost) VALUES (NEXTVAL('tscanners_seq'), ?, 'Scanner', 1, '127.0.0.1', 0, true, 1) RETURNING xid",
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();
		networkScanConfigurationData.put(SCANNER_ID, scannerId);
		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, networkScanConfigurationData.toString());
		jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		final JSONObject configuration = (JSONObject) jsonResponse.get("configuration");
		assertThat(configuration).isNotNull();
		assertThat(THIRTEEN_HOURS_IN_SECONDS).isEqualTo(configuration.getLong("timeout"));

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET global = false WHERE xid = ?", ScannerBusiness.EXTERNAL_SCANNER_ID);
		getConnection().commit();
	}

	@Test
	public void testPostScanConfigurationRequestWithReturnResult() {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		scanConfigurationJson.put(NAME, UPDATED_CONFIG_NAME);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		final JSONObject configuration = new JSONObject(response.readEntity(String.class));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("createdById"));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("updatedById"));
	}

	@Test
	public void testPostScanConfigurationRequestWithInvalidAssetIdentifierIds() throws SQLException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		final long invalidAssetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"TestAssetIdentifier", AssetIdentifierType.IP, new Source[] {Source.SCALE}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(),
				getAdminUserCustomerId());

		final JSONObject scanConfigurationJson = createDockerScanConfigurationData(accountId);
		scanConfigurationJson.put(ASSET_IDENTIFIER_IDS, new Long[] {invalidAssetIdentifierId});

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		final Response response = post(ScanConfigurationsResource.PATH, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPostScanConfigurationRequestWithInvalidAssetIds() throws SQLException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		final long invalidAssetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, updatedbyid, customerid) VALUES(?, ?::source[], ?, ?, ?) RETURNING id",
				"TestAsset", new Source[] {Source.SCALE}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, getAdminUserCustomerId());

		final JSONObject scanConfigurationJson = createDockerScanConfigurationData(accountId);
		scanConfigurationJson.put(ASSET_IDS, new Long[] {invalidAssetId});

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		final Response response = post(ScanConfigurationsResource.PATH, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPostAndPatchScanConfigurationWithWorkflowIdShouldNotBePossible() throws SQLException {
		final long workflowId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO workflows (name, configurations, customerid, createdbyid) VALUES(?, ?::jsonb, ?, ?) RETURNING id",
				"Test1", "[]",
				getTestUserCustomerId(), BaseDbObject.SYSTEM_USER);

		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		scanConfigurationJson.put("workflowId", workflowId);
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
		final long id = getCreatedId(response);

		response = get(ScanConfigurationsResource.PATH + "/" + id, Status.OK, MAINUSER_TOKEN);
		JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		// workflowId should not be possible to set. Will be updated by workflow resource.
		assertEquals(jsonObject.has("workflowId"), false);

		// Test patch as well
		patch(ScanConfigurationsResource.PATH + "/" + id, Status.NO_CONTENT, MAINUSER_TOKEN, scanConfigurationJson.toString());

		response = get(ScanConfigurationsResource.PATH + "/" + id, Status.OK, MAINUSER_TOKEN);
		jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(jsonObject.has("workflowId"), false);
	}

	@Test
	public void testDeleteRequest() throws SQLException {
		// Delete scan configuration which does not exist
		Response response = delete(ScanConfigurationsResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
		final long id = getCreatedId(response);

		delete(ScanConfigurationsResource.PATH + "/" + id, Status.NO_CONTENT, getSuperUserToken());

		final ScanConfiguration deletedConfiguration = ScanConfiguration.getById(ScanConfiguration.class, getConnection(), com.chilicoders.db.Access.ADMIN, id);
		assertNotNull(deletedConfiguration.getDeleted());
		assertEquals(getSuperUserId(), -(long) deletedConfiguration.getUpdatedById());
	}

	@Test
	public void testDeleteScanConfigurationUsedByWorkflowShouldNotBePossible() throws SQLException {
		final long workflowId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO workflows (name, configurations, customerid, createdbyid) VALUES(?, ?::jsonb, ?, ?) RETURNING id",
				"Test1", "[]",
				getTestUserCustomerId(), BaseDbObject.SYSTEM_USER);

		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
		final long id = getCreatedId(response);

		DbObject.executeUpdate(getConnection(), "UPDATE scanconfigurations SET workflowid = ? WHERE id = ?", workflowId, id);
		getConnection().commit();

		response = delete(ScanConfigurationsResource.PATH + "/" + id, Status.FORBIDDEN, MAINUSER_TOKEN);
		assertEquals(getMessage("_SCAN_CONFIGURATION_IS_USED_BY_WORKFLOW"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testDeleteScanConfigurationBulk() throws SQLException {
		final String updateNetsecFeatureQuery = "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?";
		DbObject.execute(getConnection(), updateNetsecFeatureQuery, getTestUserCustomerId());

		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, createScanConfigurationData(createScaleConfiguration(), ScanTemplate.SCALE).toString());
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, createNetworkDiscoveryScanConfigurationData("*********").toString());
		final long discoveryId = getCreatedId(response);
		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, createNetworkScanConfigurationData().toString());
		final long scanId = getCreatedId(response);
		final Map<String, String> queryParams = new HashMap<>();

		// Try to delete a non-existing scan configuration
		queryParams.put("filter", createFilter("id", "0"));
		delete(ScanConfigurationsResource.PATH, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams);
		queryParams.remove("filter");

		// Delete scan configuration two and three
		queryParams.put("limit", "2");
		queryParams.put("offset", "1");
		queryParams.put("sort", "id");
		delete(ScanConfigurationsResource.PATH, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams);
		queryParams.clear();
		final JSONArray list = getListFromResource(ScanConfigurationsResource.PATH, queryParams, MAINUSER_TOKEN);
		assertThatJson(list).isArray().hasSize(3).first().node("id").isEqualTo((int) this.scanConfigurationScaleId);

		// Delete scan configurationUsed that is used by workflow, should fail
		final JSONObject configuration = new JSONObject();
		configuration.put("configurations", new JSONArray()
				.put(new JSONObject().put("id", discoveryId).put("type", "SCAN_CONFIGURATION"))
				.put(new JSONObject().put("id", scanId).put("type", "SCAN_CONFIGURATION")));
		configuration.put("name", "Test1");
		configuration.put("scannerId", 0);

		post(WorkflowsResource.PATH, Status.CREATED, MAINUSER_TOKEN, configuration.toString());

		queryParams.put("filter", createFilter("id", scanId));
		response = delete(ScanConfigurationsResource.PATH, Status.FORBIDDEN, MAINUSER_TOKEN, queryParams);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_IS_USED_BY_WORKFLOW"));
	}

	@Test
	public void testPatchScanConfigurationRequestWithInvalidAssetIdentifierIds() throws SQLException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		final JSONObject scanConfigurationJson = createDockerScanConfigurationData(accountId);
		scanConfigurationJson.put(ASSET_IDENTIFIER_IDS, new Long[] {this.assetIdentifierId});

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		final JSONObject configuration = new JSONObject(response.readEntity(String.class));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("createdById"));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("updatedById"));
		assertTrue(configuration.getJSONArray(ASSET_IDENTIFIER_IDS).length() > 0);

		assertEquals(1,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ? AND assetidentifierid = ?",
						configuration.getInt("id"), this.assetIdentifierId));

		final long invalidAssetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"TestAssetIdentifier", AssetIdentifierType.IP, new Source[] {Source.SCALE}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(), new Date(),
				getAdminUserCustomerId());
		scanConfigurationJson.put(ASSET_IDENTIFIER_IDS, new Long[] {invalidAssetIdentifierId});
		response = patch(ScanConfigurationsResource.PATH + "/" + configuration.getInt("id"), Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPatchScanConfigurationRequestWithInvalidAssetIds() throws SQLException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		final JSONObject scanConfigurationJson = createDockerScanConfigurationData(accountId);
		scanConfigurationJson.put(ASSET_IDS, new Long[] {this.assetId});

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		final JSONObject configuration = new JSONObject(response.readEntity(String.class));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("createdById"));
		assertEquals(XMLAPITestBase.TEST_USER_ID, configuration.getInt("updatedById"));
		assertTrue(configuration.getJSONArray(ASSET_IDS).length() > 0);

		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM asset_scanconfiguration WHERE scanconfigurationid = ? AND assetid = ?",
				configuration.getInt("id"), this.assetId));

		final long invalidAssetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, updatedbyid, customerid) VALUES(?, ?::source[], ?, ?, ?) RETURNING id",
				"TestAsset", new Source[] {Source.SCALE}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, getAdminUserCustomerId());
		scanConfigurationJson.put(ASSET_IDENTIFIER_IDS, new Long[] {invalidAssetId});

		response = patch(ScanConfigurationsResource.PATH + "/" + configuration.getInt("id"), Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());

		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPatchRequestForNonExistingConfiguration() {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		final Map<String, String> queryParams = new HashMap<>();

		final Response response = patch(ScanConfigurationsResource.PATH + "/0", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testPatchRequestWithResult() {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		scanConfigurationJson.put(NAME, UPDATED_CONFIG_NAME);

		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final Response response =
				patch(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId, Status.OK, getSuperUserToken(), queryParams, scanConfigurationJson.toString());

		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(UPDATED_CONFIG_NAME, jsonObject.getString(NAME));
		assertEquals(getSuperUserId(), -jsonObject.getLong("updatedById"));
	}

	@Test
	public void testPatchRequestWithNoResult() {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		final Map<String, String> queryParams = new HashMap<>();

		patch(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());
	}

	@Test
	public void testPatchScanConfigurationBulk() throws SQLException {
		final JSONObject scanConfigurationJson = createScaleScanConfigurationData();
		scanConfigurationJson.put(NAME, UPDATED_CONFIG_NAME);
		final Map<String, String> queryParams = new HashMap<>();

		// Try to patch a non-existent scan configuration
		queryParams.put("filter", createFilter("id", "0"));
		patch(ScanConfigurationsResource.PATH, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());
		queryParams.remove("filter");

		// Patch the first three scan configurations and return result
		queryParams.put("return-result", "true");
		queryParams.put("sort", "id");
		queryParams.put("limit", "3");
		Response response = patch(ScanConfigurationsResource.PATH, Status.OK, getSuperUserToken(), queryParams, scanConfigurationJson.toString());
		final JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		for (int i = 0; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThat(jsonObject.getString(NAME)).isEqualTo(UPDATED_CONFIG_NAME);
			assertThat(-jsonObject.getLong("updatedById")).isEqualTo(getSuperUserId());
		}
		queryParams.clear();

		// Patch one asset with no result
		queryParams.put("return-result", "false");
		queryParams.put("filter", createFilter("id", (int) this.scanConfigurationScaleId));
		patch(ScanConfigurationsResource.PATH, Status.NO_CONTENT, getSuperUserToken(), queryParams, scanConfigurationJson.toString());
		queryParams.clear();

		// Patch with invalid asset identifier id
		queryParams.put("return-result", "true");
		final JSONObject assetIdentifierConfig = new JSONObject();
		assetIdentifierConfig.put("name", "TestAssetIdentifier");
		assetIdentifierConfig.put("type", AssetIdentifierType.IP);
		assetIdentifierConfig.put("source", new Source[] {Source.SCALE});
		assetIdentifierConfig.put("createdbyid", XMLAPITestBase.ADMIN_USER_ID);
		assetIdentifierConfig.put("updatedbyid", XMLAPITestBase.ADMIN_USER_ID);

		queryParams.put("return-result", "true");
		response = post(AssetIdentifiersResource.PATH, Status.CREATED, ADMIN_TOKEN, queryParams, assetIdentifierConfig.toString());
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		final long invalidAssetIdentifierId = jsonObject.getLong("id");
		scanConfigurationJson.put(ASSET_IDENTIFIER_IDS, new Long[] {invalidAssetIdentifierId});
		queryParams.put("filter", createFilter("id", invalidAssetIdentifierId));
		response = patch(ScanConfigurationsResource.PATH, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, scanConfigurationJson.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_RESOURCE_NOT_FOUND"));
		queryParams.clear();
		scanConfigurationJson.remove(ASSET_IDENTIFIER_IDS);
	}

	@Test
	public void testLinkCloudsecConfigWithAssetIdentifierRequest() {
		// Link configuration that does not exist
		Response response = put(ScanConfigurationsResource.PATH + "/0/asset-identifiers/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Success case
		response =
				put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/asset-identifiers/" + this.assetIdentifierId, Status.CREATED, MAINUSER_TOKEN,
						"");
		assertEquals(this.assetIdentifierId, getCreatedId(response));
	}

	@Test
	public void testLinkScaleConfigWithAssetIdentifierRequest() throws SQLException {
		// Link configuration that does not exist
		Response response = put(ScanConfigurationsResource.PATH + "/0/asset-identifiers/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Success case
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/asset-identifiers/" + this.assetIdentifierId, Status.CREATED, MAINUSER_TOKEN,
				"");
		assertEquals(this.assetIdentifierId, getCreatedId(response)); // this is only to verify that created path contains asset identifier id.
		assertEquals(1,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ? AND assetidentifierid = ?",
						this.scanConfigurationScaleId, this.assetIdentifierId));

		// Fails if try to link one scale config with multiple services
		response =
				put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/asset-identifiers/" + this.assetIdentifierId, Status.BAD_REQUEST, MAINUSER_TOKEN,
						"");
		assertEquals(getMessage("_SCALE_CONFIG_ALREADY_LINKED"), getErrorResponse(response).getMessage());

		// Remove link with missing config
		response = delete(ScanConfigurationsResource.PATH + "/0/asset-identifiers/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link with missing schedule
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/asset-identifiers/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/asset-identifiers/" + this.assetIdentifierId, Status.NO_CONTENT,
				MAINUSER_TOKEN);
		assertEquals(0,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ? AND assetidentifierid = ?",
						this.scanConfigurationScaleId, this.assetIdentifierId));
	}

	@Test
	public void testLinkScaleConfigWithAssetRequest() throws SQLException {
		// Link configuration that does not exist
		Response response = put(ScanConfigurationsResource.PATH + "/0/assets/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Success case
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/assets/" + this.assetId, Status.CREATED, MAINUSER_TOKEN, "");
		assertEquals(this.assetId, getCreatedId(response)); // this is only to verify that created path contains asset identifier id.
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM asset_scanconfiguration WHERE scanconfigurationid = ? AND assetid = ?",
				this.scanConfigurationScaleId, this.assetId));

		// Fails if try to link one scale config with multiple services
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/assets/" + this.assetId, Status.BAD_REQUEST, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_SCALE_CONFIG_ALREADY_LINKED"), getErrorResponse(response).getMessage());

		// Remove link with missing config
		response = delete(ScanConfigurationsResource.PATH + "/0/assets/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link with missing schedule
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/assets/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/assets/" + this.assetId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM asset_scanconfiguration WHERE scanconfigurationid = ? AND assetid = ?",
				this.scanConfigurationScaleId, this.assetId));
	}

	@Test
	public void testScaleScanConfigurationValidationFailures() {
		final JSONObject configuration = createScaleConfiguration();
		configuration.put(NSECONDS, 599);
		configuration.put(SEEDS, "http://aoeu");
		configuration.put(CLIENT_CERTIFICATE_AUTH, new JSONObject());
		configuration.put(ABORT_PATTERN, "aoe)?");
		configuration.put(MUST_MATCH, new JSONArray("[{method:\"aoe)?\"}]"));
		configuration.put(CANNOT_MATCH, new JSONArray("[{method:\"aoe)?\"}]"));
		configuration.put(ALLOWED_DOMAINS, new JSONArray("[{domain:\"test\"}]"));
		configuration.put(ADDR_BLACKLIST, new JSONArray("[\"10.0.0.0/24\", \"aoeu\", \"192.168.1.300/30\"]"));
		configuration.put(BASIC_AUTH, new JSONObject("{url-prefix:\"test\"}"));
		configuration.put(HOST_MAP, new JSONArray("[{to:[\"1.2.3.500\"]}]"));
		final JSONObject scanConfigurationJson = createScanConfigurationData(configuration, ScanTemplate.SCALE);

		final Response response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, scanConfigurationJson.toString());

		final List<String> details = getErrorResponse(response).getDetails();
		final Supplier<Stream<String>> errors = details::stream;
		assertTrue(errors.get().anyMatch(str -> str.contains(NSECONDS)));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("clientcertificate")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("privatekey")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("domain")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("user")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("from")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("to")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("clientcertificate")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("urlprefix")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("method")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("domain")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("to")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains(ABORT_PATTERN.replaceAll("-", "").toLowerCase())));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains(ADDR_BLACKLIST.replaceAll("-", "").toLowerCase())));
	}

	@Test
	public void testScaleScanConfigurationEmptyFieldsValidation() {
		final JSONObject configuration = createScaleConfiguration();
		configuration.put(SEEDS, "");
		configuration.put(CLIENT_CERTIFICATE_AUTH, new JSONObject("{client-certificate:\"\", private-key:\"\"}"));
		configuration.put(ALLOWED_DOMAINS, new JSONArray("[{domain:\"\"}]"));
		configuration.put(BASIC_AUTH, new JSONObject("{user:\"\", password:\"\", url-prefix:\"\"}"));
		configuration.put(HOST_MAP, new JSONArray("[{from:\"\", to:[\"\"]}]"));
		final JSONObject scanConfigurationJson = createScanConfigurationData(configuration, ScanTemplate.SCALE);

		final Response response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, scanConfigurationJson.toString());

		final List<String> details = getErrorResponse(response).getDetails();
		final Supplier<Stream<String>> errors = details::stream;
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("clientcertificate")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("privatekey")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("domain")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("user")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("from")));
		assertTrue(errors.get().anyMatch(str -> str.toLowerCase().contains("to")));
	}

	@Test
	public void testScheduleLink() throws SQLException {
		// Add link with missing config
		Response response = put(ScanConfigurationsResource.PATH + "/0/schedules/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link with missing schedule
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/schedules/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove not linked schedule
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/schedules/" + this.scheduleId, Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/schedules/" + this.scheduleId, Status.CREATED, MAINUSER_TOKEN, "");
		assertEquals(this.scheduleId, getCreatedId(response));
		assertEquals(1,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_genericschedule WHERE scanconfigurationid = ? AND genericscheduleid = ?",
						this.scanConfigurationScaleId, this.scheduleId));

		// Remove link with missing config
		response = delete(ScanConfigurationsResource.PATH + "/0/schedules/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link with missing schedule
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/schedules/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/schedules/" + this.scheduleId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertEquals(0,
				DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_genericschedule WHERE scanconfigurationid = ? AND genericscheduleid = ?",
						this.scanConfigurationScaleId, this.scheduleId));
	}

	@Test
	public void testTagLink() throws SQLException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		getConnection().commit();

		// Add link with missing config
		Response response = put(ScanConfigurationsResource.PATH + "/0/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link with missing tag
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN, "");
		assertEquals(getMessage("_TAG_ID_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove not linked tag
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags/" + tagId, Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Add link
		response = put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags/" + tagId, Status.CREATED, MAINUSER_TOKEN, "");
		assertEquals(tagId, getCreatedId(response));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_scanconfiguration WHERE tagid = ? AND scanconfigurationid = ?", tagId,
				this.scanConfigurationCloudsecId));

		// Remove link with missing config
		response = delete(ScanConfigurationsResource.PATH + "/0/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link with missing tag
		response = delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags/0", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_TAG_ID_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Remove link
		delete(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags/" + tagId, Status.NO_CONTENT, MAINUSER_TOKEN);
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_scanconfiguration WHERE tagid = ? AND scanconfigurationid = ?", tagId,
				this.scanConfigurationCloudsecId));

		// Edit set of linked tags
		final Integer[] firstTagSet = createSetOfTags(3, getTestUserCustomerId(), "first");
		JSONArray payload = new JSONArray(firstTagSet);
		put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, payload.toString());
		assertEquals(3, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_scanconfiguration WHERE scanconfigurationid = ? AND tagid = ANY(?)",
				this.scanConfigurationCloudsecId, firstTagSet));

		final Integer[] secondTagSet = createSetOfTags(3, getTestUserCustomerId(), "second");
		payload = new JSONArray(secondTagSet);
		put(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationCloudsecId + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, payload.toString());
		assertEquals(3, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_scanconfiguration WHERE scanconfigurationid = ? AND tagid = ANY(?)",
				this.scanConfigurationCloudsecId, secondTagSet));
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tag_scanconfiguration WHERE scanconfigurationid = ? AND tagid = ANY(?)",
				this.scanConfigurationCloudsecId, firstTagSet));
	}

	@Test
	public void testModifyLinkedSetTagsBulk() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		final JSONObject scanConfiguration = createScanConfigurationData(createScaleConfiguration(), ScanTemplate.SCALE);
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		// Create scan-configurations tags
		final JSONObject scanConfigurationTag = new JSONObject()
				.put("key", "TestKey1")
				.put("value", "TestValue1");
		Response response = post(TagsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationTag.toString());
		JSONObject createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey1")
				.containsEntry("value", "TestValue1");
		final long tagId1 = createdTag.getLong("id");
		scanConfigurationTag
				.put("key", "TestKey2")
				.put("value", "TestValue2");
		response = post(TagsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationTag.toString());
		createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey2")
				.containsEntry("value", "TestValue2");
		final long tagId2 = createdTag.getLong("id");
		final JSONObject expectedScanConfigurationTag1 = new JSONObject(scanConfigurationTag.toString())
				.put("id", tagId1)
				.put("inherited", false)
				.put("value", "TestValue1")
				.put("key", "TestKey1");
		final JSONObject expectedScanConfigurationTag2 = new JSONObject(scanConfigurationTag.toString())
				.put("id", tagId2)
				.put("inherited", false)
				.put("value", "TestValue2")
				.put("key", "TestKey2");

		// Try to add tags to a scan-configuration that doesn't exist
		queryParams.put("filter", createFilter("id", "0"));
		put(ScanConfigurationsResource.PATH + "/tags", Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, "[" + tagId1 + "," + tagId2 + "]");
		queryParams.remove("filter");

		// Add both tags to all of the scan-configurations
		queryParams.put("return-result", "true");
		put(ScanConfigurationsResource.PATH + "/tags", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, "[" + tagId1 + "," + tagId2 + "]");
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, queryParams);
		JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		for (int i = 0; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThatJson(jsonObject).node("tags")
					.isArray()
					.containsExactlyInAnyOrder(json(expectedScanConfigurationTag1.toString()), json(expectedScanConfigurationTag2.toString()));
		}

		// Remove the second tag for the last two scan-configurations
		queryParams.put("offset", "3");
		put(ScanConfigurationsResource.PATH + "/tags/", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, "[" + tagId1 + "]");
		queryParams.remove("offset");
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, queryParams);
		jsonArray = new JSONArray(response.readEntity(String.class));

		for (int i = 3; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThatJson(jsonObject).node("tags")
					.isArray()
					.doesNotContain(json(expectedScanConfigurationTag2.toString()))
					.containsExactlyInAnyOrder(json(expectedScanConfigurationTag1.toString()));
		}

		// Remove both tags for the three first scan-configurations
		queryParams.clear();
		queryParams.put("limit", "3");
		queryParams.put("sort", "id");
		put(ScanConfigurationsResource.PATH + "/tags/", Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, "[]");
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, null);
		jsonArray = new JSONArray(response.readEntity(String.class));
		assertThat(jsonArray).hasSize(5);
		for (int i = 0; i < 3; i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThat(jsonObject.get("id")).isEqualTo(i + (int) this.scanConfigurationScaleId);
			assertThatJson(jsonObject).node("tags")
					.isAbsent();
		}

	}

	@Test
	public void testAddAndDeleteTagsBulk() {
		final Map<String, String> queryParams = new HashMap<>();
		final JSONObject scanConfiguration = createScanConfigurationData(createScaleConfiguration(), ScanTemplate.SCALE);
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		queryParams.put("return-result", "true");
		final JSONObject scanConfigurationTag = new JSONObject()
				.put("key", "TestKey1")
				.put("value", "TestValue1");
		Response response = post(TagsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationTag.toString());
		final JSONObject createdTag = new JSONObject(response.readEntity(String.class));
		assertThatJson(createdTag)
				.isObject()
				.containsEntry("key", "TestKey1")
				.containsEntry("value", "TestValue1");
		final long tagId1 = createdTag.getLong("id");

		final JSONObject expectedScanConfigurationTag1 = new JSONObject(scanConfigurationTag.toString())
				.put("id", tagId1)
				.put("inherited", false)
				.put("value", "TestValue1")
				.put("key", "TestKey1");

		// Try to add tags to an scan-configuration that doesn't exist
		queryParams.put("filter", createFilter("id", "0"));
		put(ScanConfigurationsResource.PATH + "/tags/" + tagId1, Status.NOT_FOUND, MAINUSER_TOKEN, queryParams, "");
		queryParams.remove("filter");

		// Add tag to all of the scan-configurations
		queryParams.put("return-result", "true");
		put(ScanConfigurationsResource.PATH + "/tags/" + tagId1, Status.CREATED, MAINUSER_TOKEN, queryParams, "");
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, queryParams);
		JSONArray jsonArray = new JSONArray(response.readEntity(String.class));
		for (int i = 0; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThatJson(jsonObject).node("tags")
					.isArray()
					.containsExactlyInAnyOrder(json(expectedScanConfigurationTag1.toString()));
		}

		// Remove the tag for the last two scan-configurations
		queryParams.put("offset", "3");
		delete(ScanConfigurationsResource.PATH + "/tags/" + tagId1, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams);
		queryParams.remove("offset");
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, queryParams);
		jsonArray = new JSONArray(response.readEntity(String.class));
		for (int i = 3; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThatJson(jsonObject).node("tags")
					.isAbsent();
		}

		queryParams.clear();
		queryParams.put("limit", "3");
		queryParams.put("sort", "id");
		delete(ScanConfigurationsResource.PATH + "/tags/" + tagId1, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams);
		response = get(ScanConfigurationsResource.PATH, Status.OK, MAINUSER_TOKEN, null);
		jsonArray = new JSONArray(response.readEntity(String.class));
		assertThat(jsonArray).hasSize(5);
		for (int i = 0; i < jsonArray.length(); i++) {
			final JSONObject jsonObject = (JSONObject) jsonArray.get(i);
			assertThat(jsonObject.get("id")).isEqualTo(i + (int) this.scanConfigurationScaleId);
			assertThatJson(jsonObject).node("tags")
					.isAbsent();
		}

	}

	@Test
	public void testScaleScan() throws SQLException {
		// Start scan for missing scan configuration
		Response response = post(ScanConfigurationsResource.PATH + "/0/scan", Status.NOT_FOUND, MAINUSER_TOKEN);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Start scan
		response = post(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", this.scanConfigurationScaleId));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE vcservice = ? AND xsoxid = ?",
				ScanServiceType.AppsecScale.toString(), this.scanConfigurationScaleId));

		// Start disabled scan configuration
		DbObject.executeUpdate(getConnection(), "UPDATE scanconfigurations SET enabled = false WHERE id = ?", this.scanConfigurationScaleId);
		getConnection().commit();
		response = post(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/scan", Status.FORBIDDEN, MAINUSER_TOKEN);
		assertEquals(getMessage("_CONFIGURATION_DISABLED"), getErrorResponse(response).getMessage());
	}

	@Test
	public void testCloudsecScan() throws SQLException, JAXBException {
		// Load compliance policies
		Configuration.setProperty(ConfigurationKey.compliance_path, XMLAPITestBase.testPath + "compliance/cloudsec");
		CompliancePolicy.loadPolicies();
		final long policyId = DbObject.getLong(getConnection(), "SELECT id FROM compliancepolicies WHERE type = ?", CompliancePolicyType.AWS);

		// Create account
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("AWS");
		account.setType(AccountType.AWS);
		final Integer accountId = (int) account.save(getConnection());

		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setClassId(CredentialClassType.ROLE_ARN.getId());
		credential.setValue("arn");
		credential.save(getConnection());

		// Create configuration
		final CloudsecScanConfigurationTemplate configuration = new CloudsecScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.CLOUDSEC);
		configuration.setAccountId(accountId);
		configuration.setPolicyId((int) policyId);
		configuration.setRegions(StringUtils.join(new String[] {"eu-west-1", "eu-east-1", "us-west-1", "us-east-1"}, ","));

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Test", ScanTemplate.CLOUDSEC, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(4, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(4, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testAwsRoleArnNotAllowedToBeTransmittedFromOutscanToHiabScanner() throws SQLException, IOException {
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.aws_access_key_allow_remote, false, false);
		DbObject.execute(getConnection(), "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?", getTestUserCustomerId());
		final long scannerId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tscanners(xid, xuserxid, name, approved, ipaddress, inactive) VALUES (NEXTVAL('tscanners_seq'), ?, 'Scanner', 1, '***********', 0) RETURNING xid",
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		final JSONObject scanConfigurationJson = createCloudDiscoveryScanConfigurationData();
		scanConfigurationJson.put(SCANNER_ID, scannerId);

		final Response scanConfigurationOnOutscanResponse = post(ScanConfigurationsResource.PATH, Status.FORBIDDEN, MAINUSER_TOKEN, scanConfigurationJson.toString());
		assertThat(getErrorResponse(scanConfigurationOnOutscanResponse).getMessage()).isEqualTo(getMessage("_NOT_ALLOWED_ROLE_ARN_TO_BE_TRANSMITTED_TO_HIAB_SCANNERS"));

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET isoutpost = 1 WHERE xid = ?", scannerId);
		getConnection().commit();

		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
	}

	@Test
	public void testAwsRoleArnNotAllowedToBeTransmittedFromHiabToOutscanScanner() throws SQLException, IOException {
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.aws_access_key_allow_remote, false, false);
		DbObject.execute(getConnection(), "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?", getTestUserCustomerId());
		final long scannerId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tscanners(xid, xuserxid, name, approved, ipaddress, inactive, isoutpost) VALUES (NEXTVAL('tscanners_seq'), ?, 'Scanner', 1, '***********', 0, 1) RETURNING xid",
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		final JSONObject scanConfigurationJson = createCloudDiscoveryScanConfigurationData();
		scanConfigurationJson.put(SCANNER_ID, scannerId);

		XMLAPITestBase.setHiab(true);
		final String token = AuthResource.createToken(XMLAPITestBase.TEST_USER_ID, null, null, false, null);
		final Response scanConfigurationOnHiabResponse = post(ScanConfigurationsResource.PATH, Status.FORBIDDEN, token, scanConfigurationJson.toString());
		assertThat(getErrorResponse(scanConfigurationOnHiabResponse).getMessage()).isEqualTo(getMessage("_NOT_ALLOWED_ROLE_ARN_TO_BE_TRANSMITTED_TO_OUTSCAN_SCANNERS"));

		DbObject.executeUpdate(getConnection(), "UPDATE tscanners SET isoutpost = 1 WHERE xid = ?", scannerId);
		getConnection().commit();

		post(ScanConfigurationsResource.PATH, Status.FORBIDDEN, token, scanConfigurationJson.toString());
	}

	@Test
	public void testScanConfigurationWithAwsRoleArnAndLocalScannerCreatedSuccessfullyOnOutscan() throws SQLException {
		Configuration.setProperty(ConfigKeys.ConfigurationBooleanKey.aws_access_key_allow_remote, false, false);
		DbObject.execute(getConnection(), "UPDATE customers SET features = ARRAY['netsec'] WHERE id = ?", getTestUserCustomerId());
		getConnection().commit();

		final JSONObject scanConfigurationJson = createCloudDiscoveryScanConfigurationData();
		scanConfigurationJson.put(SCANNER_ID, Scanner.LOCAL_SCANNER);

		post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationJson.toString());
	}

	@Test
	public void testDockerScan() throws SQLException, JAXBException, IOException {
		// Create account
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		// Create configuration
		final DockerScanConfigurationTemplate configuration = new DockerScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.DOCKER_SCAN);

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Docker", ScanTemplate.DOCKER_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		XMLAPITestBase.setHiab(true);
		final String token;
		if (Configuration.isKubernetesEnabled()) {
			token = XMLAPITestBase.createToken(XMLAPITestBase.TEST_USER_ID, ServiceIdentity.Service.HIAB_SCHEDULER);
		}
		else {
			XMLAPITestBase.createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);
			token = AuthResource.createToken(XMLAPITestBase.TEST_USER_ID, null, null, false, null);
		}

		// Missing linked asset
		Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.BAD_REQUEST, token);
		assertEquals(getMessage("_NEED_TO_LINK_ASSET_TO_SCAN"), getErrorResponse(response).getMessage());

		// Create asset & link to scan configuration
		long assetId = DbObject.executeCountQuery(getConnection(), "INSERT INTO assets (name, customerid, source, createdbyid, updatedbyid) "
						+ "VALUES(?, ?, ?::source[], ?, ?) RETURNING id",
				"Docker", getTestUserCustomerId(), new Source[] {Source.CLOUDSEC}, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_scanconfiguration (scanconfigurationid, assetid) VALUES(?, ?)", configurationId, assetId);
		getConnection().commit();

		// Asset composition invalid
		response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.BAD_REQUEST, token);
		assertEquals(getMessage("_ASSET_COMPOSITION_INVALID"), getErrorResponse(response).getMessage());

		// Create asset identifier properties
		final DockerImageProperties properties = new DockerImageProperties();
		properties.setArchitecture("amd64");
		properties.setTag("latest");
		properties.setSize(123L);
		properties.setOs("linux");
		properties.setType(AssetIdentifierType.DOCKER_IMAGE);

		// Create asset identifier & link to asset
		final long assetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid, properties) "
						+ "VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?, ?::jsonb) RETURNING id",
				"Docker", AssetIdentifierType.DOCKER_IMAGE, new Source[] {Source.CLOUDSEC}, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, new Date(), new Date(),
				getTestUserCustomerId(), MarshallingUtils.marshal(properties));

		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO scanconfiguration_assetidentifier (scanconfigurationid, assetidentifierid) VALUES(?, ?)", configurationId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO assetidentifier_account (assetidentifierid, accountid) VALUES (?, ?)", assetIdentifierId, accountId);
		getConnection().commit();

		// Missing credential
		response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.INTERNAL_SERVER_ERROR, token);
		assertEquals(getMessage("_COULDNT_QUEUE_SCAN"), getErrorResponse(response).getMessage());

		// Create credentials
		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setValue("*****");
		credential.setClassId(CredentialClassType.DOCKER_REGISTRY.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.USERNAME.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.PASSWORD.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.FILE.getId());
		credential.save(getConnection());
		getConnection().commit();

		// Start scan with asset identifier
		response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, token);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE vcservice = ? AND xsoxid = ?",
				ScanServiceType.DOCKER_SCAN.toString(), configurationId));

		// Start scan with asset
		final long assetConfigurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Docker", ScanTemplate.DOCKER_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		assetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, updatedbyid, customerid) VALUES(?, ?::source[], ?, ?, ?) RETURNING id",
				"Docker Asset", new Source[] {Source.CLOUDSEC}, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, ?, ?)", assetId,
				assetIdentifierId, new Date(), new Date());
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_scanconfiguration (scanconfigurationid, assetid) VALUES(?, ?)", assetConfigurationId, assetId);

		response = post(ScanConfigurationsResource.PATH + "/" + assetConfigurationId + "/scan", Status.OK, token);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ? AND authentication IS NOT NULL", assetConfigurationId));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE vcservice = ? AND xsoxid = ?",
				ScanServiceType.DOCKER_SCAN.toString(), assetConfigurationId));
	}

	@Test
	public void testDockerDiscoveryScan() throws SQLException, JAXBException, IOException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			// TODO: Test should be enabled or removed
			return;
		}
		// Create account
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Docker");
		account.setType(AccountType.DOCKER);
		final Integer accountId = (int) account.save(getConnection());

		// Create configuration
		final DockerDiscoveryScanConfigurationTemplate configuration = new DockerDiscoveryScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.DOCKER_DISCOVERY);
		configuration.setAccountId(accountId);

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Docker", ScanTemplate.DOCKER_DISCOVERY, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Try to start on outscan with local scanner
		Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.FORBIDDEN, MAINUSER_TOKEN);
		assertEquals(getMessage("_NOT_ALLOWED_SCAN_" + getLocalScanner()), getErrorResponse(response).getMessage());

		XMLAPITestBase.createClientCertificateChain(ServiceIdentity.Service.HIAB_SCHEDULER);
		XMLAPITestBase.setHiab(true);
		final String token = AuthResource.createToken(XMLAPITestBase.TEST_USER_ID, null, null, false, null);

		// Create credentials
		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setValue("*****");
		credential.setClassId(CredentialClassType.DOCKER_REGISTRY.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.USERNAME.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.PASSWORD.getId());
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.FILE.getId());
		credential.save(getConnection());
		getConnection().commit();

		// Start scan
		response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, token);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE vcservice = ? AND xsoxid = ?",
				ScanServiceType.DOCKER_DISCOVERY.toString(), configurationId));
	}

	@NotNull
	private static String getLocalScanner() {
		return "LOCAL_SCANNER";
	}

	@Test
	public void testAgentScan() throws SQLException, JAXBException {
		final long tagId = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id",
				"Test", "Test", getTestUserCustomerId());

		final AgentScanConfigurationTemplate configuration = new AgentScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.AGENT_SCAN);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final JSONObject scanConfiguration = createScanConfigurationData(new JSONObject(MarshallingUtils.marshal(configuration)), ScanTemplate.AGENT_SCAN);

		// Create scan configuration
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		final long scanConfigurationId = getCreatedId(response);

		// Start scan returns unprocessable content
		response = post(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/scan", CustomResponseStatus.UNPROCESSABLE_CONTENT, MAINUSER_TOKEN);
		assertEquals(getMessage("_CANT_START_AGENT_SCAN"), getErrorResponse(response).getMessage());

		// Link to schedule (not allowed)
		put(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/schedules/0", Status.FORBIDDEN, MAINUSER_TOKEN, "");

		// Delete scan configuration
		delete(ScanConfigurationsResource.PATH + "/" + scanConfigurationId, Status.NO_CONTENT, MAINUSER_TOKEN);
		final ScanConfiguration deletedConfiguration = ScanConfiguration.getById(ScanConfiguration.class, getConnection(), Access.ADMIN, scanConfigurationId);
		assertNotNull(deletedConfiguration.getDeleted());
	}

	@Test
	public void testNetworkDiscoveryScanPortsValidation() throws JAXBException {
		final NetworkDiscoveryConfigurationTemplate configuration = new NetworkDiscoveryConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_DISCOVERY);
		configuration.setTimeout(600);
		configuration.setTargetList("80.254.228.51");
		configuration.setProtocols(new NetworkDiscoveryProtocol[] {NetworkDiscoveryProtocol.TCP});

		final Map<String, Boolean> tcpPortsMap = new HashMap<>();
		tcpPortsMap.put("80", true);
		tcpPortsMap.put("!80", true);
		tcpPortsMap.put("22,80,443", true);
		tcpPortsMap.put("80,!90", true);
		tcpPortsMap.put("-1", true);
		tcpPortsMap.put("2-4", true);
		tcpPortsMap.put("def", true);
		tcpPortsMap.put("80,!81,2-4,def", true);
		tcpPortsMap.put("80_", false);
		tcpPortsMap.put("80;81", false);

		for (final Map.Entry<String, Boolean> entry : tcpPortsMap.entrySet()) {
			final String tcpPorts = entry.getKey();
			configuration.setTcpPorts(tcpPorts);
			final boolean expectedSuccess = entry.getValue();
			if (expectedSuccess) {
				// Valid tcp ports
				MarshallingUtils.marshal(configuration);
			}
			else {
				// Invalid tcp ports
				assertThatThrownBy(() -> {
					MarshallingUtils.marshal(configuration);
				}).isInstanceOf(BeanValidationException.class).hasCauseInstanceOf(ConstraintViolationException.class);
			}
		}
	}

	@Test
	public void testNetworkDiscoveryScan() throws SQLException, JAXBException {
		final NetworkDiscoveryConfigurationTemplate configuration = new NetworkDiscoveryConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_DISCOVERY);
		configuration.setTimeout(600);
		configuration.setTargetList("80.254.228.51");
		configuration.setProtocols(new NetworkDiscoveryProtocol[] {NetworkDiscoveryProtocol.TCP});
		configuration.setTcpPorts("80");

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Discovery", ScanTemplate.NETWORK_DISCOVERY, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithBasePolicyHasNoCustomConfig() throws SQLException, JAXBException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final int scanPolicyId = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanpolicies (name, system, createdbyid, updatedbyid, customerid) VALUES (?, ?, ?, ?, ?) RETURNING id",
				"My Policy 4", false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId(scanPolicyId);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with base policy has null config", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithBasePolicyHasNoCustomConfigButHasTheOverrideConfig()
			throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final int scanPolicyId = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanpolicies (name, system, createdbyid, updatedbyid, customerid) VALUES (?, ?, ?, ?, ?) RETURNING id",
				"My Policy 4", false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId(scanPolicyId);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan that base policy has null config but override policy has config", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration),
				getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithMultipleHostnameAndIPAndTheAssetIdentifiersShouldBeSortedByTypeAndLastSeenInScanSettings() throws SQLException, JAXBException {
		final String ip1 = "**************";
		final String ip2 = "**************";
		final String hostname1 = "example1.outpost24.com";
		final String hostname2 = "example2.outpost24.com";
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		final long assetIdentifierId1 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?::timestamp, ?) RETURNING id",
				ip1, AssetIdentifierType.IP, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				"2024-04-01 00:00:00.000 +0700",
				getTestUserCustomerId());
		final long assetIdentifierId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?::timestamp, ?) RETURNING id",
				ip2, AssetIdentifierType.IP, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				"2024-05-01 00:00:00.000 +0700",
				getTestUserCustomerId());
		final long assetIdentifierId3 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?::timestamp, ?) RETURNING id",
				hostname1, AssetIdentifierType.HOSTNAME, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				"2024-04-01 00:00:00.000 +0700",
				getTestUserCustomerId());
		final long assetIdentifierId4 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?::timestamp, ?) RETURNING id",
				hostname2, AssetIdentifierType.HOSTNAME, new Source[] {Source.NETSEC}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				"2024-05-01 00:00:00.000 +0700",
				getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId1);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId3);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId4);

		final int scanPolicyId = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanpolicies (name, system, createdbyid, updatedbyid, customerid) VALUES (?, ?, ?, ?, ?) RETURNING id",
				"My Policy 4", false, XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID, getTestUserCustomerId());

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId(scanPolicyId);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan that base policy has null config but override policy has config", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration),
				getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertThat(new JSONObject(response.readEntity(String.class)).getInt("scansStarted")).isEqualTo(1);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId)).isEqualTo(1);

		final String scanSettings = DbObject.getString(getConnection(), "SELECT txsettings FROM tscanstatuss ORDER BY xid DESC LIMIT 1");
		final ParamList settings = new ParamList(scanSettings);
		final String targetCandidates = settings.getValueOfWithValue("VALUE", "NAME", ScanSettingKeys.TARGET_CANDIDATES);
		final JSONArray jsonArray = new JSONArray(targetCandidates);
		assertThat(jsonArray.length()).isEqualTo(4);
		assertThat(jsonArray.getJSONObject(0).getString("assetIdentifierName")).isEqualTo(hostname2);
		assertThat(jsonArray.getJSONObject(1).getString("assetIdentifierName")).isEqualTo(hostname1);
		assertThat(jsonArray.getJSONObject(2).getString("assetIdentifierName")).isEqualTo(ip2);
		assertThat(jsonArray.getJSONObject(3).getString("assetIdentifierName")).isEqualTo(ip1);
	}

	@Test
	public void testNetsecScanWithoutOverridePolicy() throws SQLException, JAXBException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy1);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan without overrideScanPolicyId", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithOverridePolicy() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy1);
		configuration.setOverrideScanPolicyId((int) customPolicy2);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with overrideScanPolicyId", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithFileInConfig() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		final long tagId2 = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test2", "Test2",
				getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId2, assetId);

		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId, (int) tagId2});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with file in policy config", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithNoAssetMatchWithTagIds() throws SQLException, JAXBException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		final long tagId2 = DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test2", "Test2",
				getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		// No Asset Match with input Asset Tag Ids
		configuration.setAssetTagIds(new Integer[] {(int) tagId, (int) tagId2});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with no asset match with tag ids", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.BAD_REQUEST, MAINUSER_TOKEN);
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithSWATSourceAsset() throws SQLException, JAXBException {
		// Asset with SWAT source will be ignored to start scan
		final long swatAssetId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assets (name, source, createdbyid, updatedbyid, customerid) VALUES(?, ?::source[], ?, ?, ?) RETURNING id",
				"TestAsset", new Source[] {Source.SWAT}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, getTestUserCustomerId());
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, swatAssetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", swatAssetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", swatAssetId,
				assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with SWAT-source-Asset", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.BAD_REQUEST, MAINUSER_TOKEN);
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithSWATSourceAssetIdentifier() throws SQLException, JAXBException {
		// Asset Identifier with SWAT source will be ignored to start scan
		final long swatAssetIdentifierId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO assetidentifiers (name, type, source, createdbyid, updatedbyid, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"a-clound-instance", AssetIdentifierType.AWS_INSTANCE_ID, new Source[] {Source.SWAT}, XMLAPITestBase.ADMIN_USER_ID, XMLAPITestBase.ADMIN_USER_ID, new Date(),
				new Date(), getTestUserCustomerId());
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				swatAssetIdentifierId);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(0, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithLinkedAccountToPolicy() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final Account sshAccount = new Account();
		sshAccount.setCustomerId((int) getTestUserCustomerId());
		sshAccount.setName("SSH test");
		sshAccount.setType(AccountType.SSH);
		sshAccount.setScanPolicyIds(new Integer[] {(int) customPolicy3});
		final int sshAccountId = (int) sshAccount.save(getConnection());

		DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", customPolicy3, sshAccountId);

		final Credential sshCredential = new Credential();
		sshCredential.setCustomerId((int) getTestUserCustomerId());
		sshCredential.setAccountId(sshAccountId);
		sshCredential.setValue("*****");
		sshCredential.setClassId(CredentialClassType.USERNAME.getId());
		sshCredential.save(getConnection());
		sshCredential.setClassId(CredentialClassType.PASSWORD.getId());
		sshCredential.save(getConnection());
		sshCredential.setClassId(CredentialClassType.PRIVATE_KEY.getId());
		sshCredential.save(getConnection());
		getConnection().commit();

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with SSH linked account", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithLinkedMultipleTypeAccountsToPolicy() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				assetIdentifierId2);

		final Account sshAccount = new Account();
		sshAccount.setCustomerId((int) getTestUserCustomerId());
		sshAccount.setName("SSH test");
		sshAccount.setType(AccountType.SSH);
		sshAccount.setScanPolicyIds(new Integer[] {(int) customPolicy3});
		final int sshAccountId = (int) sshAccount.save(getConnection());

		DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", customPolicy3, sshAccountId);

		final Credential sshCredential = new Credential();
		sshCredential.setCustomerId((int) getTestUserCustomerId());
		sshCredential.setAccountId(sshAccountId);
		sshCredential.setValue("*****");
		sshCredential.setClassId(CredentialClassType.USERNAME.getId());
		sshCredential.save(getConnection());
		sshCredential.setClassId(CredentialClassType.PASSWORD.getId());
		sshCredential.save(getConnection());
		sshCredential.setClassId(CredentialClassType.PRIVATE_KEY.getId());
		sshCredential.save(getConnection());
		getConnection().commit();

		final Account smbAccount = new Account();
		smbAccount.setCustomerId((int) getTestUserCustomerId());
		smbAccount.setName("SMB test");
		smbAccount.setType(AccountType.SMB);
		smbAccount.setScanPolicyIds(new Integer[] {(int) customPolicy3});
		final int smbAccountId = (int) smbAccount.save(getConnection());

		DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", customPolicy3, smbAccountId);

		final Credential smbCredential = new Credential();
		smbCredential.setCustomerId((int) getTestUserCustomerId());
		smbCredential.setAccountId(smbAccountId);
		smbCredential.setValue("*****");
		smbCredential.setClassId(CredentialClassType.USERNAME.getId());
		smbCredential.save(getConnection());
		smbCredential.setClassId(CredentialClassType.PASSWORD.getId());
		smbCredential.save(getConnection());
		smbCredential.setClassId(CredentialClassType.DOMAIN.getId());
		smbCredential.save(getConnection());
		getConnection().commit();

		final Account vsphereAccount = new Account();
		vsphereAccount.setCustomerId((int) getTestUserCustomerId());
		vsphereAccount.setName("Vsphere test");
		vsphereAccount.setType(AccountType.VSPHERE);
		vsphereAccount.setScanPolicyIds(new Integer[] {(int) customPolicy3});
		final int vsphereAccountId = (int) vsphereAccount.save(getConnection());

		DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", customPolicy3, vsphereAccountId);

		final Credential vsphereCredential = new Credential();
		vsphereCredential.setCustomerId((int) getTestUserCustomerId());
		vsphereCredential.setAccountId(vsphereAccountId);
		vsphereCredential.setValue("*****");
		vsphereCredential.setClassId(CredentialClassType.USERNAME.getId());
		vsphereCredential.save(getConnection());
		vsphereCredential.setClassId(CredentialClassType.PASSWORD.getId());
		vsphereCredential.save(getConnection());
		getConnection().commit();

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with SSH linked sshAccount", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testNetsecScanWithLinkedAWSAccountToAssetIdentifier() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {
		final long tagId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());
		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, assetId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())", assetId,
				awsAssetIdentifierId);

		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("AWS test");
		account.setType(AccountType.AWS);
		account.setAssetIdentifierIds(new Integer[] {(int) awsAssetIdentifierId});
		final int accountId = (int) account.save(getConnection());

		DbObject.executeUpdate(getConnection(), "INSERT INTO assetidentifier_account (assetidentifierid, accountid) VALUES(?, ?)", awsAssetIdentifierId, accountId);
		DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", customPolicy3, accountId);

		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setClassId(CredentialClassType.ROLE_ARN.getId());
		credential.setValue("arn");
		credential.save(getConnection());

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setTimeout(7200);
		configuration.setScanPolicyId((int) customPolicy3);
		configuration.setAssetTagIds(new Integer[] {(int) tagId});

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Network Scan with AWS linked account", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
				XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		triggerNetworkLookup(configurationId);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testAwsCloudDiscoveryScan() throws SQLException, JAXBException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("AWS");
		account.setType(AccountType.AWS);
		final Integer accountId = (int) account.save(getConnection());

		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setClassId(CredentialClassType.ROLE_ARN.getId());
		credential.setValue("arn");
		credential.save(getConnection());

		final CloudDiscoveryConfigurationTemplate configuration = new CloudDiscoveryConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.CLOUD_DISCOVERY);
		configuration.setTimeout(600);
		configuration.setAccountId(accountId);
		final CloudDiscoveryAwsConfigurationTemplate awsConfigurationTemplate = new CloudDiscoveryAwsConfigurationTemplate();
		awsConfigurationTemplate.setRegions("eu-central-1,us-east-2");
		awsConfigurationTemplate.setCloudDiscoveryType(CloudDiscoveryType.AWS);
		configuration.setCloudConfiguration(awsConfigurationTemplate);

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Cloud Discovery", ScanTemplate.CLOUD_DISCOVERY, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		final Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
	}

	@Test
	public void testAzureCloudDiscoveryScan() throws SQLException, JAXBException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("Azure_Scan");
		account.setType(AccountType.AZURE);
		final Integer accountId = (int) account.save(getConnection());

		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setClassId(CredentialClassType.TENANT_ID.getId());
		credential.setValue("tenant id");
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.CLIENT_ID.getId());
		credential.setValue("client id");
		credential.save(getConnection());
		credential.setClassId(CredentialClassType.SECRET.getId());
		credential.setValue("******");
		credential.save(getConnection());

		final CloudDiscoveryConfigurationTemplate configuration = new CloudDiscoveryConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.CLOUD_DISCOVERY);
		configuration.setTimeout(600);
		configuration.setAccountId(accountId);
		final CloudDiscoveryAzureConfigurationTemplate azureConfigurationTemplate = new CloudDiscoveryAzureConfigurationTemplate();
		azureConfigurationTemplate.setSubscriptions("my_subscription");
		azureConfigurationTemplate.setCloudDiscoveryType(CloudDiscoveryType.AZURE);
		configuration.setCloudConfiguration(azureConfigurationTemplate);

		final long configurationId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Cloud Discovery", ScanTemplate.CLOUD_DISCOVERY, MarshallingUtils.marshal(configuration), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		// Start scan
		post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
	}

	@Test
	public void testScaleApplicationLimit() throws SQLException {
		Response response = post(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));

		final List<String> newSeeds = new ArrayList<>();
		newSeeds.add("http://aoeu.com/");
		final long secondConfigId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"TestConfig 2", ScanTemplate.SCALE, createScaleConfiguration().put(SEEDS, newSeeds).toString(), getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID,
				XMLAPITestBase.TEST_USER_ID);

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET outscanwasxapps = 1 WHERE xid = ?", XMLAPITestBase.TEST_USER_ID);
		DbObject.executeUpdate(getConnection(), "UPDATE scanlogs SET status = 'FINISHED', ended = NOW()");
		getConnection().commit();

		post(ScanConfigurationsResource.PATH + "/" + secondConfigId + "/scan", Status.BAD_REQUEST, MAINUSER_TOKEN);

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET outscanwasxapps = -1 WHERE xid = ?", XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		response = post(ScanConfigurationsResource.PATH + "/" + secondConfigId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
	}

	@Test
	@RequireDatabaseCommits
	public void testScaleTimeout() throws SQLException {
		final Response response = post(ScanConfigurationsResource.PATH + "/" + this.scanConfigurationScaleId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));

		assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss WHERE vcservice = ? AND xsoxid = ? ",
				ScanServiceType.AppsecScale.toString(), this.scanConfigurationScaleId));
		DbObject.executeUpdate(getConnection(), "UPDATE tscanstatuss SET bstop = 1, dscanend = '1970-01-01'");
		getConnection().commit();
		new ScanStatusBusiness().scheduler(DateUtils.getCurrentDate("yyyy-MM-dd HH:mm"), new ScanStatusThread(0, null));
		assertEquals(0, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM tscanstatuss"));
		assertEquals(ScanLogStatus.FAILED.toString(), DbObject.getString(getConnection(), "SELECT status FROM scanlogs ORDER BY id DESC LIMIT 1"));
		assertEquals("Scan timeout.", DbObject.getString(getConnection(), "SELECT statusdetails FROM scanlogs ORDER BY id DESC LIMIT 1"));
	}

	@Test
	public void testScaleApplicationLimitWithScanData() throws SQLException, MalformedURLException, JAXBException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfigurations");

		final JSONObject scanConfigurationData = createScaleScanConfigurationData();

		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationData.toString());
		final long scanConfigurationId1 = getCreatedId(response);

		scanConfigurationData.getJSONObject("configuration").put("seeds", new String[] {"/path1", "/path2"});

		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfigurationData.toString());
		final long scanConfigurationId2 = getCreatedId(response);

		DbObject.executeUpdate(getConnection(),
				"INSERT INTO scanlogs (status, template, customerid, scanconfigurationid, createdbyid, started, ended) VALUES(?, ?, ?, ?, ?, now(), now())",
				ScanLogStatus.FINISHED, ScanTemplate.SCALE, getTestUserCustomerId(), scanConfigurationId1, XMLAPITestBase.TEST_USER_ID);

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET outscanwasxapps = 1 WHERE xid = ?", XMLAPITestBase.TEST_USER_ID);
		getConnection().commit();

		final List<Event> eventList = new ArrayList<>();

		final ScanService scanService = new ScanService();
		scanService.setVhost("outpost24.com");
		scanService.setService("https");
		scanService.setPort(443);

		final List<ScanService> scanServices = new ArrayList<>();
		scanServices.add(scanService);

		final JSONObject scanData = new JSONObject();
		scanData.put("services", new JSONArray(scanServices.stream().map(s -> s.toJson()).collect(Collectors.toList())));

		final StartScanRequest startScanRequest = StartScanRequest.builder()
				.user(LoggedOnUser.getById(LoggedOnUser.class, getConnection(), Access.ADMIN, XMLAPITestBase.TEST_USER_ID))
				.scanConfiguration(ScanConfiguration.getById(ScanConfiguration.class, getConnection(), Access.ADMIN, scanConfigurationId2))
				.scanData(scanData)
				.build();

		// Start scale scan, should fail, user already have one scanned application
		final StartScanService startScanService1 = ServiceProvider.getStartScanService(getConnection(), startScanRequest);
		try {
			final Integer startedScans = startScanService1.startScaleScan(eventList);
			assertThat(startedScans).isEqualTo(0);
		}
		catch (final BaseRestException e) {
			assertThat(e.getMessage()).isEqualTo("_TOO_MANY_APPSEC_SCALE_ASSETS");
		}

		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET outscanwasxapps = 2 WHERE xid = ?", XMLAPITestBase.TEST_USER_ID);
		startScanRequest.setUser(LoggedOnUser.getById(LoggedOnUser.class, getConnection(), Access.ADMIN, XMLAPITestBase.TEST_USER_ID));
		getConnection().commit();

		// Start scale scan, user is allowed to scan two applications, one from scan configuration and one from scan data
		final StartScanService startScanService2 = ServiceProvider.getStartScanService(getConnection(), startScanRequest);
		final Integer startedScans = startScanService2.startScaleScan(eventList);
		assertThat(startedScans).isEqualTo(1);
	}

	@Test
	public void testScaleSeedUrls() throws SQLException {
		final long scanConfigurationId1 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"TestConfig 1", ScanTemplate.SCALE, createScaleConfiguration().put(SEEDS, Arrays.asList(new String[] {"http://test.com/test1/test2"})).toString(),
				getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);

		final long scanConfigurationId2 = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"TestConfig 2", ScanTemplate.SCALE, createScaleConfiguration().put(SEEDS, Arrays.asList(new String[] {"http://test.com/test3"})).toString(),
				getTestUserCustomerId(), XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);

		DbObject.executeUpdate(getConnection(),
				"INSERT INTO scanlogs (status, template, customerid, scanconfigurationid, createdbyid, started, ended) VALUES(?, ?, ?, ?, ?, now(), now())",
				ScanLogStatus.FINISHED, ScanTemplate.SCALE, getTestUserCustomerId(), scanConfigurationId1, XMLAPITestBase.TEST_USER_ID);

		DbObject.executeUpdate(getConnection(),
				"INSERT INTO scanlogs (status, template, customerid, scanconfigurationid, createdbyid, started, ended) VALUES(?, ?, ?, ?, ?, now(), now())",
				ScanLogStatus.FINISHED, ScanTemplate.SCALE, getTestUserCustomerId(), scanConfigurationId2, XMLAPITestBase.TEST_USER_ID);

		final User user = User.getById(User.class, getConnection(), Access.ADMIN, XMLAPITestBase.TEST_USER_ID);
		assertEquals(1, user.getWasxAppCount());
	}

	@Test
	public void testNetworkScanWithCyberArkCredentials() throws SQLException, JAXBException, IOException, ParamValidationException, ExecutionException {
		startLocalServer(false, new CyberArkMockCallback());
		try {
			final CyberArkIntegrationConfiguration integrationConfiguration = new CyberArkIntegrationConfiguration();
			integrationConfiguration.setType(IntegrationType.CYBERARK);
			integrationConfiguration.setUrl("http://127.0.0.1:" + getLocalServerPort());
			integrationConfiguration.setApplicationId("AppId");

			final Integration integration = new Integration();
			integration.setName("CyberArk");
			integration.setCustomerId((int) getTestUserCustomerId());
			integration.setType(IntegrationType.CYBERARK);
			integration.setConfiguration(integrationConfiguration);
			final int integrationId = (int) integration.save(getConnection());

			final Account account = new Account();
			account.setCustomerId((int) getTestUserCustomerId());
			account.setName("SSH");
			account.setType(AccountType.SSH);
			account.setIntegrationId(integrationId);
			final int accountId = (int) account.save(getConnection());

			final Credential credential = new Credential();
			credential.setCustomerId((int) getTestUserCustomerId());
			credential.setAccountId(accountId);
			credential.setClassId(CredentialClassType.USERNAME.getId());
			credential.setValue("username");
			credential.save(getConnection());
			credential.setClassId(CredentialClassType.PASSWORD.getId());
			credential.setValue("{{Content}}");
			credential.save(getConnection());
			credential.setClassId(CredentialClassType.CYBERARK_OBJECT.getId());
			credential.setValue("test");
			final int credentialId = (int) credential.save(getConnection());

			DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", this.customPolicy3, accountId);

			final int tagId = (int) DbObject.executeCountQuery(getConnection(),
					"INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());

			DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, this.assetId);

			DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())",
					this.assetId, this.assetIdentifierId2);

			final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
			configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
			configuration.setTimeout(7200);
			configuration.setScanPolicyId((int) this.customPolicy3);
			configuration.setAssetTagIds(new Integer[] {(int) tagId});

			final long configurationId = DbObject.executeCountQuery(getConnection(),
					"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
					"Network Scan", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
					XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);

			getConnection().commit();

			// Start scan and check that settings contain password from CyberArk.
			Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
			assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
			assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE template = ?", ScanTemplate.NETWORK_LOOKUP)).isEqualTo(1);
			triggerNetworkLookup(configurationId);

			String settings = DbObject.getString(getConnection(), "SELECT txsettings FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(settings).contains("ssh/password");
			assertThat(settings).contains("value=\"12345\"");
			String issues = DbObject.getString(getConnection(), "SELECT issues FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(issues).isNull();

			// Remove CyberArk object and check that scan still start but with issues in settings.
			DbObject.execute(getConnection(), "DELETE FROM scanlogs WHERE scanconfigurationid = ?", configurationId);
			DbObject.execute(getConnection(), "DELETE FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			credential.setId(credentialId);
			credential.setValue("missing");
			credential.save(getConnection());
			getConnection().commit();

			response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
			assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
			triggerNetworkLookup(configurationId);
			assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
			settings = DbObject.getString(getConnection(), "SELECT txsettings FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(settings).doesNotContain("ssh/password");
			issues = DbObject.getString(getConnection(), "SELECT issues FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(issues).contains(IssueType.CYBERARK_RETRIEVAL_FAILED.name());
		}
		finally {
			stopLocalServer();
		}
	}

	/**
	 * Triggers the network lookup for the given scan configuration.
	 *
	 * @param configurationId The ID of the scan configuration to run the network lookup for.
	 * @throws IOException On error reading or writing data.
	 * @throws SQLException On database errors.
	 * @throws JAXBException On error (un)marshalling JSONs.
	 * @throws ExecutionException On error executing the network lookup.
	 * @throws ParamValidationException On error validating parameters.
	 */
	private void triggerNetworkLookup(final long configurationId) throws IOException, SQLException, JAXBException, ExecutionException, ParamValidationException {
		final List<ScanStatus> scanStatuses = ScanStatus.fetchObjects(ScanStatus.class, getConnection(),
				"SELECT xid, xuserxid, vcservice, networklookupdata FROM tscanstatuss WHERE vcservice = ?", "NL");
		assertThat(scanStatuses).hasSize(1);
		final ScanStatus scanStatus = scanStatuses.get(0);
		final long scanId = scanStatus.getId();

		// Prepare data for network lookup
		final Scan scan = new Scan();
		scan.setId(scanId);
		scan.setService(ScanServiceType.NETWORK_LOOKUP);
		scan.setScannerId(0L);
		scan.setUserId(scanStatus.getUserId());
		scan.setScanStartDate(new Date());
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.HOUR, 1);
		scan.setScanEnd(cal.getTime());
		scan.setNetworkLookupData(scanStatus.getNetworkLookupData());

		// Run network lookup and create report file
		final OutscanThread outscanThread = new OutscanThread(ScanApp.getInstance(false));
		final DiscoveryThread discoveryThread = new DiscoveryThread(outscanThread, scan);
		discoveryThread.start();
		await().atMost(60, TimeUnit.SECONDS)
				.until(() -> DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM signoffobjects WHERE xid = ?", scanId) == 1);
		final File reportFile = new File(OutscanThread.getReportFile(scanId, false, true, false));
		assertTrue(reportFile.exists());

		// Get report from file
		final String report;
		try (final InputStream reportStream = Files.newInputStream(reportFile.toPath())) {
			final StringWriter stringWriter = new StringWriter();
			IOUtils.copy(new GZIPInputStream(reportStream), stringWriter, StandardCharsets.UTF_8);
			report = stringWriter.toString();
		}

		// Mock resolved IPs
		final JSONObject ipEntryJson = new JSONObject();
		ipEntryJson.put("ip", "**************");
		ipEntryJson.put("targets", new JSONArray(Arrays.asList(new String[] {"TestAssetIdentifier", "**************", "a-clound-instance"})));
		ipEntryJson.put("discoveredHostnames", new JSONArray(Arrays.asList(new String[] {"example1.hostname", "example2.hostname"})));

		final JSONArray ipTargetJsonArray = new JSONArray();
		ipTargetJsonArray.put(ipEntryJson);

		final JSONObject assetEntryJson = new JSONObject();
		assetEntryJson.put("assetId", this.assetId);
		assetEntryJson.put("ipTargetInfo", ipTargetJsonArray);

		final JSONArray ipJsonArray = new JSONArray();
		ipJsonArray.put(assetEntryJson);

		final JSONObject reportJson = new JSONObject(report);
		reportJson.put("ipToTargetsMap", ipJsonArray);

		// Generate a scanqueue record
		scanStatus.setReport(reportJson.toString());
		new ScanDataService(ServiceProvider.getScanStatusService(getConnection()), ServiceProvider.getUserService(getConnection()),
				ServiceProvider.getScanlogService(getConnection()),
				new DbLayerNativeStatementExecutor(getConnection()), ServiceProvider.getSaveReportService(getConnection()),
				ServiceProvider.getSchedulingService(getConnection()), ServiceProvider.getScanSchedulingService(getConnection()),
				ServiceProvider.getReportCreateService(getConnection()), ServiceProvider.getEmailService(getConnection()),
				ServiceProvider.getTargetService(getConnection()),
				ServiceProvider.getIpService(getConnection()), ServiceProvider.getEventApi(getConnection()), ServiceProvider.getReportingService(getConnection()),
				ServiceProvider.getAuditingService(getConnection()), new ScannerDaoImpl(getConnection()), ServiceProvider.getConfigService(),
				ServiceProvider.getMessageService(), ServiceProvider.getComplianceService(getConnection()), ServiceProvider.getRuleService(getConnection()),
				ServiceProvider.getScanPolicyService(getConnection()),
				ServiceProvider.getEventApiV2(getConnection())).saveReport(scanStatus, new ArrayList<>(), new ArrayList<>());
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanqueue WHERE scanconfigurationid = ?", configurationId)).isEqualTo(1);

		// Start network scan
		final ScanQueue scanQueue = DbObject.fetchObjects(ScanQueue.class, getConnection(), "SELECT id, scandata FROM scanqueue WHERE scanconfigurationid = ?", configurationId).get(0);

		final BaseLoggedOnUser user = BaseLoggedOnUser.getById(LoggedOnUser.class, getConnection(), Access.ADMIN, XMLAPITestBase.TEST_USER_ID);
		final ScanConfiguration scanConfiguration = DbObject.getById(ScanConfiguration.class, getConnection(), configurationId);
		final StartScanRequest startScanRequest = StartScanRequest.builder()
				.user(user)
				.scanConfiguration(scanConfiguration)
				.scanData(new JSONObject(scanQueue.getScanData()))
				.build();
		scanConfiguration.startScan(getConnection(), startScanRequest, new ArrayList<>());
		DbObject.execute(getConnection(), "DELETE FROM scanqueue WHERE scanconfigurationid = ?", configurationId);
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally exposing internal representation")
	public class CyberArkMockCallback extends LocalServerCallback {
		/**
		 * Get response for cyberark requests.
		 *
		 * @param uri Request uri
		 * @return JSON response
		 */
		private String getResponse(final String uri) {
			if (uri.contains("Object=test")) {
				return new JSONObject().put("Content", "12345").put("Safe", "Test").toString();
			}
			return "";
		}

		@Override
		public void handleRequest(final Socket socket) {
			try (final DefaultBHttpServerConnection conn = new DefaultBHttpServerConnection(8 * 1024)) {
				conn.bind(socket);

				final HttpRequest request = conn.receiveRequestHeader();
				final String json = getResponse(request.getRequestLine().getUri());
				final BasicHttpResponse response = new BasicHttpResponse(HttpVersion.HTTP_1_1, HttpStatus.SC_OK, "OK");
				final BasicHttpEntity entity = new BasicHttpEntity();
				final byte[] content = json.getBytes(UTF_8);
				entity.setContent(new ByteArrayInputStream(content));
				entity.setContentLength(content.length);
				entity.setContentType("application/json");
				response.addHeader("Content-Type", "application/json");
				response.setEntity(entity);
				response.addHeader("Content-Length", "" + content.length);
				conn.sendResponseHeader(response);
				conn.sendResponseEntity(response);
				conn.flush();
				conn.shutdown();
			}
			catch (final RuntimeException | IOException | HttpException e) {
				// Ignore
			}
		}
	}

	@Test
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally exposing internal representation")
	public void testNetworkScanWithDelineaCredentials() throws SQLException, JAXBException, ParamValidationException, IOException, ExecutionException {

		final String secretName = "test";
		final String secretField = "password";
		final String secretValue = "test-password";

		startLocalServer(false, new DelineaMockCallback(1, "/test/path/", secretName, secretField, secretValue));
		try {
			final DelineaIntegrationConfiguration integrationConfiguration = new DelineaIntegrationConfiguration();
			integrationConfiguration.setType(IntegrationType.DELINEA);
			integrationConfiguration.setUrl("http://127.0.0.1:" + getLocalServerPort());
			integrationConfiguration.setUsername("test");
			integrationConfiguration.setPassword("test");

			final Integration integration = new Integration();
			integration.setName("Delinea");
			integration.setCustomerId((int) getTestUserCustomerId());
			integration.setType(IntegrationType.DELINEA);
			integration.setConfiguration(integrationConfiguration);
			final int integrationId = (int) integration.save(getConnection());

			final Account account = new Account();
			account.setCustomerId((int) getTestUserCustomerId());
			account.setName("SSH");
			account.setType(AccountType.SSH);
			account.setIntegrationId(integrationId);
			final int accountId = (int) account.save(getConnection());

			final Credential credential = new Credential();
			credential.setCustomerId((int) getTestUserCustomerId());
			credential.setAccountId(accountId);
			credential.setClassId(CredentialClassType.USERNAME.getId());
			credential.setValue("username");
			credential.save(getConnection());
			credential.setClassId(CredentialClassType.PASSWORD.getId());
			credential.setValue("{{" + secretField + "}}");
			credential.save(getConnection());
			credential.setClassId(CredentialClassType.DELINEA_SECRET.getId());
			credential.setValue(secretName);
			final int credentialId = (int) credential.save(getConnection());

			DbObject.executeUpdate(getConnection(), "INSERT INTO scanpolicy_account (scanpolicyid, accountid) VALUES(?, ?)", this.customPolicy3, accountId);

			final int tagId = (int) DbObject.executeCountQuery(getConnection(),
					"INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());

			DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, this.assetId);

			DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, NOW(), NOW())",
					this.assetId, this.assetIdentifierId2);

			final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
			configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
			configuration.setTimeout(7200);
			configuration.setScanPolicyId((int) this.customPolicy3);
			configuration.setAssetTagIds(new Integer[] {(int) tagId});

			final long configurationId = DbObject.executeCountQuery(getConnection(),
					"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
					"Network Scan", ScanTemplate.NETWORK_SCAN, MarshallingUtils.marshal(configuration), getTestUserCustomerId(),
					XMLAPITestBase.TEST_USER_ID, XMLAPITestBase.TEST_USER_ID);

			getConnection().commit();

			// Start scan and check that settings contain password from Delinea.
			Response response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
			triggerNetworkLookup(configurationId);
			assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
			assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
			String settings = DbObject.getString(getConnection(), "SELECT txsettings FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(settings).contains("ssh/password");
			assertThat(settings).contains("value=\"" + secretValue + "\"");
			String issues = DbObject.getString(getConnection(), "SELECT issues FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(issues).isNull();

			// Remove Delinea object and check that scan still start but with issues in settings.
			DbObject.execute(getConnection(), "DELETE FROM scanlogs WHERE scanconfigurationid = ?", configurationId);
			DbObject.execute(getConnection(), "DELETE FROM tscanstatuss WHERE xsoxid = ?", configurationId);

			credential.setId(credentialId);
			credential.setValue("missing");
			credential.save(getConnection());
			getConnection().commit();

			response = post(ScanConfigurationsResource.PATH + "/" + configurationId + "/scan", Status.OK, MAINUSER_TOKEN);
			triggerNetworkLookup(configurationId);
			assertEquals(1, new JSONObject(response.readEntity(String.class)).getInt("scansStarted"));
			assertEquals(1, DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", configurationId));
			settings = DbObject.getString(getConnection(), "SELECT txsettings FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(settings).doesNotContain("ssh/password");
			issues = DbObject.getString(getConnection(), "SELECT issues FROM tscanstatuss WHERE xsoxid = ?", configurationId);
			assertThat(issues).contains(IssueType.DELINEA_RETRIEVAL_FAILED.name());
		}
		finally {
			stopLocalServer();
		}
	}

	@AllArgsConstructor
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally exposing internal representation")
	public class DelineaMockCallback extends LocalServerCallback {
		private int secretId;
		private String secretPath;
		private String secretName;
		private String secretField;
		private String secretValue;

		/**
		 * Get response for Delinea requests.
		 *
		 * @param uri Request uri
		 * @return JSON response
		 */
		private String getResponse(final String uri) {
			if (uri.contains("/oauth2/token")) {
				return new JSONObject().put("access_token", "TOKEN").toString();
			}
			else if (uri.contains("/api/v1/folders")) {
				return new JSONObject().put("records", new JSONObject[] {new JSONObject().put("folderPath", this.secretPath).put("id", 0)}).toString();
			}
			else if (uri.contains("/api/v1/secrets/lookup") && uri.contains("searchText=" + secretName)) {
				return new JSONObject().put("records", new JSONObject[] {new JSONObject().put("id", this.secretId)}).toString();
			}
			else if (uri.contains("/api/v1/secrets/" + this.secretId + "/fields/" + this.secretField)) {
				return this.secretValue;
			}
			else if (uri.contains("/api/v1/secrets/" + this.secretId)) {
				return new JSONObject().put("items",
						new JSONObject[] {new JSONObject().put("filename", this.secretField).put("fileAttachmentId", 1).put("slug", this.secretField)}).toString();
			}
			return "";
		}

		@Override
		public void handleRequest(final Socket socket) {
			try (final DefaultBHttpServerConnection conn = new DefaultBHttpServerConnection(8 * 1024)) {
				conn.bind(socket);

				final HttpRequest request = conn.receiveRequestHeader();
				final String json = getResponse(request.getRequestLine().getUri());
				final BasicHttpResponse response = new BasicHttpResponse(HttpVersion.HTTP_1_1, HttpStatus.SC_OK, "OK");
				final BasicHttpEntity entity = new BasicHttpEntity();
				final byte[] content = json.getBytes(UTF_8);
				entity.setContent(new ByteArrayInputStream(content));
				entity.setContentLength(content.length);
				entity.setContentType("application/json");
				response.addHeader("Content-Type", "application/json");
				response.setEntity(entity);
				response.addHeader("Content-Length", "" + content.length);
				conn.sendResponseHeader(response);
				conn.sendResponseEntity(response);
				conn.flush();
				conn.shutdown();
			}
			catch (final RuntimeException | IOException | HttpException e) {
				// Ignore
			}
		}
	}

	@Test
	public void testNetworkScanWithAssetTags() throws SQLException, JAXBException {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		final long tagId = DbObject.executeCountQuery(getConnection(),
				"INSERT INTO tags (key, value, customerid) VALUES(?, ?, ?) RETURNING id", "Test", "Test", getTestUserCustomerId());

		DbObject.executeUpdate(getConnection(), "INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?)", tagId, this.assetId);

		DbObject.executeUpdate(getConnection(), "INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) "
				+ "VALUES(?, ?, NOW(), NOW())", this.assetId, this.assetIdentifierId2);

		final NetworkScanConfigurationTemplate configuration = new NetworkScanConfigurationTemplate();
		configuration.setTemplate(ScanTemplate.NETWORK_SCAN);
		configuration.setScanPolicyId((int) this.customPolicy1);
		configuration.setTimeout(7200);

		final JSONObject scanConfiguration = new JSONObject();
		scanConfiguration.put("name", "Test");
		scanConfiguration.put("template", ScanTemplate.NETWORK_SCAN);
		scanConfiguration.put("configuration", new JSONObject(MarshallingUtils.marshal(configuration)));

		// Create scan configuration without asset tags
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		final Integer scanConfigurationId = (int) getCreatedId(response);
		assertThat(scanConfigurationId).isGreaterThan(0);

		// Start scan, should fail since we have no asset tags
		response = post(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/scan", Status.BAD_REQUEST, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_ASSET_TAGS"));

		// Link to schedule, should fail since we have no asset tags
		response = put(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/schedules/" + this.scheduleId, Status.BAD_REQUEST, MAINUSER_TOKEN, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_ASSET_TAGS"));

		configuration.setAssetTagIds(new Integer[] {(int) tagId});
		scanConfiguration.put("configuration", new JSONObject(MarshallingUtils.marshal(configuration)));

		// Patch scan configuration and set asset tags
		patch(ScanConfigurationsResource.PATH + "/" + scanConfigurationId, Status.OK, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());

		// Start scan
		response = post(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertThat(new JSONObject(response.readEntity(String.class)).getInt("scansStarted")).isEqualTo(1);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE template = ?", ScanTemplate.NETWORK_LOOKUP)).isEqualTo(1);

		// Link to schedule
		response = put(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/schedules/" + this.scheduleId, Status.CREATED, MAINUSER_TOKEN, "");

		configuration.setAssetTagIds(null);
		scanConfiguration.put("configuration", new JSONObject(MarshallingUtils.marshal(configuration)).put("assetTagIds", JSONObject.NULL));

		// Patch and remove asset tags, should fail since scan configuration is linked to schedule
		response = patch(ScanConfigurationsResource.PATH + "/" + scanConfigurationId, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_ASSET_TAGS"));
	}

	@Test
	public void testScaleScanWithSeeds() throws SQLException, JAXBException {
		final Map<String, String> queryParams = new HashMap<>();
		final JSONObject scanConfiguration = createScaleScanConfigurationData();

		scanConfiguration.getJSONObject("configuration").put("seeds", new String[] {"http://aoeu"});

		// Create scan configuration with invalid seed
		Response response = post(ScanConfigurationsResource.PATH, Status.BAD_REQUEST, MAINUSER_TOKEN, scanConfiguration.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_URL_INVALID"));

		scanConfiguration.getJSONObject("configuration").remove("seeds");

		// Create scan configuration without seeds
		response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfiguration.toString());
		final long scanConfigurationId = getCreatedId(response);
		assertThat(scanConfigurationId).isGreaterThan(0);

		// Start scan, should fail since we have no seeds
		response = post(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/scan", Status.BAD_REQUEST, MAINUSER_TOKEN);
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_SEED_URLS"));

		// Link to schedule, should fail since we have no seeds
		response = put(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/schedules/" + this.scheduleId, Status.BAD_REQUEST, MAINUSER_TOKEN, "");
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_SEED_URLS"));

		scanConfiguration.getJSONObject("configuration").put("seeds", new String[] {"http://aoeu/"});

		// Patch scan configuration and set seeds
		patch(ScanConfigurationsResource.PATH + "/" + scanConfigurationId, Status.NO_CONTENT, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());

		// Start scan
		response = post(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/scan", Status.OK, MAINUSER_TOKEN);
		assertThat(new JSONObject(response.readEntity(String.class)).getInt("scansStarted")).isEqualTo(1);
		assertThat(DbObject.executeCountQuery(getConnection(), "SELECT COUNT(*) FROM scanlogs WHERE scanconfigurationid = ?", scanConfigurationId)).isEqualTo(1);

		// Link to schedule
		response = put(ScanConfigurationsResource.PATH + "/" + scanConfigurationId + "/schedules/" + this.scheduleId, Status.CREATED, MAINUSER_TOKEN, "");

		scanConfiguration.getJSONObject("configuration").put("seeds", JSONObject.NULL);

		// Patch and remove seeds, should fail since scan configuration is linked to schedule
		response = patch(ScanConfigurationsResource.PATH + "/" + scanConfigurationId, Status.BAD_REQUEST, MAINUSER_TOKEN, queryParams, scanConfiguration.toString());
		assertThat(getErrorResponse(response).getMessage()).isEqualTo(getMessage("_SCAN_CONFIGURATION_MISSING_SEED_URLS"));
	}

	@Test
	public void testScaleScanWithSpaCrawlingSetAndCustomerFeatureOff() throws SQLException, MalformedURLException, JAXBException {
		final JSONObject scanConfiguration = createScaleScanConfigurationData();
		scanConfiguration.getJSONObject("configuration").put("spa-crawling", true);
		final Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, scanConfiguration.toString());
		final long scanConfigurationId = getCreatedId(response);

		DbObject.executeUpdate(getConnection(), "UPDATE customers SET features = ARRAY[?] WHERE id = ?", "", getTestUserCustomerId());
		getConnection().commit();

		final List<Event> eventList = new ArrayList<>();
		final ScanService scanService = new ScanService();
		scanService.setVhost("outpost24.com");
		scanService.setService("https");
		scanService.setPort(443);

		final List<ScanService> scanServices = new ArrayList<>();
		scanServices.add(scanService);

		final StartScanRequest startScanRequest = StartScanRequest.builder()
				.user(LoggedOnUser.getById(LoggedOnUser.class, getConnection(), Access.ADMIN, XMLAPITestBase.TEST_USER_ID))
				.scanConfiguration(ScanConfiguration.getById(ScanConfiguration.class, getConnection(), Access.ADMIN, scanConfigurationId))
				.build();

		final StartScanService startScanService = ServiceProvider.getStartScanService(getConnection(), startScanRequest);
		startScanService.startScaleScan(eventList);
		final ScanStatus status = ScanStatus.getByField(ScanStatus.class, getConnection(), Access.ADMIN, "xsoxid", scanConfigurationId);
		assertThat(status.getSettings()).contains("\"spa-crawling\":false");
	}

	@Test
	public void testNetworkScanConfigurationWithNewConfigs() {
		// Create scan configuration data
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.NETWORK_SCAN);
		configuration.put("timeout", 7200);
		configuration.put("updateAssets", true);
		configuration.put("scanless", false);
		configuration.put("scanPolicyId", customPolicy3);
		configuration.put("anyScanner", true);
		configuration.put("scanAll", true);
		configuration.put("allowCloudAssetWithoutCloudResolver", false);
		configuration.put("maxConcurrentScans", 5);
		configuration.put("maxConcurrentScansPerAsset", 2);

		JSONObject scanConfigurationData = createScanConfigurationData(configuration, ScanTemplate.NETWORK_SCAN);
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationData.toString());
		JSONObject jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		JSONObject configurationJson = jsonResponse.getJSONObject("configuration");
		assertThat(configurationJson.getBoolean("anyScanner")).isTrue();
		assertThat(configurationJson.getBoolean("scanAll")).isTrue();
		assertThat(configurationJson.getBoolean("allowCloudAssetWithoutCloudResolver")).isFalse();
		assertThat(configurationJson.getInt("maxConcurrentScans")).isEqualTo(5);
		assertThat(configurationJson.getInt("maxConcurrentScansPerAsset")).isEqualTo(2);
		final long id = jsonResponse.getLong("id");

		configuration.put("anyScanner", false);
		configuration.put("scanAll", false);
		configuration.put("allowCloudAssetWithoutCloudResolver", true);
		configuration.put("maxConcurrentScans", 10);
		configuration.put("maxConcurrentScansPerAsset", 3);

		scanConfigurationData = createScanConfigurationData(configuration, ScanTemplate.NETWORK_SCAN);
		patch(ScanConfigurationsResource.PATH + "/" + id, Status.NO_CONTENT, MAINUSER_TOKEN, scanConfigurationData.toString());

		response = get(ScanConfigurationsResource.PATH + "/" + id, Status.OK, MAINUSER_TOKEN);
		jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		configurationJson = jsonResponse.getJSONObject("configuration");
		assertThat(configurationJson.getBoolean("anyScanner")).isFalse();
		assertThat(configurationJson.getBoolean("scanAll")).isFalse();
		assertThat(configurationJson.getBoolean("allowCloudAssetWithoutCloudResolver")).isTrue();
		assertThat(configurationJson.getInt("maxConcurrentScans")).isEqualTo(10);
		assertThat(configurationJson.getInt("maxConcurrentScansPerAsset")).isEqualTo(3);
	}

	@Test
	public void testScanConfigurationsWithMaxConcurrentScansConfig() throws SQLException {
		// Test Network Scan Configuration
		testMaxConcurrentScansForConfiguration(createNetworkScanConfigurationData());

		// Test Docker Scan Configuration
		final Account dockerAccount = new Account();
		dockerAccount.setCustomerId((int) getTestUserCustomerId());
		dockerAccount.setName("Docker");
		dockerAccount.setType(AccountType.DOCKER);
		final Integer dockerAccountId = (int) dockerAccount.save(getConnection());
		testMaxConcurrentScansForConfiguration(createDockerScanConfigurationData(dockerAccountId));

		// Test Cloudsec Scan Configuration
		final Account cloudsecAccount = new Account();
		cloudsecAccount.setCustomerId((int) getTestUserCustomerId());
		cloudsecAccount.setName("Cloudsec");
		cloudsecAccount.setType(AccountType.AWS);
		final Integer cloudsecAccountId = (int) cloudsecAccount.save(getConnection());
		Configuration.setProperty(ConfigurationKey.compliance_path, XMLAPITestBase.testPath + "compliance/cloudsec");
		CompliancePolicy.loadPolicies();
		final Long policyId = DbObject.getLong(getConnection(), "SELECT id FROM compliancepolicies WHERE type = ?", CompliancePolicyType.AWS);
		testMaxConcurrentScansForConfiguration(createCloudsecScanConfigurationData(cloudsecAccountId, policyId));
	}

	/**
	 * Test max concurrent configurations for a scan configuration.
	 *
	 * @param scanConfigurationData The scan configuration data.
	 * @throws SQLException If a database error occurs.
	 */
	private void testMaxConcurrentScansForConfiguration(final JSONObject scanConfigurationData) throws SQLException {
		final boolean isCloudsec = scanConfigurationData.get(TEMPLATE) == ScanTemplate.CLOUDSEC;
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("return-result", "true");

		// Create scan configuration
		Response response = post(ScanConfigurationsResource.PATH, Status.CREATED, MAINUSER_TOKEN, queryParams, scanConfigurationData.toString());
		JSONObject jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		JSONObject configurationJson = jsonResponse.getJSONObject("configuration");
		assertThat(configurationJson.getInt("maxConcurrentScans")).isEqualTo(0);
		if (!isCloudsec) {
			assertThat(configurationJson.getInt("maxConcurrentScansPerAsset")).isEqualTo(1);
		}

		// Update scan configuration
		scanConfigurationData.getJSONObject("configuration").put("maxConcurrentScans", 10);
		scanConfigurationData.getJSONObject("configuration").put("maxConcurrentScansPerAsset", 3);
		response = patch(ScanConfigurationsResource.PATH + "/" + jsonResponse.getLong("id"), Status.OK, MAINUSER_TOKEN, queryParams, scanConfigurationData.toString());
		jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		configurationJson = jsonResponse.getJSONObject("configuration");
		assertThat(configurationJson.getInt("maxConcurrentScans")).isEqualTo(10);
		if (!isCloudsec) {
			assertThat(configurationJson.getInt("maxConcurrentScansPerAsset")).isEqualTo(3);
		}

		// Get scan configuration
		response = get(ScanConfigurationsResource.PATH + "/" + jsonResponse.getLong("id"), Status.OK, MAINUSER_TOKEN);
		jsonResponse = new JSONObject(response.readEntity(String.class));
		assertThat(jsonResponse.has("configuration")).isTrue();
		configurationJson = jsonResponse.getJSONObject("configuration");
		assertThat(configurationJson.getInt("maxConcurrentScans")).isEqualTo(10);
		if (!isCloudsec) {
			assertThat(configurationJson.getInt("maxConcurrentScansPerAsset")).isEqualTo(3);
		}
	}

	/**
	 * Creates a JSON object with Scale scan configuration data.
	 *
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createScaleScanConfigurationData() {
		final JSONObject configuration = createScaleConfiguration();
		return createScanConfigurationData(configuration, ScanTemplate.SCALE);
	}

	/**
	 * Creates a JSON object with Cloud Discovery scan configuration data.
	 *
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createCloudDiscoveryScanConfigurationData() throws SQLException {
		return createScanConfigurationData(createCloudDiscoveryConfiguration(), ScanTemplate.CLOUD_DISCOVERY);
	}

	/**
	 * Creates a JSON object with Network Discovery scan configuration data.
	 *
	 * @param targets The target list
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createNetworkDiscoveryScanConfigurationData(final String targets) throws SQLException {
		return createScanConfigurationData(createNetworkDiscoveryConfiguration(targets), ScanTemplate.NETWORK_DISCOVERY);
	}

	/**
	 * Creates a JSON object with Network Discovery scan configuration data.
	 *
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createNetworkScanConfigurationData() {
		return createScanConfigurationData(createNetworkScanConfiguration(), ScanTemplate.NETWORK_SCAN);
	}

	/**
	 * Creates a JSON object with Docker scan configuration data.
	 *
	 * @param accountId id of the account.
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createDockerScanConfigurationData(final Integer accountId) {
		final JSONObject configuration = createDockerScanConfiguration(accountId);
		return createScanConfigurationData(configuration, ScanTemplate.DOCKER_SCAN);
	}

	/**
	 * Creates a JSON object with Cloudsec scan configuration data.
	 *
	 * @param accountId id of the account.
	 * @param policyId id of the policy.
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createCloudsecScanConfigurationData(final Integer accountId, final Long policyId) {
		return createScanConfigurationData(createCloudsecScanConfiguration(accountId, policyId), ScanTemplate.CLOUDSEC);
	}

	/**
	 * Creates a JSON object with scan configuration data.
	 *
	 * @param configuration configuration of the template.
	 * @param scanTemplate template type.
	 * @return JSON object for scan configuration.
	 */
	private JSONObject createScanConfigurationData(final JSONObject configuration, final ScanTemplate scanTemplate) {
		final JSONObject scanConfigurationData = new JSONObject();
		scanConfigurationData.put(NAME, "testConfig");
		scanConfigurationData.put(TEMPLATE, scanTemplate);
		scanConfigurationData.put(CONFIGURATION, configuration);
		scanConfigurationData.put(CUSTOMER_ID, getTestUserCustomerId());
		return scanConfigurationData;
	}

	/**
	 * Creates a JSON object with Scale configuration data.
	 *
	 * @return JSON object for configuration.
	 */
	private JSONObject createScaleConfiguration() {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.SCALE);
		final List<String> seeds = new ArrayList<>();
		seeds.add("https://aoeu.com/");
		configuration.put(SEEDS, seeds);
		configuration.put(NSECONDS, 600);
		configuration.put(FUZZING, true);
		configuration.put(CLIENT_CERTIFICATE_AUTH, new JSONObject("{client-certificate:\"MyCertificate\", private-key:\"MyKey\"}"));
		configuration.put(ABORT_PATTERN, "aoe?");
		configuration.put(MUST_MATCH, new JSONArray("[{method:\"aoe?\"}]"));
		configuration.put(CANNOT_MATCH, new JSONArray("[{method:\"aoe?\"}]"));
		configuration.put(ALLOWED_DOMAINS, new JSONArray("[{domain:\"www.aoeu.com\", allow-subdomains:true}]"));
		configuration.put(ADDR_BLACKLIST, new JSONArray("[\"***********/32\", \"www.aoeu.com\", \"***********/30\"]"));
		configuration.put(BASIC_AUTH, new JSONObject("{user:\"username\", password:\"password\", url-prefix:\"https://aoeu.com/\"}"));
		configuration.put(HOST_MAP, new JSONArray("[{from:\"username\", to:[\"10.0.0.0\"]}]"));
		configuration.put(INFRASTRUCTURE_SCAN, true);
		configuration.put(SPA_CRAWLING, true);
		return configuration;
	}

	/**
	 * Creates a JSON object with Cloud Discovery configuration data.
	 *
	 * @return JSON object for configuration.
	 */
	private JSONObject createCloudDiscoveryConfiguration() throws SQLException {
		final Account account = new Account();
		account.setCustomerId((int) getTestUserCustomerId());
		account.setName("AWS");
		account.setType(AccountType.AWS);
		final Integer accountId = (int) account.save(getConnection());

		final Credential credential = new Credential();
		credential.setCustomerId((int) getTestUserCustomerId());
		credential.setAccountId(accountId);
		credential.setClassId(CredentialClassType.ROLE_ARN.getId());
		credential.setValue("arn");
		credential.save(getConnection());

		final JSONObject awsDetailConfig = new JSONObject();
		awsDetailConfig.put("cloudDiscoveryType", "AWS");
		awsDetailConfig.put("regions", "eu-central-1,us-east-2");

		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.CLOUD_DISCOVERY);
		configuration.put("timeout", 600);
		configuration.put("accountId", accountId);
		configuration.put("cloudConfiguration", awsDetailConfig);
		configuration.put("importExternalTags", true);
		configuration.put("externalTagKeys", new String[] {"location"});
		return configuration;
	}

	/**
	 * Creates a JSON object with Network Discovery configuration data.
	 *
	 * @param targets The targets for the network discovery
	 * @return JSON object for configuration.
	 */
	private JSONObject createNetworkDiscoveryConfiguration(final String targets) throws SQLException {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.NETWORK_DISCOVERY);
		configuration.put("timeout", 43200);
		configuration.put("protocols", new NetworkDiscoveryProtocol[] {NetworkDiscoveryProtocol.TCP});
		configuration.put("updateAssets", true);
		configuration.put("tcpPorts", "def");
		configuration.put("targetList", targets);
		return configuration;
	}

	/**
	 * Creates a JSON object with Network Scan configuration data.
	 *
	 * @return JSON object for configuration.
	 */
	private JSONObject createNetworkScanConfiguration() {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.NETWORK_SCAN);
		configuration.put("timeout", 7200);
		configuration.put("updateAssets", true);
		configuration.put("scanless", false);
		configuration.put("scanPolicyId", customPolicy3);
		configuration.put("anyScanner", false);
		configuration.put("scanAll", false);
		configuration.put("allowCloudAssetWithoutCloudResolver", false);
		return configuration;
	}

	/**
	 * Creates a JSON object with DockerScan configuration data.
	 *
	 * @param accountId id of the account.
	 * @return JSON object for configuration.
	 */
	private JSONObject createDockerScanConfiguration(final Integer accountId) {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.DOCKER_SCAN);
		configuration.put(ACCOUNT, accountId);
		return configuration;
	}

	/**
	 * Creates a JSON object with Cloudsec Scan configuration data.
	 *
	 * @param accountId id of the account.
	 * @param policyId id of the policy.
	 * @return JSON object for configuration.
	 */
	private JSONObject createCloudsecScanConfiguration(final Integer accountId, final Long policyId) {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.CLOUDSEC);
		configuration.put("timeout", 7200);
		configuration.put("accountId", accountId);
		configuration.put("policyId", policyId);
		configuration.put("regions", "eu-central-1");
		return configuration;
	}
}
