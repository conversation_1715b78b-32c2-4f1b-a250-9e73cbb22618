package com.chilicoders.rest.resources;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.util.StringUtils;

public class AuditsResourceTest extends BaseResourceTest {

	private long auditId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();
		this.auditId =
				DbObject.executeCountQuery(getConnection(), "INSERT INTO audits (customerid, objectname, objectid) VALUES(?, ?, ?) RETURNING id", getAdminUserCustomerId(),
						"findings", 1);
		getConnection().commit();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM audits WHERE id = ?", this.auditId);
		getConnection().commit();
	}

	@Test
	public void testGetAuditHeader() {
		final String totalCount = head(AuditsResource.PATH, new HashMap<>(), ADMIN_TOKEN);
		assertTrue(StringUtils.getLongValue(totalCount) > 1);
	}

	@Test
	public void testGetAuditList() {
		final JSONArray jsonArray = getListFromResource(AuditsResource.PATH, "id,objectname,objectid", ADMIN_TOKEN);
		assertTrue(jsonArray.length() > 1);
	}

	@Test
	public void testGetAudit() {
		final Map<String, String> queryParams = new HashMap<>();
		queryParams.put("fields", "id,objectname,objectid");

		// Get audit that does not exist
		Response response = get(AuditsResource.PATH + "/0", Status.NOT_FOUND, ADMIN_TOKEN, queryParams);
		assertEquals(getMessage("_RESOURCE_NOT_FOUND"), getErrorResponse(response).getMessage());

		// Get audit
		response = get(AuditsResource.PATH + "/" + this.auditId, Status.OK, ADMIN_TOKEN, queryParams);
		final JSONObject jsonObject = new JSONObject(response.readEntity(String.class));
		assertEquals(this.auditId, jsonObject.getInt("id"));
	}
}
