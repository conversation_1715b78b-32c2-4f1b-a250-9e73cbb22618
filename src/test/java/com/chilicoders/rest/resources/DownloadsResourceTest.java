package com.chilicoders.rest.resources;

import static com.chilicoders.xmlapi.XMLAPITestBase.ADMIN_USER_ID;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertNotNull;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;

import org.apache.commons.io.FileUtils;
import org.json.JSONArray;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.ContentDisposition;

import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.reporting.api.model.ReportStatus;
import com.chilicoders.core.storage.api.model.AdditionalData;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.model.DownloadEntryType;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.xmlapi.XMLAPITestBase;

import io.findify.s3mock.S3Mock;

public class DownloadsResourceTest extends BaseResourceTest {
	private static final String AGENT_UUID = "146d3aa8-7b94-4bcc-9e87-ff8ee2ebb3fa";
	private static final String DOWNLOAD_PATH = DownloadsResource.PATH + "/" + DownloadsResource.DOWNLOAD_PATH;
	private static final String INSTALLER_FILE_NAME = "o24-agent-installer.rpm";
	private static final String INSTALLER_CONTENT = "agent-installer";

	static {
		XMLAPITestBase.initConfiguration();
	}

	/**
	 * Setup before each test.
	 */
	@Before
	public void init() {
		setupDatabase();
	}

	@Test
	public void testDownloadAgentInstaller() throws IOException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		enableAgent(XMLAPITestBase.ADMIN_USER_ID);

		final File file = File.createTempFile("agent-installer", ".rpm");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), UTF_8)) {
			writer.write(INSTALLER_CONTENT);
		}

		final S3Mock s3Mock = S3Mock.create(0);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "http://localhost:" + s3Mock.start().localAddress().getPort());
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, XMLAPITestBase.DUMMY_KEY);
		ServiceProvider.getStorageService().resetS3();

		try {
			final Customer customer = Customer.getById(Customer.class, getConnection(), Access.ADMIN,
					getTestUserCustomerId());
			final ResourceIdentifier resourceId = TenantResourceIdentifier.builder().tenantUuid(customer.getUuid())
					.domain(Domain.AGENT_INSTALLERS).key(AGENT_UUID).build();
			if (!ServiceProvider.getStorageService().doesBucketExist(resourceId)) {
				ServiceProvider.getStorageService().createBucket(resourceId);
			}
			ServiceProvider.getStorageService().storeResource(resourceId, file, AdditionalData.type(AdditionalData.FileTypes.INSTALLER));

			DbObject.executeCountQuery(getConnection(),
					"INSERT INTO downloadentries (userid, subuserid, filename, readablename, key, type, status) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
					XMLAPITestBase.ADMIN_USER_ID, -1, resourceId.toString(), INSTALLER_FILE_NAME, AGENT_UUID,
					DownloadEntryType.AGENT_INSTALLER, ReportStatus.DONE.getStatus());
			getConnection().commit();

			Response response = get(DownloadsResource.PATH + "/" + AGENT_UUID, Response.Status.OK, ADMIN_TOKEN);
			final String token = response.readEntity(String.class);
			assertNotNull(token);

			response = get(DOWNLOAD_PATH + "/" + token, Response.Status.OK, ADMIN_TOKEN);
			final String disposition = response.getHeaderString(HttpHeaders.CONTENT_DISPOSITION);
			assertThat(disposition).isNotEmpty();
			final String readableName = ContentDisposition.parse(disposition).getFilename();
			assertThat(readableName).isEqualTo(INSTALLER_FILE_NAME);
			assertThat(response.readEntity(String.class)).isEqualTo(INSTALLER_CONTENT);
		}
		finally {
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "");
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, "");
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, "");
			ServiceProvider.getStorageService().resetS3();
			s3Mock.shutdown();
			Configuration.setProperty(ConfigKeys.ConfigurationKey.agent_api_url, "");

			DbObject.executeUpdate(getConnection(), "UPDATE tusers SET agentsenabled = false WHERE xid = ?", ADMIN_USER_ID);
			getConnection().commit();

			FileUtils.deleteQuietly(file);
		}
	}

	@Test
	public void testDownloadReportFromS3() throws IOException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		final String reportContent = "abc";
		final String reportCacheUuid = "a547d716-811b-4a86-b27a-56f3febea97e";
		final String reportFilename = "o24-report.txt";

		final File file = File.createTempFile("report", ".txt");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), UTF_8)) {
			writer.write(reportContent);
		}

		final S3Mock s3Mock = S3Mock.create(0);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "http://localhost:" + s3Mock.start().localAddress().getPort());
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, XMLAPITestBase.DUMMY_KEY);
		ServiceProvider.getStorageService().resetS3();

		try {
			final Customer customer = Customer.getById(Customer.class, getConnection(), Access.ADMIN, getAdminUserCustomerId());
			final ResourceIdentifier resourceId = TenantResourceIdentifier.builder().tenantUuid(customer.getUuid())
					.domain(Domain.CACHE).key(reportCacheUuid).build();
			if (!ServiceProvider.getStorageService().doesBucketExist(resourceId)) {
				ServiceProvider.getStorageService().createBucket(resourceId);
			}
			ServiceProvider.getStorageService().storeResource(resourceId, file, null);

			DbObject.executeCountQuery(getConnection(),
					"INSERT INTO downloadentries (userid, subuserid, filename, readablename, key, type, status) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
					XMLAPITestBase.ADMIN_USER_ID, -1, "s3://" + resourceId, reportFilename, reportCacheUuid,
					DownloadEntryType.REPORT, ReportStatus.DONE.getStatus());
			getConnection().commit();

			Response response = get(DownloadsResource.PATH + "/" + reportCacheUuid, Response.Status.OK, ADMIN_TOKEN);
			final String token = response.readEntity(String.class);
			assertNotNull(token);

			response = get(DOWNLOAD_PATH + "/" + token, Response.Status.OK, ADMIN_TOKEN);
			assertThat(response.readEntity(String.class)).isEqualTo(reportContent);
		}
		finally {
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "");
			ServiceProvider.getStorageService().resetS3();
			s3Mock.shutdown();

			FileUtils.deleteQuietly(file);
		}
	}

	@Test
	public void testDownloadBlueprintFromFileSystem() throws IOException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		final String blueprintContent = "abc";
		final String blueprintFilename = "o24-blueprint.txt";
		final String blueprintCacheUuid = "544a54e4-1e6b-418f-b594-b349c8b6dbcc";

		final File file = File.createTempFile("blueprint", ".txt");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), UTF_8)) {
			writer.write(blueprintContent);
		}

		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "");
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_ssec_key, "");

		try {
			DbObject.executeCountQuery(getConnection(),
					"INSERT INTO downloadentries (userid, subuserid, filename, readablename, key, type, status) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
					XMLAPITestBase.ADMIN_USER_ID, -1, file.getPath(), blueprintFilename, blueprintCacheUuid,
					DownloadEntryType.BLUEPRINT, ReportStatus.DONE.getStatus());
			getConnection().commit();

			Response response = get(DownloadsResource.PATH + "/" + blueprintCacheUuid, Response.Status.OK, ADMIN_TOKEN);
			final String token = response.readEntity(String.class);
			assertNotNull(token);

			response = get(DOWNLOAD_PATH + "/" + token, Response.Status.OK, ADMIN_TOKEN);
			assertThat(response.readEntity(String.class)).isEqualTo(blueprintContent);
		}
		finally {
			FileUtils.deleteQuietly(file);
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, "");
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, "");
		}
	}

	@Test
	public void testDownloadBlueprintFromS3() throws IOException, SQLException {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			return;
		}
		final String blueprintContent = "abc";
		final String blueprintCacheUuid = "a547d716-811b-4a86-b27a-56f3febea97f";
		final String blueprintFilename = "o24-blueprint.txt";

		final File file = File.createTempFile("blueprint", ".txt");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), UTF_8)) {
			writer.write(blueprintContent);
		}

		final S3Mock s3Mock = S3Mock.create(0);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "http://localhost:" + s3Mock.start().localAddress().getPort());
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, XMLAPITestBase.DUMMY_KEY);
		Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_ssec_key, "");
		ServiceProvider.getStorageService().resetS3();

		try {
			final Customer customer = Customer.getById(Customer.class, getConnection(), Access.ADMIN, getAdminUserCustomerId());
			final ResourceIdentifier resourceId = TenantResourceIdentifier.builder().tenantUuid(customer.getUuid())
					.domain(Domain.BLUEPRINTS).key(blueprintCacheUuid).build();
			if (!ServiceProvider.getStorageService().doesBucketExist(resourceId)) {
				ServiceProvider.getStorageService().createBucket(resourceId);
			}
			ServiceProvider.getStorageService().storeResource(resourceId, file, null);

			DbObject.executeCountQuery(getConnection(),
					"INSERT INTO downloadentries (userid, subuserid, filename, readablename, key, type, status) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING id",
					XMLAPITestBase.ADMIN_USER_ID, -1, "s3://" + resourceId, blueprintFilename, blueprintCacheUuid,
					DownloadEntryType.BLUEPRINT, ReportStatus.DONE.getStatus());
			getConnection().commit();

			Response response = get(DownloadsResource.PATH + "/" + blueprintCacheUuid, Response.Status.OK, ADMIN_TOKEN);
			final String token = response.readEntity(String.class);
			assertNotNull(token);

			response = get(DOWNLOAD_PATH + "/" + token, Response.Status.OK, ADMIN_TOKEN);
			assertThat(response.readEntity(String.class)).isEqualTo(blueprintContent);
		}
		finally {
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint, "");
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_access_key, "");
			Configuration.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key, "");
			ServiceProvider.getStorageService().resetS3();
			s3Mock.shutdown();

			FileUtils.deleteQuietly(file);
		}
	}

	@Test
	public void testFilteringDownloadsByCacheIdAndType() throws SQLException {
		final String cacheId = "5b1df156-0fee-4608-a443-e2ab944d894f";
		DbObject.executeCountQuery(getConnection(),
				"INSERT INTO downloadentries (userid, subuserid, key, type) VALUES(?, ?, ?, ?) RETURNING id",
				XMLAPITestBase.ADMIN_USER_ID, -1, cacheId, DownloadEntryType.BLUEPRINT);
		getConnection().commit();

		final Map<String, String> queryParams = new HashMap<>();

		final String filterWithKeyAndBlueprintType =
				String.format("[{\"field\":\"key\",\"value\":\"%s\",\"comparison\":\"eq\"},{\"field\":\"type\",\"value\":\"%s\",\"comparison\":\"eq\"}]", cacheId,
						DownloadEntryType.BLUEPRINT.name());
		queryParams.put("filter", filterWithKeyAndBlueprintType);
		JSONArray jsonArray = getListFromResource(DownloadsResource.PATH, queryParams, ADMIN_TOKEN);
		assertThat(jsonArray).hasSize(1);

		queryParams.clear();
		final String filterWithKeyAndReportType =
				String.format("[{\"field\":\"key\",\"value\":\"%s\",\"comparison\":\"eq\"},{\"field\":\"type\",\"value\":\"%s\",\"comparison\":\"eq\"}]", cacheId,
						DownloadEntryType.REPORT.name());
		queryParams.put("filter", filterWithKeyAndReportType);
		jsonArray = getListFromResource(DownloadsResource.PATH, queryParams, ADMIN_TOKEN);
		assertThat(jsonArray).isEmpty();

		queryParams.clear();
		final String filterWithEmptyKeyAndBlueprintType =
				String.format("[{\"field\":\"key\",\"value\":\"%s\",\"comparison\":\"eq\"},{\"field\":\"type\",\"value\":\"%s\",\"comparison\":\"eq\"}]", "",
						DownloadEntryType.BLUEPRINT.name());
		queryParams.put("filter", filterWithEmptyKeyAndBlueprintType);
		jsonArray = getListFromResource(AssetIdentifiersResource.PATH, queryParams, ADMIN_TOKEN);
		assertThat(jsonArray).isEmpty();

		queryParams.clear();
		final String filterWithWrongKeyAndBlueprintType =
				String.format("[{\"field\":\"key\",\"value\":\"%s\",\"comparison\":\"eq\"},{\"field\":\"type\",\"value\":\"%s\",\"comparison\":\"eq\"}]", "wrong_key",
						DownloadEntryType.BLUEPRINT.name());
		queryParams.put("filter", filterWithWrongKeyAndBlueprintType);
		jsonArray = getListFromResource(AssetIdentifiersResource.PATH, queryParams, ADMIN_TOKEN);
		assertThat(jsonArray).isEmpty();
	}

	/**
	 * clean up
	 *
	 * @throws SQLException exception
	 */
	@After
	public void teardown() throws SQLException {
		DbObject.execute(getConnection(), "DELETE FROM downloadentries");
		getConnection().commit();
	}

	/**
	 * Enable agent feature for user
	 *
	 * @param userId Id of the user
	 * @throws SQLException exception
	 */
	private void enableAgent(final long userId) throws SQLException {
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET agentsenabled = true WHERE xid = ?", userId);
		getConnection().commit();
	}
}
