package com.chilicoders.event.service.handler;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

import javax.activation.DataSource;

import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.storage.api.StorageService;
import com.chilicoders.core.storage.api.model.AdditionalData;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.event.BaseTest;
import com.chilicoders.model.Event;

import io.findify.s3mock.S3Mock;

public class EmailHandlerTest extends BaseTest {
	@Autowired
	private EmailHandler emailHandler;

	@Autowired
	private ConfigurationService configService;

	@Autowired
	private StorageService storageService;

	@Test
	public void testAddFileToDataSources() throws IOException {
		if (configService.isKubernetesEnabled()) {
			return;
		}
		final String tenantUuid = "0b5aa3bf-0e2c-46cd-96e5-2935cc63f4b2";
		final String fileName1 = "test1.gpg";
		final String fileName2 = "test2.gpg";
		final String key1 = "4d17bd0f-4a23-45a0-abff-23772fbb0a65";
		final String key2 = "4d17bd0f-4a23-45a0-abff-23772fbb0a66";
		final Map<String, DataSource> dataSources = new HashMap<>();
		final Event event = new Event();

		final String reportContent1 = "abc";
		final File file1 = File.createTempFile("report1", ".txt");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file1.toPath()), UTF_8)) {
			writer.write(reportContent1);
		}
		final String reportContent2 = "abcd";
		final File file2 = File.createTempFile("report2", ".txt");
		try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(file2.toPath()), UTF_8)) {
			writer.write(reportContent2);
		}

		final Map<String, String> files = new HashMap<>();
		files.put(fileName1, "s3://cache/" + key1);
		files.put(fileName2, "s3://cache/" + key2);

		final S3Mock s3Mock = S3Mock.create(0);
		configService.setProperty(ConfigKeys.ConfigurationKey.s3_access_key.getConfigKey(), "dummy-access-key");
		configService.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key.getConfigKey(), "dummy-secret-key");
		configService.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint.getConfigKey(),
				"http://localhost:" + s3Mock.start().localAddress().getPort());
		configService.setProperty(ConfigKeys.ConfigurationKey.s3_ssec_key.getConfigKey(), "");
		storageService.resetS3();

		try {
			final ResourceIdentifier identifier1 = TenantResourceIdentifier.builder()
					.key(key1)
					.domain(Domain.CACHE)
					.tenantUuid(tenantUuid)
					.build();
			if (!storageService.doesBucketExist(identifier1)) {
				storageService.createBucket(identifier1);
			}

			final ResourceIdentifier identifier2 = TenantResourceIdentifier.builder()
					.key(key2)
					.domain(Domain.CACHE)
					.tenantUuid(tenantUuid)
					.build();
			if (!storageService.doesBucketExist(identifier2)) {
				storageService.createBucket(identifier2);
			}
			storageService.storeResource(identifier1, file1, AdditionalData.type(AdditionalData.FileTypes.REPORT));
			storageService.storeResource(identifier2, file2, AdditionalData.type(AdditionalData.FileTypes.REPORT));

			assertThat(storageService.doesResourceExist(identifier1)).isTrue();
			assertThat(storageService.doesResourceExist(identifier2)).isTrue();

			files.forEach((name, url) -> emailHandler.addFileToDataSources(url, name, tenantUuid, event, dataSources));
			assertThat(dataSources).hasSize(files.size());
			assertThat(storageService.doesResourceExist(identifier1)).isFalse();
			assertThat(storageService.doesResourceExist(identifier2)).isFalse();
		}
		finally {
			configService.setProperty(ConfigKeys.ConfigurationKey.s3_endpoint.getConfigKey(), "");
			configService.setProperty(ConfigKeys.ConfigurationKey.s3_access_key.getConfigKey(), "");
			configService.setProperty(ConfigKeys.ConfigurationKey.s3_secret_key.getConfigKey(), "");
			storageService.resetS3();
			s3Mock.shutdown();

			FileUtils.deleteQuietly(file1);
			FileUtils.deleteQuietly(file2);
		}
	}
}
