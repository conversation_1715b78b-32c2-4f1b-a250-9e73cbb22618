package com.chilicoders.core.scheduling.api.model;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.chilicoders.model.ScanTypes;

public class ScanResultTest {

	@Test
	public void testScanResult() {
		assertEquals(ScanTypes.Ok, ScanResult.OK.getScanType(false, false, false));
		assertEquals(ScanTypes.TargetUpdateFailed, ScanResult.Failed.getScanType(false, true, false));
		assertEquals(ScanTypes.ScanWindowPause, ScanResult.PausedScanWindow.getScanType(false, false, true));
		assertEquals(ScanTypes.Invalid, ScanResult.Unknown.getScanType(false, false, false));
	}

}
