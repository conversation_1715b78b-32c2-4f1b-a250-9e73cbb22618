package com.chilicoders.core.common.sql;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.RETURNS_SMART_NULLS;
import static org.mockito.Mockito.mock;

import java.util.Arrays;

import org.junit.Test;

import com.chilicoders.core.common.sql.GetFindingDeltaSql.GetFindingDeltaSqlBuilder;
import com.chilicoders.core.user.api.UserDetails;

public class GetFindingDeltaSqlTest {
	private final UserDetails user = mock(UserDetails.class, RETURNS_SMART_NULLS);

	@Test
	public void testBuildSql() {
		final GetFindingDeltaSqlBuilder sql = GetFindingDeltaSql.builder()
				.user(this.user);

		assertThat(sql.build().getSQL()).contains("SELECT id, asset, firstseen, lastseen, unchanged");

		sql.rbacFilter("test-filter1 = ?");
		sql.rbacParams(Arrays.asList("test-param1"));
		assertThat(sql.build().getSQL()).contains("AND test-filter1 = ?");

		sql.filter("test-filter2 = ?");
		sql.params(Arrays.asList("test-param2"));
		assertThat(sql.build().getSQL()).contains("AND test-filter2 = ?");

		assertThat(sql.build().getReturnAliases()).hasSize(5);
	}
}
