package com.chilicoders.core.common.sql;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.RETURNS_SMART_NULLS;
import static org.mockito.Mockito.mock;

import org.junit.Test;

import com.chilicoders.api.reportexport.ReportExportRequest;
import com.chilicoders.core.common.sql.GetComplianceTargetIdsSql.GetComplianceTargetIdsSqlBuilder;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.model.Product;

public class GetComplianceTargetIdsSqlTest {
	private final UserDetails user = mock(UserDetails.class, RETURNS_SMART_NULLS);

	@Test
	public void testBuildSql() {
		final GetComplianceTargetIdsSqlBuilder sql = GetComplianceTargetIdsSql.builder()
				.user(this.user);

		final ReportExportRequest request = new ReportExportRequest();
		sql.criteria(request);

		assertThat(sql.build().getSQL()).endsWith("AS targets");

		request.setScanJobIds(new Long[] {1L});
		request.setProduct(Product.Was);

		assertThat(sql.build().getSQL()).contains("hasWebGranted");

		request.setProduct(Product.Normal);
		assertThat(sql.build().getSQL()).contains("FROM vscanlog sl LEFT JOIN treportentrys");

		sql.complianceReportId(1);
		assertThat(sql.build().getSQL()).contains("WHEN compliant");

		request.setTargetGroupIds(new Long[] {1L});;
		assertThat(sql.build().getSQL()).contains("sl.xipxid =ANY ((SELECT ARRAY_AGG(xipxid) FROM xlinkgeneric");
	}
}
