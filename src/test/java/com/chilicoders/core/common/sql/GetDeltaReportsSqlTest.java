package com.chilicoders.core.common.sql;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.Test;

import com.chilicoders.core.common.sql.GetDeltaReportsSql.GetDeltaReportsSqlBuilder;

public class GetDeltaReportsSqlTest {
	@Test
	public void testBuildSql() {
		final GetDeltaReportsSqlBuilder sql = GetDeltaReportsSql.builder()
				.ports(false);

		assertThat(sql.build().getSQL()).contains("SELECT *, 0 AS port");

		sql.ports(true);
		assertThat(sql.build().getSQL()).contains("SELECT *, 1 AS port");

		sql.filter("test-filter = ?");
		assertThat(sql.build().getSQL()).contains("WHERE test-filter = ?");

		sql.orderBy("ORDER BY test");
		assertThat(sql.build().getSQL()).contains("ORDER BY test");
	}
}
