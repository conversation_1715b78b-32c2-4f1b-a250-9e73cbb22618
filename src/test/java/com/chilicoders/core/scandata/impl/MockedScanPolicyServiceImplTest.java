package com.chilicoders.core.scandata.impl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.scandata.api.ScanPolicyDao;
import com.chilicoders.core.scandata.api.model.CorePreference;
import com.chilicoders.core.scandata.api.model.CorePreferenceInterface;

@RunWith(MockitoJUnitRunner.class)
public class MockedScanPolicyServiceImplTest {
	@Mock
	private ScanPolicyDao scanPolicyDao;

	@Mock
	private ConfigurationService configService;

	@InjectMocks
	private ScanPolicyServiceImpl scanPolicyService;

	@Test
	public void testInitSettings() throws SQLException, IOException {
		final List<CorePreferenceInterface> prefs = new ArrayList<>();
		final CorePreference pref = new CorePreference();
		pref.setKey("test");
		pref.setGroup("testgroup");
		prefs.add(pref);
		Mockito.doReturn(prefs).when(scanPolicyDao).getCorePreferences();
		when(scanPolicyDao.createCorePreference()).thenReturn(new CorePreference());
		final File tmpFile = createWasSettingsFile();
		try {
			when(configService.getProperty(ConfigurationKey.resources_folder)).thenReturn(tmpFile.getParent());
			scanPolicyService.initCorePreferences(null, true);
			assertTrue(scanPolicyService.getDefaultCorePreferences().containsKey("global_latestchecks"));
			assertTrue(scanPolicyService.getDefaultCorePreferences().containsKey("was_enabled"));
		}
		finally {
			FileUtils.deleteQuietly(tmpFile);
		}
	}

	@Test
	public void testConvertScript() throws SQLException, IOException {
		final List<CorePreferenceInterface> prefs = new ArrayList<>();
		final CorePreference pref = new CorePreference();
		pref.setKey("test");
		pref.setGroup("testgroup");
		pref.setValue("no");
		pref.setType("checkbox");
		prefs.add(pref);
		Mockito.doReturn(prefs).when(scanPolicyDao).getCorePreferences();
		when(scanPolicyDao.createCorePreference()).thenReturn(new CorePreference());
		final File tmpFile = createWasSettingsFile();
		when(configService.getProperty(ConfigurationKey.resources_folder)).thenReturn(tmpFile.getParent());
		final Map<String, CorePreferenceInterface> result = scanPolicyService.convertPlugins("<prefset name=\"testgroup/test\"><data value=\"yes\"/></prefset>", true);
		assertTrue(result.containsKey("testgroup_test"));
		assertEquals("no", result.get("testgroup_test").getDefaultValue());
		assertEquals("yes", result.get("testgroup_test").getValue());
	}

	/**
	 * Create a was settings file for testing.
	 *
	 * @return File created
	 */
	public static File createWasSettingsFile() throws IOException {
		final File tmpFile = new File("/tmp/SettingsWas.xml");
		FileUtils.deleteQuietly(tmpFile);
		try (final Writer fw = new OutputStreamWriter(Files.newOutputStream(tmpFile.toPath()), UTF_8)) {
			fw.write("<prefset name=\"was/enabled\" nelem=\"1\"><data value=\"no\"/></prefset>\n" +
					"<prefset name=\"was/disabled\" nelem=\"1\"><data value=\"no\"/></prefset>\n" +
					"<prefset name=\"was/only\" nelem=\"1\"><data value=\"no\"/></prefset>");
		}
		return tmpFile;
	}
}
