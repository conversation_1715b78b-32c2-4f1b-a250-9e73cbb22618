package com.chilicoders.core.scandata.impl;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.chilicoders.core.scandata.impl.jpa.repository.RunningVerifyScanRepository;
import com.chilicoders.core.scheduling.api.model.RunningVerifyScan;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.test.TestUtil;
import com.chilicoders.test.model.TestReportEntry;
import com.chilicoders.test.model.TestReportVulnerability;

public class VerifyScanTest extends BaseSpringTestWithRollback {
	@Autowired
	private RunningVerifyScanRepository verifyScanRepository;

	@Autowired
	private TestUtil testUtil;

	private static final int USER_ID = 8;

	@Test
	public void testLoadData() throws SQLException {
		final int verifyId = 12;

		testUtil.insertUser(USER_ID, "Test");
		assertThat(verifyScanRepository.hasVerifyScanRunning(USER_ID, verifyId)).isFalse();
		final TestReportEntry reportEntry = TestReportEntry.builder().userId(USER_ID).build();
		final long reportEntryId = testUtil.insertReportEntry(reportEntry);
		final long findingId = testUtil.insertReportVulnerability(TestReportVulnerability.builder().reportEntryId(reportEntryId).build());
		testUtil.executeSql("INSERT INTO tscanstatuss(xid, xuserxid, xsoxid, xscanjobxid, vcservice) VALUES (1, ?, 0, 0, 'O')", USER_ID);
		testUtil.executeSql("INSERT INTO tverifys(xid, xuserxid, treport_vulns_xid, tscanstatuss_xid) VALUES (?, ?, ?, 1)", verifyId, USER_ID, findingId);
		testUtil.getStatementExec().commitTransaction();
		assertThat(verifyScanRepository.hasVerifyScanRunning(USER_ID, verifyId)).isTrue();
		final List<RunningVerifyScan> verify = verifyScanRepository.getByUserIdAndId(USER_ID, verifyId);
		assertThat(verify).hasSize(1);
		assertThat(verify.get(0).isRunning()).isTrue();
		assertThat(verify.get(0).isStillPresent()).isTrue();
	}
}
