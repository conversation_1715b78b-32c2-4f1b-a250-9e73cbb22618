package com.chilicoders.core.integrations;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import org.junit.Test;
import org.mockito.Mock;
import org.snmp4j.smi.OID;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.integrations.Snmp;
import com.chilicoders.integrations.exceptions.SnmpException;
import com.chilicoders.test.server.LocalServer;
import com.chilicoders.test.server.LocalServerCallback;

public class SnmpTest extends BaseSpringTestWithRollback {
	@Mock
	private ConfigurationService configurationService;

	private volatile byte[] message;

	@Test
	public void testSnmp() throws SnmpException, IOException {
		final int localPort = startLocalServer(LocalServer.TYPE_UDP, new LocalServerCallback() {
			public void handleRequest(final DatagramSocket socket, final DatagramPacket packet) throws IOException {
				final byte[] data = new byte[packet.getLength()];
				System.arraycopy(packet.getData(), 0, data, 0, packet.getLength());
				message = data;
			}
		});

		try {
			when(configurationService.getProperty(ConfigurationIntKey.hiab_snmp_reboots)).thenReturn(0);
			when(configurationService.getProperty(ConfigurationKey.hiab_snmp_engineid)).thenReturn("0x80000d2f03005056ab2160");
			when(configurationService.getProperty(ConfigurationIntKey.hiab_snmp_version)).thenReturn(3);
			when(configurationService.getProperty(ConfigurationKey.hiab_snmp_user)).thenReturn("test");
			when(configurationService.getProperty(ConfigurationKey.hiab_snmp_password)).thenReturn("test-password");
			when(configurationService.getProperty(ConfigurationKey.hiab_snmp_host)).thenReturn("127.0.0.1");
			when(configurationService.getProperty(ConfigurationKey.hiab_snmp_community, "public")).thenReturn("test");
			when(configurationService.getProperty(ConfigurationIntKey.hiab_snmp_port)).thenReturn(localPort);

			try (final Snmp snmp = Snmp.getInstance(configurationService)) {
				message = null;
				assertThat(snmp.sendInform(Snmp.TRAP.toDottedString())).isEqualTo(1);
				await().atMost(5, TimeUnit.SECONDS).until(() -> message != null);
				assertThat(message).hasSizeGreaterThanOrEqualTo(130);
			}

			when(configurationService.getProperty(ConfigurationIntKey.hiab_snmp_version)).thenReturn(2);

			try (final Snmp snmp = Snmp.getInstance(configurationService)) {
				message = null;
				assertThat(snmp.sendInform(Snmp.TRAP.toDottedString())).isEqualTo(1);
				await().atMost(5, TimeUnit.SECONDS).until(() -> message != null);
				assertThat(message).hasSizeGreaterThanOrEqualTo(60);
			}

			when(configurationService.getProperty(ConfigurationIntKey.hiab_snmp_version)).thenReturn(3);

			final HashMap<OID, Object> snmpMsg = new HashMap<>();
			snmpMsg.put(Snmp.INFO_PERCENTAGE, 50);
			snmpMsg.put(Snmp.INFO_VERSION, "1.0");

			try (final Snmp snmp = Snmp.getInstance(configurationService)) {
				message = null;
				assertThat(snmp.sendTrap(Snmp.TRAP_ALERT.toDottedString(), snmpMsg)).isEqualTo(1);
				await().atMost(5, TimeUnit.SECONDS).until(() -> message != null);
				assertThat(message).hasSizeGreaterThanOrEqualTo(150);
			}
		}
		finally {
			stopLocalServer();
		}
	}
}
