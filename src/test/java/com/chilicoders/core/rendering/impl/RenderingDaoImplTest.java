package com.chilicoders.core.rendering.impl;

import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.sql.SQLException;

import javax.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.test.TestSpringDataSourceConfig;
import com.chilicoders.core.user.impl.TestUserDetails;
import com.chilicoders.core.user.impl.UserServiceImplTest;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.test.TestUtil;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {TestSpringDataSourceConfig.class})
@ActiveProfiles("testCore")
@Transactional
public class RenderingDaoImplTest extends BaseSpringTestWithRollback {

	private RenderingDao renderingDao;

	private TestUserDetails testUser = new TestUserDetails(false);

	@Autowired
	private TestUtil testUtil;

	/**
	 * Setup before each test.
	 */
	@Before
	public void before() throws SQLException, IOException {
		init();
		renderingDao = new RenderingDaoImpl(testUtil.getStatementExec());
	}

	@Test
	public void testGetScannerPreferences() throws SQLException {
		UserServiceImplTest.addUser(testUser, testUtil.getStatementExec());
		final String scannerPrefsName = "testGetScannerPreferencesName";
		testUtil.executeSql(new NativeSqlStatement("INSERT INTO tsavedscanprefs(xid, xuserxid, name) VALUES (?, ?, ?)", 111L, TestUserDetails.USER_ID, scannerPrefsName));
		assertEquals(scannerPrefsName, renderingDao.getScannerPreference(111L));
	}

	@Test
	public void testGetAwsArn() throws SQLException {
		UserServiceImplTest.addUser(testUser, testUtil.getStatementExec());
		final String arn = "testGetAwsArn";
		final String name = "awsArnNAME";
		testUtil.executeSql(new NativeSqlStatement("INSERT INTO tawsarns(xid, xuserxid, name, arn) VALUES (?, ?, ?, ?)", 111L, TestUserDetails.USER_ID, name, arn));
		assertEquals(name, renderingDao.getAwsArn(TestUserDetails.USER_ID, arn));
	}

	@Test
	public void getTargetGroupName() throws SQLException {
		UserServiceImplTest.addUser(testUser, testUtil.getStatementExec());
		final String targetGroupName = "TG1";
		testUtil.executeSql(
				new NativeSqlStatement("INSERT INTO tgenericgroups(xid, name, xuserxid, xiparentid) VALUES (?, ?, ?, ?)", 122L, targetGroupName, TestUserDetails.USER_ID, 0L));
		assertEquals(targetGroupName, renderingDao.getTargetGroupName(TestUserDetails.USER_ID, 122L));
	}

	@Test
	public void getTargetGroupNameAll() throws SQLException {
		assertEquals(RenderingDaoImpl.ALL_TARGETS, renderingDao.getTargetGroupName(TestUserDetails.USER_ID, -1));
	}
}
