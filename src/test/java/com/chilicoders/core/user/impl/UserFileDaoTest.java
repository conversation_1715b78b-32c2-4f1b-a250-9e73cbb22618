package com.chilicoders.core.user.impl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.sql.SQLException;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.user.api.UserFileDao;
import com.chilicoders.core.user.api.model.UserFileInterface;
import com.chilicoders.test.TestUtil;
import com.chilicoders.util.StringUtils;

public class UserFileDaoTest extends BaseSpringTestWithRollback {
	@Autowired
	private UserFileDao userFileDao;

	@Autowired
	private TestUtil testUtil;

	@Test
	public void testMissingFile() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		assertNull(userFileDao.findOneByUserIdAndName(user.getMainUserId(), "missing"));
	}

	@Test
	public void testData() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		testUtil.executeSql("INSERT INTO toutscanfiles(xid, xuserxid, name, content) VALUES (NEXTVAL('toutscanfiles_seq'), ?, ?, ?)", user.getMainUserId(), "sjörök.json",
				StringUtils.byteToHexStr("TESTDATA".getBytes(UTF_8)));
		final UserFileInterface file = userFileDao.findOneByUserIdAndName(user.getMainUserId(), "sjörök.json");
		assertNotNull(file);
		assertEquals("TESTDATA", new String(file.getData(), UTF_8));
		assertNull(userFileDao.findOneByUserIdAndName(user.getMainUserId() + 1, "sjörök.json"));
	}
}
