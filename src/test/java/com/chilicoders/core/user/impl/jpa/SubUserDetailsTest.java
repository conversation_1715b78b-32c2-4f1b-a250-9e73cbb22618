package com.chilicoders.core.user.impl.jpa;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.core.user.impl.TestUserDetails;
import com.chilicoders.core.user.impl.UserServiceImplTest;
import com.chilicoders.db.objects.api.ResourceGroupInterface;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.test.TestUtil;

public class SubUserDetailsTest extends BaseSpringTestWithRollback {
	@Autowired
	private UserService userService;

	@Autowired
	private TestUtil testUtil;

	private final TestUserDetails user = new TestUserDetails(false);

	/**
	 * Setup before each test.
	 */
	@Before
	public void before() throws SQLException, IOException {
		init();
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
	}

	@Test
	public void testLoadResourceGroups() throws SQLException {
		testUtil.executeSql(
				new NativeSqlStatement("INSERT INTO customers(id, name, userid) VALUES (?, 'Dummy customer', ?)", TestUserDetails.CUSTOMER_ID, TestUserDetails.USER_ID));
		testUtil.executeSql(new NativeSqlStatement("INSERT INTO resourcegroups(id, name, resources, customerid) VALUES (10000, 'Group', ?::jsonb, +?)",
				"[{\"type\": \"APPSTAK\", \"tagIds\": [5]}, {\"type\": \"ASSET\", \"tagIds\": [7]}]", TestUserDetails.CUSTOMER_ID));
		addSubUser(new TestUserDetails(true), testUtil.getStatementExec());
		final UserDetails user = userService.getUserDetails(TestUserDetails.SUBUSER_ID, true);
		((SubUserDetails) user).loadResourceGroups(testUtil.getStatementExec());
		final List<ResourceGroupInterface> groups = user.getResourceGroups();
		assertThat(groups).extracting(ResourceGroupInterface::getId).containsExactly(10000);
		assertThat(user.hasLimitResources(ResourceType.APPSTAK)).isTrue();
	}

	@Test
	public void testHasLimitResources() {
		final SubUserDetails subuser = new SubUserDetails();
		assertTrue(subuser.hasLimitResources(ResourceType.APPSTAK));
		subuser.setResourceGroupIds(new Integer[0]);
		assertTrue(subuser.hasLimitResources(ResourceType.APPSTAK));
		subuser.setResourceGroupIds(new Integer[] {10000});
		final ResourceGroup group = new ResourceGroup();
		group.setResources("[{\"type\": \"APPSTAK\", \"tagIds\": [5]}, {\"type\": \"ASSET\", \"tagIds\": [7]}]");
		subuser.setResourceGroups(Collections.singletonList(group));
		assertTrue(subuser.hasLimitResources(ResourceType.APPSTAK));
		final ResourceGroup fullAppStakGroup = new ResourceGroup();
		fullAppStakGroup.setResources("[{\"type\": \"APPSTAK\"}, {\"type\": \"ASSET\", \"tagIds\": [7]}]");
		subuser.setResourceGroups(Collections.singletonList(fullAppStakGroup));
		assertFalse(subuser.hasLimitResources(ResourceType.APPSTAK));
	}
}