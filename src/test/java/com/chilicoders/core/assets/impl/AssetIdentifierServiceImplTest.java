package com.chilicoders.core.assets.impl;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.chilicoders.core.assetidentifier.api.AssetIdentifierService;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierLinkType;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.Source;
import com.chilicoders.model.docker.DockerImageInfo;
import com.chilicoders.test.TestUtil;

import com.google.common.collect.ImmutableSet;

public class AssetIdentifierServiceImplTest extends BaseSpringTestWithRollback {
	private static final String TEMPLATE = "template";
	private static final String SEEDS = "seeds";
	private static final String NSECONDS = "nseconds";
	private static final String FUZZING = "fuzzing";
	private static final String CLIENT_CERTIFICATE_AUTH = "client-certificate-auth";
	private static final String ABORT_PATTERN = "abort-pattern";
	private static final String MUST_MATCH = "must-match";
	private static final String CANNOT_MATCH = "cannot-match";
	private static final String ALLOWED_DOMAINS = "allowed-domains";
	private static final String ADDR_BLACKLIST = "addr-blacklist";
	private static final String HOST_MAP = "host-map";
	private static final String INFRASTRUCTURE_SCAN = "infrastructure-scan";

	@Autowired
	private AssetIdentifierService assetIdentifierService;

	@Autowired
	private TestUtil testUtil;

	@Test
	public void testCreateDockerImageAssetIdentifier() throws Exception {
		final String dockerRegistryName = "************";
		final Instant firstSeen = Instant.parse("2021-01-01T08:30:00.00Z");
		final DockerImageInfo image_details = new DockerImageInfo();
		image_details.setName("o24-repo-4test/rhel7");
		image_details.setTag("latest");
		image_details.setArchitecture("amd64");
		image_details.setSize(32264217L);

		final Integer customerId = testUtil.insertCustomer(5);
		final Instant now = Instant.now();

		final Integer accountId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO accounts (name, type, role, customerid) VALUES(?, CAST(? AS accounttype), ?, ?) RETURNING id",
				"Docker 4 test", "DOCKER", "For test", customerId)).intValue();

		final Integer dockerRegistryId = assetIdentifierService.createDockerRegistryAssetIdentifier(dockerRegistryName, customerId, accountId, 0, null, firstSeen);
		final Integer assetId =
				assetIdentifierService.createDockerImageAssetIdentifier(image_details, dockerRegistryId, customerId, accountId, 0, null, now).getRight().getId();

		// Change the docker registry IP address and check the docker image asset id is different.
		final Instant firstSeen2ndRegistry = Instant.parse("2021-04-01T15:45:00.00Z");
		final Integer newRegistryId =
				assetIdentifierService.createDockerRegistryAssetIdentifier(dockerRegistryName.replace("21", "45"), customerId, accountId, 0, null,
						firstSeen2ndRegistry);
		final Integer assetId2 =
				assetIdentifierService.createDockerImageAssetIdentifier(image_details, newRegistryId, customerId, accountId, 0, null, now).getRight().getId();
		assertThat(assetId).isNotEqualTo(assetId2);

		// Check there is no duplicate
		final Integer assetId3 =
				assetIdentifierService.createDockerImageAssetIdentifier(image_details, newRegistryId, customerId, accountId, 0, null, now).getRight().getId();
		assertThat(assetId2).isEqualTo(assetId3);
	}

	@Test
	public void linkAssetIdentifiersToAssetForExistingHostnameIpIdentifier() throws Exception {
		final UUID assetUuid = UUID.randomUUID();
		final int userId = 5;
		testUtil.insertUser(userId, "Test");
		final Integer customerId = testUtil.insertCustomer(userId);
		final Instant now = Instant.now();

		final long scanConfigurationId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO scanconfigurations (name, template, configuration, customerid, createdbyid, updatedbyid) VALUES(?, ?, ?::jsonb, ?, ?, ?) RETURNING id",
				"Scale configuration", ScanTemplate.SCALE,
				createScaleConfiguration().put(SEEDS, Collections.singletonList("http://test.com/test3")).toString(), customerId, userId,
				userId));

		final int firstScanId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO scanlogs (status, template, scanconfigurationid, customerid, createdbyid) VALUES(?, ?, ?, ?, ?) RETURNING id", ScanLogStatus.FINISHED,
				ScanTemplate.SCALE, scanConfigurationId, customerId, userId)).intValue();
		final int lastScanId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO scanlogs (status, template, scanconfigurationid, customerid, createdbyid) VALUES(?, ?, ?, ?, ?) RETURNING id", ScanLogStatus.FINISHED,
				ScanTemplate.SCALE, scanConfigurationId, customerId, userId)).intValue();

		final long assetId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assets (name, source, uuid, createdbyid, updatedbyid, customerid, firstscanid, lastscanid) VALUES(?, ?::source[], ?, ?, ?, ?, ?, ?) RETURNING id",
				"Asset", new Source[] {Source.SWAT}, assetUuid, userId, userId, customerId, firstScanId, firstScanId)).intValue();

		final int hostnameAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"***********", AssetIdentifierType.HOSTNAME, new Source[] {Source.SCALE}, now, now, customerId, firstScanId, lastScanId)).intValue();
		final int ipAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"yee.com", AssetIdentifierType.IP, new Source[] {Source.SCOUT}, now, now, customerId, firstScanId, lastScanId)).intValue();

		testUtil.getStatementExec().execute(new NativeSqlStatement("INSERT INTO asset_assetidentifier (assetid, assetidentifierid, firstseen, lastseen) VALUES(?, ?, ?, ?)", assetId, hostnameAssetIdentifierId, now, now));

		final List<? extends AssetIdentifierInterface> assetIdentifiers = assetIdentifierService.getAssetIdentifiersByIds(hostnameAssetIdentifierId, ipAssetIdentifierId);

		final AssetInterface asset = assetIdentifierService.linkAssetIdentifiersToAsset(customerId, lastScanId, assetIdentifiers, assetUuid.toString(), ImmutableSet.of(Source.SCALE, Source.NETSEC, Source.APPSEC)).getRight();

		assertThat(asset.getUuid()).isNotNull();
		assertThat(asset.getName()).isEqualTo("Asset");
		assertThat(asset.getFirstScanId()).isEqualTo(firstScanId);
		assertThat(asset.getLastScanId()).isEqualTo(lastScanId);
		assertThat(asset.getSource()).hasSize(3);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_scanlog WHERE assetid = ?", asset.getId()))).isEqualTo(2);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_assetidentifier WHERE assetid = ?", asset.getId()))).isEqualTo(2);
	}

	@Test
	public void linkAssetIdentifiersToAssetForNewHostnameIpIdentifier() throws Exception {
		final int userId = 5;
		testUtil.insertUser(userId, "Test");
		final Integer customerId = testUtil.insertCustomer(userId);
		final Instant now = Instant.now();

		final int lastScanId =
				testUtil.getStatementExec().getLong(new NativeSqlStatement("INSERT INTO scanlogs (status, template, customerid, createdbyid) VALUES(?, ?, ?, ?) RETURNING id",
						ScanLogStatus.FINISHED, ScanTemplate.SCALE, customerId, userId)).intValue();

		final int hostnameAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"yee.com", AssetIdentifierType.HOSTNAME, new Source[] {Source.SCALE}, now, now, customerId, lastScanId, lastScanId)).intValue();
		final int ipAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"***********", AssetIdentifierType.IP, new Source[] {Source.SCALE}, now, now, customerId, lastScanId, lastScanId)).intValue();

		final List<? extends AssetIdentifierInterface> assetIdentifiers = assetIdentifierService.getAssetIdentifiersByIds(hostnameAssetIdentifierId, ipAssetIdentifierId);

		final AssetInterface asset = assetIdentifierService.linkAssetIdentifiersToAsset(customerId, lastScanId, assetIdentifiers, null, ImmutableSet.of(Source.SCALE)).getRight();

		assertThat(asset.getId()).isNotNull();
		assertThat(asset.getName()).isEqualTo("yee.com");
		assertThat(asset.getFirstScanId()).isEqualTo(lastScanId);
		assertThat(asset.getLastScanId()).isEqualTo(lastScanId);
		assertThat(asset.getSource()).hasSize(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_scanlog WHERE assetid = ?", asset.getId()))).isEqualTo(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_assetidentifier WHERE assetid = ?", asset.getId()))).isEqualTo(2);
	}

	@Test
	public void linkAssetIdentifiersToAssetForNewAwsIdentifier() throws Exception {
		final int userId = 5;
		testUtil.insertUser(userId, "Test");
		final Integer customerId = testUtil.insertCustomer(userId);
		final Instant now = Instant.now();

		final int lastScanId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO scanlogs (status, template, customerid, createdbyid) VALUES(?, ?, ?, ?) RETURNING id",
						ScanLogStatus.FINISHED, ScanTemplate.CLOUDSEC, customerId, userId)).intValue();

		final int accountIdAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"account_1", AssetIdentifierType.AWS_ACCOUNT_ID, new Source[] {Source.SCALE}, now, now, customerId, lastScanId, lastScanId)).intValue();
		final int regionAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"ap-east-1", AssetIdentifierType.AWS_REGION, new Source[] {Source.SCOUT}, now, now, customerId, lastScanId, lastScanId)).intValue();

		testUtil.getStatementExec().execute(new NativeSqlStatement(
				"INSERT INTO assetidentifier_assetidentifier (assetidentifierid1, assetidentifierid2, firstseen, lastseen, type) VALUES (?, ?, NOW(), NOW(), ?)",
				accountIdAssetIdentifierId, regionAssetIdentifierId, AssetIdentifierLinkType.CONTAINS));

		final List<? extends AssetIdentifierInterface> assetIdentifiers = assetIdentifierService.getAssetIdentifiersByIds(accountIdAssetIdentifierId, regionAssetIdentifierId);

		final AssetInterface asset = assetIdentifierService.linkAssetIdentifiersToAsset(customerId, lastScanId, assetIdentifiers, null, ImmutableSet.of(Source.SCALE, Source.SCOUT)).getRight();

		assertThat(asset.getId()).isNotNull();
		assertThat(asset.getName()).isEqualTo("account_1/ap-east-1");
		assertThat(asset.getFirstScanId()).isEqualTo(lastScanId);
		assertThat(asset.getLastScanId()).isEqualTo(lastScanId);
		assertThat(asset.getSource()).hasSize(2);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_scanlog WHERE assetid = ?", asset.getId()))).isEqualTo(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_assetidentifier WHERE assetid = ?", asset.getId()))).isEqualTo(2);
	}

	@Test
	public void linkAssetIdentifiersToAssetForNewDockerIdentifier() throws Exception {
		final int userId = 5;
		testUtil.insertUser(userId, "Test");
		final Integer customerId = testUtil.insertCustomer(userId);
		final Instant now = Instant.now();

		final int lastScanId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO scanlogs (status, template, customerid, createdbyid) VALUES(?, ?, ?, ?) RETURNING id",
						ScanLogStatus.FINISHED, ScanTemplate.CLOUDSEC, customerId, userId)).intValue();

		final int imageAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"fedora-image", AssetIdentifierType.DOCKER_IMAGE, new Source[] {Source.SCALE}, now, now, customerId, lastScanId, lastScanId)).intValue();
		final int registryAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid, firstscanid, lastscanid) VALUES(?, ?, ?::source[], ?, ?, ?, ?, ?) RETURNING id",
				"docker-hub.com", AssetIdentifierType.DOCKER_REGISTRY, new Source[] {Source.SCALE}, now, now, customerId, lastScanId, lastScanId)).intValue();

		testUtil.getStatementExec().execute(new NativeSqlStatement(
				"INSERT INTO assetidentifier_assetidentifier (assetidentifierid1, assetidentifierid2, firstseen, lastseen, type) VALUES (?, ?, NOW(), NOW(), ?)",
				registryAssetIdentifierId, imageAssetIdentifierId, AssetIdentifierLinkType.CONTAINS));

		final List<? extends AssetIdentifierInterface> assetIdentifiers = assetIdentifierService.getAssetIdentifiersByIds(imageAssetIdentifierId, registryAssetIdentifierId);

		final AssetInterface asset = assetIdentifierService.linkAssetIdentifiersToAsset(customerId, lastScanId, assetIdentifiers, null, ImmutableSet.of(Source.SCALE)).getRight();

		assertThat(asset.getId()).isNotNull();
		assertThat(asset.getName()).isEqualTo("docker-hub.com/fedora-image");
		assertThat(asset.getFirstScanId()).isEqualTo(lastScanId);
		assertThat(asset.getLastScanId()).isEqualTo(lastScanId);
		assertThat(asset.getSource()).hasSize(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_scanlog WHERE assetid = ?", asset.getId()))).isEqualTo(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_assetidentifier WHERE assetid = ?", asset.getId()))).isEqualTo(2);
	}

	@Test
	public void linkAssetIdentifiersToAssetForNewOtherIdentifier() throws Exception {
		final int userId = 5;
		testUtil.insertUser(userId, "Test");
		final Integer customerId = testUtil.insertCustomer(userId);
		final Instant now = Instant.now();

		final int macAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?) RETURNING id",
				"00:00:5e:00:53:af", AssetIdentifierType.MAC, new Source[] {Source.SCALE}, now, now, customerId)).intValue();
		final int seedPathAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?) RETURNING id",
				"example.com/wordpress", AssetIdentifierType.SEED_PATH, new Source[] {Source.SCALE}, now, now, customerId)).intValue();
		final int netBiosAssetIdentifierId = testUtil.getStatementExec().getLong(new NativeSqlStatement(
				"INSERT INTO assetidentifiers (name, type, source, firstseen, lastseen, customerid) VALUES(?, ?, ?::source[], ?, ?, ?) RETURNING id",
				"example.com/drupal", AssetIdentifierType.NETBIOS, new Source[] {Source.SCALE}, now, now, customerId)).intValue();

		final List<? extends AssetIdentifierInterface> assetIdentifiers =
				assetIdentifierService.getAssetIdentifiersByIds(macAssetIdentifierId, seedPathAssetIdentifierId, netBiosAssetIdentifierId);

		final AssetInterface asset = assetIdentifierService.linkAssetIdentifiersToAsset(customerId, null, assetIdentifiers, null, ImmutableSet.of(Source.SCALE)).getRight();

		assertThat(asset.getId()).isNotNull();
		assertThat(asset.getName()).isEqualTo("00:00:5e:00:53:af");
		assertThat(asset.getFirstScanId()).isNull();
		assertThat(asset.getLastScanId()).isNull();
		assertThat(asset.getSource()).hasSize(1);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_scanlog WHERE assetid = ?", asset.getId()))).isEqualTo(0);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM asset_assetidentifier WHERE assetid = ?", asset.getId()))).isEqualTo(3);
	}

	@Test
	public void testCreateAssetidentifier() throws SQLException {
		final int userId = 5;
		final Integer customerId = testUtil.insertCustomer(userId);

		final AssetIdentifierInterface identifier = assetIdentifierService.createAssetIdentifier(customerId, Source.CLOUDSEC, "test", AssetIdentifierType.IP, 0, null, Instant.now(), null);
		List<? extends AssetIdentifierInterface> loaded = assetIdentifierService.getAssetIdentifiersByIds(identifier.getId());
		assertThat(loaded).hasSize(1);
		assertThat(loaded.get(0).getSource()).hasSameElementsAs(Collections.singletonList(Source.CLOUDSEC));

		final AssetIdentifierInterface updated = assetIdentifierService.createAssetIdentifier(customerId, Source.NETSEC, "test", AssetIdentifierType.IP, 0, null, Instant.now(), null);
		assertThat(identifier.getId()).isEqualTo(updated.getId());
		loaded = assetIdentifierService.getAssetIdentifiersByIds(identifier.getId());
		assertThat(loaded).hasSize(1);
		assertThat(loaded.get(0).getSource()).hasSameElementsAs(Arrays.asList(Source.CLOUDSEC, Source.NETSEC));
	}

	@Test
	public void testFindByIds() throws SQLException {
		final int userId = 5;
		final Integer customerId = testUtil.insertCustomer(userId);
		final AssetIdentifierInterface identifier = assetIdentifierService.createAssetIdentifier(customerId, Source.CLOUDSEC, "test", AssetIdentifierType.IP, 0, null, Instant.now(), null);

		assertThat(assetIdentifierService.getAssetIdentifiersByIds()).isEmpty();
		assertThat(assetIdentifierService.getAssetIdentifiersByIds(identifier.getId() + 1, identifier.getId() + 3)).isEmpty();
		assertThat(assetIdentifierService.getAssetIdentifiersByIds(identifier.getId() + 1, identifier.getId())).hasSize(1);
		assertThat(assetIdentifierService.getAssetIdentifiersByIds((Integer) null)).isEmpty();
		assertThat(assetIdentifierService.getAssetIdentifiersByIds(null, identifier.getId())).hasSize(1);
	}

	/**
	 * Creates a JSON object with configuration data.
	 *
	 * @return JSON object for configuration.
	 */
	private JSONObject createScaleConfiguration() {
		final JSONObject configuration = new JSONObject();
		configuration.put(TEMPLATE, ScanTemplate.SCALE);
		final List<String> seeds = new ArrayList<>();
		seeds.add("https://aoeu.com/");
		configuration.put(SEEDS, seeds);
		configuration.put(NSECONDS, 600);
		configuration.put(FUZZING, true);
		configuration.put(CLIENT_CERTIFICATE_AUTH, new JSONObject("{client-certificate:\"MyCertificate\", private-key:\"MyKey\"}"));
		configuration.put(ABORT_PATTERN, "aoe?");
		configuration.put(MUST_MATCH, new JSONArray("[{method:\"aoe?\"}]"));
		configuration.put(CANNOT_MATCH, new JSONArray("[{method:\"aoe?\"}]"));
		configuration.put(ALLOWED_DOMAINS, new JSONArray("[{domain:\"www.aoeu.com\", allow-subdomains:true}]"));
		configuration.put(ADDR_BLACKLIST, new JSONArray("[\"***********/32\", \"www.aoeu.com\", \"***********/30\"]"));
		configuration.put(HOST_MAP, new JSONArray("[{from:\"username\", to:[\"10.0.0.0\"]}]"));
		configuration.put(INFRASTRUCTURE_SCAN, true);
		return configuration;
	}
}