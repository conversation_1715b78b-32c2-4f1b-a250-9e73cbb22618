package com.chilicoders.core.ip.impl;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import javax.transaction.Transactional;

import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.test.TestSpringDataSourceConfig;
import com.google.common.collect.ImmutableSet;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {TestSpringDataSourceConfig.class})
@ActiveProfiles("testCore")
@Transactional
public class IpServiceImplTest {
	@Autowired
	private IpService ipService;

	@Autowired
	private ConfigurationService configService;

	@Before
	public void init() throws IOException {
		FileUtils.forceMkdir(new File("/tmp/opi.blacklist.d"));
		configService.setProperty(ConfigurationKey.blacklist_folder_path.getConfigKey(), "/tmp/opi.blacklist.d/");
	}

	@Test
	public void testIsIp() {
		assertThat(ipService.isIp("")).isFalse();
		assertThat(ipService.isIp(null)).isFalse();
		assertThat(ipService.isIp("127.0.0.1")).isTrue();
		assertThat(ipService.isIp("127.0.257.1")).isFalse();
		assertThat(ipService.isIp("1::1")).isTrue();
		assertThat(ipService.isIp("1::1/56")).isFalse();
		assertThat(ipService.isIp("1::1\n127.0.0.1")).isFalse();
		assertThat(ipService.isIp("localhost")).isFalse();
		assertThat(ipService.isIp("outpost24.com")).isFalse();
		assertThat(ipService.isIp("127.0.1")).isFalse();
	}

	@Test
	public void testGetIpValue() throws SQLException {
		assertThat(ipService.getIpValue("127.0.0.1")).isEqualTo(2130706433);
		assertThat(ipService.getIpValue("***********")).isEqualTo(1482165260);
		assertThat(ipService.getIpValue("***********/24")).isEqualTo(0);
		assertThat(ipService.getIpValue("outpost24.com") >= 5000000000L).isTrue();
		assertThat(ipService.getIpValue("åäö_..,")).isEqualTo(0);
		assertThat(ipService.getIpValue(null)).isEqualTo(0);
		assertThat(ipService.getIpValue("@instanceid") >= 5000000000L).isTrue();
		assertThat(ipService.getIpValue("\\\\WINDOWS") >= 5000000000L).isTrue();
	}

	@Test
	public void testIsInstanceId() {
		assertThat(ipService.isInstanceId("@instance")).isTrue();
		assertThat(ipService.isInstanceId("outpost24.com")).isFalse();
		assertThat(ipService.isInstanceId("127.0.0.1")).isFalse();
		assertThat(ipService.isInstanceId("\\\\WINDOWS")).isFalse();
		assertThat(ipService.isInstanceId("")).isFalse();
		assertThat(ipService.isInstanceId(null)).isFalse();
	}

	@Test
	public void testGetProtocolName() {
		assertThat(ipService.getProtocolName(6, "default")).isEqualTo("tcp");
		assertThat(ipService.getProtocolName(17, "default")).isEqualTo("udp");
		assertThat(ipService.getProtocolName(1, "default")).isEqualTo("icmp");
		assertThat(ipService.getProtocolName(2, "default")).isEqualTo("igmp");
		assertThat(ipService.getProtocolName(-1, "default")).isEqualTo("default");
		assertThat(ipService.getProtocolName(-4, "default")).isEqualTo("default");
	}

	@Test
	public void testIsLoopbackAddress() throws SQLException {
		assertThat(ipService.isLoopBackAddress("127.0.0.1")).isTrue();
		assertThat(ipService.isLoopBackAddress("sjörök")).isFalse();
		assertThat(ipService.isLoopBackAddress("***************")).isTrue();
		assertThat(ipService.isLoopBackAddress("*********")).isFalse();
		assertThat(ipService.isLoopBackAddress("***************")).isFalse();
		assertThat(ipService.isLoopBackAddress("12::12")).isFalse();
		assertThat(ipService.isLoopBackAddress("::1")).isTrue();
	}

	@Test
	public void testIpInList() throws SQLException {
		final String iplist = "***********,***********,***********-************\n***********/24,testhostname.com,@instance_id";

		assertThat(ipService.isAnyIpInList(new String[] {"************"}, iplist)).isFalse();
		assertThat(ipService.isAnyIpInList(new String[] {"***********00", "************0", "***********00", "@not_existing_instance_id", "notest.com"}, iplist)).isFalse();
		assertThat(ipService.isAnyIpInList(new String[] {null, null, null, null}, iplist)).isFalse();
		assertThat(ipService.isAnyIpInList(new String[] {"***********", "*************"}, iplist)).isFalse();
		assertThat(ipService.isAnyIpInList(new String[] {"notest.com", "@not_existing_instance_id"}, iplist)).isFalse();

		assertThat(
				ipService.isAnyIpInList(new String[] {"***********00", "************0", "***********00", "@not_existing_instance_id", "testhostname.com"}, iplist)).isTrue();
		assertThat(ipService.isAnyIpInList(new String[] {"************0", "***********00", "@instance_id"}, iplist)).isTrue();
		assertThat(ipService.isAnyIpInList(new String[] {"***********", "************0", "***********00", "@not_existing_instance_id", "testhostname.com"}, iplist)).isTrue();
		assertThat(ipService.isAnyIpInList(new String[] {"@not_existing_instance_id", "testhostname.com", "***********", "************0", "***********"}, iplist)).isTrue();
		assertThat(ipService.isAnyIpInList(new String[] {"***********"}, iplist)).isTrue();
		assertThat(ipService.isAnyIpInList(new String[] {"***********", "***********"}, iplist)).isTrue();
	}

	@Test
	public void testIpHostnameConversion() throws SQLException {
		boolean canResolveHostnames = true;
		try {
			InetAddress.getAllByName("exttest.outpost24.com");
		}
		catch (final UnknownHostException ex) {
			canResolveHostnames = false;
		}

		if (canResolveHostnames) {
			assertThat(ipService.convertHostnameToIp("")).isNull();
			assertThat(ipService.convertHostnameToIp("127.0.0.1")).isEqualTo("127.0.0.1");
			assertThat(ipService.convertHostnameToIp("invalid.outpost24.com")).isNull();
			final String ip = ipService.convertHostnameToIp("outscan.outpost24.com");
			assertThat(ip).isNotNull();
		}
	}

	@Test
	public void testValidUrl() {
		assertThat(ipService.isValidUrl("http://outpost24.com")).isTrue();
		assertThat(ipService.isValidUrl("http://outpost24.com/path")).isTrue();
		assertThat(ipService.isValidUrl("https://outpost24.com")).isTrue();
		assertThat(ipService.isValidUrl("https://outpost24.com/path")).isTrue();
		assertThat(ipService.isValidUrl("ftp://outpost24.com/path")).isFalse();
		assertThat(ipService.isValidUrl("http://outpost24.comåäö\\")).isFalse();
	}

	@Test
	public void testValidHostname() {
		assertThat(ipService.isHostname(null)).isFalse();
		assertThat(ipService.isHostname("test.com")).isTrue();
		assertThat(ipService.isHostname("test")).isTrue();
		configService.setProperty(ConfigKeys.ConfigurationBooleanKey.hiab_enabled.getConfigKey(), "false");
		assertThat(ipService.isHostname(null)).isFalse();
		assertThat(ipService.isHostname("test.com")).isTrue();
		assertThat(ipService.isHostname("test")).isTrue();
		configService.setProperty(ConfigKeys.ConfigurationBooleanKey.hiab_enabled.getConfigKey(), "true");
	}

	@Test
	public void testExclude() throws SQLException {
		assertThat(ipService.excludeTargets("127.0.0.1", "127.0.0.1", false)).isEqualTo("");
		assertThat(ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.11", false)).isEqualTo("127.0.0.1-*********\nwww.google.com\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********", "127.0.0.1", false)).isEqualTo("*********\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.1\nwww.google.com", false)).isEqualTo("*********\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********", "*********", false)).isEqualTo("127.0.0.1\n*********\n");
		assertThat(ipService.excludeTargets("2001::1-2001::3", "2001::2", false)).isEqualTo("2001::1\n2001::3\n");
		assertThat(ipService.excludeTargets("127.0.0.1\n*********\n*********\n*********", "127.0.0.1\n*********", false)).isEqualTo("*********\n*********\n");
		assertThat(ipService.excludeTargets("***********-outpost24.com", "***********", false)).isEqualTo("***********-outpost24.com\n");
		assertThat(ipService.excludeTargets("*******\n***********-outpost24.com", "***********-outpost24.com", false)).isEqualTo("*******\n");
	}

	@Test
	public void testExcludeRanges() throws SQLException {
		assertThat(ipService.excludeTargets("127.0.0.1-*********", "*********\n*********", true)).isEqualTo("127.0.0.1-*********\n");
		assertThat(ipService.excludeTargets("10.0.0.0/16", "********", true)).isEqualTo("10.0.0.0-**********\n10.0.3.1-************\n");
		assertThat(ipService.excludeTargets("10.0.0.0/16", "********\n10.0.100.100", true)).isEqualTo(
				"10.0.0.0-**********\n10.0.3.1-***********\n10.0.100.101-************\n");
		assertThat(ipService.excludeTargets("127.0.0.1\n*********\n*********\n*********", "127.0.0.1\n*********", true)).isEqualTo("*********\n*********\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.1\nwww.google.com", true)).isEqualTo("*********\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "127.0.0.11", true)).isEqualTo("127.0.0.1-*********\nwww.google.com\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********\nwww.google.com", "www.google.com", false)).isEqualTo("127.0.0.1-*********\n");
		assertThat(ipService.excludeTargets("127.0.0.1-*********", "", true)).isEqualTo("127.0.0.1-*********");
		assertThat(ipService.excludeTargets("127.0.0.1-*********", "127.0.0.1-*********", true)).isEqualTo("");

	}

	@Test
	public void testRangeFixed() throws SQLException {
		assertThat(ipService.getRangeFixed("*******/30", true)).isEqualTo(new String[] {"*******", "*******"});
		assertThat(ipService.getRangeFixed("*******/30", false)).isEqualTo(new String[] {"*******", "*******", "*******", "*******"});
		assertThat(ipService.getRangeFixed("2001:4860:4860::8888/126", false)).isEqualTo(
				new String[] {"2001:4860:4860::8888", "2001:4860:4860::8889", "2001:4860:4860::888a", "2001:4860:4860::888b"});
	}

	@Test
	public void testGetBlackList() throws IOException {
		final Path localPath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "first.blacklist");
		Files.write(localPath, "include".getBytes(UTF_8), StandardOpenOption.CREATE);

		final Path excludePath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "second.blacklist.exclude");
		Files.write(excludePath, "exclude".getBytes(UTF_8), StandardOpenOption.CREATE);

		try {
			final List<String> blackList = ipService.getBlacklist();
			assertThat(blackList.size()).isEqualTo(1);
			assertThat(blackList.contains("include")).isTrue();
			assertThat(blackList.contains("exclude")).isFalse();
		}
		finally {
			FileUtils.deleteQuietly(localPath.toFile());
			FileUtils.deleteQuietly(excludePath.toFile());
		}
	}

	@Test
	public void testGetInvalidTargets() throws SQLException, IOException {
		// verify local IPs ********* - *************** without any blacklist --- always block loop back
		Set<String> invalidtargets = ipService.getInvalidTargets("*********\n*************");
		assertThat(invalidtargets.size()).isEqualTo(2);
		assertThat(invalidtargets.contains("*********") && invalidtargets.contains("*************")).isTrue();

		// verify local IPs 0.0.0.0 - ************* without any blacklist
		invalidtargets = ipService.getInvalidTargets("0.0.0.0,*************");
		assertThat(invalidtargets.isEmpty()).isTrue();

		invalidtargets = ipService.getInvalidTargets("@");
		assertThat(invalidtargets.size()).isEqualTo(1);

		invalidtargets = ipService.getInvalidTargets("*********\n*************,0.0.0.0,*************");
		assertThat(invalidtargets.size()).isEqualTo(2);
		assertThat(invalidtargets.contains("*********") && invalidtargets.contains("*************")).isTrue();

		final Path blackListPath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "local.blacklist");
		try {
			Files.write(blackListPath, "*********/8\n0.0.0.0/8\n::1/128".getBytes(UTF_8), StandardOpenOption.CREATE);
			invalidtargets = ipService.getInvalidTargets("0.0.0.0,***********,***************");
			assertThat(invalidtargets.size()).isEqualTo(1);
			assertThat(invalidtargets.contains("0.0.0.0")).isTrue();
			assertThat(!invalidtargets.contains("***********") && !invalidtargets.contains("***************")).isTrue();

			Files.write(blackListPath, "*********/8\n0.0.0.0/8\n::1/128\n***********/16".getBytes(UTF_8), StandardOpenOption.CREATE);
			invalidtargets = ipService.getInvalidTargets("0.0.0.0,***********,***************");
			assertThat(invalidtargets.size()).isEqualTo(3);
			assertThat(invalidtargets.contains("0.0.0.0") && invalidtargets.contains("***********") && invalidtargets.contains("***************")).isTrue();
		}
		finally {
			FileUtils.deleteQuietly(blackListPath.toFile());
		}
	}

	@Test
	public void testIsValidTargetList() throws SQLException, IOException {
		// verify empty IP
		assertThat(ipService.isValidTargetList("")).isTrue();

		final String local = "*********/8\n" +
				"0.0.0.0/8\n" +
				"::1/128";

		final String privateList = "***********/16\n" +
				"**********/12\n" +
				"10.0.0.0/8\n" +
				"::ffff:10.0.0.0/104\n" +
				"::ffff:***********/112\n" +
				"::ffff:**********/108";

		final String vendorHostnameList = "outpost24.com\n" +
				"outpost24.se\n" +
				"outpost24.nl\n" +
				"outpost24.dk\n" +
				"outscan.com\n";

		final String vendorIpList = "***********/24\n" +
				"************/22\n" +
				"************/22\n" +
				"2001:67c:1084::/48";

		final String customList = "exttest.outpost24.com";

		boolean canResolveHostnames = true;
		try {
			InetAddress.getAllByName("exttest.outpost24.com");
		}
		catch (final UnknownHostException ex) {
			canResolveHostnames = false;
		}

		/*Local IPs validation*/
		// verify local IPs ********* - *************** without any blacklist --- always block loop back
		assertThat(ipService.isValidTargetList("*********")).isFalse();
		assertThat(ipService.isValidTargetList("*************")).isFalse();
		assertThat(ipService.isValidTargetList("***************")).isFalse();
		assertThat(ipService.isValidTargetList("::1")).isFalse();

		// verify local IPs 0.0.0.0 - ************* without any blacklist
		assertThat(ipService.isValidTargetList("0.0.0.0")).isTrue();
		assertThat(ipService.isValidTargetList("***********")).isTrue();
		assertThat(ipService.isValidTargetList("*************")).isTrue();

		final Path localPath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "local.blacklist");
		final Path privatePath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "private.blacklist");
		final Path customListPath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "custom.blacklist");
		final Path vendorPath = Paths.get(configService.getProperty(ConfigurationKey.blacklist_folder_path), "vendor.blacklist");
		try {
			Files.write(localPath, local.getBytes(UTF_8), StandardOpenOption.CREATE);

			// verify local IPs ********* - *************** after adding blacklist --- always block loop back
			assertThat(ipService.isValidTargetList("*********")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();
			assertThat(ipService.isValidTargetList("***************")).isFalse();
			assertThat(ipService.isValidTargetList("::1")).isFalse();

			// verify local IPs 0.0.0.0 - ************* after adding blacklist
			assertThat(ipService.isValidTargetList("0.0.0.0")).isFalse();
			assertThat(ipService.isValidTargetList("***********")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();

			/*Private IPs validation*/
			// verify private IPs *********** - ***************
			assertThat(ipService.isValidTargetList("***********")).isTrue();
			assertThat(ipService.isValidTargetList("**************")).isTrue();
			assertThat(ipService.isValidTargetList("***************")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:c0a8:0")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:c0a8:ffff")).isTrue();

			// verify private IPs ********** - **************
			assertThat(ipService.isValidTargetList("**********")).isTrue();
			assertThat(ipService.isValidTargetList("*************")).isTrue();
			assertThat(ipService.isValidTargetList("**************")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:ac10:0")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:ac1f:ffff")).isTrue();

			// verify private IPs 10.0.0.0 - 1*************
			assertThat(ipService.isValidTargetList("10.0.0.0")).isTrue();
			assertThat(ipService.isValidTargetList("1***********")).isTrue();
			assertThat(ipService.isValidTargetList("1*************")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:a00:0")).isTrue();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:aff:ffff")).isTrue();

			Files.write(privatePath, privateList.getBytes(UTF_8), StandardOpenOption.CREATE);

			// verify private IPs *********** - ***************
			assertThat(ipService.isValidTargetList("***********")).isFalse();
			assertThat(ipService.isValidTargetList("**************")).isFalse();
			assertThat(ipService.isValidTargetList("***************")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:c0a8:0")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:c0a8:ffff")).isFalse();

			// verify private IPs ********** - **************
			assertThat(ipService.isValidTargetList("**********")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();
			assertThat(ipService.isValidTargetList("**************")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:ac10:0")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:ac1f:ffff")).isFalse();

			// verify private IPs 10.0.0.0 - 1*************
			assertThat(ipService.isValidTargetList("10.0.0.0")).isFalse();
			assertThat(ipService.isValidTargetList("1***********")).isFalse();
			assertThat(ipService.isValidTargetList("1*************")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:a00:0")).isFalse();
			assertThat(ipService.isValidTargetList("0:0:0:0:0:ffff:aff:ffff")).isFalse();

			/*verifying host names*/
			assertThat(ipService.isValidTargetList("exttest.outpost24.com")).isTrue();

			Files.write(customListPath, customList.getBytes(UTF_8), StandardOpenOption.CREATE);

			// NOT allowed if resolves. Allowed if could not be resolved
			assertThat(canResolveHostnames ^ ipService.isValidTargetList("exttest.outpost24.com")).isTrue();

			/*Vandor IPs validation*/
			// verify vandor IPs ************ - **************
			assertThat(ipService.isValidTargetList("************")).isTrue();
			assertThat(ipService.isValidTargetList("*************")).isTrue();
			assertThat(ipService.isValidTargetList("**************")).isTrue();

			// verify vandor IPs *********** - *************
			assertThat(ipService.isValidTargetList("***********")).isTrue();
			assertThat(ipService.isValidTargetList("*************")).isTrue();
			assertThat(ipService.isValidTargetList("*************")).isTrue();

			// verify vandor IPs 2001:67c:1084::/48
			assertThat(ipService.isValidTargetList("2001:67c:1084:0:0:0:0:0")).isTrue();
			assertThat(ipService.isValidTargetList("2001:67c:1084:ffff:ffff:ffff:ffff:ffff")).isTrue();

			Files.write(vendorPath, ((canResolveHostnames ? vendorHostnameList : "") + vendorIpList).getBytes(UTF_8), StandardOpenOption.CREATE);

			// verify vandor IPs ************ - **************
			assertThat(ipService.isValidTargetList("************")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();
			assertThat(ipService.isValidTargetList("**************")).isFalse();

			// verify vandor IPs *********** - *************
			assertThat(ipService.isValidTargetList("***********")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();
			assertThat(ipService.isValidTargetList("*************")).isFalse();

			// verify vandor IPs 2001:67c:1084::/48
			assertThat(ipService.isValidTargetList("2001:67c:1084:0:0:0:0:0")).isFalse();
			assertThat(ipService.isValidTargetList("2001:67c:1084:ffff:ffff:ffff:ffff:ffff")).isFalse();
		}
		finally {
			FileUtils.deleteQuietly(localPath.toFile());
			FileUtils.deleteQuietly(privatePath.toFile());
			FileUtils.deleteQuietly(vendorPath.toFile());
			FileUtils.deleteQuietly(customListPath.toFile());
		}
	}

	@Test
	public void testIsCidr() {
		assertThat(ipService.isCidr("127.0.0.1")).isFalse();
		assertThat(ipService.isCidr("exttest.outpost24.com")).isFalse();
		assertThat(ipService.isCidr("127.0.0.1-*********")).isFalse();
		assertThat(ipService.isCidr("127.0.0.1/24")).isTrue();
		assertThat(ipService.isCidr("2001:db8::/96")).isTrue();
	}

	@Test
	public void testSort() {
		final List<String> ips = Arrays.asList("127.0.0.1", "********", "exttest.outpost24.com", "2001:db8::2", "2001:db8::1", "********", "********-********0");
		ipService.sort(ips);
		assertThat(ips).containsExactly("2001:db8::1", "2001:db8::2", "********", "********-********0", "********", "127.0.0.1", "exttest.outpost24.com");
	}

	@Test
	public void testGetIpCount() throws SQLException {
		assertThat(ipService.getIpCount(null)).isEqualTo(0);
		assertThat(ipService.getIpCount("")).isEqualTo(0);
		assertThat(ipService.getIpCount("127.0.0.1")).isEqualTo(1);
		assertThat(ipService.getIpCount("127.0.0.1/16")).isEqualTo(65536);
		assertThat(ipService.getIpCount("127.0.0.1\n\r*********")).isEqualTo(2);
		assertThat(ipService.getIpCount("1::1/56")).isEqualTo(Long.MAX_VALUE);
		assertThat(ipService.getIpCount("1::1/56\n127.0.0.1")).isEqualTo(Long.MAX_VALUE);
		assertThat(ipService.getIpCount("*************||test-outpost24.com")).isEqualTo(1);
		assertThat(ipService.getIpCount("2001:db8::1-2001:db8::2")).isEqualTo(2);
	}

	@Test
	public void isBlacklistedTargetTest() {
		final String blackList = "10.0.0.0/8\n"
				+ "forbidden.com\n"
				+ "*********/24\n"
				+ "2001:0000::/32\n"
				+ "ff00::/8";
		assertThat(ipService.isBlacklistedTarget("**********", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("**********", blackList)).isFalse();
		assertThat(ipService.isBlacklistedTarget("forbidden.com", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("forbidden.COM", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("***********", blackList)).isFalse();
		assertThat(ipService.isBlacklistedTarget("**********", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("Forbidden.Com", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("allowed.com", blackList)).isFalse();
		assertThat(ipService.isBlacklistedTarget("2001:67c:1e8:22::c100:68b", blackList)).isFalse();
		assertThat(ipService.isBlacklistedTarget("2001:0000:1e8:22::c100:68b", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("ff00:15:1e8:22::c100:68b", blackList)).isTrue();
		assertThat(ipService.isBlacklistedTarget("fe01:15:1e8:22::c100:68b", blackList)).isFalse();
	}

	@Test
	public void getNetworkDiscoveryTargetsTest() throws SQLException {
		// Expand CIDR and IP ranges, single IPs and hostnames will be added.
		// Invalid entries will be ignored.
		final Set<String> targets = ImmutableSet.of(
				"*************", "::1",
				"***********/30", "2001:db8:abcd:0012::0/126",
				"127.0.0.1-*********", "2001:fffd::abdd-2001:fffd::abdf",
				"hostname.com", "abc-def-123.o24.com",
				"********* - *********", "2001:db8:abcd::0 - 2001:db8:abcd::1" // invalid
		);

		final List<String> ipList = ipService.getNetworkDiscoveryTargets(String.join(System.lineSeparator(), targets));
		assertThat(ipList.toArray()).containsExactly(
				"*************", "::1", // single IPs
				"***********", "***********", "***********", "***********", // IPv4 cidr
				"2001:db8:abcd:12::", "2001:db8:abcd:12::1", "2001:db8:abcd:12::2", "2001:db8:abcd:12::3", // IPv6 cidr
				"127.0.0.1", "*********", "*********", // IPv4 range
				"2001:fffd::abdd", "2001:fffd::abde", "2001:fffd::abdf", // IPv6 range
				"hostname.com", "abc-def-123.o24.com"
		);
	}
}
