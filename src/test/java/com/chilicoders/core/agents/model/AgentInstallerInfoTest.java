package com.chilicoders.core.agents.model;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.chilicoders.core.agents.api.model.AgentInstallerInfo;
import com.chilicoders.core.storage.api.model.PresignedURLResponse;
import com.chilicoders.discover.Tag;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class AgentInstallerInfoTest {

	@Test
	public void testAgentInstallerInfoJson() throws JsonProcessingException {
		final ObjectMapper objectMapper = new ObjectMapper();
		final String expectedJson = "{\n" +
				"  \"cacheid\": \"a7508c26-154a-455e-b584-3fb056cfbfbc\",\n" +
				"  \"platform\": \"linux\",\n" +
				"  \"architecture\": \"amd64\",\n" +
				"  \"packageType\": \"rpm\",\n" +
				"  \"expires\": \"2024-12-31T23:59:59Z\",\n" +
				"	\"tags\": [\n" +
				"        {\n" +
				"            \"key\": \"device\",\n" +
				"            \"value\": \"etho\"\n" +
				"        },\n" +
				"        {\n" +
				"            \"key\": \"mac\",\n" +
				"            \"value\": \"11:11:11:ff:ff:ff\"\n" +
				"        },\n" +
				"        {\n" +
				"            \"key\": \"device\",\n" +
				"            \"value\": \"etho1\"\n" +
				"        },\n" +
				"        {\n" +
				"            \"key\": \"os\",\n" +
				"            \"value\": null\n" +
				"        }\n" +
				"    ]," +
				"  \"customAttributes\": {\n" +
				"    \"dep\": \"RnD\",\n" +
				"    \"critical\": false\n" +
				"  },\n" +
				"  \"allowedUses\": 10,\n" +
				"  \"uploadUrl\": {\n" +
				"    \"url\": \"https://s3-example\",\n" +
				"    \"headers\": {\n" +
				"      \"Authorization\": [\"Bearer exampletoken\"]\n" +
				"    }\n" +
				"  }\n" +
				"}";
		final JsonNode expectedJsonObject = objectMapper.readTree(expectedJson);

		final Map<String, Object> customAttributes = new HashMap<>();
		customAttributes.put("dep", "RnD");
		customAttributes.put("critical", false);
		final List<Tag> agentTags = new ArrayList<>(Arrays.asList(
				new Tag("device", "etho"),
				new Tag("mac", "11:11:11:ff:ff:ff"),
				new Tag("device", "etho1"),
				new Tag("os", null)
		));

		final PresignedURLResponse presignedURLResponse = new PresignedURLResponse();
		presignedURLResponse.setUrl("https://s3-example");

		final Map<String, List<String>> headers = new HashMap<>();
		final List<String> authorization = Collections.singletonList("Bearer exampletoken");
		headers.put("Authorization", authorization);
		presignedURLResponse.setHeaders(headers);

		final AgentInstallerInfo agentInstallerInfo = AgentInstallerInfo.builder()
				.cacheId("a7508c26-154a-455e-b584-3fb056cfbfbc")
				.platform("linux")
				.architecture("amd64")
				.packageType("rpm")
				.uploadUrl(presignedURLResponse)
				.expires("2024-12-31T23:59:59Z")
				.allowedUses(10L)
				.customAttributes(customAttributes)
				.tags(agentTags)
				.build();

		final JsonNode installerInfoJson = objectMapper.readTree(objectMapper.writeValueAsString(agentInstallerInfo));
		assertThat(installerInfoJson).isEqualTo(expectedJsonObject);
	}
}
