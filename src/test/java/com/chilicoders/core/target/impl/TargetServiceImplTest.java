package com.chilicoders.core.target.impl;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.targets.api.TargetData;
import com.chilicoders.core.targets.api.TargetGroupInterface;
import com.chilicoders.core.targets.api.TargetInfo;
import com.chilicoders.core.targets.api.TargetListCriteria;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.core.targets.api.model.TargetAuthenticationInterface;
import com.chilicoders.core.targets.api.model.TargetAuthenticationType;
import com.chilicoders.core.test.BaseSpringTestWithRollback;
import com.chilicoders.core.test.TestSpringDataSourceConfig;
import com.chilicoders.core.user.impl.TestUserDetails;
import com.chilicoders.core.user.impl.UserServiceImplTest;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.model.HostInfo;
import com.chilicoders.test.TestUtil;
import com.chilicoders.test.model.TestTarget;
import com.chilicoders.test.model.TestTargetGroup;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {TestSpringDataSourceConfig.class})
@ActiveProfiles("testCore")
public class TargetServiceImplTest extends BaseSpringTestWithRollback implements AbstractTargetServiceImplTest {
	@Autowired
	private TargetService targetService;

	@Autowired
	private TargetDao targetDao;

	@Autowired
	private TestUtil testUtil;

	@Autowired
	private ConfigurationService configService;

	@Before
	public void before() {
		init();
	}

	@Test
	public void testTargetAuthentication() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final TargetInfoImpl target = new TargetInfoImpl();
		target.setHostname("junit.outpost24.com");
		target.setIpAddress("*******");
		target.setUserId(user.getMainUserId());

		testUtil.executeSql("INSERT INTO tuserdatas(xid, xuserxid, ipaddress, hostname, authenticationtype) "
						+ "VALUES (NEXTVAL('tuserdatas_seq'), ?, ?::INET, ?, ?)",
				user.getMainUserId(), target.getIpAddress(), target.getHostname(), TargetAuthenticationType.SSH.getId());

		final Long id = testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT MAX(xid) FROM tuserdatas WHERE xuserxid = ?", user.getMainUserId()));
		testUtil.executeSql("INSERT INTO tauthentications(xipxid, type, sshusername, sshpassword, smbNtlmV1, resultLevel) "
						+ "VALUES (?, ?, ?, ?, 0, NULL)",
				id, TargetAuthenticationType.SSH.getId(), "USERNAME", "PASSWORD");

		final TargetAuthenticationInterface auth = targetService.getTargetAuthentication(id);
		assertThat(auth.getSshUsername()).isEqualTo("USERNAME");
		assertThat(auth.getSshPassword()).isEqualTo("PASSWORD");

		testUtil.executeSql("INSERT INTO tauthentications(xipxid, type, smbusername, smbpassword, smbNtlmV1, resultLevel) "
						+ "VALUES (?, ?, ?, ?, 0, NULL)",
				id, TargetAuthenticationType.SMB.getId(), "SMBUSERNAME", "SMBPASSWORD");
		final List<? extends TargetAuthenticationInterface> authentications = targetService.getTargetAuthentications(Collections.singletonList(id));
		assertThat(authentications.size()).isEqualTo(1);
		assertThat(authentications.get(0).getSshUsername()).isEqualTo("USERNAME");
		assertThat(authentications.get(0).getResultLevel()).isEqualTo(0);

		testUtil.executeSql("UPDATE tuserdatas SET authenticationtype = 0 WHERE xid = ?", id);
		assertThat(targetService.getTargetAuthentication(id)).isNull();
	}

	@Test
	public void testGetById() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final TargetInfoImpl target = new TargetInfoImpl();
		target.setHostname("junit.outpost24.com");
		target.setIpAddress("*******");
		target.setUserId(user.getMainUserId());
		target.setNetbios("\\\\WINDOWS");
		target.setServiceNowName("SNNOW");

		testUtil.executeSql("INSERT INTO tuserdatas(xid, xuserxid, ipaddress, hostname, netbios, snsysid) "
						+ "VALUES (NEXTVAL('tuserdatas_seq'), ?, ?::INET, ?, ?, 1)",
				user.getMainUserId(), target.getIpAddress(), target.getHostname(), target.getNetbios());

		testUtil.executeSql("INSERT INTO snassets(xid, xuserxid, snsysid, snname) "
						+ "VALUES (NEXTVAL('snassets_seq'), ?, 1, ?)",
				user.getMainUserId(), "SNNOW");

		final long id = testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT MAX(xid) FROM tuserdatas WHERE xuserxid = ?", user.getMainUserId()));
		final TargetInfo loaded = targetService.getById(user, id);
		compareTargets(target, loaded);
	}

	/**
	 * Compare two target informations and check they contain the sam information.
	 *
	 * @param expected The expected information.
	 * @param actual Actual information.
	 */
	private void compareTargets(final TargetInfo expected, final TargetInfo actual) {
		assertThat(actual.getIpAddress()).isEqualTo(expected.getIpAddress());
		assertThat(actual.getHostname()).isEqualTo(expected.getHostname());
		assertThat(actual.getNetbios()).isEqualTo(expected.getNetbios());
		assertThat(actual.getServiceNowName()).isEqualTo(expected.getServiceNowName());
	}

	@Test
	public void testTargetHostnames() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		testUtil.executeSql("INSERT INTO tuserdatas(xid, xuserxid, ipaddress, hostname, netbios, snsysid) "
						+ "VALUES (NEXTVAL('tuserdatas_seq'), ?, ?::INET, ?, ?, 1)",
				user.getMainUserId(), "*******", "host1", null);
		testUtil.executeSql("INSERT INTO tuserdatas(xid, xuserxid, ipaddress, hostname, netbios, snsysid) "
						+ "VALUES (NEXTVAL('tuserdatas_seq'), ?, ?::INET, ?, ?, 1)",
				user.getMainUserId(), "*******", "host2", null);

		final long id = testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT MAX(xid) FROM tuserdatas WHERE xuserxid = ?", user.getMainUserId()));
		final Map<Long, String> hostNames = targetService.getTargetHostnames(new Long[] {id, id - 1, id + 1}, user.getMainUserId());
		assertThat(hostNames.size()).isEqualTo(2);
		assertThat(hostNames.get(id - 1)).isEqualTo("host1");
		assertThat(hostNames.get(id)).isEqualTo("host2");
	}

	@Test
	public void testGetByAgentId() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		testUtil.executeSql("INSERT INTO tuserdatas(xid, xuserxid, agentid, hostname, netbios, snsysid) "
						+ "VALUES (NEXTVAL('tuserdatas_seq'), ?, ?, ?, ?, 1)",
				user.getMainUserId(), "agentid", "host1", null);

		assertThat(targetService.getByAgentId("agentid").getAgentId()).isEqualTo("agentid");
		assertThat(targetService.getByAgentId("nonexistant")).isNull();
	}

	@Test
	public void testUpdateTargetAuthentication() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		testUpdateTargetAuthentication(user.getMainUserId(), testUtil.getStatementExec(), targetService);
	}

	@Test
	public void testGetTargetGroup() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final long targetGroupId = testUtil.insertTargetGroup(TestTargetGroup.builder().userId(user.getMainUserId()).name("Test").build());
		final TargetGroupInterface targetGroup = targetService.getTargetGroup(targetGroupId, user.getMainUserId());
		assertThat(targetGroup).isNotNull();
		assertThat(targetGroup.getName()).isEqualTo("Test");
		assertThat(targetService.getTargetGroup(targetGroupId, user.getMainUserId() + 1)).isNull();
		assertThat(targetGroup.isRuleBased()).isFalse();

		final long targetGroupRuleId = testUtil.insertTargetGroup(TestTargetGroup.builder().userId(user.getMainUserId()).name("Testrule").build());
		testUtil.executeSql(new NativeSqlStatement("INSERT INTO xlinkrules(xid, field, value, operator, definedquery, dynamic) VALUES "
				+ "(?, 'LATESTSCANDATE', '2016-02-19', '=', 'LATESTSCANDATE::date =''2016-02-19''::date', false)", targetGroupRuleId));

		final TargetGroupInterface targetGroupRule = targetService.getTargetGroup(targetGroupRuleId, user.getMainUserId());
		assertThat(targetGroupRule.isRuleBased()).isTrue();
	}

	@Test
	public void testGetCriteria() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final TestTarget target = TestTarget.builder().ipAddress("*******").userId(user.getMainUserId()).build();
		final long targetId1 = testUtil.insertTarget(target);
		testUtil.executeSql("UPDATE targetscandata SET inactivediscoveries = 3 WHERE targetId = ?", targetId1);
		final TestTarget target2 = TestTarget.builder().ipAddress("*******").userId(user.getMainUserId()).build();
		final long targetId2 = testUtil.insertTarget(target2);
		testUtil.executeSql("UPDATE targetscandata SET inactivediscoveries = 4 WHERE targetId = ?", targetId2);
		final List<? extends TargetInfo> targets = targetService.listTargets(user, TargetListCriteria.builder().minimumInactiveDiscoveries(4L).build());
		assertThat(targets).hasSize(1);
		assertThat(targets.get(0).getId()).isEqualTo(targetId2);
	}

	@Test
	public void testCreateTargetGroup() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final long targetGroupId = targetService.createTargetGroup(user, "Target group", -1, false);
		final TargetGroupInterface targetGroup = targetService.getTargetGroup(targetGroupId, user.getMainUserId());
		assertThat(targetGroup).isNotNull();
		assertThat(targetGroup.getName()).isEqualTo("Target group");
		assertThat(targetGroup.getUserId()).isEqualTo(user.getMainUserId());
	}

	@Test
	public void testCreateTarget() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final TargetData targetData = TargetData.builder().ipAddress("************").pci(true).build();
		final long targetId = targetService.createTarget(user, targetData);
		final List<? extends TargetInfo> targets = targetService.listTargets(user, TargetListCriteria.builder().id(targetId).build());
		assertThat(targets).hasSize(1);
		assertThat(targets.get(0).isPci()).isTrue();
		assertThat(targets.get(0).getIpAddress()).isEqualTo("************");
	}

	@Test
	public void testMoveTarget() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final long targetGroupId = targetService.createTargetGroup(user, "Target group", -1, false);
		final long targetGroupId2 = targetService.createTargetGroup(user, "Target group2", -1, false);
		final TargetData targetData = TargetData.builder().ipAddress("************").build();
		final long targetId = targetService.createTarget(user, targetData);
		targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, false, targetGroupId2, null, null);
		assertThat(testUtil.getStatementExec()
				.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(xid) AS ids FROM xlinkgeneric WHERE xipxid = ?", targetId), "ids")).hasSameElementsAs(
				Collections.singletonList(targetGroupId2));
		targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, true, targetGroupId, new Long[] {targetGroupId2}, null);
		assertThat(testUtil.getStatementExec()
				.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(xid) AS ids FROM xlinkgeneric WHERE xipxid = ?", targetId), "ids")).hasSameElementsAs(
				Collections.singletonList(targetGroupId));
		targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, false, targetGroupId2, null, null);
		assertThat(testUtil.getStatementExec()
				.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(xid) AS ids FROM xlinkgeneric WHERE xipxid = ?", targetId), "ids")).hasSameElementsAs(
				Arrays.asList(targetGroupId, targetGroupId2));
	}

	@Test
	public void testMoveTargetOnlyAllowOneTargetgroup() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		user.setAllowTargetMultipleGroups(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());
		final long targetGroupId = targetService.createTargetGroup(user, "Target group", -1, false);
		final long targetGroupId2 = targetService.createTargetGroup(user, "Target group2", -1, false);
		final TargetData targetData = TargetData.builder().ipAddress("************").build();
		final long targetId = targetService.createTarget(user, targetData);
		targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, false, targetGroupId2, null, null);
		assertThat(testUtil.getStatementExec()
				.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(xid) AS ids FROM xlinkgeneric WHERE xipxid = ?", targetId), "ids")).hasSameElementsAs(
				Collections.singletonList(targetGroupId2));
		targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, true, targetGroupId, new Long[] {targetGroupId2}, null);
		assertThat(testUtil.getStatementExec()
				.getIdsArray(new NativeSqlStatement("SELECT ARRAY_AGG(xid) AS ids FROM xlinkgeneric WHERE xipxid = ?", targetId), "ids")).hasSameElementsAs(
				Collections.singletonList(targetGroupId));
		final Pair<Boolean, String> result = targetService.changeTargetGroupsForTargets(user, "" + targetId, null, false, false, false, targetGroupId2, null, null);
		assertThat(result.getKey()).isFalse();
		assertThat(result.getValue()).isEqualTo("1 targets already exists in other target groups, including ************");
	}

	@Test
	public void testTargetTemplateOverride() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final TestTarget target = TestTarget.builder().ipAddress("*******").userId(user.getMainUserId()).build();
		final long targetId = testUtil.insertTarget(target);

		final long templateId = testUtil.insertSql(new NativeSqlStatement(
				"INSERT INTO tsavedscanprefs(xid, xuserxid, targetoverride) VALUES (NEXTVAL('tsavedscanprefs_seq'), ?, ?) RETURNING xid",
				user.getMainUserId(), targetId));

		final NativeSqlStatement sql = new NativeSqlStatement(
				"SELECT u.xid, HOST(u.ipaddress) AS ipaddress, u.macaddress, u.scannerid, u.hostname, u.netbios, u.confirmed, u.hiddenurls, "
				+ "u.xuserxid, u.pci, (SELECT xid FROM tsavedscanprefs WHERE targetoverride = u.xid AND xid > 0) AS templateoverride "
				+ "FROM vuserdata u WHERE u.xid = ?", targetId);

		final List<? extends TargetInfo> targets = targetDao.getTargets(sql);
		assertThat(targets).hasSize(1);
		assertThat(targets.get(0).getTemplateOverride()).isEqualTo(templateId);
	}

	@Test
	public void testTargetTemplateOverrideFromDao() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final TestTarget target1 = TestTarget.builder().ipAddress("*******").userId(user.getMainUserId()).build();
		final long targetId1 = testUtil.insertTarget(target1);

		final TestTarget target2 = TestTarget.builder().ipAddress("*******").userId(user.getMainUserId()).build();
		final long targetId2 = testUtil.insertTarget(target2);

		final long templateId = testUtil.insertSql(new NativeSqlStatement(
				"INSERT INTO tsavedscanprefs(xid, xuserxid, targetoverride) VALUES (NEXTVAL('tsavedscanprefs_seq'), ?, ?) RETURNING xid",
				user.getMainUserId(), targetId1));

		final TargetInfo savedTarget1 = targetDao.getById(user, targetId1);
		assertThat(savedTarget1.getTemplateOverride()).isEqualTo(templateId);

		final TargetInfo savedTarget2 = targetDao.getById(user, targetId2);
		assertThat(savedTarget2.getTemplateOverride()).isEqualTo(0);
	}

	@Test
	public void testGetTargetsWithUsesLicense() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final TestTarget newTarget = TestTarget.builder()
				.userId(user.getMainUserId())
				.ipAddress("********")
				.pci(true)
				.build();

		final long targetId = testUtil.insertTarget(newTarget);
		testUtil.executeSql("UPDATE targetscandata SET useslicense = 1 WHERE targetid = ?", targetId);

		final TargetInfo target = targetDao.getById(user, targetId);
		assertThat(target.getId()).isEqualTo(targetId);
		assertThat(target.usesLicense()).isTrue();

		final NativeSqlStatement sql = new NativeSqlStatement("SELECT DISTINCT u.xid, u.hostnameid, HOST(u.ipaddress) AS ipaddress, u.hostname, "
				+ "CASE WHEN ts.useslicense = 1 THEN true ELSE false END AS useslicense, u.pci FROM tuserdatas u "
				+ "LEFT JOIN targetscandata ts ON u.xid = ts.targetid WHERE u.xid = ?", targetId);

		final List<? extends TargetInfo> targets = targetDao.getTargets(sql);
		assertThat(targets).hasSize(1);
		assertThat(targets.get(0).usesLicense()).isTrue();
	}

	@Test
	public void testRemoveTargetGroup() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final long targetGroupId = testUtil.insertTargetGroup(TestTargetGroup.builder().userId(user.getMainUserId()).name("Test").build());
		assertThat(targetGroupId).isGreaterThan(0);

		final boolean result = targetService.removeTargetGroup(user, targetGroupId, "test");
		assertThat(result).isTrue();
	}

	@Test
	public void testRemovePciTargetGroup() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final long mainUserId = user.getMainUserId();

		final TestTargetGroup targetGroup = TestTargetGroup.builder().pci(1).userId(mainUserId).name("Test").build();

		final long targetGroupId = testUtil.insertTargetGroup(targetGroup); // create target group
		assertThat(targetGroupId).isGreaterThan(0);

		final TestTarget newTarget = TestTarget.builder()
				.userId(mainUserId)
				.ipAddress("********")
				.pci(true)
				.build();
		final long targetId = testUtil.insertTarget(newTarget); // create target
		testUtil.insertLinkGeneric(targetGroupId, targetId); // assign target to the target group

		assertThat(targetService.getById(user, targetId)).isNotNull(); // verify that target exists
		assertThat(targetService.removeTargetGroup(user, targetGroupId, "test")).isTrue(); // remove the target group
		assertThat(targetService.getById(user, targetId)).isNull(); // verify that target is deleted as well
		assertThat(targetService.getTargetGroup(targetGroupId, mainUserId)).isNull(); // verify that target group doesn't exist
	}

	@Test
	public void testNumberOfAllowedHosts() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		configService.setProperty(ConfigKeys.ConfigurationBooleanKey.hiab_enabled.getConfigKey(), "0");
		user.setOutscanIp(-1);
		assertThat(targetService.numberOfAllowedHosts(user, false, false, false, 10, false)).isEqualTo(Long.MAX_VALUE);
		user.setOutscanIp(1);
		assertThat(targetService.numberOfAllowedHosts(user, false, false, false, 10, false)).isEqualTo(10);
		user.setOutscanInternalIp(2);
		assertThat(targetService.numberOfAllowedHosts(user, false, false, false, 10, true)).isEqualTo(20);
		user.setPciTargetLimit(3);
		assertThat(targetService.numberOfAllowedHosts(user, true, false, false, 10, false)).isEqualTo(30);

		configService.setProperty(ConfigKeys.ConfigurationBooleanKey.hiab_enabled.getConfigKey(), "1");
		user.setHiabIp(-1);
		assertThat(targetService.numberOfAllowedHosts(user, false, false, false, 10, false)).isEqualTo(Long.MAX_VALUE);
		user.setHiabIp(1);
		assertThat(targetService.numberOfAllowedHosts(user, false, false, false, 10, false)).isEqualTo(10);
		user.setHiabExternalIp(2);
		assertThat(targetService.numberOfAllowedHosts(user, false, true, false, 10, false)).isEqualTo(20);
	}

	@Test
	public void testAddCommonHostNames() throws SQLException {
		final TestUserDetails user = new TestUserDetails(false);
		UserServiceImplTest.addUser(user, testUtil.getStatementExec());

		final StringBuilder xids = new StringBuilder();
		final StringBuilder addToGroup = new StringBuilder();

		final HostInfo hostInfo1 = new HostInfo("outpost24.com", "127.0.0.1", 0, "outpost24.com", null, false, null);
		final HostInfo hostInfo2 = new HostInfo("outpost24.com", "127.0.0.1", 0, "outpost24.com", null, false, null);
		final HostInfo hostInfo3 = new HostInfo("www.outpost24.com", "127.0.0.1", 0, "www.outpost24.com", null, false, null);

		final List<HostInfo> ips = Arrays.asList(hostInfo1, hostInfo2, hostInfo3);
		assertThat(hostInfo1.equals(hostInfo2)).isTrue();
		assertThat(hostInfo1.equals(hostInfo3)).isFalse();
		assertThat(hostInfo1.hashCode()).isNotEqualTo(hostInfo3.hashCode());

		targetService.addCommonHostNames(user.getMainUserId(), xids, ips, addToGroup);
		assertThat(testUtil.getStatementExec().getLong(new NativeSqlStatement("SELECT COUNT(*) FROM tuserdatas WHERE xuserxid = ?", user.getMainUserId()))).isEqualTo(1);
	}
}
