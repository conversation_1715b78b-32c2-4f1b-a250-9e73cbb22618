package com.chilicoders.core.events.api;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import com.chilicoders.core.user.api.EmailType;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.impl.jpa.UserPermissionsImpl;
import com.chilicoders.model.Event;
import com.chilicoders.model.events.properties.EventDiscoveryProperties;
import com.chilicoders.model.events.properties.EventScanDoneProperties;
import com.chilicoders.model.events.properties.EventScannerMissingProperties;

@RunWith(MockitoJUnitRunner.class)
public class EventValidatorTest {
	@Test
	public void testValidateSubuserEvent() {
		final UserDetails subUser = mock(UserDetails.class);
		final UserPermissionsImpl permissions = new UserPermissionsImpl();
		when(subUser.getPermissions()).thenReturn(permissions);

		final Event targetAddedEvent = new Event(Event.O24Event.TargetAdd);
		permissions.setBoadmingroups(false);
		assertThat(EventValidator.validateSubuserEvent(targetAddedEvent, subUser)).isFalse();
		permissions.setBoadmingroups(true);
		assertThat(EventValidator.validateSubuserEvent(targetAddedEvent, subUser)).isTrue();

		final Event discoveryDoneEvent = new Event(Event.O24Event.DiscoveryDone);
		permissions.setBoadmingroups(false);
		assertThat(EventValidator.validateSubuserEvent(discoveryDoneEvent, subUser)).isFalse();
		final EventDiscoveryProperties eventDiscoveryProperties = EventDiscoveryProperties.builder()
				.emailType(EmailType.HTML)
				.build();
		discoveryDoneEvent.setProperties(eventDiscoveryProperties);
		assertThat(EventValidator.validateSubuserEvent(discoveryDoneEvent, subUser)).isFalse();
		permissions.setBoadmingroups(true);
		assertThat(EventValidator.validateSubuserEvent(discoveryDoneEvent, subUser)).isTrue();

		final Event discoveryAliveEvent = new Event(Event.O24Event.DiscoveryAlive);
		permissions.setBoadmingroups(false);
		assertThat(EventValidator.validateSubuserEvent(discoveryAliveEvent, subUser)).isFalse();
		permissions.setBoadmingroups(true);
		assertThat(EventValidator.validateSubuserEvent(discoveryAliveEvent, subUser)).isTrue();

		final Event scanScheduleScheduledEvent = new Event(Event.O24Event.ScanScheduleScheduled);
		permissions.setBoschedules(false);
		assertThat(EventValidator.validateSubuserEvent(scanScheduleScheduledEvent, subUser)).isFalse();
		permissions.setBoschedules(true);
		assertThat(EventValidator.validateSubuserEvent(scanScheduleScheduledEvent, subUser)).isTrue();

		final Event exploitAvailableEvent = new Event(Event.O24Event.ExploitAvailable);
		permissions.setBoreports(false);
		assertThat(EventValidator.validateSubuserEvent(exploitAvailableEvent, subUser)).isFalse();
		permissions.setBoreports(true);
		assertThat(EventValidator.validateSubuserEvent(exploitAvailableEvent, subUser)).isTrue();

		final Event verifyDoneEvent = new Event(Event.O24Event.VerifyDone);
		permissions.setBoreports(false);
		permissions.setVerifyscan(false);
		assertThat(EventValidator.validateSubuserEvent(verifyDoneEvent, subUser)).isFalse();
		permissions.setBoreports(true);
		permissions.setVerifyscan(true);
		assertThat(EventValidator.validateSubuserEvent(verifyDoneEvent, subUser)).isTrue();

		final Event scanDoneEvent = new Event(Event.O24Event.ScanDone);
		permissions.setBoemail(false);
		assertThat(EventValidator.validateSubuserEvent(scanDoneEvent, subUser)).isFalse();
		final EventScanDoneProperties scanDoneProperties = EventScanDoneProperties.builder()
				.scheduleName("Test")
				.build();
		scanDoneEvent.setProperties(scanDoneProperties);
		assertThat(EventValidator.validateSubuserEvent(scanDoneEvent, subUser)).isFalse();
		permissions.setBoemail(true);
		assertThat(EventValidator.validateSubuserEvent(scanDoneEvent, subUser)).isTrue();

		final Event hiabUpdateFailedEvent = new Event(Event.O24Event.HiabUpdateFailed);
		permissions.setBhadmin(false);
		assertThat(EventValidator.validateSubuserEvent(hiabUpdateFailedEvent, subUser)).isFalse();
		permissions.setBhadmin(true);
		assertThat(EventValidator.validateSubuserEvent(hiabUpdateFailedEvent, subUser)).isTrue();

		final Event scannerMissingEvent = new Event(Event.O24Event.ScannerMissing);
		final EventScannerMissingProperties scannerMissingProperties = EventScannerMissingProperties.builder()
				.scannerId(1)
				.build();
		scannerMissingEvent.setProperties(scannerMissingProperties);
		permissions.setAllScanners(false);
		assertThat(EventValidator.validateSubuserEvent(scannerMissingEvent, subUser)).isFalse();
		permissions.setAllScanners(true);
		assertThat(EventValidator.validateSubuserEvent(scannerMissingEvent, subUser)).isTrue();
	}
}
