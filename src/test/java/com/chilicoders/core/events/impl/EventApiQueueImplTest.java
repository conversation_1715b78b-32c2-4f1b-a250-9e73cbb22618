package com.chilicoders.core.events.impl;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.chilicoders.event.entities.QueueLogEntry;
import com.chilicoders.event.jpa.repository.QueueLogEntryRepository;
import com.chilicoders.model.Event;
import com.chilicoders.model.EventType;

@RunWith(MockitoJUnitRunner.class)
public class EventApiQueueImplTest {
	@InjectMocks
	private EventApiQueueImpl eventApi;

	@Mock
	private QueueLogEntryRepository queueLogEntryRepository;

	@Test
	public void testEventSaved() {
		final Long USER_ID = 123L;
		final Event event = new Event(Event.O24Event.ReleaseNotes);
		event.setUserId(USER_ID);
		event.setEventType(EventType.Unspecified);
		when(queueLogEntryRepository.save((QueueLogEntry) any())).thenAnswer(i -> i.getArguments()[0]);
		eventApi.handleEvent(event);
		final ArgumentCaptor<QueueLogEntry> argument = ArgumentCaptor.forClass(QueueLogEntry.class);
		verify(queueLogEntryRepository).save(argument.capture());
		assertEquals(USER_ID, argument.getValue().getUserId());
		assertEquals(Event.O24Event.ReleaseNotes.getId(), argument.getValue().getEvent());
	}
}