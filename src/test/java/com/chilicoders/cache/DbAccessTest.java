package com.chilicoders.cache;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import org.junit.Test;

import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.util.Configuration;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class DbAccessTest {

	static {
		XMLAPITestBase.initConfiguration();
	}

	@Test
	public void testGetInstanceReturnsSingleton() {
		final DbAccess instance = DbAccess.getInstance();
		assertNotNull(instance);
		assertEquals(instance, DbAccess.getInstance());
	}

	@Test
	public void testGetAndFreeConnection() throws SQLException {
		final Connection connection = DbAccess.getInstance().getConnection();
		assertNotNull(connection);
		DbAccess.getInstance().freeConnection(connection);
	}

	@Test(expected = DbAccessException.class)
	public void testGetConnectionNoMoreConnectionsAvailable() throws SQLException {
		final List<Connection> connections = new ArrayList<>();
		final int poolSize = Configuration.getProperty(ConfigurationIntKey.db_maxconnections);
		// Wait 5ms for DB handle
		Configuration.setProperty(ConfigurationIntKey.db_waitforhandle, 5, false);

		try {
			for (int i = 0; i <= poolSize; i++) {
				final Connection connection = DbAccess.getInstance().getConnection();
				connections.add(connection);
			}
		}
		finally {
			for (final Connection conn : connections) {
				DbAccess.getInstance().freeConnection(conn);
			}
		}
	}

	@Test
	public void testGetMaximumNumberOfConnections() throws InterruptedException {
		final int poolSize = Configuration.getProperty(ConfigurationIntKey.db_maxconnections);
		final int metricsPoolSize = Configuration.getProperty(ConfigurationIntKey.db_maxconnections_for_metrics);
		// Wait max 5s for DB handle
		final int maxWaitTime = 5 * 1000;
		Configuration.setProperty(ConfigurationIntKey.db_waitforhandle, maxWaitTime, false);

		final List<Connection> connections = new ArrayList<>();
		final AtomicReference<Throwable> exceptionConnectionPoolThread = new AtomicReference<>(null);
		// Create thread that will acquire one more connection than in the pool
		final Thread connectionPoolThread = new Thread(() -> {
			try {
				exhaustConnectionPool(poolSize, connections);
			}
			catch (final Throwable throwable) {
				exceptionConnectionPoolThread.set(throwable);
			}
		});

		final List<Connection> metricConnections = new ArrayList<>();
		final AtomicReference<Throwable> exceptionMetricsConnectionPoolThread = new AtomicReference<>(null);
		// Create thread that will acquire all metric connections in the metrics pool
		final Thread metricsConnectionPoolThread = new Thread(() -> {
			try {
				emptyMetricsConnectionPool(metricsPoolSize, metricConnections);
			}
			catch (final Throwable throwable) {
				exceptionMetricsConnectionPoolThread.set(throwable);
			}
		});

		final long start = System.currentTimeMillis();
		connectionPoolThread.start();
		Thread.sleep(100);
		metricsConnectionPoolThread.start();
		metricsConnectionPoolThread.join();

		DbAccess.getInstance().freeConnection(connections.remove(0));

		connectionPoolThread.join();

		// Assert that the freed connection interrupts the wait for free connection
		final long end = System.currentTimeMillis();
		assertThat(end - start).isLessThan(maxWaitTime);

		// Assert no exceptions
		assertThat(exceptionConnectionPoolThread.get()).isNull();
		assertThat(exceptionMetricsConnectionPoolThread.get()).isNull();

		// Assert maximum number of connections acquired
		assertThat(connections).hasSize(poolSize);
		assertThat(metricConnections).hasSize(metricsPoolSize);

		for (final Connection conn : connections) {
			DbAccess.getInstance().freeConnection(conn);
		}
		for (final Connection conn : metricConnections) {
			DbAccess.getInstance().freeMetricsConnection(conn);
		}
	}

	/**
	 * Acquires the given number of metrics database connections
	 *
	 * @param numberOfConnections The number of connections to acquire
	 * @param metricConnections A {@link List} containing acquired {@link Connection}s
	 */
	private static void emptyMetricsConnectionPool(final int numberOfConnections, final List<Connection> metricConnections) throws SQLException {
		for (int i = 0; i < numberOfConnections; i++) {
			final Connection metricsConnection = DbAccess.getInstance().getMetricsConnection();
			metricConnections.add(metricsConnection);
		}
	}

	/**
	 * Acquires the given number of database connections + 1
	 *
	 * @param numberOfConnections The number of connections to acquire
	 * @param connections A {@link List} containing acquired {@link Connection}s
	 */
	private static void exhaustConnectionPool(final int numberOfConnections, final List<Connection> connections) throws SQLException {
		Connection connection;
		for (int i = 0; i <= numberOfConnections; i++) {
			connection = DbAccess.getInstance().getConnection();
			connections.add(connection);
		}
	}
}
