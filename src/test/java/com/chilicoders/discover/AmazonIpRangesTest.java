package com.chilicoders.discover;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.HashMap;

import org.junit.Ignore;
import org.junit.Test;

public class AmazonIpRangesTest {

	@Test
	@Ignore
	public void shouldBeInRange() {
		try {
			final AmazonIpRanges ipRanges = new AmazonIpRanges(null);
			assertTrue("Should be in amazon range", ipRanges.isInRange("*********"));
		}
		catch (final Exception e) {
			fail(e.getMessage());
		}
	}

	@Test
	@Ignore
	public void shouldNotBeInRange() {
		try {
			final AmazonIpRanges ipRanges = new AmazonIpRanges(null);
			assertFalse("Should not be in amazon range", ipRanges.isInRange("***********"));
		}
		catch (final Exception e) {
			fail(e.getMessage());
		}
	}

	@Test
	@Ignore
	public void shouldPrintIpRanges() {
		try {
			final AmazonIpRanges ipRanges = new AmazonIpRanges(null);

			for (final HashMap<String, Long> range : ipRanges.getIpRanges()) {
				System.out.println(range);
			}
		}
		catch (final Exception e) {
			fail(e.getMessage());
		}
	}

	@Test
	@Ignore
	public void shouldPrintSharedIpRanges() {
		try {
			final AmazonIpRanges ipRanges = new AmazonIpRanges(null);

			for (final HashMap<String, Long> range : ipRanges.getSharedIpRanges(null)) {
				System.out.println(range);
			}
		}
		catch (final Exception e) {
			fail(e.getMessage());
		}
	}
}
