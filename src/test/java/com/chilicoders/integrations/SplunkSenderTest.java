package com.chilicoders.integrations;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.integrations.impl.SplunkSenderImpl;
import com.chilicoders.model.Splunk;
import com.chilicoders.model.SplunkMode;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.SSLTestUtils;
import com.chilicoders.xmlapi.XMLAPITestBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.tomakehurst.wiremock.junit.WireMockRule;

public class SplunkSenderTest extends XMLAPITestBase {

	static {
		XMLAPITestBase.initConfiguration();
	}

	private final ConfigurationService configService = ServiceProvider.getConfigService();

	@Rule
	public WireMockRule wireRule = new WireMockRule(wireMockConfig().dynamicHttpsPort());

	@BeforeClass
	public static void init() {
		SSLTestUtils.setSSLHostnameVerifierAndCert();
	}

	@Test
	public void testHttpEventCollector() throws JsonProcessingException {
		configService.setProperty(ConfigurationBooleanKey.hiab_enabled.getConfigKey(), "false");
		stubFor(post("/services/collector/event")
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/json")
						.withBody("{\"text\": \"Success\", \"code\": 0}")));

		final Splunk splunk = new Splunk();
		splunk.setSplunkEnabled(true);
		splunk.setSplunkHost("127.0.0.1");
		splunk.setSplunkPort(wireRule.httpsPort());
		splunk.setSplunkIndex("index");
		splunk.setSplunkUser("MAINUSER");
		splunk.setSplunkPassword("PASSWORD");
		splunk.setSplunkMode(SplunkMode.HTTP_EVENT_COLLECTOR);
		splunk.setSplunkToken("TESTTOKEN");

		new SplunkSenderImpl(configService, splunk).send("TESTMESSAGE");

		verify(postRequestedFor(urlMatching("/services/collector/event"))
				.withHeader("Content-Type", matching("application/json.*"))
				.withRequestBody(equalTo("{\"event\":\"TESTMESSAGE\",\"index\":\"index\",\"sourcetype\":\"OUTSCAN\",\"host\":\"local\"}")));
	}
}
