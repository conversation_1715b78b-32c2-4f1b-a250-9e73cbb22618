package com.chilicoders.integrations;

import static org.assertj.core.api.Assertions.assertThat;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

import java.util.concurrent.ExecutionException;

import org.json.JSONObject;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.test.TestConstants;
import com.chilicoders.integrations.cyberark.CyberArkEngine;
import com.chilicoders.integrations.cyberark.CyberArkEngine.CyberArkResponse;
import com.chilicoders.integrations.cyberark.model.CyberArkConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;

import lombok.Getter;
import lombok.Setter;

@RunWith(MockitoJUnitRunner.class)
public class CyberArkEngineTest {
	@Rule
	public WireMockRule wireMockRule = new WireMockRule(wireMockConfig().dynamicHttpsPort());

	@Mock
	private ConfigurationService configurationService;

	@Mock
	private IpService ipService;

	@Test
	public void testCyberArk() throws ExecutionException {
		final String testSafe = "test-safe";
		final String testFolder = "test-folder";
		final String testObject = "test-object";
		final String testContent = "test-content";

		stubFor(get(urlPathEqualTo("/AIMWebService/api/Accounts")).willReturn(aResponse()
				.withStatus(HttpStatus.OK.value())
				.withHeader("Content-Type", "application/json")
				.withBody(new JSONObject().put("Content", testContent).put("Safe", testSafe).toString())));

		final CyberArkConfig config = new CyberArkConfig();
		config.setUrl("https://127.0.0.1:" + wireMockRule.httpsPort());
		config.setApplicationId("testAppId");
		config.setCertificate(TestConstants.CERTIFICATE1);

		final CyberArkEngine cyberArk = CyberArkEngine.getInstance(1, configurationService, config, ipService);

		final CyberArkResponse response = cyberArk.getContent(testSafe, testFolder, testObject);

		final JSONObject json = new JSONObject(response.getContent());
		assertThat(json.get("Content")).isEqualTo(testContent);

		CyberArkEngine.destroy(1);
	}

	@Getter
	@Setter
	private static class CyberArkConfig implements CyberArkConfiguration {
		private String url;
		private String applicationId;
		private String certificate;
		private String defaultFolder;
		private String defaultSafe;
	}
}
