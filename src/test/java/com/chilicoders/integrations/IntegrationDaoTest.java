package com.chilicoders.integrations;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;

import javax.xml.bind.JAXBException;

import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.integrations.dao.IntegrationDao;
import com.chilicoders.integrations.impl.IntegrationDaoImpl;
import com.chilicoders.model.IntegrationInterface;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.rest.resources.IntegrationsResourceTest;
import com.chilicoders.xmlapi.XMLAPITestBase;

public class IntegrationDaoTest extends XMLAPITestBase {
	private Integer customerId;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws Exception {
		setupDatabaseForTest(false);

		this.customerId = DbObject.getLong(getConnection(), "SELECT id FROM customers WHERE userid = ?", TEST_USER_ID).intValue();
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM integrations");
		getConnection().commit();
	}

	@Test
	public void testGetIntegration() throws SQLException, JAXBException {
		final JSONObject integration = IntegrationsResourceTest.createIntegration(IntegrationType.WEBHOOK);

		final Integer integrationId = (int) DbObject.executeCountQuery(getConnection(),
				"INSERT INTO integrations (name, type, configuration, customerid, createdbyid) VALUES(?, ?, ?::JSONB, ?, ?) RETURNING id",
				"Test1", IntegrationType.WEBHOOK, integration.getJSONObject("configuration").toString(), this.customerId, XMLAPITestBase.TEST_USER_ID);

		final IntegrationDao integrationDao = new IntegrationDaoImpl(getConnection());

		final IntegrationInterface saved = integrationDao.getById(integrationId, this.customerId);
		assertThat(saved).isNotNull();
		assertThat(saved.getId()).isEqualTo(integrationId);
		assertThat(saved.getConfiguration()).isNotNull();
	}
}
