package com.chilicoders.integrations.impl;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.chilicoders.db.DbObject;
import com.chilicoders.integrations.api.IntegrationKeyService;
import com.chilicoders.integrations.model.IntegrationKey;
import com.chilicoders.model.KeyType;
import com.chilicoders.rest.resources.BaseResourceTest;
import com.chilicoders.service.ServiceProvider;

import static org.assertj.core.api.Assertions.assertThat;

public class IntegrationKeyServiceImplTest extends BaseResourceTest {
	private IntegrationKeyService keyService;

	/**
	 * Setup database.
	 */
	@Before
	public void initDatabase() throws SQLException {
		setupDatabase();
		this.keyService = ServiceProvider.getIntegrationKeyService(getConnection());
	}

	/**
	 * Clean database.
	 */
	@After
	public void cleanupDatabase() throws SQLException {
		DbObject.executeUpdate(getConnection(), "DELETE FROM integrationkeys WHERE customerid = ?", getTestUser().getCustomerId());
		getConnection().commit();
	}

	@Test
	public void testGenerateKey() throws NoSuchAlgorithmException, IOException, SQLException {
		final IntegrationKey key = keyService.generateRSA(getTestUser().getMainUserId(), getTestUser().getCustomerId());
		assertThat(key.getIntegrationId()).isNull();
	}

	@Test
	public void testGetKey() throws NoSuchAlgorithmException, IOException, SQLException {
		final long mainUserId = getTestUser().getMainUserId();
		final Integer customerId = getTestUser().getCustomerId();

		// Make sure we don't have any keys generated in the beginning
		final IntegrationKey key = keyService.getKey(mainUserId, KeyType.RSA);
		assertThat(key).isNull();

		// Generate a key and retrieve it from the database
		final IntegrationKey keyOnceGenerated = keyService.generateRSA(mainUserId, customerId);
		final IntegrationKey keyfromGet = keyService.getKey(mainUserId, KeyType.RSA);

		assertThat(keyOnceGenerated.getPublicKey()).isEqualTo(keyfromGet.getPublicKey());

		// Make sure that the integrationId is null on both the generated and the retrieved keys
		assertThat(keyOnceGenerated.getIntegrationId()).isNull();
		assertThat(keyfromGet.getIntegrationId()).isNull();

		// Generate a new key and retrieve it
		final IntegrationKey secondIntegrationKey = keyService.generateRSA(mainUserId, customerId);
		final IntegrationKey secondKeyFromGet = keyService.getKey(mainUserId, KeyType.RSA);

		// Make sure that the integrationId is still null (doesn't get the default value of 0)
		assertThat(secondIntegrationKey.getIntegrationId()).isNull();
		assertThat(secondKeyFromGet.getIntegrationId()).isNull();

		// Make sure that we only have 1 key (applies to the Old GUI only)
		final List<IntegrationKey> keys = keyService.getKeys(mainUserId);
		assertThat(keys.size()).isEqualTo(1);
		assertThat(keys.get(0).getPublicKey()).isEqualTo(secondIntegrationKey.getPublicKey());
	}
}
