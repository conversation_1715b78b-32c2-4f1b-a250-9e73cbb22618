<?xml version = '1.0' encoding = 'UTF-8'?>
<data>
	<target hostname="test1.example.com" ip="*******" platform="Linux" />
	<target hostname="test2.example.com" ip="*******" platform="Windows" />
	<target ip="*******" />

	<usergroup access="boreports|boemail|bodisable|verifyscan|stopscan|dashboard" name="Risk Analyst" />

	<targetgroup name="Test Network" targets="*******|*******" />
	<targetgroup filter="filter[0][field]:PLATFORM|filter[0][data][type]:string|filter[0][data][comparison]:all|filter[0][data][value]:Linux" name="Linux Targets" >
		<targetgroup name="Notifications" />
	</targetgroup>

	<user firstname="James" groups="Risk Analyst" lastname="Smith" />

	<schedule name="Test 1" targetgroups="Test Network" compliance="true">
		<scan end="2014-07-24 23:59" start="2014-07-24 16:00">
			<target ip="*******">
				<fact key="fact/product/version" port="80" protocol="tcp" type="apache/tomcat" value="7.0.25" />
				<fact key="fact/service" port="5432" protocol="tcp" type="running" value="postgresql" />
				<fact key="fact/service" port="10021" protocol="udp" type="running" value="ftp" />
				<fact key="fact/machine/class" port="10021" protocol="udp" type="" value="test" />
			</target>
			<target ip="*******">
				<fact key="fact/product/version" port="445" protocol="tcp" type="microsoft/windows" value="6.0 SP0" />
				<fact key="fact/product/version" port="80" protocol="tcp" type="microsoft/iis" value="7.0" />
				<fact key="fact/patch/installed" port="445" protocol="tcp" type="microsoft/iis" value="KB12345" />
				<fact key="fact/patch/installed" port="445" protocol="tcp" type="microsoft/windows" value="KB12325" />
				<ticket vulnid="1207037" />
			</target>
		</scan>
	</schedule>

	<schedule name="Was" uri="http://***************/" was="true">
		<scan end="2013-12-01 00:15" ip="***************" start="2013-12-01 00:00">
			<crawl method="GET" time="23" url="http://***************/" />
			<crawl url="http://***************/test.php">
				<finding id="250207" />
				<finding id="250200" />
				<finding id="250204" />
				<finding id="250210" />
			</crawl>
		</scan>
	</schedule>

	<schedule name="Demo Bank" swat="true" uri="http://bank.outpost24.com">
		<finding cvssvector="(AV:N/AC:L/Au:S/C:C/I:C/A:C)" explanation="The &quot;account_no&quot;-parameter is not being properly sanitized before it is used to query the database. This results in an SQL-injection.&#10; &#10; To remedy this, either ensure that the contents of the parameters are a comma separated list of numerical values or use prepared statement queries.&#10; SQL-injections can be used to run attacker-supplied database queries." firstseen="2015-06-01 10:34" fixed="0" id="250204" lastseen="2015-06-01 10:34" port="80" recreationflow="1. Login&#10; &#10; 2. Go to My Accounts&#10; &#10; 3. Click on &quot;View Transactions&quot; for any of the accounts&#10; &#10; 4. Change the URL-parameter &quot;account_no&quot; to: 1' or 1=1 --&#10; &#10; 5. Note that you are now able to see the transactions for all accounts">
			<url method="GET" path="0" post="" times="" url="http://bank.outpost24.com/aspx/Main.aspx?function=TransactionDetails&amp;account_no=1' or 1=1 --" />
		</finding>
		<finding cvssvector="(AV:N/AC:L/Au:N/C:N/I:P/A:N)" firstseen="2015-06-01 10:34" fixed="0" id="250204" lastseen="2015-06-01 10:34" port="443">
			<url method="GET" path="0" post="" times="" url="http://bank.outpost24.com/aspx/Main.aspx?function=TransactionDetails&amp;account_no=1' or 1=1 --" />
			<comment date="2016-10-10 11:11:11" parent="-1" comment="test" />
		</finding>
		<finding cvssvector="(AV:A/AC:L/Au:N/C:P/I:N/A:N)" firstseen="2015-06-01 10:34" fixed="0" id="250204" lastseen="2015-06-01 10:34" port="443">
			<url method="GET" path="0" post="" times="" url="http://bank.outpost24.com/aspx/Main.aspx?function=TransactionDetails&amp;account_no=1' or 1=1 --" />
			<file name="name" filename="test.txt" content="text" />
		</finding>
		<finding cvssvector="(AV:A/AC:L/Au:N/C:P/I:N/A:N)" firstseen="2015-06-01 10:34" fixed="0" id="250204" lastseen="2015-06-01 10:34" port="443" info="test" />
		<finding cvssvector="(AV:A/AC:L/Au:N/C:P/I:N/A:N)" firstseen="2015-06-01 10:34" fixed="0" id="1" lastseen="2015-06-01 10:34" port="443" />
		<finding cvssvector="(AV:X/AC:L/Au:N/C:P/I:N/A:N)" firstseen="2015-06-01 10:34" fixed="0" id="250204" lastseen="2015-06-01 10:34" port="443" />
	</schedule>

	<schedule name="PCI" pci="true" targets="*******|*******">
		<scan end="2014-07-24 23:59" start="2014-07-24 16:00">
			<target ip="*******">
				<fact key="fact/product/version" port="80" protocol="tcp" type="apache/tomcat" value="5.5.24" />
			</target>
			<target ip="*******">
				<fact key="fact/product/version" port="80" protocol="tcp" type="apache/tomcat" value="5.5.24" />
				<fact key="fact/product/version" port="80" protocol="tcp" type="php/php" value="5.1.1" />
			</target>
		</scan>
	</schedule>

	<policy name="Test">
		<requirement name="Test requirement 1" id="1">
			<description>Test</description>
			<solution>Test</solution>
			<settings>TEST</settings>
		</requirement>
		<requirement name="Test requirement 2" id="2" parent="1">
			<description>Test</description>
		</requirement>
		<requirement name="Test requirement 3">
			<description>Test</description>
		</requirement>
	</policy>

	<schedule name="Appsec" wasx="true" settings='{"scans": []}'>
		<scan end="2014-12-01 00:15" ip="bank.outpost24.com" start="2014-12-01 00:00">
			<crawl method="GET" time="23" url="http://***************/" />
			<crawl url="http://bank.outpost24.com/test.php">
				<finding id="250207" url="http://bank.outpost24.com/test.php" />
				<finding id="250200" url="http://bank.outpost24.com/test.php" />
				<finding id="250204" url="http://bank.outpost24.com/test.php" />
				<finding id="250210" url="http://bank.outpost24.com/test.php" />
			</crawl>
		</scan>
	</schedule>

</data>
