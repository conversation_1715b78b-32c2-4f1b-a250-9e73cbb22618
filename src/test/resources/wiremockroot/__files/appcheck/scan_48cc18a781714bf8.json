{"data": {"user_summary": {"name": "<PERSON>", "_id": "5d85e7a4079147e1"}, "scan_profile_id": "5d7708d8a215443f", "web_app_scan": {"settings": {"username_list": "", "num_concurrent_scans": 1, "scan_paths": true, "password_list": "", "plugins": {"ApckAuthIntegrator": {"force_reauth_interval": "40"}, "SimpleIDOR": {"run_only_when_authenticated": false}}, "exclude_plugins": "resin_cms\nInsecureCacheLoading\nNiktoDbPlugin\nEktronCMSRCE\nCrytoMinerDetection\nXSS\nSentinelJSInclude\nHTTPParamPollution\nBreachDBLookup\nCEBruteforcer\nJQueryVulnVersions\nHTTPOxy\nContentDiscovery\nPhpChecks\nDetectChattyPostMessage\nDebugFlagEnumeration\nMalwareDetection\nspring_data_cve_2017_8046\nReflectedDataPotentialSEOAbuse\nYSOSeriousJBOSS\nJoomlaObjectInjection\nJavaScriptMalwareDetection\nEnv_File_Discovery\nStoredUnicodeTransformation\nUserAgents\nDocumentDomainSuperDomain\nGenericCMSDetection\nJavaScriptWebsockFuzzer\nBrowserCrawler\nCheckAuthForLockouts\nBeconAuth\nMagentoGraphQLAuth\nOgierAuth\nClientCertificatePlugin\nDynamicSeedPage\nTelerikUIVulnerabilities\nYHCRAPIAuthHandler\nDepotnetAuthHandler\nMethodFlip\nScreenCloudGraphqlAuth\nDixons6A\nPathAnalysis\nTangent90AuthHandler\nSafeStyleUKAuthHelper\nSubDomainTakeoverScanner\nFlashAnalyser\nCrossDomainResources\nDetectBackupFiles\nPRSSI\nCachingChecks\nPasswordFieldWithAutocompleteEnabledPlugin\nDoubleEscapeSQLi\nStoredClientSideTemplateInjection\nBrowserCommunicationLogger\nHostAccessReporter\nPostMessageChecks\nPostMessageDynamicAnalysis\nPostMessage_DynamicAnalysis\nClearTextWebSocket\nWebsocketOriginValidation\nPiiFormIdentification\nSitemapPlugin\nSentinelSearch\nWebSearch\nListingFile\nRobotsPlugin\nDS_Store_Enumeration\nSourceMapDisclosure\nCounterBooksSeeder\nMSSearchEngines\nDomXSS\nESIInjection\nSentinelSecondOrderXSS_DB_Backed\nGetContextPathXSS\nSentinelSecondOrderXSS\nXSS2\nCSRFPasswordReset\nJoomlaScanner\nUmbracoScanner\nDotNetNukeScanner\nDrupalDetection\nWordpressScanner\nMagentoScanner\nDrupal_SA_Core_2018_006\nDrupalScanner\nSerendipityBlog\nvBulletinScanner\nLifeRayCMS\nOpenCart\nSitefinity\nAWSRFI\nAWSAccessKeyDisclosure\nAWSS3BucketEnum\nShoplift\nFlashVulnByMD5\nms15_034\nBashEnvInjection\nFlashCrossDomainPlugin\nJSVulnVersions\nPhpMailer_SenderInjection\nClickJacking\nSilverlightCrossDomainPlugin\nGhostButtRCE\nweblogic_cve_2019_2725\nJavaScriptLibraryAnalysis\nCitrix_CVE_2019_19781\nPulse_VPN_cve_2019_11510\nSaltStackCVE_2020_11651\nF5_CVE_2020_5902\nCISCO_CVE_2020_3452\nCISCO_CVE_2020_3187\nSolarWinds_CVE_2020_10148\nUnsupportedAdobeFlash\nSharepoint_CVE_2020_0646\nIFrameAudit\nDependencyConfusion\nGenericCheckBreachedPassword\nSignupAnalysis\nDowngradeHttpsConnectionDetection\nOwaAndActiveSync", "threat_intel_max_results": "100", "http_headers": "", "group_urls": false, "scan_headers": ["referer", "cookie", "user_agent", "all"], "attack": true, "max_threads": 10, "crawler_mode": "FOCUSSED", "discovery": false, "beta_checks": true, "generic_password_guessing": false, "param_names": true, "skip_contact_forms": false, "dom_xss": true}, "brute_force": {}, "post_forms": true, "authentication_ntlm": {"login_url": "", "password": "", "username": ""}, "wsdl_discovery": {"enabled": false}, "new_features": {"allow_sec1_chromium": false}, "targets": {"seeded_api": "openapi: 3.0.1\r\ninfo:\r\n  title: VAmPI\r\n  description: OpenAPI v3 specs for VAmPI\r\n  version: '0.1'\r\nservers:\r\n  - url: http://**************:5000\r\ncomponents: {}\r\npaths:\r\n  /createdb:\r\n    get:\r\n      tags:\r\n        - db-init\r\n      summary: Creates and populates the database with dummy data\r\n      description: <PERSON>reates and populates the database with dummy data\r\n      operationId: api_views.main.populate_db\r\n      responses:\r\n        '200':\r\n          description: <PERSON>reates and populates the database with dummy data\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  message:\r\n                    type: string\r\n                    example: 'Database populated.'\r\n  /:\r\n    get:\r\n      tags:\r\n        - home\r\n      summary: VAmPI home\r\n      description: >-\r\n        VAmPI is a vulnerable on purpose API. It was created in order to\r\n        evaluate the efficiency of third party tools in identifying\r\n        vulnerabilities in APIs but it can also be used in learning/teaching\r\n        purposes.\r\n      operationId: api_views.main.basic\r\n      responses:\r\n        '200':\r\n          description: Home - Help\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  message:\r\n                    type: string\r\n                    example: 'VAmPI the Vulnerable API'\r\n                  help:\r\n                    type: string\r\n                    example: 'VAmPI is a vulnerable on purpose API. It was created in order to evaluate the efficiency of third party tools in identifying vulnerabilities in APIs but it can also be used in learning/teaching purposes.'\r\n                  vulnerable:\r\n                    type: number\r\n                    example: 1\r\n  /users/v1:\r\n    get:\r\n      tags:\r\n        - users\r\n      summary: Retrieves all users\r\n      description: Displays all users with basic information\r\n      operationId: api_views.users.get_all_users\r\n      responses:\r\n        '200':\r\n          description: See basic info about all users\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: array\r\n                items:\r\n                  type: object\r\n                  properties:\r\n                    email:\r\n                      type: string\r\n                      example: '<EMAIL>'\r\n                    username:\r\n                      type: string\r\n                      example: 'name1'\r\n  /users/v1/_debug:\r\n    get:\r\n      tags:\r\n        - users\r\n      summary: Retrieves all details for all users\r\n      description: Displays all details for all users\r\n      operationId: api_views.users.debug\r\n      responses:\r\n        '200':\r\n          description: See all details of the users\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: array\r\n                items:\r\n                  type: object\r\n                  properties:\r\n                    admin:\r\n                      type: boolean\r\n                      example: false\r\n                    email:\r\n                      type: string\r\n                      example: '<EMAIL>'\r\n                    password:\r\n                      type: string\r\n                      example: 'pass1'\r\n                    username:\r\n                      type: string\r\n                      example: 'name1'\r\n  /users/v1/register:\r\n    post:\r\n      tags:\r\n        - users\r\n      summary: Register new user\r\n      description: Register new user\r\n      operationId: api_views.users.register_user\r\n      requestBody:\r\n        description: Username of the user\r\n        content:\r\n          application/json:\r\n            schema:\r\n              type: object\r\n              properties:\r\n                username:\r\n                  type: string\r\n                  example: 'John.Doe'\r\n                password:\r\n                  type: string\r\n                  example: 'password123'\r\n                email:\r\n                  type: string\r\n                  example: '<EMAIL>'\r\n        required: true\r\n      responses:\r\n        '200':\r\n          description: Sucessfully created user\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  message:\r\n                    type: string\r\n                    example: 'Successfully registered. Login to receive an auth token.'\r\n                  status:\r\n                    type: string\r\n                    enum: ['success', 'fail']\r\n                    example: 'success'\r\n        '400':\r\n          description: Invalid request\r\n          content: {}\r\n  /users/v1/login:\r\n    post:\r\n      tags:\r\n        - users\r\n      summary: Login to VAmPI\r\n      description: Login to VAmPI\r\n      operationId: api_views.users.login_user\r\n      requestBody:\r\n        description: Username of the user\r\n        content:\r\n          application/json:\r\n            schema:\r\n              type: object\r\n              properties:\r\n                username:\r\n                  type: string\r\n                  example: 'John.Doe'\r\n                password:\r\n                  type: string\r\n                  example: 'password123'\r\n        required: true\r\n      responses:\r\n        '200':\r\n          description: Sucessfully logged in user\r\n          content:\r\n            application/json:\r\n                schema:\r\n                  type: object\r\n                  properties:\r\n                    auth_token:\r\n                      type: string\r\n                      example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2NzAxNjA2MTcsImlhdCI6MTY3MDE2MDU1Nywic3ViIjoiSm9obi5Eb2UifQ.n17N4AxTbL4_z65-NR46meoytauPDjImUxrLiUMSTQw'\r\n                    message:\r\n                      type: string\r\n                      example: 'Successfully logged in.'\r\n                    status:\r\n                      type: string\r\n                      enum: ['success', 'fail']\r\n                      example: 'success'\r\n        '400':\r\n          description: Invalid request\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Password is not correct for the given username.'\r\n  /users/v1/{username}:\r\n    get:\r\n      tags:\r\n        - users\r\n      summary: Retrieves user by username\r\n      description: Displays user by username\r\n      operationId: api_views.users.get_by_username\r\n      parameters:\r\n        - name: username\r\n          in: path\r\n          description: retrieve username data\r\n          required: true\r\n          schema:\r\n            type: string\r\n            example: 'John.Doe'\r\n      responses:\r\n        '200':\r\n          description: Successfully display user info\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: array\r\n                items:\r\n                  type: object\r\n                  properties:\r\n                    username:\r\n                      type: string\r\n                      example: 'John.Doe'\r\n                    email:\r\n                      type: string\r\n                      example: '<EMAIL>'\r\n        '404':\r\n          description: User not found\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'User not found'\r\n\r\n    delete:\r\n      tags:\r\n        - users\r\n      summary: Deletes user by username (Only Admins)\r\n      description: Deletes user by username (Only Admins)\r\n      operationId: api_views.users.delete_user\r\n      parameters:\r\n        - name: username\r\n          in: path\r\n          description: Delete username\r\n          required: true\r\n          schema:\r\n            type: string\r\n            example: 'name1'\r\n      responses:\r\n        '200':\r\n          description: Sucessfully deleted user\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  message:\r\n                    type: string\r\n                    example: 'User deleted.'\r\n                  status:\r\n                    type: string\r\n                    enum: ['success', 'fail']\r\n                    example: 'success'\r\n        '401':\r\n          description: User not authorized\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    example: 'fail'\r\n                    enum: ['fail']\r\n                  message:\r\n                    type: string\r\n                    example: 'Only Admins may delete users!'\r\n        '404':\r\n          description: User not found\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    example: 'fail'\r\n                    enum: ['fail']\r\n                  message:\r\n                    type: string\r\n                    example: 'User not found!'\r\n  /users/v1/{username}/email:\r\n    put:\r\n      tags:\r\n        - users\r\n      summary: Update users email\r\n      description: Update a single users email\r\n      operationId: api_views.users.update_email\r\n      parameters:\r\n        - name: username\r\n          in: path\r\n          description: username to update email\r\n          required: true\r\n          schema:\r\n            type: string\r\n            example: 'name1'\r\n      requestBody:\r\n        description: field to update\r\n        content:\r\n          application/json:\r\n            schema:\r\n              type: object\r\n              properties:\r\n                email:\r\n                  type: string\r\n                  example: '<EMAIL>'\r\n        required: true\r\n      responses:\r\n        '204':\r\n          description: Sucessfully updated user email\r\n          content: {}\r\n        '400':\r\n          description: Invalid request\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Please Provide a valid email address.'\r\n        '401':\r\n          description: User not authorized\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Invalid Token'\r\n  /users/v1/{username}/password:\r\n    put:\r\n      tags:\r\n        - users\r\n      summary: Update users password\r\n      description: Update users password\r\n      operationId: api_views.users.update_password\r\n      parameters:\r\n        - name: username\r\n          in: path\r\n          description: username to update password\r\n          required: true\r\n          schema:\r\n            type: string\r\n            example: 'name1'\r\n      requestBody:\r\n        description: field to update\r\n        content:\r\n          application/json:\r\n            schema:\r\n              type: object\r\n              properties:\r\n                password:\r\n                  type: string\r\n                  example: 'pass4'\r\n        required: true\r\n      responses:\r\n        '204':\r\n          description: Sucessfully updated users password\r\n          content: {}\r\n        '400':\r\n          description: Invalid request\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Malformed Data'\r\n        '401':\r\n          description: User not authorized\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Invalid Token'\r\n  /books/v1:\r\n    get:\r\n      tags:\r\n        - books\r\n      summary: Retrieves all books\r\n      description: Retrieves all books\r\n      operationId: api_views.books.get_all_books\r\n      responses:\r\n        '200':\r\n          description: See all books\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  Books:\r\n                    type: array\r\n                    items:\r\n                      type: object\r\n                      properties:\r\n                        book_title:\r\n                          type: string\r\n                        user:\r\n                          type: string\r\n              example:\r\n                Books:\r\n                  - book_title: 'bookTitle77'\r\n                    user: 'name1'\r\n                  - book_title: 'bookTitle85'\r\n                    user: 'name2'\r\n                  - book_title: 'bookTitle47'\r\n                    user: 'admin'\r\n    post:\r\n      tags:\r\n        - books\r\n      summary: Add new book\r\n      description: Add new book\r\n      operationId: api_views.books.add_new_book\r\n      requestBody:\r\n        description: >-\r\n          Add new book with title and secret content only available to the user\r\n          who added it.\r\n        content:\r\n          application/json:\r\n            schema:\r\n              type: object\r\n              properties:\r\n                book_title:\r\n                  type: string\r\n                  example: 'book99'\r\n                secret:\r\n                  type: string\r\n                  example: 'pass1secret'\r\n        required: true\r\n      responses:\r\n        '200':\r\n          description: Sucessfully added a book\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  message:\r\n                    type: string\r\n                    example: 'Book has been added.'\r\n                  status:\r\n                    type: string\r\n                    enum: ['success', 'fail']\r\n                    example: 'success'\r\n        '400':\r\n          description: Invalid request\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Book Already exists!'\r\n        '401':\r\n          description: User not authorized\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Invalid Token'\r\n  /books/v1/{book_title}:\r\n    get:\r\n      tags:\r\n        - books\r\n      summary: Retrieves book by title along with secret\r\n      description: >-\r\n        Retrieves book by title along with secret. Only the owner may retrieve\r\n        it\r\n      operationId: api_views.books.get_by_title\r\n      parameters:\r\n        - name: book_title\r\n          in: path\r\n          description: retrieve book data\r\n          required: true\r\n          schema:\r\n            type: string\r\n            example: 'bookTitle77'\r\n      responses:\r\n        '200':\r\n          description: Successfully retrieve book info\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: array\r\n                items:\r\n                  type: object\r\n                  properties:\r\n                    book_title:\r\n                      type: string\r\n                      example: 'bookTitle77'\r\n                    owner:\r\n                      type: string\r\n                      example: 'name1'\r\n                    secret:\r\n                      type: string\r\n                      example: 'secret for bookTitle77'\r\n        '401':\r\n          description: User not authorized\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Invalid Token'\r\n        '404':\r\n          description: Book not found\r\n          content:\r\n            application/json:\r\n              schema:\r\n                type: object\r\n                properties:\r\n                  status:\r\n                    type: string\r\n                    enum: ['fail']\r\n                    example: 'fail'\r\n                  message:\r\n                    type: string\r\n                    example: 'Book not found!'", "graph_ql_default_values": "", "scope_http_and_https": false, "seeded_api_remote": "", "seeded_api_keys": "", "param_filler_values": "", "graph_ql_introspection": "", "seeded": "", "graph_ql_url": ""}, "enabled": true, "authentication": {"goscript": "def auth.login\r\n  go: http://**************:5000/\r\n  wait for: VAmPI the Vulnerable API\r\n  \r\n  #Here we run some custom javascript that makes two calls to the API: the \r\n  #first uses the provided username and password to retrieve the token, the \r\n  #second uses the token to call one of the API endpoints.  \r\n  #The scanner will see this and know that that's how to make authenticated API requests.\r\n  \r\n  js:\r\n    //Variables for the first request, to get the token\r\n    var getTokenURL = \"http://**************:5000/users/v1/login\";\r\n    getTokenContentType = \"application/json\";\r\n    getTokenURLPayload = '{\"username\": \"name1\", \"password\": \"pass1\"}'\r\n    getTokenJSONPath = \"auth_token\";\r\n    \r\n\t// Get books. We need to know a valid book.\r\n\t\r\n\t\r\n\tgetBooksUrl = \"http://**************:5000/books/v1\"\r\n\tgetBooksRequest = new XMLHttpRequest;\r\n\tgetBooksRequest.open(\"GET\", getBooksUrl, false);\r\n\tgetBooksRequest.onreadystatechange = function () {\r\n\tif (getBooksRequest.readyState == XMLHttpRequest.DONE) {\r\n\t\ttry{\r\n\t\t\tbooks = JSON.parse(getBooksRequest.responseText)\r\n\t\t\t\r\n\t\t\twindow.apckbook = books[\"Books\"][0][\"book_title\"]\r\n\t\t\tconsole.log(book)\r\n\t\t}catch(e){}\r\n\t}}\r\n\tgetBooksRequest.send()\r\n\tconsole.warn(window.apckbook)\r\n\t\r\n    //Variables for the second request, to use the token\r\n    var useTokenURL = \"http://**************:5000/books/v1/\" + window.apckbook,\r\n    useTokenContentType = \"application/json\";\r\n    \r\n    //In most cases you will not need to change anything further within this js\r\n    var getTokenRequest = new XMLHttpRequest;\r\n    useTokenRequest = new XMLHttpRequest;\r\n    tokenPathArray = getTokenJSONPath.split(\".\");\r\n    \r\n    //Submit the request to get a token\r\n    getTokenRequest.open(\"POST\", getTokenURL, !0);\r\n    getTokenRequest.setRequestHeader(\"Content-Type\", getTokenContentType);\r\n    getTokenRequest.onreadystatechange = function () {\r\n      if (getTokenRequest.readyState == XMLHttpRequest.DONE) {\r\n        var status = parseInt(getTokenRequest.status);\r\n        if (status >= 200 && status < 300) {\r\n          document.write(\"<p>Get token request success: HTTP status \" + getTokenRequest.status + \"</p>\");\r\n          var token = JSON.parse(getTokenRequest.responseText);\r\n          for (var i = 0; i < tokenPathArray.length; i++) {\r\n            token = token[tokenPathArray[i]];\r\n          };\r\n          document.write(\"<p>Token: \" + token.slice(0, 5) + \"...\" + token.slice(-5) + \"</p>\");\r\n          \r\n          //If we got a token, submit the second request, using the token\r\n          useTokenRequest.open(\"GET\", useTokenURL, !0);\r\n          useTokenRequest.setRequestHeader(\"Authorization\", \"Bearer \" + token);\r\n          useTokenRequest.setRequestHeader(\"Content-Type\", useTokenContentType);\r\n          useTokenRequest.onreadystatechange = function () {\r\n            status = parseInt(useTokenRequest.status);\r\n            //If we got a 2xx response, write it to the browser.  GoScript can then read it to confirm it worked.\r\n            if (useTokenRequest.readyState == XMLHttpRequest.DONE) {\r\n              if (status >= 200 && status < 300) {\r\n                document.write(\"<p>\" + useTokenRequest.responseText + \"</p>\");\r\n              }\r\n            }\r\n          };\r\n          useTokenRequest.send();\r\n        } else {\r\n          document.write(\"<p>First request failed with response code \" + getTokenRequest.status + \"</p>\")\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.write(\"<p>Sending request to get token.</p>\");\r\n    getTokenRequest.send(getTokenURLPayload);\r\n  pause: 3", "login_url": "", "password": "pass1", "access_denied_keywords": "", "username_field_substrings": "", "access_granted_keywords": "", "username": "name1", "password_field_substrings": ""}, "goscripts": {"scan_only_goscripts": false, "goscripts": ""}}, "target_hosts": ["**************"], "vuln_counts": [0, 0, 2], "target_urls": ["https://**************:5000/"], "protected": false, "modified": 1685527304, "settings": {"config_flags": "keep_it_short"}, "cache": {"vuln_counts_list": [0, 0, 2], "started": 1697786669, "domain_details": {}, "scan_completed": 1697804175, "vuln_counts": {"high": 0, "critical": 0, "low": 2, "medium": 0, "info": 4}, "run_count": 2, "is_running": false, "is_paused": false, "is_live": false, "progress": {"activity": "Scan successful", "stage": "Stage 4 of 6", "raw_stages": ["Initialising Scan", "Reconnaissance", "Enumerating Network", "Scanning Infrastructure", "Scanning Web Applications", "Finalising <PERSON><PERSON>"], "warning": 2, "progress": 1, "detail": {"average_delay": "0.00", "request_total": 0, "error_count": 0, "request_rate": "0.00"}, "raw_stage": "Scanning Infrastructure"}, "warning_count": 2, "confirmed_vuln_counts": {"high": 0, "critical": 0, "low": 0, "medium": 0, "info": 0}, "user": {"name": "<PERSON>", "_id": "5d85e7a4079147e1"}, "is_scheduled": false, "confirmed_vuln_counts_list": [0, 0, 0], "scanhub": "hub.16.public", "status": "COMPLETED"}, "created": 1685530904, "seconds_since_last_update": 2853574, "profile": "default", "meta_migration": {}, "tags": ["customer:258df0ae-b8a3-481a-9d2e-7a03ecf0592f", "source:SCALE_API"], "next_window_finish": "N/A", "schedule_times": [], "blacklist_target_urls": [], "user_id": "5d85e7a4079147e1", "domain": "d60fe14bb75c4e97", "time_on_pause": "", "network_scan": {"vuln_scan": {"advanced_settings": {"include_info_vulns": false, "passive_scan_webapplications": true, "skip_fast_network_discovery": false}, "enabled": true, "authentication": {"general": {"password": "", "username": ""}, "ssh": {"password": "", "port": 22, "username": ""}}}, "port_scan": {"extra_tcp_ports": "", "dead_host_detection": false, "tcp_scan_type": "syn", "scan_type": "ports_only", "ports": "top_1000", "discovery_sweep": false, "scan_timings": "normal", "enabled": false, "extra_udp_ports": "", "scan_udp": false}, "run_first": false, "use_openvas": false}, "target_tags": [{"application": true, "name": "https://**************:5000/"}, {"application": false, "name": "**************"}], "integration": {}, "_id": "48cc18a781714bf8", "target_vulns": [], "trashed": false, "status": "COMPLETED", "scan_hub": "hub.16.public", "next_scan": "Never", "_cls": "ScanDefinition.StandardScanDefinition", "description": "Outpost24 - API Scan", "watchers": ["5d85e7a4079147e1"], "trashed_by_summary": null, "blacklist_target_hosts": [], "scan_complete": 1697804175, "result_set_count": 2, "disabled": false, "debug": {"fake_scan": false}, "next_window_start": "N/A", "raw_status": "COMPLETED", "blacklist_target_tags": [], "domain_details": {"name": "Outpost 24", "_id": "d60fe14bb75c4e97"}, "dev_settings": {"scanning_hub": "__use_default__"}, "auto_rescan_vuln": false, "schedule": {"repeat_interval": "", "schedule_conflict": false}, "infrastructure": ["**************"], "is_live": false, "progress": {"activity": "Scan successful", "stage": "Stage 4 of 6", "raw_stages": ["Initialising Scan", "Reconnaissance", "Enumerating Network", "Scanning Infrastructure", "Scanning Web Applications", "Finalising <PERSON><PERSON>"], "warning": 2, "progress": 1, "detail": {"average_delay": "0.00", "request_total": 0, "error_count": 0, "request_rate": "0.00"}, "raw_stage": "Scanning Infrastructure"}, "applications": ["https://**************:5000/"]}, "success": true, "message": "OK"}