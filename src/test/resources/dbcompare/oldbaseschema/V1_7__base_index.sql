SET standard_conforming_strings = off;
SET check_function_bodies = false;
SET client_min_messages = warning;
SET escape_string_warning = off;
SET default_tablespace = '';
SET default_with_oids = false;

--
-- Name: idx_auditu; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_audituser ON taudits USING btree (xuserxid);


--
-- Name: idx_countryregion; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_countryregion ON tcountryl USING btree (vcregion);


--
-- Name: idx_fk_report_setup; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_fk_report_setup ON treport_setups USING btree (fk_treportentrys_xid);


--
-- Name: idx_fk_report_vuln; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_fk_report_vuln ON treport_vulns USING btree (fk_treportentrys_xid);
CREATE INDEX newfindings ON treport_vulns(fk_treportentrys_xid, xipxid) where bnew=1 and bfalsepos=0;


CREATE INDEX idx_reportentry_xipxid ON treportentrys(xipxid);

--
-- Name: idx_reportentry_dreportdate; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_reportentry_dreportdate ON treportentrys USING btree (dreportdate);


--
-- Name: idx_reportentry_scanlog; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_reportentry_scanlog ON treportentrys USING btree (xscanlogxid);


--
-- Name: idx_reportentry_xuserxid; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_reportentry_xuserxid ON treportentrys USING btree (xuserxid);


--
-- Name: idx_salesorderid; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE UNIQUE INDEX idx_salesorderid ON tasales USING btree (vcorderid);


create index idx_tscanlogs_startdate_xid on tscanlogs(dscanstartdate, xid, xuserxid);

--
-- Name: idx_scanlog_udd; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_scanlog_udd ON tscanlogs USING btree (xuserxid, bdeleted, dscanstartdate);


--
-- Name: idx_scanlogtype; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_scanlogtype ON tscanlogs USING btree (itype);


--
-- Name: idx_scheduleobject_ud; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_scheduleobject_ud ON tscheduleobjects USING btree (xuserxid, bdeleted);


--
-- Name: idx_subemail; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_subemail ON tsubusers USING btree (vcemail);


--
-- Name: idx_subparent; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_subparent ON tsubusers USING btree (xiparentid);


--
-- Name: idx_taorg_create; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_taorg_create ON taorganizations USING btree (xiparentid, xcreator);


--
-- Name: idx_taorg_update; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_taorg_update ON taorganizations USING btree (xiparentid, xupdator);


--
-- Name: idx_tasal_create; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_tasal_create ON tasales USING btree (xiparentid, xcreator);


--
-- Name: idx_tscanlogs_udht; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_tscanlogs_udht ON tscanlogs USING btree (dscanstartdate, itype, xuserxid, vchost);


--
-- Name: idx_tuserdatas_userhost; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_tuserdatas_userhost ON tuserdatas USING btree (ipaddress, xuserxid);


--
-- Name: idx_tvultexts_xid; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_tvultexts_xid ON tvultexts USING btree (xid);


--
-- Name: idx_tvulxrefs_vcid; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_tvulxrefs_vcid ON tvulxrefs USING btree (iid);


--
-- Name: idx_userscanlog; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_userscanlog ON tscanlogs USING btree (xuserxid);


--
-- Name: idx_useruserdata; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_useruserdata ON tuserdatas USING btree (xuserxid);


--
-- Name: idx_vcvulnid_report_vuln; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_vcvulnid_report_vuln ON treport_vulns USING btree (vcvulnid);


--
-- Name: idx_workflowstatus; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_workflowstatus ON tworkflows USING btree (xuserxid, status);
CREATE INDEX idx_workflow_finding_user ON tworkflows(findingid, xuserxid);


--
-- Name: idx_xtime; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_xtime ON taudits USING btree (xtime);


--
-- Name: idxgenericlxip; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxgenericlxip ON tgenericgroups USING btree (xuserxid, xid, xiparentid);


--
-- Name: idxgenericparent; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxgenericparent ON tgenericgroups USING btree (xiparentid);


--
-- Name: idxlog_login; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxlog_login ON log_login USING btree (xuserid, dlastlogon);

--
-- Name: idxsubusername; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idx_subusername ON tsubusers USING btree (vcusername);


--
-- Name: idxsubusersearch; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxsubusersearch ON tsubusers USING btree (vcfirstname, vclastname, vcemail, vcusername);


--
-- Name: idxuseremail; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxuseremail ON tusers USING btree (vcemail);


--
-- Name: idxusername; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxusername ON tusers USING btree (vcusername);


--
-- Name: idxuserorg; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxuserorg ON tusers USING btree (xiorg);


--
-- Name: idxuserparent; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxuserparent ON tusers USING btree (xiparentid);


--
-- Name: idxusersearch; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxusersearch ON tusers USING btree (vclastname, vcacctid, vccompany, vcphoneday, vcusername, vcemail, vcfirstname);


--
-- Name: idxvtfam; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX idxvtfam ON tvultexts USING btree (vcfam);


--
-- Name: taorgname; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX taorgname ON taorganizations USING btree (vcname);


--
-- Name: taorgparent; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX taorgparent ON taorganizations USING btree (xiparentid);


--
-- Name: tasaleparent; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX tasaleparent ON tasales USING btree (xiparentid);


create index tqueuelogs_sent on tqueuelogs(dsent, xid);

--
-- Name: tqueues_thread; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX tqueues_thread ON tqueues USING btree (xthreadid);


--
-- Name: xlinkso_group; Type: INDEX; Schema: public; Owner: -; Tablespace:
--

CREATE INDEX xlinkso_group ON xlinkscheduleobject USING btree (xid, xgroupxid);

create index idx_scanlogs_scanjob on tscanlogs(xscanjobxid, xuserxid);
create index idx_treportentrys_userxid_scanjobxid on treportentrys(xuserxid, xscanjobxid);
create index idx_treportvulns_xtp on treport_vulns(fk_treportentrys_xid,itype,iport);
create index idx_tscanlogs_ixb on tscanlogs(itype, xscanjobxid, bdeleted);

create index idxmonitorhost on tmonitorhostports( vchost );

create index idxmonitorport on tmonitorhostports( iport );

create index idxmonitorlog on tmonitorlogs( xmonitorxid );

CREATE INDEX idxmonitorsetup ON tmonitorsetups USING btree (xid, xuserxid);

create index idx_xlinkgeneric_xipxid on xlinkgeneric( xipxid );

create index idx_scanstatus_scanner on tscanstatuss( scannerid, remotexid );

create index idx_scanstatus_updated on tscanstatuss( dupdated );

create index idx_scanstatus_stop on tscanstatuss( bstop );

create index idx_scanstatus_sync on tscanstatuss( bsync );

CREATE INDEX xlinkrules_xid ON xlinkrules(xid);

create index idx_treportverifys_fk on treport_verifys(fk_treport_vulns_xid);

CREATE INDEX idx_xlinkgeneric_xid ON xlinkgeneric USING btree (xid);

CREATE INDEX idx_auditxid ON taudits USING btree (xxid);

CREATE INDEX idx_tgenericgroups_u ON tgenericgroups USING btree (xuserxid);

CREATE INDEX idx_treportentrys_prevxix ON treportentrys USING btree (xprevxid);

CREATE INDEX idx_scanlogdate ON tscanlogs USING btree (dscanstartdate);

CREATE INDEX idx_scanlogs_test1 ON tscanlogs USING btree (itype, xuserxid, dscanstartdate);

CREATE INDEX idx_tscanlogs_xipxid ON tscanlogs USING btree (xipxid);

CREATE INDEX idx_tscheduleobject_date ON tscheduleobjects USING btree (nextscandate);

CREATE INDEX idx_tuserdatas_hux ON tuserdatas USING btree (ipaddress, xuserxid, scannerid);

CREATE INDEX idx_userorgdata ON tuserdatas USING btree (xuserxid, scannerid);

CREATE INDEX idx_vultextdate ON tvultexts USING btree (dcreated);

CREATE INDEX idxlinkgroup ON xlinkgroup USING btree (xvcgroup, xid, vcname, xvcapp);

CREATE INDEX idx_scanlogs_test2 ON tscanlogs USING btree (vchost, scannerid);

CREATE INDEX idx_scanstatus_user ON tscanstatuss USING btree (xuserxid);

create index idx_treportentrys_xipxiddate on treportentrys(xipxid, dreportdate);

CREATE INDEX idx_reportentrys_scanjob on treportentrys(xscanjobxid);

CREATE INDEX idx_treportvulns_fixed on treport_vulns(fk_treportentrys_xid, fixed);

CREATE INDEX idx_treportvulns_accepted ON treport_vulns(xipxid, iport, iprotocol, vcvulnid);

CREATE INDEX idx_logging_userref ON tloggings(xuserxid, xrefid);

CREATE INDEX idx_treportvulns_fkppv ON treport_vulns(fk_treportentrys_xid, iport, iprotocol, vcvulnid);

CREATE INDEX idx_translations_language ON ttranslations(language);

CREATE INDEX idx_translations_key ON ttranslations(key);

CREATE INDEX idx_translations_type ON ttranslations(type);

create index hostids_idx on hostids(id);

CREATE INDEX idx_deletedtargets_xgroupxid ON tdeletedtargets(xgroupxid);

create index idx2_vcvulnid_report_vuln on treport_vulns( vcvulnid);

CREATE INDEX addedTargets ON taudits USING GIN (addedtargets);
CREATE INDEX removedTargets ON taudits USING GIN (removedtargets);
CREATE INDEX audit_time_xid ON taudits(xtime, xid);

CREATE INDEX twasauth_was on twasauths( wasxid, recordtype );

CREATE INDEX twascookie_was on twascookies( wasxid );

CREATE INDEX twasparameter_was on twasparameters( wasxid );

CREATE INDEX xpath_xid ON xgenericpaths(xid);
CREATE INDEX xpath_parentxid ON xgenericpaths(parentxid);

create index idx_passwordrecovery_expire on tpasswordrecoverys( expire );

CREATE INDEX wasdiscoveryresults_xipxid ON twasdiscoveryresults(xipxid);
CREATE INDEX wasdiscoveryresults_xuserxid ON twasdiscoveryresults(xuserxid);
CREATE INDEX wasdiscoveryresults_xscanjobxid ON twasdiscoveryresults(xscanjobxid);

CREATE INDEX idx_tsavedscanprefs_targetoverride ON tsavedscanprefs(targetoverride);

CREATE INDEX idx_treport_vulns_bfalsepos ON treport_vulns(bfalsepos, disputeaccepted);

create index idx_texploits_cve on texploits (cve);

create index idx_texploits_scriptid on texploits (scriptid);

create unique index attributes_user_column on tattributes(columnid, xuserxid);

create index was_user on twasscheduleobjects(xuserxid);

CREATE INDEX triskchanges_idx ON triskchanges(xuserxid, vcvulnid);

create index twaslatest_xscanjobxid on twaslatestdiscovery(xscanjobxid);

create index tscanlog_last on tscanlogs(xuserxid, xsoxid, xid) where itype=20;

create index tuserdatas_ipaddress on tuserdatas(ipaddress);

CREATE INDEX idx_xlinkgeneric_ipval ON xlinkgeneric USING btree (hostnameid);

CREATE INDEX xlinkgeneric_ipaddress ON xlinkgeneric USING btree (ipaddress);

CREATE INDEX idx_entrys_xix ON treportentrys USING btree (xid, hostnameid, xuserxid);

CREATE INDEX idx_reportentry_stat ON treportentrys USING btree (xuserxid, xsoxid, hostnameid, dreportdate);

CREATE INDEX idx_tuserdatas_xui ON tuserdatas USING btree (scannerid, xuserxid, hostnameid);

CREATE INDEX tuserdatas_ipvalue ON tuserdatas USING btree (hostnameid);

CREATE INDEX twaslatestdiscovery_user ON twaslatestdiscovery(xuserxid);
CREATE INDEX twaslatestdiscovery_xipxid ON twaslatestdiscovery(xipxid);

CREATE INDEX ON treport_vulns (acceptexpires) WHERE acceptedlength > 0;

CREATE INDEX tappaccesstoken ON tappaccess( tokenkey );

create index idx_tasales_latestapproved on tasales(xid, xiaorg) where bapproved=1 and bcancel=0;

CREATE INDEX trulesdef_ruleid ON trulesdef(ruleid);
CREATE INDEX trulesdefhistory_ruleid ON trulesdefhistory(ruleid);

create index tvultexts_bnessus on tvultexts(bnessus);

CREATE INDEX twasfindings_treportentry ON twasfindings(treportentry);
CREATE INDEX twasfindings_vcvulnid ON twasfindings(vcvulnid);
CREATE INDEX twasurls_treportentry ON twasurls(treportentry);
create index twasurls_xipxid on twasurls(xipxid);
CREATE INDEX twaspaths_treportentry ON twaspaths(treportentry);
CREATE INDEX twaspaths_parent ON twaspaths(parent);

create unique index tuserdatas_unique_hostname ON tuserdatas (hostname, netbios, xuserxid, pci, scannerid) where ipaddress is null;
create index trulesdef_parent on trulesdef(parent);

CREATE INDEX complianceaccepted_target_finding ON tcomplianceaccepted(vcvulnid, xipxid);

CREATE INDEX compliancefalsepositive_target_finding ON tcompliancefalsepositive(vcvulnid, xipxid);

CREATE INDEX tcompliancescandata_reportentryxid ON tcompliancescandata(reportentryxid);
CREATE INDEX tcompliancescandata_scanlogxid ON tcompliancescandata(scanlogxid);

CREATE INDEX tcompliancerequirements_identifier ON tcompliancerequirements(identifier);
CREATE INDEX tcompliancequestions_identifier ON tcompliancequestions(identifier);

create index tscanlogs_scanschema on tscanlogs(scanschema) where scanschema is not null;

create index tscanlogs_xsoxid on tscanlogs(xid, xsoxid) where itype in (20, 21);
CREATE INDEX twaspaths_branch ON twaspaths USING GIN(branch);

CREATE INDEX rule_updated ON trules(updated);
create index trulesdefhistoryid on trulesdefhistory(rulehistoryid);

CREATE INDEX trulesissueshistory_historyid ON trulesissueshistory(rulehistoryid);
CREATE INDEX tvulxrefshistory_historyid ON tvulxrefshistory(rulehistoryid);

create index tscanlogs_scanupdate on tscanlogs(xuserxid, xipxid) where itype=0 and scanschema is not null;

CREATE INDEX tproductinformation_name ON tproductinformation(name);

CREATE INDEX trules_family on trules(family);
CREATE INDEX trules_requiredproduct ON trules(requiredProduct);
CREATE INDEX trules_requiredproduct_array ON trules USING GIN (requiredproduct);

CREATE INDEX tscanlogs_scanlessreportxid ON tscanlogs(scanlessreportxid) WHERE itype=50;

create index idx_tscanlogs_type1920 on tscanlogs(dscanstartdate, xid, xuserxid) where itype in (19,20);
create index idx_pcidisputes on treport_vulns(disputestatus) where bfalsepos=1 and disputestatus < 2 and disputeaccepted <> 1;

CREATE INDEX solution_target ON treportsolutions USING btree (targetxid);
CREATE INDEX solution_user ON treportsolutions USING btree (userxid);

CREATE INDEX remediation_user ON targetremediation(xuserxid);
CREATE INDEX remediation_xipxid ON targetremediation(xipxid);

CREATE INDEX tscanlogs_soxid_xipxid ON tscanlogs(xsoxid, xipxid);
CREATE INDEX trules_script ON trules(ruleid) WHERE isscript;

CREATE INDEX tdiscoveryresults_user_scanjob_alive ON tdiscoveryresults(xuserxid, xscanjobxid, alive);
CREATE INDEX tdiscoveryresults_xscanjobxid ON tdiscoveryresults(xscanjobxid);

CREATE INDEX scanlog_pci ON tscanlogs(dscanstartdate, xuserxid) WHERE bdeleted=0 AND xtemplate=10;

CREATE INDEX idx_treportvulns_new ON treport_vulns(xipxid) WHERE bnew=1 AND irisk>0;

CREATE INDEX swatschedules_customerid ON swatschedules(customerid);
CREATE INDEX tickets_customerid ON tickets(customerid);
CREATE INDEX tickets_scheduleid ON tickets(scheduleid);
CREATE INDEX tickets_type ON tickets(type);

CREATE INDEX swat_test ON treport_vulns(swatstatus) WHERE COALESCE(swatstatus,0)>0;
CREATE INDEX targetvulnerabilitytrend_xipxid ON targetvulnerabilitytrend(xipxid);

CREATE INDEX hiab_audit ON taudits(xid) WHERE xvcapp='tHiab';
