<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE main SYSTEM "https://outscan.uat.outpost24.com/dtd/XMLReport.dtd">
<main>
   <report>OUTSCAN Security Report</report>
   <reportinfo>
      <type>Vulnerability</type>
      <id>6E110CF6A43A3EB98C55D99A1831575A</id>
      <reportdate>2024-06-12 13:41</reportdate>
      <timezone>GMT+2</timezone>
      <creator>Outpost24</creator>
      <selectedschedulejob>Test</selectedschedulejob>
      <selectedschedulejobrunning>The schedule is currently scanning, this can result in a report with partial findings.</selectedschedulejobrunning>
      <date>2018-11-26 11:38 - 2018-11-26 11:53</date>
      <test>1</test>
      <threat>6</threat>
   </reportinfo>
   <hostlist>
      <host>
         <ip>**************</ip>
         <name/>
         <platform>CentOS</platform>
         <high>0</high>
         <medium>5</medium>
         <low>0</low>
         <info>14</info>
         <port>2</port>
         <start>2018-11-26 11:38</start>
         <end>2018-11-26 11:53</end>
         <template>Normal</template>
         <completescan>true</completescan>
         <businesscriticality>Medium</businesscriticality>
         <exposed>Yes</exposed>
      </host>
   </hostlist>
   <portlist>
      <portlist-host>
         <ip>**************</ip>
         <name/>
         <date>2018-11-26 11:38</date>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
            <history>
               <firstseen>2018-11-26 11:38</firstseen>
            </history>
         </portinfo>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
            <history>
               <firstseen>2018-11-26 11:38</firstseen>
            </history>
         </portinfo>
      </portlist-host>
   </portlist>
   <detaillist>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1212131</id>
         <name>Mod_wsgi: Group Privilege Dropping Error Handling Privilege Escalation Weakness</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>6.9</cvss_score>
         <cvss_vector>(AV:L/AC:M/Au:N/C:C/I:C/A:C) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with relative ease and local access to the system by an attacker who does not have access to credentials with full loss of confidentiality, full impact to the integrity of information and serious issues in rendering the system or information availability. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <farsightrisk>41</farsightrisk>
         <farsightriskdelta>-17</farsightriskdelta>
         <likelihoodupdated>2022-10-26 17:14</likelihoodupdated>
         <family>mod_wsgi</family>
         <product>Mod_wsgi</product>
         <description>mod_wsgi for Apache, when creating a daemon process group, does not properly handle when group privileges cannot be dropped, which might allow attackers to gain privileges via unspecified vectors.</description>
         <information>This vulnerability was identified because (1) the detected version of Mod_wsgi, 3.4, is less than 4.2.4</information>
         <falsepositive>0</falsepositive>
         <fpby>Tommy Automatisson</fpby>
         <solutiontitle>Upgrade to version 4.5.17 or later of Mod_wsgi</solutiontitle>
         <solution>Upgrade to version 4.5.17 or later of Mod_wsgi.</solution>
         <category>Update</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>https://modwsgi.readthedocs.io/</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://modwsgi.readthedocs.org/en/latest/release-notes/version-4.2.4.html</url>
            </reference>
         </referencelist>
         <cve>
            <id>CVE-2014-8583</id>
         </cve>
         <bug>
            <id>68111</id>
         </bug>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
         <comments>
            <comment>
               <type>private</type>
               <created>2020-01-07 11:24</created>
               <by>Tommy Automatisson</by>
               <text>Test</text>
            </comment>
         </comments>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1208562</id>
         <name>Mod_wsgi: Process Manipulation Local Privilege Escalation</name>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>6.2</cvss_score>
         <cvss_vector>(AV:L/AC:H/Au:N/C:C/I:C/A:C) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with advanced skills and local access to the system by an attacker who does not have access to credentials with full loss of confidentiality, full impact to the integrity of information and serious issues in rendering the system or information availability. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <farsightrisk>37</farsightrisk>
         <farsightriskdelta>-6</farsightriskdelta>
         <likelihoodlastseen>2020-10-07 10:02</likelihoodlastseen>
         <likelihoodupdated>2022-10-26 17:13</likelihoodupdated>
         <family>mod_wsgi</family>
         <product>Mod_wsgi</product>
         <description>The mod_wsgi module, when daemon mode is enabled, does not properly handle error codes returned by setuid when run on certain Linux kernels, which allows local users to gain privileges via vectors related to the number of running processes.</description>
         <information>This vulnerability was identified because (1) the detected version of Mod_wsgi, 3.4, is less than 3.5</information>
         <falsepositive>0</falsepositive>
         <solutiontitle>Upgrade to version 4.5.17 or later of Mod_wsgi</solutiontitle>
         <solution>Upgrade to version 4.5.17 or later of Mod_wsgi.</solution>
         <category>Update</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>https://modwsgi.readthedocs.io/</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://modwsgi.readthedocs.org/en/latest/release-notes/version-3.5.html</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://blog.dscpl.com.au/2014/05/security-release-for-modwsgi-version-35.html</url>
            </reference>
         </referencelist>
         <cve>
            <id>CVE-2014-0240</id>
         </cve>
         <bug>
            <id>67532</id>
         </bug>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>268043</id>
         <name>OS Detection</name>
         <portinfo>
            <portnumber>General</portnumber>
         </portinfo>
         <risk>0</risk>
         <family>os</family>
         <product>Operating System</product>
         <description>It was possible to determine operating system on the target host.</description>
         <information>CentOS</information>
         <falsepositive>1</falsepositive>
         <fpcomment>111</fpcomment>
         <fpby>Tommy Automatisson</fpby>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>125993</id>
         <name>Traceroute</name>
         <portinfo>
            <portnumber>General</portnumber>
         </portinfo>
         <risk>0</risk>
         <family>icmp</family>
         <product>ICMP</product>
         <description>This check tries to determine the path; traceroute, between our attacker
and your target host. This path may give an attacker valuable information about
through which routers; hops, traffic passes through. This is not a
vulnerability in itself it is merely considered information, however, an
attacker could possibly use this information to determine what ISP you have and
so forth.

Note:
The path is not static and will most likely change depending on from which host
you perform the traceroute. There is also no way you can fix this problem as it
involves changing configurations on all the hops along the way.</description>
         <information>host[:dport]/protocol (16 hops)
1 **********:443/tcp
2 ***********:443/tcp
3 *************:443/tcp
4 *************:443/tcp
5 **************:443/tcp
6 *************:443/tcp
7 ***************:443/tcp
8 ***********:443/tcp
9 ***********:443/tcp
10 ***********:443/tcp
11 *************:443/tcp
12 **************:443/tcp
13 **************:443/tcp
14 **************:443/tcp
15 **************:443/tcp
16 **************:443/tcp [open]</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc792</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>217099</id>
         <name>SSL/TLS Cipher Suite List</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>The service running on this port supports the following cipher suites.</description>
         <information>&lt;rtab&gt;&lt;columns&gt;3&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;TLS1.0 Cipher Suite&lt;/col&gt;&lt;col&gt;OpenSSL Name&lt;/col&gt;&lt;col&gt;SNI Name&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_256_CBC_SHA&lt;/col&gt;&lt;col&gt;AES256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_CAMELLIA_256_CBC_SHA&lt;/col&gt;&lt;col&gt;CAMELLIA256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_128_CBC_SHA&lt;/col&gt;&lt;col&gt;AES128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_CAMELLIA_128_CBC_SHA&lt;/col&gt;&lt;col&gt;CAMELLIA128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;&lt;rtab&gt;&lt;columns&gt;3&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;TLS1.2 Cipher Suite&lt;/col&gt;&lt;col&gt;OpenSSL Name&lt;/col&gt;&lt;col&gt;SNI Name&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES256-SHA384&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_256_CBC_SHA256&lt;/col&gt;&lt;col&gt;AES256-SHA256&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_256_CBC_SHA&lt;/col&gt;&lt;col&gt;AES256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_CAMELLIA_256_CBC_SHA&lt;/col&gt;&lt;col&gt;CAMELLIA256-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES128-SHA256&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA&lt;/col&gt;&lt;col&gt;ECDHE-RSA-AES128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_128_CBC_SHA256&lt;/col&gt;&lt;col&gt;AES128-SHA256&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_AES_128_CBC_SHA&lt;/col&gt;&lt;col&gt;AES128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;TLS_RSA_WITH_CAMELLIA_128_CBC_SHA&lt;/col&gt;&lt;col&gt;CAMELLIA128-SHA&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>200827</id>
         <name>SSL/TLS Certificate Information</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>X.509 Certificate Information</description>
         <information>Chain ID: b187edf553beae48
Provided SNI-Name: (without-sni)

Chain Depth: 0
Certificate Issuer:
    Country: --
    Organization: SomeOrganization
    Organizational Unit: SomeOrganizationalUnit
    Locality: SomeCity
    Province: SomeState
    Common Name: vulcano

Certificate Subject:
    Country: --
    Organization: SomeOrganization
    Organizational Unit: SomeOrganizationalUnit
    Locality: SomeCity
    Province: SomeState
    Common Name: vulcano

Valid not before:2015-05-18T10:43:13Z
Valid not after: 2016-05-17T10:43:13Z
Key algorithm: RSA, 2048 bits
Signature algorithm: SHA256WithRSA


 ============</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>249067</id>
         <name>SSL/TLS Signature-Signing Algorithm</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>Digital signatures are used to verify that data is received from the correct sender. Your signature can only be generated using your private key but can be verified by anyone with your public key.  If your private key is ever stolen someone else will be able to impersonate you and could send out malicious content with the trust that it&apos;s coming from you.</description>
         <information>Your signature is signed using the algorithm: SHA256WithRSA</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>124463</id>
         <name>HTTP Detection</name>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>http</family>
         <product>HTTP</product>
         <description>An HTTP server was detected.</description>
         <information>HTTP/1.1 404 Not Found
Date: Mon, 26 Nov 2018 09:43:19 GMT
Server: Apache/2.4.6 (CentOS) OpenSSL/1.0.2k-fips mod_wsgi/3.4 Python/2.7.5 mod_perl/2.0.10 Perl/v5.16.3
Content-Length: 50
Connection: close
Content-Type: text/html; charset=iso-8859-1</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc2616</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>200826</id>
         <name>HTTP Information</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>http</family>
         <product>HTTP</product>
         <description>HTTP Server Information</description>
         <information>&lt;rtab&gt;&lt;columns&gt;4&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;HTTP Version&lt;/col&gt;&lt;col&gt;SSL&lt;/col&gt;&lt;col&gt;Request Pipelining&lt;/col&gt;&lt;col&gt;Connection Keep-Alive&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;1.1&lt;/col&gt;&lt;col&gt;yes&lt;/col&gt;&lt;col&gt;yes&lt;/col&gt;&lt;col&gt;(unknown)&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc2616</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1208562</id>
         <name>Mod_wsgi: Process Manipulation Local Privilege Escalation</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>6.2</cvss_score>
         <cvss_vector>(AV:L/AC:H/Au:N/C:C/I:C/A:C) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with advanced skills and local access to the system by an attacker who does not have access to credentials with full loss of confidentiality, full impact to the integrity of information and serious issues in rendering the system or information availability. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <farsightrisk>37</farsightrisk>
         <farsightriskdelta>-6</farsightriskdelta>
         <likelihoodlastseen>2020-10-07 10:02</likelihoodlastseen>
         <likelihoodupdated>2022-10-26 17:13</likelihoodupdated>
         <family>mod_wsgi</family>
         <product>Mod_wsgi</product>
         <description>The mod_wsgi module, when daemon mode is enabled, does not properly handle error codes returned by setuid when run on certain Linux kernels, which allows local users to gain privileges via vectors related to the number of running processes.</description>
         <information>This vulnerability was identified because (1) the detected version of Mod_wsgi, 3.4, is less than 3.5</information>
         <falsepositive>0</falsepositive>
         <solutiontitle>Upgrade to version 4.5.17 or later of Mod_wsgi</solutiontitle>
         <solution>Upgrade to version 4.5.17 or later of Mod_wsgi.</solution>
         <category>Update</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>https://modwsgi.readthedocs.io/</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://modwsgi.readthedocs.org/en/latest/release-notes/version-3.5.html</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://blog.dscpl.com.au/2014/05/security-release-for-modwsgi-version-35.html</url>
            </reference>
         </referencelist>
         <cve>
            <id>CVE-2014-0240</id>
         </cve>
         <bug>
            <id>67532</id>
         </bug>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>125950</id>
         <name>TCP SYN|FIN</name>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>tcp</family>
         <product>TCP</product>
         <description>The TCP implementation on this host replies to invalid TCP packets.
Packets with both the SYN and the FIN bit set are considered invalid
and should be discarded.

Not doing so could have a negative impact on IDS systems. Depending on
the order in which the flag-bits are parsed, the TCP implementation
of this host&apos;s operating system may see the SYN bit first and initiate
a connection while the TCP implementation of the IDS parses the FIN bit
first, making it believe that the connection has just been closed.

Effectively, this could mean that a connection can be established without
the IDS keeping track of it.</description>
         <information/>
         <falsepositive>0</falsepositive>
         <solutiontitle>Apply latest patches for TCP</solutiontitle>
         <solution>Apply the latest patches for TCP</solution>
         <category>Patch</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://www.ietf.org/rfc/rfc793.txt</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://nvd.nist.gov/nvd.cfm?cvename=CVE-2007-3537</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://archives.neohapsis.com/archives/bugtraq/2002-10/0266.html</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://www.kb.cert.org/vuls/id/464113</url>
            </reference>
         </referencelist>
         <cve/>
         <bug>
            <id>7487</id>
         </bug>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1222353</id>
         <name>TLSv1.0 Detected</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>4.3</cvss_score>
         <cvss_vector>(AV:N/AC:M/Au:N/C:P/I:N/A:N) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with relative ease and network access to the system by an attacker who does not have access to credentials with some impact on confidentiality, no impact to integrity of information and without affecting the availability of the information or system. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>TLS 1.0 is not considered strong cryptography since it has known issues in the following areas:

i) Not protected against cipher-block chaining (CBC) attacks.
ii) The initialization vector (IV) is not an explicit IV.
iii) Padding errors are not handled correctly.</description>
         <information>This finding was reported because (1) TLSv1.0 was detected as supported encryption protocol.</information>
         <falsepositive>0</falsepositive>
         <solutiontitle>Disable TLSv1.0</solutiontitle>
         <solution>Disable TLSv1.0</solution>
         <category>Reconfigure</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
            <reference>
               <type>advisory</type>
               <url>https://blogs.msdn.microsoft.com/friis/2016/07/25/disabling-tls-1-0-on-your-windows-2008-r2-server-just-because-you-still-have-one/</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>https://learn.microsoft.com/en-us/windows-server/security/tls/tls-registry-settings</url>
            </reference>
            <reference>
               <type>advisory</type>
               <url>https://www.pcisecuritystandards.org/documents/Migrating_from_SSL_Early_TLS_Information_Supplement_v1.pdf</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>https://wiki.mozilla.org/Security/Server_Side_TLS</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>https://ssl-config.mozilla.org/</url>
            </reference>
            <reference>
               <type>advisory</type>
               <url>http://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-52r1.pdf</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>200817</id>
         <name>SSL/TLS Certificate Expired</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>4.3</cvss_score>
         <cvss_vector>(AV:N/AC:M/Au:N/C:N/I:P/A:N) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with relative ease and network access to the system by an attacker who does not have access to credentials with no impact on confidentiality, some impact to the integrity of information and without affecting the availability of the information or system. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>The SSL/TLS certificate used by this service has expired.</description>
         <information>Valid not before:2015-05-18T10:43:13Z
Valid not after: 2016-05-17T10:43:13Z</information>
         <falsepositive>0</falsepositive>
         <solutiontitle>Update SSL certificate</solutiontitle>
         <solution>Purchase/Generate a new SSL/TLS certificate for this service.</solution>
         <category>Workaround</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>200826</id>
         <name>HTTP Information</name>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>http</family>
         <product>HTTP</product>
         <description>HTTP Server Information</description>
         <information>&lt;rtab&gt;&lt;columns&gt;4&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;HTTP Version&lt;/col&gt;&lt;col&gt;SSL&lt;/col&gt;&lt;col&gt;Request Pipelining&lt;/col&gt;&lt;col&gt;Connection Keep-Alive&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;1.1&lt;/col&gt;&lt;col&gt;no&lt;/col&gt;&lt;col&gt;yes&lt;/col&gt;&lt;col&gt;yes&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc2616</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1330525</id>
         <name>SSL/TLS Certificate Transparency Validation Failure</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>ssl</family>
         <product>SSL/TLS</product>
         <description>Certificate Transparency helps eliminate these flaws by providing an open framework for monitoring and auditing SSL certificates in nearly real time. Specifically, Certificate Transparency makes it possible to detect SSL certificates that have been mistakenly issued by a certificate authority or maliciously acquired from an otherwise unimpeachable certificate authority. It also makes it possible to identify certificate authorities that have gone rogue and are maliciously issuing certificates.

Google starts to report Certificate Validation Failures over Certificate Transparency from 2018-04-30.</description>
         <information>At least two certificate transparency log servers are required to evaluate as valid.
Virtual host: no-sni&lt;rtab&gt;&lt;columns&gt;7&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Chain-id&lt;/col&gt;&lt;col&gt;Timestamp&lt;/col&gt;&lt;col&gt;Log&lt;/col&gt;&lt;col&gt;Operator&lt;/col&gt;&lt;col&gt;url&lt;/col&gt;&lt;col&gt;Valid&lt;/col&gt;&lt;col&gt;information&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;b187edf553beae48&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;col&gt;False&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle>Add Certificate to CT Logs</solutiontitle>
         <solution>Make sure the CA is adding the signed certificates to the Certificate Transparency Logs.</solution>
         <category>Workaround</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc6101</url>
            </reference>
            <reference>
               <type>advisory</type>
               <url>https://www.certificate-transparency.org/</url>
            </reference>
            <reference>
               <type>advisory</type>
               <url>https://github.com/chromium/ct-policy/blob/master/log_policy.md</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1236903</id>
         <name>Test Tracking Information</name>
         <portinfo>
            <portnumber>General</portnumber>
         </portinfo>
         <risk>0</risk>
         <family>misc</family>
         <product>Unspecified</product>
         <description>This script reports information that is required for troubleshooting and tracking.</description>
         <information>Host ************** tested from ************ with scan track ID RhIKbow5WuRbH6rRIiaPZgE9RiNgGrsV</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1212131</id>
         <name>Mod_wsgi: Group Privilege Dropping Error Handling Privilege Escalation Weakness</name>
         <portinfo>
            <portnumber>80</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <cvss_score>6.9</cvss_score>
         <cvss_vector>(AV:L/AC:M/Au:N/C:C/I:C/A:C) (cdp:ND/td:ND/cr:ND/ir:ND/ar:ND)</cvss_vector>
         <cvss_vector_description>This vulnerability can be exploited with relative ease and local access to the system by an attacker who does not have access to credentials with full loss of confidentiality, full impact to the integrity of information and serious issues in rendering the system or information availability. There are currently no exploits in the public domain. However, attacks may be well described or privately held.</cvss_vector_description>
         <risk>2</risk>
         <farsightrisk>41</farsightrisk>
         <farsightriskdelta>-17</farsightriskdelta>
         <likelihoodupdated>2022-10-26 17:14</likelihoodupdated>
         <family>mod_wsgi</family>
         <product>Mod_wsgi</product>
         <description>mod_wsgi for Apache, when creating a daemon process group, does not properly handle when group privileges cannot be dropped, which might allow attackers to gain privileges via unspecified vectors.</description>
         <information>This vulnerability was identified because (1) the detected version of Mod_wsgi, 3.4, is less than 4.2.4</information>
         <falsepositive>1</falsepositive>
         <fpcomment>Test</fpcomment>
         <fpby>Tommy Automatisson</fpby>
         <solutiontitle>Upgrade to version 4.5.17 or later of Mod_wsgi</solutiontitle>
         <solution>Upgrade to version 4.5.17 or later of Mod_wsgi.</solution>
         <category>Update</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>https://modwsgi.readthedocs.io/</url>
            </reference>
            <reference>
               <type>solution</type>
               <url>http://modwsgi.readthedocs.org/en/latest/release-notes/version-4.2.4.html</url>
            </reference>
         </referencelist>
         <cve>
            <id>CVE-2014-8583</id>
         </cve>
         <bug>
            <id>68111</id>
         </bug>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
         <comments>
            <comment>
               <type>private</type>
               <created>2020-01-07 11:24</created>
               <by>Tommy Automatisson</by>
               <text>Test</text>
            </comment>
         </comments>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost>**************</virtualhost>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>124463</id>
         <name>HTTP Detection</name>
         <portinfo>
            <portnumber>443</portnumber>
            <protocol>TCP</protocol>
            <service>http</service>
         </portinfo>
         <risk>0</risk>
         <family>http</family>
         <product>HTTP</product>
         <description>An HTTP server was detected.</description>
         <information>HTTP/1.1 200 OK
Date: Mon, 26 Nov 2018 09:43:20 GMT
Server: Apache/2.4.6 (CentOS) OpenSSL/1.0.2k-fips mod_wsgi/3.4 Python/2.7.5 mod_perl/2.0.10 Perl/v5.16.3
Content-Length: 481
Connection: close
Content-Type: text/html;charset=ISO-8859-1</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc2616</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1221985</id>
         <name>Products Installed</name>
         <portinfo>
            <portnumber>General</portnumber>
         </portinfo>
         <risk>0</risk>
         <family>misc</family>
         <product>Unspecified</product>
         <description>This finding lists all the products that were detected by the scanner during the scan.</description>
         <information>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Product&lt;/col&gt;&lt;col&gt;Version&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;Perl&lt;/col&gt;&lt;col&gt;5.16.3&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;CentOS&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Mod_perl&lt;/col&gt;&lt;col&gt;2.0.10&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Linux Kernel&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Mod_wsgi&lt;/col&gt;&lt;col&gt;3.4&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Apache HTTP Server&lt;/col&gt;&lt;col&gt;2.4.6&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;OpenSSL&lt;/col&gt;&lt;col&gt;1.0.2k&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Python&lt;/col&gt;&lt;col&gt;2.7.5&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>No known solution</category>
         <referencelist>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
      <detail>
         <ip>**************</ip>
         <hostname/>
         <platform>CentOS</platform>
         <date>2018-11-26 11:38</date>
         <virtualhost/>
         <targetsecurity_zone>Online</targetsecurity_zone>
         <id>1212152</id>
         <name>Backported Software Detection</name>
         <portinfo>
            <portnumber>General</portnumber>
         </portinfo>
         <risk>0</risk>
         <family>ssh</family>
         <product>SSH</product>
         <description>Backported software was detected and findings related to the backported components have not been included in the scan results.

In order to increase the accuracy of the scan results, an authenticated scan with use of SMB or SSH credentials is recommended.

To enable reporting potential false positives, disable the &quot;Filter potential false positives&quot; option in the scan policy.

Please note that the above doesn&apos;t apply to PCI scans. All findings related to the reported components will be included in the PCI scan results.</description>
         <information>&lt;rtab&gt;&lt;columns&gt;2&lt;/columns&gt;&lt;hdr&gt;&lt;col&gt;Product&lt;/col&gt;&lt;col&gt;Version&lt;/col&gt;&lt;/hdr&gt;&lt;row&gt;&lt;col&gt;Perl&lt;/col&gt;&lt;col&gt;5.16.3&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Linux Kernel&lt;/col&gt;&lt;col&gt;&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Apache HTTP Server&lt;/col&gt;&lt;col&gt;2.4.6&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;OpenSSL&lt;/col&gt;&lt;col&gt;1.0.2k&lt;/col&gt;&lt;/row&gt;&lt;row&gt;&lt;col&gt;Python&lt;/col&gt;&lt;col&gt;2.7.5&lt;/col&gt;&lt;/row&gt;&lt;/rtab&gt;</information>
         <falsepositive>0</falsepositive>
         <solutiontitle/>
         <solution/>
         <category>Not classified</category>
         <referencelist>
            <reference>
               <type>url</type>
               <url>http://tools.ietf.org/html/rfc4253</url>
            </reference>
         </referencelist>
         <cve/>
         <bug/>
         <verify/>
         <history>
            <firstseen>2018-11-26 11:38</firstseen>
            <added>1</added>
         </history>
      </detail>
   </detaillist>
</main>
