<?xml version="1.0" encoding="UTF-8"?>
<ITEMLIST><POLICY><NAME>PCI - Windows v2.0</NAME><DESCRIPTION>https://www.pcisecuritystandards.org/documents/pci_dss_v2.pdf</DESCRIPTION></POLICY><ITEM><DESCRIPTION>Assure that these standards address all known security vulnerabilities and are consistent with industry-accepted system hardening standards. Sources of industry-accepted system hardening standardsmay include, but are not limited to: 
*Center for Internet Security (CIS)
*International Organization for Standardization (ISO)
*SysAdmin Audit Network Security (SANS) Institute
*National Institute of Standards Technology (NIST)</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2.2 Develop configuration standards for all system components</NAME><REPORT>true</REPORT><XID>3134</XID><PARENT>3129</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Logging mechanisms and the ability to track user activities are critical in preventing, detecting, or minimizing the impact of a data compromise. The presence of logs in all environments allows thorough tracking, alerting, and analysis when something does go wrong. Determining the cause of a compromise is very difficult, if not impossible, without system activity logs.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10 Track and monitor all access to network resources and cardholder data</NAME><REPORT>true</REPORT><XID>3135</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>6</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>With a minimum of three months immediately available for analysis (for example, online, archived, or restorable from back-up). </DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10.7 Retain audit trail history for at least one year</NAME><REPORT>true</REPORT><XID>3136</XID><PARENT>3135</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>2</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Specifically those with direct connectivity to the Internet (for example, laptops used by employees), and which are used to access the organization’s network, have personal firewall software installed and active.

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\SharedAccess\Parameters\FirewallPolicy\StandardProfile\EnableFirewall&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require firewall to be enabled in Standard Profile&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\SharedAccess\Parameters\FirewallPolicy\DomainProfile\EnableFirewall&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require firewall to be enabled in Domain Profile&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>1.4.a Verify that mobile and/or employee-owned computers.</NAME><REPORT>true</REPORT><XID>3137</XID><PARENT>3133</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient\CrossSiteSyncFlags&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;CrossSiteSyncFlags&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient\CrossSiteSyncFlags&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At most&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;2&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;CrossSiteSyncFlags&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atmost&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient\Enabled&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Enabled&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\Config\FrequencyCorrectRate&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;4&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;FrequencyCorrectRate&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\TimeProviders\NtpClient\InputProvider&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;InputProvider&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\Parameters\Type&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Regexp&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;NT5DS|NTP&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require time-sync protocol to be NT5DS or NTP&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;regex&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10.4.a Verify that time-synchronization technology is implemented</NAME><REPORT>true</REPORT><XID>3144</XID><PARENT>3141</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Confirm that access control systems are configured to enforce privileges assigned to individuals based on job classification and function.

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\Application\RestrictGuestAccess&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict guest read access to application log&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\Security\RestrictGuestAccess&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict guest read access to security log&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\System\RestrictGuestAccess&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict guest read access to system log&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>7.2.2 Assignment of privileges to individuals based on job classification and function</NAME><REPORT>true</REPORT><XID>3145</XID><PARENT>3140</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Any physical access to data or systems that house cardholder data provides the opportunity for individuals to access devices or data and to remove systems or hardcopies, and should be appropriately restricted. For the purposes of Requirement 9, &quot;onsite personnel&quot; refers to full-time and part-time employees, temporary employees, contractors and consultants who are physically present on the entity’s premises. 
A &quot;visitor&quot; refers to a vendor, guest of any onsite personnel, service workers, or anyone who needs to enter the facility for a short duration, usually not more than one day. 
&quot;Media&quot; refers to all paper and electronic media containing cardholder data.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>9 Restrict physical access to cardholder data</NAME><REPORT>true</REPORT><XID>3128</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>5</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Malicious individuals (external and internal to an entity) often use vendor default passwords and other vendor default settings to compromise systems. These passwords and settings are well known by hacker communities and are easily determined via public information.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2 Do not use vendor-supplied defaults for system passwords and other security parameter</NAME><REPORT>true</REPORT><XID>3129</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>2</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>To ensure critical data can only be accessed by authorized personnel, systems and processes must be in place to limit access based on need to know and according to job responsibilities.
&quot;Need to know&quot; is when access rights are granted to only the least amount of data and privileges needed to perform a job</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>7 Restrict access to cardholder data by business need to know</NAME><REPORT>true</REPORT><XID>3130</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>3</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that a policy exists to control distribution of media, and that the policy covers all distributed media including that distributed to individuals.

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\Allocatecdroms&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Disabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict CD-ROM access&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\Allocatefloppies&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Disabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict Floppy access&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict USB access&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\UsbStor\Start&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;4&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict UsbStor\Start access&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>9.7 Maintain strict control over the  internal or external distribution of any kind of media</NAME><REPORT>true</REPORT><XID>3131</XID><PARENT>3128</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Firewalls are devices that control computer traffic allowed between an entity’s networks (internal) and untrusted networks (external), as well as traffic into and out of more sensitive areas within an entity’s internal trusted networks. The cardholder data environment is an example of a more sensitive area within an entity’s trusted network. A firewall examines all network traffic and blocks those transmissions that do not meet the specified security criteria. All systems must be protected from unauthorized access from untrusted networks, whether entering the system via the Internet as e-commerce, employee Internet access through desktop browsers, employee e-mail access, dedicated connections such as business-to-business connections, via wireless networks, or via other sources. Often, seeming
ly insignificant paths to and from untrusted networks can provide unprotected pathways into key systems. Firewalls are a key protection mechanism for any computer network. Other system components may provide firewall functionality, provided they meet the minimum requirements for firewalls as provided in Requirement 1. Where other system components are used within the cardholder data environment to provide firewall functionality, these devices 
must be included within the scope and assessment of Requirement 1.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>1 Install and maintain a firewall configuration to protect cardholder data</NAME><REPORT>true</REPORT><XID>3132</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Specifically those with direct connectivity to the Internet (for example, laptops used by employees), which are used to access the organization’s network. </DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>1.4 Install personal firewall software on any mobile and/or employee-owned computers</NAME><REPORT>true</REPORT><XID>3133</XID><PARENT>3132</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>For non-consumer users and administrators on all system components.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5 Ensure proper user identification and authentication management</NAME><REPORT>true</REPORT><XID>3146</XID><PARENT>3139</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Configure system security parameters to prevent misuse.</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2.2.3 Configure system security parameters to prevent misuse.</NAME><REPORT>true</REPORT><XID>3138</XID><PARENT>3134</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>2</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Assigning a unique identification (ID) to each person with access ensures that each individual is uniquely accountable for his or her actions. When such accountability is in place, actions taken on critical data and systems
are performed by, and can be traced to, known and authorized users. 

Note: These requirements are applicable for all accounts, including point-of-sale accounts, with administrative capabilities and all accounts used to view or access cardholder data or to access systems with cardholder data. However, Requirements 8.1, 8.2 and 8.5.8 through 8.5.15 are not intended to apply to user accounts within a point-of-sale payment application that only have access to one card number at a time in order to facilitate a 
single transaction (such as cashier accounts)</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8 Assign a unique ID to each person with computer access</NAME><REPORT>true</REPORT><XID>3139</XID><PARENT>-1</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>4</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>That restricts access based on a user’s need to know, and is set to &quot;deny  all&quot; unless specifically allowed. </DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>7.2 Establish an access control system for systems components with multiple users</NAME><REPORT>true</REPORT><XID>3140</XID><PARENT>3130</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Note: One example of time synchronization technology is Network Time Protocol (NTP).
</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10.4 Using time-synchronization technology, synchronize all critical system clocks and times</NAME><REPORT>true</REPORT><XID>3141</XID><PARENT>3135</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Processes should be in place to immediately restore at least the last three months’ logs for analysis

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\Application\Retention&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;365&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require application event log retention for at least one year&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\Security\Retention&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;365&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require security event log retention for at least one year&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\Eventlog\System\Retention&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;365&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require system event log retention for at least one year&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10.7.b Verify that audit logs are available for at least one year </NAME><REPORT>true</REPORT><XID>3142</XID><PARENT>3136</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Implement security features for any required services, protocols or daemons that are considered to be insecure - for example, use secured technologies such as SSH, S-FTP, SSL, or IPSec  VPN to protect insecure services such as NetBIOS, file-sharing, Telnet, FTP, etc</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2.2.2 Enable only necessary and secure services, protocols, daemons, etc., as required for the function of the system.</NAME><REPORT>true</REPORT><XID>3143</XID><PARENT>3134</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Do not allow an individual to submit a new password that is the same as any of the last four passwords he or he has used</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.12 Do not allow an individual to submit a new password that is the same as any of the last four passwords he or he has used</NAME><REPORT>true</REPORT><XID>3154</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>4</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that only necessary services or protocols are enabled

*Windows Services</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;0&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;services&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;WZCSVC&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;WMServer&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;W3SVC&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;VSS&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;upnphost&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;TlntSvr&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;TFTPD&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;TemService&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;TapiSrv&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;srvcsurg&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Spooler&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;SNMPTRAP&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;SNMP&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;SMTPSVC&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;SharedAccess&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Schedule&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;RpcLocator&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Remote_Storage_User_Link&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Remote_Storage_Server&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;RemoteRegistry&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;RemoteAccess&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;RasMan&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;RasAuto&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Pop3Svc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;NWCWorkstation&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Ntfrs&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;NntpSvc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Netman&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;MSFtpsvc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;mnmsrvc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Messenger&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;MacPrint&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;MacFile&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;LicenseService&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;IISADMIN&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;HTTPFilter&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;helpsvc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;FAX&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;ClipSrv&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;CiSvc&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Browser&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;BINLSVC&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Appmon&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;AppMgr&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;service&amp;gt;&amp;lt;NAME&amp;gt;Alerter&amp;lt;/NAME&amp;gt;&amp;lt;/service&amp;gt;&amp;lt;/services&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2.2.2.a For a sample of system components, inspect enabled system services, daemons, and protocols</NAME><REPORT>true</REPORT><XID>3147</XID><PARENT>3143</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>For a sample of system components, obtain and inspect system configuration settings to verify that password parameters are set to require that once a user account is locked out, it remains locked for a minimum of 30 minutes or until a system administrator resets the account

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTLOCKOUTDURATION&lt;/NAME&gt;&lt;VALUE&gt;30&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.14 Set the lockout duration to a minimum of 30 minutes or until administrator enables the user ID</NAME><REPORT>true</REPORT><XID>3148</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>6</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that common security parameters are set appropriately

*Windows Policy
*Audit Policy
*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;MAXPASSWORDAGING&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Success, Failure&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\SafeDllSearchMode&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require applications to look for dll&amp;amp;#39;s in system path before working folder&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Control\Lsa\NoLMHash&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require LAN Manager not to store hash values on password change&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\policies\system\LegalNoticeCaption&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Not empty&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require LegalNoticeCaption to be specified&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;set&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\policies\system\LegalNoticeText&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Not empty&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require LegalNoticeText to be specified&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;set&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\policies\system\DontDisplayLastUserName&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require DontDisplayLastUserName to be enabled&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Driver Signing\Policy&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;1&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require Unsigned Driver Installation Behaviour to be &amp;amp;quot;Warn, but allow&amp;amp;quot;&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Microsoft\Driver Signing\Policy&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At most&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;2&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require Unsigned Driver Installation Behaviour to be &amp;amp;quot;Warn, but allow&amp;amp;quot;&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atmost&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Control\Lsa\LimitBlankPasswordUse&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Equals&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;Enabled&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require LimitBlankPasswordsUse to be Enabled&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;equals&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Policies\Microsoft\Windows\EventLog\System\MaxSize&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;32768&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require maximum size for the system log file to be more than 32768 kilobytes&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security\MaxSize&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;81920&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require maximum size for the security log file to be more than 81920 kilobytes&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application\MaxSize&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At least&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;32768&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require maximum size for the application log file to be more than 32768 kilobytes&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atleast&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>2.2.3.c For a sample of system components</NAME><REPORT>true</REPORT><XID>3149</XID><PARENT>3138</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Limit repeated access attempts  by locking out the user ID after not  more than six attempts</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.13 Limit repeated access attempts  by locking out the user ID after not  more than six attempts</NAME><REPORT>true</REPORT><XID>3150</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>5</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Require a minimum password length of at least seven characters</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.10 Require a minimum password length of at least seven characters</NAME><REPORT>true</REPORT><XID>3151</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>2</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that the time servers accept time updates from specific, industry-accepted external sources (to prevent a malicious individual from changing the clock). Optionally, those updates can be encrypted with a symmetric key, and access control lists can be created that specify the IP addresses of client machines that will be provided with the time updates (to prevent unauthorized use of internal time servers).

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\W32Time\Parameters\NtpServer&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;Regexp&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;time\.windows\.com|time\.nist\.gov&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Require time settings to be recieved from industry-accepted time sources&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;regex&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>10.4.3 Time settings are received from industry-accepted time sources</NAME><REPORT>true</REPORT><XID>3152</XID><PARENT>3141</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>2</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that authentication parameters are set to require that a user’s account be locked out after not more than six invalid logon attempts.

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTLOCKOUTTHRESHOLD&lt;/NAME&gt;&lt;VALUE&gt;6&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.13.a For a sample of system components, obtain and inspect system configuration settings</NAME><REPORT>true</REPORT><XID>3153</XID><PARENT>3150</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Change user passwords at least every 90 days</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.9 Change user passwords at least every 90 days</NAME><REPORT>true</REPORT><XID>3155</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>For a sample of system components, obtain and inspect system configuration settings to verify that system/session idle time out features have been set to 15 minutes or less.

*Registry Keys</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&amp;lt;registry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SYSTEM\CurrentControlSet\Services\LanManServer\Parameters\AutoDisconnect&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At most&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;15&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict max time before auto-disconnect&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atmost&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;entry&amp;gt;&amp;lt;KEY&amp;gt;HKLM\SOFTWARE\Policies\Microsoft\Windows NT\Terminal Services\MaxIdleTime&amp;lt;/KEY&amp;gt;&amp;lt;READABLETYPE&amp;gt;At most&amp;lt;/READABLETYPE&amp;gt;&amp;lt;VALUE&amp;gt;900000&amp;lt;/VALUE&amp;gt;&amp;lt;DESCRIPTION&amp;gt;Restrict max idle time (ms)&amp;lt;/DESCRIPTION&amp;gt;&amp;lt;VERSIONTYPE&amp;gt;atmost&amp;lt;/VERSIONTYPE&amp;gt;&amp;lt;/entry&amp;gt;&amp;lt;/registry&amp;gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.15 If a session has been idle for more than 15 minutes, require the user to re-authenticate to re-activate the terminal or session</NAME><REPORT>true</REPORT><XID>3156</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>7</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Use passwords containing both numeric and alphabetic characters</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.11 Use passwords containing both numeric and alphabetic characters</NAME><REPORT>true</REPORT><XID>3157</XID><PARENT>3146</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>3</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that password parameters are set to require that new passwords cannot be the same as the four previously used passwords

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;MINPASSWORDHISTORY&lt;/NAME&gt;&lt;VALUE&gt;4&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.12.a For a sample of system components, obtain and inspect system configuration settings</NAME><REPORT>true</REPORT><XID>3158</XID><PARENT>3154</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that password parameters are set to require passwords to be at least seven characters long

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;MINPASSWORDLENGTH&lt;/NAME&gt;&lt;VALUE&gt;7&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.10.a For a sample of system components, obtain and inspect system configuration settings</NAME><REPORT>true</REPORT><XID>3159</XID><PARENT>3151</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that password parameters are set to require passwords to contain both numeric and alphabetic characters.

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;true&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.11.a For a sample of system components, obtain and inspect system configuration settings</NAME><REPORT>true</REPORT><XID>3160</XID><PARENT>3157</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
<ITEM><DESCRIPTION>Verify that user password parameters are set to require users to change passwords at least every 90 days.

*Windows Policy</DESCRIPTION><SETTINGS>&lt;ITEMS&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PORTS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;FILECONTENTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;INSTALLEDAPPLICATIONS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;COMMANDEXECUTE&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;CHECKCONFIG&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;MAXPASSWORDAGING&lt;/NAME&gt;&lt;VALUE&gt;90&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PASSWORDCOMPLEXITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REVERSIBLEPASSWORDENCRYPTION&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTDISABLED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSGUESTRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSADMINRENAMED&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;LIMITNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYLOGONASBATCH&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;DENYGUESTNETWORKACCESS&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITLOGON&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTLOGON&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITACCOUNTMANAGEMENT&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITOBJECTACCESS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPOLICYCHANGE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITPRIVILEGEUSE&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;AUDITSYSTEMEVENTS&lt;/NAME&gt;&lt;VALUE&gt;Ignore&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;ACCOUNTCHECKS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;REGISTRYKEYS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES_POLICY&lt;/NAME&gt;&lt;VALUE&gt;1&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WINDOWSSERVICES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;EXCLUDEACCEPTEDVULNERABILITIES&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;IGNOREVULNERABILITYAVAILABILITY&lt;/NAME&gt;&lt;VALUE&gt;false&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;PATCHLEVELS&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;ITEM&gt;&lt;NAME&gt;WMIQUERIES&lt;/NAME&gt;&lt;VALUE&gt;&lt;/VALUE&gt;&lt;/ITEM&gt;&lt;/ITEMS&gt;</SETTINGS><NAME>8.5.9.a For a sample of system components, obtain and inspect system configuration settings</NAME><REPORT>true</REPORT><XID>3161</XID><PARENT>3155</PARENT><POLICY>PCI - Windows v2.0</POLICY><ORDERID>1</ORDERID><WINDOWSVERSION>Any</WINDOWSVERSION><POLICYID>1018</POLICYID></ITEM>
</ITEMLIST>