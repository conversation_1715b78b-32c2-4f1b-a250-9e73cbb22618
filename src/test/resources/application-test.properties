server.port = 8092

server.ssl.enabled = false

spring.datasource.url = ***********************************************
spring.datasource.username = opsuser
spring.datasource.password = opsuser
spring.datasource.driverClassName = org.postgresql.Driver
spring.datasource.defaultAutoCommit = false

spring.jpa.hibernate.ddl-auto = none
spring.jpa.show-sql = false
spring.jpa.format-sql = true
spring.jpa.hibernate.naming.physical-strategy = org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.current_session_context_class = org.springframework.orm.hibernate5.SpringSessionContext

spring.flyway.enabled = false

service.name = o24reportservice

db.url = ***********************************************
db.username = opsuser
db.password = opsuser
db.driver = org.postgresql.Driver

hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
hibernate.show_sql = true
hibernate.format_sql = true

kafka.consumer.vm.topic.reportexport = test.o24.vm.reportexport
kafka.consumer.vm.groupid = o24.vm
