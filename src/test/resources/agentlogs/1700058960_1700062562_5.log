{"level":7,"timestamp":1700058960,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700062560,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700062560,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700062560,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700062560,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
