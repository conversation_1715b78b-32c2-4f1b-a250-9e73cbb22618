{"level":7,"timestamp":1699990546,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699994146,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699994146,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1699994146,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1699994146,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1699994166,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:34587-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699994187,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:35532-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699994212,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:52128-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699994212,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1699994212,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699994212,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699997746,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699997746,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1699997746,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1699997746,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1699997776,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:57640-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699997801,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:49099-\u003e**********:53: read: no route to host","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699997822,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:42191-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1699997822,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1699997822,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1699997822,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700001346,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700001346,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700001346,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700001346,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700001376,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:58490-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700001402,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:57244-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700001422,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:60760-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700001422,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700001422,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700001422,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700004946,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700004946,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700004946,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700004946,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700004976,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:42709-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700005002,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:39203-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700005027,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:60921-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700005027,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700005027,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700005027,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700008546,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700008546,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700008546,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700008546,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700008576,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:54052-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700008606,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:48870-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700008632,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:41284-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700008632,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700008632,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700008632,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700012146,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700012146,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700012146,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700012146,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700012176,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:55194-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700012202,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:59333-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700012227,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:42845-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700012227,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700012227,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700012227,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700015746,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700015746,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700015746,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700015746,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700015776,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:54927-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700015802,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:44603-\u003e**********:53: read: no route to host","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700015827,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:40043-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700015827,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700015827,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700015827,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700019346,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700019346,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700019346,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700019346,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700019376,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:42853-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700019401,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:58542-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700019427,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:41157-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700019427,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700019427,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700019427,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700022946,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700022946,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700022946,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700022946,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700022976,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:52276-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700023001,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:32813-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700023027,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:55533-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700023027,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700023027,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700023027,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700026546,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700026546,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700026546,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700026546,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700026576,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:51757-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700026602,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:48975-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700026622,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:33469-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700026622,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700026622,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700026622,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700030146,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700030146,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700030146,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700030146,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700030176,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:54542-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700030201,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:51527-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700030227,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:44656-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700030227,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700030227,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700030227,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700033746,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700033746,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700033746,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700033746,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700033776,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:53397-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700033801,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:53976-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700033827,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:40536-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700033827,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700033827,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700033827,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700037346,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700037346,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700037346,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700037346,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700037366,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad.devnext.outpost24.com on **********:53: read udp **********:33389-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700037387,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad01.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad01.devnext.outpost24.com on **********:53: read udp **********:37121-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700037407,"message":"error sending request","data":{"ERROR":"Get \"https://teddysalad02.devnext.outpost24.com/rest/v0/tenants/64addef2-0f18-4ecc-8181-9629b878e829/agents/3a3881ba-a85b-4522-b15d-c941d4c13e0e/config\": dial tcp: lookup teddysalad02.devnext.outpost24.com on **********:53: read udp **********:44888-\u003e**********:53: i/o timeout","TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:358","TRACE_10":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_12":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_13":":proc.go:259","TRACE_14":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","TRACE_3":":callhome.go:137","TRACE_4":":callhome.go:42","TRACE_5":":main.go:654","TRACE_6":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_7":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_8":":main.go:118","TRACE_9":"spf13/cobra.(*Command).execute:command.go:854"}}
{"level":3,"timestamp":1700037407,"message":"error getting settings","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/errors.Trace:errors.go:49","TRACE_1":":callhome.go:138","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:42","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":3,"timestamp":1700037407,"message":"error while calling home","data":{"ERROR":"no api in settings","ERROR_TRACE_0":"auth/teddy-salad-agent/agent/connector.(*Settings).doRequest:connector.go:344","ERROR_TRACE_1":"auth/teddy-salad-agent/agent/connector.(*Settings).GetConfig:config.go:46","ERROR_TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_11":"spf13/cobra.(*Command).Execute:command.go:895","ERROR_TRACE_12":":proc.go:259","ERROR_TRACE_13":":asm_amd64.s:1595","ERROR_TRACE_2":":callhome.go:137","ERROR_TRACE_3":":callhome.go:42","ERROR_TRACE_4":":main.go:654","ERROR_TRACE_5":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","ERROR_TRACE_6":"auth/teddy-salad-agent/agent/service.Run:service.go:62","ERROR_TRACE_7":":main.go:118","ERROR_TRACE_8":"spf13/cobra.(*Command).execute:command.go:854","ERROR_TRACE_9":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_0":"lib/arctium/v4/log.Log:log.go:165","TRACE_1":":main.go:661","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700037407,"message":"MAIN LOOP","data":{"TRACE_0":"auth/teddy-salad-agent/agent/service.(*ServiceControl).GetChannel:service_nix.go:88","TRACE_1":":main.go:594","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700040946,"message":"entered callhome","data":{"TRACE_0":":main.go:654","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":":asm_amd64.s:1595","TRACE_2":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_3":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_4":":main.go:118","TRACE_5":"spf13/cobra.(*Command).execute:command.go:854","TRACE_6":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_7":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_8":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_9":":proc.go:259"}}
{"level":7,"timestamp":1700040946,"message":"trying to enroll","data":{"TRACE_0":":callhome.go:96","TRACE_1":":callhome.go:95","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700040946,"message":"not time to re-enroll","data":{"ERROR":"not time to reenroll","TRACE_0":":callhome.go:109","TRACE_1":":callhome.go:99","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
{"level":7,"timestamp":1700040946,"message":"enrolled","data":{"TRACE_0":":callhome.go:122","TRACE_1":"lib/arctium/v4/log.Log:log.go:164","TRACE_10":"spf13/cobra.(*Command).Execute:command.go:895","TRACE_11":":proc.go:259","TRACE_12":":asm_amd64.s:1595","TRACE_2":":callhome.go:36","TRACE_3":":main.go:654","TRACE_4":"auth/teddy-salad-agent/agent/service.run:service_nix.go:84","TRACE_5":"auth/teddy-salad-agent/agent/service.Run:service.go:62","TRACE_6":":main.go:118","TRACE_7":"spf13/cobra.(*Command).execute:command.go:854","TRACE_8":"spf13/cobra.(*Command).ExecuteC:command.go:958","TRACE_9":"spf13/cobra.(*Command).Execute:command.go:895"}}
