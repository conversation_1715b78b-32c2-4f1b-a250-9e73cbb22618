package com.chilicoders.core.test;

import java.security.GeneralSecurityException;
import java.security.SecureRandom;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;

import org.junit.Assert;

import com.chilicoders.util.SslUtils;

/**
 * SSL Utilities for testing SSL connections without caring about the certificate.
 */
public class SSLUtils {
	/**
	 * Set a hostname verifier and certificate checker that allows everything.
	 */
	public static void setSSLHostnameVerifierAndCert() {
		try {
			final KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
			kmf.init(null, null);

			final SSLContext sc = SSLContext.getInstance("TLSv1.2");
			sc.init(kmf.getKeyManagers(), SslUtils.getTrustAllTrustManager(), new SecureRandom());
			HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

			final HostnameVerifier nicehostNameVerifier = (hostname, session) -> {
				return true;
			};
			HttpsURLConnection.setDefaultHostnameVerifier(nicehostNameVerifier);
		}
		catch (final GeneralSecurityException ex) {
			Assert.fail("Could not initiate SSL verifier");
		}
	}
}
