package com.chilicoders.core.user.impl;

import com.chilicoders.core.user.api.CustomerInformation;
import com.chilicoders.core.user.impl.jpa.CustomerInformationImpl;

public class TestCustomerInformationFactory {

	public static final String ADDRESS = "address";
	public static final String CITY = "city";
	public static final String POSTAL = "postal";
	public static final String PHONE = "phone";
	public static final String PCI_EMAIL = "pciEmail";
	public static final String PCI_NAME = "pciName";

	/**
	 * Get customer information.
	 *
	 * @param userId User id
	 * @return Customer information
	 */
	public static CustomerInformation getCustomerInformation(final long userId) {
		final CustomerInformationImpl customerInfo = new CustomerInformationImpl();
		customerInfo.setAddress(ADDRESS + userId);
		customerInfo.setCity(CITY + userId);
		customerInfo.setPostal(POSTAL + userId);
		customerInfo.setPhone(PHONE + userId);
		customerInfo.setPciEmail(PCI_EMAIL + userId);
		customerInfo.setPciName(PCI_NAME + userId);
		return customerInfo;
	}
}
