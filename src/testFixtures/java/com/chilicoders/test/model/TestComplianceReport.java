package com.chilicoders.test.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Builder
@Entity(name = "TestComplianceReport")
@Table(name = "tcompliancereports")
public class TestComplianceReport {
	@Setter
	@Id
	@Column(name = "xid")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tcompliancereports_seq_generator")
	@SequenceGenerator(name = "tcompliancereports_seq_generator", sequenceName = "tcompliancereports_seq", initialValue = 1, allocationSize = 1)
	private long id;

	@Column(name = "xuserxid")
	private long userId;

	@Column(name = "xsubuserxid")
	private long subUserId;

	@Column
	private Date date;

	@Column
	private long policyId;

	@Type(type = "long-array")
	@Column(columnDefinition = "bigint[]")
	private Long[] targetGroups;

	@Column
	private long scanJobId;

	@Column
	private long compliant;

	@Column
	private long notCompliant;

	@Column
	private long unknownCompliance;
}
