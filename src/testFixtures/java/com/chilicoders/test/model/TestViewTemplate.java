package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.model.ViewType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
@TypeDefs({
	@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
	@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Entity(name = "TestViewTemplate")
@Table(name = "viewtemplates")
public class TestViewTemplate {
	@Id
	@Setter
	@Column
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "viewtemplates_seq_generator")
	@SequenceGenerator(name = "viewtemplates_seq_generator", sequenceName = "viewtemplates_id_seq", initialValue = 1, allocationSize = 1)
	private Integer id;

	@Column
	private Integer customerId;

	@Column
	private String name;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "enum_type")
	private ViewType type;

	@Column
	@Type(type = "jsonb")
	private String filters;

	@Column
	@Type(type = "jsonb")
	private String columnsWidth;

	@Column
	private boolean system;
}
