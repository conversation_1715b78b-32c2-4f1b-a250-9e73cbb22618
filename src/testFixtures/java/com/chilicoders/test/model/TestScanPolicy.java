package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
@TypeDefs({
	@TypeDef(name = "json", typeClass = JsonBinaryType.class)
})
@Entity(name = "TestScanPolicy")
@Table(name = "scanpolicies")
public class TestScanPolicy {
	@Setter
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "scanpolicies_seq_generator")
	@SequenceGenerator(name = "scanpolicies_seq_generator", sequenceName = "scanpolicies_id_seq", initialValue = 1, allocationSize = 1)
	private Integer id;

	@Column
	private Integer customerId;

	@Column
	private String name;

	@Column
	private boolean system;

	@Column(columnDefinition = "json")
	@Type(type = "json")
 	private String settings;
}
