package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.vladmihalcea.hibernate.type.array.IntArrayType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Builder
@TypeDefs({
		@TypeDef(name = "int-array", typeClass = IntArrayType.class)
})
@Entity(name = "TestUserRole")
@Table(name = "userroles")
public class TestUserRole {
	@Id
	@Setter
	@Column
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userroles_seq_generator")
	@SequenceGenerator(name = "userroles_seq_generator", sequenceName = "userroles_id_seq", initialValue = 1, allocationSize = 1)
	private Integer id;

	@Column
	private String name;

	@Column
	private Integer customerId;

	@Column
	@Type(type = "int-array")
	private Integer[] permissionIds;

	@Column
	private boolean system;
}
