package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "TestScanner")
@Table(name = "tscanners")
public class TestScanner {
	@Setter
	@Id
	@Column(name = "xid")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tscanners_seq_generator")
	@SequenceGenerator(name = "tscanners_seq_generator", sequenceName = "tscanners_seq", initialValue = 1, allocationSize = 1)
	private long id;

	@Column
	private String name;

	@Column(name = "xuserxid")
	private long userId;

	@Column(name = "isgroup")
	@Type(type = "numeric_boolean")
	@Builder.Default
	private boolean group = false;

	@Column(name = "groupxid")
	private long groupId;

	@Column
	private String ipaddress;
}
