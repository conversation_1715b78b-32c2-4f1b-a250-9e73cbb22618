package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "TestTicket")
@Table(name = "tworkflows")
public class TestTicket {
	@Setter
	@Id
	@Column(name = "xid")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tworkflows_seq_generator")
	@SequenceGenerator(name = "tworkflows_seq_generator", sequenceName = "tworkflows_seq", initialValue = 1, allocationSize = 1)
	private long id;

	@Column(name = "xuserxid")
	private long userId;

	@Column(name = "xipxid")
	private long targetId;

	@Column(name = "vcvulnid")
	private long vulnerabilityId;

	@Column(name = "iport")
	private long port;

	@Column(name = "iprotocol")
	private long protocol;

	@Column
	private int status;

	@Column
	private int priority;

	@Column
	private long findingId;
}
