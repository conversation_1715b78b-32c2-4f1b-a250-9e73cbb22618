package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.IntegrationType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDefs({
		@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
		@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Entity(name = "TestEventSubscription")
@Table(name = "eventsubscriptions")
public class TestEventSubscription {
	@Id
	@Setter
	@Column
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "eventsubscriptions_seq_generator")
	@SequenceGenerator(name = "eventsubscriptions_seq_generator", sequenceName = "eventsubscriptions_id_seq", initialValue = 1, allocationSize = 1)
	private Integer id;

	@Column
	private Integer customerId;

	@Column
	private String name;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "enum_type")
	private Trigger trigger;

	@Column
	private Integer viewTemplateId;

	@Column
	private Integer integrationId;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "enum_type")
	private IntegrationType integrationType;

	@Column
	@Type(type = "jsonb")
	private String contentConfiguration;

	@Column
	@Type(type = "jsonb")
	private String settings;
}
