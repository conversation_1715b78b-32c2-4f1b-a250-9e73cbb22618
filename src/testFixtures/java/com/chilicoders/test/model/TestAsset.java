package com.chilicoders.test.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.model.Source;
import com.chilicoders.model.SubscriptionType;
import com.vladmihalcea.hibernate.type.array.EnumArrayType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Builder
@TypeDefs({
		@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
		@TypeDef(name = "source_array_type",
				typeClass = EnumArrayType.class,
				parameters = {
						@Parameter(
								name = EnumArrayType.SQL_ARRAY_TYPE,
								value = "source"
						)
				}
		),
		@TypeDef(name = "subscriptiontype_array_type",
				typeClass = EnumArrayType.class,
				parameters = {
						@Parameter(
								name = EnumArrayType.SQL_ARRAY_TYPE,
								value = "subscriptiontype"
						)
				}
		)
})
@Entity(name = "TestAsset")
@Table(name = "assets")
public class TestAsset {
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "assets_seq_generator")
	@SequenceGenerator(name = "assets_seq_generator", sequenceName = "assets_id_seq", initialValue = 1, allocationSize = 1)
	private Integer id;

	@Column
	private String name;

	@Column
	private Integer customerId;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "source_array_type")
	private Source[] source;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "subscriptiontype_array_type")
	private SubscriptionType[] activeSubscriptionTypes;
}
