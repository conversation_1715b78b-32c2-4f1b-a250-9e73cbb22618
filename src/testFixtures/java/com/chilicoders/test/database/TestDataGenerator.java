package com.chilicoders.test.database;

import java.sql.SQLException;
import java.util.UUID;

import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;

public class TestDataGenerator {
	/**
	 * Gets a subuser appaccess token with the supplied privileges.
	 *
	 * @param statementExecutor Statement executor.
	 * @param rights Privileges to obtain, name should be taken from tusergroups.
	 * @param limitedIpAccess A range of targets the subuser can access, null for no limit.
	 * @param parentId Parent subuser id
	 * @param mainUserId Main user id
	 * @return Appaccess token.
	 */
	public static String getSubuser(final NativeStatementExecutor statementExecutor, final String[] rights, final String limitedIpAccess, final long parentId,
									final long mainUserId) throws SQLException {
		statementExecutor.execute(
				new NativeSqlStatement("INSERT INTO tusergroups (xid, xuserxid, vcname) VALUES (nextval('tusergroups_seq'), ?, ?)", mainUserId, "User group"));
		for (final String right : rights) {
			final boolean bool = right.equals("compliance_enabled")
					|| right.equals("web")
					|| right.equals("readlicense")
					|| right.equals("grantalltickets")
					|| right.equals("readonly")
					|| right.equals("read_auditlogs");
			statementExecutor.execute(new NativeSqlStatement("UPDATE tusergroups SET " + right + "= ? WHERE xid=(SELECT MAX(xid) FROM tusergroups)", bool ? true : 1));
		}
		final String res = UUID.randomUUID().toString().toUpperCase();
		statementExecutor.execute(
				new NativeSqlStatement("INSERT INTO tsubusers(xid, xiparentid, vcusername, vcpassword, vcfirstname, vclastname, vcemail, boallhosts, xisubparentid) "
						+ "VALUES (nextval('tsubusers_seq'), ?, ?, upper(md5(?)), 'Subuser', 'Readonlyallaccess', '<EMAIL>', "
						+ (limitedIpAccess != null ? 0 : 1)
						+ ", ?)", mainUserId, res, "security", parentId));
		statementExecutor.execute(new NativeSqlStatement("INSERT INTO xlinksubusergroups(xsubxid, xgroupxid) "
				+ "VALUES ((SELECT xid FROM tsubusers WHERE xiparentid = ? ORDER BY xid DESC LIMIT 1), (SELECT xid FROM tusergroups WHERE xuserxid=? ORDER BY xid DESC LIMIT 1))",
				mainUserId, mainUserId));
		statementExecutor.execute(new NativeSqlStatement("INSERT INTO tappaccess(xid, xuserxid, xsubuserxid, tokenkey, name) "
				+ "VALUES (nextval('tappaccess_seq'), ?, (SELECT xid FROM tsubusers WHERE xiparentid=? ORDER BY XID DESC LIMIT 1), ?, ?)", mainUserId, mainUserId, res,
				"Random token"));

		if (limitedIpAccess != null) {
			statementExecutor.execute(new NativeSqlStatement(
					"INSERT INTO xlinksubhosts(xsubxid, vctarget, ipaddress, endipaddress) VALUES ((SELECT xid FROM tsubusers WHERE xiparentid=? ORDER BY xid DESC LIMIT 1), ?, ?::inet, ?::inet)",
					mainUserId, limitedIpAccess, limitedIpAccess.substring(0, limitedIpAccess.indexOf('-')), limitedIpAccess.substring(limitedIpAccess.indexOf('-') + 1)));
		}

		return res;
	}
}
