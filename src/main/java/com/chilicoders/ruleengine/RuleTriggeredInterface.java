package com.chilicoders.ruleengine;

import java.util.List;

import org.json.JSONObject;

import com.chilicoders.core.rule.api.ExecutableRuleInterface;
import com.chilicoders.core.rule.api.RuleInterface;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.ReportEntryTypes;

public interface RuleTriggeredInterface {
	void ruleTriggered(final RuleInterface rule, final String vhost, final int port, final String path, final Protocol protocol, final RuleTriggered triggered,
					   final String information, final String product,
					   final boolean potentialFalsePositive, final ReportEntryTypes itype, final String patchInformation, final JSONObject matchInformation)
			throws RuleException;

	void rulesLoaded(final List<? extends ExecutableRuleInterface> rules, final long milliSecondsToLoad);

	void ruleEngineFinished(final long executionTime, final int rulesTriggered);
}
