package com.chilicoders.ruleengine.model;

import java.util.Date;

import javax.persistence.Column;

import com.chilicoders.ruleengine.ProductionInformationInterface;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Getter
public class ProductInformation implements ProductionInformationInterface {
	@Column
	private String name;
	@Column
	private String parser;
	@Column
	private int parserOptions;
	@Column(name = "partorder")
	private Short[] parserOrders;
	@Column
	private String parserDelimiters;
	@Setter
	@Column
	private String[] backported;
	@Setter
	@Column
	private String product;
	@Setter
	@Column
	private String[] productChain;
	@Column
	private String[] branches;
	@Column
	private String[] signatureKeys;
	@Column
	private Date productEndOfLife;
	@Column
	private String[] versionEndOfLife;
	@Column
	private boolean patchesImpliedByVersion;
	@Setter
	@Column
	private String[] latestServicePacks;

	private String[][] versionEndOfLifeCache = null;

	@Override
	public Date getProductEol() {
		return productEndOfLife;
	}

	/**
	 * @return Signature keys.
	 */
	public String[] getSignatureKeys() {
		if (signatureKeys == null) {
			return new String[0];
		}
		final String[] res = new String[signatureKeys.length];
		for (int i = 0; i < res.length; i++) {
			final String[] parts = signatureKeys[i].split("§");
			res[i] = StringUtils.setEmpty(parts[0], "").trim();
		}
		return res;
	}

	/**
	 * @return Backported product information.
	 */
	public String[][] getBackported() {
		if (backported == null) {
			return new String[0][0];
		}
		final String[][] res = new String[backported.length][2];
		for (int i = 0; i < res.length; i++) {
			final String[] parts = backported[i].split("§");
			res[i][0] = parts[0];
			if (parts.length > 1) {
				res[i][1] = parts[1];
			}
			else {
				res[i][1] = "None";
			}
		}
		return res;
	}

	/**
	 * @return Version EOL information.
	 */
	@Override
	public String[][] getVersionEndOfLife() {
		if (versionEndOfLifeCache == null) {
			if (versionEndOfLife == null) {
				versionEndOfLifeCache = new String[0][0];
			}
			else {
				final String[][] result = new String[versionEndOfLife.length][2];
				for (int i = 0; i < result.length; i++) {
					final String[] parts = versionEndOfLife[i].split("§");
					result[i][0] = parts[0];
					if (parts.length > 1) {
						result[i][1] = parts[1];
					}
				}

				versionEndOfLifeCache = result;
			}
		}
		return versionEndOfLifeCache;
	}

	@Override
	public boolean getPatchesImpliedByVersion() {
		return patchesImpliedByVersion;
	}
}
