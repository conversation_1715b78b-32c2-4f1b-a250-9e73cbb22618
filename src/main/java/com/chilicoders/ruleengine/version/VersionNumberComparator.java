package com.chilicoders.ruleengine.version;

import java.sql.SQLException;

import com.chilicoders.ruleengine.RuleUtilities;
import com.chilicoders.ruleengine.comparators.EvaluatorContextInterface;
import com.chilicoders.util.StringUtils;

public class VersionNumberComparator {
	private static boolean checkBranch(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		final String branch = RuleUtilities.getBranch(product, conditionValue, context);
		return branch == null || context.getVersionParser(product).versionCompare(value, branch, product) >= 0;
	}

	public static boolean lessThanIgnoreBranch(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		return context.getVersionParser(product).versionCompare(value, conditionValue, product) == VersionParser.COMPARISON_LESS;
	}

	/**
	 * Checks if version is less than.
	 *
	 * @param value Value to check.
	 * @param conditionValue Version to check against.
	 * @param context Evaluator context.
	 * @param product Product.
	 * @return True if less.
	 */
	public static boolean lessThan(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		if (context.getVersionParser(product).versionCompare(value, conditionValue, product) == VersionParser.COMPARISON_LESS) {
			return checkBranch(value, conditionValue, context, product);
		}
		return false;
	}

	public static boolean greaterThan(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		return context.getVersionParser(product).versionCompare(value, conditionValue, product) == VersionParser.COMPARISON_MORE;
	}

	public static boolean lessThanEqualsIgnoreBranch(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product)
			throws SQLException {
		final int versionCompare = context.getVersionParser(product).versionCompare(value, conditionValue, product);
		return versionCompare == VersionParser.COMPARISON_LESS || versionCompare == VersionParser.COMPARISON_EQUAL;
	}

	/**
	 * Checks if version is less than or equals..
	 *
	 * @param value Value to check.
	 * @param conditionValue Version to check against.
	 * @param context Evaluator context.
	 * @param product Product.
	 * @return True if less.
	 */
	public static boolean lessThanEquals(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		final int versionCompare = context.getVersionParser(product).versionCompare(value, conditionValue, product);
		if (versionCompare == VersionParser.COMPARISON_EQUAL || versionCompare == VersionParser.COMPARISON_LESS) {
			return checkBranch(value, conditionValue, context, product);
		}
		return false;
	}

	public static boolean equals(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		return context.getVersionParser(product).versionCompare(value, conditionValue, product) == VersionParser.COMPARISON_EQUAL;
	}

	public static boolean greaterThanEquals(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		return context.getVersionParser(product).versionCompare(value, conditionValue, product) >= 0;
	}

	/**
	 * Checks if version is on same branch.
	 *
	 * @param value Version to check.
	 * @param conditionValue Version to check against.
	 * @param context Evaluator context.
	 * @param product Product.
	 * @return True is on same branch.
	 */
	public static boolean branch(final String value, final String conditionValue, final EvaluatorContextInterface context, final String product) throws SQLException {
		final String branch = RuleUtilities.getBranch(product, conditionValue, context);
		final String installedBranch = RuleUtilities.getBranch(product, value, context);
		if (branch == null && installedBranch == null) {
			return false;
		}
		return StringUtils.compare(branch, installedBranch);
	}

	/**
	 * Checks if version is on between two versions.
	 *
	 * @param version Version to check.
	 * @param low Low version to check against.
	 * @param high High version to check against.
	 * @param context Evaluator context.
	 * @param product Product.
	 * @return True is on same branch.
	 */
	public static boolean between(final String version, final String low, final String high, final EvaluatorContextInterface context, final String product) throws SQLException {
		final VersionParser parser = context.getVersionParser(product);
		final int highRes = parser.versionCompare(version, high, product);
		if (highRes == VersionParser.COMPARISON_MORE) {
			return false;
		}
		final int compareLow = parser.versionCompare(version, low, product);
		return compareLow == VersionParser.COMPARISON_EQUAL || compareLow == VersionParser.COMPARISON_MORE;
	}

	public static boolean regexp(final String version, final String conditionValue, final EvaluatorContextInterface context, final String product) {
		return StringUtils.setEmpty(version, "").matches(conditionValue);
	}
}
