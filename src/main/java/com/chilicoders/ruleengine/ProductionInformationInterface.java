package com.chilicoders.ruleengine;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.chilicoders.util.StringUtils;

public interface ProductionInformationInterface {
	String getName();

	String getParser();

	int getParserOptions();

	Short[] getParserOrders();

	String getParserDelimiters();

	String[][] getBackported();

	String getProduct();

	String[] getProductChain();

	String[] getBranches();

	/**
	 * Clean product information.
	 *
	 * @param list Indata.
	 * @return Cleaned indata.
	 */
	static List<String> cleanProductInformation(final List<String> list) {
		if (list == null) {
			return null;
		}
		final List<String> returnList = new ArrayList<>();
		for (final String info : list) {
			if (!StringUtils.isEmpty(info)) {
				returnList.add(info.split("§")[0]);
			}
		}
		return returnList;
	}

	String[] getSignatureKeys();

	Date getProductEol();

	String[][] getVersionEndOfLife();

	boolean getPatchesImpliedByVersion();

	/**
	 * Get latest service pack for version.
	 *
	 * @param version version
	 * @return Latest service pack
	 */
	default String[] getLatestServicePack(final String version) {
		if (getLatestServicePacks() == null || StringUtils.isEmpty(version)) {
			return null;
		}
		for (final String latestServicePack : getLatestServicePacks()) {
			final String[] packs = latestServicePack.split("§");
			if (StringUtils.setEmpty(packs[0], "").trim().equals(version.trim())) {
				return new String[] {packs[0], packs.length > 1 ? packs[1] : ""};
			}
		}
		for (final String latestServicePack : getLatestServicePacks()) {
			final String[] packs = latestServicePack.split("§");
			if (StringUtils.setEmpty(packs[0], "").trim().startsWith(version.replaceAll("SP(\\d)*", "").trim())) {
				return new String[] {packs[0], packs.length > 1 ? packs[1] : ""};
			}
		}
		return null;
	}

	String[] getLatestServicePacks();

}
