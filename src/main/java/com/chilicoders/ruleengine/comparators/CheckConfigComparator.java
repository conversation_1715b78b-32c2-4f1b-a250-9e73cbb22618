package com.chilicoders.ruleengine.comparators;

import java.sql.SQLException;
import java.util.List;

import com.chilicoders.core.rule.api.ExecutableRuleInterface;
import com.chilicoders.core.rule.api.RuleDefinitionInterface;
import com.chilicoders.core.rule.api.RuleFact;
import com.chilicoders.ruleengine.InstalledProductInfo;
import com.chilicoders.ruleengine.ScanDataCollector.ComplianceFactTypes;
import com.chilicoders.util.StringUtils;

public class CheckConfigComparator implements RuleComparator {

	public static final class CheckConfigComparison {
		private final String service;
		private final String description;
		private final String line;

		/**
		 * Create new CheckConfigComparison.
		 *
		 * @param service Service name
		 * @param description Check description
		 * @param line Probe line for check
		 */
		public CheckConfigComparison(final String service, final String description, final String line) {
			this.description = description;
			this.service = service;
			this.line = line;
		}
	}

	private void addDescriptionAndKey(final StringBuilder sb, final String description, final String key) {
		sb.append("<row><col>").append(StringUtils.setEmpty(description, "")).append("</col><col>").append(key).append("</col></row>");
	}

	@SuppressWarnings("unchecked")
	@Override
	public EvaluateResult compareProduct(final InstalledProductInfo productInfo, final RuleDefinitionInterface ruleDef, final EvaluatorContextInterface context, final RuleContextInterface ruleContext,
										 final String productName) throws SQLException {
		if (!ruleDef.isCompliance()) {
			return EvaluateResult.NoMatch;
		}
		final List<CheckConfigComparison> list = (List<CheckConfigComparison>) ruleDef.getGenericObject();

		if (!ruleContext.getRule().isCompliance()) {
			return EvaluateResult.NoMatch;
		}
		final ExecutableRuleInterface rule = ruleContext.getRule();

		final StringBuilder sb = new StringBuilder();
		for (final CheckConfigComparison key : list) {
			final RuleFact fact = context.getComplianceFact(ComplianceFactTypes.CheckConfigChecks, key.line);
			if (fact == null) {
				addDescriptionAndKey(sb, key.description + " (Service check not performed)", key.service);
			}
			else {
				rule.getComplianceFacts().add(fact);
				if (!"true".equals(fact.getFact())) {
					addDescriptionAndKey(sb, key.description, key.service);
				}
			}
		}

		if (sb.length() > 0) {
			final String desc = "\n[B:[Services]]\n<rtab><columns>2</columns>" + sb + "</rtab>";
			return new EvaluateResult(EvaluateResultType.Match, desc);
		}

		return EvaluateResult.NoMatch;
	}

}
