package com.chilicoders.ruleengine.comparators;

import java.sql.SQLException;

import com.chilicoders.core.rule.api.RuleDefinitionInterface;
import com.chilicoders.ruleengine.InstalledProductInfo;

public class EditionComparator implements RuleComparator {

	@Override
	public EvaluateResult compareProduct(final InstalledProductInfo productInfo, final RuleDefinitionInterface ruleDef, final EvaluatorContextInterface context, final RuleContextInterface ruleContext,
										 final String productName) throws SQLException {
		final boolean res = ruleDef.getConditionValue().equals(productInfo.edition);
		String gatheredInformation = res ? String.format(reasonString, "edition", context.getPresentableProductName(ruleDef.getProduct()), ruleDef.getConditionValue()) : null;
		if (ruleDef.isHideReport()) {
			gatheredInformation = null;
		}
		return new EvaluateResult(res, gatheredInformation);
	}
}
