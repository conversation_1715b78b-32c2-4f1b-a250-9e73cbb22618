package com.chilicoders.ruleengine;

import com.chilicoders.model.WasTypes;
import com.chilicoders.util.xml.XmlUtils;

import lombok.Getter;

@Getter
public class Crawled {
	private final String url;
	private final String method;
	private final long time;
	private final int code;
	private final String post;

	/**
	 * Create new Crawled object.
	 *
	 * @param url Url
	 * @param post Post data
	 * @param method Request mehod
	 * @param time Request time
	 * @param code Response code
	 */
	public Crawled(final String url, final String post, final String method, final long time, final int code) {
		this.url = url;
		this.post = post;
		this.method = method;
		this.time = time;
		this.code = code;
	}

	/**
	 * Get XML for object.
	 *
	 * @param buffer Buffer where xml will be saved
	 * @param fuzzed true if url was fuzzed, otherwise false
	 */
	public void toXML(final StringBuilder buffer, final boolean fuzzed) {
		buffer.append("<REPORT>");
		buffer.append("<TYPE>").append(WasTypes.Crawled.getId()).append("</TYPE>");
		buffer.append("<URL>").append(XmlUtils.escapeXmlText(getUrl(), true)).append("</URL>");
		buffer.append("<METHOD>").append(XmlUtils.escapeXmlText(getMethod(), true)).append("</METHOD>");
		buffer.append("<TIME>").append(getTime()).append("</TIME>");
		buffer.append("<CODE>").append(getCode()).append("</CODE>");
		buffer.append("<POST>").append(XmlUtils.escapeXmlText(getPost(), true)).append("</POST>");
		buffer.append("<FUZZED>").append(fuzzed ? 1 : 0).append("</FUZZED>");
		buffer.append("</REPORT>");
	}
}
