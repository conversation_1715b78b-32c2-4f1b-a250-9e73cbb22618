package com.chilicoders.ruleengine.models;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.json.JSONObject;

import com.chilicoders.model.InformationPortEncryptionType;
import com.chilicoders.model.Protocol;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally incorporating reference to mutable object")
public class PortInformation {
	private final int port;

	private final Protocol protocol;

	private String serviceName;

	private Set<String> tlsVersions = new HashSet<>();

	private Set<String> tlsCiphers = new HashSet<>();

	private Set<String> sshCiphers = new HashSet<>();

	private Set<String> sshHexAlgorithms = new HashSet<>();

	private String protocolVersion;

	private String banner;

	private String layer;

	private Boolean encaps;

	public PortInformation(final int port, final Protocol protocol) {
		this.port = port;
		this.protocol = protocol;
	}

	/**
	 * Get port information as JSON.
	 *
	 * @return JSON
	 */
	public JSONObject getJson() {
		final JSONObject json = new JSONObject();
		json.put("port", this.port);
		final com.chilicoders.rest.models.Service.Protocol protocol =
				com.chilicoders.rest.models.Service.Protocol.getProtocol(this.protocol.name().toUpperCase());
		json.putOpt("protocol", protocol);
		json.putOpt("serviceName", this.serviceName);
		json.putOpt("banner", this.banner);
		json.putOpt("layer", this.layer);
		json.putOpt("encaps", this.encaps);

		final List<JSONObject> encryptionList = new ArrayList<>();
		if (this.protocolVersion != null) {
			final JSONObject encryption = new JSONObject();
			encryption.put("type", InformationPortEncryptionType.SSH_VERSION);
			encryption.put("value", this.protocolVersion);
			encryptionList.add(encryption);
		}
		for (final String value : this.tlsVersions) {
			final JSONObject encryption = new JSONObject();
			encryption.put("type", InformationPortEncryptionType.TLS_VERSION);
			encryption.put("value", value);
			encryptionList.add(encryption);
		}
		for (final String value : this.tlsCiphers) {
			final JSONObject encryption = new JSONObject();
			encryption.put("type", InformationPortEncryptionType.TLS_CIPHER);
			encryption.put("value", value);
			encryptionList.add(encryption);
		}
		for (final String value : this.sshCiphers) {
			final JSONObject encryption = new JSONObject();
			encryption.put("type", InformationPortEncryptionType.SSH_CIPHER);
			encryption.put("value", value);
			encryptionList.add(encryption);
		}
		for (final String value : this.sshHexAlgorithms) {
			final JSONObject encryption = new JSONObject();
			encryption.put("type", InformationPortEncryptionType.SSH_KEX);
			encryption.put("value", value);
			encryptionList.add(encryption);
		}
		if (!encryptionList.isEmpty()) {
			json.put("encryption", encryptionList);
		}

		final JSONObject service = new JSONObject();
		service.put("port", this.port);
		service.put("protocol", this.protocol.value);
		json.put("service", service);

		return json;
	}
}
