package com.chilicoders.ruleengine;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.chilicoders.model.WasTypes;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

@Getter
public class HostPort {
	private String host;
	private String port;
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private final HashMap<String, ArrayList<Crawled>> crawled = new HashMap<>();
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private final HashMap<String, ArrayList<Finding>> findings = new HashMap<>();

	@Override
	public int hashCode() {
		return this.host.hashCode();
	}

	@Override
	public boolean equals(final Object obj) {
		if (!(obj instanceof HostPort)) {
			return false;
		}
		return this.host.equals(((HostPort) obj).host) && StringUtils.compare(port, ((HostPort) obj).port);
	}

	/**
	 * Create new HostPort object.
	 *
	 * @param uri Url
	 */
	public HostPort(final String uri) {
		try {
			final URL url = new URL(uri);
			int port = url.getPort();
			if (port == -1) {
				if (url.getProtocol().equalsIgnoreCase("http")) {
					port = 80;
				}
				else if (url.getProtocol().equalsIgnoreCase("https")) {
					port = 443;
				}
			}

			this.host = url.getHost();
			this.host = StringUtils.trimChar(this.host, true, '[');
			this.host = StringUtils.trimChar(this.host, true, ']');
			this.port = "" + port;
		}
		catch (final RuntimeException | MalformedURLException e) {
			// Ignore
		}
	}

	/**
	 * Get virtual host.
	 *
	 * @return The virtual host
	 */
	public String getVirtualHost() {
		if ("80".equals(this.port) || StringUtils.isEmpty(this.port)) {
			return host;
		}
		return this.host + ":" + this.port;
	}

	/**
	 * Add a crawled url to host.
	 *
	 * @param url Url crawled
	 * @param hm Map with information for the crawled url
	 */
	public void addCrawled(final String url, final HashMap<String, String> hm) {
		final String path = url.replaceAll("[#?].*", "");
		final ArrayList<Crawled> list = this.crawled.computeIfAbsent(path, k -> new ArrayList<>());

		final String method = StringUtils.setEmpty(hm.get("METHOD"), "").toLowerCase();
		final long time = StringUtils.getLongValue(hm.get("TIME"), 0);
		final int code = StringUtils.getIntValue(hm.get("CODE"), 0);
		final String post = StringUtils.setEmpty(hm.get("POST"), "");

		list.add(new Crawled(url, post, method, time, code));
	}

	/**
	 * Add finding to host.
	 *
	 * @param url Url for finding
	 * @param hm Map with information for the finding
	 */
	public void addFinding(final String url, final HashMap<String, String> hm) {
		final String id = StringUtils.setEmpty(hm.get("ID"), "");
		final ArrayList<Finding> list = this.findings.computeIfAbsent(id, k -> new ArrayList<>());
		final String method = StringUtils.setEmpty(hm.get("METHOD"), "").toLowerCase();
		final String time = StringUtils.setEmpty(hm.get("TIME"), "");
		final String post = StringUtils.setEmpty(hm.get("POST"), "");
		final String content = StringUtils.setEmpty(hm.get("CONTENT"), "");
		final String requestheaders = StringUtils.setEmpty(hm.get("REQUESTHEADERS"), "");
		final String responseheaders = StringUtils.setEmpty(hm.get("RESPONSEHEADERS"), "");
		final String auxdata = StringUtils.setEmpty(hm.get("AUXDATA"), "");
		final String wasFindingId = StringUtils.setEmpty(hm.get("WASFINDINGID"), "");
		final String specialNotes = StringUtils.setEmpty(hm.get("SPECIALNOTES"), "");

		list.add(new Finding(WasTypes.Finding, id, url, method, time, post, content, requestheaders, responseheaders, auxdata, hm.get("MATCH"),
				StringUtils.getIntValue(hm.get("MATCHSTART"), 0), StringUtils.getIntValue(hm.get("MATCHLENGTH"), 0), wasFindingId, specialNotes));
	}

	/**
	 * Get host as XML.
	 *
	 * @return XML data
	 */
	public StringBuilder toXML() {
		final StringBuilder buffer = new StringBuilder();
		buffer.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?><REPORTLIST>");

		for (final Map.Entry<String, ArrayList<Crawled>> url : this.crawled.entrySet()) {
			final ArrayList<Crawled> list = url.getValue();
			for (final Crawled crawled : list) {
				crawled.toXML(buffer, false);
			}
		}

		for (final ArrayList<Finding> findings : this.findings.values()) {
			for (final Finding finding : findings) {
				finding.toXML(buffer);
			}
		}

		buffer.append("</REPORTLIST>");

		return buffer;
	}
}
