package com.chilicoders.hiab.update;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.DbTable;
import com.chilicoders.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@DbTable(name = "dummy")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScanUsageInfo extends DbObject {
	@DbField(name = "max_hosts")
	private long maxHosts;
	@DbField(name = "hosts_used")
	private long hostsUsed;
	@DbField(name = "max_scans")
	private long maxScans;
	@DbField(name = "scans_used")
	private long scansUsed;
	@DbField(name = "scans_left")
	private long scansLeft;
	@DbField(name = "webapps_used")
	private long webappsUsed;
	@DbField(name = "webapp_scans_left")
	private long webappScansLeft;
	@DbField(name = "webapp_scans_used")
	private long webappScansUsed;

	/**
	 * Creates ScanUsageInfo object with give String array
	 *
	 * @param keyInfo The String array containing the values from HAIB
	 * @return ScanUsageInfo object
	 */
	protected static ScanUsageInfo getScanUsageInfo(final String[] keyInfo) {
		final ScanUsageInfo scanInfo = new ScanUsageInfo();
		if (keyInfo != null && keyInfo.length >= 11) {
			// max_hosts|hosts_used|max_scans|scans_used|scans_left|webapps_used|webapp_scans_left|webapp_scans_used|
			scanInfo.setMaxHosts(StringUtils.getLongValue(keyInfo[3]));
			scanInfo.setHostsUsed(StringUtils.getLongValue(keyInfo[4]));
			scanInfo.setMaxScans(StringUtils.getLongValue(keyInfo[5]));
			scanInfo.setScansUsed(StringUtils.getLongValue(keyInfo[6]));
			scanInfo.setScansLeft(StringUtils.getLongValue(keyInfo[7]));
			scanInfo.setWebappsUsed(StringUtils.getLongValue(keyInfo[8]));
			scanInfo.setWebappScansLeft(StringUtils.getLongValue(keyInfo[9]));
			scanInfo.setWebappScansUsed(StringUtils.getLongValue(keyInfo[10]));
		}
		return scanInfo;
	}
}
