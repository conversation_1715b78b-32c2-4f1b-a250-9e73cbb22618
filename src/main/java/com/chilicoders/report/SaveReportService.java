package com.chilicoders.report;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;

import org.json.JSONException;

import com.chilicoders.core.customer.api.CustomerInterface;
import com.chilicoders.core.scandata.api.model.ScanStatusInterface;
import com.chilicoders.event.model.Event;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.integrations.appcheck.model.AppcheckScanDataInterface;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.ReportDataList;
import com.fasterxml.jackson.core.JsonProcessingException;

public interface SaveReportService {
	void saveScaleReport(final ScanStatusInterface scan, final ReportDataList report, final List<Event> eventList)
			throws IOException, XMLStreamException, SQLException, ParamValidationException, JAXBException;

	void saveDockerReport(final ScanStatusInterface scan, final ReportDataList report, final List<Event> eventList)
			throws IOException, XMLStreamException, SQLException, JAXBException;

	void saveCloudsecReport(final ScanStatusInterface scan, final ReportDataList report, final List<Event> eventList)
			throws IOException, SQLException, JAXBException;

	void saveNetworkScanReport(final ScanStatusInterface scan, final ReportDataList report, final List<Event> eventList)
			throws IOException, XMLStreamException, SQLException, JAXBException;

	void saveNetworkDiscoveryReport(final ScanStatusInterface scan, final String report, final List<Event> eventList)
			throws JAXBException, SQLException, IOException;

	void saveCloudDiscoveryReport(final ScanStatusInterface scan, final String report, final List<Event> eventList)
			throws JAXBException, SQLException, IOException;

	void saveDockerDiscoveryReport(final ScanStatusInterface scan, final String report, final List<Event> eventList)
			throws JAXBException, SQLException, IOException;

	/**
	 * Iterates the AppCheckScanResult and maps AppCheckVulnerabilities by its host.
	 * Creates Assets Matches and Findings for each host's vulnerabilities.
	 *
	 * @param scanData {@link AppcheckScanDataInterface} The scan data from appcheckscandata table
	 * @param events A {@link List} of {@link Event}s to be published
	 * @throws JsonProcessingException in case of error
	 * @throws SQLException in case of error
	 */
	void saveAppcheckScan(final AppcheckScanDataInterface scanData, final List<Event> events) throws SQLException, JsonProcessingException;

	Integer[] migrateNetworkScanReport(final CustomerInterface customer, final ReportDataList report, final AssetInterface asset)
			throws SQLException, JSONException, IOException, XMLStreamException;

	void addBaseFindings(ReportDataList reportList, ScanStatusInterface scan) throws SQLException;
}
