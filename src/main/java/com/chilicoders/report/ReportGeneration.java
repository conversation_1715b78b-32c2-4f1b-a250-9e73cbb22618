package com.chilicoders.report;

import java.sql.Connection;
import java.sql.SQLException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.reporting.api.model.ReportDownloadEntryInterface;
import com.chilicoders.core.reporting.api.model.ReportStatus;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;

public class ReportGeneration {
	private static final Logger LOG = LogManager.getLogger(ReportGeneration.class);
	private static final long REPORT_STATUS_CHECK_INTERVAL_MS = 1000L;
	private static final long REPORT_STATUS_CHECK_INTERVAL_TEST_MS = 20L;
	private static final long REPORT_GENERATION_ABORT_DURATION_MS = 1000L * 60L * 60L * 6;

	/**
	 * Gets the configurations for the filter descriptions.
	 *
	 * @param user The user
	 * @param reportEntryId Report entry Id
	 * @return Instance of {@link ReportDownloadEntryInterface}
	 */
	public static ReportDownloadEntryInterface waitForReportGeneration(final UserDetails user, final long reportEntryId) throws SQLException, InterruptedException {
		final long start = System.currentTimeMillis();
		ReportDownloadEntryInterface reportEntry;
		long interval = Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.test_mode) ? REPORT_STATUS_CHECK_INTERVAL_TEST_MS : REPORT_STATUS_CHECK_INTERVAL_MS;
		do {
			try (final Connection conn = DbAccess.getInstance().getConnection()) {
				reportEntry = ServiceProvider.getReportingService(conn).getReportDownloadEntry(user, reportEntryId);
				if (reportEntry == null) {
					LOG.info("No report entry found with id: " + reportEntryId);
					return null;
				}
				if (!Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.test_mode) && System.currentTimeMillis() - start > REPORT_STATUS_CHECK_INTERVAL_MS * 60) {
					interval = REPORT_STATUS_CHECK_INTERVAL_MS * 10;
				}
			}
			Thread.sleep(interval);
		}
		while (ReportStatus.ONGOING == reportEntry.getStatus() && (System.currentTimeMillis() - start) < REPORT_GENERATION_ABORT_DURATION_MS);

		if (ReportStatus.ONGOING == reportEntry.getStatus()) {
			LOG.error("Report generation took more than 6 hours, aborted. ID=" + reportEntryId);
		}
		else {
			LOG.info("Report generated. ID=" + reportEntryId);
		}
		return reportEntry;
	}

	/**
	 * Delete download entry record
	 *
	 * @param reportEntry report entry to be removed
	 */
	public static void deleteReportEntry(final ReportDownloadEntryInterface reportEntry) throws SQLException {
		if (reportEntry == null) {
			return;
		}
		XmlAble.executeUpdate("DELETE FROM downloadentries WHERE id=?", reportEntry.getId());
	}

	/**
	 * Close connection before generating report
	 *
	 * @param conn Database connection to be closed
	 */
	public static void closeConnectionBeforeGeneratingReport(final Connection conn) throws SQLException {
		LOG.debug("Close connection before generating report. Connection: " + conn);
		conn.close();
	}
}
