package com.chilicoders.report;

import java.util.Date;

import com.chilicoders.model.Protocol;
import com.chilicoders.model.ReportEntryTypes;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
@Getter
public class VulnerabilityInformation {
	private long reportId;
	private ReportEntryTypes type;
	private long ruleId;
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Date lastSeenDate;
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Date firstSeenDate;
	private boolean previouslyDetected;
	private Protocol protocol;
}
