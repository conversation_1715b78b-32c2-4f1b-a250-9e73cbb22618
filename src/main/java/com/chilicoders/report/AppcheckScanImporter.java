package com.chilicoders.report;

import java.sql.SQLException;
import java.util.List;

import com.chilicoders.core.scandata.api.model.ScanResultInterface;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.event.model.Event;
import com.chilicoders.integrations.appcheck.model.AppcheckScanDataInterface;
import com.fasterxml.jackson.core.JsonProcessingException;

import co.elastic.apm.api.ElasticApm;

public class AppcheckScanImporter {
	/**
	 * Converts an AppCheck scan result to Assets, Matches and Findings
	 *
	 * @param scanResult {@link AppcheckScanDataInterface} the scan result
	 * @param statementExecutor The {@link TransactionalNativeStatementExecutor}
	 * @param saveReportService The {@link SaveReportService}
	 * @param events A {@link List} of {@link Event}s to be published
	 * @throws JsonProcessingException In case of error
	 * @throws SQLException In case of error
	 */
	public static void saveScan(final ScanResultInterface scanResult, final TransactionalNativeStatementExecutor statementExecutor, final SaveReportService saveReportService,
								final List<Event> events)
			throws JsonProcessingException, SQLException {

		final AppcheckScanDataInterface appcheckScanResult = (AppcheckScanDataInterface) scanResult;
		ElasticApm.currentTransaction().setLabel("AppCheckScan", appcheckScanResult.getScanId());
		ElasticApm.currentTransaction().setLabel("AppCheckRun", appcheckScanResult.getRunId());
		saveReportService.saveAppcheckScan(appcheckScanResult, events);
		statementExecutor.execute(new NativeSqlStatement("UPDATE appcheckscandata SET scanresult = NULL WHERE id = ?", scanResult.getId()));
	}
}
