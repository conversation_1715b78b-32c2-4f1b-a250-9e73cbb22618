package com.chilicoders.report.objects;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.DbTable;
import com.chilicoders.api.reportexport.model.FindingCountInfoInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@DbTable(name = "dummy")
public class FindingCountInfo extends DbObject implements FindingCountInfoInterface {
	@DbField
	private long ports = 0;

	@DbField
	private long lowrisk;

	@DbField
	private long mediumrisk;

	@DbField
	private long highrisk;

	@DbField
	private long lowaccepted;

	@DbField
	private long mediumaccepted;

	@DbField
	private long highaccepted;

	@DbField
	private long cvssSum;

	@DbField
	private long lowFp;

	@DbField
	private long mediumFp;

	@DbField
	private long highFp;

	@DbField
	private long information;

	@DbField
	private boolean notReachable;

	@DbField
	private String key;

	@DbField
	private long newFindings;

	@DbField
	private long unchanged;

	@DbField
	private Set<String> owasp = new HashSet<>();

	@DbField
	private Set<String> informationalOwasp = new HashSet<>();

	private long risks = 0;
	private long[] findings = new long[] {0, 0, 0, 0, 0};
	private long[] acceptedFindings = new long[] {0, 0, 0, 0, 0};
	private long[] fpFindings = new long[] {0, 0, 0, 0, 0};
	private long[] delta = new long[] {0, 0, 0, 0, 0, 0};
	private int[] ageFindings = new int[5];
	private int[] remediation = new int[5];
	private HashSet<String> targetList = new HashSet<>();
	private HashSet<String> reports = new HashSet<>();
	private long count = 0;

	@Override
	protected void postCreate(final Connection conn) throws SQLException, InstantiationException {
		risks = lowrisk + mediumrisk + highrisk;
		acceptedFindings = new long[] {0, lowaccepted, mediumaccepted, 0, highaccepted};
		fpFindings = new long[] {0, lowFp, mediumFp, 0, highFp};
		findings = new long[] {information, lowrisk, mediumrisk, 0, highrisk};
		delta = new long[] {newFindings, 0, unchanged, 0, 0, 0};
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public long[] getAcceptedFindings() {
		return acceptedFindings;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public long[] getFindings() {
		return findings;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public long[] getFpFindings() {
		return fpFindings;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public int[] getAgeFindings() {
		return ageFindings;
	}

	public long getCount() {
		return count;
	}

	public void setCount(final long count) {
		this.count = count;
	}

	public long getCvssSum() {
		return cvssSum;
	}

	public void setCvssSum(final long cvssSum) {
		this.cvssSum = cvssSum;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public long[] getDelta() {
		return delta;
	}

	public long getPorts() {
		return ports;
	}

	public void setPorts(final long ports) {
		this.ports = ports;
	}

	public String getKey() {
		return key;
	}

	public boolean isNotReachable() {
		return notReachable;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Set<String> getOwasp() {
		return owasp;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Set<String> getInformationalOwasp() {
		return informationalOwasp;
	}

	public long getRisks() {
		return risks;
	}

	public void setRisks(final long risks) {
		this.risks = risks;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public int[] getRemediation() {
		return remediation;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public HashSet<String> getReports() {
		return reports;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public HashSet<String> getTargetList() {
		return targetList;
	}
}
