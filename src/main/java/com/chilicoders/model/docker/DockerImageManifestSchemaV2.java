package com.chilicoders.model.docker;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Message received via http. It represents docker image manifest.
 */
@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Setter
@NoArgsConstructor
public class DockerImageManifestSchemaV2 {
	private Layer[] layers;

	@SuppressFBWarnings("UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD")
	@Getter
	@NoArgsConstructor
	public static class Layer {
		public Long size;
	}

	/**
	 * Creates {@link DockerImageInfo}.
	 *
	 * @param name the name of image.
	 * @param tag the tag of image.
	 * @return {@link DockerImageInfo} object.
	 */
	public DockerImageInfo createImageInfo(final String name, final String tag) {
		final DockerImageInfo imageInfo = new DockerImageInfo();
		imageInfo.setName(name);
		imageInfo.setTag(tag);
		imageInfo.setSize(totalSize());
		return imageInfo;
	}

	/**
	 * Iterate on layers and calculate total size.
	 *
	 * @return the total size of the image.
	 */
	private Long totalSize() {
		Long size = 0L;
		if (layers != null) {
			for (final Layer layer : layers) {
				size += layer.getSize();
			}
		}
		return size;
	}
}