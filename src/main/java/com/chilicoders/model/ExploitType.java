package com.chilicoders.model;

import javax.xml.bind.annotation.XmlEnumValue;

public enum ExploitType {
	@XmlEnumValue("Unknown")
	UNKNOWN,
	@XmlEnumValue("Core Security")
	CORE,
	@XmlEnumValue("Immunity")
	IMMUNITY,
	@XmlEnumValue("Exploit Database")
	EXPLOIT_DB,
	@XmlEnumValue("DSquare Security")
	D_SQUARE,
	@XmlEnumValue("Contagio")
	CONTAGIO,
	@XmlEnumValue("Metasploit")
	METASPLOIT,
	SAINT,
	@XmlEnumValue("Security Focus")
	SECURITY_FOCUS,
	@XmlEnumValue("Snort")
	SNORT,
	@XmlEnumValue("Farsight")
	FARSIGHT;
}
