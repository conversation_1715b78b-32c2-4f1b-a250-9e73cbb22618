package com.chilicoders.model.converter;

import javax.persistence.AttributeConverter;

import com.chilicoders.model.Protocol;

/**
 * Converts to/from database between Protocol and integer.
 */
public class ProtocolConverter implements AttributeConverter<Protocol, Integer> {

	@Override
	public Integer convertToDatabaseColumn(final Protocol attribute) {
		if (attribute != null) {
			return attribute.value;
		}
		return null;
	}

	@Override
	public Protocol convertToEntityAttribute(final Integer dbData) {
		if (dbData != null) {
			return Protocol.fromValue(dbData);
		}
		return null;
	}

}
