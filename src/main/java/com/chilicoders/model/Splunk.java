package com.chilicoders.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import com.chilicoders.integrations.SplunkInterface;

import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "splunks")
public class Splunk implements SplunkInterface {
	@Id
	@NotNull
	@Column(name = "xid")
	private Long id;

	private String splunkHost;

	private Integer splunkPort;

	private String splunkUser;

	private String splunkPassword;

	private Boolean splunkAudit;

	private boolean splunkEnabled;

	private String splunkIndex;

	@Enumerated(EnumType.ORDINAL)
	private SplunkMode splunkMode;

	private String splunkToken;

	public boolean isSplunkAudit() {
		return splunkAudit != null ? splunkAudit : false;
	}
}
