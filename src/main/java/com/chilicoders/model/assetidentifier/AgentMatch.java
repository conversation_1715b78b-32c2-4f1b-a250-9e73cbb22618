package com.chilicoders.model.assetidentifier;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Data;

/**
 * An Agent Match that will be sync from teddy-salad
 * E.g. "matches":[{"data":{"from":{"identifier":"agentuuid","relation":"CONTAINS","type":"AGENT"},"identifier":"00:00:00:ff:ff:ff","type":"MAC"},"type":"ASSET_IDENTIFIER"}]
 * Epic: DEV-8483
 */
@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Data
public class AgentMatch {
	private IdentifierData data;
	private String type;
}
