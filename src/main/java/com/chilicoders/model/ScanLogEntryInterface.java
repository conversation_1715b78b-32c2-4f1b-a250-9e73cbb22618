package com.chilicoders.model;

import java.time.Instant;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Represents data in the new scanlog table.
 */
public interface ScanLogEntryInterface {
	void setStatus(ScanLogStatus status);

	ScanLogStatus getStatus();

	void setStatusDetails(String statusDetails);

	String getStatusDetails();

	Integer getId();

	void setCustomerId(final Integer customerId);

	Integer getCustomerId();

	void setAssetIdentifierId(Integer assetIdentifierId);

	Integer getAssetIdentifierId();

	void setScanConfigurationId(Integer scanConfigurationId);

	Integer getScanConfigurationId();

	void setWorkflowId(Integer workflowId);

	Integer getWorkflowId();

	void setAssetId(Integer assetId);

	Integer getAssetId();

	void setSchema(String schema);

	String getSchema();

	String getAttacker();

	void setScanless(boolean scanless);

	boolean isScanless();

	void setParentId(Integer parentId);

	Integer getParentId();

	void setTemplate(ScanTemplate template);

	ScanTemplate getTemplate();

	void setLatestRuleDate(Instant now);

	void setStarted(Instant toInstant);

	Instant getStarted();

	void setEnded(Instant toInstant);

	Instant getEnded();

	void setJobId(String jobId);

	String getJobId();

	void setScheduleId(Integer scheduleId);

	Integer getScheduleId();

	void setScannerId(Integer scannerId);

	Integer getScannerId();

	void setCreatedById(Integer createdById);

	Integer getCreatedById();

	void setExpectedStart(Instant expectedStart);

	Instant getExpectedStart();

	void setExpectedEnd(Instant expectedEnd);

	Instant getExpectedEnd();

	void setAuthentication(Authentication[] authentication) throws JAXBException;

	Authentication[] getAuthentication() throws JAXBException;

	String getAuthenticationString();

	@Getter
	@Setter
	@Schema(name = "ScanLog.Authentication")
	@XmlAccessorType(XmlAccessType.FIELD)
	public static class Authentication {
		@NotNull
		private AuthenticationType type;

		@XmlElement
		private AuthenticationStatus status;
	}

	void setTargets(List<String> targets);

	List<String> getTargets();

	void setVirtualHosts(List<String> virtualHosts);

	List<String> getVirtualHosts();

	void setInvocationId(Integer invocationId);

	Integer getInvocationId();
}
