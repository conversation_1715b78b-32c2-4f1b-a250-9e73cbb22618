package com.chilicoders.model;

import java.util.Arrays;
import java.util.Set;

import com.google.common.collect.ImmutableSet;

public enum Source {
	SCOUT,
	SWAT,
	SCALE,
	SCALE_API,
	SCALE_SPA,
	CLOUDSEC,
	NETSEC,
	SNAPSHOT,
	ASSURE,
	APPSEC,
	OFFSEC;

	private static final Set<Source> SWAT_SOURCES = ImmutableSet.of(APPSEC, ASSURE, SNAPSHOT, SWAT, OFFSEC);

	public static Set<Source> getSwatSources() {
		return ImmutableSet.copyOf(SWAT_SOURCES);
	}

	public static boolean containSwatSources(final Source... source) {
		return (source != null && Arrays.stream(source).anyMatch(SWAT_SOURCES::contains));
	}

	/**
	 * Creates a {@link Source} from given {@link String}
	 *
	 * @param name The {@link String} to parse
	 * @return The source that corresponds to the given {@link String}. {@code null} if no entry matches the given {@link String}
	 */
	public static Source fromString(final String name) {
		try {
			return Source.valueOf(name);
		}
		catch (final IllegalArgumentException exception) {
			return null;
		}
	}
}
