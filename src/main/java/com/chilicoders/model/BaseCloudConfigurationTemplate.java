package com.chilicoders.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorNode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;

@Getter
@Setter
@XmlDiscriminatorNode(value = "cloudDiscoveryType")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlSeeAlso({CloudDiscoveryAwsConfigurationTemplate.class, CloudDiscoveryAzureConfigurationTemplate.class})
public class BaseCloudConfigurationTemplate {
	private CloudDiscoveryType cloudDiscoveryType;
}
