package com.chilicoders.model;

public enum ErrorCode {
	NotLoggedIn(100),
	AccessDenied(101),
	IncorrectLogin(102),
	NoRecordsRemoved(103),
	FieldsMissing(104),
	InvalidCountryCode(107),
	InvalidMobileNumber(108),
	UsernameTooShort(109),
	UsernameAlreadyExist(110),
	PasswordTooShort(111),
	AccountLocked(112),
	InputValidationFailed(118),
	InvalidEmail(120),
	ImportingData(123),
	LoggedOutInactivity(124),
	PendingRequests(125),
	LdapFailed(126),
	RequestTimeout(127),
	WrongVersion(130),
	Unsupported(131),
	ConnectionFailed(132),
	TooManyTargets(133),
	BasicAuthenticationUnsupported(134),
	AutomaticLoginFailed(996),
	InternalServerError(500),
	TemporarilyUnavailable(503),
	EnrollingFailed(997),
	ServerNotRegistered(999),
	Unauthorized(401),
	Forbidden(403),
	UnprocessableContent(422);

	public final int code;

	private ErrorCode(final int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	/**
	 * Get ErroCode from id.
	 *
	 * @param value Id value
	 * @return ErrorCode
	 */
	public static ErrorCode get(final int value) {
		for (final ErrorCode e : ErrorCode.values()) {
			if (e.getCode() == value) {
				return e;
			}
		}
		return null;
	}
}
