package com.chilicoders.model;

import javax.xml.bind.JAXBException;

import com.chilicoders.core.scanpolicy.model.ScanPolicySettings;

public interface ScanPolicyInterface {
	Integer getId();

	Integer getCustomerId();

	String getName();

	boolean isSystem();

	Integer[] getAccountIds();

	ScanPolicySettings getSettings() throws JAXBException;

	void setSettings(final ScanPolicySettings scanPolicySettings) throws JAXBException;
}
