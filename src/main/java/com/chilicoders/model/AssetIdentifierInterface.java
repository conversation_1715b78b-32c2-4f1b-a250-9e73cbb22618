package com.chilicoders.model;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorNode;
import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorValue;

import com.chilicoders.discover.Tag;
import com.chilicoders.model.assetidentifier.AgentMatch;
import com.chilicoders.rest.adapters.InstantXmlAdapter;
import com.chilicoders.rest.adapters.MapXmlAdapter;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

public interface AssetIdentifierInterface extends Serializable {
	Integer getId();

	String getName();

	String getPresentableName();

	AssetIdentifierType getType();

	Integer[] getLinks();

	Integer getScannerId();

	Instant getLastSeen();

	Source[] getSource();

	Integer getFirstScanId();

	Integer getLastScanId();

	Integer[] getAccountIds();

	Integer[] getAssetIds();

	String getPlatform();

	void setName(String name);

	void setType(AssetIdentifierType type);

	void setScannerId(Integer scannerId);

	void setFirstSeen(final Instant time);

	void setLastSeen(final Instant time);

	void setFirstScanId(final Integer scanLogId);

	void setLastScanId(final Integer scanLogId);

	void addSource(final Source source);

	void setId(Integer ipIdentifierId);

	void setUpdatedById(Integer systemUser);

	void setDeleted(Integer systemUser);

	void setPlatform(String platform);

	void setLinks(Integer[] integers);

	void setPresentableName(String presentableName);

	@Setter
	@Getter
	@XmlDiscriminatorNode(value = "type")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlSeeAlso({DockerImageProperties.class, AgentProperties.class})
	@Schema(description = "The asset base properties")
	public static class BaseProperties {
		@NotNull
		private AssetIdentifierType type;
	}

	@Setter
	@Getter
	@Schema(name = "AssetIdentifier.DockerImageProperties", description = "The docker image properties")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlDiscriminatorValue(value = "DOCKER_IMAGE")
	public static class DockerImageProperties extends BaseProperties {
		private String os;
		private String tag;
		private Long size;
		private String architecture;
	}

	@Setter
	@Getter
	@Schema(name = "AssetIdentifier.AgentProperties")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlDiscriminatorValue(value = "AGENT")
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	class AgentProperties extends BaseProperties {
		private String uuid;
		private String version;
		@XmlJavaTypeAdapter(InstantXmlAdapter.class)
		private Instant lastSynchronized;
		@XmlJavaTypeAdapter(MapXmlAdapter.class)
		private Map<String, String> customAttributes;
		private List<Tag> tags = new ArrayList<>();
		@XmlJavaTypeAdapter(InstantXmlAdapter.class)
		private Instant retired;
		private List<AgentMatch> matches = new ArrayList<>();

		public static final String MAC_ADDRESS_ATTRIBUTE_KEY = "mac";
	}

	BaseProperties getProperties() throws JAXBException;

	void setProperties(String propertiesJson);
}
