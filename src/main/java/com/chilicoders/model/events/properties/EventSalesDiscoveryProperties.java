package com.chilicoders.model.events.properties;

import java.util.Map;

import com.chilicoders.model.Event.O24Event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class EventSalesDiscoveryProperties extends EventProperties {
	private static final long serialVersionUID = -6713567639558029886L;

	private String alive;
	private String dead;
	private String tested;
	private String added;

	private String firstName;
	private String lastName;
	private String company;

	@Override
	protected void addEventProperties(final Map<String, String> properties, final O24Event event) {
		properties.put("ALIVE_LIST", alive);
		properties.put("DEAD_LIST", dead);
		properties.put("ADDED_LIST", added);
		properties.put("TESTED_LIST", tested);

		properties.put("CUSTOMERFIRSTNAME", firstName);
		properties.put("CUSTOMERLASTNAME", lastName);
		properties.put("CUSTOMERCOMPANY", company);
	}
}
