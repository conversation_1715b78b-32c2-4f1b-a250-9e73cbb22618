package com.chilicoders.model.events.properties;

import java.util.Map;

import com.chilicoders.model.Event.O24Event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class EventWasScanStartedProperties extends EventProperties {
	private static final long serialVersionUID = 6293913299490232562L;

	private Long subuserId;
	private String scheduleName;

	@Override
	protected void addEventProperties(final Map<String, String> properties, final O24Event event) {
		properties.put("SUBUSERXID", "" + (subuserId == null ? -1 : subuserId));
		properties.put("SCHEDULEJOB", scheduleName);
	}
}
