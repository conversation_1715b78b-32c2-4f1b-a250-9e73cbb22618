package com.chilicoders.model.events.properties;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.model.Event.O24Event;
import com.fasterxml.jackson.annotation.JsonIgnore;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@SuppressFBWarnings({"EI_EXPOSE_REP2", "NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public final class EventScheduleStartedProperties extends EventAttributeProperties {
	private static final long serialVersionUID = 2438909075732959031L;

	private Long subuserId;
	private String scheduleName;
	@SuppressFBWarnings("EI_EXPOSE_REP")
	@NonNull
	private List<TargetInfo> hostList;

	@Override
	protected void addEventProperties(final Map<String, String> properties, final O24Event event) {
		if (subuserId != null && subuserId != 0) {
			properties.put("SUBUSERXID", "" + subuserId);
		}
		properties.put("SCHEDULEJOB", scheduleName);
		for (int i = 0; i < customAttributes.size(); i++) {
			properties.put("CUSTOM" + i, customAttributes.get(i));
		}
	}

	@JsonIgnore
	@Override
	public Map<String, Map<String, Map<String, String>>> getLists() {
		final Map<String, Map<String, Map<String, String>>> result = new HashMap<>();
		final Map<String, Map<String, String>> targetMap = new HashMap<>();
		result.put("HOSTINFO", targetMap);
		if (hostList != null) {
			for (final TargetInfo targetInfo : hostList) {
				final Map<String, String> target = new HashMap<>();
				target.put("HOST", targetInfo.target);
				if (targetInfo.ipAddress != null) {
					target.put("HOSTIP", targetInfo.ipAddress);
				}
				targetMap.put("" + targetInfo.targetId, target);
			}
		}
		return result;
	}

	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Getter
	public static class TargetInfo implements Serializable {
		private static final long serialVersionUID = 5270973104872292307L;

		protected String ipAddress;
		protected String target;
		protected long targetId;
	}

	@Override
	public void getTargetInfo(final List<String> ipAddress, final List<Long> targetIds, final List<String> hostNames, final List<Long> hostIds,
							  final IpService ipService) throws SQLException {
		for (final TargetInfo target : hostList) {
			targetIds.add(target.targetId);
		}
	}
}
