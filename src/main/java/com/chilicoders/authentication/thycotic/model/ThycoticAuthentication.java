package com.chilicoders.authentication.thycotic.model;

import java.util.Objects;

import com.chilicoders.core.authentication.thycotic.model.api.ThycoticAuthenticationInterface;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DbTable(name = "thycoticauth")
public class ThycoticAuthentication extends XmlAble implements ThycoticAuthenticationInterface {

	@DbField(id = true, autoIncrementedId = true, dontSave = true)
	private long id;

	@DbField
	private long userId;

	@DbField
	private boolean ssh;

	@DbField
	private boolean smb;

	@DbField
	private long scanPolicyId;

	@DbField
	private long credentialProviderId;

	@DbField
	private String path;

	@DbField
	private String secretName;

	@Override
	public boolean equals(final Object object) {
		if (this == object) {
			return true;
		}
		if (object == null || getClass() != object.getClass()) {
			return false;
		}

		final ThycoticAuthentication that = (ThycoticAuthentication) object;
		return Objects.equals(this.id, that.id)
				&& Objects.equals(this.userId, that.userId)
				&& Objects.equals(this.ssh, that.ssh)
				&& Objects.equals(this.smb, that.smb)
				&& Objects.equals(this.scanPolicyId, that.scanPolicyId)
				&& Objects.equals(this.credentialProviderId, that.credentialProviderId)
				&& Objects.equals(this.path, that.path)
				&& Objects.equals(this.secretName, that.secretName);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, userId, ssh, smb, scanPolicyId, credentialProviderId, path, secretName);
	}
}
