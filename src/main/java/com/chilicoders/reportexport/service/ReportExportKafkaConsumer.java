package com.chilicoders.reportexport.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import com.chilicoders.api.reportexport.ReportExportRequest;
import com.chilicoders.api.reportexport.ReportExportService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
@ConditionalOnProperty(value = "kafka.enabled", havingValue = "true")
public class ReportExportKafkaConsumer {
	private static final Logger LOG = LogManager.getLogger(ReportExportKafkaConsumer.class);

	@Autowired
	private ReportExportService service;

	@Autowired
	private ObjectMapper objectMapper;

	/**
	 * Kafka listener that forwards messages to the eventhandler.
	 *
	 * @param message Message in json format.
	 */
	@KafkaListener(topics = {"${kafka.consumer.vm.topic.reportexport}"}, groupId = "${kafka.consumer.vm.groupid}", containerFactory = "reportKafkaListenerContainerFactory")
	public void consume(final String message) {
		try {
			if (message != null) {
				final ReportExportRequest reportExportRequest = objectMapper.readValue(message, ReportExportRequest.class);
				service.exportReport(reportExportRequest);
			}
		}
		catch (final JsonProcessingException e) {
			LOG.error("Error parsing data: " + message.substring(0, Math.min(100, message.length())), e);
		}
	}
}