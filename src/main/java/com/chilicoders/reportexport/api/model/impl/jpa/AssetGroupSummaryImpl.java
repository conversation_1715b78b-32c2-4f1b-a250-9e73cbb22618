package com.chilicoders.reportexport.api.model.impl.jpa;

import com.chilicoders.db.IntegerArrayType;
import com.chilicoders.db.objects.api.AssetGroupSummaryInterface;

import lombok.Getter;

import javax.persistence.Column;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

@Getter
@TypeDefs({
		@TypeDef(name = "integer-array", typeClass = IntegerArrayType.class)
})
public class AssetGroupSummaryImpl implements AssetGroupSummaryInterface {

	private static final long serialVersionUID = -2178221398213850548L;

	@Column
	private Integer assetGroupId;

	@Column
	private String assetGroupName;

	@Column
	private Integer findings;

	@Column
	private Integer cvssV2High;

	@Column
	private Integer cvssV2Medium;

	@Column
	private Integer cvssV2Low;

	@Column
	private Integer cvssV2Recommendation;

	@Column
	private Integer cvssV2NotClassified;

	@Column
	private Float cvssV2Average;

	@Column
	private Integer cvssV3Critical;

	@Column
	private Integer cvssV3High;

	@Column
	private Integer cvssV3Medium;

	@Column
	private Integer cvssV3Low;

	@Column
	private Integer cvssV3Recommendation;

	@Column
	private Integer cvssV3NotClassified;

	@Column
	private Float cvssV3Average;

	@Column
	private Integer acceptedCritical;

	@Column
	private Integer acceptedHigh;

	@Column
	private Integer acceptedMedium;

	@Column
	private Integer acceptedLow;

	@Column
	private Integer acceptedRecommendation;

	@Column
	private Integer acceptedNotClassified;

	@Column
	@Type(type = "integer-array")
	private Integer[] owasp;

	@Column
	@Type(type = "integer-array")
	private Integer[] owaspApi;

	@Column
	@Type(type = "integer-array")
	private Integer[] owaspMobile;

	@Column
	private Integer cvssCritical;

	@Column
	private Integer cvssHigh;

	@Column
	private Integer cvssMedium;

	@Column
	private Integer cvssLow;

	@Column
	private Integer cvssRecommendation;

	public void setCvssV2Average(final Number cvssV2Average) {
		this.cvssV2Average = cvssV2Average == null ? null : cvssV2Average.floatValue();
	}

	public void setCvssV3Average(final Number cvssV3Average) {
		this.cvssV3Average = cvssV3Average == null ? null : cvssV3Average.floatValue();
	}

	public Set<Integer> getOwasp() {
		return this.owasp == null ? null : new HashSet<>(Arrays.asList(this.owasp));
	}

	@Override
	public Set<Integer> getOwaspApi() {
		return this.owaspApi == null ? null : new HashSet<>(Arrays.asList(this.owaspApi));
	}

	@Override
	public Set<Integer> getOwaspMobile() {
		return this.owaspMobile == null ? null : new HashSet<>(Arrays.asList(this.owaspMobile));
	}
}
