package com.chilicoders.reportexport.api.model.impl.compliance;

import java.util.List;
import java.util.Map;

import com.chilicoders.core.compliance.api.model.RequirementBaseInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
public class ComplianceSettings {

	private List<? extends RequirementBaseInterface> auditPolicyRequirements;
	private List<? extends RequirementBaseInterface> checkAccountRequirements;
	private List<? extends RequirementBaseInterface> checkConfigRequirements;
	private List<? extends RequirementBaseInterface> cmdExecuteRequirements;
	private List<? extends RequirementBaseInterface> dbQueryRequirements;
	private List<? extends RequirementBaseInterface> fileCheckRequirements;
	private List<? extends RequirementBaseInterface> fileContentCheckRequirements;
	private List<? extends RequirementBaseInterface> installedApplicationRequirements;
	private List<? extends RequirementBaseInterface> patchLevelRequirements;
	private List<? extends RequirementBaseInterface> userRightConstrainsRequirements;
	private List<? extends RequirementBaseInterface> vulnerabilityRequirements;
	private List<? extends RequirementBaseInterface> wmiQueryRequirements;
	private Map<PasswordPolicy, String> passwordPolicyConfig;
	private String ports;
	private String portsPolicy;
	private List<RegistryKey> registryKeys;
	private List<String> windowsServiceNames;
	private String windowsServicesPolicy;
	private String maxVulnearbilityScore;
	private String excludeAcceptedVulnearbilities;
	private String ignoreVulnerabilityAvailability;

}
