package com.chilicoders.reportexport.api.model.impl.jpa;

import javax.persistence.Column;

import com.chilicoders.db.objects.api.AssetFindingDeltaInterface;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AssetFindingDeltaImpl implements AssetFindingDeltaInterface {
	@Column
	private Integer id;

	@Column
	private Integer assetId;

	@Column
	private String assetName;

	@Column
	private Integer firstSeen;

	@Column
	private Integer lastSeen;

	@Column
	private Integer unchanged;
}
