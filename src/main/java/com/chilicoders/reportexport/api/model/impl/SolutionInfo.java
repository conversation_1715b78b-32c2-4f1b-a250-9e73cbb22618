package com.chilicoders.reportexport.api.model.impl;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class SolutionInfo implements Comparable<SolutionInfo> {
	private String solutionId;
	private int solutionType;
	private String solutionProduct;
	private String solution;
	private String solutionTitle;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private int[] solutionRisks = new int[] {0, 0, 0};
	private long vulnId;

	/**
	 * Create new solution info object.
	 *
	 * @param solutionId Solution id
	 * @param solution Solution text
	 * @param solutionProduct Solution product
	 * @param solutionType Solution type
	 * @param solutionTitle Solution title
	 * @param vulnId Vuln id
	 */
	public SolutionInfo(final String solutionId, final String solution, final String solutionProduct, final int solutionType, final String solutionTitle, final long vulnId) {
		this.solutionId = solutionId;
		this.solutionType = solutionType;
		this.solutionProduct = solutionProduct;
		this.solution = solution;
		this.solutionTitle = solutionTitle;
		this.vulnId = vulnId;
	}

	@Override
	public int compareTo(final SolutionInfo info) {
		if ((info.solutionRisks[0] + info.solutionRisks[1] + info.solutionRisks[2]) < (this.solutionRisks[0] + this.solutionRisks[1] + this.solutionRisks[2])) {
			return -1;
		}
		else if ((info.solutionRisks[0] + info.solutionRisks[1] + info.solutionRisks[2]) > (this.solutionRisks[0] + this.solutionRisks[1] + this.solutionRisks[2])) {
			return 1;
		}
		return 0;
	}

	@Override
	public boolean equals(final Object obj) {
		return super.equals(obj);
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	public String getSolutionId() {
		return solutionId;
	}

	public int getSolutionType() {
		return solutionType;
	}

	public String getSolutionProduct() {
		return solutionProduct;
	}

	public String getSolution() {
		return solution;
	}

	public String getSolutionTitle() {
		return solutionTitle;
	}

	public int[] getSolutionRisks() {
		return solutionRisks;
	}

	public long getVulnId() {
		return vulnId;
	}
}
