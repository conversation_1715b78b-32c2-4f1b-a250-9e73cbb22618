package com.chilicoders.reportexport.api.model.impl.jpa;

import java.time.Instant;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;

import org.apache.commons.lang3.ArrayUtils;
import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.db.objects.api.AssetGroupInterface;
import com.chilicoders.model.AssetGroupType;
import com.chilicoders.model.SubscriptionType;
import com.vladmihalcea.hibernate.type.array.EnumArrayType;
import com.vladmihalcea.hibernate.type.array.internal.AbstractArrayType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
@Entity(name = "assetgroups")
@TypeDefs({
		@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
		@TypeDef(name = "enum-array", typeClass = EnumArrayType.class),
		@TypeDef(name = "json", typeClass = JsonBinaryType.class)
})
public class AssetGroupImpl implements AssetGroupInterface {

	private static final long serialVersionUID = -5440636875923433063L;

	@Id
	@Column
	private Integer id;

	@Column
	private String name;

	@Column
	private Boolean managed;

	@Column
	private String summary;

	@Column
	private Instant summaryPublishedAt;

	@Column
	@Type(type = "enum-array", parameters = @Parameter(name = AbstractArrayType.SQL_ARRAY_TYPE, value = "subscriptiontype"))
	private SubscriptionType[] activeSubscriptionTypes;

	@Column
	@Enumerated(EnumType.STRING)
	@Type(type = "enum_type")
	private AssetGroupType type;

	@Column
	private boolean appSec;

	/**
	 * Determine whether an asset group is SWAT or not
	 *
	 * @return true if active subscription types include SWAT or is empty but sources contain SWAT, false otherwise.
	 */
	public boolean isSwat() {
		if (ArrayUtils.isEmpty(this.activeSubscriptionTypes)) {
			return managed;
		}
		return SubscriptionType.containsSwatSubscription(activeSubscriptionTypes);
	}

	@Override
	public String getPlatform() {
		return null;
	}
}