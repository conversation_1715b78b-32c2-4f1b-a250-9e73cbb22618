package com.chilicoders.reportexport.dao.impl;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.hibernate.type.StandardBasicTypes;

import com.chilicoders.api.reportexport.model.AssetSummaryInterface;
import com.chilicoders.api.reportexport.model.FindingCountDataInterface;
import com.chilicoders.api.reportexport.model.FindingCountInfoInterface;
import com.chilicoders.api.reportexport.model.PciComponentInterface;
import com.chilicoders.api.reportexport.model.PciDiscoveryTargetsInterface;
import com.chilicoders.api.reportexport.model.PortInfoDataInterface;
import com.chilicoders.api.reportexport.model.PortInfoInterface;
import com.chilicoders.api.reportexport.model.ReportHostInterface;
import com.chilicoders.api.reportexport.model.ReportHostSummaryInterface;
import com.chilicoders.api.reportexport.model.ReportTextInterface;
import com.chilicoders.api.reportexport.model.TopVulnsInfoInterface;
import com.chilicoders.api.reportexport.model.impl.FindingCountInfo;
import com.chilicoders.api.reportexport.model.impl.PortInfo;
import com.chilicoders.api.reportexport.model.impl.jpa.AssetSummaryImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.ComplianceFindingImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.DiscoveryImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.FindingCountDataImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.PciComponentImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.PciDiscoveryTargetsImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.PortInfoDataImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.ReportHostImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.ReportHostSummaryImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.ReportTextDataImpl;
import com.chilicoders.api.reportexport.model.impl.jpa.TopVulnsInfoImpl;
import com.chilicoders.core.assets.impl.AssetImpl;
import com.chilicoders.core.compliance.api.model.ComplianceAnswerInterface;
import com.chilicoders.core.compliance.api.model.ComplianceFindingInterface;
import com.chilicoders.core.compliance.api.model.CompliancePolicyInterface;
import com.chilicoders.core.compliance.api.model.ComplianceQuestionInterface;
import com.chilicoders.core.compliance.api.model.ComplianceRequirementInterface;
import com.chilicoders.core.compliance.impl.ComplianceRequirementRepository;
import com.chilicoders.core.customer.api.CustomerInterface;
import com.chilicoders.core.customer.impl.jpa.CustomerImpl;
import com.chilicoders.core.reporting.api.model.WasFinding;
import com.chilicoders.core.reporting.impl.jpa.SpecialNoteImpl;
import com.chilicoders.core.reporting.impl.jpa.WasFindingImpl;
import com.chilicoders.core.reporting.impl.jpa.WasUrlImpl;
import com.chilicoders.core.user.impl.jpa.SubUserDetails;
import com.chilicoders.db.objects.api.AssetFindingDeltaInterface;
import com.chilicoders.db.objects.api.AssetGroupFindingDeltaInterface;
import com.chilicoders.db.objects.api.AssetGroupInterface;
import com.chilicoders.db.objects.api.AssetGroupSummaryInterface;
import com.chilicoders.db.objects.api.AttachmentInterface;
import com.chilicoders.db.objects.api.CommentInterface;
import com.chilicoders.db.objects.api.DiscoveryInterface;
import com.chilicoders.db.objects.api.ExploitInterface;
import com.chilicoders.db.objects.api.ReportFileInterface;
import com.chilicoders.db.objects.api.ReportSetupInterface;
import com.chilicoders.db.objects.api.ResourceGroupInterface;
import com.chilicoders.db.objects.api.ScanLogInterface;
import com.chilicoders.db.objects.api.ScheduleSummaryInterface;
import com.chilicoders.db.objects.api.SolutionInterface;
import com.chilicoders.db.objects.api.SpecialNoteInterface;
import com.chilicoders.db.objects.api.SummarySolutionInterface;
import com.chilicoders.db.objects.api.SwatScheduleSummaryInterface;
import com.chilicoders.db.objects.api.VulnerabilityCommentInterface;
import com.chilicoders.db.objects.api.WasUrl;
import com.chilicoders.db.objects.api.XRefInterface;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalJpaNativeStatementExecutor;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.reportexport.api.ReportExportServiceProvider;
import com.chilicoders.reportexport.api.model.impl.compliance.ComplianceAnswer;
import com.chilicoders.reportexport.api.model.impl.jpa.AssetFindingDeltaImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.AssetGroupFindingDeltaImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.AssetGroupImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.AssetGroupSummaryImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.AttachmentImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.CommentImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.CompliancePolicyImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ComplianceQuestionImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ComplianceRequirementImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ExploitImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ReportFileImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ReportSetupImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ScanLogImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.ScheduleSummaryImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.SolutionImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.SummarySolutionImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.SwatScheduleSummaryImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.VulnerabilityCommentImpl;
import com.chilicoders.reportexport.api.model.impl.jpa.XRefImpl;
import com.chilicoders.reportexport.service.ReportInfo;

public class ReportExportDaoImpl extends AbstractReportExportDao {
	private final ComplianceRequirementRepository complianceRequirementRepository;

	/**
	 * Constructs an instance of the report export dao that use dblayer.
	 *
	 * @param reportInfo the report info object
	 * @param serviceProvider an object that provides the core services to the export classes
	 * @param statementExec a native statement executor
	 * @param complianceRequirementRepository Compliance requirement repository
	 */
	public ReportExportDaoImpl(final ReportInfo reportInfo, final ReportExportServiceProvider serviceProvider, final TransactionalNativeStatementExecutor statementExec,
							   final ComplianceRequirementRepository complianceRequirementRepository) {
		super(reportInfo, serviceProvider, statementExec);
		this.complianceRequirementRepository = complianceRequirementRepository;
	}

	private TransactionalJpaNativeStatementExecutor getStatementExec() {
		return (TransactionalJpaNativeStatementExecutor) statementExec;
	}

	@Override
	protected String getString(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().getString(statement);
	}

	@Override
	protected Set<String> getTargetNameSet(final NativeSqlStatement statement) throws SQLException {
		return new HashSet<>(getStatementExec().execute(statement, "name", StandardBasicTypes.STRING));
	}

	@Override
	protected List<ReportTextInterface> getReportTextDataList(final NativeSqlStatement statement) throws SQLException {
		return new ArrayList<>(getStatementExec().executeDTO(statement, ReportTextDataImpl.class));
	}

	@Override
	protected Set<Long> getScanlogIdsSet(final NativeSqlStatement statement) throws SQLException {
		return new HashSet<>(getStatementExec().execute(statement, "xid", StandardBasicTypes.LONG));
	}

	@Override
	public ReportHostInterface createReportHost() {
		return new ReportHostImpl();
	}

	@Override
	protected List<ReportHostSummaryInterface> getReportHostInfoList(final NativeSqlStatement statement) throws SQLException {
		return new ArrayList<>(getStatementExec().executeDTO(statement, ReportHostSummaryImpl.class, true));
	}

	@Override
	protected List<? extends ReportHostInterface> getReportHostDataList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ReportHostImpl.class, false);
	}

	@Override
	protected ScheduleSummaryInterface getScheduleObject(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().getSingleDtoResult(statement, ScheduleSummaryImpl.class);
	}

	@Override
	protected List<? extends ReportSetupInterface> getReportSetupList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ReportSetupImpl.class, false);
	}

	@Override
	protected List<? extends SpecialNoteInterface> getSpecialNoteList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, SpecialNoteImpl.class, false);
	}

	@Override
	protected List<? extends ComplianceFindingInterface> getComplianceFindingList(final NativeSqlStatement statement) throws SQLException {
		final List<ComplianceFindingImpl> result = getStatementExec().executeDTO(statement, ComplianceFindingImpl.class, false);
		result.forEach(finding -> {
			finding.setRequirement(complianceRequirementRepository.findById(finding.getVulnId()).orElse(null));
		});
		return result;
	}

	@Override
	protected List<? extends ComplianceQuestionInterface> getComplianceQuestionList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ComplianceQuestionImpl.class, false);
	}

	@Override
	protected List<? extends ComplianceRequirementInterface> getComplianceRequirementList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ComplianceRequirementImpl.class, false);
	}

	@Override
	protected CompliancePolicyInterface getCompliancePolicy(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().getSingleDtoResult(statement, CompliancePolicyImpl.class);
	}

	@Override
	protected List<? extends XRefInterface> getXrefList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, XRefImpl.class, false);
	}

	@Override
	public XRefInterface createXRef(final String type, final String ref) {
		return new XRefImpl(type, ref);
	}

	@Override
	protected List<? extends ReportFileInterface> getReportFileList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ReportFileImpl.class, false);
	}

	@Override
	protected List<? extends AttachmentInterface> getFindingAttachmentList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AttachmentImpl.class, false);
	}

	@Override
	protected List<? extends VulnerabilityCommentInterface> getVulnerabilityCommentList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, VulnerabilityCommentImpl.class, false);
	}

	@Override
	protected List<? extends SwatScheduleSummaryInterface> getSwatScheduleList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, SwatScheduleSummaryImpl.class, false);
	}

	@Override
	protected SwatScheduleSummaryInterface getSwatSchedule(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().getSingleDtoResult(statement, SwatScheduleSummaryImpl.class);
	}

	@Override
	protected List<? extends ExploitInterface> getExploitList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ExploitImpl.class, false);
	}

	@Override
	protected ScanLogInterface getScanLog(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().getSingleDtoResult(statement, ScanLogImpl.class);
	}

	@Override
	protected List<? extends ScanLogInterface> getScanLogList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, ScanLogImpl.class, false);
	}

	@Override
	protected List<? extends FindingCountInfoInterface> getFindingCountInfoList(final NativeSqlStatement statement) throws SQLException {
		final List<? extends FindingCountDataInterface> findingData = getStatementExec().executeDTO(statement, FindingCountDataImpl.class, true);
		final List<FindingCountInfoInterface> findingCounts = new ArrayList<>();
		for (final FindingCountDataInterface finding : findingData) {
			findingCounts.add(new FindingCountInfo(finding));
		}
		return findingCounts;
	}

	@Override
	public FindingCountInfoInterface createFindingCountInfo() {
		return new FindingCountInfo();
	}

	@Override
	protected List<? extends TopVulnsInfoInterface> getTopVulnerabilityInfoList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, TopVulnsInfoImpl.class, false);
	}

	@Override
	protected List<? extends PortInfoInterface> getPortInfoList(final NativeSqlStatement statement) throws SQLException {
		final List<? extends PortInfoDataInterface> portData = getStatementExec().executeDTO(statement, PortInfoDataImpl.class, false);
		final List<PortInfoInterface> portInfos = new ArrayList<>();
		for (final PortInfoDataInterface port : portData) {
			portInfos.add(new PortInfo(port));
		}
		return portInfos;
	}

	@Override
	public PortInfoInterface createPortInfo() {
		return new PortInfo();
	}

	@Override
	public boolean hasTickets(final long userId) throws SQLException {
		return getStatementExec().getLong(new NativeSqlStatement("SELECT COALESCE(MIN(xid), 0) FROM tworkflows WHERE xuserxid = ?", userId)) > 0;
	}

	@Override
	protected List<? extends AssetFindingDeltaInterface> getAssetFindingDelta(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetFindingDeltaImpl.class, false);
	}

	@Override
	protected List<? extends AssetGroupFindingDeltaInterface> getAssetGroupFindingDelta(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetGroupFindingDeltaImpl.class, false);
	}

	@Override
	protected List<? extends DiscoveryInterface> getDiscoveryList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, DiscoveryImpl.class, false);
	}

	@Override
	protected List<? extends WasUrl> getWasUrlList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, WasUrlImpl.class, false);
	}

	@Override
	protected List<? extends SummarySolutionInterface> getExecutiveSummarySolutions(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, SummarySolutionImpl.class, false);
	}

	@Override
	protected List<? extends SolutionInterface> getFindingSolutions(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, SolutionImpl.class, false);
	}

	@Override
	protected List<? extends WasFinding> getWasFindings(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().execute(statement, WasFindingImpl.class);
	}

	@Override
	protected ComplianceAnswerInterface createComplianceAnswer() {
		return new ComplianceAnswer();
	}

	@Override
	protected List<? extends AssetGroupInterface> getAssetGroupList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetGroupImpl.class, true);
	}

	@Override
	protected List<? extends AssetGroupSummaryInterface> getAssetGroupSummaryList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetGroupSummaryImpl.class, true);
	}

	@Override
	protected List<? extends AssetInterface> getAssetsList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetImpl.class, true);
	}

	@Override
	protected List<? extends AssetSummaryInterface> getAssetSummaryList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, AssetSummaryImpl.class, true);
	}

	@Override
	protected List<? extends CommentInterface> getAssetCommentsList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, CommentImpl.class, false);
	}

	@Override
	protected List<? extends PciComponentInterface> getPciComponents(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, PciComponentImpl.class);
	}

	@Override
	protected List<? extends PciDiscoveryTargetsInterface> getPciDiscoveryTargets(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, PciDiscoveryTargetsImpl.class);
	}

	@Override
	protected List<ResourceGroupInterface> loadSubUserResourceGroups(final SubUserDetails subUser) throws SQLException {
		return subUser.loadResourceGroups(getStatementExec());
	}

	@Override
	protected List<? extends CustomerInterface> getCustomersList(final NativeSqlStatement statement) throws SQLException {
		return getStatementExec().executeDTO(statement, CustomerImpl.class, false);
	}
}
