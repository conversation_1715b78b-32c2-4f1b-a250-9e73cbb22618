package com.chilicoders.workflow;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.list.UnmodifiableList;

import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.util.Configuration;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;

/**
 * This class contains all valid combinations of workflow chains vs supported scanners.
 * See breakdown document for details: https://outpost24.atlassian.net/wiki/spaces/PCD/pages/156631081/DEV-7926+Chaining+scan+types+in+portal#Expected-behaviour
 */
@Builder
@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
public class WorkflowScannerValidator {

	private static List<WorkflowScannerValidator> VALIDATORS;

	static {
		final List<WorkflowScannerValidator> validators = new ArrayList<>();
		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Collections.singletonList(ScanTemplate.SCALE)))
				.allowOutscan(true).allowAppsecHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Collections.singletonList(ScanTemplate.CLOUDSEC)))
				.allowOutscan(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.DOCKER_DISCOVERY, ScanTemplate.DOCKER_SCAN)))
				.allowNormalHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Collections.singletonList(ScanTemplate.DOCKER_SCAN)))
				.scannerFromAssetIdentifier(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Collections.singletonList(ScanTemplate.NETWORK_SCAN)))
				.allowOutscan(true).allowNormalHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.NETWORK_SCAN, ScanTemplate.SCALE)))
				.allowOutscan(true).allowNormalHIAB(true).allowAppsecHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.NETWORK_DISCOVERY, ScanTemplate.NETWORK_SCAN, ScanTemplate.SCALE)))
				.allowOutscan(true).allowNormalHIAB(true).allowAppsecHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.NETWORK_DISCOVERY, ScanTemplate.NETWORK_SCAN)))
				.allowOutscan(true).allowNormalHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.CLOUD_DISCOVERY, ScanTemplate.NETWORK_SCAN, ScanTemplate.SCALE)))
				.allowOutscan(true).allowNormalHIAB(true).allowAppsecHIAB(true).allowLocalFromHIAB(true).build());

		validators.add(WorkflowScannerValidator.builder()
				.scanTemplates(new UnmodifiableList<>(Arrays.asList(ScanTemplate.CLOUD_DISCOVERY, ScanTemplate.NETWORK_SCAN)))
				.allowOutscan(true).allowNormalHIAB(true).allowLocalFromHIAB(true).build());

		VALIDATORS = new UnmodifiableList<>(validators);
	}

	private UnmodifiableList<ScanTemplate> scanTemplates;
	private boolean allowOutscan;
	private boolean allowNormalHIAB;
	private boolean allowAppsecHIAB;
	private boolean allowLocalFromHIAB;
	private boolean scannerFromAssetIdentifier;

	/**
	 * Validates the scanner, will throw exception if invalid.
	 *
	 * @param scanner the scanner to validate
	 * @throws ForbiddenException thrown if invalid
	 */
	private void validateScanner(final ScannerImpl scanner) throws ForbiddenException {
		if (this.scannerFromAssetIdentifier) {
			if (scanner != null) {
				throw new ForbiddenException("_OMIT_SCANNER_WHEN_ASSET_IDENTIFIER");
			}
			return;
		}
		if (scanner == null) {
			throw new ForbiddenException("_SCANNER_NOT_FOUND");
		}

		if (Configuration.isHiabEnabled()) {
			if (scanner.isOutpost() && !this.allowOutscan) {
				throw new ForbiddenException("_NOT_ALLOWED_SCANNER_OUTSCAN");
			}
			if (scanner.getId() == Scanner.LOCAL_SCANNER && !this.allowLocalFromHIAB) {
				throw new ForbiddenException("_NOT_ALLOWED_SCAN_LOCAL_SCANNER");
			}
		}
		else {
			if (scanner.getId() == Scanner.LOCAL_SCANNER && !this.allowOutscan) {
				throw new ForbiddenException("_NOT_ALLOWED_SCANNER_OUTSCAN");
			}
		}

		if (!scanner.isOutpost() && scanner.getId() != Scanner.LOCAL_SCANNER && !scanner.getAppsecScaleScanner() && !this.allowNormalHIAB) {
			throw new ForbiddenException("_NOT_ALLOWED_SCANNER_NORMAL_HIAB");
		}
		if (!scanner.isOutpost() && scanner.getId() != Scanner.LOCAL_SCANNER && scanner.getAppsecScaleScanner() && !this.allowAppsecHIAB) {
			throw new ForbiddenException("_NOT_ALLOWED_SCANNER_APPSEC_HIAB");
		}
	}

	/**
	 * Validates the scanner for the given chain of scan templates.
	 *
	 * @param scanTemplates scan templates used in workflow.
	 * @param scanner the scanner to be validated.
	 * @throws ForbiddenException thrown if invalid scanner.
	 */
	public static void validate(final List<ScanTemplate> scanTemplates, final ScannerImpl scanner) throws ForbiddenException {
		final WorkflowScannerValidator validator = VALIDATORS.stream().filter(v -> v.scanTemplates.equals(scanTemplates)).findFirst().get();
		validator.validateScanner(scanner);

		// Special case for CLOUDSEC, only allowed from outscan with local scanner
		if (scanTemplates.get(0) == ScanTemplate.CLOUDSEC && scanner.getId() == Scanner.EXTERNAL_SCANNER_ID) {
			throw new ForbiddenException("_NOT_ALLOWED_SCANNER_OUTSCAN");
		}
	}

	/**
	 * Validates the list of scanners for the given chain of scan templates.
	 * Exception will be thrown if list contains no valid scanner at all, i.e.
	 * at least one scanner needs to be valid.
	 *
	 * @param scanTemplates scan templates used in workflow.
	 * @param scanners the list of scanner to be validated.
	 * @throws ForbiddenException thrown if no valid scanner in list.
	 */
	public static void validateGroup(final List<ScanTemplate> scanTemplates, final List<ScannerImpl> scanners) throws ForbiddenException {
		final List<ScannerImpl> validScanners = scanners.stream().filter(scanner -> {
			try {
				validate(scanTemplates, scanner);
				return true;
			}
			catch (final ForbiddenException e) {
				// Ignore exception and remove invalid scanner
				return false;
			}
		}).collect(Collectors.toList());

		if (validScanners.isEmpty()) {
			throw new ForbiddenException("_WORKFLOW_SCANNER_GROUP_CONTAINS_NO_VALID_SCANNERS");
		}
	}
}