//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0-b170531.0717 
//         See <a href="https://jaxb.java.net/">https://jaxb.java.net/</a> 
//         Any modifications to this file will be lost upon recompilation of the source schema. 
//         Generated on: 2018.02.26 at 12:18:06 PM CET 
//

package com.chilicoders.probe.xml;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.chilicoders.probe.xml package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

	private final static QName _DescRtab_QNAME = new QName("", "rtab");
	private final static QName _DescImg_QNAME = new QName("", "img");
	private final static QName _ReportDesc_QNAME = new QName("", "desc");
	private final static QName _ReportCvss_QNAME = new QName("", "cvss");
	private final static QName _ReportCve_QNAME = new QName("", "cve");
	private final static QName _ReportBid_QNAME = new QName("", "bid");
	private final static QName _ReportSolution_QNAME = new QName("", "solution");
	private final static QName _ReportAccuracy_QNAME = new QName("", "accuracy");
	private final static QName _ReportProduct_QNAME = new QName("", "product");
	private final static QName _ReportCvssVector_QNAME = new QName("", "cvss_vector");
	private final static QName _ReportDateCreated_QNAME = new QName("", "date_created");
	private final static QName _RootResponseReportHole_QNAME = new QName("", "hole");
	private final static QName _RootResponseReportInfo_QNAME = new QName("", "info");
	private final static QName _RootResponseReportPort_QNAME = new QName("", "port");
	private final static QName _RootResponseReportService_QNAME = new QName("", "service");
	private final static QName _RootResponseReportHost_QNAME = new QName("", "host");
	private final static QName _RootResponseStatusEvent_QNAME = new QName("", "event");
	private final static QName _RootResponseStatusPlugins_QNAME = new QName("", "plugins");
	private final static QName _RootResponseStatusPlugin_QNAME = new QName("", "plugin");
	private final static QName _RootResponseStatusTime_QNAME = new QName("", "time");
	private final static QName _RootResponsePluginfoXrefsUrl_QNAME = new QName("", "url");
	private final static QName _RootResponsePluginfoXrefsPatch_QNAME = new QName("", "patch");
	private final static QName _RootResponsePluginfoXrefsAdvisory_QNAME = new QName("", "advisory");

	/**
	 * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.chilicoders.probe.xml
	 */
	public ObjectFactory() {
	}

	/**
	 * Create an instance of {@link Root }
	 */
	public Root createRoot() {
		return new Root();
	}

	/**
	 * Create an instance of {@link Rtab }
	 */
	public Rtab createRtab() {
		return new Rtab();
	}

	/**
	 * Create an instance of {@link Root.Response }
	 */
	public Root.Response createRootResponse() {
		return new Root.Response();
	}

	/**
	 * Create an instance of {@link Root.Response.Report }
	 */
	public Root.Response.Report createRootResponseReport() {
		return new Root.Response.Report();
	}

	/**
	 * Create an instance of {@link Root.Response.Status }
	 */
	public Root.Response.Status createRootResponseStatus() {
		return new Root.Response.Status();
	}

	/**
	 * Create an instance of {@link Root.Response.Pref }
	 */
	public Root.Response.Pref createRootResponsePref() {
		return new Root.Response.Pref();
	}

	/**
	 * Create an instance of {@link Root.Response.Pluginfo }
	 */
	public Root.Response.Pluginfo createRootResponsePluginfo() {
		return new Root.Response.Pluginfo();
	}

	/**
	 * Create an instance of {@link Root.Response.Pluginfo.Facts }
	 */
	public Root.Response.Pluginfo.Facts createRootResponsePluginfoFacts() {
		return new Root.Response.Pluginfo.Facts();
	}

	/**
	 * Create an instance of {@link com.chilicoders.probe.xml.Report }
	 */
	public com.chilicoders.probe.xml.Report createReport() {
		return new com.chilicoders.probe.xml.Report();
	}

	/**
	 * Create an instance of {@link Col }
	 */
	public Col createCol() {
		return new Col();
	}

	/**
	 * Create an instance of {@link Desc }
	 */
	public Desc createDesc() {
		return new Desc();
	}

	/**
	 * Create an instance of {@link Data }
	 */
	public Data createData() {
		return new Data();
	}

	/**
	 * Create an instance of {@link Img }
	 */
	public Img createImg() {
		return new Img();
	}

	/**
	 * Create an instance of {@link Rtab.Hdr }
	 */
	public Rtab.Hdr createRtabHdr() {
		return new Rtab.Hdr();
	}

	/**
	 * Create an instance of {@link Rtab.Row }
	 */
	public Rtab.Row createRtabRow() {
		return new Rtab.Row();
	}

	/**
	 * Create an instance of {@link Root.Response.Report.Port }
	 */
	public Root.Response.Report.Port createRootResponseReportPort() {
		return new Root.Response.Report.Port();
	}

	/**
	 * Create an instance of {@link Root.Response.Report.Service }
	 */
	public Root.Response.Report.Service createRootResponseReportService() {
		return new Root.Response.Report.Service();
	}

	/**
	 * Create an instance of {@link Root.Response.Report.Host }
	 */
	public Root.Response.Report.Host createRootResponseReportHost() {
		return new Root.Response.Report.Host();
	}

	/**
	 * Create an instance of {@link Root.Response.Status.Event }
	 */
	public Root.Response.Status.Event createRootResponseStatusEvent() {
		return new Root.Response.Status.Event();
	}

	/**
	 * Create an instance of {@link Root.Response.Status.Plugins }
	 */
	public Root.Response.Status.Plugins createRootResponseStatusPlugins() {
		return new Root.Response.Status.Plugins();
	}

	/**
	 * Create an instance of {@link Root.Response.Status.Plugin }
	 */
	public Root.Response.Status.Plugin createRootResponseStatusPlugin() {
		return new Root.Response.Status.Plugin();
	}

	/**
	 * Create an instance of {@link Root.Response.Status.Time }
	 */
	public Root.Response.Status.Time createRootResponseStatusTime() {
		return new Root.Response.Status.Time();
	}

	/**
	 * Create an instance of {@link Root.Response.Pref.Default }
	 */
	public Root.Response.Pref.Default createRootResponsePrefDefault() {
		return new Root.Response.Pref.Default();
	}

	/**
	 * Create an instance of {@link Root.Response.Pref.Current }
	 */
	public Root.Response.Pref.Current createRootResponsePrefCurrent() {
		return new Root.Response.Pref.Current();
	}

	/**
	 * Create an instance of {@link Root.Response.Pluginfo.Xrefs }
	 */
	public Root.Response.Pluginfo.Xrefs createRootResponsePluginfoXrefs() {
		return new Root.Response.Pluginfo.Xrefs();
	}

	/**
	 * Create an instance of {@link Root.Response.Pluginfo.Facts.Fact }
	 */
	public Root.Response.Pluginfo.Facts.Fact createRootResponsePluginfoFactsFact() {
		return new Root.Response.Pluginfo.Facts.Fact();
	}

	/**
	 * Create an instance of {@link Root.Response.Pluginfo.Facts.PackageFact }
	 */
	public Root.Response.Pluginfo.Facts.PackageFact createRootResponsePluginfoFactsPackageFact() {
		return new Root.Response.Pluginfo.Facts.PackageFact();
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Rtab }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Rtab }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "rtab", scope = Desc.class)
	public JAXBElement<Rtab> createDescRtab(Rtab value) {
		return new JAXBElement<>(_DescRtab_QNAME, Rtab.class, Desc.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Img }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Img }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "img", scope = Desc.class)
	public JAXBElement<Img> createDescImg(Img value) {
		return new JAXBElement<>(_DescImg_QNAME, Img.class, Desc.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Desc }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Desc }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "desc", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<Desc> createReportDesc(Desc value) {
		return new JAXBElement<>(_ReportDesc_QNAME, Desc.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "cvss", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<Integer> createReportCvss(Integer value) {
		return new JAXBElement<>(_ReportCvss_QNAME, Integer.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "cve", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportCve(String value) {
		return new JAXBElement<>(_ReportCve_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "bid", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportBid(String value) {
		return new JAXBElement<>(_ReportBid_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "solution", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportSolution(String value) {
		return new JAXBElement<>(_ReportSolution_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "accuracy", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportAccuracy(String value) {
		return new JAXBElement<>(_ReportAccuracy_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "product", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportProduct(String value) {
		return new JAXBElement<>(_ReportProduct_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "cvss_vector", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportCvssVector(String value) {
		return new JAXBElement<>(_ReportCvssVector_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "date_created", scope = com.chilicoders.probe.xml.Report.class)
	public JAXBElement<String> createReportDateCreated(String value) {
		return new JAXBElement<>(_ReportDateCreated_QNAME, String.class, com.chilicoders.probe.xml.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "hole", scope = Root.Response.Report.class)
	public JAXBElement<com.chilicoders.probe.xml.Report> createRootResponseReportHole(com.chilicoders.probe.xml.Report value) {
		return new JAXBElement<>(_RootResponseReportHole_QNAME, com.chilicoders.probe.xml.Report.class, Root.Response.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "info", scope = Root.Response.Report.class)
	public JAXBElement<com.chilicoders.probe.xml.Report> createRootResponseReportInfo(com.chilicoders.probe.xml.Report value) {
		return new JAXBElement<>(_RootResponseReportInfo_QNAME, com.chilicoders.probe.xml.Report.class, Root.Response.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Port }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Port }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "port", scope = Root.Response.Report.class)
	public JAXBElement<Root.Response.Report.Port> createRootResponseReportPort(Root.Response.Report.Port value) {
		return new JAXBElement<>(_RootResponseReportPort_QNAME, Root.Response.Report.Port.class, Root.Response.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Service }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Service }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "service", scope = Root.Response.Report.class)
	public JAXBElement<Root.Response.Report.Service> createRootResponseReportService(Root.Response.Report.Service value) {
		return new JAXBElement<>(_RootResponseReportService_QNAME, Root.Response.Report.Service.class, Root.Response.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Host }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Report.Host }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "host", scope = Root.Response.Report.class)
	public JAXBElement<Root.Response.Report.Host> createRootResponseReportHost(Root.Response.Report.Host value) {
		return new JAXBElement<>(_RootResponseReportHost_QNAME, Root.Response.Report.Host.class, Root.Response.Report.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Event }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Event }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "event", scope = Root.Response.Status.class)
	public JAXBElement<Root.Response.Status.Event> createRootResponseStatusEvent(Root.Response.Status.Event value) {
		return new JAXBElement<>(_RootResponseStatusEvent_QNAME, Root.Response.Status.Event.class, Root.Response.Status.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugins }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugins }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "plugins", scope = Root.Response.Status.class)
	public JAXBElement<Root.Response.Status.Plugins> createRootResponseStatusPlugins(Root.Response.Status.Plugins value) {
		return new JAXBElement<>(_RootResponseStatusPlugins_QNAME, Root.Response.Status.Plugins.class, Root.Response.Status.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugin }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugin }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "plugin", scope = Root.Response.Status.class)
	public JAXBElement<Root.Response.Status.Plugin> createRootResponseStatusPlugin(Root.Response.Status.Plugin value) {
		return new JAXBElement<>(_RootResponseStatusPlugin_QNAME, Root.Response.Status.Plugin.class, Root.Response.Status.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Time }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link Root.Response.Status.Time }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "time", scope = Root.Response.Status.class)
	public JAXBElement<Root.Response.Status.Time> createRootResponseStatusTime(Root.Response.Status.Time value) {
		return new JAXBElement<>(_RootResponseStatusTime_QNAME, Root.Response.Status.Time.class, Root.Response.Status.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "url", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsUrl(String value) {
		return new JAXBElement<>(_RootResponsePluginfoXrefsUrl_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "solution", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsSolution(String value) {
		return new JAXBElement<>(_ReportSolution_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "patch", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsPatch(String value) {
		return new JAXBElement<>(_RootResponsePluginfoXrefsPatch_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "advisory", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsAdvisory(String value) {
		return new JAXBElement<>(_RootResponsePluginfoXrefsAdvisory_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "bid", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsBid(String value) {
		return new JAXBElement<>(_ReportBid_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

	/**
	 * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 *
	 * @param value Java instance representing xml element's value.
	 * @return the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
	 */
	@XmlElementDecl(namespace = "", name = "cve", scope = Root.Response.Pluginfo.Xrefs.class)
	public JAXBElement<String> createRootResponsePluginfoXrefsCve(String value) {
		return new JAXBElement<>(_ReportCve_QNAME, String.class, Root.Response.Pluginfo.Xrefs.class, value);
	}

}
