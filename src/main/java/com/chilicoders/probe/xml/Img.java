//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0-b170531.0717 
//         See <a href="https://jaxb.java.net/">https://jaxb.java.net/</a> 
//         Any modifications to this file will be lost upon recompilation of the source schema. 
//         Generated on: 2018.02.26 at 12:18:06 PM CET 
//

package com.chilicoders.probe.xml;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;

/**
 * "type" => so far, only "png" and "svg"
 *
 *
 * <p>Java class for img complex type.
 *
 * <p>The following schema fragment specifies the expected         content contained within this class.
 *
 * <pre>
 * &lt;complexType name="img"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;attribute name="type" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "img", propOrder = {
		"content"
})
public class Img {

	@XmlValue
	protected String content;
	@XmlAttribute(name = "type", required = true)
	protected String type;

	/**
	 * "type" => so far, only "png" and "svg"
	 *
	 * @return possible object is
	 *        {@link String }
	 */
	public String getContent() {
		return content;
	}

	/**
	 * Sets the value of the content property.
	 *
	 * @param value allowed object is
	 *        {@link String }
	 */
	public void setContent(String value) {
		this.content = value;
	}

	/**
	 * Gets the value of the type property.
	 *
	 * @return possible object is
	 *        {@link String }
	 */
	public String getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 *
	 * @param value allowed object is
	 *        {@link String }
	 */
	public void setType(String value) {
		this.type = value;
	}

}
