//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0-b170531.0717
//         See <a href="https://jaxb.java.net/">https://jaxb.java.net/</a>
//         Any modifications to this file will be lost upon recompilation of the source schema.
//         Generated on: 2018.02.26 at 12:18:06 PM CET
//

package com.chilicoders.probe.xml;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementRefs;
import javax.xml.bind.annotation.XmlElements;
import javax.xml.bind.annotation.XmlMixed;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;
import javax.xml.datatype.XMLGregorianCalendar;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected         content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="response" maxOccurs="unbounded"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;group ref="{}response-element" maxOccurs="unbounded"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
		"response"
})
@XmlRootElement(name = "root")
public class Root {

	@XmlElement(required = true)
	protected List<Root.Response> response;

	/**
	 * Gets the value of the response property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list,
	 * not a snapshot. Therefore any modification you make to the
	 * returned list will be present inside the JAXB object.
	 * This is why there is not a <CODE>set</CODE> method for the response property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 * <pre>
	 *    getResponse().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link Root.Response }
	 */
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public List<Root.Response> getResponse() {
		if (response == null) {
			response = new ArrayList<>();
		}
		return this.response;
	}

	/**
	 * <p>Java class for anonymous complex type.
	 *
	 * <p>The following schema fragment specifies the expected         content contained within this class.
	 *
	 * <pre>
	 * &lt;complexType&gt;
	 *   &lt;complexContent&gt;
	 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
	 *       &lt;sequence&gt;
	 *         &lt;group ref="{}response-element" maxOccurs="unbounded"/&gt;
	 *       &lt;/sequence&gt;
	 *     &lt;/restriction&gt;
	 *   &lt;/complexContent&gt;
	 * &lt;/complexType&gt;
	 * </pre>
	 */
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlType(name = "", propOrder = {
			"responseElement"
	})
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public static class Response {

		@XmlElements({
				@XmlElement(name = "pluginfo", type = Root.Response.Pluginfo.class),
				@XmlElement(name = "pref", type = Root.Response.Pref.class),
				@XmlElement(name = "status", type = Root.Response.Status.class),
				@XmlElement(name = "report", type = Root.Response.Report.class)
		})
		protected List<Object> responseElement;

		/**
		 * Gets the value of the responseElement property.
		 *
		 * <p>
		 * This accessor method returns a reference to the live list,
		 * not a snapshot. Therefore any modification you make to the
		 * returned list will be present inside the JAXB object.
		 * This is why there is not a <CODE>set</CODE> method for the responseElement property.
		 *
		 * <p>
		 * For example, to add a new item, do as follows:
		 * <pre>
		 *    getResponseElement().add(newItem);
		 * </pre>
		 *
		 *
		 * <p>
		 * Objects of the following type(s) are allowed in the list
		 * {@link Root.Response.Pluginfo }
		 * {@link Root.Response.Pref }
		 * {@link Root.Response.Status }
		 * {@link Root.Response.Report }
		 */
		public List<Object> getResponseElement() {
			if (responseElement == null) {
				responseElement = new ArrayList<>();
			}
			return this.responseElement;
		}

		/**
		 * <p>Java class for anonymous complex type.
		 *
		 * <p>The following schema fragment specifies the expected         content contained within this class.
		 *
		 * <pre>
		 * &lt;complexType&gt;
		 *   &lt;complexContent&gt;
		 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *       &lt;sequence&gt;
		 *         &lt;element name="name" type="{}text"/&gt;
		 *         &lt;element name="summary" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="threat_summary" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="description" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="cvss_vector" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="date_created" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="systemload_estimate" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="operating-system" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="solution" type="{}text" minOccurs="0"/&gt;
		 *         &lt;element name="facts" minOccurs="0"&gt;
		 *           &lt;complexType&gt;
		 *             &lt;complexContent&gt;
		 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                 &lt;sequence&gt;
		 *                   &lt;element name="fact" maxOccurs="unbounded" minOccurs="0"&gt;
		 *                     &lt;complexType&gt;
		 *                       &lt;complexContent&gt;
		 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                           &lt;attribute name="key" type="{}text" /&gt;
		 *                           &lt;attribute name="type" type="{}text" /&gt;
		 *                         &lt;/restriction&gt;
		 *                       &lt;/complexContent&gt;
		 *                     &lt;/complexType&gt;
		 *                   &lt;/element&gt;
		 *                   &lt;element name="package-fact" maxOccurs="unbounded" minOccurs="0"&gt;
		 *                     &lt;complexType&gt;
		 *                       &lt;complexContent&gt;
		 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                           &lt;attribute name="package" type="{}text" /&gt;
		 *                         &lt;/restriction&gt;
		 *                       &lt;/complexContent&gt;
		 *                     &lt;/complexType&gt;
		 *                   &lt;/element&gt;
		 *                 &lt;/sequence&gt;
		 *               &lt;/restriction&gt;
		 *             &lt;/complexContent&gt;
		 *           &lt;/complexType&gt;
		 *         &lt;/element&gt;
		 *         &lt;element name="xrefs" minOccurs="0"&gt;
		 *           &lt;complexType&gt;
		 *             &lt;complexContent&gt;
		 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                 &lt;sequence&gt;
		 *                   &lt;group ref="{}xref-element" maxOccurs="unbounded"/&gt;
		 *                 &lt;/sequence&gt;
		 *               &lt;/restriction&gt;
		 *             &lt;/complexContent&gt;
		 *           &lt;/complexType&gt;
		 *         &lt;/element&gt;
		 *       &lt;/sequence&gt;
		 *       &lt;attribute name="id" use="required" type="{http://www.w3.org/2001/XMLSchema}integer" /&gt;
		 *       &lt;attribute name="file" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *       &lt;attribute name="category" use="required"&gt;
		 *         &lt;simpleType&gt;
		 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
		 *             &lt;enumeration value="setting"/&gt;
		 *             &lt;enumeration value="scan"/&gt;
		 *             &lt;enumeration value="bannergrab"/&gt;
		 *             &lt;enumeration value="fingerprint"/&gt;
		 *             &lt;enumeration value="CAT_DETECT"/&gt;
		 *             &lt;enumeration value="CAT_ATTACK"/&gt;
		 *             &lt;enumeration value="CAT_DOS"/&gt;
		 *             &lt;enumeration value="CAT_KILL"/&gt;
		 *             &lt;enumeration value="CAT_LOCAL"/&gt;
		 *             &lt;enumeration value="CAT_PORTSCAN"/&gt;
		 *             &lt;enumeration value="CAT_ENDSCAN"/&gt;
		 *             &lt;enumeration value="CAT_INIT"/&gt;
		 *             &lt;enumeration value="CAT_SETTING"/&gt;
		 *             &lt;enumeration value="CAT_FINGERPRINT"/&gt;
		 *             &lt;enumeration value="CAT_SCAN"/&gt;
		 *             &lt;enumeration value="enumeration"/&gt;
		 *             &lt;enumeration value="attack"/&gt;
		 *             &lt;enumeration value="smb"/&gt;
		 *             &lt;enumeration value="local"/&gt;
		 *             &lt;enumeration value="exploit"/&gt;
		 *             &lt;enumeration value="dos"/&gt;
		 *             &lt;enumeration value="flood"/&gt;
		 *             &lt;enumeration value="kill"/&gt;
		 *             &lt;enumeration value="end"/&gt;
		 *           &lt;/restriction&gt;
		 *         &lt;/simpleType&gt;
		 *       &lt;/attribute&gt;
		 *       &lt;attribute name="family" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *     &lt;/restriction&gt;
		 *   &lt;/complexContent&gt;
		 * &lt;/complexType&gt;
		 * </pre>
		 */
		@XmlAccessorType(XmlAccessType.FIELD)
		@XmlType(name = "", propOrder = {
				"name",
				"summary",
				"threatSummary",
				"description",
				"cvssVector",
				"dateCreated",
				"systemloadEstimate",
				"operatingSystem",
				"solution",
				"facts",
				"xrefs"
		})
		public static class Pluginfo {

			@XmlElement(required = true)
			protected String name;
			protected String summary;
			@XmlElement(name = "threat_summary")
			protected String threatSummary;
			protected String description;
			@XmlElement(name = "cvss_vector")
			protected String cvssVector;
			@XmlElement(name = "date_created")
			protected String dateCreated;
			@XmlElement(name = "systemload_estimate")
			protected String systemloadEstimate;
			@XmlElement(name = "operating-system")
			protected String operatingSystem;
			protected String solution;
			protected Root.Response.Pluginfo.Facts facts;
			protected Root.Response.Pluginfo.Xrefs xrefs;
			@XmlAttribute(name = "id", required = true)
			protected BigInteger id;
			@XmlAttribute(name = "file", required = true)
			protected String file;
			@XmlAttribute(name = "category", required = true)
			protected String category;
			@XmlAttribute(name = "family", required = true)
			protected String family;

			/**
			 * Gets the value of the name property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getName() {
				return name;
			}

			/**
			 * Sets the value of the name property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setName(String value) {
				this.name = value;
			}

			/**
			 * Gets the value of the summary property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getSummary() {
				return summary;
			}

			/**
			 * Sets the value of the summary property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setSummary(String value) {
				this.summary = value;
			}

			/**
			 * Gets the value of the threatSummary property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getThreatSummary() {
				return threatSummary;
			}

			/**
			 * Sets the value of the threatSummary property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setThreatSummary(String value) {
				this.threatSummary = value;
			}

			/**
			 * Gets the value of the description property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getDescription() {
				return description;
			}

			/**
			 * Sets the value of the description property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setDescription(String value) {
				this.description = value;
			}

			/**
			 * Gets the value of the cvssVector property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getCvssVector() {
				return cvssVector;
			}

			/**
			 * Sets the value of the cvssVector property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setCvssVector(String value) {
				this.cvssVector = value;
			}

			/**
			 * Gets the value of the dateCreated property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getDateCreated() {
				return dateCreated;
			}

			/**
			 * Sets the value of the dateCreated property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setDateCreated(String value) {
				this.dateCreated = value;
			}

			/**
			 * Gets the value of the systemloadEstimate property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getSystemloadEstimate() {
				return systemloadEstimate;
			}

			/**
			 * Sets the value of the systemloadEstimate property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setSystemloadEstimate(String value) {
				this.systemloadEstimate = value;
			}

			/**
			 * Gets the value of the operatingSystem property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getOperatingSystem() {
				return operatingSystem;
			}

			/**
			 * Sets the value of the operatingSystem property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setOperatingSystem(String value) {
				this.operatingSystem = value;
			}

			/**
			 * Gets the value of the solution property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getSolution() {
				return solution;
			}

			/**
			 * Sets the value of the solution property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setSolution(String value) {
				this.solution = value;
			}

			/**
			 * Gets the value of the facts property.
			 *
			 * @return possible object is
			 *        {@link Root.Response.Pluginfo.Facts }
			 */
			public Root.Response.Pluginfo.Facts getFacts() {
				return facts;
			}

			/**
			 * Sets the value of the facts property.
			 *
			 * @param value allowed object is
			 *        {@link Root.Response.Pluginfo.Facts }
			 */
			public void setFacts(Root.Response.Pluginfo.Facts value) {
				this.facts = value;
			}

			/**
			 * Gets the value of the xrefs property.
			 *
			 * @return possible object is
			 *        {@link Root.Response.Pluginfo.Xrefs }
			 */
			public Root.Response.Pluginfo.Xrefs getXrefs() {
				return xrefs;
			}

			/**
			 * Sets the value of the xrefs property.
			 *
			 * @param value allowed object is
			 *        {@link Root.Response.Pluginfo.Xrefs }
			 */
			public void setXrefs(Root.Response.Pluginfo.Xrefs value) {
				this.xrefs = value;
			}

			/**
			 * Gets the value of the id property.
			 *
			 * @return possible object is
			 *        {@link BigInteger }
			 */
			public BigInteger getId() {
				return id;
			}

			/**
			 * Sets the value of the id property.
			 *
			 * @param value allowed object is
			 *        {@link BigInteger }
			 */
			public void setId(BigInteger value) {
				this.id = value;
			}

			/**
			 * Gets the value of the file property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getFile() {
				return file;
			}

			/**
			 * Sets the value of the file property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setFile(String value) {
				this.file = value;
			}

			/**
			 * Gets the value of the category property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getCategory() {
				return category;
			}

			/**
			 * Sets the value of the category property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setCategory(String value) {
				this.category = value;
			}

			/**
			 * Gets the value of the family property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getFamily() {
				return family;
			}

			/**
			 * Sets the value of the family property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setFamily(String value) {
				this.family = value;
			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;sequence&gt;
			 *         &lt;element name="fact" maxOccurs="unbounded" minOccurs="0"&gt;
			 *           &lt;complexType&gt;
			 *             &lt;complexContent&gt;
			 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *                 &lt;attribute name="key" type="{}text" /&gt;
			 *                 &lt;attribute name="type" type="{}text" /&gt;
			 *               &lt;/restriction&gt;
			 *             &lt;/complexContent&gt;
			 *           &lt;/complexType&gt;
			 *         &lt;/element&gt;
			 *         &lt;element name="package-fact" maxOccurs="unbounded" minOccurs="0"&gt;
			 *           &lt;complexType&gt;
			 *             &lt;complexContent&gt;
			 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *                 &lt;attribute name="package" type="{}text" /&gt;
			 *               &lt;/restriction&gt;
			 *             &lt;/complexContent&gt;
			 *           &lt;/complexType&gt;
			 *         &lt;/element&gt;
			 *       &lt;/sequence&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"fact",
					"packageFact"
			})
			public static class Facts {

				protected List<Root.Response.Pluginfo.Facts.Fact> fact;
				@XmlElement(name = "package-fact")
				protected List<Root.Response.Pluginfo.Facts.PackageFact> packageFact;

				/**
				 * Gets the value of the fact property.
				 *
				 * <p>
				 * This accessor method returns a reference to the live list,
				 * not a snapshot. Therefore any modification you make to the
				 * returned list will be present inside the JAXB object.
				 * This is why there is not a <CODE>set</CODE> method for the fact property.
				 *
				 * <p>
				 * For example, to add a new item, do as follows:
				 * <pre>
				 *    getFact().add(newItem);
				 * </pre>
				 *
				 *
				 * <p>
				 * Objects of the following type(s) are allowed in the list
				 * {@link Root.Response.Pluginfo.Facts.Fact }
				 */
				public List<Root.Response.Pluginfo.Facts.Fact> getFact() {
					if (fact == null) {
						fact = new ArrayList<>();
					}
					return this.fact;
				}

				/**
				 * Gets the value of the packageFact property.
				 *
				 * <p>
				 * This accessor method returns a reference to the live list,
				 * not a snapshot. Therefore any modification you make to the
				 * returned list will be present inside the JAXB object.
				 * This is why there is not a <CODE>set</CODE> method for the packageFact property.
				 *
				 * <p>
				 * For example, to add a new item, do as follows:
				 * <pre>
				 *    getPackageFact().add(newItem);
				 * </pre>
				 *
				 *
				 * <p>
				 * Objects of the following type(s) are allowed in the list
				 * {@link Root.Response.Pluginfo.Facts.PackageFact }
				 */
				public List<Root.Response.Pluginfo.Facts.PackageFact> getPackageFact() {
					if (packageFact == null) {
						packageFact = new ArrayList<>();
					}
					return this.packageFact;
				}

				/**
				 * <p>Java class for anonymous complex type.
				 *
				 * <p>The following schema fragment specifies the expected         content contained within this class.
				 *
				 * <pre>
				 * &lt;complexType&gt;
				 *   &lt;complexContent&gt;
				 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
				 *       &lt;attribute name="key" type="{}text" /&gt;
				 *       &lt;attribute name="type" type="{}text" /&gt;
				 *     &lt;/restriction&gt;
				 *   &lt;/complexContent&gt;
				 * &lt;/complexType&gt;
				 * </pre>
				 */
				@XmlAccessorType(XmlAccessType.FIELD)
				@XmlType(name = "")
				public static class Fact {

					@XmlAttribute(name = "key")
					protected String key;
					@XmlAttribute(name = "type")
					protected String type;

					/**
					 * Gets the value of the key property.
					 *
					 * @return possible object is
					 *        {@link String }
					 */
					public String getKey() {
						return key;
					}

					/**
					 * Sets the value of the key property.
					 *
					 * @param value allowed object is
					 *        {@link String }
					 */
					public void setKey(String value) {
						this.key = value;
					}

					/**
					 * Gets the value of the type property.
					 *
					 * @return possible object is
					 *        {@link String }
					 */
					public String getType() {
						return type;
					}

					/**
					 * Sets the value of the type property.
					 *
					 * @param value allowed object is
					 *        {@link String }
					 */
					public void setType(String value) {
						this.type = value;
					}

				}

				/**
				 * <p>Java class for anonymous complex type.
				 *
				 * <p>The following schema fragment specifies the expected         content contained within this class.
				 *
				 * <pre>
				 * &lt;complexType&gt;
				 *   &lt;complexContent&gt;
				 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
				 *       &lt;attribute name="package" type="{}text" /&gt;
				 *     &lt;/restriction&gt;
				 *   &lt;/complexContent&gt;
				 * &lt;/complexType&gt;
				 * </pre>
				 */
				@XmlAccessorType(XmlAccessType.FIELD)
				@XmlType(name = "")
				public static class PackageFact {

					@XmlAttribute(name = "package")
					protected String _package;

					/**
					 * Gets the value of the package property.
					 *
					 * @return possible object is
					 *        {@link String }
					 */
					public String getPackage() {
						return _package;
					}

					/**
					 * Sets the value of the package property.
					 *
					 * @param value allowed object is
					 *        {@link String }
					 */
					public void setPackage(String value) {
						this._package = value;
					}

				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;sequence&gt;
			 *         &lt;group ref="{}xref-element" maxOccurs="unbounded"/&gt;
			 *       &lt;/sequence&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"xrefElement"
			})
			public static class Xrefs {

				@XmlElementRefs({
						@XmlElementRef(name = "url", type = JAXBElement.class, required = false),
						@XmlElementRef(name = "solution", type = JAXBElement.class, required = false),
						@XmlElementRef(name = "patch", type = JAXBElement.class, required = false),
						@XmlElementRef(name = "advisory", type = JAXBElement.class, required = false),
						@XmlElementRef(name = "bid", type = JAXBElement.class, required = false),
						@XmlElementRef(name = "cve", type = JAXBElement.class, required = false)
				})
				protected List<JAXBElement<String>> xrefElement;

				/**
				 * Gets the value of the xrefElement property.
				 *
				 * <p>
				 * This accessor method returns a reference to the live list,
				 * not a snapshot. Therefore any modification you make to the
				 * returned list will be present inside the JAXB object.
				 * This is why there is not a <CODE>set</CODE> method for the xrefElement property.
				 *
				 * <p>
				 * For example, to add a new item, do as follows:
				 * <pre>
				 *    getXrefElement().add(newItem);
				 * </pre>
				 *
				 *
				 * <p>
				 * Objects of the following type(s) are allowed in the list
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 * {@link JAXBElement }{@code <}{@link String }{@code >}
				 */
				public List<JAXBElement<String>> getXrefElement() {
					if (xrefElement == null) {
						xrefElement = new ArrayList<>();
					}
					return this.xrefElement;
				}

			}

		}

		/**
		 * <p>Java class for anonymous complex type.
		 *
		 * <p>The following schema fragment specifies the expected         content contained within this class.
		 *
		 * <pre>
		 * &lt;complexType&gt;
		 *   &lt;complexContent&gt;
		 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *       &lt;sequence&gt;
		 *         &lt;element name="default" maxOccurs="unbounded" minOccurs="0"&gt;
		 *           &lt;complexType&gt;
		 *             &lt;complexContent&gt;
		 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                 &lt;sequence&gt;
		 *                   &lt;element name="data" type="{}data" maxOccurs="unbounded"/&gt;
		 *                 &lt;/sequence&gt;
		 *                 &lt;attribute name="nelem" use="required" type="{}positive-integer" /&gt;
		 *               &lt;/restriction&gt;
		 *             &lt;/complexContent&gt;
		 *           &lt;/complexType&gt;
		 *         &lt;/element&gt;
		 *         &lt;element name="current"&gt;
		 *           &lt;complexType&gt;
		 *             &lt;complexContent&gt;
		 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *                 &lt;sequence&gt;
		 *                   &lt;element name="data" type="{}data" maxOccurs="unbounded"/&gt;
		 *                 &lt;/sequence&gt;
		 *                 &lt;attribute name="nelem" use="required" type="{}positive-integer" /&gt;
		 *               &lt;/restriction&gt;
		 *             &lt;/complexContent&gt;
		 *           &lt;/complexType&gt;
		 *         &lt;/element&gt;
		 *       &lt;/sequence&gt;
		 *       &lt;attribute name="name" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *       &lt;attribute name="type" use="required" type="{}pref-type" /&gt;
		 *     &lt;/restriction&gt;
		 *   &lt;/complexContent&gt;
		 * &lt;/complexType&gt;
		 * </pre>
		 */
		@XmlAccessorType(XmlAccessType.FIELD)
		@XmlType(name = "", propOrder = {
				"_default",
				"current"
		})
		public static class Pref {

			@XmlElement(name = "default")
			protected List<Root.Response.Pref.Default> _default;
			@XmlElement(required = true)
			protected Root.Response.Pref.Current current;
			@XmlAttribute(name = "name", required = true)
			protected String name;
			@XmlAttribute(name = "type", required = true)
			protected PrefType type;

			/**
			 * Gets the value of the default property.
			 *
			 * <p>
			 * This accessor method returns a reference to the live list,
			 * not a snapshot. Therefore any modification you make to the
			 * returned list will be present inside the JAXB object.
			 * This is why there is not a <CODE>set</CODE> method for the default property.
			 *
			 * <p>
			 * For example, to add a new item, do as follows:
			 * <pre>
			 *    getDefault().add(newItem);
			 * </pre>
			 *
			 *
			 * <p>
			 * Objects of the following type(s) are allowed in the list
			 * {@link Root.Response.Pref.Default }
			 */
			public List<Root.Response.Pref.Default> getDefault() {
				if (_default == null) {
					_default = new ArrayList<>();
				}
				return this._default;
			}

			/**
			 * Gets the value of the current property.
			 *
			 * @return possible object is
			 *        {@link Root.Response.Pref.Current }
			 */
			public Root.Response.Pref.Current getCurrent() {
				return current;
			}

			/**
			 * Sets the value of the current property.
			 *
			 * @param value allowed object is
			 *        {@link Root.Response.Pref.Current }
			 */
			public void setCurrent(Root.Response.Pref.Current value) {
				this.current = value;
			}

			/**
			 * Gets the value of the name property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getName() {
				return name;
			}

			/**
			 * Sets the value of the name property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setName(String value) {
				this.name = value;
			}

			/**
			 * Gets the value of the type property.
			 *
			 * @return possible object is
			 *        {@link PrefType }
			 */
			public PrefType getType() {
				return type;
			}

			/**
			 * Sets the value of the type property.
			 *
			 * @param value allowed object is
			 *        {@link PrefType }
			 */
			public void setType(PrefType value) {
				this.type = value;
			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;sequence&gt;
			 *         &lt;element name="data" type="{}data" maxOccurs="unbounded"/&gt;
			 *       &lt;/sequence&gt;
			 *       &lt;attribute name="nelem" use="required" type="{}positive-integer" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"data"
			})
			public static class Current {

				@XmlElement(required = true)
				protected List<Data> data;
				@XmlAttribute(name = "nelem", required = true)
				protected BigInteger nelem;

				/**
				 * Gets the value of the data property.
				 *
				 * <p>
				 * This accessor method returns a reference to the live list,
				 * not a snapshot. Therefore any modification you make to the
				 * returned list will be present inside the JAXB object.
				 * This is why there is not a <CODE>set</CODE> method for the data property.
				 *
				 * <p>
				 * For example, to add a new item, do as follows:
				 * <pre>
				 *    getData().add(newItem);
				 * </pre>
				 *
				 *
				 * <p>
				 * Objects of the following type(s) are allowed in the list
				 * {@link Data }
				 */
				public List<Data> getData() {
					if (data == null) {
						data = new ArrayList<>();
					}
					return this.data;
				}

				/**
				 * Gets the value of the nelem property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getNelem() {
					return nelem;
				}

				/**
				 * Sets the value of the nelem property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setNelem(BigInteger value) {
					this.nelem = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;sequence&gt;
			 *         &lt;element name="data" type="{}data" maxOccurs="unbounded"/&gt;
			 *       &lt;/sequence&gt;
			 *       &lt;attribute name="nelem" use="required" type="{}positive-integer" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"data"
			})
			public static class Default {

				@XmlElement(required = true)
				protected List<Data> data;
				@XmlAttribute(name = "nelem", required = true)
				protected BigInteger nelem;

				/**
				 * Gets the value of the data property.
				 *
				 * <p>
				 * This accessor method returns a reference to the live list,
				 * not a snapshot. Therefore any modification you make to the
				 * returned list will be present inside the JAXB object.
				 * This is why there is not a <CODE>set</CODE> method for the data property.
				 *
				 * <p>
				 * For example, to add a new item, do as follows:
				 * <pre>
				 *    getData().add(newItem);
				 * </pre>
				 *
				 *
				 * <p>
				 * Objects of the following type(s) are allowed in the list
				 * {@link Data }
				 */
				public List<Data> getData() {
					if (data == null) {
						data = new ArrayList<>();
					}
					return this.data;
				}

				/**
				 * Gets the value of the nelem property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getNelem() {
					return nelem;
				}

				/**
				 * Sets the value of the nelem property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setNelem(BigInteger value) {
					this.nelem = value;
				}

			}

		}

		/**
		 * <p>Java class for anonymous complex type.
		 *
		 * <p>The following schema fragment specifies the expected         content contained within this class.
		 *
		 * <pre>
		 * &lt;complexType&gt;
		 *   &lt;complexContent&gt;
		 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *       &lt;sequence&gt;
		 *         &lt;group ref="{}report-element" maxOccurs="unbounded"/&gt;
		 *       &lt;/sequence&gt;
		 *       &lt;attribute name="addr" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *     &lt;/restriction&gt;
		 *   &lt;/complexContent&gt;
		 * &lt;/complexType&gt;
		 * </pre>
		 */
		@XmlAccessorType(XmlAccessType.FIELD)
		@XmlType(name = "", propOrder = {
				"reportElement"
		})
		public static class Report {

			@XmlElementRefs({
					@XmlElementRef(name = "hole", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "info", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "port", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "service", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "host", type = JAXBElement.class, required = false)
			})
			protected List<JAXBElement<?>> reportElement;
			@XmlAttribute(name = "addr")
			protected String addr;

			/**
			 * Gets the value of the reportElement property.
			 *
			 * <p>
			 * This accessor method returns a reference to the live list,
			 * not a snapshot. Therefore any modification you make to the
			 * returned list will be present inside the JAXB object.
			 * This is why there is not a <CODE>set</CODE> method for the reportElement property.
			 *
			 * <p>
			 * For example, to add a new item, do as follows:
			 * <pre>
			 *    getReportElement().add(newItem);
			 * </pre>
			 *
			 *
			 * <p>
			 * Objects of the following type(s) are allowed in the list
			 * {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
			 * {@link JAXBElement }{@code <}{@link com.chilicoders.probe.xml.Report }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Report.Port }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Report.Service }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Report.Host }{@code >}
			 */
			public List<JAXBElement<?>> getReportElement() {
				if (reportElement == null) {
					reportElement = new ArrayList<>();
				}
				return this.reportElement;
			}

			/**
			 * Gets the value of the addr property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getAddr() {
				return addr;
			}

			/**
			 * Sets the value of the addr property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setAddr(String value) {
				this.addr = value;
			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="id" type="{}positive-integer" /&gt;
			 *       &lt;attribute name="addr" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *       &lt;attribute name="hwaddr" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *       &lt;attribute name="alive" type="{http://www.w3.org/2001/XMLSchema}integer" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"content"
			})
			public static class Host {

				@XmlValue
				protected String content;
				@XmlAttribute(name = "id")
				protected BigInteger id;
				@XmlAttribute(name = "addr")
				protected String addr;
				@XmlAttribute(name = "hwaddr")
				protected String hwaddr;
				@XmlAttribute(name = "alive")
				protected BigInteger alive;

				/**
				 * Gets the value of the content property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getContent() {
					return content;
				}

				/**
				 * Sets the value of the content property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setContent(String value) {
					this.content = value;
				}

				/**
				 * Gets the value of the id property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getId() {
					return id;
				}

				/**
				 * Sets the value of the id property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setId(BigInteger value) {
					this.id = value;
				}

				/**
				 * Gets the value of the addr property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getAddr() {
					return addr;
				}

				/**
				 * Sets the value of the addr property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setAddr(String value) {
					this.addr = value;
				}

				/**
				 * Gets the value of the hwaddr property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getHwaddr() {
					return hwaddr;
				}

				/**
				 * Sets the value of the hwaddr property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setHwaddr(String value) {
					this.hwaddr = value;
				}

				/**
				 * Gets the value of the alive property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getAlive() {
					return alive;
				}

				/**
				 * Sets the value of the alive property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setAlive(BigInteger value) {
					this.alive = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="id" type="{}positive-integer" /&gt;
			 *       &lt;attribute name="port" type="{}port-number" /&gt;
			 *       &lt;attribute name="proto" type="{}proto-string" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "")
			public static class Port {

				@XmlAttribute(name = "id")
				protected BigInteger id;
				@XmlAttribute(name = "port")
				protected Integer port;
				@XmlAttribute(name = "proto")
				protected ProtoString proto;

				/**
				 * Gets the value of the id property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getId() {
					return id;
				}

				/**
				 * Sets the value of the id property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setId(BigInteger value) {
					this.id = value;
				}

				/**
				 * Gets the value of the port property.
				 *
				 * @return possible object is
				 *        {@link Integer }
				 */
				public Integer getPort() {
					return port;
				}

				/**
				 * Sets the value of the port property.
				 *
				 * @param value allowed object is
				 *        {@link Integer }
				 */
				public void setPort(Integer value) {
					this.port = value;
				}

				/**
				 * Gets the value of the proto property.
				 *
				 * @return possible object is
				 *        {@link ProtoString }
				 */
				public ProtoString getProto() {
					return proto;
				}

				/**
				 * Sets the value of the proto property.
				 *
				 * @param value allowed object is
				 *        {@link ProtoString }
				 */
				public void setProto(ProtoString value) {
					this.proto = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="id" type="{}positive-integer" /&gt;
			 *       &lt;attribute name="port" type="{}port-number" /&gt;
			 *       &lt;attribute name="proto" type="{}proto-string" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"content"
			})
			public static class Service {

				@XmlValue
				protected String content;
				@XmlAttribute(name = "id")
				protected BigInteger id;
				@XmlAttribute(name = "port")
				protected Integer port;
				@XmlAttribute(name = "proto")
				protected ProtoString proto;

				/**
				 * Gets the value of the content property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getContent() {
					return content;
				}

				/**
				 * Sets the value of the content property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setContent(String value) {
					this.content = value;
				}

				/**
				 * Gets the value of the id property.
				 *
				 * @return possible object is
				 *        {@link BigInteger }
				 */
				public BigInteger getId() {
					return id;
				}

				/**
				 * Sets the value of the id property.
				 *
				 * @param value allowed object is
				 *        {@link BigInteger }
				 */
				public void setId(BigInteger value) {
					this.id = value;
				}

				/**
				 * Gets the value of the port property.
				 *
				 * @return possible object is
				 *        {@link Integer }
				 */
				public Integer getPort() {
					return port;
				}

				/**
				 * Sets the value of the port property.
				 *
				 * @param value allowed object is
				 *        {@link Integer }
				 */
				public void setPort(Integer value) {
					this.port = value;
				}

				/**
				 * Gets the value of the proto property.
				 *
				 * @return possible object is
				 *        {@link ProtoString }
				 */
				public ProtoString getProto() {
					return proto;
				}

				/**
				 * Sets the value of the proto property.
				 *
				 * @param value allowed object is
				 *        {@link ProtoString }
				 */
				public void setProto(ProtoString value) {
					this.proto = value;
				}

			}

		}

		/**
		 * <p>Java class for anonymous complex type.
		 *
		 * <p>The following schema fragment specifies the expected         content contained within this class.
		 *
		 * <pre>
		 * &lt;complexType&gt;
		 *   &lt;complexContent&gt;
		 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
		 *       &lt;sequence&gt;
		 *         &lt;group ref="{}update-element" maxOccurs="unbounded" minOccurs="0"/&gt;
		 *       &lt;/sequence&gt;
		 *       &lt;attribute name="id" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *       &lt;attribute name="type" use="required"&gt;
		 *         &lt;simpleType&gt;
		 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
		 *             &lt;enumeration value="ok"/&gt;
		 *             &lt;enumeration value="error"/&gt;
		 *             &lt;enumeration value="update"/&gt;
		 *           &lt;/restriction&gt;
		 *         &lt;/simpleType&gt;
		 *       &lt;/attribute&gt;
		 *       &lt;attribute name="origin" use="required"&gt;
		 *         &lt;simpleType&gt;
		 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
		 *             &lt;enumeration value="attack"/&gt;
		 *             &lt;enumeration value="pause"/&gt;
		 *             &lt;enumeration value="unpause"/&gt;
		 *             &lt;enumeration value="quit"/&gt;
		 *             &lt;enumeration value="stop"/&gt;
		 *             &lt;enumeration value="detach"/&gt;
		 *             &lt;enumeration value="resume"/&gt;
		 *             &lt;enumeration value="upload"/&gt;
		 *             &lt;enumeration value="prefset"/&gt;
		 *             &lt;enumeration value="prefget"/&gt;
		 *             &lt;enumeration value="pref_upload"/&gt;
		 *             &lt;enumeration value="plugset"/&gt;
		 *             &lt;enumeration value="pluginfo"/&gt;
		 *             &lt;enumeration value="invalid"/&gt;
		 *           &lt;/restriction&gt;
		 *         &lt;/simpleType&gt;
		 *       &lt;/attribute&gt;
		 *       &lt;attribute name="addr" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
		 *     &lt;/restriction&gt;
		 *   &lt;/complexContent&gt;
		 * &lt;/complexType&gt;
		 * </pre>
		 */
		@XmlAccessorType(XmlAccessType.FIELD)
		@XmlType(name = "", propOrder = {
				"content"
		})
		public static class Status {

			@XmlElementRefs({
					@XmlElementRef(name = "event", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "plugins", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "plugin", type = JAXBElement.class, required = false),
					@XmlElementRef(name = "time", type = JAXBElement.class, required = false)
			})
			@XmlMixed
			protected List<Serializable> content;
			@XmlAttribute(name = "id", required = true)
			protected String id;
			@XmlAttribute(name = "type", required = true)
			protected String type;
			@XmlAttribute(name = "origin", required = true)
			protected String origin;
			@XmlAttribute(name = "addr", required = true)
			protected String addr;

			/**
			 * Gets the value of the content property.
			 *
			 * <p>
			 * This accessor method returns a reference to the live list,
			 * not a snapshot. Therefore any modification you make to the
			 * returned list will be present inside the JAXB object.
			 * This is why there is not a <CODE>set</CODE> method for the content property.
			 *
			 * <p>
			 * For example, to add a new item, do as follows:
			 * <pre>
			 *    getContent().add(newItem);
			 * </pre>
			 *
			 *
			 * <p>
			 * Objects of the following type(s) are allowed in the list
			 * {@link JAXBElement }{@code <}{@link Root.Response.Status.Event }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugins }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Status.Plugin }{@code >}
			 * {@link JAXBElement }{@code <}{@link Root.Response.Status.Time }{@code >}
			 * {@link String }
			 */
			public List<Serializable> getContent() {
				if (content == null) {
					content = new ArrayList<>();
				}
				return this.content;
			}

			/**
			 * Gets the value of the id property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getId() {
				return id;
			}

			/**
			 * Sets the value of the id property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setId(String value) {
				this.id = value;
			}

			/**
			 * Gets the value of the type property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getType() {
				return type;
			}

			/**
			 * Sets the value of the type property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setType(String value) {
				this.type = value;
			}

			/**
			 * Gets the value of the origin property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getOrigin() {
				return origin;
			}

			/**
			 * Sets the value of the origin property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setOrigin(String value) {
				this.origin = value;
			}

			/**
			 * Gets the value of the addr property.
			 *
			 * @return possible object is
			 *        {@link String }
			 */
			public String getAddr() {
				return addr;
			}

			/**
			 * Sets the value of the addr property.
			 *
			 * @param value allowed object is
			 *        {@link String }
			 */
			public void setAddr(String value) {
				this.addr = value;
			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="value"&gt;
			 *         &lt;simpleType&gt;
			 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
			 *             &lt;enumeration value="started"/&gt;
			 *             &lt;enumeration value="completed"/&gt;
			 *             &lt;enumeration value="detached"/&gt;
			 *             &lt;enumeration value="stopped"/&gt;
			 *             &lt;enumeration value="category-change"/&gt;
			 *           &lt;/restriction&gt;
			 *         &lt;/simpleType&gt;
			 *       &lt;/attribute&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"content"
			})
			public static class Event {

				@XmlValue
				protected String content;
				@XmlAttribute(name = "value")
				protected String value;

				/**
				 * Gets the value of the content property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getContent() {
					return content;
				}

				/**
				 * Sets the value of the content property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setContent(String value) {
					this.content = value;
				}

				/**
				 * Gets the value of the value property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getValue() {
					return value;
				}

				/**
				 * Sets the value of the value property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setValue(String value) {
					this.value = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *       &lt;attribute name="status" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *       &lt;attribute name="msg" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "")
			public static class Plugin {

				@XmlAttribute(name = "id")
				protected String id;
				@XmlAttribute(name = "status")
				protected String status;
				@XmlAttribute(name = "msg")
				protected String msg;

				/**
				 * Gets the value of the id property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getId() {
					return id;
				}

				/**
				 * Sets the value of the id property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setId(String value) {
					this.id = value;
				}

				/**
				 * Gets the value of the status property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getStatus() {
					return status;
				}

				/**
				 * Sets the value of the status property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setStatus(String value) {
					this.status = value;
				}

				/**
				 * Gets the value of the msg property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getMsg() {
					return msg;
				}

				/**
				 * Sets the value of the msg property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setMsg(String value) {
					this.msg = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="status" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "", propOrder = {
					"content"
			})
			public static class Plugins {

				@XmlValue
				protected String content;
				@XmlAttribute(name = "status")
				protected String status;

				/**
				 * Gets the value of the content property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getContent() {
					return content;
				}

				/**
				 * Sets the value of the content property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setContent(String value) {
					this.content = value;
				}

				/**
				 * Gets the value of the status property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getStatus() {
					return status;
				}

				/**
				 * Sets the value of the status property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setStatus(String value) {
					this.status = value;
				}

			}

			/**
			 * <p>Java class for anonymous complex type.
			 *
			 * <p>The following schema fragment specifies the expected         content contained within this class.
			 *
			 * <pre>
			 * &lt;complexType&gt;
			 *   &lt;complexContent&gt;
			 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
			 *       &lt;attribute name="name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
			 *       &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}time" /&gt;
			 *     &lt;/restriction&gt;
			 *   &lt;/complexContent&gt;
			 * &lt;/complexType&gt;
			 * </pre>
			 */
			@XmlAccessorType(XmlAccessType.FIELD)
			@XmlType(name = "")
			public static class Time {

				@XmlAttribute(name = "name")
				protected String name;
				@XmlAttribute(name = "value")
				@XmlSchemaType(name = "time")
				protected XMLGregorianCalendar value;

				/**
				 * Gets the value of the name property.
				 *
				 * @return possible object is
				 *        {@link String }
				 */
				public String getName() {
					return name;
				}

				/**
				 * Sets the value of the name property.
				 *
				 * @param value allowed object is
				 *        {@link String }
				 */
				public void setName(String value) {
					this.name = value;
				}

				/**
				 * Gets the value of the value property.
				 *
				 * @return possible object is
				 *        {@link XMLGregorianCalendar }
				 */
				public XMLGregorianCalendar getValue() {
					return value;
				}

				/**
				 * Sets the value of the value property.
				 *
				 * @param value allowed object is
				 *        {@link XMLGregorianCalendar }
				 */
				public void setValue(XMLGregorianCalendar value) {
					this.value = value;
				}

			}

		}

	}

}
