//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0-b170531.0717
//         See <a href="https://jaxb.java.net/">https://jaxb.java.net/</a>
//         Any modifications to this file will be lost upon recompilation of the source schema.
//         Generated on: 2018.02.26 at 12:18:06 PM CET
//

package com.chilicoders.probe.xml;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for pref-type.
 *
 * <p>The following schema fragment specifies the expected         content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="pref-type"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="entry"/&gt;
 *     &lt;enumeration value="checkbox"/&gt;
 *     &lt;enumeration value="radio"/&gt;
 *     &lt;enumeration value="password"/&gt;
 *     &lt;enumeration value="list"/&gt;
 *     &lt;enumeration value="file"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 */
@XmlType(name = "pref-type")
@XmlEnum
public enum PrefType {

	@XmlEnumValue("entry")
	ENTRY("entry"),
	@XmlEnumValue("checkbox")
	CHECKBOX("checkbox"),
	@XmlEnumValue("radio")
	RADIO("radio"),
	@XmlEnumValue("password")
	PASSWORD("password"),
	@XmlEnumValue("list")
	LIST("list"),
	@XmlEnumValue("file")
	FILE("file");
	private final String value;

	PrefType(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static PrefType fromValue(String v) {
		for (final PrefType c : PrefType.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

}
