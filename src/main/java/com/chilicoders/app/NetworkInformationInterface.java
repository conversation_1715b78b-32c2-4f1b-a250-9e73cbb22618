package com.chilicoders.app;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.util.StringUtils;

public interface NetworkInformationInterface {
	void initialize(boolean bool);

	List<BwLimit> getBandwidthLimits();

	void loadBandwidthLimits();

	boolean isLocalAddress(String ip);

	void loadNetworkInterfaces();

	void loadNetworkInterface(final String uuid);

	String getIpAddress();

	Collection<String> getWhitelist();

	boolean hasIpv4Configured();

	List<String> getHttpListen();

	String getProxyHost();

	int getProxyPort();

	String getProxyPassword();

	String getProxyType();

	String getProxyUser();

	String getNtpServer();

	void loadNtpSettings();

	void loadProxySettings();

	void loadSmtpSettings();

	String getSmtpRelay();

	String getSmtpRelayUser();

	String getSmtpRelayPassword();

	Set<NetworkAddress> getAllAddresses();

	@DbTable(name = "dummy")
	public static class NetworkAddress extends XmlAble {
		private NetworkAddress(final String address) {
			ip = address;
		}

		@DbField
		private String ip;

		/**
		 * Get list of network addresses from string.
		 *
		 * @param data List of addresses separated by newline
		 * @return List of network addresses
		 */
		public static Collection<NetworkAddress> getFromString(final String data) {
			final List<NetworkAddress> res = new ArrayList<>();
			if (data == null) {
				return res;
			}
			final String[] parts = data.split("\n");
			for (final String p : parts) {
				if (!StringUtils.isEmpty(p.trim())) {
					res.add(new NetworkAddress(p.trim()));
				}
			}
			return res;
		}

		@Override
		public boolean equals(final Object obj) {
			if (!(obj instanceof NetworkAddress)) {
				return false;
			}
			return ip.equals(((NetworkAddress) obj).ip);
		}

		@Override
		public int hashCode() {
			return ip.hashCode();
		}

		public String getIp() {
			return ip;
		}
	}

	String getMacAddress();

	Collection<String> getInterfaceNames();

	void loadUiWhiteList();

	boolean hasIpv6Configured();

	boolean getHardening();
}
