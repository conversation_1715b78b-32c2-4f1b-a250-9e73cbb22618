package com.chilicoders.event.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Configuration
@ConfigurationProperties(prefix = "syslog")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SyslogEventConfig {
	private boolean kafka;
	private String brokers;
}
