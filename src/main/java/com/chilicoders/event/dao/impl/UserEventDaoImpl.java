package com.chilicoders.event.dao.impl;

import static com.chilicoders.model.Event.O24Event.DiscoveryDone;
import static com.chilicoders.model.Event.O24Event.ScanDone;

import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.db.query.JpaNativeStatementExecutor;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.event.dao.UserEventDao;
import com.chilicoders.event.entities.QueueLogEntry;
import com.chilicoders.event.entities.StringPair;
import com.chilicoders.event.entities.UserEvent;
import com.chilicoders.event.jpa.repository.QueueLogEntryRepository;
import com.chilicoders.model.Event;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.model.EventType;

@Component
public class UserEventDaoImpl implements UserEventDao {

	/**
	 * Demo user id.
	 */
	private static final int DEMO_USER_ID = 5;

	@Autowired
	private JpaNativeStatementExecutor statementExec;

	@Autowired
	private QueueLogEntryRepository queueLogRepository;

	@Override
	public List<StringPair> getValidAddressesByIps(final String[] ips, final String[] hostnames, final Long eventId, final Long userId, final Long subuserId) {
		final NativeSqlStatement sql = new NativeSqlStatement();
		sql.addSql("SELECT DISTINCT HOST(l.ipaddress) AS ipaddress, 1::text AS valid "
				+ "FROM (SELECT DISTINCT explode_array::inet AS ipaddress, :id AS xid, :subuserid AS xsubuserxid, :userid AS xuserxid FROM explode_array(:ips)) AS l ");
		sql.addSql("LEFT JOIN tsubusers s ON s.xid = l.xsubuserxid LEFT JOIN xlinksubhosts x ON x.xsubxid = s.xid ");
		sql.addSql("WHERE (l.xsubuserxid IS NULL OR l.xsubuserxid = -1 OR s.boallhosts = 1 OR ((SELECT pci FROM tuserdatas d WHERE d.xid=l.xid) = 1 "
				+ "AND (SELECT GREATEST(pciscoping, pcischeduling, pcidisputing, pcireporting) FROM vsubroles r WHERE r.xsubxid=l.xsubuserxid) = 1) ");
		sql.addSql("OR (l.ipaddress >= x.ipaddress AND l.ipaddress <= x.endipaddress) OR l.ipaddress "
				+ "IN (SELECT ipaddress FROM vsubhost WHERE xsubxid = l.xsubuserxid AND ipaddress = l.ipaddress)) ");
		sql.addSql("AND (l.ipaddress IN (SELECT l.ipaddress FROM tloggings lx, xlinkloggings x "
				+ "WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.ipaddress <= l.ipaddress AND x.endipaddress >= l.ipaddress ");
		sql.addSql("UNION ALL SELECT xg.ipaddress FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg WHERE lx.xid = l.xid AND x.xid = lx.xid "
				+ "AND p.parentxid = x.groupxid AND p.xid = xg.xid AND xg.ipaddress = l.ipaddress) ");
		sql.addSql("OR l.xid IN (SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 ");
		sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 "
				+ "AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) = 0)");
		sql.addSql(") UNION ALL ");
		sql.addSql("SELECT DISTINCT hi.hostname, 1::text AS valid "
				+ "FROM (SELECT DISTINCT hostid(explode_array) AS hostnameid, :id AS xid, :subuserid AS xsubuserxid, :userid AS xuserxid FROM explode_array(:hostnames)) AS l ");
		sql.addSql("LEFT JOIN hostids hi ON hi.id = l.hostnameid LEFT JOIN tsubusers s ON s.xid = l.xsubuserxid LEFT JOIN xlinksubhosts x ON x.xsubxid = s.xid ");
		sql.addSql("WHERE (l.xsubuserxid IS NULL OR l.xsubuserxid = -1 OR s.boallhosts = 1 OR ((SELECT pci FROM tuserdatas d WHERE d.xid=l.xid) = 1 "
				+ "AND (SELECT GREATEST(pciscoping, pcischeduling, pcidisputing, pcireporting) FROM vsubroles r WHERE r.xsubxid=l.xsubuserxid) = 1) ");
		sql.addSql("OR (l.hostnameid = x.hostnameid) OR l.hostnameid IN (SELECT hostnameid FROM vsubhost WHERE xsubxid = l.xsubuserxid AND hostnameid = l.hostnameid)) ");
		sql.addSql("AND (l.hostnameid IN (SELECT l.hostnameid FROM tloggings lx, xlinkloggings x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.hostnameid = l.hostnameid ");
		sql.addSql("UNION ALL SELECT xg.hostnameid FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg WHERE lx.xid = l.xid AND x.xid = lx.xid "
				+ "AND p.parentxid = x.groupxid AND p.xid = xg.xid AND xg.hostnameid = l.hostnameid) ");
		sql.addSql("OR l.xid IN (SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 ");
		sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 "
				+ "AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) = 0)");
		sql.addSql(")");
		sql.setParameter("ips", ips);
		sql.setParameter("hostnames", hostnames);
		sql.setParameter("subuserid", subuserId);
		sql.setParameter("id", eventId);
		sql.setParameter("userid", userId);
		return statementExec.execute(sql, "StringPairResult");
	}

	@Override
	public List<StringPair> getValidAddressesScanNotDone(final Object[] ips, final Long eventId, final Long userId, final Long subuserId) {
		final NativeSqlStatement sql = new NativeSqlStatement();
		sql.addSql("SELECT DISTINCT xipxid::text AS ipaddress, 1::text AS valid "
				+ "FROM (SELECT DISTINCT explode_array AS xipxid, :id AS xid, :subuserid AS xsubuserxid, :userid AS xuserxid FROM explode_array(:ips)) AS l ");
		sql.addSql("LEFT JOIN tsubusers s ON s.xid = l.xsubuserxid ");
		sql.addSql("WHERE (l.xsubuserxid IS NULL OR l.xsubuserxid = -1 OR s.boallhosts = 1 OR ((SELECT pci FROM tuserdatas d WHERE d.xid=l.xipxid) = 1 "
				+ "AND (SELECT GREATEST(pciscoping, pcischeduling, pcidisputing, pcireporting) FROM vsubroles r WHERE r.xsubxid=l.xsubuserxid) = 1) ");
		sql.addSql("OR l.xipxid =ANY ((SELECT ARRAY_AGG(xipxid) FROM vsubhost WHERE xipxid = l.xipxid)::BIGINT[])) ");
		sql.addSql("AND (l.xipxid IN (SELECT u.xid FROM tloggings lx, xlinkloggings x, tuserdatas u WHERE u.xid = l.xipxid AND lx.xid = l.xid AND x.xid = lx.xid "
				+ "AND ((x.ipaddress <= u.ipaddress AND x.endipaddress >= u.ipaddress) "
				+ "OR u.hostnameid = x.hostnameid OR '@' || u.aws_instance_id = x.targets OR '~' || u.agentid = x.targets) ");
		sql.addSql("UNION ALL SELECT xg.xipxid FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg "
				+ "WHERE lx.xid = l.xid AND x.xid = lx.xid AND p.parentxid = x.groupxid AND p.xid = xg.xid AND xg.xipxid = l.xipxid) ");
		sql.addSql("OR l.xid IN (SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 ");
		sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 "
				+ "AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) = 0)");
		sql.addSql(")");
		sql.setParameter("ips", ips);
		sql.setParameter("subuserid", subuserId);
		sql.setParameter("id", eventId);
		sql.setParameter("userid", userId);
		return statementExec.execute(sql, "StringPairResult");
	}

	@Override
	public List<StringPair> getValidAddressesScanDone(final Object[] ips, final Long eventId, final Long userId, final Long subuserId) {
		final NativeSqlStatement sql = new NativeSqlStatement();
		sql.addSql("SELECT DISTINCT xipxid::text AS ipaddress, (SELECT string_agg(g.name || ',') AS string_agg "
				+ "FROM tgenericgroups g, xlinkgeneric x WHERE g.xid = x.xid AND g.xuserxid = l.xuserxid AND x.xipxid = l.xipxid "
				+ "AND (l.xsubuserxid = -1 OR l.xsubuserxid IS NULL OR s.boallhosts = 1 OR g.xid IN (SELECT xid FROM vsubgroup WHERE xsubxid = l.xsubuserxid))) AS valid ");
		sql.addSql("FROM (SELECT DISTINCT explode_array AS xipxid, :id AS xid, :subuserid AS xsubuserxid, :userid AS xuserxid FROM explode_array(:ips)) "
				+ "AS l LEFT JOIN tsubusers s ON s.xid = l.xsubuserxid ");
		sql.addSql("WHERE (l.xsubuserxid IS NULL OR l.xsubuserxid = -1 OR s.boallhosts = 1 OR l.xipxid =ANY ((SELECT ARRAY_AGG(xipxid) "
				+ "FROM vsubhost WHERE xipxid = l.xipxid)::BIGINT[])) ");
		sql.addSql("AND (l.xipxid IN (SELECT u.xid FROM tloggings lx, xlinkloggings x, tuserdatas u WHERE u.xid = l.xipxid AND lx.xid = l.xid AND x.xid = lx.xid "
				+ "AND ((x.ipaddress <= u.ipaddress AND x.endipaddress >= u.ipaddress) "
				+ "OR u.hostnameid=x.hostnameid OR '@' || u.aws_instance_id = x.targets OR '~' || u.agentid = x.targets) ");
		sql.addSql("UNION ALL SELECT xg.xipxid FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg "
				+ "WHERE lx.xid = l.xid AND x.xid = lx.xid AND p.parentxid = x.groupxid AND p.xid = xg.xid AND xg.xipxid = l.xipxid) ");
		sql.addSql("OR l.xid IN (SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 ");
		sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 "
				+ "AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) = 0)");
		sql.addSql(")");
		sql.setParameter("ips", ips);
		sql.setParameter("subuserid", subuserId);
		sql.setParameter("id", eventId);
		sql.setParameter("userid", userId);
		return statementExec.execute(sql, "StringPairResult");
	}

	@Override
	public List<UserEvent> getUserEvents(final Event event, final String[] ips, final Long[] hostIds) {
		final NativeSqlStatement sql = new NativeSqlStatement("SELECT l.*, COALESCE(s.vcfullname, m.vcfullname) AS createdby FROM tloggings l LEFT JOIN tusers m ON "
				+ "l.xuserxid=m.xid LEFT JOIN tsubusers s ON l.xsubuserxid=s.xid WHERE l.bactive = 1 "
				+ "AND m.bactive = 1 AND (xsubuserxid IS NULL OR xsubuserxid = -1 OR s.bactive = 1) ");

		sql.appendConditionalFilterNamed(event.getUserEventId() > 0, "l.xid = :xid", "xid", event.getUserEventId());
		if (event.getEvent() != O24Event.ReleaseNotes) {
			if (event.getUserId() != -1) {
				sql.appendFilterNamed("xuserxid = :xuserxid", "xuserxid", event.getUserId());
			}
			else {
				sql.appendFilterNamed("xuserxid != :xuserxid", "xuserxid", DEMO_USER_ID);
			}
		}

		sql.appendConditionalFilter(event.getEvent() != O24Event.Unknown, "xrefid = :xrefid");
		sql.setParameter("xrefid", event.getEvent().getId());

		if (event.getTargetIds() != null) {
			sql.setParameter("targetIds", event.getTargetIds().toArray(new Long[0]));
			if (event.getEventType() == EventType.Swat) {
				sql.addSql(" AND ((l.xsubuserxid is NULL OR l.xsubuserxid = -1) OR s.allswat = 1 OR l.xsubuserxid "
						+ "IN (SELECT xsubxid FROM xlinksubswat WHERE xsubxid = l.xsubuserxid AND swatid = ANY (:targetIds))) ");
				sql.addSql("AND l.xid IN (");
				sql.addSql("SELECT lx.xid FROM tloggings lx, xlinkloggingswat x WHERE lx.xid = l.xid AND x.xid = lx.xid AND (x.swatid = -1 OR x.swatid = ANY (:targetIds)) ");
				sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinkloggingswat x WHERE x.xid=lx.xid) = 0)");
			}
			else if (event.getEvent() != O24Event.DiscoveryDone) {
				sql.addSql(" AND ((l.xsubuserxid is NULL OR l.xsubuserxid = -1) OR s.boallhosts = 1 OR l.xsubuserxid "
						+ "IN (SELECT xsubxid FROM vsubhost WHERE xsubxid = l.xsubuserxid AND xipxid =ANY (:targetIds)) ");
				sql.addSql("OR ((SELECT MAX(pci) FROM tuserdatas WHERE xid = ANY (:targetIds)) = 1) "
						+ "AND (SELECT GREATEST(pciscoping, pcischeduling, pcidisputing, pcireporting) FROM vsubroles r WHERE r.xsubxid=l.xsubuserxid)=1) ");
				sql.addSql("AND l.xid IN (");
				sql.addSql("SELECT lx.xid FROM tloggings lx, xlinkloggings x, "
						+ "(SELECT DISTINCT ipaddress, hostnameid, '@' || aws_instance_id AS aws_instance_id, '~' || agentid AS agentid "
						+ "FROM tuserdatas u WHERE xid = ANY (:targetIds)) AS u WHERE lx.xid = l.xid AND x.xid = lx.xid AND ((x.ipaddress <= u.ipaddress "
						+ "AND x.endipaddress >= u.ipaddress) OR u.hostnameid = x.hostnameid OR u.aws_instance_id = x.targets OR u.agentid = x.targets) ");
				sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg "
						+ "WHERE lx.xid = l.xid AND x.xid = lx.xid AND p.parentxid = x.groupxid AND p.xid = xg.xid AND xg.xipxid = ANY (:targetIds) ");
				sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 ");
				sql.addSql("UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 "
						+ "AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) = 0)");
			}
		}

		if (event.getEvent() == DiscoveryDone || event.getEvent() == ScanDone) {
			if (event.getSubuserId() > 0) {
				sql.appendFilterNamed("(l.myscans = 0 OR l.xsubuserxid = :xsubuserid)", "xsubuserid", event.getSubuserId());
			}
			else {
				sql.appendFilter("(l.myscans = 0 OR l.xsubuserxid <= 0 OR l.xsubuserxid IS NULL)");
			}
		}

		if (event.getEvent() != O24Event.ReleaseNotes) {
			final String subclause = "((l.xsubuserxid IS NULL OR l.xsubuserxid = -1) OR s.boallhosts = 1 OR l.xsubuserxid IN "
					+ "(SELECT xsubxid FROM vsubhost WHERE xsubxid IN (SELECT DISTINCT xsubuserxid FROM tloggings WHERE xuserxid = :xuserxid AND xrefid = :xrefid) "
					+ "AND (ipaddress = ANY(:ips::inet[]) OR (ipaddress IS NULL AND hostnameid = ANY(:hostids::BIGINT[]))))) AND l.xid IN (SELECT lx.xid FROM tloggings lx, xlinkloggings x, "
					+ "(SELECT DISTINCT ip::INET AS ipaddress FROM UNNEST(:ips::inet[]) AS ip) AS u, (SELECT DISTINCT UNNEST::bigint AS hostnameid FROM UNNEST(:hostids)) AS h WHERE lx.xid = l.xid AND x.xid = lx.xid "
					+ "AND ((X.IPADDRESS <= U.IPADDRESS AND X.ENDIPADDRESS >= U.IPADDRESS) OR (X.HOSTNAMEID = H.HOSTNAMEID)) "
					+ "UNION ALL SELECT lx.xid FROM tloggings lx, xlinklogginggroups x, xgenericpaths p, xlinkgeneric xg "
					+ "WHERE lx.xid = l.xid AND x.xid = lx.xid AND p.parentxid = x.groupxid AND p.xid = xg.xid AND (xg.ipaddress = ANY(:ips::inet[]) OR (xg.ipaddress IS NULL AND xg.hostnameid = ANY(:hostids::BIGINT[]))) "
					+ "UNION ALL SELECT lx.xid FROM tloggings lx, xlinklogginggroups x WHERE lx.xid = l.xid AND x.xid = lx.xid AND x.groupxid = -1 "
					+ "UNION ALL SELECT lx.xid FROM tloggings lx WHERE lx.xid = l.xid AND (SELECT COUNT(xid) FROM xlinklogginggroups x WHERE x.xid=lx.xid) = 0 AND (SELECT COUNT(xid) FROM xlinkloggings x WHERE x.xid=lx.xid) =0)";

			if (ArrayUtils.isNotEmpty(event.getIpAddress()) || ArrayUtils.isNotEmpty(event.getHostIds())) {
				sql.appendFilter(subclause);
				sql.setParameter("ips", ips);
				sql.setParameter("hostids", hostIds);
			}

			if (event.getSubuserId() > 0) {
				sql.appendFilterNamed("((l.xsubuserxid IS NULL OR l.xsubuserxid = -1) OR s.superuser=1 OR s.xpathdown~(',' || :xsubuserid || ','))", "xsubuserid",
						event.getSubuserId());
			}
			else if (event.getEvent() == O24Event.UserLogin) {
				sql.appendFilter("((l.xsubuserxid is NULL OR l.xsubuserxid = -1) OR s.superuser = 1)");
			}

			if (event.getEventType() == EventType.Was) {
				sql.appendFilter("((scantype & 2) = 2)");
			}
			else if (event.getEventType() == EventType.Normal) {
				sql.appendFilter("((scantype & 1) = 1)");
			}
			else if (event.getEventType() == EventType.PCI) {
				sql.appendFilter("((scantype & 4) = 4)");
			}
			else if (event.getEventType() == EventType.Swat) {
				sql.appendFilter("((scantype & 8) = 8)");
			}
		}
		return statementExec.execute(sql, UserEvent.class);
	}

	@Override
	public QueueLogEntry saveQueueLogEntry(final QueueLogEntry entry) {
		return queueLogRepository.save(entry);
	}

}
