package com.chilicoders.event.dao.impl;

import static com.chilicoders.event.model.Trigger.ANNOUNCEMENT_TYPE_TRIGGERS;
import static com.chilicoders.event.model.Trigger.USER_SCOPED_TRIGGERS;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Table;
import javax.xml.bind.JAXBException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

import com.chilicoders.db.query.JpaNativeStatementExecutor;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.event.dao.EventDao;
import com.chilicoders.event.entities.Activity;
import com.chilicoders.event.entities.Asset;
import com.chilicoders.event.entities.AssetGroup;
import com.chilicoders.event.entities.Check;
import com.chilicoders.event.entities.Comment;
import com.chilicoders.event.entities.ComplianceFinding;
import com.chilicoders.event.entities.ConsumptionStats;
import com.chilicoders.event.entities.EventSubscription;
import com.chilicoders.event.entities.Finding;
import com.chilicoders.event.entities.Integration;
import com.chilicoders.event.entities.IntegrationKey;
import com.chilicoders.event.entities.Match;
import com.chilicoders.event.entities.MatchJson;
import com.chilicoders.event.entities.ProductInformation;
import com.chilicoders.event.entities.ResourceGroup;
import com.chilicoders.event.entities.Scan;
import com.chilicoders.event.entities.ScanConfiguration;
import com.chilicoders.event.entities.Schedule;
import com.chilicoders.event.entities.SubUser;
import com.chilicoders.event.entities.User;
import com.chilicoders.event.entities.UserRole;
import com.chilicoders.event.entities.ViewTemplate;
import com.chilicoders.event.entities.Workflow;
import com.chilicoders.event.jpa.repository.ActivityRepository;
import com.chilicoders.event.jpa.repository.AssetGroupRepository;
import com.chilicoders.event.jpa.repository.AssetRepository;
import com.chilicoders.event.jpa.repository.CheckRepository;
import com.chilicoders.event.jpa.repository.CommentRepository;
import com.chilicoders.event.jpa.repository.ComplianceFindingRepository;
import com.chilicoders.event.jpa.repository.ConsumptionStatsRepository;
import com.chilicoders.event.jpa.repository.EventSubscriptionRepository;
import com.chilicoders.event.jpa.repository.FindingRepository;
import com.chilicoders.event.jpa.repository.IntegrationKeyRepository;
import com.chilicoders.event.jpa.repository.IntegrationRepository;
import com.chilicoders.event.jpa.repository.MatchRepository;
import com.chilicoders.event.jpa.repository.ProductInformationRepository;
import com.chilicoders.event.jpa.repository.ResourceGroupRepository;
import com.chilicoders.event.jpa.repository.ScanConfigurationRepository;
import com.chilicoders.event.jpa.repository.ScanLogRepository;
import com.chilicoders.event.jpa.repository.ScheduleRepository;
import com.chilicoders.event.jpa.repository.SubUserRepository;
import com.chilicoders.event.jpa.repository.UserRepository;
import com.chilicoders.event.jpa.repository.UserRoleRepository;
import com.chilicoders.event.jpa.repository.ViewTemplateRepository;
import com.chilicoders.event.jpa.repository.WorkflowRepository;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.FindingStatus;
import com.chilicoders.model.MatchType;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;

@Component
@AllArgsConstructor
@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally exposing internal representation")
public class EventDaoImpl implements EventDao {

	/**
	 * Logger used for this class.
	 */
	private static final Logger LOG = LogManager.getLogger(EventDaoImpl.class);

	private final JpaNativeStatementExecutor statementExec;

	private final EventSubscriptionRepository eventSubscriptionRepository;

	private final AssetRepository assetRepository;

	private final FindingRepository findingRepository;

	private final MatchRepository matchRepository;

	private final ResourceGroupRepository resourceGroupRepository;

	private final ScanConfigurationRepository scanConfigurationRepository;

	private final WorkflowRepository workflowRepository;

	private final ScanLogRepository scanRepository;

	private final ScheduleRepository scheduleRepository;

	private final UserRepository userRepository;

	private final SubUserRepository subUserRepository;

	private final ViewTemplateRepository viewTemplateRepository;

	private final UserRoleRepository userRoleRepository;

	private final IntegrationRepository integrationRepository;

	private final IntegrationKeyRepository integrationKeyRepository;

	private final ConsumptionStatsRepository consumptionStatsRepository;

	private final ComplianceFindingRepository complianceFindingRepository;

	private final CommentRepository commentRepository;

	private final AssetGroupRepository assetGroupRepository;

	private final ActivityRepository activityRepository;

	private final CheckRepository checkRepository;

	private final ProductInformationRepository productInformationRepository;

	@Override
	public List<EventSubscription> getEventSubscriptions(final Integer customerId, final Trigger trigger) {
		return eventSubscriptionRepository.findByCustomerIdAndTriggerAndDeletedIsNull(customerId, trigger.toString());
	}

	@Override
	public List<EventSubscription> getEventSubscriptionFromId(final Integer id) {
		return eventSubscriptionRepository.findByIdAndDeletedIsNull(id);
	}

	@Override
	public List<EventSubscription> getEventSubscriptionsFromAnnouncementTrigger(final Trigger trigger) {
		if (ANNOUNCEMENT_TYPE_TRIGGERS.contains(trigger)) {
			return eventSubscriptionRepository.findByTriggerAndDeletedIsNull(trigger.toString());
		}
		return new ArrayList<>();
	}

	@Override
	public List<EventSubscription> getUserScopedEventSubscriptions(final Trigger trigger, final Integer userId) {
		if (!USER_SCOPED_TRIGGERS.contains(trigger) || userId == null) {
			return new ArrayList<>();
		}
		return eventSubscriptionRepository.findByTriggerAndSettingsUserId(trigger.toString(), userId);
	}

	@Override
	public List<EventSubscription> getFindingTransitioningEventSubscriptions(final Integer customerId, final FindingStatus oldStatus, final FindingStatus newStatus) {
		final String jsonbNull = "null";
		return eventSubscriptionRepository.findByCustomerIdAndStatusTransitioning(customerId, oldStatus == null ? jsonbNull : oldStatus.name(),
				newStatus == null ? jsonbNull : newStatus.name());
	}

	@Override
	public List<EventSubscription> getEventSubscriptionsWithEntityType(final Integer customerId, final Trigger trigger, final String entityType) {
		return eventSubscriptionRepository.findByCustomerIdAndSettingEntityType(customerId, trigger.name(), entityType);
	}

	@Override
	public Asset getAssetFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Asset.class, assetRepository);
	}

	@Override
	public Finding getFindingFromId(final Integer id, final String filters) {
		final Finding finding = findByIdAndFilters(id, filters, Finding.class, findingRepository);
		final List<MatchJson> matches = new ArrayList<>();
		if (finding == null || finding.getMatchIds() == null) {
			return finding;
		}
		try {
			for (final Integer matchId : finding.getMatchIds()) {
				final Match match = getMatchFromId(matchId);
				if (match == null || match.getType() != MatchType.GATHEREDINFORMATION) {
					continue;
				}
				final MatchJson matchJson = match.getMatch();
				if (matchJson == null || StringUtils.isEmpty(matchJson.getGatheredInformation())) {
					continue;
				}
				matches.add(matchJson);
			}
			if (matches.size() > 0) {
				finding.setMatches(matches.toArray(new MatchJson[0]));
			}
		}
		catch (final JAXBException e) {
			LOG.warn("Could not set matches value on event finding.", e);
		}
		return finding;
	}

	@Override
	public Match getMatchFromId(final Integer id) {
		return matchRepository.findById(id).orElse(null);
	}

	@Override
	public ResourceGroup getResourceGroupFromId(final Integer id) {
		return resourceGroupRepository.findById(id).orElse(null);
	}

	@Override
	public ScanConfiguration getScanConfigurationFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, ScanConfiguration.class, scanConfigurationRepository);
	}

	@Override
	public Workflow getWorkflowFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Workflow.class, workflowRepository);
	}

	@Override
	public Scan getScanFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Scan.class, scanRepository);
	}

	@Override
	public Schedule getScheduleFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Schedule.class, scheduleRepository);
	}

	@Override
	public ComplianceFinding getComplianceFindingFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, ComplianceFinding.class, complianceFindingRepository);
	}

	@Override
	public Comment getCommentFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Comment.class, commentRepository);
	}

	@Override
	public AssetGroup getAssetGroupFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, AssetGroup.class, assetGroupRepository);
	}

	@Override
	public Activity getActivityFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Activity.class, activityRepository);
	}

	@Override
	public Check getCheckFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, Check.class, checkRepository);
	}

	@Override
	public ProductInformation getProductInformationFromId(final Integer id, final String filters) {
		return findByIdAndFilters(id, filters, ProductInformation.class, productInformationRepository);
	}

	@Override
	public User getUserFromId(final Integer id) {
		return userRepository.findById(id).orElse(null);
	}

	@Override
	public User getUserFromCustomerId(final Integer customerId) {
		return userRepository.findTopByCustomerId(customerId);
	}

	@Override
	public SubUser getSubUserFromId(final Integer id) {
		return subUserRepository.findById(id).orElse(null);
	}

	@Override
	public UserRole getUserRoleFromId(final Integer id) {
		return userRoleRepository.findById(id).orElse(null);
	}

	@Override
	public ViewTemplate getViewTemplateFromId(final Integer customerId, final Integer id) {
		return viewTemplateRepository.findByIdAndCustomerId(id, customerId);
	}

	@Override
	public ConsumptionStats getConsumptionStatsFromId(final Integer id) {
		return consumptionStatsRepository.findById(id).orElse(null);
	}

	@Override
	public Integration getIntegrationFromId(final Integer id) {
		return integrationRepository.findById(id).orElse(null);
	}

	@Override
	public Integration saveIntegration(final Integration integration) {
		return integrationRepository.save(integration);
	}

	@Override
	public IntegrationKey getIntegrationKeyFromCustomerId(final Integer customerId) {
		return integrationKeyRepository.findTopByCustomerId(customerId);
	}

	/**
	 * Select object with id and filters
	 *
	 * @param id id to select
	 * @param filters custom filters in where query
	 * @param classType class to cast object to
	 * @param <T> return reflection type
	 * @param repository repository extends JpaRepository
	 * @return object by id and custom filters
	 */
	private <T> T findByIdAndFilters(final Integer id, final String filters, final Class<T> classType, final JpaRepository<T, Integer> repository) {
		if (id == null) {
			return null;
		}

		if (StringUtils.isEmpty(filters)) {
			return repository.findById(id).orElse(null);
		}

		final NativeSqlStatement sql = new NativeSqlStatement();
		final Table table = classType.getAnnotation(Table.class);
		if (table == null) {
			return null;
		}
		sql.addSql("SELECT * FROM " + table.name() + " WHERE id = :id");
		sql.setParameter("id", id);
		sql.addSql(" AND " + filters);
		final List<T> list = statementExec.execute(sql, classType);
		return list.isEmpty() ? null : list.get(0);
	}
}
