package com.chilicoders.event.dao;

import java.util.List;

import com.chilicoders.event.entities.Activity;
import com.chilicoders.event.entities.Asset;
import com.chilicoders.event.entities.AssetGroup;
import com.chilicoders.event.entities.Check;
import com.chilicoders.event.entities.Comment;
import com.chilicoders.event.entities.ComplianceFinding;
import com.chilicoders.event.entities.ConsumptionStats;
import com.chilicoders.event.entities.EventSubscription;
import com.chilicoders.event.entities.Finding;
import com.chilicoders.event.entities.Integration;
import com.chilicoders.event.entities.IntegrationKey;
import com.chilicoders.event.entities.Match;
import com.chilicoders.event.entities.ProductInformation;
import com.chilicoders.event.entities.ResourceGroup;
import com.chilicoders.event.entities.Scan;
import com.chilicoders.event.entities.ScanConfiguration;
import com.chilicoders.event.entities.Schedule;
import com.chilicoders.event.entities.SubUser;
import com.chilicoders.event.entities.User;
import com.chilicoders.event.entities.UserRole;
import com.chilicoders.event.entities.ViewTemplate;
import com.chilicoders.event.entities.Workflow;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.FindingStatus;

/**
 * Dao for event data such as retrieval of object instances from its id.
 */
public interface EventDao {

	/**
	 * Returns the event subscription list.
	 *
	 * @param customerId the customer id
	 * @param trigger the event trigger
	 * @return the event subscription list
	 */
	List<EventSubscription> getEventSubscriptions(Integer customerId, Trigger trigger);

	/**
	 * Returns the event subscription
	 * @param id the event subscription id
	 * @return the event subscription
	 */
	List<EventSubscription> getEventSubscriptionFromId(Integer id);

	/**
	 * Returns the asset matching the id.
	 *
	 * @param id the asset id
	 * @param filters the filters
	 * @return the asset
	 */
	Asset getAssetFromId(Integer id, String filters);

	/**
	 * Returns the finding matching the id.
	 *
	 * @param id the finding id
	 * @param filters the filters
	 * @return the finding
	 */
	Finding getFindingFromId(Integer id, String filters);

	/**
	 * Returns the matches matching the id.
	 *
	 * @param id the matches id
	 * @return the Matches
	 */
	Match getMatchFromId(Integer id);

	/**
	 * Returns the resource group matching the id.
	 *
	 * @param id the resource group id
	 * @return the resource group
	 */
	ResourceGroup getResourceGroupFromId(Integer id);

	/**
	 * Returns the scan configuration the id.
	 *
	 * @param id the scan configuration id
	 * @param filters the filters
	 * @return the scan configuration
	 */
	ScanConfiguration getScanConfigurationFromId(Integer id, String filters);

	/**
	 * Returns the scan matching the id.
	 *
	 * @param id the scan id
	 * @param filters the filters
	 * @return the scan
	 */
	Scan getScanFromId(Integer id, String filters);

	/**
	 * Returns the schedule matching the id.
	 *
	 * @param id the schedule id
	 * @param filters the filters
	 * @return the schedule
	 */
	Schedule getScheduleFromId(Integer id, String filters);

	/**
	 * Returns the user matching the id.
	 *
	 * @param id the user id
	 * @return the user
	 */
	User getUserFromId(Integer id);

	/**
	 * Returns the user matching the id.
	 *
	 * @param id the customer id
	 * @return the user
	 */
	User getUserFromCustomerId(Integer id);

	/**
	 * Returns the user matching the id.
	 *
	 * @param id the sub user id
	 * @return the subuser
	 */
	SubUser getSubUserFromId(Integer id);

	/**
	 * Returns the user role matching the id.
	 *
	 * @param id the role id
	 * @return the user role
	 */
	UserRole getUserRoleFromId(Integer id);

	/**
	 * Returns the view template matching the id.
	 *
	 * @param customerId the customer id
	 * @param id the view template id
	 * @return the view template
	 */
	ViewTemplate getViewTemplateFromId(Integer customerId, Integer id);

	/**
	 * Returns the integration matching the id.
	 *
	 * @param id the integration id
	 * @return the user
	 */
	Integration getIntegrationFromId(Integer id);

	/**
	 * Save the integration.
	 *
	 * @param integration the integration
	 * @return integration
	 */
	Integration saveIntegration(Integration integration);

	/**
	 * Returns the integration matching the id.
	 *
	 * @param id the integration id
	 * @return the user
	 */
	IntegrationKey getIntegrationKeyFromCustomerId(Integer id);

	/**
	 * Returns the consumption stats matching the id.
	 *
	 * @param id the consumption stats id
	 * @return the user
	 */
	ConsumptionStats getConsumptionStatsFromId(Integer id);

	List<EventSubscription> getEventSubscriptionsFromAnnouncementTrigger(Trigger trigger);

	/**
	 * Returns the watcher-type event subscriptions matching trigger and user ID.
	 *
	 * @param trigger Watcher-type trigger
	 * @param userId user ID setting of the event subscription
	 * @return the list of matching event subscriptions
	 */
	List<EventSubscription> getUserScopedEventSubscriptions(Trigger trigger, Integer userId);

	/**
	 * Returns the finding status transition event subscriptions with matching configured from-to statuses.
	 *
	 * @param customerId The customer ID.
	 * @param oldStatus The from-status. 'null' means any.
	 * @param newStatus The to-status. 'null' means any.
	 * @return the list of matching event subscriptions
	 */
	List<EventSubscription> getFindingTransitioningEventSubscriptions(Integer customerId, FindingStatus oldStatus, FindingStatus newStatus);

	/**
	 * Returns the workflow matching the id.
	 *
	 * @param id the workflow id
	 * @param filters the filters
	 * @return the workflow
	 */
	Workflow getWorkflowFromId(Integer id, String filters);

	/**
	 * Return compliance finding by id and filters
	 *
	 * @param id complianceFinding id
	 * @param filters filter by string
	 * @return the ComplianceFinding
	 */
	ComplianceFinding getComplianceFindingFromId(Integer id, String filters);

	/**
	 * Returns event subscriptions with matching trigger and setting.entityType.
	 * Trigger types may include things like comment created, attachment uploaded, etc.
	 * Entity types may include finding, asset, asset group, etc.
	 *
	 * @param customerId The customer ID
	 * @param trigger The trigger
	 * @param entityType The entity type
	 * @return the event subscriptions
	 */
	List<EventSubscription> getEventSubscriptionsWithEntityType(Integer customerId, Trigger trigger, String entityType);

	/**
	 * Return comment by id and filters
	 *
	 * @param id comment id
	 * @param filters filter by string
	 * @return the {@link Comment}
	 */
	Comment getCommentFromId(Integer id, String filters);

	/**
	 * Return asset group by id and filters
	 *
	 * @param id asset group id
	 * @param filters filter by string
	 * @return the {@link AssetGroup}
	 */
	AssetGroup getAssetGroupFromId(Integer id, String filters);

	/**
	 * Return asset group activity by id and filters
	 *
	 * @param id activity id
	 * @param filters filter by string
	 * @return the {@link Activity}
	 */
	Activity getActivityFromId(Integer id, String filters);

	/**
	 * Return check by id and filters
	 *
	 * @param id check id
	 * @param filters filter by string
	 * @return the {@link Check}
	 */
	Check getCheckFromId(Integer id, String filters);

	/**
	 * Return product information by id and filters
	 *
	 * @param id product information id
	 * @param filters filter by string
	 * @return the {@link ProductInformation}
	 */
	ProductInformation getProductInformationFromId(Integer id, String filters);
}
