package com.chilicoders.event.entities;

import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.model.WorkflowInterface.Configuration;
import com.chilicoders.rest.adapters.InstantXmlAdapter;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;
import com.vladmihalcea.hibernate.type.array.IntArrayType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@TypeDefs({
		@TypeDef(name = "int-array", typeClass = IntArrayType.class),
		@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Entity
@Table(name = "workflowsview")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Workflow implements Serializable {
	@Getter
	private static final long serialVersionUID = 7613865038479000090L;

	@Getter
	@Setter
	@Id
	@Column
	private Integer id;

	@Getter
	@Setter
	private Integer customerId;

	/**
 	* The name of the scanner
 	*/
	@Getter
	@Setter
	private String scannerName;

	@Getter
	@Setter
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant created;

	@Getter
	@Setter
	private String createdBy;

	@Getter
	@Setter
	private Integer createdById;

	@Getter
	@Setter
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant updated;

	@Getter
	@Setter
	private String updatedBy;

	@Getter
	@Setter
	private Integer updatedById;

	@Getter
	@Setter
	private String name;

	@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
	@Getter
	@Setter
	@Type(type = "int-array")
	private Integer[] scheduleIds;

	@Getter
	@Setter
	private boolean enabled;

	@Getter
	@Setter
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant lastScan;

	private String tags;

	@Getter
	@Setter
	private Integer scannerId;

	@Type(type = "jsonb")
	private String configurations;

	@Getter
	@Setter
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant nextOccurrence;

	public Configuration[] getConfigurations() throws JAXBException {
		return this.configurations == null ? null : MarshallingUtils.unmarshalList(Configuration.class, this.configurations).toArray(new Configuration[0]);
	}

	public void setConfigurations(final Configuration[] configurations) throws JAXBException {
		this.configurations = configurations == null || configurations.length == 0
				? null : MarshallingUtils.marshalList(Configuration.class, Arrays.asList(configurations));
	}

	public String getTojson() throws JAXBException {
		return MarshallingUtils.marshal(this);
	}

	/**
	 * Get tags.
	 *
	 * @return Array with tag objects
	 */
	public Tag[] getTags() throws JAXBException {
		if (StringUtils.isEmpty(this.tags)) {
			return null;
		}
		@SuppressWarnings("unchecked")
		final Collection<Tag> tags = (Collection<Tag>) MarshallingUtils.unmarshal(Tag.class, this.tags);
		final Tag[] result = Tag.filterOutSystemTags(tags);
		return result.length == 0 ? null : result;
	}

	public void setTags(final Tag[] tags) throws JAXBException {
		this.tags = tags != null ? MarshallingUtils.marshal(tags) : null;
	}
}
