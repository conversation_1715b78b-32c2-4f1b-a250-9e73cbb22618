package com.chilicoders.event.entities;

import java.io.Serializable;
import java.time.Instant;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.event.model.Trigger;
import com.chilicoders.rest.models.EntityType;
import com.chilicoders.rest.models.SubEntityType;
import com.chilicoders.util.MarshallingUtils;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@TypeDefs({
	@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Entity
@Table(name = "notifications")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification implements Serializable {
	/**
	 * Serialization id.
	 */
	private static final long serialVersionUID = 4900407538759841311L;

	/**
	 * Notification id.
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "notifications_seq_generator")
	@SequenceGenerator(name = "notifications_seq_generator", sequenceName = "notifications_id_seq", allocationSize = 1)
	private Integer id;

	/**
	 * The customer ID.
	 */
	private Integer customerId;

	/**
	 * When this notification was last marked as read.
	 */
	private Instant read;

	/**
	 * The user ID which this notification is for.
	 */
	private Long userId;

	/**
	 * The subuser ID which this notification is for.
	 */
	private Long subUserId;

	/**
	 * The event subscription ID that generates this notification.
	 */
	private Integer eventSubscriptionId;

	/**
	 * The content of this notification, including a subject and a message
	 */
	@Type(type = "jsonb")
	private String content;

	public Content getContent() throws JAXBException {
		return MarshallingUtils.unmarshal(Content.class, this.content);
	}

	@Getter
	@Setter
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Schema(name = "Notification.Content")
	@XmlAccessorType(XmlAccessType.FIELD)
	public static class Content {
		@NotEmpty
		private String subject;

		private String message;
		
		private String url;

		@NotNull
		private Trigger trigger;

		private EntityType entityType;

		private Integer entityId;

		private SubEntityType subEntityType;

		private Integer subEntityId;
	}
}
