package com.chilicoders.event.entities;

import java.time.Instant;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.model.MatchType;
import com.chilicoders.model.Source;
import com.chilicoders.model.SubscriptionType;
import com.chilicoders.rest.adapters.InstantXmlAdapter;
import com.chilicoders.util.MarshallingUtils;
import com.vladmihalcea.hibernate.type.array.EnumArrayType;
import com.vladmihalcea.hibernate.type.array.IntArrayType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity for a match.
 */

@SuppressFBWarnings("EI_EXPOSE_REP2")
@TypeDefs({
		@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
		@TypeDef(name = "int-array", typeClass = IntArrayType.class),
		@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class),
		@TypeDef(name = "source_array_type",
				typeClass = EnumArrayType.class,
				parameters = {
						@Parameter(
								name = EnumArrayType.SQL_ARRAY_TYPE,
								value = "source"
						)
				}
		)
})

@Entity
@Table(name = "matchesview")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Match {
	/**
	 * Match id.
	 */
	@Id
	private Integer id;

	/**
	 * The ID of the customer that owns this match.
	 */
	private Integer customerId;

	/**
	 * When this match was created.
	 */
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant created;

	/**
	 * The user name of who created this match.
	 */
	private String createdBy;

	/**
	 * When this match was last updated.
	 */
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant updated;

	/**
	 * The user name of who updated this match.
	 */
	private String updatedBy;

	/**
	 * The asset Id 
	 */
	private Integer assetId;

	/**
	 * The match type
	 */
	@Enumerated(EnumType.STRING)
	private MatchType type;

	/**
	 * The fist time seen
	 */
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant firstSeen;

	/**
	 * The last time seen
	 */
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant lastSeen;

	/**
	 * The first scan id
	 */
	private Integer firstScanId;

	/**
	 * The last scan id
	 */
	private Integer lastScanId;

	/**
	 * The match at json format
	 */
	@Type(type = "jsonb")
	private String match;

	/**
	 * The findings ids
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP")
	@Type(type = "int-array")
	private Integer[] findingIds;

	/**
	 * The source
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP")
	@Enumerated(EnumType.STRING)
	@Type(type = "source_array_type")
	private Source[] source;

	/**
	 * the subscription type
	 */
	@Enumerated(EnumType.STRING)
	private SubscriptionType subscriptionType;

	/**
	 * The tags
	 */
	private String tags;

	/**
	 * Get match
	 *
	 * @return match
	 */
	public MatchJson getMatch() throws JAXBException {
		return this.match == null ? null : MarshallingUtils.unmarshal(MatchJson.class, this.match);
	}

	/**
	 * Set match
	 *
	 * @param match the match
	 */
	public void setMatch(final MatchJson match) throws JAXBException {
		this.match = match != null ? MarshallingUtils.marshal(match) : null;
	}
}
