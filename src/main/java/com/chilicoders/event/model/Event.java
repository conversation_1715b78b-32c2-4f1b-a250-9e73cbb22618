package com.chilicoders.event.model;

import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.function.Function;

import com.chilicoders.rest.models.EntityType;
import com.fasterxml.jackson.annotation.JsonIgnore;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

/**
 * A event that was triggered in the system. This will be sent to EventService which will handle them.
 */

@SuppressFBWarnings({"EI_EXPOSE_REP2", "EI_EXPOSE_REP"})
@Getter
@Setter
public class Event {

	/**
	 * Available settings in events.
	 */
	public enum Setting {
		TIMEBEFORE("timeBefore", Duration.class, Duration::parse),
		TIMESINCE("timeSince", Duration.class, Duration::parse),
		DISKUSAGE("diskUsage", String.class, s -> s),
		METRIC("metric", String.class, s -> s),
		LIMIT("limit", Integer.class, i -> i),
		USERID("userId", Integer.class, u -> u),
		ORIGINALTRIGGER("originalTrigger", Trigger.class, Trigger::valueOf),
		FROM("from", String.class, s -> s),
		TO("to", String.class, s -> s),
		ENTITYTYPE("entityType", EntityType.class, EntityType::valueOf);

		@Getter
		private final String key;

		private final Class<?> valueClass;

		private final Function<String, ?> valueConverter;

		/**
		 * Create a new enum instance.
		 *
		 * @param key the key used to identify this enum in a human-readable manner.
		 * @param valueClass the type of the value that this enum represents.
		 * @param valueConverter a function to convert the value to its type.
		 */
		Setting(final String key, final Class<?> valueClass, final Function<String, ?> valueConverter) {
			this.key = key;
			this.valueClass = valueClass;
			this.valueConverter = valueConverter;
		}

		@Override
		public String toString() {
			return this.getKey();
		}
	}

	public enum HandleMode {
		NORMAL, // event to be handled normally
		SKIP_WATCHER, // if this event may trigger watcher events, skip the watcher event
		WATCHER_ONLY // ignore this event and only handle the watcher events that it may trigger
	}

	/**
	 * Get a setting value from this event with the given type.
	 *
	 * @param setting the setting
	 * @param valueClass the class type of the value
	 * @param <T> the type of the value
	 * @return the value, if present
	 */
	public <T> T getSetting(final Setting setting, final Class<T> valueClass) {
		final String value = this.settings.get(setting);
		if (value == null) {
			throw new NoSuchElementException(setting.key);
		}

		if (!setting.valueClass.isAssignableFrom(valueClass)) {
			throw new ClassCastException("Cannot convert " + setting.valueClass.getSimpleName() + " to " + valueClass.getSimpleName());
		}

		@SuppressWarnings("unchecked")
		final Class<T> settingClass = (Class<T>) setting.valueClass;
		return settingClass.cast(setting.valueConverter.apply(value));
	}

	/**
	 * When this event was created.
	 */
	private Date timestamp;

	/**
	 * The trigger for this event.
	 */
	private Trigger trigger;

	/**
	 * The settings associated with this event.
	 */
	private Map<Setting, String> settings;

	/**
	 * The (event) objects associated with this event;
	 */
	private Map<EventObject.EventObjectIdentifier, EventObject> objects;

	/**
	 * The (event) objectsId associated with this event;
	 */
	private Map<EventObject.EventObjectIdentifier, Integer> objectIds;

	/**
	 * Other generic event data that are not {@link EventObject} or identifiable by ID (i.e. not database entities) in a similar manner as those in {@link com.chilicoders.event.entities}
	 * or configurable by user. The value should have simple type, such as a numeric, string, or array/list of numerics/strings etc.
	 * An example usage of this is for announcement type events, such as the version in a release note.
	 */
	private Map<EventObject.EventObjectIdentifier, Object> others;

	@JsonIgnore
	private HandleMode handleMode = HandleMode.NORMAL;
}
