package com.chilicoders.event.rest.events;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.objects.EventMetadata;
import com.chilicoders.model.FindingStatus;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

/**
 * The Finding event(s) model(s).
 */
@SuppressFBWarnings("EI_EXPOSE_REP")
@Getter
@Setter
public class FindingEvent {

	/**
	 * The event metadata for this event.
	 */
	private EventMetadata event;

	/**
	 * The finding id for this event.
	 */
	private Integer findingId;

	/**
	 * The user pertaining to this event.
	 */
	private com.chilicoders.event.objects.User user;

	/**
	 * The asset id.
	 */
	private Integer assetId;

	/**
	 * The scan schedule id pertaining to this event.
	 */
	private Integer scheduleId;

	/**
	 * The scan configuration id pertaining to this event.
	 */
	private Integer configurationId;

	/**
	 * The scan pertaining id to this event.
	 */
	private Integer scanId;

	/**
	 * Time before the finding expires.
	 */
	private Duration timeBefore;

	/**
	 * The event subscription id.
	 */
	private Integer eventSubscriptionId;

	/**
	 * Construct finding event model
	 *
	 * @param findingId The finding id
	 * @param customerId The customer id
	 * @param user The user who triggered the event
	 * @param scheduleId The schedule
	 * @param configurationId The scan configuration
	 * @param scanId The scan
	 * @param eventSubscriptionId The subscription id
	 * @param assetId The asset id
	 */
	public FindingEvent(final Integer findingId, final Integer customerId, final com.chilicoders.event.objects.User user, final Integer scheduleId,
						final Integer configurationId, final Integer scanId, final Integer eventSubscriptionId, final Integer assetId) {
		this.event = EventMetadata.builder().timestamp(Instant.now()).customerId(customerId).build();
		this.findingId = findingId;
		this.user = user;
		this.scheduleId = scheduleId;
		this.configurationId = configurationId;
		this.scanId = scanId;
		this.eventSubscriptionId = eventSubscriptionId;
		this.assetId = assetId;
	}

	/**
	 * Construct a FINDING_SEEN event from this model.
	 *
	 * @return the created event
	 */

	public final Event seen() {
		return toEvent(Trigger.FINDING_SEEN);
	}

	/**
	 * Construct a FINDING_CREATED event from this model.
	 *
	 * @return the created event
	 */
	public final Event created() {
		return toEvent(Trigger.FINDING_CREATED);
	}

	/**
	 * Construct a FINDING_MODIFIED event from this model.
	 *
	 * @return the created event
	 */

	public final Event modified() {
		return toEvent(Trigger.FINDING_MODIFIED);
	}

	/**
	 * Construct a FINDING_MODIFIED event by status transitioning.
	 * This event is distinguishable from other FINDING_MODIFIED events by the {@link Event#getHandleMode()} flag.
	 *
	 * @return the created event
	 */
	public final Event modifiedByTransitioning() {
		final Event event = toEvent(Trigger.FINDING_MODIFIED);
		event.setHandleMode(Event.HandleMode.SKIP_WATCHER);
		return event;
	}

	/**
	 * Construct a FINDING_RISK_ACCEPTED_EXPIRATION event from this model.
	 *
	 * @param timeBefore number of days from now / last trigger time to notify before finding acceptance expiring
	 * @return the created event
	 */
	public final Event riskAcceptedExpiration(final Integer timeBefore) {
		final Event event = toEvent(Trigger.FINDING_RISK_ACCEPTED_EXPIRATION);
		final Map<Event.Setting, String> settings = new HashMap<>();
		settings.put(Event.Setting.TIMEBEFORE, Objects.toString(timeBefore, "0"));
		event.setSettings(settings);
		return event;
	}

	/**
	 * Construct a FINDING_STATUS_TRANSITIONED event from this model.
	 *
	 * @param oldStatus The old status before the transition
	 * @param newStatus The new status after the transition
	 * @return the created event
	 */
	public final Event statusTransitioned(final FindingStatus oldStatus, final FindingStatus newStatus) {
		final Event event = toEvent(Trigger.FINDING_STATUS_TRANSITIONED);
		final Map<Event.Setting, String> settings = new HashMap<>();
		settings.put(Event.Setting.FROM, oldStatus.name());
		settings.put(Event.Setting.TO, newStatus.name());
		event.setSettings(settings);
		return event;
	}

	/**
	 * Construct an {@link Event} from this object model.
	 *
	 * @param trigger the trigger of the event
	 * @return the construct event object.
	 */
	private Event toEvent(final Trigger trigger) {
		final Event event = new Event();

		event.setTrigger(trigger);
		this.event.setTrigger(trigger);
		event.setTimestamp(Date.from(this.event.getTimestamp()));

		final Map<EventObject.EventObjectIdentifier, EventObject> objects = new HashMap<>();
		final Map<EventObject.EventObjectIdentifier, Integer> objectIds = new HashMap<>();
		objects.put(this.event.identify(), this.event);
		objectIds.put(EventObjectIdentifier.FINDING, this.findingId);

		if (user != null) {
			objects.put(this.user.identify(), this.user);
		}

		if (scheduleId != null) {
			objectIds.put(EventObjectIdentifier.SCHEDULE, this.scheduleId);
		}

		if (configurationId != null) {
			objectIds.put(EventObjectIdentifier.SCANCONFIGURATION, this.configurationId);
		}

		if (scanId != null) {
			objectIds.put(EventObjectIdentifier.SCAN, this.scanId);
		}

		if (eventSubscriptionId != null) {
			objectIds.put(EventObjectIdentifier.EVENT_SUBSCRIPTION, this.eventSubscriptionId);
		}

		if (assetId != null) {
			objectIds.put(EventObjectIdentifier.ASSET, this.assetId);
		}

		event.setObjects(objects);
		event.setObjectIds(objectIds);
		return event;
	}

}
