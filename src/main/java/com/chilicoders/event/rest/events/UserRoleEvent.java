package com.chilicoders.event.rest.events;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.objects.EventMetadata;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

/**
 * The User Role event(s) model(s).
 */
@Getter
@Setter
@SuppressFBWarnings("EI_EXPOSE_REP")
public class UserRoleEvent {

	/**
	 * The event metadata for this event.
	 */
	private EventMetadata event;

	/**
	 * The user role id for this event.
	 */
	private Integer userRoleId;

	/**
	 * The user pertaining to this event.
	 */
	private com.chilicoders.event.objects.User user;

	/**
	 * Construct user role event
	 *
	 * @param userRoleId the user role id
	 * @param customerId the customer id
	 * @param user the user event object
	 */
	public UserRoleEvent(final Integer userRoleId, final Integer customerId, final com.chilicoders.event.objects.User user) {
		this.event = EventMetadata.builder().timestamp(Instant.now()).customerId(customerId).build();
		this.userRoleId = userRoleId;
		this.user = user;
	}

	/**
	 * Construct a ROLE_CREATED event from this model.
	 *
	 * @return the created event
	 */
	public final Event created() {
		return toEvent(Trigger.ROLE_CREATED);
	}

	/**
	 * Construct a ROLE_DELETED event from this model.
	 *
	 * @return the created event
	 */
	public final Event deleted() {
		return toEvent(Trigger.ROLE_DELETED);
	}

	/**
	 * Construct a ROLE_DELETED event from this model.
	 *
	 * @return the created event
	 */
	public final Event modified() {
		return toEvent(Trigger.ROLE_MODIFIED);
	}

	/**
	 * Construct an {@link Event} from this object model.
	 *
	 * @param trigger the trigger of the event
	 * @return the construct event object.
	 */
	private Event toEvent(final Trigger trigger) {
		final Event event = new Event();
		event.setTrigger(trigger);
		this.event.setTrigger(trigger);
		event.setTimestamp(Date.from(this.event.getTimestamp()));

		final Map<EventObject.EventObjectIdentifier, EventObject> objects = new HashMap<>();
		final Map<EventObject.EventObjectIdentifier, Integer> objectIds = new HashMap<>();
		objects.put(this.event.identify(), this.event);
		objectIds.put(EventObjectIdentifier.USER_ROLE, this.userRoleId);

		if (user != null) {
			objects.put(this.user.identify(), this.user);
		}

		event.setObjects(objects);
		event.setObjectIds(objectIds);
		return event;
	}
}
