package com.chilicoders.event.rest.events;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.objects.EventMetadata;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Getter
@Setter
public class AssetGroupEvent {

	/**
	 * The event metadata for this event.
	 */
	private EventMetadata event;

	/**
	 * The asset group ID that this event applied to
	 */
	private Integer assetGroupId;

	/**
	 * The activity ID that the event applied to
	 */
	private Integer activityId;

	/**
	 * Construct an asset group event model
	 *
	 * @param customerId The customer ID
	 * @param assetGroupId The asset group ID
	 * @param activityId The activity ID
	 */
	public AssetGroupEvent(final Integer customerId, final Integer assetGroupId, final Integer activityId) {
		this.event = EventMetadata.builder().timestamp(Instant.now()).customerId(customerId).build();
		this.assetGroupId = assetGroupId;
		this.activityId = activityId;
	}

	/**
	 * Construct an EXECUTIVE_SUMMARY_UPDATED event from this model.
	 *
	 * @return the created event
	 */

	public final Event executiveSummaryUpdated() {
		return toEvent(Trigger.EXECUTIVE_SUMMARY_UPDATED);
	}

	/**
	 * Construct an ACTIVITY_FEED_UPDATED event from this model.
	 *
	 * @return the created event
	 */
	public final Event activityFeedUpdated() {
		return toEvent(Trigger.ACTIVITY_FEED_UPDATED);
	}

	/**
	 * Construct a ASSET_GROUP_CREATED event from this model.
	 *
	 * @return the created event
	 */
	public final Event created() {
		return toEvent(Trigger.ASSET_GROUP_CREATED);
	}

	/**
	 * Construct a ASSET_GROUP_DELETED event from this model.
	 *
	 * @return the deleted event
	 */
	public final Event deleted() {
		return toEvent(Trigger.ASSET_GROUP_DELETED);
	}

	/**
	 * Construct a ASSET_GROUP_MODIFIED event from this model.
	 *
	 * @return the modified event
	 */
	public final Event modified() {
		return toEvent(Trigger.ASSET_GROUP_MODIFIED);
	}

	/**
	 * Construct an {@link Event} from this object model.
	 *
	 * @param trigger the trigger of the event
	 * @return the construct event object.
	 */
	private Event toEvent(final Trigger trigger) {
		final Event event = new Event();

		event.setTrigger(trigger);
		this.event.setTrigger(trigger);
		event.setTimestamp(Date.from(this.event.getTimestamp()));

		final Map<EventObject.EventObjectIdentifier, EventObject> objects = new HashMap<>();
		final Map<EventObject.EventObjectIdentifier, Integer> objectIds = new HashMap<>();
		objects.put(this.event.identify(), this.event);
		objectIds.put(EventObject.EventObjectIdentifier.ASSET_GROUP, this.assetGroupId);

		if (this.activityId != null) {
			objectIds.put(EventObject.EventObjectIdentifier.ACTIVITY, this.activityId);
		}

		event.setObjects(objects);
		event.setObjectIds(objectIds);
		return event;
	}
}
