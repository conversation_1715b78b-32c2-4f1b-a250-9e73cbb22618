package com.chilicoders.event.rest.events;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.objects.EventMetadata;
import com.chilicoders.event.objects.User;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

/**
 * The Schedule event(s) model(s).
 */
@Getter
@Setter
@SuppressFBWarnings("EI_EXPOSE_REP")
public final class ScheduleEvent {

	/**
	 * The event metadata for this event.
	 */
	private EventMetadata event;

	/**
	 * The schedule id pertaining to this event.
	 */
	private Integer scheduleId;

	/**
	 * The event subscription id.
	 */
	private Integer eventSubscriptionId;

	/**
	 * The user pertaining to this event.
	 */
	private User user;

	/**
	 * Construct a schedule event
	 *
	 * @param scheduleId the schedule id
	 * @param customerId the customer id
	 * @param eventSubscriptionId the event subscription id
	 * @param user the user
	 */
	public ScheduleEvent(final Integer scheduleId, final Integer customerId, final User user, final Integer eventSubscriptionId) {
		this.event = EventMetadata.builder().timestamp(Instant.now()).customerId(customerId).build();
		this.scheduleId = scheduleId;
		this.eventSubscriptionId = eventSubscriptionId;
		this.user = user;
	}

	/**
	 * Construct a SCHEDULE_CREATED event from this model.
	 *
	 * @return the created event
	 */
	public final Event created() {
		return toEvent(Trigger.SCHEDULE_CREATED);
	}

	/**
	 * Construct a SCHEDULE_DELETED event from this model.
	 *
	 * @return the created event
	 */
	public final Event deleted() {
		return toEvent(Trigger.SCHEDULE_DELETED);
	}

	/**
	 * Construct a SCHEDULE_MODIFIED event from this model.
	 *
	 * @return the created event
	 */
	public final Event modified() {
		return toEvent(Trigger.SCHEDULE_MODIFIED);
	}

	/**
	 * Construct a SCHEDULE_SCHEDULED event from this model.
	 *
	 * @param timeBefore the number of days before the scheduled date
	 * @return the created event
	 */
	public final Event scheduled(final Integer timeBefore) {
		final Event event = toEvent(Trigger.SCHEDULE_SCHEDULED);

		final Map<Event.Setting, String> settings = new HashMap<>();
		settings.put(Event.Setting.TIMEBEFORE, timeBefore != null ? timeBefore.toString() + "d" : "0d");

		event.setSettings(settings);
		return event;
	}

	/**
	 * Construct an {@link Event} from this object model.
	 *
	 * @param trigger the trigger of the event
	 * @return the construct event object.
	 */
	private Event toEvent(final Trigger trigger) {
		final Event event = new Event();
		event.setTrigger(trigger);
		this.event.setTrigger(trigger);
		event.setTimestamp(Date.from(this.event.getTimestamp()));

		final Map<EventObject.EventObjectIdentifier, EventObject> objects = new HashMap<>();
		final Map<EventObject.EventObjectIdentifier, Integer> objectIds = new HashMap<>();
		objects.put(this.event.identify(), this.event);
		objectIds.put(EventObjectIdentifier.SCHEDULE, this.scheduleId);

		if (user != null) {
			objects.put(this.user.identify(), this.user);
		}

		if (eventSubscriptionId != null) {
			objectIds.put(EventObjectIdentifier.EVENT_SUBSCRIPTION, this.eventSubscriptionId);
		}

		event.setObjects(objects);
		event.setObjectIds(objectIds);
		return event;
	}
}
