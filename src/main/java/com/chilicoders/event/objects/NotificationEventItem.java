package com.chilicoders.event.objects;

import com.chilicoders.event.model.EventObject;
import com.chilicoders.rest.models.EntityType;
import com.chilicoders.rest.models.SubEntityType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationEventItem implements EventObject {

	/**
	 * The customer id.
	 */
	private Integer customerId;

	/**
	 * The event subscription ID
	 */
	private Integer eventSubscriptionId;

	/**
	 * The notification subject.
	 */
	private String subject;

	/**
	 * The notification message.
	 */
	private String message;

	/**
	 * The notification url.
	 */
	private String url;

	/**
	 * The user or sub-user ID.
	 */
	private Integer userId;

	/**
	 * The entity type.
	 */
	private EntityType entityType;

	/**
	 * The entity ID.
	 */
	private Integer entityId;

	/**
	 * The sub entity type.
	 */
	private SubEntityType subEntityType;

	/**
	 * The sub entity ID.
	 */
	private Integer subEntityId;

	@Override
	public EventObjectIdentifier identify() {
		return EventObjectIdentifier.NOTIFICATION;
	}
}
