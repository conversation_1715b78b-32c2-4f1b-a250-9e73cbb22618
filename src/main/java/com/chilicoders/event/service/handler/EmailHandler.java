package com.chilicoders.event.service.handler;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.MessagingException;

import org.apache.commons.io.FileUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.openpgp.PGPPublicKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.chilicoders.api.ReportExportApi;
import com.chilicoders.api.reportexport.ReportExportRequest;
import com.chilicoders.api.reportexport.ReportExportResponse;
import com.chilicoders.api.reportexport.ReportExportResponse.ExportResult;
import com.chilicoders.core.appliance.api.ApplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.email.api.EmailService;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.reporting.api.ReportTemplate;
import com.chilicoders.core.reporting.api.ReportingService;
import com.chilicoders.core.reporting.api.model.ReportDownloadEntryInterface;
import com.chilicoders.core.reporting.api.model.ReportFormat;
import com.chilicoders.core.reporting.api.model.ReportLevel;
import com.chilicoders.core.reporting.api.model.ReportOrigin;
import com.chilicoders.core.reporting.api.model.ReportStatus;
import com.chilicoders.core.reporting.api.model.ReportType;
import com.chilicoders.core.reporting.impl.jpa.repository.ReportDownloadEntryRepository;
import com.chilicoders.core.storage.api.LinkParameters;
import com.chilicoders.core.storage.api.StorageService;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.PresignedURLResponse;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.core.user.api.UserAttribute;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.db.ArrayUtil;
import com.chilicoders.event.config.ReportConfig;
import com.chilicoders.event.dao.UserEventDao;
import com.chilicoders.event.entities.QueueLogEntry;
import com.chilicoders.event.entities.UserEvent;
import com.chilicoders.event.service.EventHandler;
import com.chilicoders.event.service.EventKafkaMessageSender;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.filters.FilterName;
import com.chilicoders.filters.FilteringData;
import com.chilicoders.filters.FilteringUtils;
import com.chilicoders.filters.GroupSortInfo;
import com.chilicoders.model.Event;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.model.EventType;
import com.chilicoders.model.Product;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.Pgp;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TemplateUtils;
import com.chilicoders.util.TextConverter;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

/**
 * A handler that sends out emails from an event, based on the settings of a userevent.
 */
@Component
public class EmailHandler extends RecipientTypeHandler {
	/**
	 * Logger used for this class.
	 */
	private static final Logger LOG = LogManager.getLogger(EventHandler.class);

	/**
	 * User service.
	 */
	@Autowired
	private UserService userService;

	/**
	 * User event DAO.
	 */
	@Autowired
	private UserEventDao userEventDao;

	/**
	 * Configuration service.
	 */
	@Autowired
	private ConfigurationService configService;

	/**
	 * Message service.
	 */
	@Autowired
	private MessageService messageService;

	/**
	 * Reporting service.
	 */
	@Autowired
	private ReportingService reportingService;

	/**
	 * Email service.
	 */
	@Autowired
	private EmailService emailService;

	/**
	 * IP Service.
	 */
	@Autowired
	private ApplianceService applianceService;

	/**
	 * Report export API.
	 */
	@Autowired
	private ReportExportApi reportExportApi;

	@Autowired
	private StorageService storageService;

	@Autowired
	private ReportDownloadEntryRepository reportDownloadEntryRepository;

	@Autowired
	@Getter
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private ReportConfig reportConfig;

	@Autowired(required = false)
	@Qualifier("eventkafkaMessageSender")
	private EventKafkaMessageSender kafkaMessageSender;

	/**
	 * Constructs an EmailHandler.
	 */
	public EmailHandler() {
		super();
	}

	@Override
	public void send(final UserEvent userEvent, final Event event, final UserDetails user, final EventHandlerCache eventHandlerCache) throws SQLException, MessagingException {
		LOG.debug("Sending email for userEvent: " + userEvent.getId());
		event.getProperties().getInternalProperties(event.getEvent()).remove("DISCOVERYDELTA");
		event.getProperties().getInternalProperties(event.getEvent()).remove("DISCOVERY_DELTA_MSG");
		final Map<String, DataSource> files = new HashMap<>();
		if (event.getEvent() == O24Event.DiscoveryDone && userEvent.isOnlyOnPreviouslyActive()) {
			event.getProperties().getInternalProperties(event.getEvent()).put("DISCOVERYDELTA", "1");
			event.getProperties().getInternalProperties(event.getEvent()).put("DISCOVERY_DELTA_MSG", "_DISCOVERY_DELTA_MSG");
		}

		if (event.getEvent() == O24Event.ScanDone || event.getEvent() == O24Event.DiscoveryDone) {
			if (userEvent.isAttachReport() && !StringUtils.isEmpty(userEvent.getReportEncryptionKey())) {
				event.getProperties().getInternalProperties(event.getEvent()).put("ISDISCOVERY", event.getEvent() == O24Event.DiscoveryDone ? "1" : "0");
				if (event.getEvent() == O24Event.ScanDone && event.getEventType() == EventType.Normal) {
					event.getProperties().getInternalProperties(event.getEvent()).put("REPORTTEMPLATE", Long.toString(userEvent.getReportTemplate()));
				}
				if (!attachReport(userEvent, event, user, files)) {
					return;
				}
			}
			else {
				event.getProperties().getInternalProperties(event.getEvent()).remove("FILELIST");
				event.getProperties().getInternalProperties(event.getEvent()).remove("KEYMESSAGE");
			}
		}

		if (event.getEvent() == O24Event.LargeReport) {
			if ("5".equals(event.getProperties().getInternalProperties(event.getEvent()).get("reportinfo"))) {
				event.getProperties().getInternalProperties(event.getEvent()).put("REPORTINFO", messageService.getMessage("event.6.report", user.getLanguage(), "TEMPLATE"));
			}
			else if ("6".equals(event.getProperties().getInternalProperties(event.getEvent()).get("reportinfo"))) {
				event.getProperties().getInternalProperties(event.getEvent()).put("REPORTINFO", messageService.getMessage("event.6.stopped", user.getLanguage(), "TEMPLATE"));
			}

			event.getProperties().getInternalProperties(event.getEvent()).remove("reportinfo");
		}

		sendEmail(userEvent, event, user, files);
	}

	/**
	 * Sends the email to the recipient.
	 *
	 * @param userEvent User event being handled.
	 * @param event Event triggered.
	 * @param user User details.
	 * @param files Map of files, with readable names and DataSource.
	 */
	private void sendEmail(final UserEvent userEvent, final Event event, final UserDetails user, final Map<String, DataSource> files) throws SQLException {
		String template = "event.email." + event.getEvent().getId();
		if (!userEvent.isTargetInformation() && (event.getEvent().getId() <= O24Event.FindingHighRisk.getId()
				|| event.getEvent() == O24Event.ReportUpdated
				|| event.getEvent() == O24Event.ScanDone)) {
			template += ".notarget";
		}

		if ((event.getEvent() == O24Event.ScanDone || event.getEvent() == O24Event.DiscoveryDone) && TextConverter.toBoolean(
				event.getProperties().getInternalProperties(event.getEvent()).get("ISWAS"))) {
			template += ".was";
		}

		if (event.getEvent() == O24Event.DiscussionUpdated) {
			event.getProperties()
					.getInternalProperties(event.getEvent())
					.put("DISCUSSIONMSG", TemplateUtils.replaceTags(messageService.getMessage(template + "." + userEvent.getScanFormat(), user.getLanguage(),
									"TEMPLATE", user.getSalesOrganizationId()), user, event.getProperties().getInternalProperties(event.getEvent()), false, user.getLanguage(),
							configService, messageService, applianceService));
		}

		event.getProperties().getInternalProperties(event.getEvent()).put("EVENTNAME", userEvent.getEventName());
		event.getProperties().getInternalProperties(event.getEvent()).put("EVENTCREATEDBY", userEvent.getCreatedBy());
		event.getProperties().getInternalProperties(event.getEvent()).put("CUSTOMSUBJECT", TextConverter.coalesce(userEvent.getCustomSubject(), ""));
		event.getProperties().getInternalProperties(event.getEvent()).put("CUSTOMTEXT", TextConverter.coalesce(userEvent.getCustomText(), ""));
		event.getProperties().getInternalProperties(event.getEvent()).put("CUSTOMCONFIG", userEvent.isCustomConfig() && configService.isHiabEnabled() ? "1" : "0");
		if (userEvent.isCustomConfig() && configService.isHiabEnabled()) {
			if (event.getProperties().getInternalProperties(event.getEvent()).get("FIELDSBASEDMSG") == null) {
				final List<? extends UserAttribute> userAttributes = userService.getUserAttributes(user.getMainUserId(), event.getEvent().getAttributeMode());
				event.getProperties()
						.getInternalProperties(event.getEvent())
						.put("FIELDSBASEDMSG", buildFieldsBasedMessage(configService, userAttributes, event.getProperties().getInternalProperties(event.getEvent()),
								event.getEvent(), userEvent.getSyslogFields(), userEvent.getAttributeFields(), null, true));
			}
		}

		final String message = emailService.sendEmail(userEvent.getRecipient(), null, user, template, event.getProperties().getInternalProperties(event.getEvent()),
				userEvent.getEmailEncryptionKey(), files);
		LOG.debug("Email sent to: " + userEvent.getRecipient() + " due to event: " + userEvent.getEventName());
		final QueueLogEntry entry = new QueueLogEntry();
		entry.setData(message);
		entry.setEvent(event.getEvent().getId());
		entry.setSubuserId(userEvent.getSubuserId() == null ? -1 : userEvent.getSubuserId());
		entry.setInvalid(message == null);
		entry.setUserId(userEvent.getUserId());
		entry.setType(com.chilicoders.core.events.model.EventType.EMAIL.getValue());
		entry.setCreatedNow();
		userEventDao.saveQueueLogEntry(entry);
	}

	/**
	 * TODO: Also in reportexportbusiness
	 * Parses the state of the visible columns/filters/sorting and grouping from the UI.
	 *
	 * @param state The state string.
	 * @return An object, should be a {@code HashMap<String, Object>}
	 * @throws UnsupportedEncodingException When UTF8 is not supported, should never happen.
	 */
	private Object parseRegEx(final String state) throws UnsupportedEncodingException {
		final Pattern pattern = Pattern.compile("^(a|n|d|b|s|o)\\:(.*)$");
		final Matcher matcher = pattern.matcher(URLDecoder.decode(state, "UTF8"));
		if (!matcher.matches() || matcher.groupCount() != 2) {
			return null;
		}
		final char type = matcher.group(1).charAt(0);
		final String value = matcher.group(2);
		switch (type) {
			case 'n':
				return Float.parseFloat(value);
			case 'd':
				return DateUtils.parseDate(value);
			case 'b':
				return "1".equals(value);
			case 'a':
				final List<Object> list = new ArrayList<>();
				final String[] values = value.split("\\^");
				for (final String s : values) {
					list.add(this.parseRegEx(s));
				}
				return list;
			case 'o':
				final HashMap<String, Object> all = new HashMap<>();
				final String[] val = value.split("\\^");
				for (final String s : val) {
					final String[] kv = s.split("=");
					all.put(kv[0], this.parseRegEx(kv[1]));
				}
				return all;
			default:
				return value;
		}
	}

	/**
	 * Gets the group sort information from the request, creating it if it is not existing.
	 *
	 * @param request Report export request.
	 * @return Group and sort information.
	 */
	private GroupSortInfo getAndCreateGroupSortInfo(final ReportExportRequest request) {
		if (request.getGroupSortInfo() == null) {
			request.setGroupSortInfo(new GroupSortInfo());
		}
		return request.getGroupSortInfo();
	}

	/**
	 * TODO: This is in reportexportbusiness as well.
	 * Adds the filters to the HTTP request if any are available in the template.
	 *
	 * @param request The HTTP request.
	 * @param reportTemplate The report template.
	 */
	public void addFiltersToRequest(final ReportExportRequest request, final ReportTemplate reportTemplate) {
		if (reportTemplate == null) {
			return;
		}
		try {
			final String serverFilter = reportTemplate.getServerFilter();
			if (serverFilter != null) {
				final Map<String, String> parameters = new HashMap<>();
				final String[] parts = serverFilter.split("&");
				for (final String part : parts) {
					final String[] keyValue = part.split("=");
					if (keyValue.length == 2) {
						parameters.put(URLDecoder.decode(keyValue[0], "UTF8"), URLDecoder.decode(keyValue[1], "UTF8"));
					}
				}
				request.setFilteringData(new FilteringData(FilterName.FILTER, FilteringUtils.extractFilterData(parameters, FilterName.FILTER)));
				request.setSolutionFilteringData(new FilteringData(FilterName.SOLUTION_FILTER, FilteringUtils.extractFilterData(parameters, FilterName.SOLUTION_FILTER)));
				request.setFindingsFilteringData(new FilteringData(FilterName.FINDING_FILTER, FilteringUtils.extractFilterData(parameters, FilterName.FINDING_FILTER)));
				request.setTargetFilteringData(new FilteringData(FilterName.TARGET_FILTER, FilteringUtils.extractFilterData(parameters, FilterName.TARGET_FILTER)));
				request.setTargetPortFilteringData(
						new FilteringData(FilterName.TARGET_PORT_FILTER, FilteringUtils.extractFilterData(parameters, FilterName.TARGET_PORT_FILTER)));
				request.setTargetFindingsFilteringData(
						new FilteringData(FilterName.TARGET_FINDINGS_FILTER, FilteringUtils.extractFilterData(parameters, FilterName.TARGET_FINDINGS_FILTER)));
			}

			@SuppressWarnings("unchecked")
			final HashMap<String, Object> object = (HashMap<String, Object>) parseRegEx(reportTemplate.getState());

			final Object grouping = object.get("grouping");
			if (grouping instanceof String) {
				if (!StringUtils.isEmpty((String) object.get("grouping"))) {
					getAndCreateGroupSortInfo(request).setGroupBy((String) object.get("grouping"));
				}
			}

			@SuppressWarnings("unchecked")
			final HashMap<String, Object> sort = (HashMap<String, Object>) object.get("sort");
			if (sort != null) {
				final String field = (String) sort.get("field");
				final String direction = (String) sort.get("direction");
				final String column = (String) sort.get("column");
				if (!StringUtils.isEmpty(field)) {
					getAndCreateGroupSortInfo(request).setSort(field);
				}
				if (!StringUtils.isEmpty(direction)) {
					getAndCreateGroupSortInfo(request).setDirection(direction);
				}
				if (!StringUtils.isEmpty(column)) {
					request.setSortColumn(column);
				}
			}
		}
		catch (final UnsupportedEncodingException | ParamValidationException e) {
			LOG.error("Bad encoding", e);
		}
	}

	/**
	 * Add report to the email to be sent.
	 *
	 * @param userEvent The user defined event listener.
	 * @param event The event that trigger in the system.
	 * @param user The user that should get the report.
	 * @param files Files to be attached.
	 * @return true to continue with sending the email, false if this is handled via reportservice and kafka
	 */
	private boolean attachReport(final UserEvent userEvent, final Event event, final UserDetails user, final Map<String, DataSource> files) {
		if (event.getEventType() == EventType.PCI) {
			event.getProperties().getInternalProperties(event.getEvent()).put("KEYMESSAGE", messageService.getMessage("pciscandone", user.getLanguage(), "TEMPLATE"));
			return true;
		}
		boolean reportFailed = false;
		final Map<String, String> hm = event.getProperties() != null ? event.getProperties().getInternalProperties(event.getEvent()) : new HashMap<>();
		try {
			final ReportExportRequest newRequest = new ReportExportRequest();
			newRequest.setReportType(("1".equals(hm.get("PCI")) ? ReportType.Pci : "1".equals(hm.get("ISDISCOVERY")) ? ReportType.Discovery : ReportType.Technical));
			newRequest.setScheduleId(hm.get("SOXID"));
			newRequest.setScanJobIds(ArrayUtil.createIdArray(hm.get("SCANJOBXID")));
			newRequest.setTargetIds(ArrayUtil.createIdArray(hm.get("TARGETS")));
			newRequest.setCheckReportSize(true);
			newRequest.setDiscoveryDelta(com.chilicoders.util.StringUtils.getBooleanValue(hm.get("DISCOVERYDELTA")));
			newRequest.setReportPassword(userEvent.getReportPassword());
			newRequest.setUserId(user.getMainUserId());
			newRequest.setReportLevel(ReportLevel.Detailed);
			if (user.isSubUser()) {
				newRequest.setSubuserId(user.getSubUserId());
			}
			if (StringUtils.getBooleanValue(hm.get("ISWAS"))) {
				newRequest.setProduct(Product.Was);
			}

			final long reporttemplateId = TextConverter.toLong(hm.get("REPORTTEMPLATE"));
			if (reporttemplateId > 0) {
				final ReportTemplate reportTemplate = reportingService.getReportTemplate(user.getMainUserId(), reporttemplateId);
				if (reportTemplate != null) {
					addFiltersToRequest(newRequest, reportTemplate);
					newRequest.setReportTemplateName(TextConverter.coalesce(reportTemplate.getName(), ""));
					newRequest.setReportTemplate(reportTemplate.getId());
				}
			}

			final PGPPublicKey encryptionKey;
			if (Pgp.UNENCRYPTED_KEY.equals(userEvent.getReportEncryptionKey())) {
				encryptionKey = null;
			}
			else {
				encryptionKey = userService.getEmailEncryptionKey(user, userEvent.getReportEncryptionKey());
				if (encryptionKey == null) {
					LOG.debug("Encryption requested but no key available, ignoring report generation");
					hm.put("ENCRYPTIONFAILED", "1");
					reportFailed = true;
				}
			}

			if (reportConfig.isKafka() && userEvent.getReportType() > 0) {
				newRequest.setEvent(event);
				newRequest.setUserEvent(userEvent);
				kafkaMessageSender.send(newRequest);
				return false;
			}

			final String baseName = hm.get("JOBNAME") + "_" + hm.get("SCANJOBDATE");
			long totalSize = 0;
			for (int i = 0; i < ReportFormat.values().length && !reportFailed && totalSize <= configService.getProperty(ConfigKeys.ConfigurationIntKey.mail_maxmessagesize);
					i++) {
				if ((userEvent.getReportType() & (1 << i)) > 0) {
					newRequest.setReportFormat(ReportFormat.values()[i]);
					final String filename = baseName + "." + newRequest.getReportFormat().getExtension();
					newRequest.setReportName(filename);
					final ReportDownloadEntryInterface result = generateReport(user, newRequest);
					if (result == null || result.getStatus() != ReportStatus.DONE) {
						reportFailed = true;
					}
					else {
						String unencryptedFilename = result.getFilename();
						if (result.isCloudStorage()) {
							unencryptedFilename = result.downloadToTempFile(user, storageService);
						}
						if (encryptionKey != null) {
							final File tmpFile = File.createTempFile("report_", ".pgp");
							try (final FileOutputStream fos = new FileOutputStream(tmpFile)) {
								if (Pgp.getInstance(configService).signAndEncryptFile(unencryptedFilename, fos, true, encryptionKey)) {
									FileUtils.deleteQuietly(new File(unencryptedFilename));
									files.put(filename + ".pgp", new FileDataSource(tmpFile.getAbsolutePath()));
									totalSize += tmpFile.length();
								}
							}
						}
						else {
							files.put(filename, new FileDataSource(unencryptedFilename));
							totalSize += result.getSize();
						}
						reportDownloadEntryRepository.deleteByUserIdAndId(user.getMainUserId(), result.getId());
					}
				}
			}
			if (totalSize > configService.getProperty(ConfigKeys.ConfigurationIntKey.mail_maxmessagesize)) {
				files.values().forEach(item -> {
					if (item instanceof FileDataSource) {
						FileUtils.deleteQuietly(((FileDataSource) item).getFile());
					}
				});
				files.clear();
				hm.put("MESSAGETOOLARGE", "1");
			}

			if (!reportFailed && !files.isEmpty()) {
				hm.put("REPORTSENT", "1");
			}
			else if (TextConverter.toBoolean(hm.get("ENCRYPTIONFAILED"))) {
				hm.put("KEYMESSAGE", messageService.getMessage("noencryptionkeyfound", "en", "TEMPLATE"));
			}
			else if (reportFailed) {
				hm.put("KEYMESSAGE", messageService.getMessage("failed.generate.report", "en", "TEMPLATE"));
			}
		}
		catch (final RuntimeException | IOException e) {
			LOG.error("attachReport failed: ", e);
		}
		return true;
	}

	/**
	 * Generate report.
	 *
	 * @param user User details.
	 * @param request Report export request.
	 * @return Report download entry.
	 */
	private ReportDownloadEntryInterface generateReport(final UserDetails user, final ReportExportRequest request) {
		try {
			request.setScheduleType(ReportOrigin.EVENT);

			final ReportExportResponse response = reportExportApi.generateReport(request, configService.getProperty(ConfigurationKey.report_service_url));
			if (response == null) {
				LOG.error("Failed to generate report for event");
				return null;
			}
			if (response.getResultCode() != ExportResult.STARTED) {
				LOG.error("Error generating report: " + response.getErrorMessage());
				return null;
			}
			return reportingService.waitForReportGeneration(user, response.getReportDownloadEntryId());
		}
		catch (final SQLException | InterruptedException e) {
			LOG.error("Error downloading report", e);
		}
		return null;
	}

	/**
	 * Once the reports are added sends the email out to the user.
	 *
	 * @param event Event which is received from the report service. The files map contains all the exported reports.
	 */
	public void finalizeScanDoneEvent(final Event event) {
		try {
			final UserDetails user = event.getSubuserId() > 0
					? userService.getUserDetails(event.getSubuserId(), true) :
					userService.getUserDetails(event.getUserId(), false);
			final Map<String, DataSource> files = new HashMap<>();
			if (event.getFiles() != null) {
				LOG.debug("Found {} files", event.getFiles().size());
				event.getFiles().forEach((name, url) -> addFileToDataSources(url, name, user.getCustomerUuid(), event, files));
			}
			sendEmail(event.getUserEvent(), event, user, files);
		}
		catch (final Throwable e) {
			LOG.error("Error handling event", e);
		}
	}

	/**
	 * Add file to a map of data sources.
	 *
	 * @param filePath The file path.
	 * @param fileName The file name.
	 * @param tenantUuid The customer uuid.
	 * @param event A {@link Event} instance.
	 * @param dataSources A map of data sources.
	 */
	public void addFileToDataSources(final String filePath, final String fileName, final String tenantUuid, final Event event,
			final Map<String, DataSource> dataSources) {
		if (filePath.startsWith("s3://")) {
			try {
				final File tempFile = File.createTempFile("email", "s3");
				if (!configService.isKubernetesEnabled()) {
					LOG.debug("Downloading file: {}.", fileName);
					final ResourceIdentifier identifier = TenantResourceIdentifier.builder()
							.tenantUuid(tenantUuid)
							.domain(Domain.CACHE)
							.key(filePath.replace("s3://cache/", ""))
							.build();
					storageService.downloadResource(identifier, tempFile.getAbsolutePath());
					storageService.deleteResource(identifier);
				}
				else if (event.getPresignedUrlsMap() != null && event.getPresignedUrlsMap().containsKey(fileName)) {
					final PresignedURLResponse presignedURLResponseForDownload = PresignedURLResponse.getPresignedUrl(event.getPresignedUrlsMap().get(fileName),
							LinkParameters.Operation.GET.toString());
					if (presignedURLResponseForDownload != null) {
						LOG.debug("Downloading file: {} with presigned url.", fileName);
						try (CloseableHttpClient httpClient = SslUtils.buildHttpClientForPSProviders(configService)) {
							presignedURLResponseForDownload.downloadResource(httpClient, Files.newOutputStream(tempFile.toPath()));
							final PresignedURLResponse presignedURLResponseForDelete = PresignedURLResponse.getPresignedUrl(event.getPresignedUrlsMap().get(fileName),
									LinkParameters.Operation.DELETE.toString());
							if (presignedURLResponseForDelete != null) {
								LOG.debug("Deleting file: {} with presigned url.", fileName);
								presignedURLResponseForDelete.deleteResource(httpClient);
							}
						}
					}
				}
				dataSources.put(fileName, new FileDataSource(tempFile));
			}
			catch (final IOException e) {
				LOG.error("Error attaching report", e);
			}
		}
		else {
			dataSources.put(fileName, new FileDataSource(filePath));
		}
	}
}
