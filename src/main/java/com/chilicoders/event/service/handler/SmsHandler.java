package com.chilicoders.event.service.handler;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.appliance.api.ApplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.events.model.EventType;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.event.dao.UserEventDao;
import com.chilicoders.event.entities.QueueLogEntry;
import com.chilicoders.event.entities.UserEvent;
import com.chilicoders.model.Event;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TemplateUtils;

/**
 * A handler for sending out SMS as a response to a user defined event listener.
 */
@Component
public class SmsHandler extends RecipientTypeHandler {
	/**
	 * Logger used for this class.
	 */
	private static final Logger LOG = LogManager.getLogger(SmsHandler.class);

	/**
	 * User event DAO.
	 */
	@Autowired
	private UserEventDao userEventDao;

	/**
	 * Configuration service.
	 */
	@Autowired
	private ConfigurationService configService;

	/**
	 * Message service.
	 */
	@Autowired
	private MessageService messageService;

	/**
	 * IP service.
	 */
	@Autowired
	private ApplianceService applianceService;

	/**
	 * Constructs a new SmsHandler.
	 */
	public SmsHandler() {
		super();
	}

	@Override
	public void send(final UserEvent userEvent, final Event event, final UserDetails user, final EventHandlerCache eventHandlerCache)
			throws SQLException, MalformedURLException {
		final String notarget =
				(!userEvent.isTargetInformation() && (event.getEvent().getId() <= O24Event.FindingHighRisk.getId() || event.getEvent() == O24Event.ReportUpdated))
						? ".notarget"
						: "";
		String message = messageService.getMessage("event.sms." + event.getEvent().getId() + notarget, user.getLanguage(), "TEMPLATE");
		message = TemplateUtils.replaceTags(message, user, event.getProperties().getInternalProperties(event.getEvent()), false, user.getLanguage(), configService,
				messageService, applianceService);

		final String recipients = userEvent.getRecipient();
		if (!StringUtils.isEmpty(recipients)) {
			final String[] split = recipients.split(",|;");
			for (final String recipient : split) {
				final String trackingId = sendSms(message, recipient, user.getMainUserId());
				final QueueLogEntry log = new QueueLogEntry();
				log.setType(EventType.SMS.getValue());
				log.setTrackingId(trackingId);
				log.setInvalid(trackingId == null);
				log.setData(message);
				log.setUserId(user.getMainUserId());
				log.setSubuserId(user.getSubUserId());
				log.setCreatedNow();
				userEventDao.saveQueueLogEntry(log);
			}
		}
	}

	/**
	 * Send out an sms.
	 *
	 * @param message The message to send.
	 * @param recipient Recipient phone number.
	 * @param userId User id.
	 * @return The tracking id upon success, null otherwise.
	 */
	private String sendSms(final String message, final String recipient, final long userId) {
		if (configService.getProperty(ConfigurationBooleanKey.dev_sysout_sms)) {
			LOG.debug("Sending sms: " + message);
			return "";
		}
		final String retval;

		LOG.debug("sendSms: Sending SMS to " + recipient + " for user " + userId);
		try {
			if (StringUtils.isEmpty(message)) {
				LOG.warn("sendSms: SMS data empty");
				return null;
			}

			final int size = StringUtils.isEmpty(message) ? 0 : message.length();
			final int sizelimit = configService.getProperty(ConfigurationIntKey.sms_maxmessagesize);
			if (size > sizelimit) {
				LOG.warn("sendSms: SMS too large: " + size + "/" + sizelimit);
				return "";
			}

			retval = sendSpiriusSms(recipient, Long.toString(userId), message);

			LOG.debug("sendSms: Sent SMS to " + recipient + " for user " + userId + " with the return value " + retval);
		}
		catch (final Exception e) {
			LOG.error("sendSms: ", e);
			return null;
		}

		if (retval != null) {
			return retval;
		}
		LOG.error("sendSms: Something went wrong sending SMS to " + recipient);

		return null;
	}

	/**
	 * Send an SMS using the Spirius POST interface.
	 *
	 * @param to Number to send to (MSISDN international format).
	 * @param extId External ID to set.
	 * @param message The message to send.
	 * @return Response ID from Spirius.
	 */
	private String sendSpiriusSms(final String to, final String extId, final String message) throws IOException {
		final JSONObject request = new JSONObject();
		request.put("User", configService.getProperty(ConfigurationKey.sms_uid));
		request.put("Pass", configService.getProperty(ConfigurationKey.sms_pwd));
		request.put("To", to);
		// Changing "From" setting from "Outpost24" require a lot of preparations since some telephone operators require this to be pre-registered. Contact NSOC before changing this.
		request.put("From", "Outpost24");
		request.put("FromType", "A");
		request.put("Msg", message);
		request.put("ExtId", extId);

		final URL url = new URL(configService.getProperty(ConfigurationKey.sms_gateway));
		final HttpURLConnection connection = "https://".equals(url.getProtocol()) ? SslUtils.getTrustAllHttpsConnection(url) : (HttpURLConnection) url.openConnection();
		try {
			connection.setRequestMethod("POST");
			connection.setDoOutput(true);
			connection.setDoInput(true);
			connection.addRequestProperty("Content-Type", "application/json;charset=UTF-8");
			connection.getOutputStream().write(request.toString().getBytes(StandardCharsets.UTF_8));

			final int responseCode = connection.getResponseCode();
			final String response = IOUtils.toString(connection.getInputStream(), StandardCharsets.UTF_8);
			if (responseCode < HttpStatus.SC_OK || responseCode >= HttpStatus.SC_BAD_REQUEST) {
				LOG.error("Got response code: " + responseCode + ", URL: " + url + ", request: " + request + ", response: " + response);
				return null;
			}

			return response.split(",")[1].trim();
		}
		finally {
			connection.disconnect();
		}
	}
}
