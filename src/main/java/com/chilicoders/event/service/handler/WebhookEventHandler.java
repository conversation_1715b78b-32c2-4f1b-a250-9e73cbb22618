package com.chilicoders.event.service.handler;

import java.io.IOException;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.xml.bind.JAXBException;

import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.event.dao.EventDao;
import com.chilicoders.event.entities.Integration;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.EventObject;
import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.event.objects.EventMetadata;
import com.chilicoders.event.objects.WebhookEventItem;
import com.chilicoders.integrations.webhook.WebhookEngine;
import com.chilicoders.integrations.webhook.WebhookEngine.AccessTokenResponse;
import com.chilicoders.integrations.webhook.WebhookEngine.WebhookResponse;
import com.chilicoders.integrations.webhook.model.AuthenticationType;
import com.chilicoders.model.IntegrationInterface.WebhookIntegrationConfiguration;
import com.chilicoders.model.IntegrationInterface.WebhookIntegrationConfiguration.OauthV2AuthenticationConfiguration;
import com.chilicoders.model.IntegrationType;

@Component
public class WebhookEventHandler {

	/**
	 * Logger used for this class.
	 */
	private static final Logger LOG = LogManager.getLogger(WebhookEventHandler.class);

	/**
	 * portal event dao.
	 */
	@Autowired
	private EventDao eventDao;

	/**
	 * config service.
	 */
	@Autowired
	private ConfigurationService configService;

	/**
	 * ip service.
	 */
	@Autowired
	private IpService ipService;

	/**
	 * Receives an event from the system using a message queue and handles the message.
	 *
	 * @param event Event received from the system.
	 */
	public void handleEvent(final Event event) {
		// Note: The current implementation may require some enhancements as defined in DEV-6687
		final Map<EventObject.EventObjectIdentifier, EventObject> objects = event.getObjects();
		final EventMetadata eventMetadata = (EventMetadata) objects.get(EventObjectIdentifier.EVENT);

		final WebhookEventItem webhook = (WebhookEventItem) objects.get(EventObjectIdentifier.WEBHOOK);
		final Integer integrationId = webhook.getIntegrationId();
		final Integration integration = eventDao.getIntegrationFromId(integrationId);

		if (integration == null) {
			LOG.debug("The integration with id : " + integrationId + " was not found. Event date: " + eventMetadata.getTimestamp().toString());
			return;
		}

		if (integration.getType() != IntegrationType.WEBHOOK) {
			LOG.debug("The integration with id : " + integration.getId() + " has a wrong integration type: " + integration.getType());
			return;
		}

		try {
			final WebhookIntegrationConfiguration integrationConfiguration = (WebhookIntegrationConfiguration) integration.getConfiguration();
			final WebhookEngine webhookEngine = WebhookEngine.getInstance(integrationId, configService, integrationConfiguration, ipService, integration.getUpdated());

			if (integrationConfiguration.getAuthenticationConfiguration() != null
					&& integrationConfiguration.getAuthenticationConfiguration().getAuthenticationType() == AuthenticationType.OAUTH2_AUTHENTICATION) {
				final OauthV2AuthenticationConfiguration authenticationConf = (OauthV2AuthenticationConfiguration) integrationConfiguration.getAuthenticationConfiguration();
				if (!authenticationConf.isAuthorized()) {
					LOG.info("The webhook integration with id : "
							+ integration.getId()
							+ " failed to deliver content because the Oauth webhook integration is not authorized.");
					return;
				}
				final AccessTokenResponse accessTokenResponse = webhookEngine.checkAccessTokenValidity();
				if (accessTokenResponse != null) {
					if (accessTokenResponse.getStatus() == org.springframework.http.HttpStatus.OK) {
						final JSONObject responseJson = new JSONObject(accessTokenResponse.getContent());
						if (responseJson.has("access_token")) {
							authenticationConf.setAccessToken(responseJson.getString("access_token"));
							authenticationConf.setAccessTokenCreated(accessTokenResponse.getNow());
							authenticationConf.setAccessTokenExpiresIn(accessTokenResponse.getNow().plusSeconds(responseJson.getInt("expires_in")));
							authenticationConf.setRefreshToken(responseJson.getString("refresh_token"));
							authenticationConf.setAuthorized(true);
							integrationConfiguration.setAuthenticationConfiguration(authenticationConf);
							integration.setConfiguration(integrationConfiguration);
							eventDao.saveIntegration(integration);
							LOG.debug("Set access token to: " + authenticationConf.getAccessToken());
						}
					}
					else {
						authenticationConf.setAuthorized(false);
						authenticationConf.setErrorMessage(accessTokenResponse.getError());
						integrationConfiguration.setAuthenticationConfiguration(authenticationConf);
						integration.setConfiguration(integrationConfiguration);
						eventDao.saveIntegration(integration);
						LOG.info("The webhook integration with id : "
								+ integration.getId()
								+ " failed to deliver content because the Oauth webhook integration is not authorized.");
						return;
					}
				}
			}

			boolean done = false;
			int attempts = 0;
			final int maxRetries = webhook.getMaxRetries();
			final int retryInterval = webhook.getRetryInterval();
			while (!done) {
				LOG.debug("Sending webhook to {}", webhookEngine.getWebhookUrl());
				final WebhookResponse webhookResponse = webhookEngine.process(webhook);
				LOG.debug("HttpResponse: {}", webhookResponse.getContent());
				LOG.debug("Status code: {}, message: {}", webhookResponse.getStatusCode(), webhookResponse.getStatusMessage());
				switch (webhookResponse.getStatusCode()) {
					case HttpStatus.SC_OK:
					case HttpStatus.SC_CREATED:
					case HttpStatus.SC_ACCEPTED:
					case HttpStatus.SC_NO_CONTENT:
						LOG.debug("Successfully sending webhook to {}", webhookEngine.getWebhookUrl());
						LOG.debug("HttpResponse: {}", webhookResponse.getContent());
						done = true;
						break;
					default:
						if (attempts > maxRetries) {
							LOG.info("The integration with id : "
									+ integration.getId()
									+ " failed to deliver content after several attempts. "
									+ "Http status " + webhookResponse.getStatusCode() + ". "
									+ webhookResponse.getStatusMessage());
							return;
						}
						LOG.debug("Retry sending webhook to {}", webhookEngine.getWebhookUrl());
						attempts += 1;
						TimeUnit.SECONDS.sleep(retryInterval);
				}
			}
		}
		catch (final NoSuchAlgorithmException | KeyStoreException | CertificateException | KeyManagementException
					 | IOException | URISyntaxException | JAXBException | InterruptedException e) {
			LOG.info("The integration with id : " + integration.getId() + " failed to get webhook engine instance. " + e.getMessage(), e);
		}
	}
}
