package com.chilicoders.compliance;

import java.util.Date;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@DbTable(name = "tcompliancereports")
public class ComplianceReport extends XmlAble {

	public ComplianceReport() {
	}

	/**
	 * Create new compliance report.
	 *
	 * @param targetGroups List of target groups
	 * @param scanJobId Scanjob id
	 * @param policyId Compliance policy id
	 * @param userId User id
	 * @param subUserId Subuser id
	 */
	public ComplianceReport(final Long[] targetGroups, final long scanJobId, final long policyId, final long userId, final long subUserId) {
		date = new Date();
		this.targetGroups = targetGroups;
		this.scanJobId = scanJobId;
		this.policyId = policyId;
		this.userId = userId;
		this.subUserId = subUserId;
	}

	@DbField(id = true)
	private long xid;

	@DbField
	private Date date;

	@DbField
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private Long[] targetGroups;

	@DbField
	private long scanJobId;

	@DbField
	private long policyId;

	@DbField(name = "xuserxid")
	private long userId;

	@DbField(name = "xsubuserxid")
	private long subUserId;
}
