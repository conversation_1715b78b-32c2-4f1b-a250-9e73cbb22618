package com.chilicoders.compliance;

public enum ComplianceVersionType {
	Atleast,
	AtleastAllowMissing,
	Equals,
	None,
	AtMost,
	AtMostAllowMissing,
	Set,
	EqualsAllowMissing,
	RegEx,
	Unknown;

	/**
	 * Get ComplianceVersionType from string.
	 *
	 * @param data Type as string
	 * @return ComplianceVersionType
	 */
	public static ComplianceVersionType fromString(final String data) {
		for (final ComplianceVersionType type : values()) {
			if (type.name().equalsIgnoreCase(data)) {
				return type;
			}
		}
		return Unknown;
	}
}