package com.chilicoders.integrations.syslog.model;

import java.util.ArrayList;
import java.util.List;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

/**
 * Holds information about how to send data in syslog messages.
 */
@Getter
public class Event {
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private List<Integer> events = new ArrayList<>();

	private String paramName;

	@Setter
	private String uiName;

	@Setter
	private String syslogName;

	@Setter
	private String timeStamp;

	@Setter
	private String arcsightName;

	@Setter
	private String arcsightLabel;

	@Setter
	private int id;

	@Setter
	private String example;

	private String dependsOn;

	private boolean hiabOnly;

	/**
	 * Common information model, name for Splunk.
	 */
	@Setter
	private String cim;

	/**
	 * Set events from a commaseparated list.
	 *
	 * @param events Commaseparated list of ids.
	 */
	public void setEvent(final String events) {
		final String[] parts = events.split(",");
		for (final String part : parts) {
			this.events.add(Integer.parseInt(part));
		}
	}

	public void setHmName(final String paramName) {
		this.paramName = paramName;
	}

	public void setDep(final String dependsOn) {
		this.dependsOn = dependsOn;
	}

	public void setHiabOnly(final int value) {
		hiabOnly = value > 0;
	}
}
