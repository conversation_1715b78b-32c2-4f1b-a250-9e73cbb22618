package com.chilicoders.integrations;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import javax.xml.bind.JAXBException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.credentials.model.CredentialInterface;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.core.templaterendering.api.TemplateRenderer;
import com.chilicoders.integrations.cyberark.CyberArkEngine;
import com.chilicoders.integrations.cyberark.CyberArkEngine.CyberArkResponse;
import com.chilicoders.integrations.dao.IntegrationDao;
import com.chilicoders.integrations.delinea.DelineaEngine;
import com.chilicoders.integrations.delinea.model.DelineaResponse;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.IntegrationInterface;
import com.chilicoders.model.IntegrationInterface.CyberArkIntegrationConfiguration;
import com.chilicoders.model.IntegrationInterface.DelineaIntegrationConfiguration;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.rest.models.CredentialClassType;
import com.chilicoders.templaterendering.api.Jinja2RenderingRequest;
import com.chilicoders.util.StringUtils;
import com.google.common.collect.ImmutableMap;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@Service
public class IntegrationServiceImpl implements IntegrationService {
	private static final Logger LOG = LogManager.getLogger(IntegrationServiceImpl.class);

	@SuppressFBWarnings("EI_EXPOSE_REP2")
	@Autowired
	private ConfigurationService configurationService;

	@Autowired
	private IpService ipService;

	@Autowired
	private TemplateRenderer templateRenderer;

	@Autowired
	private IntegrationDao integrationDao;

	/**
	 * Constructor,
	 *
	 * @param configurationService Configuration service
	 * @param integrationDao Integration dao
	 * @param ipService Ip service
	 * @param templateRenderer Template renderer
	 */
	public IntegrationServiceImpl(final ConfigurationService configurationService, final IntegrationDao integrationDao, final IpService ipService,
			final TemplateRenderer templateRenderer) {
		this.configurationService = configurationService;
		this.integrationDao = integrationDao;
		this.ipService = ipService;
		this.templateRenderer = templateRenderer;
	}

	@Override
	public IntegrationInterface getIntegration(final Integer integrationId, final Integer customerId) throws SQLException {
		return this.integrationDao.getById(integrationId, customerId);
	}

	@Override
	public String getIntegrationContent(final IntegrationInterface integration, final AssetInterface asset, final Map<Integer, CredentialInterface> credentials,
										final Map<IntegrationType, List<String>> issues) {
		try {
			if (integration.getType() == IntegrationType.CYBERARK) {
				issues.putIfAbsent(integration.getType(), new ArrayList<>());
				final CyberArkIntegrationConfiguration configuration = (CyberArkIntegrationConfiguration) integration.getConfiguration();

				CredentialInterface credential = credentials.get(CredentialClassType.CYBERARK_FOLDER.getId());
				final String folder = credential == null ? null : credential.decodeValue(this.configurationService);
				credential = credentials.get(CredentialClassType.CYBERARK_SAFE.getId());
				final String safe = credential == null ? null : credential.decodeValue(this.configurationService);
				credential = credentials.get(CredentialClassType.CYBERARK_OBJECT.getId());
				String object = "";
				if (credential != null) {
					credential.decodeValue(this.configurationService);
					if (credential.isJinjaValue()) {
						if (asset != null) {
							final Map<String, Object> binding = new HashMap<>();
							binding.put("asset", asset);

							object = this.templateRenderer.render(Jinja2RenderingRequest.builder()
									.templates(ImmutableMap.of("credential", credential.getValue()))
									.variables(binding)
									.build()).values().stream().findFirst().orElse(null);
						}
					}
					else {
						object = credential.getValue();
					}
				}

				if (StringUtils.isEmpty(object)) {
					issues.get(integration.getType()).add(createIssue(integration, "Object not defined"));
					return null;
				}

				final CyberArkEngine cyberark = CyberArkEngine.getInstance(integration.getId(), configurationService, configuration, ipService);
				final CyberArkResponse response = cyberark.getContent(safe, folder, object);
				if (response.getStatus() == HttpStatus.OK) {
					return response.getContent();
				}

				if (!StringUtils.isEmpty(response.getError())) {
					issues.get(integration.getType()).add(createIssue(integration, response.getError()));
				}
			}
			else if (integration.getType() == IntegrationType.DELINEA) {
				issues.putIfAbsent(integration.getType(), new ArrayList<>());
				final DelineaIntegrationConfiguration configuration = (DelineaIntegrationConfiguration) integration.getConfiguration();

				CredentialInterface credential = credentials.get(CredentialClassType.DELINEA_FOLDER.getId());
				final String folder = credential == null ? null : credential.decodeValue(this.configurationService);
				credential = credentials.get(CredentialClassType.DELINEA_SECRET.getId());
				String secret = "";
				if (credential != null) {
					credential.decodeValue(this.configurationService);
					if (credential.isJinjaValue()) {
						if (asset != null) {
							final Map<String, Object> binding = new HashMap<>();
							binding.put("asset", asset);

							secret = this.templateRenderer.render(Jinja2RenderingRequest.builder()
									.templates(ImmutableMap.of("credential", credential.getValue()))
									.variables(binding)
									.build()).values().stream().findFirst().orElse(null);
						}
					}
					else {
						secret = credential.getValue();
					}
				}

				if (StringUtils.isEmpty(secret)) {
					issues.get(integration.getType()).add(createIssue(integration, "Secret not defined"));
					return null;
				}

				final DelineaEngine delinea = DelineaEngine.getInstance(integration.getId(), this.configurationService, configuration, this.ipService);
				final DelineaResponse response = delinea.getContent(folder, secret);
				if (response.getStatus() == HttpStatus.OK) {
					return response.getContent();
				}

				if (!StringUtils.isEmpty(response.getError())) {
					issues.get(integration.getType()).add(createIssue(integration, response.getError()));
				}
			}
		}
		catch (final JAXBException | ExecutionException e) {
			LOG.error("Could not get content from integration", e);
		}

		return null;
	}

	/**
	 * Create issue JSON.
	 *
	 * @param integration Integration
	 * @param reason Reason
	 * @return Issue JSON
	 */
	private String createIssue(final IntegrationInterface integration, final String reason) {
		final IssueType issueType;
		switch (integration.getType()) {
			case CYBERARK:
				issueType = IssueType.CYBERARK_RETRIEVAL_FAILED;
				break;
			case DELINEA:
				issueType = IssueType.DELINEA_RETRIEVAL_FAILED;
				break;
			default:
				issueType = null;
				break;
		}

		final JSONObject issueJson = new JSONObject();
		issueJson.put("type", issueType == null ? null : issueType.name());
		final JSONObject integrationJson = new JSONObject();
		integrationJson.put("id", integration.getId());
		integrationJson.put("name", integration.getName());
		final JSONObject dataJson = new JSONObject();
		dataJson.put("reason", reason);
		dataJson.put("integration", integrationJson);
		issueJson.put("data", dataJson);
		return issueJson.toString();
	}
}
