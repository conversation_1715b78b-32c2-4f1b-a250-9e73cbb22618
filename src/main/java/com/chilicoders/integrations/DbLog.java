package com.chilicoders.integrations;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.reporting.api.model.Finding;
import com.chilicoders.core.reporting.api.model.Ticket;
import com.chilicoders.core.reporting.api.model.VulnerabilityComment;
import com.chilicoders.core.reporting.api.model.VulnerabilityReference;
import com.chilicoders.core.scandata.api.ScanPolicyService;
import com.chilicoders.core.scandata.api.ScanlogService;
import com.chilicoders.core.scandata.api.ScheduleService;
import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.model.FarsightProduct;
import com.chilicoders.db.objects.api.ScanLogInterface;
import com.chilicoders.db.objects.api.ScanPolicyInterface;
import com.chilicoders.db.objects.api.ScheduleInterface;
import com.chilicoders.model.Event;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.Template;
import com.chilicoders.model.events.properties.EventProperties;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.ReportMessageUtils;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Stores finding information in an external database.
 */
@Component
@Scope("prototype")
public class DbLog {
	private static final Logger LOG = LogManager.getLogger(DbLog.class);

	private static final String SQL = "sqlserver";
	private static final String MYSQL = "mysql";
	private static final String ACCESS = "access";
	private static final String ORACLE = "oracle:thin";
	private static final String POSTGRE = "postgresql";

	private int dbType = -1;
	private static final int DBTYPE_MSSQL = 0;
	private static final int DBTYPE_MYSQL = 1;
	public static final int DBTYPE_POSTGRES = 2;

	private static final String[] PROTOCOLS = {SQL, MYSQL, POSTGRE, ACCESS, ORACLE};
	private static final String[] DRIVERS =
			{"com.microsoft.sqlserver.jdbc.SQLServerDriver", "com.mysql.jdbc.Driver", "org.postgresql.Driver", "", "oracle.jdbc.driver.OracleDriver"};
	private static final int[] DEFAULTPORTS = {1433, 3306, 5432};

	private final ConfigurationService configurationService;

	private final ScanlogService scanlogService;

	private final ScanPolicyService scanPolicyService;

	private final ScheduleService scheduleService;

	private final MessageService messageService;

	private static final String SCAN_JOBID = "SCAN_JOBID";
	private static final String SCHEDULE_ID = "SCHEDULE_ID";
	private static final String REPORT_ID = "REPORT_ID";
	private static final String SCHEDULE_NAME = "SCHEDULE_NAME";
	private static final String SCAN_START_DATE = "SCAN_START_DATE";
	private static final String SCAN_END_DATE = "SCAN_END_DATE";
	private static final String SCAN_POLICY = "SCAN_POLICY";
	private static final String IS_WAS_ENABLED = "IS_WAS_ENABLED";
	private static final String HOST = "HOST";
	private static final String HOSTNAME = "HOSTNAME";
	private static final String NETBIOS = "NETBIOS";
	private static final String PLATFORM = "PLATFORM";
	private static final String REPORT_DATE = "REPORT_DATE";
	private static final String SCRIPT_ID = "SCRIPT_ID";
	private static final String SCRIPT_NAME = "SCRIPT_NAME";
	private static final String PORT = "PORT";
	private static final String PROTOCOL = "PROTOCOL";
	private static final String RISK_FACTOR = "RISK_FACTOR";
	private static final String CVSS_SCORE = "CVSS_SCORE";
	private static final String FAMILY = "FAMILY";
	private static final String PRODUCT = "PRODUCT";
	private static final String SOLUTION = "SOLUTION";
	private static final String CVE = "CVE";
	private static final String BUGTRAQ = "BUGTRAQ";
	private static final String IS_VERIFIED = "IS_VERIFIED";
	private static final String IS_NEW = "IS_NEW";
	private static final String FIRST_SEEN = "FIRST_SEEN";
	private static final String LAST_SEEN = "LAST_SEEN";
	private static final String SCANNER_NAME = "SCANNER_NAME";
	private static final String SERVICE_NAME = "SERVICE_NAME";
	private static final String HAS_EXPLOITS = "HAS_EXPLOITS";
	private static final String IS_COMPLIANT = "IS_COMPLIANT";
	private static final String VUL_TYPE = "VUL_TYPE";
	private static final String IS_RISK_ACCEPTED = "IS_RISK_ACCEPTED";
	private static final String ACCEPTANCE_EXP = "ACCEPTANCE_EXP";
	private static final String ACCEPTED_BY = "ACCEPTED_BY";
	private static final String ACCEPTED_DATE = "ACCEPTED_DATE";
	private static final String IS_FALSE_POSITIVE = "IS_FALSE_POSITIVE";
	private static final String FALSE_POSITIVE_COMMENTS = "FALSE_POSITIVE_COMMENTS";
	private static final String FALSE_POSITIVE_BY = "FALSE_POSITIVE_BY";
	private static final String INFORMATION = "INFORMATION";
	private static final String CVSSVECTOR = "CVSSVECTOR";
	// DB migration 2022-04-13-01: 6 new columns added to the table
	private static final String CYRATING = "CYRATING";
	private static final String EXPLOIT_PROBABILITY = "EXPLOIT_PROBABILITY";
	private static final String CYRATING_DELTA = "CYRATING_DELTA";
	private static final String EXPLOIT_PROBABILITY_DELTA = "EXPLOIT_PROBABILITY_DELTA";
	private static final String CYRATING_UPDATED = "CYRATING_UPDATED";
	private static final String CYRATING_LASTSEEN = "CYRATING_LASTSEEN";
	// DB migration 2022-07-06: 3 new columns added to the table
	private static final String CVSSV3_SCORE = "CVSSV3_SCORE";
	private static final String CVSSV3_SEVERITY = "CVSSV3_SEVERITY";
	private static final String CVSSV3_VECTOR = "CVSSV3_VECTOR";
	// DB migration 2022-10-17: 41 new columns added to the table
	private static final String DESCRIPTION = "DESCRIPTION";
	private static final String SOLUTION_TYPE = "SOLUTION_TYPE";
	private static final String FINDING_REFERENCE = "FINDING_REFERENCE";
	private static final String AGE = "AGE";
	private static final String IS_PREVIOUSLY_DETECTED = "IS_PREVIOUSLY_DETECTED";
	private static final String IS_POTENTIAL_FALSE_POSITIVE = "IS_POTENTIAL_FALSE_POSITIVE";
	private static final String ACCEPT_COMMENT = "ACCEPT_COMMENT";
	private static final String NVD_CVSS_SCORE = "NVD_CVSS_SCORE";
	private static final String VIRTUAL_HOST = "VIRTUAL_HOST";
	private static final String PATCH_INFORMATION = "PATH_INFORMATION";
	private static final String IS_NEW_OR_FIXED = "IS_NEW_OR_FIXED";
	private static final String FALSE_POSITIVE_DATE = "FALSE_POSITIVE_DATE";
	private static final String OVERRIDE_COMMENT = "OVERRIDE_COMMENT";
	private static final String FINDING_ID = "FINDING_ID";
	private static final String CUSTOM_0 = "CUSTOM_0";
	private static final String CUSTOM_1 = "CUSTOM_1";
	private static final String CUSTOM_2 = "CUSTOM_2";
	private static final String CUSTOM_3 = "CUSTOM_3";
	private static final String CUSTOM_4 = "CUSTOM_4";
	private static final String CUSTOM_5 = "CUSTOM_5";
	private static final String CUSTOM_6 = "CUSTOM_6";
	private static final String CUSTOM_7 = "CUSTOM_7";
	private static final String CUSTOM_8 = "CUSTOM_8";
	private static final String CUSTOM_9 = "CUSTOM_9";
	private static final String TARGET_CUSTOM_0 = "TARGET_CUSTOM_0";
	private static final String TARGET_CUSTOM_1 = "TARGET_CUSTOM_1";
	private static final String TARGET_CUSTOM_2 = "TARGET_CUSTOM_2";
	private static final String TARGET_CUSTOM_3 = "TARGET_CUSTOM_3";
	private static final String TARGET_CUSTOM_4 = "TARGET_CUSTOM_4";
	private static final String TARGET_CUSTOM_5 = "TARGET_CUSTOM_5";
	private static final String TARGET_CUSTOM_6 = "TARGET_CUSTOM_6";
	private static final String TARGET_CUSTOM_7 = "TARGET_CUSTOM_7";
	private static final String TARGET_CUSTOM_8 = "TARGET_CUSTOM_8";
	private static final String TARGET_CUSTOM_9 = "TARGET_CUSTOM_9";
	private static final String FARSIGHT_MENTIONS = "FARSIGTH_MENTIONS";
	private static final String FARSIGHT_THREAT_ACTORS = "FARSIGHT_THREAT_ACTORS";
	private static final String FARSIGHT_EXPLOITS = "FARSIGHT_EXPLOITS";
	private static final String BUSINESS_CRITICALITY = "BUSINESS_CRITICALITY";
	private static final String IS_EXPOSED = "IS_EXPOSED";
	private static final String INSTANCE_ID = "INSTANCE_ID";
	private static final String ARN = "ARN";
	// DB migration 2022-11-02: 3 new columns added to the table
	private static final String TASK = "TASK";
	private static final String VULNERABILITY_COMMENTS = "VULNERABILITY_COMMENTS";
	private static final String CVSS_VECTOR_DESCRIPTION = "CVSS_VECTOR_DESCRIPTION";
	private final String[] columns = {
			SCAN_JOBID,
			IS_WAS_ENABLED,
			SCAN_START_DATE,
			SCAN_END_DATE,
			SCHEDULE_NAME,
			SCHEDULE_ID,
			REPORT_ID,
			SCAN_POLICY,
			HOST,
			HOSTNAME,
			NETBIOS,
			PLATFORM,
			REPORT_DATE,
			SCRIPT_ID,
			SCRIPT_NAME,
			PORT,
			PROTOCOL,
			RISK_FACTOR,
			CVSS_SCORE,
			FAMILY,
			PRODUCT,
			IS_FALSE_POSITIVE,
			FALSE_POSITIVE_COMMENTS,
			FALSE_POSITIVE_BY,
			SOLUTION,
			CVE,
			BUGTRAQ,
			IS_VERIFIED,
			FIRST_SEEN,
			LAST_SEEN,
			IS_RISK_ACCEPTED,
			ACCEPTANCE_EXP,
			SCANNER_NAME,
			HAS_EXPLOITS,
			IS_COMPLIANT,
			SERVICE_NAME,
			VUL_TYPE,
			IS_NEW,
			INFORMATION,
			ACCEPTED_BY,
			ACCEPTED_DATE,
			CVSSVECTOR,
			CYRATING,
			EXPLOIT_PROBABILITY,
			CYRATING_DELTA,
			EXPLOIT_PROBABILITY_DELTA,
			CYRATING_UPDATED,
			CYRATING_LASTSEEN,
			CVSSV3_SCORE,
			CVSSV3_SEVERITY,
			CVSSV3_VECTOR,
			DESCRIPTION,
			SOLUTION_TYPE,
			FINDING_REFERENCE,
			AGE,
			IS_PREVIOUSLY_DETECTED,
			IS_POTENTIAL_FALSE_POSITIVE,
			ACCEPT_COMMENT,
			NVD_CVSS_SCORE,
			VIRTUAL_HOST,
			PATCH_INFORMATION,
			IS_NEW_OR_FIXED,
			FALSE_POSITIVE_DATE,
			OVERRIDE_COMMENT,
			FINDING_ID,
			CUSTOM_0,
			CUSTOM_1,
			CUSTOM_2,
			CUSTOM_3,
			CUSTOM_4,
			CUSTOM_5,
			CUSTOM_6,
			CUSTOM_7,
			CUSTOM_8,
			CUSTOM_9,
			TARGET_CUSTOM_0,
			TARGET_CUSTOM_1,
			TARGET_CUSTOM_2,
			TARGET_CUSTOM_3,
			TARGET_CUSTOM_4,
			TARGET_CUSTOM_5,
			TARGET_CUSTOM_6,
			TARGET_CUSTOM_7,
			TARGET_CUSTOM_8,
			TARGET_CUSTOM_9,
			FARSIGHT_MENTIONS,
			FARSIGHT_THREAT_ACTORS,
			FARSIGHT_EXPLOITS,
			BUSINESS_CRITICALITY,
			IS_EXPOSED,
			INSTANCE_ID,
			ARN,
			TASK,
			VULNERABILITY_COMMENTS,
			CVSS_VECTOR_DESCRIPTION
	};

	/**
	 * Constructor.
	 *
	 * @param configurationService Configuration service.
	 * @param scanlogService Scan log service.
	 * @param scanPolicyService Scan policy service.
	 * @param scheduleService Scheduling service.
	 * @param messageService Message service.
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	@Autowired
	public DbLog(final ConfigurationService configurationService, final ScanlogService scanlogService, final ScanPolicyService scanPolicyService,
				 final ScheduleService scheduleService, final MessageService messageService) {
		this.configurationService = configurationService;
		this.scanlogService = scanlogService;
		this.scanPolicyService = scanPolicyService;
		this.scheduleService = scheduleService;
		this.messageService = messageService;
		try {
			final String host = configurationService.getProperty(ConfigurationKey.hiab_dblog_db_host);
			dbType = configurationService.getProperty(ConfigurationIntKey.hiab_dblog_db_type); // 0=mssql, 1=mysql, 2=postgre, 3=access, 4=oracle
			final String dbConnStr = PROTOCOLS[dbType];
			final String dbName = configurationService.getProperty(ConfigurationKey.hiab_dblog_db_name);
			final int port = configurationService.getProperty(ConfigurationIntKey.hiab_dblog_db_port, DEFAULTPORTS[dbType]);
			InetAddress address = null;

			if (host != null && !"".equals(host)) {
				try {
					address = InetAddress.getByName(host);
				}
				catch (final UnknownHostException e) {
					LOG.info("Error locating " + host + " :", e);
				}
			}
			LOG.debug("Initialized dblog : " + host + ":" + port + ":" + dbConnStr + ", ");

			if (address != null && dbName != null) {
				final String jdbcUrl = "jdbc:" + dbConnStr + "://" + address.getHostAddress() + ":" + port + (dbType != DBTYPE_MSSQL ? "/" : ";databaseName=") + dbName;
				configurationService.setProperty(ConfigurationKey.hiab_dblog_jdbc_url.getConfigKey(), jdbcUrl);
			}
		}
		catch (final Exception e) {
			LOG.warn("Failed initialize Dblog", e);
		}
	}

	/**
	 * Fetch a value.
	 *
	 * @param key Key name.
	 * @param val Value.
	 * @return String representation.
	 */
	private String getValue(final String key, final Object val) {
		String value = "";
		if (val != null) {
			value = val.toString();
		}

		if (RISK_FACTOR.equalsIgnoreCase(key)) {
			if (StringUtils.isNumber(value)) {
				final String[] riskLevels = {"Information", "Low Risk", "Medium Risk", "Unknown", "High Risk"};
				final int riskLevel = Integer.parseInt(value);
				value = riskLevels[riskLevel];
			}
		}
		else {
			value = StringUtils.isEmpty(value) || "0".equalsIgnoreCase(value) || "FALSE".equalsIgnoreCase(value)
					? "No"
					: "1".equalsIgnoreCase(value) || "TRUE".equalsIgnoreCase(value) ? "Yes" : value;
		}

		return value;
	}

	/**
	 * Parse date.
	 *
	 * @param date Date to parse.
	 * @return A valid parsed date.
	 */
	private Date parseDate(final Date date) {
		final Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		if (cal.get(Calendar.YEAR) > 9999) {
			cal.set(9999, 11, 31, 23, 59, 59);
		}
		return cal.getTime();
	}

	/**
	 * Gets the protocol as a displayable string.
	 *
	 * @param value Protocol value.
	 * @return Displayable protocol value.
	 */
	private static String getDisplayableProtocol(final String value) {
		if (value == null) {
			return "n/a";
		}
		if ("-1".equals(value)) {
			return "";
		}
		if ("1".equals(value)) {
			return "ICMP";
		}
		if ("2".equals(value)) {
			return "IGMP";
		}
		if ("6".equals(value)) {
			return "TCP";
		}
		if ("17".equals(value)) {
			return "UDP";
		}
		return "n/a";
	}

	/**
	 * Create database relations.
	 *
	 * @param conn A database connection.
	 * @param tableName Table name.
	 * @return True on success.
	 */
	@SuppressFBWarnings("SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE")
	private boolean createRelations(final Connection conn, final String tableName) {
		boolean ret = false;
		String query = null;
		try {
			query = dbType == DBTYPE_MSSQL ? "IF NOT EXISTS (SELECT table_name FROM information_schema.tables WHERE table_name = '"
					+ tableName
					+ "' AND table_catalog = '"
					+ conn.getCatalog()
					+ "') CREATE TABLE "
					+ tableName
					+ " (" :
					"CREATE TABLE IF NOT EXISTS " + tableName + " (";

			query += SCAN_JOBID + " bigint, "
					+ SCHEDULE_ID + " bigint, "
					+ REPORT_ID + " bigint, "
					+ FINDING_ID + " bigint, "
					+ SCHEDULE_NAME + " varchar(255), "
					+ SCAN_START_DATE + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ SCAN_END_DATE + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ SCAN_POLICY + " varchar(255), "
					+ IS_WAS_ENABLED + " varchar(255), "
					+ HOST + " varchar(255), "
					+ INSTANCE_ID + " varchar(255), "
					+ ARN + " varchar(255), "
					+ VIRTUAL_HOST + " varchar(255), "
					+ HOSTNAME + " varchar(255), "
					+ NETBIOS + " varchar(255), "
					+ PLATFORM + " varchar(255), "
					+ BUSINESS_CRITICALITY + " varchar(255), "
					+ REPORT_DATE + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ SCRIPT_ID + " bigint, "
					+ SCRIPT_NAME + " text, "
					+ PORT + " varchar(255), "
					+ PROTOCOL + " varchar(255), "
					+ RISK_FACTOR + " varchar(255), "
					+ CVSS_SCORE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ CVSSVECTOR + " text, "
					+ CVSSV3_SCORE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ CVSSV3_SEVERITY + " varchar(255), "
					+ CVSSV3_VECTOR + " text, "
					+ NVD_CVSS_SCORE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ OVERRIDE_COMMENT + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ VULNERABILITY_COMMENTS + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ FAMILY + " varchar(255), "
					+ PRODUCT + " text, "
					+ DESCRIPTION + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ FINDING_REFERENCE + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ AGE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ IS_PREVIOUSLY_DETECTED + " varchar(255), "
					+ SOLUTION_TYPE + " varchar(255), "
					+ SOLUTION + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ CVE + " varchar(255), "
					+ BUGTRAQ + " text, "
					+ IS_NEW_OR_FIXED + " varchar(255), "
					+ IS_VERIFIED + " varchar(255), "
					+ IS_NEW + " varchar(255), "
					+ IS_EXPOSED + " varchar(255), "
					+ FIRST_SEEN + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ LAST_SEEN + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ SCANNER_NAME + " varchar(255), "
					+ SERVICE_NAME + " varchar(255), "
					+ HAS_EXPLOITS + " varchar(255), "
					+ CYRATING + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ EXPLOIT_PROBABILITY + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ CYRATING_DELTA + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ EXPLOIT_PROBABILITY_DELTA + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
					+ CYRATING_UPDATED + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ CYRATING_LASTSEEN + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ FARSIGHT_MENTIONS + " bigint, "
					+ FARSIGHT_THREAT_ACTORS + " bigint, "
					+ FARSIGHT_EXPLOITS + " bigint, "
					+ IS_COMPLIANT + " varchar(255), "
					+ VUL_TYPE + " varchar(255), "
					+ INFORMATION + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ IS_RISK_ACCEPTED + " varchar(255), "
					+ ACCEPTANCE_EXP + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ ACCEPTED_BY + " varchar(255), "
					+ ACCEPTED_DATE + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ ACCEPT_COMMENT + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ IS_FALSE_POSITIVE + " varchar(255), "
					+ IS_POTENTIAL_FALSE_POSITIVE + " varchar(255), "
					+ FALSE_POSITIVE_COMMENTS + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ FALSE_POSITIVE_BY + " varchar(255), "
					+ FALSE_POSITIVE_DATE + (dbType == DBTYPE_MSSQL ? " datetime default null, " : " timestamp null default null, ")
					+ PATCH_INFORMATION + " varchar(255), "
					+ TASK + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ CVSS_VECTOR_DESCRIPTION + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ CUSTOM_0 + " varchar(255), "
					+ CUSTOM_1 + " varchar(255), "
					+ CUSTOM_2 + " varchar(255), "
					+ CUSTOM_3 + " varchar(255), "
					+ CUSTOM_4 + " varchar(255), "
					+ CUSTOM_5 + " varchar(255), "
					+ CUSTOM_6 + " varchar(255), "
					+ CUSTOM_7 + " varchar(255), "
					+ CUSTOM_8 + " varchar(255), "
					+ CUSTOM_9 + " varchar(255), "
					+ TARGET_CUSTOM_0 + " varchar(255), "
					+ TARGET_CUSTOM_1 + " varchar(255), "
					+ TARGET_CUSTOM_2 + " varchar(255), "
					+ TARGET_CUSTOM_3 + " varchar(255), "
					+ TARGET_CUSTOM_4 + " varchar(255), "
					+ TARGET_CUSTOM_5 + " varchar(255), "
					+ TARGET_CUSTOM_6 + " varchar(255), "
					+ TARGET_CUSTOM_7 + " varchar(255), "
					+ TARGET_CUSTOM_8 + " varchar(255), "
					+ TARGET_CUSTOM_9 + " varchar(255)"
					+ ");";

			try (final Statement st = conn.createStatement()) {
				st.executeUpdate(query);
				conn.commit();
			}
			ret = true;
			LOG.info("Successfully created relations : " + tableName);
		}
		catch (final Exception e) {
			LOG.error("Error during sendDblog, Relation creation failed ", e);
			LOG.error("SQL Failed >>>" + query);
		}
		return ret;
	}

	/**
	 * Migrate database relations.
	 *
	 * @param conn A database connection.
	 * @param tableName Table name.
	 * @return True on success.
	 */
	@SuppressFBWarnings("SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING")
	private boolean migrateRelations(final Connection conn, final String tableName) {
		// Migration 01
		String query = null;
		try {
			// Check if Cyrating exists
			if (conn.getMetaData()
					.getColumns(conn.getCatalog(), conn.getSchema(), dbType == DBTYPE_POSTGRES ? tableName.toLowerCase() : tableName,
							dbType == DBTYPE_POSTGRES ? CYRATING.toLowerCase() : CYRATING)
					.next()) {
				LOG.info("DB migration 01 already done: " + tableName);
				return true;
			}
			// if not add Cyrating items
			else {
				query = "ALTER TABLE " + tableName
						+ " ADD" + (dbType == DBTYPE_MSSQL ? " " : " COLUMN ") + CYRATING + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + EXPLOIT_PROBABILITY + (dbType == DBTYPE_MSSQL ? " float," : " double precision,")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CYRATING_DELTA + (dbType == DBTYPE_MSSQL ? " float," : " double precision,")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + EXPLOIT_PROBABILITY_DELTA + (dbType == DBTYPE_MSSQL ? " float," : " double precision,")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CYRATING_UPDATED + (dbType == DBTYPE_MSSQL
						? " datetime default null,"
						: " timestamp null default null,")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CYRATING_LASTSEEN + (dbType == DBTYPE_MSSQL
						? " datetime default null;"
						: " timestamp null default null;");

				try (final PreparedStatement ps = conn.prepareStatement(query)) {
					ps.executeUpdate();
					conn.commit();
				}
				LOG.info("Successful relations migration 01: " + tableName);
			}
		}
		catch (final Exception e) {
			LOG.error("Error during sendDblog, relations migration 01 failed. " + e.getMessage(), e);
			LOG.error("SQL query: " + query);
			return false;
		}
		// Migration 02
		query = null;
		try {
			// Check if CVSSV3 exists
			if (conn.getMetaData()
					.getColumns(conn.getCatalog(), conn.getSchema(), dbType == DBTYPE_POSTGRES ? tableName.toLowerCase() : tableName,
							dbType == DBTYPE_POSTGRES ? CVSSV3_SCORE.toLowerCase() : CVSSV3_SCORE)
					.next()) {
				LOG.info("DB migration 02 already done: " + tableName);
			}
			// if not add CVSSV3 items
			else {
				query = "ALTER TABLE " + tableName
						+ " ADD" + (dbType == DBTYPE_MSSQL ? " " : " COLUMN ") + CVSSV3_SCORE + (dbType == DBTYPE_MSSQL ? " float," : " double precision,")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CVSSV3_SEVERITY + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CVSSV3_VECTOR + " text;";
				try (final PreparedStatement ps = conn.prepareStatement(query)) {
					ps.executeUpdate();
					conn.commit();
				}
				LOG.info("Successful relations migration 02: " + tableName);
			}
		}
		catch (final Exception e) {
			LOG.error("Error during sendDblog, relations migration 02 failed. " + e.getMessage(), e);
			LOG.error("SQL query: " + query);
			return false;
		}
		// Migration 03
		query = null;
		try {
			// Check if DESCRIPTION exists
			if (conn.getMetaData()
					.getColumns(conn.getCatalog(), conn.getSchema(), dbType == DBTYPE_POSTGRES ? tableName.toLowerCase() : tableName,
							dbType == DBTYPE_POSTGRES ? DESCRIPTION.toLowerCase() : DESCRIPTION)
					.next()) {
				LOG.info("DB migration 03 already done: " + tableName);
			}
			// if not add DESCRIPTION and other missing items
			else {
				query = "ALTER TABLE " + tableName
						+ " ADD" + (dbType == DBTYPE_MSSQL ? " " : " COLUMN ") + DESCRIPTION + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + SOLUTION_TYPE + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FINDING_REFERENCE + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + AGE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + IS_PREVIOUSLY_DETECTED + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + IS_POTENTIAL_FALSE_POSITIVE + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + ACCEPT_COMMENT + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + NVD_CVSS_SCORE + (dbType == DBTYPE_MSSQL ? " float, " : " double precision, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + VIRTUAL_HOST + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + PATCH_INFORMATION + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + IS_NEW_OR_FIXED + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FALSE_POSITIVE_DATE + (dbType == DBTYPE_MSSQL
						? " datetime default null,"
						: " timestamp null default null, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + OVERRIDE_COMMENT + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FINDING_ID + " bigint, "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_0 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_1 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_2 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_3 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_4 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_5 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_6 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_7 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_8 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CUSTOM_9 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_0 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_1 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_2 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_3 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_4 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_5 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_6 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_7 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_8 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + TARGET_CUSTOM_9 + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FARSIGHT_MENTIONS + " bigint, "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FARSIGHT_THREAT_ACTORS + " bigint, "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + FARSIGHT_EXPLOITS + " bigint, "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + BUSINESS_CRITICALITY + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + IS_EXPOSED + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + INSTANCE_ID + " varchar(255), "
						+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + ARN + " varchar(255);";

				try (final PreparedStatement ps = conn.prepareStatement(query)) {
					ps.executeUpdate();
					conn.commit();
				}
				LOG.info("Successful relations migration 03: " + tableName);
			}
		}
		catch (final Exception e) {
			LOG.error("Error during sendDblog, relations migration 03 failed. " + e.getMessage(), e);
			LOG.error("SQL query: " + query);
			return false;
		}
		// Migration 04
		try {
			// Check if TASK exists
			if (conn.getMetaData()
					.getColumns(conn.getCatalog(), conn.getSchema(), dbType == DBTYPE_POSTGRES ? tableName.toLowerCase() : tableName,
							dbType == DBTYPE_POSTGRES ? TASK.toLowerCase() : TASK)
					.next()) {
				LOG.info("DB migration 04 already done: " + tableName);
				return true;
			}
			// if not add TASK and other missing items
			query = "ALTER TABLE " + tableName
					+ " ADD" + (dbType == DBTYPE_MSSQL ? " " : " COLUMN ") + TASK + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + VULNERABILITY_COMMENTS + (dbType == DBTYPE_MYSQL ? " longtext, " : " text, ")
					+ (dbType == DBTYPE_MSSQL ? " " : " ADD COLUMN ") + CVSS_VECTOR_DESCRIPTION + (dbType == DBTYPE_MYSQL ? " longtext;" : " text;");

			try (final PreparedStatement ps = conn.prepareStatement(query)) {
				ps.executeUpdate();
				conn.commit();
			}
			LOG.info("Successful relations migration 04: " + tableName);
			return true;
		}
		catch (final Exception e) {
			LOG.error("Error during sendDblog, relations migration 01 failed. " + e.getMessage(), e);
			LOG.error("SQL query: " + query);
			return false;
		}
	}

	/**
	 * Send log.
	 *
	 * @param user the user details
	 * @param tableName Table name.
	 * @param properties Event properties
	 * @param findings Findings to send.
	 * @param comments Vulnerability comments.
	 * @param tasks Tasks.
	 * @param references Vulnerability references.
	 * @param isTest Test that it works, if true will not commit any changes.
	 * @param event Event being sent out.
	 * @param scanners Scanners for the user.
	 * @return Number of inserted rows.
	 */
	@SuppressFBWarnings("SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING")
	public int sendLog(final UserDetails user, final String tableName, final EventProperties properties, final Stream<? extends Finding> findings,
					   final List<? extends VulnerabilityComment> comments, final List<? extends Ticket> tasks, final List<? extends VulnerabilityReference> references,
					   final boolean isTest, final Event event, final List<? extends Scanner> scanners) {
		int ret = 0;
		final Map<Long, String> scannerNameMap = new HashMap<>();
		if (scanners != null) {
			scanners.stream().forEach(s -> scannerNameMap.put(s.getId(), s.getName()));
		}
		try {
			if (findings != null) {
				try (final Connection conn = DbLogConnection.getInstance(configurationService).getConnection(DRIVERS[dbType])) {
					if (conn != null) {
						if (createRelations(conn, tableName) && migrateRelations(conn, tableName)) {
							final String values =
									"(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
							try (final PreparedStatement st = conn.prepareStatement(
									"INSERT INTO " + tableName + " (" + StringUtils.join(this.columns, ",") + ") VALUES " + values)) {
								ret += setValues(st, properties, findings, comments, tasks, references, event, scannerNameMap, user);
								ret += st.executeBatch().length;
								if (!isTest) {
									conn.commit();
								}
							}
						}
					}
					else {
						LOG.info("Error: Could not get connection");
					}
				}
			}
		}
		catch (final RuntimeException | SQLException ex) {
			ex.printStackTrace();
			LOG.error("Error during send dblog", ex);
			ret = 0;
		}
		return ret;
	}

	/**
	 * Set the values to a prepared statement.
	 *
	 * @param st Prepared statement.
	 * @param properties Values to add.
	 * @param findings List of findings.
	 * @param comments Vulnerability comments.
	 * @param tasks Tasks.
	 * @param references Vulnerability references.
	 * @param event Event being sent out.
	 * @param scannerNameMap Map for scanner names.
	 * @param user the user details.
	 * @return Number of inserted rows.
	 */
	private int setValues(final PreparedStatement st, final EventProperties properties, final Stream<? extends Finding> findings,
						  final List<? extends VulnerabilityComment> comments, final List<? extends Ticket> tasks,
						  final List<? extends VulnerabilityReference> references, final Event event, final Map<Long, String> scannerNameMap, final UserDetails user)
			throws SQLException {
		int ret = 0;
		final Map<Long, String> templateNames = new HashMap<>();
		final Map<Long, String> scheduleNames = new HashMap<>();
		if (findings != null) {
			final ReportMessageUtils reportMessageUtils = new ReportMessageUtils(this.messageService);
			int count = 0;
			ScanLogInterface scan = null;
			String host = "";
			final Iterator<? extends Finding> it = findings.iterator();
			while (it.hasNext()) {
				int index = 1;
				final Finding fn = it.next();
				st.setLong(index++, fn.getScanJobId());
				if (properties != null) {
					st.setString(index++, getValue(IS_WAS_ENABLED, properties.getInternalProperties(event.getEvent()).get("ISWAS")));
					st.setTimestamp(index++, new Timestamp(DateUtils.parseTimeDate(properties.getInternalProperties(event.getEvent()).get("SCANSTART")).getTime()));
					st.setTimestamp(index++, new Timestamp(DateUtils.parseTimeDate(properties.getInternalProperties(event.getEvent()).get("SCANEND")).getTime()));

				}
				else {
					st.setString(index++, getValue(IS_WAS_ENABLED, (fn.getTemplateId() == Template.Was.getId())));
					if (fn.getTarget() != null && (!host.equals(fn.getTarget()) || scan == null)) {
						final List<? extends ScanLogInterface> scans = scanlogService.getScanLogs(fn.getScanJobId(), fn.getScheduleId(), fn.getTarget());
						if (scans != null && scans.size() > 0) {
							scan = scans.get(0);
							host = fn.getTarget();
						}
					}
					st.setTimestamp(index++, scan != null ? new Timestamp(scan.getStartDate().getTime()) : null);
					st.setTimestamp(index++, scan != null ? new Timestamp(scan.getEndDate().getTime()) : null);
				}
				String scheduleName = scheduleNames.get(fn.getScheduleId());
				if (scheduleName == null && !scheduleNames.containsKey(fn.getScheduleId())) {
					final ScheduleInterface schedule = scheduleService.getSchedule(fn.getScheduleId());
					if (schedule != null) {
						scheduleName = schedule.getName();
					}
					scheduleNames.put(fn.getScheduleId(), scheduleName);
				}
				st.setString(index++, scheduleName);
				st.setLong(index++, fn.getScheduleId());
				st.setLong(index++, fn.getReportId());
				String templateName = templateNames.get(fn.getTemplateId());
				if (templateName == null && !templateNames.containsKey(fn.getTemplateId())) {
					final ScanPolicyInterface policy = scanPolicyService.getScanPolicy(fn.getTemplateId());
					if (policy != null) {
						templateName = policy.getName();
					}
					templateNames.put(fn.getTemplateId(), templateName);
				}
				st.setString(index++, templateName);
				st.setString(index++, fn.getTarget());
				st.setString(index++, fn.getHostname());
				st.setString(index++, fn.getNetbios());
				st.setString(index++, "NOTDETECTED".equalsIgnoreCase(fn.getPlatform()) ? "Not Detected" : fn.getPlatform());
				st.setTimestamp(index++, fn.getReportDate() != null ? new Timestamp(fn.getReportDate().getTime()) : null);
				st.setLong(index++, fn.getRuleId());
				st.setString(index++, fn.getName());
				st.setString(index++, fn.getPort() == -1 ? "General" : String.valueOf(fn.getPort()));
				st.setString(index++, getDisplayableProtocol(fn.getProtocol() + ""));
				st.setString(index++, getValue(RISK_FACTOR, fn.getRiskLevel()));
				st.setDouble(index++, fn.getCvssScore() < 0 ? 0.0 : fn.getCvssScore());
				st.setString(index++, fn.getFamily());
				st.setString(index++, fn.getProductName());
				st.setString(index++, fn.isFalsePositive() ? "Yes" : "No");
				st.setString(index++, fn.getFalsePositiveComment());
				st.setString(index++, fn.getFalsePositiveBy());
				st.setString(index++, fn.getSolution());
				st.setString(index++, (StringUtils.isEmpty(fn.getCve()) || "NOCVE".equals(fn.getCve())) ? "No CVE" : fn.getCve());
				st.setString(index++, (StringUtils.isEmpty(fn.getBugTraq()) || "NOBUG".equals(fn.getBugTraq())) ? "No bugtraq" : fn.getBugTraq());
				st.setString(index++, getValue(IS_VERIFIED, fn.isVerified()));
				st.setTimestamp(index++, fn.getFirstSeen() != null ? new Timestamp(fn.getFirstSeen().getTime()) : null);
				st.setTimestamp(index++, fn.getLastSeen() != null ? new Timestamp(fn.getLastSeen().getTime()) : null);
				st.setString(index++, getValue(IS_RISK_ACCEPTED, fn.isAccepted()));
				st.setTimestamp(index++, fn.getAcceptExpires() != null ? new Timestamp(parseDate(fn.getAcceptExpires()).getTime()) : null);
				st.setString(index++, scannerNameMap.getOrDefault(fn.getScannerId(), "Unknown"));
				st.setString(index++, getValue(HAS_EXPLOITS, fn.hasExploits()));
				st.setString(index++, getValue(IS_COMPLIANT, fn.isCompliant()));
				st.setString(index++, fn.getServiceName());
				st.setString(index++, fn.getVulnerabilityType().name());
				st.setString(index++, getValue(IS_NEW, fn.isNew()));
				st.setString(index++, fn.getData());
				st.setString(index++, fn.getAcceptedBy());
				st.setTimestamp(index++, fn.getAcceptDate() != null ? new Timestamp(fn.getAcceptDate().getTime()) : null);
				st.setString(index++, fn.getCvssVector());
				if (user.hasFarsight(FarsightProduct.NETSEC) && fn.getCyrating() != null) {
					st.setDouble(index++, fn.getCyrating());
					if (dbType == DBTYPE_MSSQL) {
						st.setDouble(index++, fn.getExploitProbability() != null ? fn.getExploitProbability() : java.sql.Types.NULL);
						st.setDouble(index++, fn.getCyratingDelta() != null ? fn.getCyratingDelta() : java.sql.Types.NULL);
						st.setDouble(index++, fn.getExploitProbabilityDelta() != null ? fn.getExploitProbabilityDelta() : java.sql.Types.NULL);
					}
					else {
						if (fn.getExploitProbability() != null) {
							st.setDouble(index++, fn.getExploitProbability());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
						if (fn.getCyratingDelta() != null) {
							st.setDouble(index++, fn.getCyratingDelta());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
						if (fn.getExploitProbabilityDelta() != null) {
							st.setDouble(index++, fn.getExploitProbabilityDelta());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
					}
				}
				else {
					if (dbType == DBTYPE_MSSQL) {
						st.setDouble(index++, java.sql.Types.NULL);
						st.setDouble(index++, java.sql.Types.NULL);
						st.setDouble(index++, java.sql.Types.NULL);
						st.setDouble(index++, java.sql.Types.NULL);
					}
					else {
						st.setNull(index++, java.sql.Types.NULL);
						st.setNull(index++, java.sql.Types.NULL);
						st.setNull(index++, java.sql.Types.NULL);
						st.setNull(index++, java.sql.Types.NULL);
					}
				}
				st.setTimestamp(index++,
						(user.hasFarsight(FarsightProduct.NETSEC) && fn.getCyratingUpdated() != null) ? new Timestamp(fn.getCyratingUpdated().getTime()) : null);
				st.setTimestamp(index++,
						(user.hasFarsight(FarsightProduct.NETSEC) && fn.getCyratingLastSeen() != null) ? new Timestamp(fn.getCyratingLastSeen().getTime()) : null);
				st.setDouble(index++, (fn.getCvssV3Score() == null || fn.getCvssV3Score() < 0) ? 0.0 : fn.getCvssV3Score());
				st.setString(index++, StringUtils.isEmpty(fn.getCvssV3Severity()) ? "" : fn.getCvssV3Severity());
				st.setString(index++, StringUtils.isEmpty(fn.getCvssV3Vector()) ? "" : fn.getCvssV3Vector());
				st.setString(index++, StringUtils.isEmpty(fn.getDescription()) ? "" : fn.getDescription());
				st.setString(index++, fn.getSolutionType() != null ? fn.getSolutionType().name() : "");
				st.setString(index++, getVulnerabilityReferences(references, fn.getRuleId()));
				st.setDouble(index++, fn.getAge());
				st.setString(index++, getValue(IS_PREVIOUSLY_DETECTED, fn.isPreviouslyDetected()));
				st.setString(index++, getValue(IS_POTENTIAL_FALSE_POSITIVE, fn.getIsPotentialFalsePositive()));
				st.setString(index++, fn.getAcceptedComment());
				st.setDouble(index++, fn.getNvdCvssScore());
				st.setString(index++, fn.getVirtualHost());
				st.setString(index++, fn.getPatchInformation());
				st.setString(index++, fn.isFixed() ? "Removed" : (fn.isNew() ? "Added" : "Unchanged"));
				st.setTimestamp(index++, fn.getDisputeDate() != null ? new Timestamp(fn.getDisputeDate().getTime()) : null); // FALSE_POSITVE_DATE
				st.setString(index++, fn.getOverrideComment());
				st.setLong(index++, fn.getId());
				st.setString(index++, fn.getCustom0());
				st.setString(index++, fn.getCustom1());
				st.setString(index++, fn.getCustom2());
				st.setString(index++, fn.getCustom3());
				st.setString(index++, fn.getCustom4());
				st.setString(index++, fn.getCustom5());
				st.setString(index++, fn.getCustom6());
				st.setString(index++, fn.getCustom7());
				st.setString(index++, fn.getCustom8());
				st.setString(index++, fn.getCustom9());
				st.setString(index++, fn.getTargetcustom0());
				st.setString(index++, fn.getTargetcustom1());
				st.setString(index++, fn.getTargetcustom2());
				st.setString(index++, fn.getTargetcustom3());
				st.setString(index++, fn.getTargetcustom4());
				st.setString(index++, fn.getTargetcustom5());
				st.setString(index++, fn.getTargetcustom6());
				st.setString(index++, fn.getTargetcustom7());
				st.setString(index++, fn.getTargetcustom8());
				st.setString(index++, fn.getTargetcustom9());
				if (user.hasFarsight(FarsightProduct.NETSEC)) {
					if (dbType == DBTYPE_MSSQL) {
						st.setLong(index++, fn.getBluelivMentions() != null ? fn.getBluelivMentions() : java.sql.Types.NULL);
						st.setLong(index++, fn.getBluelivThreatActors() != null ? fn.getBluelivThreatActors() : java.sql.Types.NULL);
						st.setLong(index++, fn.getBluelivExploits() != null ? fn.getBluelivExploits() : java.sql.Types.NULL);
					}
					else {
						if (fn.getBluelivMentions() != null) {
							st.setLong(index++, fn.getBluelivMentions());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
						if (fn.getBluelivThreatActors() != null) {
							st.setLong(index++, fn.getBluelivThreatActors());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
						if (fn.getBluelivExploits() != null) {
							st.setLong(index++, fn.getBluelivExploits());
						}
						else {
							st.setNull(index++, java.sql.Types.NULL);
						}
					}
				}
				else {
					if (dbType == DBTYPE_MSSQL) {
						st.setLong(index++, java.sql.Types.NULL);
						st.setLong(index++, java.sql.Types.NULL);
						st.setLong(index++, java.sql.Types.NULL);
					}
					else {
						st.setNull(index++, java.sql.Types.NULL);
						st.setNull(index++, java.sql.Types.NULL);
						st.setNull(index++, java.sql.Types.NULL);
					}
				}
				st.setString(index++, fn.getBusinessCriticality() != null ? fn.getBusinessCriticality().name() : "");
				st.setString(index++, getValue(IS_EXPOSED, fn.isExposed()));
				st.setString(index++, fn.getAwsInstanceId());
				st.setString(index++, fn.getAwsArn());
				st.setString(index++, getTaskInfo(tasks, fn, reportMessageUtils));
				st.setString(index++, getVulnerabilityComments(comments, fn, reportMessageUtils, user));
				st.setString(index++, getCvssVectorDescription(fn, reportMessageUtils));
				st.addBatch();
				count++;
				if (count == 100) {
					count = 0;
					ret += st.executeBatch().length;
				}
			}
		}
		return ret;
	}

	/**
	 * Get task info of the finding
	 *
	 * @param tasks the list of tasks that belongs to the user account and refers to the finding targets.
	 * @param finding the finding
	 * @param reportMessageUtils the report message utils instance
	 * @return id, status and assignee when exists else empty string
	 */
	private String getTaskInfo(final List<? extends Ticket> tasks, final Finding finding, final ReportMessageUtils reportMessageUtils) throws SQLException {
		final StringBuilder taskInfo = new StringBuilder();
		if (tasks != null) {
			final Stream<? extends Ticket> tickets = tasks.stream().filter(task -> task.getTargetId() == finding.getTargetId() && task.getVulnId() == finding.getRuleId() &&
					task.getPort() == finding.getPort() && task.getProtocol() == Protocol.fromValue(finding.getProtocol()));
			final Iterator<? extends Ticket> it = tickets.iterator();
			while (it.hasNext()) {
				final Ticket ticket = it.next();
				taskInfo.append(taskInfo.length() != 0 ? ", " : "");
				String status = "" + ticket.getStatus();
				if ("1".equals(status)) {
					status = reportMessageUtils.getString("report.field.print.tasknew");
				}
				else if ("2".equals(status)) {
					status = reportMessageUtils.getString("report.field.print.taskassigned");
				}
				else if ("3".equals(status)) {
					status = reportMessageUtils.getString("report.field.print.taskresolved");
				}
				else if ("4".equals(status)) {
					status = reportMessageUtils.getString("report.field.print.taskverified");
				}
				else if ("6".equals(status)) {
					status = reportMessageUtils.getString("report.field.print.taskreopened");
				}
				taskInfo.append(ticket.getTaskId() + " : " + status + " (" + ticket.getAssignee() + ")");
			}
		}
		taskInfo.append(taskInfo.length() == 0 ? reportMessageUtils.getString("not.assigned") : ".");
		return taskInfo.toString();
	}

	/**
	 * Get vulnerability comments of the finding
	 *
	 * @param comments all comments that belong to the main user account.
	 * @param finding the finding
	 * @param reportMessageUtils the report message utils instance
	 * @param user the user details.
	 * @return vulnerability comments when exist else empty string
	 */
	private String getVulnerabilityComments(final List<? extends VulnerabilityComment> comments, final Finding finding, final ReportMessageUtils reportMessageUtils,
											final UserDetails user) throws SQLException {
		String value = "";
		if (comments != null) {
			final StringBuilder sb = new StringBuilder();
			for (final VulnerabilityComment comment : comments) {
				final Long scriptId = comment.getScriptId();
				final Long findingId = comment.getFindingId();
				if ((findingId == null || findingId != finding.getId()) && (scriptId == null || scriptId != finding.getRuleId())) {
					continue;
				}
				sb.append("#").append(reportMessageUtils.getString(findingId != null && findingId == finding.getId() ? "report.finding" : "report.private")).append("\n");
				sb.append("- ")
						.append(DateUtils.formatDateTimezone(
								DateUtils.parseDateFormat(StringUtils.setEmpty(comment.getCreated().toString(), ""), null, DateUtils.getTimezone(0)),
								DateUtils.getDateFormat(user.getDateFormat()) + " " + DateUtils.getTimeFormat(user.getTimeFormat()),
								DateUtils.getTimezone(user.getGmtOffset())));
				if (!StringUtils.isEmpty(comment.getName())) {
					sb.append(" ").append(reportMessageUtils.getString("report.by")).append(" ").append(comment.getName());
				}
				sb.append("\n");
				sb.append(comment.getComment());
				sb.append("\n\n");
			}
			if (sb.length() != 0) {
				value = sb.toString().trim();
			}
		}
		return value;
	}

	/**
	 * Get CVSS vector description of the finding
	 *
	 * @param finding the finding
	 * @param reportMessageUtils the report message utils instance
	 * @return CVSS vector description when CVSS exists else empty string
	 */
	private String getCvssVectorDescription(final Finding finding, final ReportMessageUtils reportMessageUtils) throws SQLException {
		String value = "";
		if (finding.getRiskLevel() > 0 && !StringUtils.isEmpty(finding.getCvssVector())) {
			value = reportMessageUtils.formatCvss2Vector(finding.getCvssVector(), finding.hasExploits());
		}
		return value;
	}

	/**
	 * Get vulnerability references
	 *
	 * @param references Vulnerability references for rule id set
	 * @param ruleId the rule id
	 * @return vulnerability references
	 */
	private String getVulnerabilityReferences(final List<? extends VulnerabilityReference> references, final long ruleId) throws SQLException {
		if (references == null) {
			return "";
		}
		final StringBuilder sb = new StringBuilder();
		for (final VulnerabilityReference reference : references) {
			if (reference.getRuleId() == ruleId) {
				if (sb.length() != 0) {
					sb.append(", ");
				}
				sb.append(reference.getType());
				sb.append(": ");
				sb.append(reference.getReference());
			}
		}
		return sb.toString().trim();
	}
}
