package com.chilicoders.integrations.delinea.model;

import java.util.List;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Setter
public class SecretLookup {
	private int skip;
	private int take;
	private int total;
	private int pageCount;
	private int currentPage;
	private int batchCount;
	private int prevSkip;
	private int nextSkip;
	private boolean hasPrev;
	private boolean hasNext;
	private boolean success;
	private List<Secret> records;
	private List<Sort> sortBy;
	private String severity;

}