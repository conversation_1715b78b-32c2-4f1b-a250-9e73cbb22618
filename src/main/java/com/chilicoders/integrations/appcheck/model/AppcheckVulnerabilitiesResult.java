package com.chilicoders.integrations.appcheck.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class AppcheckVulnerabilitiesResult {
	@JsonProperty("success")
	private boolean success;

	@JsonProperty("count")
	private String count;

	@JsonProperty("pages")
	private int pages;

	@JsonProperty("page")
	private int page;
}