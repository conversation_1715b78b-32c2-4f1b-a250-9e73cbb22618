package com.chilicoders.finding.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.reporting.api.model.FindingInterface;
import com.chilicoders.core.reporting.dao.FindingDao;
import com.chilicoders.finding.model.FindingImpl;

@Component
public class FindingDaoImpl implements FindingDao {
	@Autowired
	private FindingImplRepository findingImplRepository;

	@Override
	public FindingInterface createFinding() {
		return new FindingImpl();
	}

	@Override
	public Integer save(final FindingInterface finding) throws SQLException {
		return findingImplRepository.save((FindingImpl) finding).getId();
	}

	@Override
	public List<? extends FindingInterface> getFindings(final Integer customerId, final Integer assetId) throws SQLException {
		return findingImplRepository.findByCustomerIdAndAssetIdAndDeletedIsNull(customerId, assetId);
	}
}
