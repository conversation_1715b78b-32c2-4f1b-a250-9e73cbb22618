package com.chilicoders.rest.adapters;

import java.time.OffsetTime;
import java.time.format.DateTimeFormatter;

import javax.xml.bind.annotation.adapters.XmlAdapter;

public class OffsetTimeXmlAdapter extends XmlAdapter<String, OffsetTime> {
	@Override
	public OffsetTime unmarshal(final String stringValue) {
		return stringValue != null ? DateTimeFormatter.ISO_OFFSET_TIME.parse(stringValue, OffsetTime::from) : null;
	}

	@Override
	public String marshal(final OffsetTime value) {
		return value != null ? DateTimeFormatter.ISO_OFFSET_TIME.format(value) : null;
	}
}
