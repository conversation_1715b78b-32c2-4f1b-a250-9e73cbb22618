package com.chilicoders.rest.adapters;

import java.time.Instant;
import java.time.format.DateTimeFormatter;

import javax.xml.bind.annotation.adapters.XmlAdapter;

public class InstantXmlAdapter extends XmlAdapter<String, Instant> {
	@Override
	public Instant unmarshal(final String stringValue) {
		return stringValue != null ? DateTimeFormatter.ISO_OFFSET_DATE_TIME.parse(stringValue, Instant::from) : null;
	}

	@Override
	public String marshal(final Instant value) {
		return value != null ? DateTimeFormatter.ISO_INSTANT.format(value) : null;
	}
}
