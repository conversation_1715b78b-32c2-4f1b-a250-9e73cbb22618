package com.chilicoders.rest;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;

import javax.inject.Inject;
import javax.servlet.ServletConfig;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Application;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import javax.ws.rs.core.UriInfo;

import org.apache.commons.codec.digest.DigestUtils;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.api.DataStoreService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;

import io.swagger.v3.jaxrs2.integration.resources.BaseOpenApiResource;
import io.swagger.v3.oas.annotations.Operation;

@Path("/openapi.{type:json|yaml}")
public class OpenApiResource extends BaseOpenApiResource {
	@Context
	private ServletConfig config;

	@Context
	private Application app;

	@Inject
	private Connection conn;

	/**
	 * Get openapi file.
	 *
	 * @param headers Headers
	 * @param uriInfo Uri info
	 * @param type File type
	 * @return The openapi file
	 */
	@GET
	@Produces({MediaType.APPLICATION_JSON, O24MediaType.APPLICATION_YAML})
	@Operation(hidden = true)
	public Response getOpenApi(@Context final HttpHeaders headers, @Context final UriInfo uriInfo, @PathParam("type") final String type) throws Exception {
		final String checkSum;
		try (final InputStream inputStream = Files.newInputStream(Paths.get(XMLAPI.getPrefix() + "WEB-INF/openapi.yaml"))) {
			checkSum = DigestUtils.md5Hex(inputStream);
		}

		final DataStoreService dataStoreService = ServiceProvider.getDataStoreService(this.conn);
		final String dataStoreEntryKey = "openapi." + ("yaml".equalsIgnoreCase(type) ? "yaml" : "json") + ".checksum";
		final DataStoreEntry dataStoreEntry = dataStoreService.getEntry(dataStoreEntryKey);
		final File resultFile = new File(Configuration.getProperty(ConfigurationKey.openapi_path) + "openapi." + ("yaml".equalsIgnoreCase(type) ? "yaml" : "json"));

		if (dataStoreEntry == null || !dataStoreEntry.getValue().equals(checkSum) || !resultFile.exists()) {
			final Response response = super.getOpenApi(headers, this.config, this.app, uriInfo, type);
			if (response.getStatusInfo() != Response.Status.OK) {
				return response;
			}
			try (final Writer writer = new OutputStreamWriter(Files.newOutputStream(resultFile.toPath()), UTF_8)) {
				writer.write((String) response.getEntity());
			}
			dataStoreService.setEntry(dataStoreEntryKey, checkSum);
			this.conn.commit();
		}

		final Response response = Response.status(Response.Status.OK)
				.type("yaml".equalsIgnoreCase(type) ? O24MediaType.APPLICATION_YAML_TYPE : MediaType.APPLICATION_JSON_TYPE)
				.entity(new StreamingOutput() {
					@Override
					public void write(final OutputStream output) throws IOException, WebApplicationException {
						Files.copy(resultFile.toPath(), output);
					}
				})
				.build();

		return response;
	}

}
