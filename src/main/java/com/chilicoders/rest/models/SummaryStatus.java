package com.chilicoders.rest.models;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public enum SummaryStatus {
	PENDING_REVIEW,
	PENDING_QA,
	PENDING_PUBLICATION,
	PUBLISHED,
	REJECTED;

	public static final Map<SummaryStatus, Set<SummaryStatus>> STATUS_TRANSITIONS;

	static {
		final Map<SummaryStatus, Set<SummaryStatus>> tmpMap = new HashMap<>();
		tmpMap.put(PENDING_REVIEW, new HashSet<>(Arrays.asList(PENDING_QA, REJECTED)));
		tmpMap.put(PENDING_QA, new HashSet<>(Arrays.asList(PENDING_REVIEW, PENDING_PUBLICATION, REJECTED)));
		tmpMap.put(PENDING_PUBLICATION, new HashSet<>(Arrays.asList(PENDING_REVIEW, PUBLISHED, REJECTED)));
		tmpMap.put(PUBLISHED, new HashSet<>(Collections.singletonList(PUBLISHED)));
		tmpMap.put(REJECTED, new HashSet<>(Collections.singletonList(PENDING_QA)));
		STATUS_TRANSITIONS = Collections.unmodifiableMap(tmpMap);
	}
}
