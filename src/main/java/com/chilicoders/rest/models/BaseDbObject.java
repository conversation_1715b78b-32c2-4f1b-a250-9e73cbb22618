package com.chilicoders.rest.models;

import java.time.Instant;
import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbField;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.rest.exceptions.InputValidationException;

import io.swagger.v3.oas.annotations.media.Schema;

@XmlAccessorType(XmlAccessType.NONE)
public class BaseDbObject extends XmlAble implements RestObject {
	@DbField(id = true, autoIncrementedId = true, dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The id of the object")
	private Integer id;

	@DbField(access = AccessType.ADMIN, noDefault = true)
	@XmlElement
	@Schema(description = "The customer id the object belongs to")
	private Integer customerId;

	@DbField(dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The created date")
	private Instant created;

	@DbField(dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The updated date")
	private Instant updated;

	@DbField(dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The name of who created the object")
	private String createdBy;

	@DbField(dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The name of who updated the object")
	private String updatedBy;

	@DbField(noDefault = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The userid of the person that created the object")
	private Integer createdById;

	@DbField(noDefault = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The userid of the person that updated the object")
	private Integer updatedById;

	@DbField
	private Instant deleted;

	public Integer getId() {
		return this.id;
	}

	public Integer getCustomerId() {
		return this.customerId;
	}

	public Instant getCreated() {
		return this.created;
	}

	public Instant getUpdated() {
		return this.updated;
	}

	public String getCreatedBy() {
		return this.createdBy;
	}

	public String getUpdatedBy() {
		return this.updatedBy;
	}

	public Integer getCreatedById() {
		return this.createdById;
	}

	public Integer getUpdatedById() {
		return this.updatedById;
	}

	public Instant getDeleted() {
		return this.deleted;
	}

	public void setId(final Integer id) {
		this.id = id;
	}

	public void setCustomerId(final Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * Set customerId from user.
	 *
	 * @param user User
	 */
	public void setCustomerId(final UserDetails user) throws InputValidationException {
		if (user == null && (this.customerId == null || this.customerId <= 0)) {
			throw new InputValidationException(null, ErrorCode.InputValidationFailed, Arrays.asList("customerId is required."));
		}
		else if (user != null) {
			this.customerId = user.getCustomerId();
		}
	}

	public void setUpdated(final Instant updated) {
		this.updated = updated;
	}

	public void setCreatedById(final Integer createdById) {
		this.createdById = createdById;
	}

	public void setUpdatedById(final Integer updatedById) {
		this.updatedById = updatedById;
	}

	public void setDeleted(final Integer updatedById) {
		this.deleted = Instant.now();
		this.updatedById = updatedById;
	}
}
