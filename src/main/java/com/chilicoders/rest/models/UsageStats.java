package com.chilicoders.rest.models;

import java.time.Instant;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "UsageStats")
@DbTable(name = "usagestats", view = "usagestatsview")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
public class UsageStats extends BaseDbObject {

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String customerName;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String organization;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String salesAccountId;

	@DbField
	@XmlElement
	private Instant statsUpdated;

	@DbField
	@XmlElement
	private Instant applianceStatsUpdated;

	@DbField(noDefault = true)
	@XmlElement
	private Integer scaleConfigurations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer scoutConfigurations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer cloudsecConfigurations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceScaleConfigurations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer users;

	@DbField
	@XmlElement
	private Instant lastLogin;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceUsers;

	@DbField
	@XmlElement
	private Instant applianceLastLogin;

	@DbField(noDefault = true)
	@XmlElement
	private Integer findingsCritical;

	@DbField(noDefault = true)
	@XmlElement
	private Integer findingsHigh;

	@DbField(noDefault = true)
	@XmlElement
	private Integer findingsMedium;

	@DbField(noDefault = true)
	@XmlElement
	private Integer findingsLow;

	@DbField(noDefault = true)
	@XmlElement
	private Integer findingsRecommendations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceFindingsCritical;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceFindingsHigh;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceFindingsMedium;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceFindingsLow;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceFindingsRecommendations;

	@DbField(noDefault = true)
	@XmlElement
	private Integer scans;

	@DbField
	@XmlElement
	private Instant lastScan;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceScans;

	@DbField
	@XmlElement
	private Instant applianceLastScan;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsAws;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsGcp;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsAzure;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsVsphere;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsBasic;

	@DbField(noDefault = true)
	@XmlElement
	private Integer accountsWeb;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsAws;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsGcp;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsAzure;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsVsphere;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsBasic;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAccountsWeb;

	@DbField(noDefault = true)
	@XmlElement
	private Integer cyr3conAssets;

	@DbField(noDefault = true)
	@XmlElement
	private Integer cyr3conApplianceAssets;

	@DbField(noDefault = true)
	@XmlElement
	private Integer agents;

	@DbField(noDefault = true)
	@XmlElement
	private Integer agentsScanned;

	@DbField(noDefault = true)
	@XmlElement
	private Integer agentsOutdated;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAgents;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAgentsScanned;

	@DbField(noDefault = true)
	@XmlElement
	private Integer applianceAgentsOutdated;
}
