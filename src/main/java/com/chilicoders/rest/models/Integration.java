package com.chilicoders.rest.models;

import java.time.Instant;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.model.IntegrationInterface;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.rest.models.Tag.TagFilter;
import com.chilicoders.util.MarshallingUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "Integration")
@DbTable(name = "integrations", view = "integrationsview")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
public class Integration extends BaseDbObject implements IntegrationInterface {
	@DbField
	@XmlElement
	@NotEmpty
	private String name;

	@DbField(listfilter = true, filterClass = String.class, enumFilterName = "integrationtype")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private IntegrationType type;

	@DbField(saveCast = "JSON")
	@NotNull
	private String configuration;

	@DbField(readViewOnly = true, filterObject = TagFilter.class)
	private String tags;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Instant verified;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String verifiedBy;

	@DbField(noDefault = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Integer verifiedById;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Boolean verifyStatus;

	/**
	 * Get tags.
	 *
	 * @return Array with tag objects
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	public Tag[] getTags() throws JAXBException {
		return filterSystemTags(this.tags);
	}

	/**
	 * Unmarshal JSON string from DB to configuration object.
	 *
	 * @return The unmarshalled configuration object.
	 */
	@XmlElement
	public BaseIntegrationConfiguration getConfiguration() throws JAXBException {
		if (this.configuration == null) {
			return null;
		}
		return MarshallingUtils.unmarshal(BaseIntegrationConfiguration.class, this.configuration);
	}

	/**
	 * Marshal configuration object to JSON string for storing in DB.
	 *
	 * @param configuration The configuration object.
	 */
	public void setConfiguration(final BaseIntegrationConfiguration configuration) throws JAXBException {
		this.configuration = MarshallingUtils.marshal(configuration);
	}
}
