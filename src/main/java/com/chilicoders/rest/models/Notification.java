package com.chilicoders.rest.models;

import java.time.Instant;

import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.event.entities.Notification.Content;
import com.chilicoders.util.MarshallingUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "Notification")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "notifications")
public class Notification extends BaseDbObject {

	@DbField(noDefault = true)
	@XmlElement
	private Long userId;

	@DbField(noDefault = true)
	@XmlElement
	private Long subUserId;

	@DbField(noDefault = true)
	@XmlElement
	private Integer eventSubscriptionId;

	@DbField
	@XmlElement
	private Instant read;

	@DbField(saveCast = "JSON")
	private String content;

	@XmlElement
	public Content getContent() throws JAXBException {
		return MarshallingUtils.unmarshal(Content.class, this.content);
	}

	@XmlElement
	public void setContent(final Content content) throws JAXBException {
		this.content = MarshallingUtils.marshal(content);
	}
}
