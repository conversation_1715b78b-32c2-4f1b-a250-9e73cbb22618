package com.chilicoders.rest.models;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;
import org.json.JSONObject;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.ScanSelectionType;
import com.chilicoders.model.Source;
import com.chilicoders.rest.models.Tag.TagFilter;
import com.chilicoders.util.MarshallingUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Schema(name = "AssetIdentifier")
@DbTable(name = "assetidentifiers", view = "assetidentifiersview")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
public class AssetIdentifier extends BaseDbObject implements AssetIdentifierInterface {
	private static final long serialVersionUID = -7706241164324869531L;

	/**
	 * No-arg constructor for marshalling purposes.
	 */
	public AssetIdentifier() {
	}

	/**
	 * Constructor to create an asset identifier quickly.
	 *
	 * @param userId The ID of the creator
	 * @param customerId The customer id
	 * @param type The type of identifier it is
	 * @param name The value of the identifier, e.g. the IP address
	 */
	public AssetIdentifier(final Integer userId, final Integer customerId, final AssetIdentifierType type, final String name) {
		this(userId, customerId, type, name, null);
	}

	/**
	 * Constructor to create an asset identifier quickly.
	 *
	 * @param userId The ID of the creator
	 * @param customerId The customer id
	 * @param type The type of identifier it is
	 * @param name The value of the identifier, e.g. the IP address
	 * @param presentableName The presentable name of the identifier
	 */
	public AssetIdentifier(final Integer userId, final Integer customerId, final AssetIdentifierType type, final String name,
						   final String presentableName) {
		this.setCreatedById(userId);
		this.setCustomerId(customerId);
		this.type = type;
		this.name = name;
		this.presentableName = presentableName;
	}

	@DbField(id = true, autoIncrementedId = true, dontSave = true)
	@XmlElement
	@Schema(readOnly = true)
	private Integer id;

	@DbField
	@XmlElement
	@NotEmpty
	private String name;

	@DbField
	@XmlElement
	private String presentableName;

	@DbField(listfilter = true, filterClass = String.class, enumFilterName = "assetidentifiertype")
	@XmlElement
	@NotNull
	private AssetIdentifierType type;

	@DbField
	@XmlElement
	private Instant firstSeen;

	@DbField
	@XmlElement
	private Instant lastSeen;

	@DbField(noDefault = true)
	@XmlElement
	private Integer scannerId;

	@DbField(readViewOnly = true)
	@XmlElement
	@Schema(readOnly = true)
	private String scannerName;

	@DbField(noDefault = true, readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Integer ownership;

	@DbField(noDefault = true)
	@XmlElement
	@Min(value = 0)
	@Max(value = 100)
	private Integer customOwnership;

	@DbField(noDefault = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Integer firstScanId;

	@DbField(noDefault = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Integer lastScanId;

	@Getter
	@DbField(saveCast = "source[]", listfilter = true, filterClass = String.class, enumFilterName = "source")
	@XmlElement
	@NotNull
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Source[] source;

	@DbField(readViewOnly = true, listfilter = true, filterClass = Integer.class, enumFilterName = "integer")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] links;

	@DbField(readViewOnly = true, listfilter = true, filterClass = Integer.class, enumFilterName = "integer")
	@XmlElement
	@Schema
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] assetIds;

	@DbField(readViewOnly = true, filterClass = String.class, specialFilter = "array_to_string(assetnames, '')")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private String[] assetNames;

	@DbField(readViewOnly = true, filterObject = TagFilter.class)
	private String tags;

	@DbField(readViewOnly = true, listfilter = true, filterClass = Integer.class, enumFilterName = "integer")
	@XmlElement
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] accountIds;

	@DbField
	@XmlElement
	private String platform;

	@DbField(saveCast = "JSON", jsonClass = BaseProperties.class)
	private String properties;

	@DbField(noDefault = true)
	private Integer migration;

	@Getter
	@XmlElement
	@Schema(readOnly = true)
	private ScanSelectionType scanSelectionType;

	@Getter
	@XmlElement
	@Schema(readOnly = true)
	private Integer linkId;

	/**
	 * Get tags. Exclude system tags.
	 *
	 * @return Array with tag objects
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	public Tag[] getTags() throws JAXBException {
		return filterSystemTags(this.tags);
	}

	/**
	 * Unmarshall JSON string from DB to object.
	 *
	 * @return The unmarshalled object.
	 */
	@XmlElement
	public BaseProperties getProperties() throws JAXBException {
		if (this.properties == null) {
			return null;
		}

		final JSONObject propertiesJson = new JSONObject(this.properties);
		propertiesJson.put("type", this.type.name());
		return MarshallingUtils.unmarshal(BaseProperties.class, propertiesJson.toString());
	}

	/**
	 * Marshall properties object to JSON string for storing in DB.
	 *
	 * @param properties The properties object.
	 */
	public void setProperties(final BaseProperties properties) throws JAXBException {
		this.properties = properties == null ? null : MarshallingUtils.marshal(properties);
	}

	public void setProperties(final String properties) {
		this.properties = properties;
	}

	/**
	 * Add new source.
	 *
	 * @param source Source
	 */
	public void addSource(final Source source) {
		final List<Source> sourceList = new ArrayList<>(Arrays.asList(this.source == null ? new Source[0] : this.source));
		if (!sourceList.contains(source)) {
			sourceList.add(source);
		}
		this.source = sourceList.toArray(new Source[0]);
	}
}
