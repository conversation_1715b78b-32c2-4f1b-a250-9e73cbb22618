package com.chilicoders.rest.models;

import java.time.Instant;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlReadOnly;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(name = "RuleExportRequest")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@Getter
@Setter
public class RuleExportRequest {
	@XmlElement
	@XmlReadOnly
	private Instant ruleDate;

	@XmlElement
	@XmlReadOnly
	private Instant farsightDate;

	@XmlElement
	@XmlReadOnly
	private Instant exploitDate;

	@XmlElement
	@XmlReadOnly
	private Instant productDate;

	@XmlElement
	@XmlReadOnly
	private Instant patchDate;
}
