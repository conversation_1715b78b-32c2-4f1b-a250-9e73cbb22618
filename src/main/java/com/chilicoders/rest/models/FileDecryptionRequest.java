package com.chilicoders.rest.models;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(name = "FileDecryptionRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
public class FileDecryptionRequest {
	private boolean unsigned;

	@NotEmpty
	private String auditComment;
}