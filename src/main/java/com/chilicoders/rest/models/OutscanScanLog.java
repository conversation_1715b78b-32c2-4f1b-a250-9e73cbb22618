package com.chilicoders.rest.models;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "OutscanScanLog")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "tscanlogs", view = "vscanlog")
public class OutscanScanLog extends XmlAble {

	public enum Status {
		Invalid(-1),
		Ok(0),
		Forced(1),
		Timeout(2),
		Stopped(3),
		StoppedByUser(4),
		<PERSON>(5),
		<PERSON><PERSON><PERSON><PERSON><PERSON>(6),
		Failed(7);

		private int id;

		private Status(final int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}

		/**
		 * Fetches the status given an id.
		 *
		 * @param id The id to fetch.
		 * @return A scantype.
		 */
		public static Status fromId(final int id) {
			for (final Status status : Status.values()) {
				if (status.getId() == id) {
					return status;
				}
			}
			return Invalid;
		}
	}

	@DbField(name = "xid", id = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long id;

	@DbField(name = "xuserxid")
	private long userId;

	@DbField(name = "itype")
	private int status;

	@DbField(name = "dscanstartdate")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Date startDate;

	@DbField(name = "dscanenddate")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Date endDate;

	@DbField(name = "vchost")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String target;

	@DbField(name = "xipxid")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long targetId;

	@DbField(name = "schedulejob", readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String schedule;

	@DbField(name = "xsoxid")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long scheduleId;

	@DbField(name = "xscanjobxid")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long scanJobId;

	/**
	 * Get status.
	 *
	 * @return The status.
	 */
	@XmlElement
	public Status getStatus() {
		final Status status = Status.fromId(this.status);
		if (status == Status.StoppedByUser) {
			return Status.Stopped;
		}
		else if (status == Status.KilledLarge) {
			return Status.Large;
		}
		return status;
	}
}
