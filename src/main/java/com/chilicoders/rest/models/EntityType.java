package com.chilicoders.rest.models;

import java.util.Map;

import com.chilicoders.event.model.EventObject.EventObjectIdentifier;
import com.chilicoders.model.ViewType;
import com.google.common.collect.ImmutableMap;

public enum EntityType {
	FINDING,
	ASSET_GROUP,
	ASSET,
	SCAN_CONFIGURATION,
	SCHEDULE,
	SCAN,
	USER,
	ROLE,
	RESOURCE_GROUP,
	COMP<PERSON>IANCE,
	WORKFLOW,
	CHECK,
	PRODUCT_INFORMATION,
	INFORMATION;

	public static final Map<EntityType, EventObjectIdentifier> ENTITY_TYPE_EVENT_OBJECT_IDENTIFIER_MAP;

	static {
		final ImmutableMap.Builder<EntityType, EventObjectIdentifier> builder = ImmutableMap.builder();
		builder.put(FINDING, EventObjectIdentifier.FINDING);
		builder.put(ASSET_GROUP, EventObjectIdentifier.ASSET_GROUP);
		builder.put(ASSET, EventObjectIdentifier.ASSET);
		builder.put(SCAN_CONFIGURATION, EventObjectIdentifier.SCANCONFIGURATION);
		builder.put(SCHEDULE, EventObjectIdentifier.SCHEDULE);
		builder.put(SCAN, EventObjectIdentifier.SCAN);
		builder.put(USER, EventObjectIdentifier.USER);
		builder.put(ROLE, EventObjectIdentifier.USER_ROLE);
		builder.put(RESOURCE_GROUP, EventObjectIdentifier.RESOURCE_GROUP);
		builder.put(COMPLIANCE, EventObjectIdentifier.COMPLIANCE_FINDING);
		builder.put(WORKFLOW, EventObjectIdentifier.WORKFLOW);
		builder.put(CHECK, EventObjectIdentifier.CHECK);
		builder.put(PRODUCT_INFORMATION, EventObjectIdentifier.PRODUCT_INFORMATION);

		ENTITY_TYPE_EVENT_OBJECT_IDENTIFIER_MAP = builder.build();
	}

	public static final Map<EntityType, ViewType> ENTITY_TYPE_VIEW_TYPE_MAP;

	static {
		final ImmutableMap.Builder<EntityType, ViewType> builder = ImmutableMap.builder();
		builder.put(FINDING, ViewType.FINDINGS);
		builder.put(COMPLIANCE, ViewType.COMPLIANCE);
		builder.put(ASSET, ViewType.ASSETS);
		builder.put(SCAN_CONFIGURATION, ViewType.SCAN_CONFIGURATIONS);
		builder.put(SCHEDULE, ViewType.SCHEDULES);
		builder.put(SCAN, ViewType.SCANS);
		builder.put(USER, ViewType.USERS);
		builder.put(WORKFLOW, ViewType.WORKFLOWS);
		builder.put(ASSET_GROUP, ViewType.ASSET_GROUPS);
		builder.put(CHECK, ViewType.CHECKS);
		builder.put(PRODUCT_INFORMATION, ViewType.PRODUCT_INFORMATION);

		ENTITY_TYPE_VIEW_TYPE_MAP = builder.build();
	}
}
