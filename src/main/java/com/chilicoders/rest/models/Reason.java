package com.chilicoders.rest.models;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "Reason")
@XmlAccessorType(XmlAccessType.NONE)
public class Reason {
	@XmlElement
	@NotEmpty
	private String reason;
}
