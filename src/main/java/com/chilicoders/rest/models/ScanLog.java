package com.chilicoders.rest.models;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbFilter;
import com.chilicoders.db.DbTable;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.ScanLogEntryInterface;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.rest.models.Tag.TagFilter;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "ScanLog")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "scanlogs", view = "scanlogsview")
public class ScanLog extends BaseDbObject implements ScanLogEntryInterface {
	@DbField
	@XmlElement
	@Schema(description = "The id of the scan job")
	private String jobId;

	@DbField(listfilter = true, filterClass = String.class, enumFilterName = "scanstatus")
	@XmlElement
	@NotNull
	@Schema(description = "The scan status")
	private ScanLogStatus status;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The scanner name")
	private String scannerName;

	@DbField
	@XmlElement
	@Schema(description = "The scan status details")
	private String statusDetails;

	@DbField(filterClass = String.class, specialFilter = "array_to_string(targets, '')")
	@XmlElement
	@Schema(description = "The targets of the scan")
	@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
	private List<String> targets;

	@DbField(filterClass = String.class, specialFilter = "array_to_string(virtualhosts, '')")
	@XmlElement
	@Schema(description = "The virtual hosts related to the scan")
	@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
	private List<String> virtualHosts;

	@DbField
	@XmlElement
	@Schema(description = "The ID for the same invocation scans")
	private Integer invocationId;

	@DbField(name = "template", listfilter = true, filterClass = String.class, enumFilterName = "scantemplate")
	@XmlElement
	@NotNull
	@Schema(description = "The scan source")
	private ScanTemplate source;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The id of the scan configuration")
	private Integer scanConfigurationId;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The name of the scan configuration")
	private String scanConfigurationName;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The id of the workflow")
	private Integer workflowId;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The name of the workflow")
	private String workflowName;

	@DbField
	@XmlElement
	@Schema(description = "The scan started date")
	private Instant started;

	@DbField
	@XmlElement
	@Schema(description = "The scan ended date")
	private Instant ended;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The scan expected start date")
	private Instant expectedStart;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The scan expected end date")
	private Instant expectedEnd;

	@DbField
	private String schema;

	@DbField
	private String attacker;

	@DbField(noDefault = true)
	private Integer scannerId;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "Set to true when scan blueprint is available")
	private boolean blueprintAvailable;

	@DbField(readViewOnly = true, filterObject = TagFilter.class)
	private String tags;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The link id of asset on which the scan is performed")
	private Integer assetIdentifierId;

	@DbField(readViewOnly = true, listfilter = true, filterClass = String.class, enumFilterName = "assetidentifiertype")
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The type of asset on which the scan is performed")
	private AssetIdentifierType assetIdentifierType;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The name of asset on which the scan is performed")
	private String assetIdentifierName;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The id of asset on which the scan is performed")
	private Integer assetId;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY, description = "The name of asset on which the scan is performed")
	private String assetName;

	@DbField
	@XmlElement
	@Schema(description = "The latest rule date taken into account to perform the scan")
	private Instant latestRuleDate;

	@DbField
	@XmlElement
	@Schema(description = "Set to true when scan is scanless (eg only detection performed)")
	private boolean scanless;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The id of the parent scan.")
	private Integer parentId;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The schedule id when scan triggered by a schedule.")
	private Integer scheduleId;

	@DbField(saveCast = "JSON", filterObject = AuthenticationFilter.class)
	private String authentication;

	/**
	 * Get tags.
	 *
	 * @return Array with tag objects
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The tags")
	public Tag[] getTags() throws JAXBException {
		return filterSystemTags(this.tags);
	}

	public ScanTemplate getTemplate() {
		return this.source;
	}

	public void setTemplate(final ScanTemplate template) {
		this.source = template;
	}

	public String getAuthenticationString() {
		return authentication;
	}

	@XmlElement
	@Schema(description = "The authentication used in the scan")
	public Authentication[] getAuthentication() throws JAXBException {
		return this.authentication == null ? null : MarshallingUtils.unmarshalList(Authentication.class, this.authentication).toArray(new Authentication[0]);
	}

	public void setAuthentication(final Authentication[] authentication) throws JAXBException {
		this.authentication = authentication == null ? null : MarshallingUtils.marshalList(Authentication.class, Arrays.asList(authentication));
	}

	public static class AuthenticationFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value,
								   final double gmtOffset) {
			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);
			if (isNull || isNotNull) {
				return fieldName + " IS " + (isNotNull ? "NOT " : "") + "NULL";
			}

			if (StringUtils.isEmpty(value)) {
				return "";
			}

			final String[] values = value.split(":", 2);

			final StringBuilder filter = new StringBuilder();
			if (!StringUtils.isEmpty(values[0])) {
				final String filterValue = StringUtils.escapeSQL(values[0]).replaceAll("\\?", "_").replaceAll("\\*", "%").trim();
				StringUtils.concatenateFilters(filter, "type ILIKE ?");
				params.add("%" + filterValue + "%");
			}

			if (values.length > 1 && !StringUtils.isEmpty(values[1])) {
				final String filterValue = StringUtils.escapeSQL(values[1]).replaceAll("\\?", "_").replaceAll("\\*", "%").trim();
				StringUtils.concatenateFilters(filter, "status ILIKE ?");
				params.add("%" + filterValue + "%");
			}

			if (filter.length() == 0) {
				return "";
			}

			return "EXISTS (SELECT * FROM jsonb_to_recordset(" + fieldName + ") AS x(type TEXT, status TEXT) WHERE " + filter.toString() + ")";
		}
	}
}
