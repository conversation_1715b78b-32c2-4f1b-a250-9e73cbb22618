package com.chilicoders.rest.models;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@Schema(name = "PasswordRecoveryRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
public class PasswordRecoveryRequest {
	@NotEmpty
	private String username;
}
