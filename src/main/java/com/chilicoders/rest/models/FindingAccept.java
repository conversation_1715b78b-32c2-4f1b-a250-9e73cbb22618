package com.chilicoders.rest.models;

import java.time.Instant;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import org.eclipse.persistence.oxm.annotations.XmlReadOnly;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "FindingAccept")
@XmlAccessorType(XmlAccessType.NONE)
public class FindingAccept {

	@XmlElement
	@NotNull
	@Future
	private Instant acceptedUntil;

	@XmlElement
	@XmlReadOnly
	private String acceptedComment;

	public Instant getAcceptedUntil() {
		return acceptedUntil;
	}

	public String getAcceptedComment() {
		return acceptedComment;
	}
}
