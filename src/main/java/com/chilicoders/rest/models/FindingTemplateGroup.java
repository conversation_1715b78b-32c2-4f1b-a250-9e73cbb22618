package com.chilicoders.rest.models;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "FindingTemplateGroup")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "findingtemplategroups", view = "findingtemplategroupsview")
public class FindingTemplateGroup extends BaseDbObject {

	@DbField
	@XmlElement
	@NotEmpty
	@Schema(description = "The name of the finding template group")
	private String name;

	@DbField(noDefault = true)
	@XmlElement
	@Schema(description = "The parent finding template group")
	private Integer parentId;

	@DbField(readViewOnly = true, restFilterable = false)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] pathUp;

	@DbField(readViewOnly = true, restFilterable = false)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] pathDown;
}
