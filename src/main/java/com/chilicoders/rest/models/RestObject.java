package com.chilicoders.rest.models;

import com.chilicoders.core.user.api.UserDetails;

public interface RestObject {
	Integer SYSTEM_USER = 0;

	Integer SYSTEM_TAG_ID = 0;

	int APPSEC_USER_ID = 100;

	/**
	 * Get id of updater.
	 *
	 * @param user User
	 * @return The user id.
	 */
	static Integer getUpdaterId(final UserDetails user) {
		if (user == null) {
			return SYSTEM_USER;
		}
		else if (user.getConsultancyMode() && user.getConsultancyUser() != null) {
			return (int) user.getConsultancyUser().getMainUserId();
		}
		return (int) (user.isSubUser() ? -user.getSubUserId() : user.getMainUserId());
	}
}
