package com.chilicoders.rest.models;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.ws.rs.core.StreamingOutput;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.core.encryption.api.exception.EncryptionException;
import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;
import com.chilicoders.core.reporting.api.model.ReportStatus;
import com.chilicoders.core.storage.api.model.GenericResourceIdentifier;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.model.DownloadEntryType;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "downloadentries")
@Getter
@Setter
public class BlueprintCacheEntry extends XmlAble {
	private static final Logger LOG = LogManager.getLogger(BlueprintCacheEntry.class);

	@DbField(id = true, set = "sync")
	private long id;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Date created = new Date();

	@DbField(isUserId = true)
	private long userId;

	@DbField(set = "sync")
	private String filename;

	@DbField
	@XmlElement(name = "name")
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String readableName;

	@DbField(isSubuserId = true)
	private long subUserId;

	@DbField
	private String contentType;

	@DbField
	private String contentDisposition;

	@DbField
	private String contentTransferEncoding;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String key;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String status;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long size;

	@DbField
	private String checksum;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String token;

	@DbField
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Date tokenExpires;

	@DbField(xmlOnly = true)
	private String readableSize;

	@DbField
	private DownloadEntryType type;

	@DbField
	private long scannerId;

	public BlueprintCacheEntry() {
		this.type = DownloadEntryType.BLUEPRINT;
	}

	/**
	 * Create a blueprint cache entry.
	 *
	 * @param user Logged on user.
	 */
	public BlueprintCacheEntry(final UserDetails user) {
		this(user, DownloadEntryType.BLUEPRINT);
	}

	/**
	 * Create a blueprint cache entry.
	 *
	 * @param user Logged on user.
	 * @param type The {@link DownloadEntryType} instance.
	 */
	public BlueprintCacheEntry(final UserDetails user, final DownloadEntryType type) {
		this.userId = user.getMainUserId();
		this.subUserId = user.getSubUserId();
		this.type = type;
	}

	/**
	 * Deletes data from disc if available.
	 */
	public void removeData() {
		if (this.filename != null) {
			FileUtils.deleteQuietly(new File(this.filename));
		}
	}

	public ReportStatus getStatus() {
		return ReportStatus.getStatusByLabel(this.status);
	}

	/**
	 * Creates a Response for downloading the blueprint.
	 *
	 * @param decryptFile If true, the blueprint file will be decrypted.
	 * @return The response.
	 */
	public Response createDownloadResponse(final boolean decryptFile) {
		final ResponseBuilder response = Response.ok();
		response.type(this.contentType);
		if (this.readableName != null) {
			response.header("Content-Disposition", "attachment; filename=\"" + this.readableName + "\"");
		}
		if (this.contentTransferEncoding != null) {
			response.header("Content-Transfer-Encoding", this.contentTransferEncoding);
		}
		response.header("X-Accel-Buffering", "no");
		response.entity((StreamingOutput) output -> {
			if (Configuration.isKubernetesEnabled() || isCloudStorage()) {
				// Get blueprints from K8S or S3
				final ResourceIdentifier resourceId = getResourceId();
				try (final InputStream is = ServiceProvider.getStorageService().getResourceStream(resourceId)) {
					if (decryptFile) {
						ServiceProvider.getEncryptionService().decrypt(EncryptionDecryptionRequest.builder().input(is).output(output).build());
					}
					else {
						IOUtils.copy(is, output);
					}
				}
				catch (final EncryptionException e) {
					LOG.error("Error decrypting blueprint file {}", this.getReadableName(), e);
				}
			}
			else {
				// Get blueprints from local storage
				try {
					if (decryptFile) {
						ServiceProvider.getEncryptionService().decrypt(EncryptionDecryptionRequest.builder().input(Paths.get(filename)).output(output).build());
					}
					else {
						Files.copy(Paths.get(filename), output);
					}
				}
				catch (final EncryptionException e) {
					LOG.error("Error decrypting local blueprint file {}", this.getReadableName(), e);
				}
			}
		});
		return response.build();
	}

	/**
	 * Check if the blueprint is in cloud storage.
	 *
	 * @return true if it's in cloud storage, false otherwise.
	 */
	private boolean isCloudStorage() {
		return getFilename() != null && getFilename().startsWith("s3://");
	}

	/**
	 * Get resource identifier from filename.
	 *
	 * @return A {@link ResourceIdentifier} instance, null if not using cloud storage.
	 */
	private ResourceIdentifier getResourceId() {
		if (!isCloudStorage()) {
			return null;
		}
		final String[] objectPath = this.getFilename().replace("s3://", "").split(ResourceIdentifier.SPLIT_CHARACTER, 2);
		return new GenericResourceIdentifier(objectPath[0], objectPath[1]);
	}
}
