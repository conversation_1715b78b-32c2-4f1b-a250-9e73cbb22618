package com.chilicoders.rest.models;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.chilicoders.bl.certificate.CsrCreationsResult;
import com.chilicoders.core.libellum.ServiceIdentity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Schema(name = "CertificateCreationRequest")
@XmlAccessorType(XmlAccessType.NONE)
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CertificateCreationRequest {
	@XmlElement
	private String certificateSigningRequest;

	@XmlElement
	private Long scannerId;

	@XmlElement
	private String key;

	@XmlElement
	private ServiceIdentity.Service service;

	/**
	 * Creates certificate renewal request data.
	 *
	 * @param csrResult the CSR creation result from libellum.
	 * @return data for certificate renewal.;
	 */
	public static CertificateCreationRequest create(final CsrCreationsResult csrResult) {
		return CertificateCreationRequest.builder()
				.certificateSigningRequest(csrResult.getResult())
				.build();
	}
}