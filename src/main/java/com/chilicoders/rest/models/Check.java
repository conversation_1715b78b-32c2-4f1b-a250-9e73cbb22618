package com.chilicoders.rest.models;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.model.Severity;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Check")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "", view = "checksview")
public class Check extends XmlAble {

	@Getter
	@DbField(id = true, filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The check id")
	private Integer id;

	@Getter
	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The check CVE")
	private String cve;

	@DbField(filterable = true)
	private String bugTraq;

	@Getter
	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The check name")
	private String name;

	@DbField(filterable = true, readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The CVSS V2 score")
	private double nvdCvssV2Score;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The CVSS V3 score")
	private Double nvdCvssV3Score;

	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The CVSS V2 vector")
	private String nvdCvssV2Vector;

	@DbField(filterable = true, readViewOnly = true)
	@Schema(readOnly = true, description = "The CVSS V3 vector")
	private String nvdCvssV3Vector;

	@DbField(listfilter = true, filterClass = String.class, enumFilterName = "severity")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The CVSS V3 Severity")
	private Severity cvssV3Severity;

	@DbField(filterable = true, saveAsInteger = true, forceBooleanFilter = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "Set to true when exploits are available")
	private boolean hasExploits;

	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The created date")
	private Instant created;

	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The updated date")
	private Instant updated;

	@DbField(filterable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The check description")
	private String description;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Double cyrating;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Double previousCyrating;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Double cyratingDelta;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Instant cyratingUpdated;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Instant cyratingLastSeen;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The exploit probability")
	private Double exploitProbability;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The previous exploit probability")
	private Double previousExploitProbability;

	@DbField(filterable = true, readViewOnly = true, isNullable = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The exploit probability delta")
	private Double exploitProbabilityDelta;

	@DbField(filterable = true, readViewOnly = true, isNullable = true, saveCast = "JSON", jsonClass = Farsight.class)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private String farsight;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The software component")
	private String softwareComponent;

	@DbField(readViewOnly = true, listfilter = true, filterClass = Integer.class, enumFilterName = "integer")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The OWASP 2017 scores")
	private Integer[] owasp2017;

	@DbField(readViewOnly = true, listfilter = true, filterClass = String.class, enumFilterName = "solutiontype")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The solution type")
	private SolutionType solutionType;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The solution")
	private String solution;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The solution product")
	private String solutionProduct;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The solution title")
	private String solutionTitle;

	@DbField(readViewOnly = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The solution uuid")
	private String solutionUuid;

	@DbField(readViewOnly = true, isNullable = true, saveCast = "JSON", jsonClass = Classification.Classifications.class)
	private String classifications;

	/**
	 * Get bugTraq ids.
	 *
	 * @return Array with bugTraq ids
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The bug track")
	public int[] getBugTraq() {
		if (StringUtils.isEmpty(this.bugTraq)) {
			return null;
		}
		final List<Integer> bugTraqs = new ArrayList<>();
		for (final String bugTraq : this.bugTraq.split(",")) {
			final int bugTraqId = StringUtils.getIntValue(bugTraq.trim());
			if (bugTraqId > 0) {
				bugTraqs.add(bugTraqId);
			}
		}
		return bugTraqs.size() == 0 ? null : bugTraqs.stream().mapToInt(i -> i).toArray();
	}

	/**
	 * Get cvss v3 vector.
	 *
	 * @return Vector
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The CVSS V3 vector")
	public String getNvdCvssV3Vector() {
		if (StringUtils.isEmpty(this.nvdCvssV3Vector)) {
			return null;
		}
		return "(" + this.nvdCvssV3Vector.replaceFirst("CVSS:3.[01]/", "") + ")";
	}

	/**
	 * Unmarshall JSON string from DB to object.
	 *
	 * @return The unmarshalled object.
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The farsight consists in additional information on risk such as likelihood, threat activity")
	public Farsight getFarsight() throws JAXBException {
		return this.farsight == null ? null : MarshallingUtils.unmarshal(Farsight.class, this.farsight);
	}

	/**
	 * Unmarshall JSON string from DB to object.
	 *
	 * @return The unmarshalled object.
	 */
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	public Classification.Classifications getClassifications() throws JAXBException {
		return this.classifications == null ? null : MarshallingUtils.unmarshal(Classification.Classifications.class, this.classifications);
	}
}
