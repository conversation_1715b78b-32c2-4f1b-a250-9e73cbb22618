package com.chilicoders.rest.models;

import java.time.Instant;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.model.AssetLinkInterface;
import com.chilicoders.model.ScanSelectionType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "AssetLink")
@DbTable(name = "asset_assetidentifier")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
public class AssetLink extends XmlAble implements AssetLinkInterface {

	@DbField(id = true, autoIncrementedId = true, dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Integer id;

	@DbField
	@XmlElement
	private Integer assetId;

	@DbField
	@XmlElement
	@NotNull
	private Integer assetIdentifierId;

	@DbField
	@XmlElement
	@NotNull
	private Instant firstSeen;

	@DbField
	@XmlElement
	@NotNull
	private Instant lastSeen;

	@DbField
	@XmlElement
	private ScanSelectionType scanSelectionType = ScanSelectionType.INCLUDED;
}
