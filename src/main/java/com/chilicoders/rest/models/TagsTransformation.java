package com.chilicoders.rest.models;

import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlReadOnly;
import org.json.JSONObject;

import com.chilicoders.util.MarshallingUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(name = "TagsTransformation")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
public class TagsTransformation {

	private String objects;

	private String from;

	private String to;

	@XmlElement
	@XmlReadOnly
	public void setObjects(final Objects objects) throws JAXBException {
		this.objects = objects == null ? null : MarshallingUtils.marshal(objects);
	}

	public Objects getObjects() throws JAXBException {
		return this.objects == null ? null : MarshallingUtils.unmarshal(Objects.class, this.objects);
	}

	public JSONObject getObjectsJson() {
		return this.objects == null ? null : new JSONObject(this.objects);
	}

	@XmlElement
	@XmlReadOnly
	public void setFrom(final ToFrom from) throws JAXBException {
		this.from = from == null ? null : MarshallingUtils.marshal(from);
	}

	public ToFrom getFrom() throws JAXBException {
		return this.from == null ? null : MarshallingUtils.unmarshal(ToFrom.class, this.from);
	}

	@XmlElement
	@XmlReadOnly
	public void setTo(final ToFrom to) throws JAXBException {
		this.to = to == null ? null : MarshallingUtils.marshal(to);
	}

	public ToFrom getTo() throws JAXBException {
		return this.to == null ? null : MarshallingUtils.unmarshal(ToFrom.class, this.to);
	}

	@Getter
	@Schema(name = "TagsTransformation.ToFrom")
	@XmlAccessorType(XmlAccessType.FIELD)
	public static class ToFrom {
		private String key;

		private boolean allKeys;

		private String value;

		private boolean allValues;
	}

	@Getter
	@Schema(name = "TagsTransformation.Objects")
	@XmlAccessorType(XmlAccessType.FIELD)
	public static class Objects {
		private boolean accounts;

		private boolean assetGroups;

		private boolean assets;

		private boolean assetIdentifiers;

		private boolean eventSubscriptions;

		private boolean managedReports;

		private boolean scheduledReports;

		private boolean complianceFindings;

		private boolean findings;

		private boolean information;

		private boolean scanConfigurations;

		private boolean dashboards;

		private boolean integrations;

		private boolean scanPolicies;

		private boolean viewTemplates;

		private boolean users;

		private boolean scanConfigurationSettings;

		private boolean scheduledReportSettings;

		private boolean resourceGroupSettings;

		private boolean workflows;
	}
}
