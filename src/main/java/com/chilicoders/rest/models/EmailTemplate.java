package com.chilicoders.rest.models;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.db.objects.XmlAble;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name = "EmailTemplate")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "emailtemplates")
public class EmailTemplate extends XmlAble {

	/**
	 * The email template id.
	 */
	@DbField(id = true, autoIncrementedId = true, dontSave = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true, description = "The id of the email template")
	private Integer id;

	/**
	 * The email template name.
	 */
	@DbField
	@XmlElement
	@NotEmpty
	private String name;

	/**
	 * The email subject.
	 */
	@DbField
	@XmlElement
	private String subject;

	/**
	 * The email content.
	 */
	@DbField
	@XmlElement
	private String content;
}
