package com.chilicoders.rest.util;

import java.io.IOException;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.Header;
import com.auth0.jwt.interfaces.Payload;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.rest.exceptions.UnauthorizedException;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.AuthenticationV1Api;
import io.kubernetes.client.openapi.models.V1TokenReview;
import io.kubernetes.client.openapi.models.V1TokenReviewSpec;
import io.kubernetes.client.openapi.models.V1TokenReviewStatus;
import io.kubernetes.client.util.Config;
import lombok.Data;

/**
 * Utility class for dealing with JWT tokens
 */
public class JWTData {

	private static ApiClient client;

	static {
		if (com.chilicoders.util.Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled)) {
			try {
				client = Config.defaultClient();
				Configuration.setDefaultApiClient(client);
			}
			catch (final IOException e) {
				throw new RuntimeException(e);
			}
		}
	}

	/**
	 * Data class for a parsed JWT
	 */
	@Data
	public static class Parsed {
		private final Header header;
		private final Payload payload;
	}

	/**
	 * Parse JWT token and return a {@link Parsed} object
	 *
	 * @param jwt Token as a string
	 * @return Parsed instance
	 */
	public static Parsed parseToken(final String jwt) {
		final String[] chunks = jwt.split("\\.");
		if (chunks.length < 2) {
			throw new IllegalArgumentException("Not a valid token");
		}
		final Base64.Decoder decoder = Base64.getUrlDecoder();
		final com.auth0.jwt.impl.JWTParser jwtParser = new com.auth0.jwt.impl.JWTParser();
		final String header = new String(decoder.decode(chunks[0]), Charset.defaultCharset());
		final String payload = new String(decoder.decode(chunks[1]), Charset.defaultCharset());
		final Header parsedHeader = jwtParser.parseHeader(header);
		final Payload parsedPayload = jwtParser.parsePayload(payload);

		return new Parsed(parsedHeader, parsedPayload);
	}

	/**
	 * Check if token is expired
	 *
	 * @param jwtData A parsed token
	 * @return true is token is expired
	 */
	public static boolean isExpired(final Parsed jwtData) {
		return Instant.now().isAfter(jwtData.getPayload().getExpiresAtAsInstant());
	}

	/**
	 * Get subject from token
	 *
	 * @param jwtData Parsed token
	 * @return A String containing the subject
	 */
	public static String getSubject(final Parsed jwtData) {
		return jwtData.getPayload().getSubject();
	}

	/**
	 * Get service account name from parsed token
	 *
	 * @param jwtData Parsed token
	 * @return A String containing the service account name
	 */
	public static String getServiceAccountName(final Parsed jwtData) {
		return getNestedClaim(jwtData.getPayload(), "kubernetes.io/serviceaccount/name");
	}

	/**
	 * Get a claim value on a specific path
	 *
	 * @param payload The payload from a parsed JWT
	 * @param path The path on which the value exists
	 * @return A string representation of the claim value
	 * @throws RuntimeException when an incorrect path is given
	 */
	@SuppressWarnings("unchecked")
	private static String getNestedClaim(final Payload payload, final String path) {
		final String[] parts = path.split("/");
		final String lastPart = parts[parts.length - 1];
		Map<String, Claim> current = payload.getClaim(parts[0]).as(Map.class);
		for (int i = 1; i < parts.length - 1; i++) {
			if (current == null) {
				throw new RuntimeException("Claim " + path + " not found");
			}
			current = (Map<String, Claim>) current.get(parts[i]);
		}
		if (current == null || current.get(lastPart) == null) {
			throw new RuntimeException("Claim " + path + " not found");
		}
		return String.valueOf(current.get(lastPart));
	}

	/**
	 * Check if a parsed token is a k8s token
	 *
	 * @param jwtData Parsed JWT
	 * @return true if issued by k8s, false otherwise
	 */
	public static boolean isK8sToken(final Parsed jwtData) {
		final Claim aud = jwtData.getPayload().getClaim("aud");
		final List<String> audList = aud.asList(String.class);
		final Claim sub = jwtData.getPayload().getClaim("sub");
		if (sub == null || audList == null) {
			return false;
		}

		final String subString = sub.asString();
		if (subString != null && subString.contains("system:serviceaccount")) { // "contains()" because sometimes the string starts with "
			for (final String audience : audList) {
				if (audience.contains("https://kubernetes.default.svc")) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Get user UID from a parsed JWT
	 *
	 * @param jwtData Parsed JWT
	 * @return String containing a user UID
	 */
	public static String getUserUID(final Parsed jwtData) {
		return getNestedClaim(jwtData.getPayload(), "kubernetes.io/serviceaccount/uid");
	}

	/**
	 * Validate token by asking k8s to check the signature
	 *
	 * @param token A JWT string
	 * @throws ApiException When there is an error in the kubernetes API
	 */
	public static void validateToken(final String token) throws ApiException {
		final Parsed jwtData = JWTData.parseToken(token);

		if (isExpired(jwtData)) {
			throw new UnauthorizedException("Authentication token expired");
		}
		if (getSubject(jwtData) == null) {
			throw new UnauthorizedException("Invalid token claims: subject is missing");
		}
		final AuthenticationV1Api authApi = new AuthenticationV1Api();

		// Setting up the TokenReview request
		final V1TokenReview tokenReview = new V1TokenReview();
		final V1TokenReviewSpec spec = new V1TokenReviewSpec();
		spec.setToken(token);

		tokenReview.setSpec(spec);

		// Perform the token review
		final V1TokenReview result = authApi.createTokenReview(tokenReview, null, null, null);

		final V1TokenReviewStatus status = result.getStatus();
		if (status.getAuthenticated() == null || !status.getAuthenticated()) {
			throw new UnauthorizedException("Invalid token. Token review failed: " + status.getError());
		}
	}
}
