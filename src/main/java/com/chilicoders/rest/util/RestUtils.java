package com.chilicoders.rest.util;

import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

import javax.ws.rs.core.Response.Status;

import org.apache.commons.io.FileUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONObject;

import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.CertificateUtils;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.HttpsThread;
import com.chilicoders.util.StringUtils;

public class RestUtils {

	private static final File TOKEN_PATH = new File(Configuration.getProperty(ConfigurationKey.kubernetes_token_path));

	/**
	 * Post a request on given URI with given JSON data.
	 *
	 * @param uri the URI where the request should be sent.
	 * @param data the data to be sent.
	 * @param useProxy Set to true if use a proxy for this request.
	 * @return the response of the request or null if could not create the client.
	 */
	public static HttpResponse post(final String uri, final JSONObject data, final boolean useProxy) throws IOException, CertificateException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		return post(uri, data, useProxy, true);
	}

	/**
	 * Post a request on given URI with given JSON data.
	 *
	 * @param uri the URI where the request should be sent.
	 * @param data the data to be sent.
	 * @param useProxy Set to true if use a proxy for this request.
	 * @param useK8sJwt Set to true if k8s JWT token should be added as header to request.
	 * @param skipExtendedIpaCa Skip the use of extended ipaca for this post.
	 * @return the response of the request or null if could not create the client.
	 */
	public static HttpResponse post(final String uri, final JSONObject data, final boolean useProxy, final boolean useK8sJwt, final boolean skipExtendedIpaCa)
			throws IOException, CertificateException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		final StringEntity input = new StringEntity(data.toString());
		input.setContentType(APPLICATION_JSON);
		return post(uri, input, useProxy, useK8sJwt, skipExtendedIpaCa);
	}

	/**
	 * Post a request on given URI with given JSON data.
	 *
	 * @param uri the URI where the request should be sent.
	 * @param data the data to be sent.
	 * @param useProxy Set to true if use a proxy for this request.
	 * @param useK8sJwt Set to true if k8s JWT token should be added as header to request.
	 * @return the response of the request or null if could not create the client.
	 */
	public static HttpResponse post(final String uri, final JSONObject data, final boolean useProxy, final boolean useK8sJwt)
			throws IOException, CertificateException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		return post(uri, data, useProxy, useK8sJwt, false);
	}

	/**
	 * Post a request on given URI.
	 *
	 * @param uri the URI where the request should be sent.
	 * @param entity The data to be sent.
	 * @param useProxy Set to true if use a proxy for this request.
	 * @param useK8sJwt Set to true if k8s JWT token should be added as header to request.
	 * @param skipExtendedIpaCa Skip the use of extended ipaca for this post.
	 * @return the response of the request or null if could not create the client.
	 */
	public static HttpResponse post(final String uri, final HttpEntity entity, final boolean useProxy, final boolean useK8sJwt, final boolean skipExtendedIpaCa)
			throws IOException, CertificateException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		final RequestConfig.Builder config = RequestConfig.custom();
		config.setSocketTimeout(Configuration.getProperty(ConfigurationIntKey.https_timeout) * 1000);
		config.setConnectTimeout(Configuration.getProperty(ConfigurationIntKey.https_connect_timeout) * 1000);
		config.setConnectionRequestTimeout(Configuration.getProperty(ConfigurationIntKey.https_timeout) * 1000);

		final HttpClient httpClient = HttpsThread.getNewHttpClient(useProxy, skipExtendedIpaCa);
		final HttpPost postRequest = new HttpPost(uri);
		postRequest.setConfig(config.build());

		if (useK8sJwt) {
			String token = getToken();
			if (token == null) {
				token = Configuration.getProperty(ConfigurationKey.libellum_service_override_token);
			}
			if (token != null) {
				postRequest.addHeader("Authorization", "Bearer " + token);
			}
		}

		if (CertificateUtils.shouldUseApplianceSni(ServiceProvider.getLibellumConfig())) {
			postRequest.setHeader("Host", Configuration.getProperty(ConfigurationKey.appliance_sni));
		}

		postRequest.setEntity(entity);

		// For test add certificate in the header. In production environment NGINX does that.
		// Now since DEV-11245, we should forward the libellum cert to the K8s-based libellum service
		final boolean isTestMode = Configuration.getProperty(ConfigurationBooleanKey.test_mode);
		final boolean isExtendCertIpaCa = Configuration.getProperty(ConfigurationBooleanKey.certificate_extend_ssl_context_ipa_ca);
		if ((isTestMode || isExtendCertIpaCa) && !skipExtendedIpaCa) {
			final String certificate = StringUtils.readTextFile(ServiceProvider.getLibellumConfig().getClientCertificatePath());
			final String encodedCertificate = URLEncoder.encode(certificate, StandardCharsets.UTF_8.name());
			postRequest.setHeader("X-SSL-CERT", encodedCertificate);
		}

		return httpClient.execute(postRequest);
	}

	public static boolean isSuccessfulResponse(final HttpResponse response) {
		return response != null && response.getStatusLine() != null && response.getStatusLine().getStatusCode() == Status.OK.getStatusCode();
	}

	/**
	 * Get token to use in k8s environment
	 *
	 * @return Token as a string
	 * @throws IOException if reading token from file fails.
	 */
	private static String getToken() throws IOException {
		if (TOKEN_PATH.exists()) {
			return FileUtils.readFileToString(TOKEN_PATH, StandardCharsets.UTF_8);
		}
		return null;
	}
}
