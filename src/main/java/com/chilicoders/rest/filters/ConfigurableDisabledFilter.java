package com.chilicoders.rest.filters;

import javax.annotation.Priority;
import javax.ws.rs.Priorities;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.Context;
import javax.ws.rs.ext.Provider;

import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.rest.annotations.ConfigurableDisabled;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.util.Configuration;

@Provider
@Priority(Priorities.AUTHORIZATION)
public class ConfigurableDisabledFilter extends BaseFilter implements ContainerRequestFilter {
	@Context
	private ResourceInfo resourceInfo;

	@Override
	public void filter(final ContainerRequestContext context) {
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.disable_endpoints)
				&& (resourceInfo.getResourceMethod().isAnnotationPresent(ConfigurableDisabled.class)
				|| resourceInfo.getResourceClass().isAnnotationPresent(ConfigurableDisabled.class))
		) {
			throw new NotFoundException();
		}
		if (Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.reject_non_hiab_requests)) {
			throw new NotFoundException();
		}
	}
}