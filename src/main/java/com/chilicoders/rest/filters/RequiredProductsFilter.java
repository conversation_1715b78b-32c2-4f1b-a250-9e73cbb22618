package com.chilicoders.rest.filters;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Priority;
import javax.ws.rs.Priorities;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.Context;
import javax.ws.rs.ext.Provider;

import com.chilicoders.bl.UserBusiness;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.model.Product;
import com.chilicoders.rest.annotations.RequiredProducts;
import com.chilicoders.rest.exceptions.ForbiddenException;

@Provider
@Priority(Priorities.AUTHORIZATION)
@RequiredProducts
public class RequiredProductsFilter extends BaseFilter implements ContainerRequestFilter {
	@Context
	private ResourceInfo resourceInfo;

	@Override
	public void filter(final ContainerRequestContext context) {
		final RequiredProducts annotation =
				resourceInfo.getResourceMethod().getAnnotation(RequiredProducts.class) != null ? resourceInfo.getResourceMethod().getAnnotation(RequiredProducts.class) :
						resourceInfo.getResourceClass().getAnnotation(RequiredProducts.class);
		final BaseLoggedOnUser user = (BaseLoggedOnUser) context.getProperty("user");
		if (user == null) {
			return;
		}

		@SuppressWarnings("unused")
		final boolean readOnly =
				context.getMethod().equalsIgnoreCase("GET") || context.getMethod().equalsIgnoreCase("HEAD") || !context.getMethod().equalsIgnoreCase("OPTIONS");
		final UserBusiness ub = new UserBusiness(user);

		int hasNumberOfProducts = 0;

		for (final Product product : annotation.products()) {
			final boolean hasProduct = ub.hasProduct(product);
			hasNumberOfProducts += hasProduct ? 1 : 0;
			if (hasProduct && annotation.or()) {
				return;
			}
		}

		if (hasNumberOfProducts < annotation.products().length) {
			final List<String> products = new ArrayList<>();
			for (final Product product : annotation.products()) {
				products.add(product.name());
			}
			throw new ForbiddenException("_MISSING_PRODUCTS", null, products);
		}
	}
}
