package com.chilicoders.rest.parameters;

import javax.ws.rs.DefaultValue;
import javax.ws.rs.QueryParam;

import com.chilicoders.bl.MessageBusiness;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.Configuration;

import io.swagger.v3.oas.annotations.Parameter;

public class PaginationParameters {
	@Parameter(description = "Maximum number of entries to return")
	@QueryParam("limit")
	@DefaultValue("-1")
	private int limit;
	@Parameter(description = "Zero-based offset from first entry in list")
	@QueryParam("offset")
	@DefaultValue("0")
	private int offset;
	@Parameter(description = "One-based page number with page size equal to limit")
	@QueryParam("page")
	@DefaultValue("-1")
	private int page;

	public PaginationParameters() {
	}

	/**
	 * Get the limit to use in the SQL query.
	 *
	 * @return The limit to use in the SQL query.
	 */
	public int getLimit() {
		return (this.limit < 1 || this.limit > Configuration.getProperty(ConfigurationIntKey.rest_api_max_limit)) ? Configuration.getProperty(
				ConfigurationIntKey.rest_api_max_limit) : this.limit;
	}

	/**
	 * Get the limit to use in the SQL query ignoring rest_api_max_limit.
	 *
	 * @return The actual limit to use in the SQL query.
	 */
	public int getLimitIgnoreMaxLimit() {
		return this.limit < 1 ? -1 : this.limit;
	}

	/**
	 * Get the offset to use in the SQL query.
	 *
	 * @return The offset to use in the SQL query.
	 */
	public int getOffset() {
		if (this.page > 0 && this.offset < 1) {
			return (this.page - 1) * this.getLimit();
		}
		if (this.limit > Configuration.getProperty(ConfigurationIntKey.rest_api_max_limit)) {
			throw new InputValidationException(String.format(MessageBusiness.getMessageString("_EXCEEDS_REQUEST_LIMIT_SET_LIMIT_UNDER", "en"),
					Long.toString(Configuration.getProperty(ConfigurationIntKey.rest_api_max_limit))));
		}
		return this.offset < 0 ? 0 : this.offset;
	}

	/**
	 * Get the offset to use in the SQL query ignoring rest_api_max_limit.
	 *
	 * @return The actual offset to use in the SQL query.
	 */
	public int getOffsetIgnoreMaxLimit() {
		if (this.page > 0 && this.offset < 1) {
			return (this.page - 1) * this.getLimitIgnoreMaxLimit();
		}
		return this.offset < 0 ? 0 : this.offset;
	}
}
