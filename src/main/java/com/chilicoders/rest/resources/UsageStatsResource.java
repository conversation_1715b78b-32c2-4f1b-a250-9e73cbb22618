package com.chilicoders.rest.resources;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.rest.annotations.RequiredRoles;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.Cyr3conUsageStats;
import com.chilicoders.rest.models.UsageStats;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = UsageStatsResource.PATH)
@Path(UsageStatsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredRoles(roles = {Role.Administrator, Role.Support, Role.LicenseManager, Role.CustomerSuccess, Role.MsspManager})
public class UsageStatsResource extends BaseResource {
	public static final String PATH = "usage-stats";
	public static final String CYR3CON = "cyr3con";

	/**
	 * Get header with total count of {@link UsageStats}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getUsageStatsHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getUsageStatsFromDb(getConnection(), getUser(), null, null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	@GET
	@Operation(summary = "Get a list of usage stats",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = UsageStats.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getUsageStatsList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<UsageStats>>() {
		}, getUsageStatsFromDb(getConnection(), getUser(), null, null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get usage stats by ID.
	 *
	 * @param usageStatsId The ID of the usage stats to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The usage stats.
	 */
	@GET
	@Path("{usageStatsId: \\d+}")
	@Operation(summary = "Get usage stats by ID")
	public UsageStats getUsageStats(@Parameter(required = true) @PathParam("usageStatsId") final Integer usageStatsId,
									@BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<UsageStats> stats = getUsageStatsFromDb(getConnection(), getUser(), usageStatsId, null, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (stats.isEmpty()) {
			throw new NotFoundException();
		}
		return stats.get(0);
	}

	/**
	 * Get stats for cyr3con usage.
	 *
	 * @return The usage stats.
	 */
	@GET
	@Path(CYR3CON)
	@Operation(summary = "Get cyr3con stats")
	public Response getCyr3conStats() throws SQLException {
		final Cyr3conUsageStats stats = DbObject.get(Cyr3conUsageStats.class, getConnection(), "SELECT COUNT(*)::INTEGER AS users, "
				+ "(SELECT SUM(COALESCE(cyr3conassets, 0) + COALESCE(cyr3conapplianceassets, 0)) FROM usagestats)::INTEGER AS targets FROM tusers WHERE farsightproducts IS NOT NULL");
		return Response.ok(stats).build();
	}

	/**
	 * Get a list of usage stats from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param customerId Optional customer ID to get stats for.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<UsageStats>> getUsageStatsFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id, final Integer customerId,
																	  final PaginationParameters paginationParameters,
																	  final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																	  final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects)
			throws SQLException {
		final List<Object> params = new ArrayList<>();
		final StringBuilder filter = new StringBuilder("deleted IS NULL");

		if (customerId != null) {
			StringUtils.concatenateFilters(filter, "customerid = ?");
			params.add(customerId);
		}
		else if (user.hasRoles(Role.MsspManager)) {
			StringUtils.concatenateFilters(filter,
					"(customerid = ? OR customerid IN (SELECT id FROM customers WHERE userid IN (SELECT xid FROM tusers WHERE xiparentid = ANY(?))))");
			params.add(user.getCustomerId());
			params.add(DbHelper.createIdArray(user.getSalesOrganizations()));
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		return UsageStats.getObjects(UsageStats.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, filter.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
