package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.XmlAble.ObjectsResponse;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.model.ViewType;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.ViewTemplate;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ViewTemplatesResource.PATH)
@Path(ViewTemplatesResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ViewTemplatesResource extends TaggableResource {
	public static final String PATH = "view-templates";

	private static final Pair<String, String> TAG_LINK_TABLE = ImmutablePair.of("tag_viewtemplate", "viewtemplateid");

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	@Override
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Pair<String, String> getTagLinkTable() {
		return TAG_LINK_TABLE;
	}

	/**
	 * Get header with total count of {@link ViewTemplate}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.VIEW_TEMPLATES_VIEW, UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE})
	public Response getViewTemplatesHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getViewTemplatesFromDb(getConnection(), getUser(), null, null, true, filteringParameters, true, false).getCount();

		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link ViewTemplate}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link ViewTemplate}s.
	 */
	@GET
	@Operation(summary = "Get a list of view templates",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ViewTemplate.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.VIEW_TEMPLATES_VIEW, UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE})
	public Response getViewTemplateList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(getViewTemplatesFromDb(getConnection(), getUser(), null, null, true, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, true, true, true));
	}

	/**
	 * Get view template by ID.
	 *
	 * @param viewTemplateId The ID of the view template to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The view template.
	 */
	@GET
	@Path("{viewTemplateId: \\d+}")
	@Operation(summary = "Get view template by ID")
	@RequiredPermissions(permissions = {UserRolePermission.VIEW_TEMPLATES_VIEW, UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE})
	public ViewTemplate getViewTemplate(
			@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		return getViewTemplateFromDb(viewTemplateId, fieldSelectionParameters);
	}

	/**
	 * Create a {@link ViewTemplate}.
	 *
	 * @param viewTemplate The {@link ViewTemplate} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a view template")
	@Valid
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response createViewTemplate(
			@Parameter(required = true) @Valid @NotNull final ViewTemplate viewTemplate,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws URISyntaxException, SQLException {

		if (getUser().hasLimitResources(ResourceType.VIEWTEMPLATE)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		if (viewTemplate.getType() == ViewType.FINDING_TEMPLATES && !getUser().hasRoles(Role.GhostLabs)) {
			throw new ForbiddenException();
		}

		final Integer existingViewTemplateId = (Integer) DbObject.getObject(getConnection(),
				"SELECT id FROM viewtemplates WHERE customerid = ? AND name = ? AND type = ? AND deleted IS NULL", getUser().getCustomerId(), viewTemplate.getName(),
				viewTemplate.getType());
		if (existingViewTemplateId != null) {
			throw new InputValidationException("_VIEW_TEMPLATE_ALREADY_EXISTS");
		}

		viewTemplate.setCustomerId(getUser());
		viewTemplate.setCreatedById(getUpdaterId());

		if (viewTemplate.getGroupId() != null) {
			ViewTemplateGroupsResource.validateGroupAndParentHierarchy(getConnection(), getUser(), viewTemplate.getGroupId(), null, false, viewTemplate.getType());
		}

		final int viewTemplateId = (int) viewTemplate.save(getConnection());

		if (viewTemplate.isDefault()) {
			updateDefaultViewTemplate(getConnection(), getUser(), viewTemplate.getType(), viewTemplateId);
		}

		getConnection().commit();

		final Response.ResponseBuilder response = createCreatedResponse(viewTemplateId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getViewTemplatesFromDb(getConnection(), getUser(), viewTemplateId, null, true, null, false, true).getObjects().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link ViewTemplate}.
	 *
	 * @param viewTemplateId The ID of the {@link ViewTemplate} to update.
	 * @param patch The JSON patch.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{viewTemplateId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a view template")
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response patchViewTemplate(
			@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = ViewTemplate.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final ViewTemplate oldViewTemplate = getViewTemplateFromDb(viewTemplateId, fieldSelectionParameters);

		final Set<String> missingColumns = new HashSet<>();
		final ViewTemplate newViewTemplate = ViewTemplate.patch(oldViewTemplate, patch, missingColumns);
		newViewTemplate.setId(viewTemplateId);
		newViewTemplate.setType(oldViewTemplate.getType());
		newViewTemplate.setCustomerId(oldViewTemplate.getCustomerId());
		newViewTemplate.setCreatedById(oldViewTemplate.getCreatedById());
		newViewTemplate.setUpdatedById(getUpdaterId());

		if (!oldViewTemplate.isSystem()) {
			final Integer existingViewTemplateId = (Integer) DbObject.getObject(getConnection(),
					"SELECT id FROM viewtemplates WHERE id != ? AND customerid = ? AND name = ? AND type = ? AND deleted IS NULL", viewTemplateId,
					newViewTemplate.getCustomerId(), newViewTemplate.getName(), newViewTemplate.getType());
			if (existingViewTemplateId != null) {
				throw new InputValidationException("_VIEW_TEMPLATE_ALREADY_EXISTS");
			}

			if (newViewTemplate.getGroupId() != null && !newViewTemplate.getGroupId().equals(oldViewTemplate.getGroupId())) {
				ViewTemplateGroupsResource.validateGroupAndParentHierarchy(getConnection(), getUser(), newViewTemplate.getGroupId(), null, false, newViewTemplate.getType());
			}

			newViewTemplate.save(getConnection());
		}

		if (!missingColumns.contains("isDefault") && oldViewTemplate.isDefault() != newViewTemplate.isDefault()) {
			updateDefaultViewTemplate(getConnection(), getUser(), newViewTemplate.getType(), oldViewTemplate.isDefault() ? null : viewTemplateId);
		}

		getConnection().commit();

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getViewTemplatesFromDb(getConnection(), getUser(), viewTemplateId, null, true, null, false, true).getObjects().get(0)).build();
		}
		return null;
	}

	/**
	 * Update default {@link ViewTemplate}.
	 *
	 * @param conn Database connection
	 * @param user User
	 * @param viewTemplateType {@link ViewTemplate} type
	 * @param viewTemplateId Id of {@link ViewTemplate} that should be set as default
	 */
	private void updateDefaultViewTemplate(final Connection conn, final BaseLoggedOnUser user, final ViewType viewTemplateType, final Integer viewTemplateId)
			throws SQLException {
		// Lock transaction per user to make sure set default view template runs in sequence to avoid duplicate key exception on viewtemplate_defaults_unique.
		DbObject.execute(conn, "SELECT COUNT(*) FROM pg_advisory_xact_lock(5433, ?::integer)", user.isSubUser() ? user.getSubUserId() : user.getMainUserId());

		ViewTemplate.executeUpdate(conn, "DELETE FROM viewtemplate_defaults WHERE viewtemplatetype = ? AND " + (user.isSubUser() ? "subuserid" : "userid") + " = ?",
				viewTemplateType, user.isSubUser() ? user.getSubUserId() : user.getMainUserId());
		if (viewTemplateId != null) {
			ViewTemplate.executeUpdate(conn,
					"INSERT INTO viewtemplate_defaults (viewtemplateid, viewtemplatetype, " + (user.isSubUser() ? "subuserid" : "userid") + ") VALUES (?, ?, ?)",
					viewTemplateId, viewTemplateType, user.isSubUser() ? user.getSubUserId() : user.getMainUserId());
		}
	}

	/**
	 * Delete a {@link ViewTemplate}.
	 *
	 * @param viewTemplateId The ID of the {@link ViewTemplate} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{viewTemplateId: \\d+}")
	@Operation(summary = "Delete a view template")
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response deleteViewTemplate(@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId) throws SQLException {
		final ViewTemplate viewTemplate = getViewTemplateFromDb(viewTemplateId, null);
		if (viewTemplate.isSystem()) {
			throw new ForbiddenException("_NOT_ALLOWED_TO_CHANGE_SYSTEM_VIEW_TEMPLATES");
		}

		viewTemplate.setDeleted(getUpdaterId());
		viewTemplate.save(getConnection());
		ViewTemplate.executeUpdate(getConnection(), "DELETE FROM viewtemplate_defaults WHERE viewtemplateid = ?", viewTemplate.getId());
		getConnection().commit();

		return null;
	}

	/**
	 * Link a {@link ViewTemplate} to a {@link com.chilicoders.rest.models.Tag}.
	 *
	 * @param viewTemplateId The {@link ViewTemplate} to link.
	 * @param tagId The {@link com.chilicoders.rest.models.Tag} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{viewTemplateId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Create a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response putViewTemplateTagLink(@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId,
										   @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException, URISyntaxException {
		modifyViewTemplateTagLinks(TagModifyInfo.builder()
				.resourceId(viewTemplateId)
				.addedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return createCreatedResponse(viewTemplateId + "/tags/" + tagId).build();
	}

	/**
	 * Delete a link between a {@link ViewTemplate} and a {@link com.chilicoders.rest.models.Tag}.
	 *
	 * @param viewTemplateId The {@link ViewTemplate} to unlink.
	 * @param tagId The {@link com.chilicoders.rest.models.Tag} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{viewTemplateId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response deleteViewTemplateTagLink(@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId,
											  @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException {
		modifyViewTemplateTagLinks(TagModifyInfo.builder()
				.resourceId(viewTemplateId)
				.deletedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link com.chilicoders.rest.models.Tag} linked to a {@link ViewTemplate}
	 *
	 * @param viewTemplateId The {@link ViewTemplate} to modify
	 * @param tagIds The new set of {@link com.chilicoders.rest.models.Tag}
	 * @return The response
	 */
	@PUT
	@Path("{viewTemplateId: \\d+}/tags")
	@Operation(summary = "Modify linked set of tags")
	@RequiredPermissions(permissions = UserRolePermission.VIEW_TEMPLATES_VIEW_AND_MANAGE)
	public Response modifyTagLinks(@Parameter(required = true) @PathParam("viewTemplateId") final Integer viewTemplateId,
								   @RequestBody(required = true,
										   content = @Content(array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds)
			throws SQLException {
		modifyViewTemplateTagLinks(TagModifyInfo.builder()
				.resourceId(viewTemplateId)
				.resultTags(TagsResource.extractTagIds(tagIds)).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Validate view template tags and update the links
	 *
	 * @param tagModifyInfo info about added/deleted/modified tags
	 */
	private void modifyViewTemplateTagLinks(final TagModifyInfo tagModifyInfo) throws SQLException {
		final ViewTemplate viewTemplate = getViewTemplateFromDb(tagModifyInfo.getResourceId(), null);

		if (modifyTagLinks(tagModifyInfo) > 0 && !viewTemplate.isSystem()) {
			viewTemplate.setUpdatedById(getUpdaterId());
			viewTemplate.save(getConnection());
		}
	}

	/**
	 * Get a list of view templates from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param extraFilter Optional Extra filters including parameters that are not in default filters.
	 * @param applyResourceFiltering Optional {@code true} if resource filter should not be ignored for user having limited resources {@code false} otherwise.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<ViewTemplate> getViewTemplatesFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id, final String extraFilter,
																		  final boolean applyResourceFiltering, final FilteringParameters filteringParameters,
																		  final boolean getCount, final boolean includeObjects) throws SQLException {
		return getViewTemplatesFromDb(conn, user, id, extraFilter, applyResourceFiltering, null, null, null, filteringParameters, getCount, includeObjects, false);
	}

	/**
	 * Get a list of view templates from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param extraFilter Optional Extra filters including parameters that are not in default filters.
	 * @param applyResourceFiltering Optional {@code true} if resource filter should not be ignored for user having limited resources {@code false} otherwise.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param streamObjects Whether to get the objects as stream.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<ViewTemplate> getViewTemplatesFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id, final String extraFilter,
																		  final boolean applyResourceFiltering, final PaginationParameters paginationParameters,
																		  final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																		  final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects,
																		  final boolean streamObjects)
			throws SQLException {
		if (user == null) {
			throw new ForbiddenException();
		}

		final StringBuilder filter = new StringBuilder("deleted IS NULL");
		StringUtils.concatenateFilters(filter, "(customerid = ? OR system)");
		if (!StringUtils.isEmpty(extraFilter)) {
			StringUtils.concatenateFilters(filter, extraFilter);
		}

		final List<Object> params = new ArrayList<>();
		params.add(user.getCustomerId());

		if (applyResourceFiltering && user.hasLimitResources(ResourceType.VIEWTEMPLATE)) {
			((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.VIEWTEMPLATE);
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setTableOverride("getViewTemplates(?, ?::INTEGER, ?::INTEGER)");
		config.setTableParams(user.getCustomerId(), user.isSubUser() ? null : user.getMainUserId(), user.isSubUser() ? user.getSubUserId() : null);

		return ViewTemplate.getObjects(ViewTemplate.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, id != null ? id.toString() : null, getCount, includeObjects, streamObjects, filter.toString(), params);
	}

	/**
	 * Get the {@link ViewTemplate} corresponding to `id` from DB.
	 *
	 * @param id The ID of the {@link ViewTemplate} to be able to get a particular one.
	 * @param fieldSelectionParameters What fields to return.
	 * @return viewTemplate, or null if not found.
	 */
	private ViewTemplate getViewTemplateFromDb(final Integer id, final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ViewTemplate> viewTemplates =
				getViewTemplatesFromDb(getConnection(), getUser(), id, null, true, null, null, fieldSelectionParameters, null, false, true, false).getObjects();
		if (viewTemplates.isEmpty()) {
			throw new NotFoundException();
		}
		return viewTemplates.get(0);
	}

}
