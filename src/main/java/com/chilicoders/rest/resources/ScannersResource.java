package com.chilicoders.rest.resources;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ScannersResource.PATH)
@Path(ScannersResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ScannersResource extends BaseResource {
	public static final String PATH = "scanners";

	/**
	 * Get header with total count of {@link ScannerImpl}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getScannerHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getScannersFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of scanners.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return List of scanners.
	 */
	@GET
	@Operation(summary = "Get a list of scanners",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ScannerImpl.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getScannerList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<ScannerImpl>>() {
		}, getScannersFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a scanner.
	 *
	 * @param scannerId The ID of the scanner to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The scanner.
	 */
	@GET
	@Path("{scannerId: \\d+}")
	@Operation(summary = "Get a scanner")
	public ScannerImpl getScanner(@Parameter(required = true) @PathParam("scannerId") final Integer scannerId,
								  @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException, IllegalAccessException, IOException {
		final List<ScannerImpl> scanners = getScannersFromDb(getConnection(), getUser(), scannerId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (scanners.isEmpty()) {
			throw new NotFoundException();
		}
		return scanners.get(0);
	}

	/**
	 * Get a list of scanners from DB.
	 *
	 * @param connection The database connection.
	 * @param user The user to get the {@link ScannerImpl}s for.
	 * @param id Optional ID of the scanner to get.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List of scanners, with count if applicable.
	 */
	protected static Pair<Long, List<ScannerImpl>> getScannersFromDb(final Connection connection, final BaseLoggedOnUser user, final Integer id,
																	 final PaginationParameters paginationParameters,
																	 final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																	 final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects)
			throws SQLException {
		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.MAINUSER);
		config.setIgnoreUserIdField(true);

		final StringBuilder sql = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(sql, "deleted = 0 AND (xuserxid = ? OR global)");
		params.add(user.getMainUserId());

		if (!user.hasAllScannerAccess()) {
			StringUtils.concatenateFilters(sql,
					"(xid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?) OR groupxid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?) OR mode = 0)");
			params.add(user.getSubUserId());
			params.add(user.getSubUserId());
		}

		return ScannerImpl.getObjects(ScannerImpl.class, connection, user, config, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, id != null ? id.toString() : null, getCount, includeObjects, sql.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
