package com.chilicoders.rest.resources;

import static com.chilicoders.core.configuration.api.DataStoreEntry.DataStoreEntryKeys;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditInterface;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.api.DataStoreService;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.rest.annotations.AllowedOnPlatforms;
import com.chilicoders.rest.annotations.RequiredRoles;
import com.chilicoders.service.ServiceProvider;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ReleaseNotesResource.PATH)
@Path(ReleaseNotesResource.PATH)
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.TEXT_PLAIN)
@AllowedOnPlatforms(hiab = false)
@RequiredRoles(roles = {Role.Administrator, Role.ReleaseManager})
public class ReleaseNotesResource extends BaseResource {
	public static final String PATH = "release-notes";
	public static final String TRIGGER_NOTIFICATION = "/trigger-notification";
	public static final String NOTIFICATION_TRIGGER = "/notification-trigger";

	/**
	 * Get the latest timestamp when release notes notification was triggered
	 *
	 * @return the timestamp string stored in DB
	 * @deprecated Replaced by equivalent endpoint under {@link AnnouncementsResource}.
	 */
	@GET
	@Path(NOTIFICATION_TRIGGER)
	@Operation(summary = "Get last release notes notification trigger timestamp (Deprecated: 2025-06-24)", deprecated = true,
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(schema = @Schema(implementation = String.class))))
	@Deprecated
	public String getLastTriggerTimestamp() throws SQLException {
		final DataStoreEntry entry = ServiceProvider.getDataStoreService(getConnection()).getEntry(DataStoreEntryKeys.LAST_RELEASE_NOTES_TRIGGER.name());
		return entry == null ? null : entry.getValue();
	}

	/**
	 * Trigger the release notes notification. Update the latest timestamp in DataStore.
	 *
	 * @return the newly updated timestamp value.
	 * @deprecated Replaced by equivalent endpoint under {@link AnnouncementsResource}.
	 */
	@POST
	@Path(TRIGGER_NOTIFICATION)
	@Operation(summary = "Trigger release notes notification and set trigger timestamp (Deprecated: 2025-06-24)", deprecated = true,
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(schema = @Schema(implementation = String.class))))
	@Deprecated
	public String triggerReleaseNotesNotification() throws SQLException {
		final Connection conn = getConnection();
		XMLAPI.sendNewReleaseNoteEvent(conn);

		final String currentTimeUTC = Instant.now().toString();
		final DataStoreService dsService = ServiceProvider.getDataStoreService(conn);
		dsService.setEntry(DataStoreEntryKeys.LAST_RELEASE_NOTES_TRIGGER.name(), currentTimeUTC);

		final BaseLoggedOnUser user = getUser();
		final AuditInterface audit =
				ServiceProvider.getAuditingService(conn).createAudit(user, user.getMainUserId(), AppName.RELEASENOTES, AuditMode.UPDATE, null, "_NEW_RELEASENOTES", false);
		ServiceProvider.getAuditingService(conn).audit(audit);
		conn.commit();

		return currentTimeUTC;
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
