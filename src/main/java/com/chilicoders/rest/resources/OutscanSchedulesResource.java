package com.chilicoders.rest.resources;

import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.validation.Valid;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONException;

import com.chilicoders.api.scheduling.StartScanRequest;
import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.bl.SavedscanprefBusiness;
import com.chilicoders.bl.ScheduleObjectBusiness;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.scheduling.api.ScanSchedulingService;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.ScanPolicy;
import com.chilicoders.db.objects.ScheduleObject;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.Template;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.StringUtils;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "outscan-schedules")
@Path(OutscanSchedulesResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class OutscanSchedulesResource extends BaseResource {
	public static final String PATH = "outscan/schedules";

	/**
	 * Get header with total count of {@link ScheduleObject}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getOutscanScheduleHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getSchedulesFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	@GET
	@Operation(summary = "Get a list of schedules",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ScheduleObject.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getOutscanScheduleList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<ScheduleObject>>() {
		}, getSchedulesFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true, false));
	}

	/**
	 * Get a schedule by ID.
	 *
	 * @param scheduleId The ID of the schedule to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The schedule.
	 */
	@GET
	@Path("{scheduleId: \\d+}")
	@Operation(summary = "Get a schedule")
	public ScheduleObject getOutscanSchedule(@Parameter(required = true) @PathParam("scheduleId") final long scheduleId,
											 @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ScheduleObject> list =
				getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		return list.get(0);
	}

	/**
	 * Create a schedule.
	 *
	 * @param schedule The schedule to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a schedule")
	@Valid
	public Response postOutscanSchedule(@Parameter(required = true) @Valid final ScheduleObject schedule,
										@BeanParam final FieldSelectionParameters fieldSelectionParameters, @BeanParam final ReturnResultParameters returnResultParameters)
			throws SQLException, ParamValidationException, URISyntaxException, JSONException, IOException {
		if (getUser().isMigration()) {
			throw new ForbiddenException("_MIGRATION_NOT_ALLOWED_CHANGES");
		}

		schedule.setUserId(getUser().getMainUserId());
		schedule.setUpdator(getUser().isSubUser() ? -getUser().getSubUserId() : getUser().getMainUserId());

		final long scheduleId = saveSchedule(schedule, null);
		getConnection().commit();

		final ResponseBuilder response = createCreatedResponse(scheduleId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getRight().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a schedule.
	 *
	 * @param scheduleId The ID of the schedule to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{scheduleId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a schedule")
	public Response patchOutscanSchedule(
			@Parameter(required = true) @PathParam("scheduleId") final long scheduleId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = ScheduleObject.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException, JSONException, ParamValidationException, IOException {
		if (getUser().isMigration()) {
			throw new ForbiddenException("_MIGRATION_NOT_ALLOWED_CHANGES");
		}

		final List<ScheduleObject> list = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}

		final Set<String> missingColumns = new HashSet<>();
		final ScheduleObject newSchedule = ScheduleObject.patch(list.get(0), patch, missingColumns);
		newSchedule.setId(scheduleId);
		this.saveSchedule(newSchedule, missingColumns);
		getConnection().commit();

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getRight().get(0))
					.build();
		}

		return null;
	}

	/**
	 * Validate and save a schedule.
	 *
	 * @param schedule The schedule to save.
	 * @param missingColumns Optional set to add columns not in the patch to.
	 * @return The ID of the saved schedule.
	 */
	private long saveSchedule(final ScheduleObject schedule, final Set<String> missingColumns) throws SQLException, JSONException, ParamValidationException, IOException {
		final SavedscanprefBusiness sb = new SavedscanprefBusiness(getUser());
		final ScanPolicy policy = sb.getScanPolicy(getConnection(), schedule.getTemplateId(), 0, getUser().getMainUserId(), true, false);
		if (policy == null) {
			throw new InputValidationException(ErrorCode.InputValidationFailed);
		}

		if (Configuration.isHiabEnabled()) {
			if (!Configuration.isRegistered()) {
				throw new ForbiddenException("_NO_HIAB_LICENSE", ErrorCode.AccessDenied);
			}
		}
		else {
			if (!getUser().hasOutscanLicense()) {
				throw new ForbiddenException("_NO_OUTSCAN_LICENSE", ErrorCode.AccessDenied);
			}
		}

		final ScheduleObjectBusiness sob = new ScheduleObjectBusiness(getUser());
		return sob.updateRecord(null, null, null, getConnection(), schedule, false, true, getUser(), null, missingColumns == null ? new HashSet<String>() : missingColumns,
				false, true);
	}

	/**
	 * Delete a schedule.
	 *
	 * @param scheduleId The ID of the schedule to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{scheduleId: \\d+}")
	@Operation(summary = "Delete a schedule")
	public Response deleteOutscanSchedule(@Parameter(required = true) @PathParam("scheduleId") final long scheduleId) throws SQLException {
		final List<ScheduleObject> list = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		final ScheduleObject schedule = list.get(0);

		DbObject.executeUpdate(getConnection(), "UPDATE schedules SET deleted = TRUE WHERE id=? AND userid=?", schedule.getId(), getUser().getMainUserId());
		audit(scheduleId, AppName.SCHEDULEOBJECT, AuditMode.DELETE, schedule.getName(), null, null, null);
		getConnection().commit();

		return null;
	}

	/**
	 * Start scanning the schedule now.
	 *
	 * @param scheduleId The schedule to start scanning.
	 * @return The response.
	 */
	@POST
	@Path("{scheduleId: \\d+}/scan")
	@Produces(MediaType.TEXT_PLAIN)
	@Operation(summary = "Start scanning now", responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(schema = @Schema(type = "integer"))))
	public Response scanOutscanSchedule(@Parameter(required = true) @PathParam("scheduleId") final long scheduleId)
			throws SQLException, InputValidationException, JsonParseException, JsonMappingException, IOException {
		if (getUser().isMigration()) {
			throw new ForbiddenException("_MIGRATION_NOT_ALLOWED_CHANGES");
		}

		final List<ScheduleObject> list = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		final ScheduleObject schedule = list.get(0);

		final StartScanRequest startScan = StartScanRequest.builder()
				.scheduleId(schedule.getId())
				.userId(getUser().getMainUserId())
				.subuserId(getUser().getSubUserId())
				.scanNow(true)
				.build();
		final ScanSchedulingService scanStartService = ServiceProvider.getScanSchedulingService(getConnection());
		final Long scanJobId = scanStartService.startScans(startScan);
		getConnection().commit();
		if (scanJobId == null) {
			throw new NotFoundException();
		}

		return Response.status(Status.OK).type(MediaType.TEXT_PLAIN).language(getLanguage()).entity(scanJobId.toString()).build();
	}

	/**
	 * Get a list of schedules from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param readOnly Whether we're the user needs write permission or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<ScheduleObject>> getSchedulesFromDb(final Connection conn, final BaseLoggedOnUser user, final Long id,
																		 final PaginationParameters paginationParameters,
																		 final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																		 final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects,
																		 final boolean readOnly)
			throws SQLException {
		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(where, "userid = ? AND template NOT IN (?, ?, ?)");
		params.add(user.getMainUserId());
		params.add(Template.Was.getId());
		params.add(Template.AppsecScale.getId());
		params.add(Template.Pci.getId());

		StringUtils.concatenateFilters(where, "NOT deleted");

		if (!user.hasAllTargetsAccess() || !user.hasAllScannerAccess() || !user.allowScheduleManagement(true)) {
			StringUtils.concatenateFilters(where, "subuserid IN (SELECT xid FROM tsubusers WHERE xpathup ~ (',' || ? || ','))");
			params.add(user.getSubUserId());
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setCustomSorting(new String[][] {{"LASTSCANDATE", "finalscandate"}, {"TEMPLATEID", "templatename"}});
		config.setUseTrueBooleans(false);

		return ScheduleObject.getObjects(ScheduleObject.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, where.toString(),
				params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
