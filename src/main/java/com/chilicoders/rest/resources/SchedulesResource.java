package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import com.chilicoders.api.EventApiInterfaceV2;
import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.XmlAble.ObjectsResponse;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.rest.events.ScheduleEvent;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.EventSubscription;
import com.chilicoders.rest.models.Schedule;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = SchedulesResource.PATH)
@Path(SchedulesResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class SchedulesResource extends BaseResource {
	public static final String PATH = "schedules";

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	/**
	 * Get header with total count of {@link Schedule}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCHEDULES_VIEW, UserRolePermission.SCHEDULES_VIEW_AND_MANAGE})
	public Response getScheduleHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getSchedulesFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false, false).getCount();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link Schedule}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link Schedule}s.
	 */
	@GET
	@Operation(summary = "Get a list of schedules",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = Schedule.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCHEDULES_VIEW, UserRolePermission.SCHEDULES_VIEW_AND_MANAGE})
	public Response getScheduleList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(getSchedulesFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, true, true, true));
	}

	/**
	 * Get a {@link Schedule} by ID.
	 *
	 * @param scheduleId The ID of the {@link Schedule} to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The {@link Schedule}.
	 */
	@GET
	@Path("{scheduleId: \\d+}")
	@Operation(summary = "Get a schedule")
	@RequiredPermissions(permissions = {UserRolePermission.SCHEDULES_VIEW, UserRolePermission.SCHEDULES_VIEW_AND_MANAGE})
	public Schedule getSchedule(@Parameter(required = true) @PathParam("scheduleId") final Integer scheduleId,
								@BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<Schedule> schedules = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getObjects();
		if (schedules.isEmpty()) {
			throw new NotFoundException();
		}
		return schedules.get(0);
	}

	/**
	 * Create a {@link Schedule}.
	 *
	 * @param schedule The {@linkplain Schedule} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a schedule")
	@Valid
	@RequiredPermissions(permissions = UserRolePermission.SCHEDULES_VIEW_AND_MANAGE)
	public Response postSchedule(@Parameter(required = true) @Valid @NotNull final Schedule schedule, @BeanParam final FieldSelectionParameters fieldSelectionParameters,
								 @BeanParam final ReturnResultParameters returnResultParameters)
			throws URISyntaxException, SQLException, JAXBException {
		schedule.setNextOccurrence(schedule.calculateNextOccurrence(true));
		final Integer scheduleId = saveSchedule(schedule);
		getConnection().commit();

		final List<Event> eventList = new ArrayList<>();
		final ScheduleEvent scheduleEvent = new ScheduleEvent(scheduleId, schedule.getCustomerId(), null, null);
		eventList.add(scheduleEvent.created());
		addScheduledEvent(getConnection(), getUser(), schedule, eventList);
		final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(getConnection());
		for (final Event event : eventList) {
			eventApi.handleEvent(event, getUser());
		}

		final ResponseBuilder response = createCreatedResponse(scheduleId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getObjects().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link Schedule}.
	 *
	 * @param scheduleId The ID of the {@link Schedule} to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{scheduleId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a schedule")
	@RequiredPermissions(permissions = UserRolePermission.SCHEDULES_VIEW_AND_MANAGE)
	public Response patchSchedule(@Parameter(required = true) @PathParam("scheduleId") final Integer scheduleId,
								  @RequestBody(required = true, content = @Content(schema = @Schema(implementation = Schedule.class))) final String patch,
								  @BeanParam final FieldSelectionParameters fieldSelectionParameters,
								  @BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<Schedule> scheduleList = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, false).getObjects();
		if (scheduleList.isEmpty()) {
			throw new NotFoundException();
		}
		final Schedule schedule = scheduleList.get(0);
		final Schedule newSchedule = Schedule.patch(schedule, patch);
		newSchedule.setId(scheduleId);
		newSchedule.setCreatedById(scheduleList.get(0).getCreatedById());
		newSchedule.setNextOccurrence(newSchedule.calculateNextOccurrence(true));
		saveSchedule(newSchedule);
		getConnection().commit();

		final List<Event> eventList = new ArrayList<>();
		final ScheduleEvent scheduleEvent = new ScheduleEvent(scheduleId, newSchedule.getCustomerId(), null, null);
		eventList.add(scheduleEvent.modified());
		if (schedule.getNextOccurrence() != null && newSchedule.getNextOccurrence() != null && !newSchedule.getNextOccurrence().isEqual(schedule.getNextOccurrence())) {
			addScheduledEvent(getConnection(), getUser(), newSchedule, eventList);
		}
		final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(getConnection());
		for (final Event event : eventList) {
			eventApi.handleEvent(event, getUser());
		}

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, fieldSelectionParameters, null, false, true, false).getObjects().get(0)).build();
		}

		return null;
	}

	/**
	 * Delete a {@link Schedule}.
	 *
	 * @param scheduleId The ID of the {@link Schedule} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{scheduleId: \\d+}")
	@Operation(summary = "Delete a schedule")
	@RequiredPermissions(permissions = UserRolePermission.SCHEDULES_VIEW_AND_MANAGE)
	public Response deleteSchedule(@Parameter(required = true) @PathParam("scheduleId") final int scheduleId) throws SQLException {
		final List<Schedule> scheduleList = getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, false).getObjects();
		if (scheduleList.isEmpty()) {
			throw new NotFoundException();
		}

		final Schedule schedule = scheduleList.get(0);
		schedule.setDeleted(getUpdaterId());
		saveSchedule(schedule);
		getConnection().commit();

		final ScheduleEvent scheduleEvent = new ScheduleEvent(scheduleId, schedule.getCustomerId(), null, null);
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(scheduleEvent.deleted(), getUser());

		return null;
	}

	/**
	 * Get a list of {@link Schedule}s from DB.
	 *
	 * @param connection The database connection.
	 * @param user The user to get the {@link Schedule}s for.
	 * @param id The ID of the schedule if wanting a specific one.
	 * @param paginationParameters The pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters The filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param streamObjects Whether to get the objects as stream.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<Schedule> getSchedulesFromDb(final Connection connection, final BaseLoggedOnUser user, final Integer id,
																   final PaginationParameters paginationParameters, final SortingParameters sortingParameters,
																   final FieldSelectionParameters fieldSelectionParameters, final FilteringParameters filteringParameters,
																   final boolean getCount, final boolean includeObjects, final boolean streamObjects) throws SQLException {
		final List<Object> params = new ArrayList<>();
		final StringBuilder filter = new StringBuilder("deleted IS NULL");

		StringUtils.concatenateFilters(filter, "customerid = ?");
		params.add(user.getCustomerId());

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setCustomSorting(new String[][] {
				{
						"frequency",
						"(CASE frequency WHEN 'NONE' THEN 0 WHEN 'ONCE' THEN 1 WHEN 'HOURLY' THEN 2 WHEN 'DAILY' THEN 3 WHEN 'WEEKLY' THEN 4 WHEN 'MONTHLY' THEN 5 WHEN 'YEARLY' THEN 6 END)"
				}
		});

		if (fieldSelectionParameters != null && !StringUtils.isEmpty(fieldSelectionParameters.getRequestFields())) {
			fieldSelectionParameters.setRequestFields(fieldSelectionParameters.getRequestFields() + ",timezone");
		}

		return Schedule.getObjects(Schedule.class, connection, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, streamObjects, filter.toString(), params);
	}

	/**
	 * Save a schedule.
	 *
	 * @param schedule The schedule to save.
	 * @return Id of created object
	 */
	private Integer saveSchedule(final Schedule schedule) throws SQLException {
		schedule.setCustomerId(getUser().getCustomerId());
		if (schedule.getId() != null && schedule.getId() > 0) {
			schedule.setUpdatedById(getUpdaterId());
		}
		else {
			schedule.setCreatedById(getUpdaterId());
		}
		return (int) schedule.save(getConnection());
	}

	/**
	 * Add scheduled event when relevant.
	 *
	 * @param conn Database connection.
	 * @param user User.
	 * @param schedule Schedule.
	 * @param eventList Event list.
	 */
	public static void addScheduledEvent(final Connection conn, final BaseLoggedOnUser user, final Schedule schedule, final List<Event> eventList) {
		try {
			final String where = "deleted IS NULL AND customerid = ? AND trigger = ?";
			final List<Object> params = new ArrayList<>();
			params.add(schedule.getCustomerId());
			params.add(Trigger.SCHEDULE_SCHEDULED);

			final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
			final List<EventSubscription> eventSubscriptions =
					EventSubscription.getObjects(EventSubscription.class, conn, user, config, null, null, null, null, null, false, true,
							where, params).getRight();
			for (final EventSubscription eventSubscription : eventSubscriptions) {
				if (eventSubscription.getLastTriggered() != null && eventSubscription.getSettings() != null && eventSubscription.getSettings().getTimeBefore() != null &&
						schedule.getNextOccurrence() != null && schedule.getNextOccurrence()
						.toInstant()
						.isBefore(eventSubscription.getLastTriggered().plus(eventSubscription.getSettings().getTimeBefore(), ChronoUnit.DAYS))) {
					final ScheduleEvent scheduleEvent = new ScheduleEvent(schedule.getId(), schedule.getCustomerId(), null, eventSubscription.getId());
					eventList.add(scheduleEvent.scheduled(eventSubscription.getSettings() == null ? null : eventSubscription.getSettings().getTimeBefore()));
				}
			}
		}
		catch (final SQLException | JAXBException e) {
			// Ignore
		}
	}

}
