package com.chilicoders.rest.resources;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.SubscriptionAudit;
import com.chilicoders.rest.annotations.AllowedOnPlatforms;
import com.chilicoders.rest.annotations.RequiredRoles;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.SortingParameters;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = SubscriptionAuditsResource.PATH)
@Path(SubscriptionAuditsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@AllowedOnPlatforms(hiab = false)
@RequiredRoles(roles = {Role.Administrator})
public class SubscriptionAuditsResource extends BaseResource {
	public static final String PATH = "subscription-audits";

	/**
	 * Get header with total count of {@link SubscriptionAudit}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getSubscriptionAuditHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getSubscriptionAuditsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	@GET
	@Operation(summary = "Get a list of subscription audits",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = SubscriptionAudit.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getSubscriptionAuditList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<SubscriptionAudit>>() {
		}, getSubscriptionAuditsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a subscription audit by ID.
	 *
	 * @param auditId The ID of the subscription audit to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The subscription audit.
	 */
	@GET
	@Path("{auditId: \\d+}")
	@Operation(summary = "Get a subscription audit")
	public SubscriptionAudit getSubscriptionAudit(@Parameter(required = true) @PathParam("auditId") final long auditId,
												  @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<SubscriptionAudit> subscriptionAudits =
				getSubscriptionAuditsFromDb(getConnection(), getUser(), auditId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (subscriptionAudits.isEmpty()) {
			throw new NotFoundException();
		}
		return subscriptionAudits.get(0);
	}

	/**
	 * Get a list of subscription audits from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<SubscriptionAudit>> getSubscriptionAuditsFromDb(final Connection conn, final BaseLoggedOnUser user, final Long id,
																					 final PaginationParameters paginationParameters,
																					 final SortingParameters sortingParameters,
																					 final FieldSelectionParameters fieldSelectionParameters,
																					 final FilteringParameters filteringParameters, final boolean getCount,
																					 final boolean includeObjects) throws SQLException {
		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setIgnoreUserIdField(true);

		return SubscriptionAudit.getObjects(SubscriptionAudit.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, id != null ? id.toString() : null, getCount, includeObjects,
				where.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
