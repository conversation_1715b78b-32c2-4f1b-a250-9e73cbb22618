package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.model.AccountType;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.Account;
import com.chilicoders.rest.models.Credential;
import com.chilicoders.rest.models.Integration;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.Tag;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

@io.swagger.v3.oas.annotations.tags.Tag(name = AccountsResource.PATH)
@Path(AccountsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AccountsResource extends TaggableResource {
	public static final String PATH = "accounts";

	private static final Pair<String, String> TAG_LINK_TABLE = ImmutablePair.of("tag_account", "accountid");

	/**
	 * Get header with total count of {@link Account}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.ACCOUNTS_VIEW, UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE})
	public Response getAccountHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getAccountsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link Account}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link Account}s.
	 */
	@GET
	@Operation(summary = "Get a list of accounts",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = Account.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.ACCOUNTS_VIEW, UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE})
	public Response getAccountList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<Account>>() {
		}, getAccountsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true, true,
				false));
	}

	/**
	 * Get a {@link Account}.
	 *
	 * @param accountId The ID of the {@link Account} to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The {@link Account}.
	 */
	@GET
	@Path("{accountId: \\d+}")
	@Operation(summary = "Get a account")
	@RequiredPermissions(permissions = {UserRolePermission.ACCOUNTS_VIEW, UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE})
	public Account getAccount(@Parameter(required = true) @PathParam("accountId") final Integer accountId, @BeanParam final FieldSelectionParameters fieldSelectionParameters)
			throws SQLException, IllegalAccessException {
		final List<Account> accounts =
				getAccountsFromDb(getConnection(), getUser(), accountId, null, null, fieldSelectionParameters, null, false, true, true, false).getRight();
		if (accounts.isEmpty()) {
			throw new NotFoundException();
		}
		return accounts.get(0);
	}

	/**
	 * Create a {@link Account}.
	 *
	 * @param account The {@link Account} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a account")
	@Valid
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response postAccount(
			@Parameter(required = true) @Valid @NotNull final Account account,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws URISyntaxException, SQLException, JAXBException {
		if (getUser().hasLimitResources(ResourceType.ACCOUNT)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		account.setCustomerId(getUser().getCustomerId());
		account.setCreatedById(getUpdaterId());

		if (account.getIntegrationId() != null) {
			final List<Integration> integrations =
					IntegrationsResource.getIntegrationsFromDb(getConnection(), getUser(), account.getIntegrationId(), null, null, null, null, false, true).getRight();
			if (integrations.isEmpty()) {
				throw new InputValidationException("_INTEGRATION_NOT_FOUND");
			}
			final Integration integration = integrations.get(0);
			if (integration.getType() != IntegrationType.CYBERARK && integration.getType() != IntegrationType.DELINEA) {
				throw new InputValidationException("_NOT_ALLOWED_LINK_INTEGRATION_CREDENTIALS");
			}
		}

		final Integer accountId = (int) account.save(getConnection());
		getConnection().commit();

		final ResponseBuilder response = createCreatedResponse(accountId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getAccountsFromDb(getConnection(), getUser(), accountId, null, null, fieldSelectionParameters, null, false, true, true, false).getRight().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link Account}.
	 *
	 * @param accountId The ID of the {@link Account} to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{accountId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a account")
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response patchAccount(
			@Parameter(required = true) @PathParam("accountId") final Integer accountId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = Account.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<Account> accounts = getAccountsFromDb(getConnection(), getUser(), accountId, null, null, null, null, false, true, false, false).getRight();
		if (accounts.isEmpty()) {
			throw new NotFoundException();
		}

		final Account newAccount = Account.patch(accounts.get(0), patch);
		newAccount.setId(accountId);
		newAccount.setCustomerId(getUser().getCustomerId());
		newAccount.setCreatedById(accounts.get(0).getCreatedById());
		newAccount.setUpdatedById(getUpdaterId());

		if (newAccount.getIntegrationId() != null) {
			final List<Integration> integrations =
					IntegrationsResource.getIntegrationsFromDb(getConnection(), getUser(), newAccount.getIntegrationId(), null, null, null, null, false, true).getRight();
			if (integrations.isEmpty()) {
				throw new InputValidationException("_INTEGRATION_NOT_FOUND");
			}
			final Integration integration = integrations.get(0);
			if (integration.getType() != IntegrationType.CYBERARK && integration.getType() != IntegrationType.DELINEA) {
				throw new InputValidationException("_NOT_ALLOWED_LINK_INTEGRATION_CREDENTIALS");
			}
		}

		newAccount.save(getConnection());
		getConnection().commit();

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(
							getAccountsFromDb(getConnection(), getUser(), accountId, null, null, fieldSelectionParameters, null, false, true, true, false).getRight().get(0))
					.build();
		}

		return null;
	}

	/**
	 * Delete a {@link Account}.
	 *
	 * @param accountId The ID of the {@link Account} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{accountId: \\d+}")
	@Operation(summary = "Delete a account")
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response deleteAccount(@Parameter(required = true) @PathParam("accountId") final Integer accountId) throws SQLException {
		final List<Account> accounts = getAccountsFromDb(getConnection(), getUser(), accountId, null, null, null, null, false, true, false, false).getRight();
		if (accounts.isEmpty()) {
			throw new NotFoundException();
		}
		final Account account = accounts.get(0);
		account.setCustomerId(getUser().getCustomerId());
		account.setDeleted(getUpdaterId());
		account.save(getConnection());
		getConnection().commit();

		return null;
	}

	/**
	 * Link a {@link Account} to a {@link Tag}.
	 *
	 * @param accountId The {@link Account} to link.
	 * @param tagId The {@link Tag} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{accountId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Create a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response putAccountTagLink(@Parameter(required = true) @PathParam("accountId") final Integer accountId,
									  @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException, URISyntaxException {
		modifyAccountTagLinks(TagModifyInfo.builder()
				.resourceId(accountId)
				.addedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return createCreatedResponse(accountId + "/tags/" + tagId).build();
	}

	/**
	 * Delete a link between a {@link Account} and a {@link Tag}.
	 *
	 * @param accountId The {@link Account} to unlink.
	 * @param tagId The {@link Tag} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{accountId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response deleteAccountTagLink(@Parameter(required = true) @PathParam("accountId") final Integer accountId,
										 @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException {
		modifyAccountTagLinks(TagModifyInfo.builder()
				.resourceId(accountId)
				.deletedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link Tag} linked to an {@link Account}
	 *
	 * @param accountId The {@link Account} to modify
	 * @param tagIds The new set of {@link Tag}
	 * @return The response
	 */
	@PUT
	@Path("{accountId: \\d+}/tags")
	@Operation(summary = "Modify linked set of tags")
	@RequiredPermissions(permissions = UserRolePermission.ACCOUNTS_VIEW_AND_MANAGE)
	public Response modifyTagLinks(@Parameter(required = true) @PathParam("accountId") final Integer accountId,
								   @RequestBody(required = true,
										   content = @Content(array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds)
			throws SQLException {
		modifyAccountTagLinks(TagModifyInfo.builder()
				.resourceId(accountId)
				.resultTags(TagsResource.extractTagIds(tagIds)).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Validate account tags and update the links
	 *
	 * @param tagModifyInfo info about added/deleted/modified tags
	 */
	private void modifyAccountTagLinks(final TagModifyInfo tagModifyInfo) throws SQLException {
		final List<Account> accounts =
				getAccountsFromDb(getConnection(), getUser(), tagModifyInfo.getResourceId(), null, null, null, null, false, true, false, false).getRight();
		if (accounts.isEmpty()) {
			throw new NotFoundException();
		}

		if (modifyTagLinks(tagModifyInfo) > 0) {
			accounts.get(0).setUpdatedById(getUpdaterId());
			accounts.get(0).save(getConnection());
		}
	}

	/**
	 * Get a list of {@link Account}s from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param getCredentials Whether to load the related credentials or not.
	 * @param ignoreResourceLimitation Ignore user resource limitations when getting accounts
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<Account>> getAccountsFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id,
																 final PaginationParameters paginationParameters,
																 final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																 final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects,
																 final boolean getCredentials, final boolean ignoreResourceLimitation)
			throws SQLException {
		final StringBuilder filter = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(filter, "deleted IS NULL");
		StringUtils.concatenateFilters(filter, "customerid = ?");
		params.add(user.getCustomerId());

		if (user.hasLimitResources(ResourceType.ACCOUNT) && !ignoreResourceLimitation) {
			((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.ACCOUNT);
		}

		if (user.getConsultancyMode()) {
			StringUtils.concatenateFilters(filter, "type = ANY(?::accounttype[])");
			params.add(new AccountType[] {AccountType.WEB, AccountType.BASIC});
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);

		if (fieldSelectionParameters != null) {
			fieldSelectionParameters.setRequestFields(StringUtils.isEmpty(fieldSelectionParameters.getRequestFields())
					? fieldSelectionParameters.getRequestFields() : (fieldSelectionParameters.getRequestFields() + ",type"));
		}

		final Pair<Long, List<Account>> result =
				Account.getObjects(Account.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
						id != null ? id.toString() : null, getCount, includeObjects, filter.toString(), params);

		if (getCredentials) {
			if (result.getRight() != null) {
				for (final Account account : result.getRight()) {
					final Pair<Long, List<Credential>> credentials =
							CredentialsResource.getCredentialsFromDb(conn, user, null, account.getId(), null, null, null, null, false, true);
					if (credentials.getRight() != null && !credentials.getRight().isEmpty()) {
						account.setCredentials(credentials.getRight().toArray(new Credential[0]));
					}
				}
			}
		}

		return result;
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	@Override
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Pair<String, String> getTagLinkTable() {
		return TAG_LINK_TABLE;
	}

	/**
	 * Helper method to validate account IDs
	 *
	 * @param conn DB connection
	 * @param user logged on user
	 * @param ignoreResourceLimitation Ignore limitations on viewing accounts for user
	 * @return The validated account id array
	 * @throws NotFoundException if the requested account ids are not valid
	 */
	public static List<Integer> getValidAccountIds(final Connection conn, final BaseLoggedOnUser user, final boolean ignoreResourceLimitation) throws SQLException {
		final List<Object> params = new ArrayList<>();
		final StringBuilder filter = new StringBuilder("deleted IS NULL");

		if (user != null) {
			StringUtils.concatenateFilters(filter, "customerid = ?");
			params.add(user.getCustomerId());
			if (user.hasLimitResources(ResourceType.ACCOUNT) && !ignoreResourceLimitation) {
				((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.ACCOUNT);
			}
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);

		final List<Integer> accounts = Account.getObjects(Account.class, conn, user, config, null, null, null, null, null, false, true, filter.toString(), params)
				.getRight()
				.stream()
				.map(x -> x.getId())
				.collect(
						Collectors.toList());

		return accounts;
	}

	/**
	 * Helper method to validate account IDs
	 *
	 * @param conn DB connection
	 * @param user logged on user
	 * @return The validated account id array
	 * @throws NotFoundException if the requested account ids are not valid
	 */
	public static List<Integer> getValidAccountIds(final Connection conn, final BaseLoggedOnUser user) throws SQLException {
		return getValidAccountIds(conn, user, false);
	}

}
