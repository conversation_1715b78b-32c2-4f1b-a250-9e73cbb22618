package com.chilicoders.rest.resources;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.chilicoders.api.EventApiInterfaceV2;
import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.bl.ScannerBusiness;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.libellum.ServiceIdentity.Service;
import com.chilicoders.core.scanconfiguration.model.AgentScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudsecScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerDiscoveryScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.ScaleScanConfigurationTemplate;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.core.user.api.model.Feature;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.ScanStatus;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.db.objects.XmlAble.ObjectsResponse;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.rest.events.ScanConfigurationEvent;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.ScanConfigurationInterface.AgentScanConfigurationProperties;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.AllowedOnPlatforms;
import com.chilicoders.rest.annotations.ConcurrencyRestricted;
import com.chilicoders.rest.annotations.RequestAuthentication;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.InternalServerErrorException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.exceptions.UnprocessableContentException;
import com.chilicoders.rest.models.Account;
import com.chilicoders.rest.models.Asset;
import com.chilicoders.rest.models.AssetIdentifier;
import com.chilicoders.rest.models.Credential;
import com.chilicoders.rest.models.CredentialClassType;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.ScanConfiguration;
import com.chilicoders.rest.models.ScanConfiguration.StartScan;
import com.chilicoders.rest.models.Schedule;
import com.chilicoders.rest.models.Tag;
import com.chilicoders.rest.models.TestSetupScript;
import com.chilicoders.rest.models.Workflow;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.rest.util.RestUtils;
import com.chilicoders.scan.LuaCheck;
import com.chilicoders.scan.impl.StartScanRequest;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;

import com.google.common.base.Strings;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

@io.swagger.v3.oas.annotations.tags.Tag(name = ScanConfigurationsResource.PATH)
@Path(ScanConfigurationsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ScanConfigurationsResource extends TaggableResource {
	private static final Logger LOG = LogManager.getLogger(ScanConfigurationsResource.class);

	public static final String PATH = "scan-configurations";

	public static final String TEST_SETUP_SCRIPT_PATH = "test-setup-script";

	private static final Pair<String, String> TAG_LINK_TABLE = ImmutablePair.of("tag_scanconfiguration", "scanconfigurationid");

	@Override
	public Pair<String, String> getTagLinkTable() {
		return TAG_LINK_TABLE;
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	/**
	 * Get header with total count of {@link ScanConfiguration}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCAN_CONFIGURATIONS_VIEW, UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE})
	public Response getScanConfigurationHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getScanConfigurationsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false, false).getCount();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link ScanConfiguration}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link ScanConfiguration}s.
	 */
	@GET
	@Operation(summary = "Get a list of scan configurations",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ScanConfiguration.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCAN_CONFIGURATIONS_VIEW, UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE})
	public Response getScanConfigurationList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, true, true, true));
	}

	/**
	 * Get a {@link ScanConfiguration} by ID.
	 *
	 * @param scanConfigurationId The ID of the {@link ScanConfiguration} to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The {@link ScanConfiguration}.
	 */
	@GET
	@Path("{scanConfigurationId: \\d+}")
	@Operation(summary = "Get a scan configuration")
	@RequiredPermissions(permissions = {UserRolePermission.SCAN_CONFIGURATIONS_VIEW, UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE})
	public ScanConfiguration getScanConfiguration(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
												  @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, fieldSelectionParameters, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		return scanConfigurations.get(0);
	}

	/**
	 * Create a {@link ScanConfiguration}.
	 *
	 * @param scanConfiguration The {@linkplain ScanConfiguration} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a scan configuration")
	@Valid
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response postScanConfiguration(@Parameter(required = true) @Valid @NotNull final ScanConfiguration scanConfiguration,
										  @BeanParam final FieldSelectionParameters fieldSelectionParameters,
										  @BeanParam final ReturnResultParameters returnResultParameters)
			throws URISyntaxException, SQLException, JAXBException {
		if (getUser().hasLimitResources(ResourceType.SCANCONFIGURATION)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		final List<Event> eventList = new ArrayList<>();
		scanConfiguration.setCustomerId(getUser());
		scanConfiguration.setTemplate(scanConfiguration.getConfiguration().getTemplate());

		if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
			final AgentScanConfigurationProperties properties = new AgentScanConfigurationProperties();
			properties.setType(ScanTemplate.AGENT_SCAN);
			properties.setSyncToAgentApi(true);
			scanConfiguration.setProperties(properties);
		}

		if (scanConfiguration.getGroupId() != null) {
			ScanConfigurationGroupsResource.validateGroupAndParentHierarchy(getConnection(), getUser(), scanConfiguration.getGroupId(), null, false);
		}

		final Integer scanConfigurationId = saveConfiguration(scanConfiguration, null);

		if (scanConfiguration.getTemplate() != ScanTemplate.SCALE) {
			updateAssetIdentifierLinks(scanConfigurationId, null, scanConfiguration.getAssetIdentifierIds());
			updateAssetLinks(scanConfigurationId, null, scanConfiguration.getAssetIds());
		}

		getConnection().commit();
		final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfigurationId,
				scanConfiguration.getCustomerId(), null, null, null);
		eventList.add(scanConfigurationEvent.created());
		final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(getConnection());
		for (final Event event : eventList) {
			eventApi.handleEvent(event, getUser());
		}

		final ResponseBuilder response = createCreatedResponse(scanConfigurationId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null,
					fieldSelectionParameters, null, false, true, false).getObjects().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link ScanConfiguration}.
	 *
	 * @param scanConfigurationId The ID of the {@link ScanConfiguration} to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{scanConfigurationId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a scan configuration")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response patchScanConfiguration(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
										   @RequestBody(required = true, content = @Content(schema = @Schema(implementation = ScanConfiguration.class))) final String patch,
										   @BeanParam final FieldSelectionParameters fieldSelectionParameters,
										   @BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanConfiguration oldScanConfiguration = scanConfigurations.get(0);
		final List<Event> eventList = new ArrayList<>();
		updateScanConfigurations(scanConfigurations, patch);
		getConnection().commit();
		final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfigurationId,
				oldScanConfiguration.getCustomerId(), null, null, null);
		eventList.add(scanConfigurationEvent.modified());
		final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(getConnection());
		for (final Event event : eventList) {
			eventApi.handleEvent(event, getUser());
		}
		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null,
					fieldSelectionParameters, null, false, true, false).getObjects().get(0)).build();
		}

		return null;
	}

	/**
	 * Partially update one or several {@link ScanConfiguration}.
	 *
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @param filteringParameters Filtering parameters.
	 * @return The response.
	 */
	@PATCH
	@Valid
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update one or several scan configurations")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response patchScanConfigurations(@RequestBody(required = true, content = @Content(schema = @Schema(implementation = ScanConfiguration.class))) final String patch,
											@BeanParam final FieldSelectionParameters fieldSelectionParameters,
											@BeanParam final PaginationParameters paginationParameters,
											@BeanParam final SortingParameters sortingParameters,
											@BeanParam final ReturnResultParameters returnResultParameters,
											@BeanParam final FilteringParameters filteringParameters) throws SQLException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, null, filteringParameters, false, true,
						false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		updateScanConfigurations(scanConfigurations, patch);
		getConnection().commit();
		final List<Event> eventList = new ArrayList<>();
		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			eventList.add(scanConfigurationEvent.modified());
		}
		final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(getConnection());
		for (final Event event : eventList) {
			eventApi.handleEvent(event, getUser());
		}
		final ArrayList<Integer> ids = new ArrayList<>();
		scanConfigurations.forEach(scanConfiguration -> ids.add(scanConfiguration.getId()));
		if (returnResultParameters.shouldReturnResult()) {
			return responseWithCount(getScanConfigurationsFromDb(getConnection(), getUser(), null, ids.toArray(new Integer[0]), null, null,
					fieldSelectionParameters, null, true, true, true));
		}

		return null;
	}

	/**
	 * Update one or several scan configurations
	 *
	 * @param scanConfigurations Scan configurations to update
	 * @param patch The patch to apply
	 */
	private void updateScanConfigurations(final List<ScanConfiguration> scanConfigurations, final String patch) throws JAXBException, SQLException {
		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final Set<String> missingColumns = new HashSet<>();
			final ScanConfiguration newScanConfiguration = ScanConfiguration.patch(scanConfiguration, patch, missingColumns);
			newScanConfiguration.setId(scanConfiguration.getId());
			newScanConfiguration.setCreatedById(scanConfiguration.getCreatedById());
			newScanConfiguration.setProperties(scanConfiguration.getProperties());
			newScanConfiguration.setTemplate(newScanConfiguration.getConfiguration().getTemplate());

			if (newScanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
				// If agent feature of this customer is disabled, they couldn't enable the agent scan configuration by themselves.
				if (!getUser().hasFeature(Feature.AGENTS) && newScanConfiguration.isEnabled()) {
					throw new ForbiddenException();
				}

				if (scanConfiguration.isEnabled() && !newScanConfiguration.isEnabled()) {
					deleteAgentSchedule(newScanConfiguration, getUser().getCustomerUuid());
				}
				else {
					AgentScanConfigurationProperties properties = (AgentScanConfigurationProperties) newScanConfiguration.getProperties();
					if (properties == null) {
						properties = new AgentScanConfigurationProperties();
						properties.setType(ScanTemplate.AGENT_SCAN);
					}
					properties.setSyncToAgentApi(true);
					newScanConfiguration.setProperties(properties);
				}
			}

			if (newScanConfiguration.getGroupId() != null && !newScanConfiguration.getGroupId().equals(scanConfiguration.getGroupId())) {
				ScanConfigurationGroupsResource.validateGroupAndParentHierarchy(getConnection(), getUser(), newScanConfiguration.getGroupId(), null, false);
			}

			saveConfiguration(newScanConfiguration, scanConfiguration);

			if (newScanConfiguration.getTemplate() != ScanTemplate.SCALE) {
				updateAssetIdentifierLinks(scanConfiguration.getId(), scanConfiguration.getAssetIdentifierIds(),
						newScanConfiguration.getAssetIdentifierIds());
				updateAssetLinks(scanConfiguration.getId(), scanConfiguration.getAssetIds(), newScanConfiguration.getAssetIds());
			}
		}
	}

	/**
	 * Update linked {@link AssetIdentifier}s.
	 *
	 * @param scanConfigurationId Id of {@link ScanConfiguration}
	 * @param currentAssetIdentifierIds Array with current linked {@link AssetIdentifier} ids
	 * @param newAssetIdentifierIds Array with {@link AssetIdentifier} ids to link
	 */
	private void updateAssetIdentifierLinks(final Integer scanConfigurationId, final Integer[] currentAssetIdentifierIds,
											final Integer[] newAssetIdentifierIds) throws SQLException {
		final Integer[] currentAssetIdentifierLinkIds = currentAssetIdentifierIds == null ? new Integer[0] : currentAssetIdentifierIds;
		Arrays.sort(currentAssetIdentifierLinkIds);
		final Integer[] newAssetIdentifierLinkIds = newAssetIdentifierIds == null ? new Integer[0] : newAssetIdentifierIds;
		Arrays.sort(newAssetIdentifierLinkIds);
		if (!Arrays.equals(currentAssetIdentifierLinkIds, newAssetIdentifierLinkIds)) {
			if (newAssetIdentifierLinkIds.length > 0) {
				final long count = AssetIdentifiersResource.getAssetIdentifiersFromDb(getConnection(), getUser(), getServiceIdentity(), newAssetIdentifierLinkIds, null,
						null, null, null, null, true, false, false).getCount();
				if (count < newAssetIdentifierLinkIds.length) {
					throw new NotFoundException();
				}
			}
			if (currentAssetIdentifierLinkIds.length != 0) {
				DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ?", scanConfigurationId);
			}
			for (final Integer assetIdentifierId : newAssetIdentifierLinkIds) {
				DbObject.executeUpdate(getConnection(), "INSERT INTO scanconfiguration_assetidentifier (scanconfigurationid, assetidentifierid) VALUES (?, ?)",
						scanConfigurationId, assetIdentifierId);
			}
		}
	}

	/**
	 * Update linked {@link Asset}s.
	 *
	 * @param scanConfigurationId Id of {@link ScanConfiguration}
	 * @param currentAssetIds Array with current linked {@link Asset} ids
	 * @param newAssetIds Array with {@link Asset} ids to link
	 */
	private void updateAssetLinks(final Integer scanConfigurationId, final Integer[] currentAssetIds, final Integer[] newAssetIds) throws SQLException {
		final Integer[] currentAssetLinkIds = currentAssetIds == null ? new Integer[0] : currentAssetIds;
		Arrays.sort(currentAssetLinkIds);
		final Integer[] newAssetLinkIds = newAssetIds == null ? new Integer[0] : newAssetIds;
		Arrays.sort(newAssetLinkIds);
		if (!Arrays.equals(currentAssetLinkIds, newAssetLinkIds)) {
			if (newAssetLinkIds.length > 0) {
				final long count = AssetsResource.getAssetsFromDb(getConnection(), getUser(), getServiceIdentity(), newAssetLinkIds, null,
						null, null, null, null, true, false, false).getCount();
				if (count != newAssetLinkIds.length) {
					throw new NotFoundException();
				}
			}
			if (currentAssetLinkIds.length != 0) {
				DbObject.executeUpdate(getConnection(), "DELETE FROM asset_scanconfiguration WHERE scanconfigurationid = ?", scanConfigurationId);
			}
			for (final Integer assetId : newAssetLinkIds) {
				DbObject.executeUpdate(getConnection(), "INSERT INTO asset_scanconfiguration (scanconfigurationid, assetid) VALUES (?, ?)", scanConfigurationId, assetId);
			}
		}
	}

	/**
	 * Delete a {@link ScanConfiguration}.
	 *
	 * @param scanConfigurationId The ID of the {@link ScanConfiguration} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{scanConfigurationId: \\d+}")
	@Operation(summary = "Delete a scan configuration")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteScanConfiguration(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId)
			throws SQLException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		final ScanConfiguration scanConfiguration = scanConfigurations.get(0);

		if (scanConfiguration.getWorkflowId() != null) {
			throw new ForbiddenException("_SCAN_CONFIGURATION_IS_USED_BY_WORKFLOW");
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
			deleteAgentSchedule(scanConfiguration, getUser().getCustomerUuid());
		}

		scanConfiguration.setDeleted(getUpdaterId());
		scanConfiguration.save(getConnection());
		getConnection().commit();

		final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfigurationId,
				scanConfiguration.getCustomerId(), null, null, null);
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanConfigurationEvent.deleted(), getUser());

		return null;
	}

	/**
	 * Delete one or several {@link ScanConfiguration}.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param filteringParameters Filtering parameters.
	 * @return The response.
	 */
	@DELETE
	@Valid
	@Operation(summary = "Delete one or several scan configurations")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteScanConfigurations(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, null, filteringParameters, false, true,
						false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			if (scanConfiguration.getWorkflowId() != null) {
				throw new ForbiddenException("_SCAN_CONFIGURATION_IS_USED_BY_WORKFLOW");
			}

			if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
				deleteAgentSchedule(scanConfiguration, getUser().getCustomerUuid());
			}

			scanConfiguration.setDeleted(getUpdaterId());
			scanConfiguration.save(getConnection());
		}
		getConnection().commit();

		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanConfigurationEvent.deleted(), getUser());
		}

		return null;
	}

	/**
	 * Start a scan based on this {@link ScanConfiguration}.
	 *
	 * @param scanConfigurationId The ID of the {@link ScanConfiguration} to start scanning.
	 * @return The response.
	 */
	@POST
	@Path("{scanConfigurationId: \\d+}/scan")
	@Operation(summary = "Start a scan based on this configuration")
	@RequiredPermissions(permissions = UserRolePermission.SCANS_VIEW_AND_MANAGE)
	public Response scanSchedule(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId)
			throws SQLException, JAXBException, MalformedURLException, ExecutionException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		final ScanConfiguration scanConfiguration = scanConfigurations.get(0);

		if (scanConfiguration.getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		if (!scanConfiguration.isEnabled()) {
			throw new ForbiddenException("_CONFIGURATION_DISABLED");
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
			throw new UnprocessableContentException("_CANT_START_AGENT_SCAN");
		}

		validateConfigurationOutsideWorkflow(scanConfiguration, null);

		final List<Event> eventList = new ArrayList<>();
		final StartScanRequest startScanRequest = StartScanRequest.builder().user(getUser()).build();
		final Integer scansStarted = scanConfiguration.startScan(getConnection(), startScanRequest, eventList);
		if (scansStarted > 0) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			eventList.add(scanConfigurationEvent.started());
		}
		getConnection().commit();

		for (final Event event : eventList) {
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(event, getUser());
		}

		final StartScan scan = new StartScan();
		scan.setScansStarted(scansStarted);

		return Response.ok(scan).build();
	}

	/**
	 * Link a {@link ScanConfiguration} to a {@link Schedule}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to link.
	 * @param scheduleId The {@link Schedule} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{scanConfigurationId: \\d+}/schedules/{scheduleId: \\d+}")
	@Operation(summary = "Create a link to a schedule")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response putScanConfigurationScheduleLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
													 @Parameter(required = true) @PathParam("scheduleId") final Integer scheduleId)
			throws SQLException, URISyntaxException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		final ScanConfiguration scanConfiguration = scanConfigurations.get(0);

		if (scanConfiguration.getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
			throw new ForbiddenException();
		}

		final List<Schedule> schedules = SchedulesResource.getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, false).getObjects();
		if (schedules.isEmpty()) {
			throw new NotFoundException();
		}

		validateConfigurationOutsideWorkflow(scanConfiguration, null);

		if (DbObject.executeUpdate(getConnection(),
				"INSERT INTO scanconfiguration_genericschedule (scanconfigurationid, genericscheduleid) VALUES (?, ?) ON CONFLICT DO NOTHING",
				scanConfigurationId, scheduleId) > 0) {
			getConnection().commit();
		}

		return createCreatedResponse(scanConfigurationId + "/schedules/" + scheduleId).build();
	}

	/**
	 * Delete a link between a {@link ScanConfiguration} and a {@link Schedule}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to unlink.
	 * @param scheduleId The {@link Schedule} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{scanConfigurationId: \\d+}/schedules/{scheduleId: \\d+}")
	@Operation(summary = "Delete a link to a schedule")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteScanConfigurationScheduleLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
														@Parameter(required = true) @PathParam("scheduleId") final Integer scheduleId)
			throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		if (scanConfigurations.get(0).getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		final List<Schedule> schedules = SchedulesResource.getSchedulesFromDb(getConnection(), getUser(), scheduleId, null, null, null, null, false, true, false).getObjects();
		if (schedules.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfiguration_genericschedule WHERE scanconfigurationid = ? AND genericscheduleid = ?",
				scanConfigurationId, scheduleId) == 0) {
			throw new NotFoundException();
		}

		getConnection().commit();

		return null;
	}

	/**
	 * Link a {@link ScanConfiguration} to an {@link AssetIdentifier}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to link.
	 * @param assetIdentifierId The {@link AssetIdentifier} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{scanConfigurationId: \\d+}/asset-identifiers/{assetIdentifierId: \\d+}")
	@Operation(summary = "Create a link to an asset identifier")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response putAssetIdentifierLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
										   @Parameter(required = true) @PathParam("assetIdentifierId") final Integer assetIdentifierId)
			throws SQLException, URISyntaxException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		if (scanConfigurations.get(0).getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		if (scanConfigurations.get(0).getTemplate() == ScanTemplate.SCALE) {
			if (isConfigurationLinkedToAssetIdentifier(scanConfigurationId)) {
				throw new InputValidationException("_SCALE_CONFIG_ALREADY_LINKED", ErrorCode.InputValidationFailed);
			}

			if (!((ScaleScanConfigurationTemplate) scanConfigurations.get(0).getConfiguration()).areSeedsOneService(false)) {
				throw new InputValidationException("_SEED_MULTIPLE_HOSTS");
			}
		}

		final List<AssetIdentifier> assetIdentifiers =
				AssetIdentifiersResource.getAssetIdentifiersFromDb(getConnection(), getUser(), null, assetIdentifierId, null, null, null, null, false, true, false)
						.getObjects();
		if (assetIdentifiers.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(),
				"INSERT INTO scanconfiguration_assetidentifier (scanconfigurationid, assetidentifierid) VALUES (?, ?) ON CONFLICT DO NOTHING", scanConfigurationId,
				assetIdentifierId) > 0) {
			getConnection().commit();
		}

		return createCreatedResponse(scanConfigurationId + "/asset-identifiers/" + assetIdentifierId).build();
	}

	private boolean isConfigurationLinkedToAssetIdentifier(final Integer scanConfigurationId) throws SQLException {
		return DbObject.getLong(getConnection(), "SELECT COUNT(*) FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ?", scanConfigurationId) > 0;
	}

	/**
	 * Delete a link between a {@link ScanConfiguration} and an {@link AssetIdentifier}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to unlink.
	 * @param assetIdentifierId The {@link AssetIdentifier} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{scanConfigurationId: \\d+}/asset-identifiers/{assetIdentifierId: \\d+}")
	@Operation(summary = "Delete a link to an asset identifier")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteAssetIdentifierLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
											  @Parameter(required = true) @PathParam("assetIdentifierId") final Integer assetIdentifierId)
			throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		if (scanConfigurations.get(0).getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		final List<AssetIdentifier> assetIdentifiers =
				AssetIdentifiersResource.getAssetIdentifiersFromDb(getConnection(), getUser(), null, assetIdentifierId, null, null, null, null, false, true, false)
						.getObjects();
		if (assetIdentifiers.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(), "DELETE FROM scanconfiguration_assetidentifier WHERE scanconfigurationid = ? AND assetidentifierid = ?",
				scanConfigurationId, assetIdentifierId) == 0) {
			throw new NotFoundException();
		}

		getConnection().commit();

		return null;
	}

	/**
	 * Link a {@link ScanConfiguration} to an {@link Asset}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to link.
	 * @param assetId The {@link Asset} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{scanConfigurationId: \\d+}/assets/{assetId: \\d+}")
	@Operation(summary = "Create a link to an asset")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response putAssetLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
								 @Parameter(required = true) @PathParam("assetId") final Integer assetId)
			throws SQLException, URISyntaxException, JAXBException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanConfiguration scanConfiguration = scanConfigurations.get(0);
		if (scanConfiguration.getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.SCALE) {
			if (scanConfiguration.getAssetIds() != null && scanConfiguration.getAssetIds().length != 0) {
				throw new InputValidationException("_SCALE_CONFIG_ALREADY_LINKED", ErrorCode.InputValidationFailed);
			}

			if (!((ScaleScanConfigurationTemplate) scanConfiguration.getConfiguration()).areSeedsOneService(false)) {
				throw new InputValidationException("_SEED_MULTIPLE_HOSTS");
			}
		}

		final List<Asset> assets = AssetsResource.getAssetsFromDb(getConnection(), getUser(), getServiceIdentity(), assetId, null, null, null, null, false, true).getRight();
		if (assets.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(), "INSERT INTO asset_scanconfiguration (scanconfigurationid, assetid) VALUES (?, ?) ON CONFLICT DO NOTHING",
				scanConfigurationId, assetId) > 0) {
			getConnection().commit();
		}

		return createCreatedResponse(scanConfigurationId + "/assets/" + assetId).build();
	}

	/**
	 * Delete a link between a {@link ScanConfiguration} and an {@link Asset}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to unlink.
	 * @param assetId The {@link Asset} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{scanConfigurationId: \\d+}/assets/{assetId: \\d+}")
	@Operation(summary = "Delete a link to an asset")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteAssetLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
									@Parameter(required = true) @PathParam("assetId") final Integer assetId)
			throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		if (scanConfigurations.get(0).getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		final List<Asset> assets = AssetsResource.getAssetsFromDb(getConnection(), getUser(), getServiceIdentity(), assetId, null, null, null, null, false, true).getRight();
		if (assets.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(), "DELETE FROM asset_scanconfiguration WHERE scanconfigurationid = ? AND assetid = ?", scanConfigurationId, assetId) == 0) {
			throw new NotFoundException();
		}

		getConnection().commit();

		return null;
	}

	/**
	 * Test a LUA setup script.
	 *
	 * @param test The script to test.
	 * @return The response.
	 */
	@POST
	@Path(ScanConfigurationsResource.TEST_SETUP_SCRIPT_PATH)
	@Operation(summary = "Test a setup LUA script")
	@Valid
	@ConcurrencyRestricted
	@AllowedOnPlatforms(hiab = false)
	@RequestAuthentication(services = {Service.OUTSCAN_FRONTEND, Service.OUTSCAN_ADMIN})
	public Response testSetupScript(@Parameter(required = true) @Valid final TestSetupScript test) {
		if (!Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.isattacker)) {
			throw new ForbiddenException();
		}

		final OutputStream os = new ByteArrayOutputStream();
		try {
			LuaCheck.executeLuaCheck(test.getHostmap() == null ? null : test.getHostmap().toString(), test.getBlacklist() == null ? null : test.getBlacklist().toString(),
					test.getScript(), os, null, null);
		}
		catch (final InterruptedException | IOException | JAXBException e) {
			throw new InternalServerErrorException();
		}
		return Response.ok(os.toString()).build();
	}

	/**
	 * Test a LUA setup script.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to get hostmap and other things from.
	 * @param script The script to test.
	 * @return The response.
	 */
	@POST
	@Path("{scanConfigurationId: \\d+}/" + TEST_SETUP_SCRIPT_PATH)
	@Consumes(MediaType.TEXT_PLAIN)
	@Operation(summary = "Test an application setup LUA script")
	@ConcurrencyRestricted
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response testSetupScriptForScanConfiguration(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
														@Parameter(required = true) final String script) throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), scanConfigurationId, null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanConfiguration scanConfiguration = scanConfigurations.get(0);
		if (scanConfiguration.getTemplate() != ScanTemplate.SCALE) {
			throw new ForbiddenException();
		}

		final OutputStream os = new ByteArrayOutputStream();
		try {
			final TestSetupScript test = new TestSetupScript();
			if (getUser() != null) {
				test.setScript(script);
				test.setHostmap(((ScaleScanConfigurationTemplate) scanConfiguration.getConfiguration()).getHostMap());
			}

			if (Configuration.isHiabEnabled() && getUser() != null && getUser().hasWebAppScanningLicense(Configuration.getConfigService())) {
				LuaCheck.executeLuaCheckHiab(getConnection(), getUser(), 0, test.getHostmap() == null ? null : test.getHostmap().toString(),
						test.getBlacklist() == null ? null : test.getBlacklist().toString(), test.getScript(), os);
			}
			else if (Configuration.getProperty(ConfigKeys.ConfigurationKey.lua_check_url) != null && getUser() != null && getUser().hasWebAppScanningLicense(
					Configuration.getConfigService())) {
				final String path = Configuration.getProperty(ConfigKeys.ConfigurationKey.lua_check_url) + getPathWithBaseUrl() + TEST_SETUP_SCRIPT_PATH;
				LOG.info("Test LUA setup script forwarding: " + path);
				final HttpResponse response = RestUtils.post(path, new JSONObject(MarshallingUtils.marshal(test)), false, false, true);
				LOG.info("Test LUA setup script forwarding completed with " + response.getStatusLine().getStatusCode());
				return Response.status(response.getStatusLine().getStatusCode()).entity(EntityUtils.toString(response.getEntity(), "UTF-8")).build();
			}
			else if (getUser() != null && getUser().hasWebAppScanningLicense(
					Configuration.getConfigService())) {
				final ScanStatus scanStatus = new ScanStatus();
				scanStatus.setUserId(getUser().getId());
				scanStatus.setService(ScanServiceType.LuaCheck);
				scanStatus.setId(scanStatus.save(getConnection()));
				LuaCheck.executeLuaCheck(test.getHostmap() == null ? null : test.getHostmap().toString(), test.getBlacklist() == null ? null : test.getBlacklist().toString(),
						test.getScript(), os, getUser().getCustomerUuid(), scanStatus);
				scanStatus.save(getConnection());
				getConnection().commit();
				final long startTime = System.currentTimeMillis();
				boolean hasResult = !Strings.isNullOrEmpty(ScanStatus.getById(ScanStatus.class, getConnection(), scanStatus.getId()).getReport());
				while (System.currentTimeMillis() - startTime < Duration.ofSeconds(Configuration.getProperty(ConfigKeys.ConfigurationIntKey.lua_check_timeout) * 2L).toMillis() && !hasResult) {
					Thread.sleep(1000);
					hasResult = !Strings.isNullOrEmpty(ScanStatus.getById(ScanStatus.class, getConnection(), scanStatus.getId()).getReport());
				}
				final ScanStatus reportStatus = ScanStatus.getById(ScanStatus.class, getConnection(), scanStatus.getId());
				if (reportStatus != null) {
					DbObject.removeObject(ScanStatus.class, getConnection(), reportStatus.getId());
					getConnection().commit();
					if (hasResult) {
						final String result = reportStatus.getReport();
						return Response.status(Response.Status.OK).entity(result).build();
					}
				}
				return Response.status(Response.Status.REQUEST_TIMEOUT).build();
			}
			else {
				throw new ForbiddenException();
			}
		}
		catch (final SQLException | InterruptedException | IOException | JAXBException ex) {
			LOG.error("Couldn't forward LUA check", ex);
			throw new InternalServerErrorException();
		}
		catch (final CertificateException | NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
			LOG.error("Couldn't perform post request with extended SSL context", e);
			throw new InternalServerErrorException();
		}
		return Response.ok(os.toString()).build();
	}

	/**
	 * Link a {@link ScanConfiguration} to a {@link Tag}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to link.
	 * @param tagId The {@link Tag} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{scanConfigurationId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Create a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response putScanConfigurationTagLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
												@Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException, URISyntaxException {
		final ScanConfiguration scanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
				.resourceId(scanConfigurationId)
				.addedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		if (scanConfiguration != null) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanConfigurationEvent.modified(), getUser());
		}

		return createCreatedResponse(scanConfigurationId + "/tags/" + tagId).build();
	}

	/**
	 * Create a link between one or several {@link ScanConfiguration} and a {@link Tag}.
	 *
	 * @param tagId The {@link Tag} to link.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param filteringParameters Filtering parameters.
	 * @return The response.
	 */
	@PUT
	@Path("/tags/{tagId: \\d+}")
	@Operation(summary = "Create a tag link to one or several scan configurations")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response putScanConfigurationTagLinks(
			@Parameter(required = true) @PathParam("tagId") final Integer tagId,
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException, URISyntaxException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, null, filteringParameters, false, true,
						false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final List<Event> eventList = new ArrayList<>();
		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final ScanConfiguration modifiedScanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
					.resourceId(scanConfiguration.getId())
					.addedTags(new Integer[] {tagId}).build());
			if (modifiedScanConfiguration != null) {
				final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(modifiedScanConfiguration.getId(),
						modifiedScanConfiguration.getCustomerId(), null, null, null);
				eventList.add(scanConfigurationEvent.modified());
			}
		}
		getConnection().commit();

		for (final Event event : eventList) {
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(event, getUser());
		}

		return createCreatedResponse("tags/" + tagId).build();
	}

	/**
	 * Delete a link between a {@link ScanConfiguration} and a {@link Tag}.
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to unlink.
	 * @param tagId The {@link Tag} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{scanConfigurationId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteScanConfigurationTagLink(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
												   @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException {
		final ScanConfiguration scanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
				.resourceId(scanConfigurationId)
				.deletedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		if (scanConfiguration != null) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanConfigurationEvent.modified(), getUser());
		}

		return null;
	}

	/**
	 * Delete a link between one or several {@link ScanConfiguration} and a {@link Tag}.
	 *
	 * @param tagId The {@link Tag} to unlink.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param filteringParameters Filtering parameters.
	 * @return The response.
	 */
	@DELETE
	@Path("/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a tag link to one or several scan configurations")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response deleteScanConfigurationTagLinks(
			@Parameter(required = true) @PathParam("tagId") final Integer tagId,
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, null, filteringParameters, false, true,
						false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final List<Event> eventList = new ArrayList<>();
		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final ScanConfiguration modifiedScanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
					.resourceId(scanConfiguration.getId())
					.deletedTags(new Integer[] {tagId}).build());
			if (modifiedScanConfiguration != null) {
				final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(modifiedScanConfiguration.getId(),
						modifiedScanConfiguration.getCustomerId(), null, null, null);
				eventList.add(scanConfigurationEvent.modified());
			}
		}
		getConnection().commit();

		for (final Event event : eventList) {
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(event, getUser());
		}

		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link Tag} linked to a {@link ScanConfiguration}
	 *
	 * @param scanConfigurationId The {@link ScanConfiguration} to modify
	 * @param tagIds The new set of {@link Tag}
	 * @return The response
	 */
	@PUT
	@Path("{scanConfigurationId: \\d+}/tags")
	@Operation(summary = "Modify linked set of tags")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response modifyScanConfigurationTagLinks(@Parameter(required = true) @PathParam("scanConfigurationId") final Integer scanConfigurationId,
													@RequestBody(required = true, content = @Content(
															array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds)
			throws SQLException {
		final ScanConfiguration scanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
				.resourceId(scanConfigurationId)
				.resultTags(TagsResource.extractTagIds(tagIds)).build());
		getConnection().commit();

		if (scanConfiguration != null) {
			final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanConfiguration.getId(),
					scanConfiguration.getCustomerId(), null, null, null);
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanConfigurationEvent.modified(), getUser());
		}

		return null;
	}

	/**
	 * Validate scan configuration tags and update the links
	 *
	 * @param tagModifyInfo info about added/deleted/modified tags
	 * @return scan configuration if modified else null
	 */
	private ScanConfiguration modifyScanConfigurationTagLinks(final TagModifyInfo tagModifyInfo) throws SQLException {
		final List<ScanConfiguration> scanConfigurations
				= getScanConfigurationsFromDb(getConnection(), getUser(), tagModifyInfo.getResourceId(), null, null, null, null, false, true, false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}

		if (modifyTagLinks(tagModifyInfo) > 0) {
			final ScanConfiguration scanConfiguration = scanConfigurations.get(0);
			scanConfiguration.setUpdatedById(getUpdaterId());
			scanConfiguration.save(getConnection());
			return scanConfiguration;
		}
		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link Tag} linked to several {@link ScanConfiguration}
	 *
	 * @param tagIds The new set of {@link Tag}
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param filteringParameters Filtering parameters.
	 * @return The response
	 */
	@PUT
	@Path("/tags")
	@Operation(summary = "Modify linked set of tags to several scan configurations")
	@RequiredPermissions(permissions = UserRolePermission.SCAN_CONFIGURATIONS_VIEW_AND_MANAGE)
	public Response modifyScanConfigurationsTagLinks(
			@RequestBody(required = true, content = @Content(array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds,
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final List<ScanConfiguration> scanConfigurations =
				getScanConfigurationsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, null, filteringParameters, false, true,
						false).getObjects();
		if (scanConfigurations.isEmpty()) {
			throw new NotFoundException();
		}
		final List<Event> eventList = new ArrayList<>();
		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			final ScanConfiguration modifiedScanConfiguration = modifyScanConfigurationTagLinks(TagModifyInfo.builder()
					.resourceId(scanConfiguration.getId())
					.resultTags(TagsResource.extractTagIds(tagIds)).build());
			if (modifiedScanConfiguration != null) {
				final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(modifiedScanConfiguration.getId(),
						modifiedScanConfiguration.getCustomerId(), null, null, null);
				eventList.add(scanConfigurationEvent.modified());
			}
		}
		getConnection().commit();

		for (final Event event : eventList) {
			ServiceProvider.getEventApiV2(getConnection()).handleEvent(event, getUser());
		}

		return null;
	}

	/**
	 * Get a list of {@link ScanConfiguration}s from DB.
	 *
	 * @param connection The database connection.
	 * @param user The user to get the {@link ScanConfiguration}s for.
	 * @param id The ID of the scan configuration if wanting a specific one.
	 * @param paginationParameters The pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters The filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param streamObjects Whether to get the objects as stream.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<ScanConfiguration> getScanConfigurationsFromDb(final Connection connection, final BaseLoggedOnUser user, final Integer id,
																					final PaginationParameters paginationParameters,
																					final SortingParameters sortingParameters,
																					final FieldSelectionParameters fieldSelectionParameters,
																					final FilteringParameters filteringParameters, final boolean getCount,
																					final boolean includeObjects, final boolean streamObjects) throws SQLException {
		return getScanConfigurationsFromDb(connection, user, id, null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, getCount,
				includeObjects, streamObjects);
	}

	/**
	 * Get a list of {@link ScanConfiguration}s from DB.
	 *
	 * @param connection The database connection.
	 * @param user The user to get the {@link ScanConfiguration}s for.
	 * @param id The ID of the scan configuration if wanting a specific one.
	 * @param ids The IDs of the scan configurations if wanting several.
	 * @param paginationParameters The pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters The filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param streamObjects Whether to get the objects as stream.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<ScanConfiguration> getScanConfigurationsFromDb(final Connection connection, final BaseLoggedOnUser user, final Integer id,
																					final Integer[] ids, final PaginationParameters paginationParameters,
																					final SortingParameters sortingParameters,
																					final FieldSelectionParameters fieldSelectionParameters,
																					final FilteringParameters filteringParameters, final boolean getCount,
																					final boolean includeObjects, final boolean streamObjects) throws SQLException {
		final List<Object> params = new ArrayList<>();
		final StringBuilder filter = new StringBuilder("deleted IS NULL");

		StringUtils.concatenateFilters(filter, "customerid = ?");
		params.add(user.getCustomerId());

		if (ids != null) {
			StringUtils.concatenateFilters(filter, "id = ANY(?)");
			params.add(ids);
		}

		if (user.hasLimitResources(ResourceType.SCANCONFIGURATION)) {
			((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.SCANCONFIGURATION);
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);

		if (!Configuration.isHiabEnabled()) {
			config.addColumnOverride("scannerName", "(CASE WHEN COALESCE(scannerid, 0) = 0 THEN 'Outscan' ELSE scannername END)");
		}

		return ScanConfiguration.getObjects(ScanConfiguration.class, connection, user, config, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, id != null ? id.toString() : null, getCount, includeObjects, streamObjects, filter.toString(), params);
	}

	/**
	 * Save a scan configuration.
	 *
	 * @param scanConfiguration The scan configuration to save.
	 * @param currentScanConfiguration The current scan configuration.
	 * @return ID of the created object
	 */
	private Integer saveConfiguration(final ScanConfiguration scanConfiguration, final ScanConfiguration currentScanConfiguration)
			throws SQLException, JAXBException {
		if ((scanConfiguration.getTemplate() == ScanTemplate.NETWORK_DISCOVERY || scanConfiguration.getTemplate() == ScanTemplate.CLOUD_DISCOVERY)
				&& !getUser().hasFeature(Feature.NETSEC)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		ScannerImpl scanner = null;
		if (Arrays.asList(ScanTemplate.SCALE, ScanTemplate.NETWORK_SCAN, ScanTemplate.DOCKER_DISCOVERY, ScanTemplate.NETWORK_DISCOVERY, ScanTemplate.CLOUD_DISCOVERY)
				.contains(scanConfiguration.getTemplate())) {
			if (scanConfiguration.getScannerId() != null) {
				final Pair<Long, List<ScannerImpl>> scanners =
						ScannersResource.getScannersFromDb(getConnection(), getUser(), scanConfiguration.getScannerId(), null, null, null, null, true, true);
				if (scanners.getLeft() == 0) {
					throw new NotFoundException();
				}
				if (scanConfiguration.getTemplate() == ScanTemplate.CLOUD_DISCOVERY) {
					scanner = scanners.getRight().get(0);
					if (scanner.getAppsecScaleScanner()) {
						throw new ForbiddenException("_NOT_ALLOWED_SCANNER_APPSEC_HIAB");
					}
				}
			}
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.SWAT && !getUser().hasRoles(Role.SwatService, Role.SuperSwatService)) {
			throw new ForbiddenException("_MISSING_ROLES", null, Arrays.asList(Role.SwatService.name(), Role.SuperSwatService.name()));
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.CLOUDSEC && Configuration.isHiabEnabled()) {
			throw new ForbiddenException("_CLOUDSEC_CONFIGURATIONS_NOT_ALLOWED_HIAB");
		}

		if (scanConfiguration.getCustomerId() != null && !(scanConfiguration.getTemplate() == ScanTemplate.SWAT && getUser().hasRoles(Role.SwatService,
				Role.SuperSwatService))) {
			scanConfiguration.setCustomerId(getUser().getCustomerId());
		}

		if (scanConfiguration.getId() != null && scanConfiguration.getId() > 0) {
			scanConfiguration.setUpdatedById(getUpdaterId());
		}
		else {
			scanConfiguration.setCreatedById(getUpdaterId());
		}

		if (scanConfiguration.getTemplate() == ScanTemplate.SCALE) {
			final ScaleScanConfigurationTemplate scaleScanConfigurationTemplate = (ScaleScanConfigurationTemplate) scanConfiguration.getConfiguration();
			scaleScanConfigurationTemplate.validateSeeds();
			if (!scaleScanConfigurationTemplate.areSeedsOneService(true)) {
				throw new InputValidationException("_SEED_MULTIPLE_HOSTS");
			}
			if (currentScanConfiguration != null && !ArrayUtils.isEmpty(currentScanConfiguration.getScheduleIds())) {
				validateConfigurationOutsideWorkflow(scanConfiguration, null);
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.CLOUDSEC) {
			final CloudsecScanConfigurationTemplate template = (CloudsecScanConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser());
			scanConfiguration.setConfiguration(template);
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.NETWORK_SCAN) {
			final NetworkScanConfigurationTemplate template = (NetworkScanConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser(), scanConfiguration.getScannerId(), ServiceProvider.getConfigService());
			if (currentScanConfiguration != null && !ArrayUtils.isEmpty(currentScanConfiguration.getScheduleIds())) {
				validateConfigurationOutsideWorkflow(scanConfiguration, null);
			}
			scanConfiguration.setConfiguration(template);
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.DOCKER_SCAN) {
			final DockerScanConfigurationTemplate template = (DockerScanConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser());
			if (currentScanConfiguration != null && !ArrayUtils.isEmpty(currentScanConfiguration.getScheduleIds())) {
				validateConfigurationOutsideWorkflow(scanConfiguration, null);
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.DOCKER_DISCOVERY) {
			final DockerDiscoveryScanConfigurationTemplate template = (DockerDiscoveryScanConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser());
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.NETWORK_DISCOVERY) {
			final NetworkDiscoveryConfigurationTemplate template = (NetworkDiscoveryConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser(), ServiceProvider.getConfigService());
			if (!template.isUpdateAssets() && scanConfiguration.getWorkflowId() != null) {
				throw new InputValidationException("_WORKFLOW_DISCOVERY_CREATE_ASSETS");
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.CLOUD_DISCOVERY) {
			final CloudDiscoveryConfigurationTemplate template = (CloudDiscoveryConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser());
			if (!template.isUpdateAssets() && scanConfiguration.getWorkflowId() != null) {
				throw new InputValidationException("_WORKFLOW_DISCOVERY_CREATE_ASSETS");
			}
			if (scanner != null) {
				final List<Account> accounts = AccountsResource.getAccountsFromDb(getConnection(), getUser(), template.getAccountId(),
						null, null, null, null, false, true, true, false).getRight();
				if (!accounts.isEmpty()) {
					final Credential arnCredential = Arrays.stream(accounts.get(0).getCredentials())
							.filter(credential -> CredentialClassType.ROLE_ARN.getId().equals(credential.getClassId())).findAny()
							.orElse(null);
					if (arnCredential != null && !Configuration.getProperty(ConfigKeys.ConfigurationBooleanKey.aws_access_key_allow_remote)) {
						if (Configuration.isHiabEnabled() && scanner.isOutpost()) {
							throw new ForbiddenException("_NOT_ALLOWED_ROLE_ARN_TO_BE_TRANSMITTED_TO_OUTSCAN_SCANNERS");
						}
						if (!Configuration.isHiabEnabled() && !scanner.isOutpost() && scanner.getId() != ScannerBusiness.LOCAL_SCANNER) {
							throw new ForbiddenException("_NOT_ALLOWED_ROLE_ARN_TO_BE_TRANSMITTED_TO_HIAB_SCANNERS");
						}
					}
				}
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.AGENT_SCAN) {
			final AgentScanConfigurationTemplate template = (AgentScanConfigurationTemplate) scanConfiguration.getConfiguration();
			template.validate(new DbLayerNativeStatementExecutor(getConnection()), getUser());
		}

		return (int) scanConfiguration.save(getConnection());
	}

	/**
	 * Validate if scan configuration can be used outside of workflow.
	 *
	 * @param scanConfiguration Scan configuration to validate
	 * @param workflow Workflow to check when scan configuration is first in chain
	 */
	public static void validateConfigurationOutsideWorkflow(final ScanConfiguration scanConfiguration, final Workflow workflow) throws JAXBException {
		if (scanConfiguration.getTemplate() == ScanTemplate.NETWORK_SCAN) {
			final NetworkScanConfigurationTemplate configuration = (NetworkScanConfigurationTemplate) scanConfiguration.getConfiguration();
			if (ArrayUtils.isEmpty(configuration.getAssetTagIds())) {
				throw new InputValidationException("_SCAN_CONFIGURATION_MISSING_ASSET_TAGS");
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.SCALE) {
			final ScaleScanConfigurationTemplate configuration = (ScaleScanConfigurationTemplate) scanConfiguration.getConfiguration();
			if (!configuration.areSeedsOneService(false)) {
				throw new InputValidationException("_SCAN_CONFIGURATION_MISSING_SEED_URLS");
			}

			final Integer scannerId = workflow == null ? scanConfiguration.getScannerId() : workflow.getScannerId();
			if (Configuration.isHiabEnabled() && workflow == null && (scannerId == null || scannerId == ScannerBusiness.LOCAL_SCANNER)) {
				throw new ForbiddenException("_NOT_ALLOWED_SCAN_LOCAL_SCANNER");
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.DOCKER_DISCOVERY) {
			final Integer scannerId = workflow == null ? scanConfiguration.getScannerId() : workflow.getScannerId();
			if (!Configuration.isHiabEnabled() && workflow == null && (scannerId == null || scannerId == ScannerBusiness.LOCAL_SCANNER)) {
				throw new ForbiddenException("_NOT_ALLOWED_SCAN_LOCAL_SCANNER");
			}
		}
		else if (scanConfiguration.getTemplate() == ScanTemplate.DOCKER_SCAN) {
			if (ArrayUtils.isEmpty(scanConfiguration.getAssetIds())) {
				throw new InputValidationException("_NEED_TO_LINK_ASSET_TO_SCAN");
			}
		}
	}

	/**
	 * Delete agent schedule for scan configuration.
	 *
	 * @param scanConfiguration Scan configuration
	 * @param customerUuid UUID of the customer
	 */
	private static void deleteAgentSchedule(final ScanConfiguration scanConfiguration, final String customerUuid) throws JAXBException {
		final AgentScanConfigurationProperties properties = (AgentScanConfigurationProperties) scanConfiguration.getProperties();
		if (properties != null && properties.getScheduleUuid() != null) {
			try {
				ServiceProvider.getAgentsService().deleteSchedule(customerUuid, properties.getScheduleUuid());
				properties.setScheduleUuid(null);
				scanConfiguration.setProperties(properties);
			}
			catch (final Exception e) {
				LOG.error("Failed to remove agent schedule " + properties.getScheduleUuid() + " when removing scan configuration " + scanConfiguration.getId(), e);
			}
		}
	}

	/**
	 * Delete customer's agent schedule for scan configuration.
	 *
	 * @param databaseConnection A database connection
	 * @param customer Customer
	 */
	protected static void deleteCustomerAgentSchedule(final Connection databaseConnection, final Customer customer) throws JAXBException, SQLException {
		final String sql = DbHelper.getSelect(ScanConfiguration.class, Access.ADMIN, "template = ? AND customerid = ? AND deleted IS NULL");

		final List<ScanConfiguration> scanConfigurations = ScanConfiguration.fetchObjects(ScanConfiguration.class, databaseConnection, sql, ScanTemplate.AGENT_SCAN, customer.getId());

		for (final ScanConfiguration scanConfiguration : scanConfigurations) {
			deleteAgentSchedule(scanConfiguration, customer.getUuid());
			scanConfiguration.save(databaseConnection);
		}
	}

	/**
	 * Update "enabled" field for scan configurations base on customer
	 *
	 * @param databaseConnection A database connection
	 * @param customer Customer
	 * @param scanTemplates List of scan template
	 * @param enabled Updated value
	 */
	protected static void updateScanConfigurationsEnable(final Connection databaseConnection, final Customer customer, final ScanTemplate[] scanTemplates, final boolean enabled) throws SQLException {
		final String updateQuery = "UPDATE scanconfigurations SET enabled = ? WHERE customerid = ? AND template = ANY(?::scantemplate[]) AND enabled != ? AND deleted IS NULL";
		final List<Object> updateParams = new ArrayList<>();
		updateParams.add(enabled);
		updateParams.add(customer.getId());
		updateParams.add(scanTemplates);
		updateParams.add(enabled);

		DbObject.executeUpdate(databaseConnection, updateQuery, updateParams);
	}
}
