package com.chilicoders.rest.resources;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.bl.MessageBusiness;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.ManagedReportGroup;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ManagedReportGroupsResource.PATH)
@Path(ManagedReportGroupsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ManagedReportGroupsResource extends BaseResource {
	public static final String PATH = "managed-report-groups";

	/**
	 * Get header with total count of {@link ManagedReportGroup}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count (Deprecated: 2019-12-11)", deprecated = true, responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getManagedReportGroupHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getManagedReportGroupsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	@GET
	@Operation(summary = "Get a list of managed report groups (Deprecated: 2019-12-11)", deprecated = true, responses = @ApiResponse(description = DEFAULT_RESPONSE,
			content = @Content(array = @ArraySchema(schema = @Schema(implementation = ManagedReportGroup.class))),
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getManagedReportGroupList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<ManagedReportGroup>>() {
		}, getManagedReportGroupsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a managed report group by ID.
	 *
	 * @param managedReportGroupId The ID of the managed report group to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The managed report group.
	 */
	@GET
	@Path("{managedReportGroupId: -?\\d+}")
	@Operation(summary = "Get a managed report group (Deprecated: 2019-12-11)", deprecated = true)
	public ManagedReportGroup getManagedReportGroup(@Parameter(required = true) @PathParam("managedReportGroupId") final long managedReportGroupId,
													@BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ManagedReportGroup> list =
				getManagedReportGroupsFromDb(getConnection(), getUser(), managedReportGroupId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		return list.get(0);
	}

	/**
	 * Get a list of managed report groups from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<ManagedReportGroup>> getManagedReportGroupsFromDb(final Connection conn, final BaseLoggedOnUser user, final Long id,
																					   final PaginationParameters paginationParameters,
																					   final SortingParameters sortingParameters,
																					   final FieldSelectionParameters fieldSelectionParameters,
																					   final FilteringParameters filteringParameters, final boolean getCount,
																					   final boolean includeObjects) throws SQLException {
		final StringBuilder allManagedReportsWhere = new StringBuilder("xuserxid = ?");
		final List<Object> allManagedReportsParams = new ArrayList<>();
		allManagedReportsParams.add(user.getMainUserId());

		final StringBuilder ungroupedManagedReportsWhere = new StringBuilder("xuserxid = ? AND reportgroupid = 0");
		final List<Object> ungroupedManagedReportsParams = new ArrayList<>();
		ungroupedManagedReportsParams.add(user.getMainUserId());

		final StringBuilder countWhere = new StringBuilder("xuserxid = ? AND reportgroupid = ANY(xpathdown)");
		final List<Object> countParams = new ArrayList<>();
		countParams.add(user.getMainUserId());

		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		if (user.isSubUser() && !user.isSuperUser() && (DbObject.executeCountQuery(conn, "SELECT managedserviceslimited FROM tusers WHERE xid=?", user.getMainUserId())
				!= 0)) {
			final Long[] subUsers = DbObject.getLongArray(conn, "SELECT ARRAY_AGG(xid) FROM tsubusers WHERE xpathup ~ (',' || ? || ',')", user.getSubUserId());
			final Long[] subuserGroups =
					DbObject.getLongArray(conn, "SELECT ARRAY_AGG(reportgroupid) FROM xlinkmanagedservices WHERE reportgroupid != 0 AND xsubuserxid = ANY (?)",
							new Object[] {subUsers});
			final Long[] subuserReports = DbObject.getLongArray(conn,
					"SELECT ARRAY_AGG(reportid) FROM xlinkmanagedservices WHERE (reportgroupid = 0 OR reportgroupid IS NULL) AND xsubuserxid = ANY (?)",
					new Object[] {subUsers});

			StringUtils.concatenateFilters(allManagedReportsWhere, "(xid = ANY(?) OR reportgroupid = ANY(?))");
			allManagedReportsParams.add(subuserReports);
			allManagedReportsParams.add(subuserGroups);

			StringUtils.concatenateFilters(ungroupedManagedReportsWhere, "xid = ANY(?)");
			ungroupedManagedReportsParams.add(subuserReports);

			StringUtils.concatenateFilters(countWhere, "reportgroupid = ANY(?)");
			countParams.add(subuserGroups);

			StringUtils.concatenateFilters(where, "xpathdown && (?)");
			params.add(subuserGroups);
		}

		final Long allCount = DbObject.getLong(conn, "SELECT COUNT(DISTINCT xid) FROM tmanagedreports WHERE " + allManagedReportsWhere.toString(), allManagedReportsParams);
		final Long ungroupedCount =
				DbObject.getLong(conn, "SELECT COUNT(DISTINCT xid) FROM tmanagedreports WHERE " + ungroupedManagedReportsWhere.toString(), ungroupedManagedReportsParams);

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.MAINUSER);
		config.addColumnOverride("count", "(SELECT COUNT(DISTINCT xid) FROM tmanagedreports WHERE " + countWhere.toString() + ")");
		config.addColumnParams("count", countParams);

		final Pair<Long, List<ManagedReportGroup>> list =
				ManagedReportGroup.getObjects(ManagedReportGroup.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters,
						filteringParameters,
						id != null ? id.toString() : null, getCount, includeObjects, where.toString(), params);

		if (id == null || id == 0) {
			final ManagedReportGroup ungroupedGroup = new ManagedReportGroup();
			ungroupedGroup.setId(0);
			ungroupedGroup.setParentId(-1);
			ungroupedGroup.setUserId(user.getMainUserId());
			ungroupedGroup.setName(MessageBusiness.getMessageString("_UNGROUPED", StringUtils.setEmpty(user.getLanguage(), "en"), "MESSAGES"));
			ungroupedGroup.setCount(ungroupedCount);
			list.getRight().add(0, ungroupedGroup);
		}

		if (id == null || id == -1) {
			final ManagedReportGroup allGroup = new ManagedReportGroup();
			allGroup.setId(-1);
			allGroup.setParentId(-2);
			allGroup.setUserId(user.getMainUserId());
			allGroup.setName(MessageBusiness.getMessageString("_ALL_REPORTS", StringUtils.setEmpty(user.getLanguage(), "en"), "MESSAGES"));
			allGroup.setCount(allCount);
			list.getRight().add(0, allGroup);
		}

		return Pair.of(getCount ? (Long) (list.getLeft() + 2) : list.getLeft(), list.getRight());
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
