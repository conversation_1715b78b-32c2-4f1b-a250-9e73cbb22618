package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.event.rest.events.ResourceGroupEvent;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.Resource;
import com.chilicoders.rest.models.ResourceGroup;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ResourceGroupsResource.PATH)
@Path(ResourceGroupsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ResourceGroupsResource extends BaseResource {
	public static final String PATH = "resource-groups";

	/**
	 * Get header with total count of {@link ResourceGroup}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getResourceGroupHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getResourceGroupsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link ResourceGroup}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link ResourceGroup}s.
	 */
	@GET
	@Operation(summary = "Get a list of resource groups",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ResourceGroup.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getResourceGroupList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<ResourceGroup>>() {
		}, getResourceGroupsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a {@link ResourceGroup}.
	 *
	 * @param resourceGroupId The ID of the {@link ResourceGroup} to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The {@link ResourceGroup}.
	 */
	@GET
	@Path("{resourceGroupId: \\d+}")
	@Operation(summary = "Get a resource group")
	public ResourceGroup getResourceGroup(@Parameter(required = true) @PathParam("resourceGroupId") final Integer resourceGroupId,
										  @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ResourceGroup> resourceGroups =
				getResourceGroupsFromDb(getConnection(), getUser(), resourceGroupId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (resourceGroups.isEmpty()) {
			throw new NotFoundException();
		}
		return resourceGroups.get(0);
	}

	/**
	 * Create a {@link ResourceGroup}.
	 *
	 * @param resourceGroup The {@link ResourceGroup} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a resource group")
	@Valid
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response postResourceGroup(
			@Parameter(required = true) @Valid @NotNull final ResourceGroup resourceGroup,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws URISyntaxException, SQLException, JAXBException {
		resourceGroup.setCustomerId(getUser().getCustomerId());
		resourceGroup.setCreatedById(getUpdaterId());
		updateResourceTags(getConnection(), resourceGroup);
		final Integer resourceGroupId = (int) resourceGroup.save(getConnection());
		getConnection().commit();

		final ResourceGroupEvent resourceGroupEvent = new ResourceGroupEvent(resourceGroupId, resourceGroup.getCustomerId(), null);
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(resourceGroupEvent.created(), getUser());

		final ResponseBuilder response = createCreatedResponse(resourceGroupId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getResourceGroupsFromDb(getConnection(), getUser(), resourceGroupId, null, null, fieldSelectionParameters, null, false, true).getRight().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link ResourceGroup}.
	 *
	 * @param resourceGroupId The ID of the {@link ResourceGroup} to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{resourceGroupId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a resource group")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response patchResourceGroup(
			@Parameter(required = true) @PathParam("resourceGroupId") final Integer resourceGroupId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = ResourceGroup.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<ResourceGroup> resourceGroups = getResourceGroupsFromDb(getConnection(), getUser(), resourceGroupId, null, null, null, null, false, true).getRight();
		if (resourceGroups.isEmpty()) {
			throw new NotFoundException();
		}

		if (resourceGroups.get(0).isSystem()) {
			throw new ForbiddenException("_NOT_ALLOWED_CHANGE_SYSTEM_RESOURCEGROUPS");
		}

		final ResourceGroup newResourceGroup = ResourceGroup.patch(resourceGroups.get(0), patch);
		newResourceGroup.setId(resourceGroupId);
		newResourceGroup.setCustomerId(getUser().getCustomerId());
		newResourceGroup.setCreatedById(resourceGroups.get(0).getCreatedById());
		newResourceGroup.setUpdatedById(getUpdaterId());
		updateResourceTags(getConnection(), newResourceGroup);
		newResourceGroup.save(getConnection());
		getConnection().commit();

		final ResourceGroupEvent resourceGroupEvent = new ResourceGroupEvent(resourceGroupId, newResourceGroup.getCustomerId(), null);
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(resourceGroupEvent.modified(), getUser());

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getResourceGroupsFromDb(getConnection(), getUser(), resourceGroupId, null, null, fieldSelectionParameters, null, false, true).getRight().get(0))
					.build();
		}

		return null;
	}

	/**
	 * Update tags in ResourceGroup.
	 *
	 * @param conn Database connection
	 * @param resourceGroup ResourceGroup to update
	 */
	private void updateResourceTags(final Connection conn, final ResourceGroup resourceGroup) throws JAXBException, SQLException {
		final Resource[] resources = resourceGroup.getResources();
		if (resources != null) {
			for (final Resource resource : resources) {
				if (resource.getTagIds() != null) {
					final Integer[] tagIds = ResourceGroup.getIntegerArray(conn, "SELECT array_agg(id) FROM tags WHERE customerid = ? AND id =ANY(?) AND deleted IS NULL",
							resourceGroup.getCustomerId(), resource.getTagIds());
					resource.setTagIds(tagIds == null || tagIds.length == 0 ? null : tagIds);
				}
			}
		}
		resourceGroup.setResources(resources);
	}

	/**
	 * Delete a {@link ResourceGroup}.
	 *
	 * @param resourceGroupId The ID of the {@link ResourceGroup} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{resourceGroupId: \\d+}")
	@Operation(summary = "Delete a resource group")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response deleteResourceGroup(@Parameter(required = true) @PathParam("resourceGroupId") final Integer resourceGroupId) throws SQLException {
		final List<ResourceGroup> resourceGroups = getResourceGroupsFromDb(getConnection(), getUser(), resourceGroupId, null, null, null, null, false, true).getRight();
		if (resourceGroups.isEmpty()) {
			throw new NotFoundException();
		}

		if (resourceGroups.get(0).isSystem()) {
			throw new ForbiddenException("_NOT_ALLOWED_CHANGE_SYSTEM_RESOURCEGROUPS");
		}

		final ResourceGroup resourceGroup = resourceGroups.get(0);
		resourceGroup.setCustomerId(getUser().getCustomerId());
		resourceGroup.setDeleted(getUpdaterId());
		resourceGroup.save(getConnection());

		ResourceGroup.executeUpdate("UPDATE tsubusers SET resourcegroupids = array_remove(resourcegroupids, ?) WHERE xiparentid = ? AND ? = ANY(resourcegroupids)",
				resourceGroup.getId(), getUser().getMainUserId(), resourceGroup.getId());

		getConnection().commit();

		final ResourceGroupEvent resourceGroupEvent = new ResourceGroupEvent(resourceGroupId, resourceGroup.getCustomerId(), null);
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(resourceGroupEvent.deleted(), getUser());

		return null;
	}

	/**
	 * Get a list of {@link ResourceGroup}s from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<ResourceGroup>> getResourceGroupsFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id,
																			 final PaginationParameters paginationParameters,
																			 final SortingParameters sortingParameters,
																			 final FieldSelectionParameters fieldSelectionParameters,
																			 final FilteringParameters filteringParameters, final boolean getCount,
																			 final boolean includeObjects)
			throws SQLException {
		final StringBuilder filter = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(filter, "deleted IS NULL");
		StringUtils.concatenateFilters(filter, "(customerid = ? OR system)");
		params.add(user.getCustomerId());

		if (!user.hasPermission(UserRolePermission.USERS_VIEW, UserRolePermission.USERS_VIEW_AND_MANAGE)) {
			StringUtils.concatenateFilters(filter, "id = ANY(?)");
			params.add(((LoggedOnSubUser) user).getResourceGroupIds());
		}

		return ResourceGroup.getObjects(ResourceGroup.class, conn, user, new DbObjectSendConfiguration(Access.ADMIN), paginationParameters, sortingParameters,
				fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, filter.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
