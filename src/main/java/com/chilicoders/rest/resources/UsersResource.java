package com.chilicoders.rest.resources;

import static com.chilicoders.rest.annotations.RequestAuthentication.ServiceAccounts.SWAT;

import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.bl.UserBusiness;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.core.libellum.ServiceIdentity.Service;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.core.user.api.model.TwoFactorType;
import com.chilicoders.db.Access;
import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.User;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.ConfigurableDisabled;
import com.chilicoders.rest.annotations.RequestAuthentication;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.annotations.RequiredRoles;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.AdminChangePasswordRequest;
import com.chilicoders.rest.models.ChangePasswordRequest;
import com.chilicoders.rest.models.Tag;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.Pgp;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

@io.swagger.v3.oas.annotations.tags.Tag(name = UsersResource.PATH)
@Path(UsersResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredRoles(roles = Role.MainUser)
public class UsersResource extends TaggableResource {
	public static final String PATH = "users";

	private static final Pair<String, String> TAG_LINK_TABLE = ImmutablePair.of("tag_user", "userid");

	@Override
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Pair<String, String> getTagLinkTable() {
		return TAG_LINK_TABLE;
	}

	/**
	 * Get header with total count of {@link User}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.USERS_VIEW, UserRolePermission.USERS_VIEW_AND_MANAGE})
	@RequestAuthentication(services = {Service.OUTSCAN_REMOTE_SUPPORT, Service.SWAT_API}, serviceAccounts = {SWAT}, optional = true)
	public Response getUserHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getUsersFromDb(getConnection(), getUser(), getServiceIdentity(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of users.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The users.
	 */
	@GET
	@Operation(summary = "Get a list of users",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = User.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.USERS_VIEW, UserRolePermission.USERS_VIEW_AND_MANAGE})
	@RequestAuthentication(services = {Service.OUTSCAN_REMOTE_SUPPORT, Service.SWAT_API}, serviceAccounts = {SWAT}, optional = true)
	public Response getUserList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<User>>() {
		}, getUsersFromDb(getConnection(), getUser(), getServiceIdentity(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true,
				true));
	}

	/**
	 * Get a user.
	 *
	 * @param userId The ID of the user to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The user.
	 */
	@GET
	@Path("{userId: \\d+}")
	@Operation(summary = "Get a user")
	@RequiredPermissions(permissions = {UserRolePermission.USERS_VIEW, UserRolePermission.USERS_VIEW_AND_MANAGE})
	@RequestAuthentication(services = {Service.SWAT_API}, serviceAccounts = {SWAT}, optional = true)
	public User getUser(@Parameter(required = true) @PathParam("userId") final Integer userId, @BeanParam final FieldSelectionParameters fieldSelectionParameters)
			throws SQLException, IllegalAccessException, IOException {
		final List<User> list = getUsersFromDb(getConnection(), getUser(), getServiceIdentity(), userId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		return list.get(0);
	}

	/**
	 * Get current user.
	 *
	 * @param fieldSelectionParameters What fields to return.
	 * @return The user.
	 */
	@GET
	@Path("me")
	@Operation(summary = "Get current user; if current user is a sub-user, redirect to /sub-users/me")
	@RequiredRoles(roles = {})
	public Response getCurrentUser(@BeanParam final FieldSelectionParameters fieldSelectionParameters)
			throws SQLException, IllegalAccessException, IOException, URISyntaxException {
		if (getUser().isSubUser()) {
			return Response.temporaryRedirect(buildApiRedirectUrl(SubUsersResource.PATH + "/me")).build();
		}
		return Response.ok(getUser((int) getUser().getMainUserId(), fieldSelectionParameters)).build();
	}

	/**
	 * Partially update a user.
	 *
	 * @param userId The ID of the user to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{userId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a user")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response patchUser(
			@Parameter(required = true) @PathParam("userId") final Integer userId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = User.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException, IOException {
		final List<User> list = getUsersFromDb(getConnection(), getUser(), null, userId, null, null, null, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}

		final Set<String> missingColumns = new HashSet<>();
		final User user = User.patch(list.get(0), patch, missingColumns);
		user.setId(userId);

		if (!missingColumns.contains("pgppublickey")) {
			Pgp.getInstance(Configuration.getConfigService()).validatePortalPGPKey(user);
			missingColumns.remove("emailencryptionkey");
		}

		if (!missingColumns.contains("twofactortype")) {
			if (userId == getUser().getMainUserId() && TwoFactorType.SMS.equals(user.getTwoFactorType())) {
				AuthResource.validateSmsValidationCode(getConnection(), user.getTwoFactorCode());
			}

			missingColumns.remove("twofactorauthentication");
		}

		UserBusiness.updateUser(getConnection(), user, true, getUser(), missingColumns, false, null);

		getConnection().commit();

		final User updatedUser = User.getById(User.class, getConnection(), userId);
		if (updatedUser == null) {
			throw new NotFoundException();
		}
		UserBusiness.sendEventV2(updatedUser, getConnection(), Trigger.USER_MODIFIED, getUser());

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getUsersFromDb(getConnection(), getUser(), null, userId, null, null, fieldSelectionParameters, null, false, true).getRight().get(0)).build();
		}

		return null;
	}

	/**
	 * Partially update current user.
	 *
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("me")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update current user; if current user is a sub-user, redirect to /sub-users/me")
	@RequiredRoles(roles = {})
	public Response patchCurrentUser(
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = User.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException, URISyntaxException, IOException {
		if (getUser().isSubUser()) {
			return Response.temporaryRedirect(buildApiRedirectUrl(SubUsersResource.PATH + "/me")).build();
		}
		return patchUser((int) getUser().getMainUserId(), patch, fieldSelectionParameters, returnResultParameters);
	}

	/**
	 * Change password for current user.
	 *
	 * @param password {@link ChangePasswordRequest} object containing the updated password.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@POST
	@Path("me/change-password")
	@Operation(summary = "Change current users password; if current user is a sub-user, redirect to /sub-users/me/change-password")
	public Response changePasswordCurrentUser(
			@Parameter(required = true) @Valid @NotNull final ChangePasswordRequest password,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, URISyntaxException, IOException, IllegalAccessException {
		if (getUser().isSubUser()) {
			return Response.temporaryRedirect(buildApiRedirectUrl(SubUsersResource.PATH + "/me/change-password")).build();
		}

		final User user = getUser((int) getUser().getMainUserId(), fieldSelectionParameters);

		final HashSet<String> missingColumns = new HashSet<>(Arrays.asList(DbHelper.getFields(User.class, new HashSet<>())));

		user.setClearPassword(password.getPassword());
		user.setPassword(password.getPassword());
		missingColumns.remove("password");

		user.setOldPassword(password.getOldPassword());
		missingColumns.remove("oldpassword");

		UserBusiness.updateUser(getConnection(), user, true, getUser(), missingColumns, false, null);
		getConnection().commit();

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(
					getUsersFromDb(getConnection(), getUser(), null, (int) getUser().getMainUserId(), null, null, fieldSelectionParameters, null, false, true).getRight()
							.get(0)).build();
		}
		return null;
	}

	/**
	 * Change password for a user. Can only be used by an admin user and the endpoint must be configured to be enabled.
	 *
	 * @param request {@link ChangePasswordRequest} object containing the updated password.
	 * @return The response.
	 */
	@POST
	@Path("admin-change-password")
	@Operation(summary = "Change password for a user, only for admins and endpoint is disabled unless configured to be enabled")
	@RequiredRoles(roles = {Role.Administrator})
	@ConfigurableDisabled
	@Hidden
	public Response changePasswordUser(
			@Parameter(required = true) @Valid @NotNull final AdminChangePasswordRequest request)
			throws SQLException {
		DbObject.executeUpdate(getConnection(), "UPDATE tusers SET vcpassword = UPPER(MD5(?)) WHERE vcusername = ?", request.getPassword(), request.getUsername());
		getConnection().commit();
		return null;
	}

	/**
	 * Link a {@link User} to a {@link Tag}.
	 *
	 * @param userId The {@link User} to link.
	 * @param tagId The {@link Tag} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{userId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Create a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response putUserTagLink(@Parameter(required = true) @PathParam("userId") final Integer userId, @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException, URISyntaxException {
		modifyUserTagLinks(TagModifyInfo.builder()
				.resourceId(userId)
				.addedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		final User updatedUser = User.getById(User.class, getConnection(), userId);
		if (updatedUser == null) {
			throw new NotFoundException();
		}
		UserBusiness.sendEventV2(updatedUser, getConnection(), Trigger.USER_MODIFIED, getUser());
		return createCreatedResponse(userId + "/tags/" + tagId).build();
	}

	/**
	 * Delete a link between a {@link User} and a {@link Tag}.
	 *
	 * @param userId The {@link User} to unlink.
	 * @param tagId The {@link Tag} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{userId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response deleteUserTagLink(@Parameter(required = true) @PathParam("userId") final Integer userId,
									  @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException {
		modifyUserTagLinks(TagModifyInfo.builder()
				.resourceId(userId)
				.deletedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		final User updatedUser = User.getById(User.class, getConnection(), userId);
		if (updatedUser == null) {
			throw new NotFoundException();
		}
		UserBusiness.sendEventV2(updatedUser, getConnection(), Trigger.USER_MODIFIED, getUser());
		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link Tag} linked to a {@link User}.
	 *
	 * @param userId The {@link User} to modify
	 * @param tagIds The new set of {@link Tag}
	 * @return The response
	 */
	@PUT
	@Path("{userId: \\d+}/tags")
	@Operation(summary = "Modify linked set of tags")
	@RequiredPermissions(permissions = UserRolePermission.USERS_VIEW_AND_MANAGE)
	public Response modifyTagLinks(@Parameter(required = true) @PathParam("userId") final Integer userId,
								   @RequestBody(required = true,
										   content = @Content(array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds)
			throws SQLException {
		modifyUserTagLinks(TagModifyInfo.builder()
				.resourceId(userId)
				.resultTags(TagsResource.extractTagIds(tagIds)).build());
		getConnection().commit();

		final User updatedUser = User.getById(User.class, getConnection(), userId);
		if (updatedUser == null) {
			throw new NotFoundException();
		}
		UserBusiness.sendEventV2(updatedUser, getConnection(), Trigger.USER_MODIFIED, getUser());
		return null;
	}

	/**
	 * Validate user tags and update the links
	 *
	 * @param tagModifyInfo info about added/deleted/modified tags
	 * @return user
	 */
	private User modifyUserTagLinks(final TagModifyInfo tagModifyInfo) throws SQLException {
		final List<User> users
				= getUsersFromDb(getConnection(), getUser(), getServiceIdentity(), tagModifyInfo.getResourceId(), null, null, null, null, false, true).getRight();

		if (users.isEmpty()) {
			throw new NotFoundException();
		}

		modifyTagLinks(tagModifyInfo);
		return users.get(0);
	}

	/**
	 * Get a list of users from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param service Service to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<User>> getUsersFromDb(final Connection conn, final BaseLoggedOnUser user, final ServiceIdentity service, final Integer id,
														   final PaginationParameters paginationParameters,
														   final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
														   final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects)
			throws SQLException {
		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();
		final DbObjectSendConfiguration config;

		if (user != null) {
			final boolean isAdmin = user.hasRoles(Role.Administrator, Role.LicenseManager, Role.Support);
			if (!isAdmin) {
				StringUtils.concatenateFilters(where, "xid = ?");
				params.add(user.getMainUserId());
			}
			config = new DbObjectSendConfiguration(Access.createAccess(AccessType.ADMIN, "mainuser," + (isAdmin ? "admin" : "user")));
		}
		else if (service != null && service.getService() == Service.OUTSCAN_REMOTE_SUPPORT) {
			config = new DbObjectSendConfiguration(Access.createAccess(AccessType.ADMIN, "remotesupport"));
		}
		else if (service != null && service.getService() == Service.SWAT_API) {
			config = new DbObjectSendConfiguration(Access.createAccess(AccessType.ADMIN, "mainuser,admin"));
			StringUtils.concatenateFilters(where, "web");
		}
		else {
			throw new ForbiddenException();
		}

		StringUtils.concatenateFilters(where, "xid > ?");
		params.add(Configuration.isOosEnabled() ? 99 : (Configuration.isHiabEnabled() ? 10 : 0));

		return User.getObjects(User.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, where.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
