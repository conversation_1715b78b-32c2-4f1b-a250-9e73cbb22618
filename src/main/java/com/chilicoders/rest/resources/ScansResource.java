package com.chilicoders.rest.resources;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.ws.rs.core.StreamingOutput;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.json.JSONObject;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.customer.api.CustomerInterface;
import com.chilicoders.core.scandata.api.IssueTransformationProcessor;
import com.chilicoders.core.scandata.api.model.DataType;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.core.storage.api.StorageService;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.XmlAble.ObjectsResponse;
import com.chilicoders.event.rest.events.ScanEvent;
import com.chilicoders.model.DownloadEntryType;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.annotations.RequiredRoles;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.BlueprintCacheEntry;
import com.chilicoders.rest.models.Reason;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.ScanData;
import com.chilicoders.rest.models.ScanLog;
import com.chilicoders.rest.models.ScanLogMetadata;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.scan.BlueprintDownloader;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.SendBluePrint;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import software.amazon.awssdk.services.s3.model.S3Exception;

@Tag(name = ScansResource.PATH)
@Path(ScansResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ScansResource extends BaseResource {
	public static final String PATH = "scans";

	/**
	 * Get header with total count of {@link ScanLog}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getScanHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getScanLogsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false, false).getCount();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of scans.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The scans.
	 */
	@GET
	@Operation(summary = "Get a list of scans",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = ScanLog.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getScanList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(getScanLogsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters,
				filteringParameters, true, true, true));
	}

	/**
	 * Get a scan.
	 *
	 * @param scanId The ID of the scan to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The scan.
	 */
	@GET
	@Path("{scanId: \\d+}")
	@Operation(summary = "Get a scan")
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public ScanLog getScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId, @BeanParam final FieldSelectionParameters fieldSelectionParameters)
			throws SQLException {
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, fieldSelectionParameters, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		return scanLogs.get(0);
	}

	/**
	 * Get metadata for a scan.
	 *
	 * @param scanId The ID of the scan to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The scan metadata.
	 */
	@GET
	@Path("{scanId: \\d+}/metadata")
	@Operation(summary = "Get metadata for a scan")
	@RequiredRoles(roles = {Role.Administrator, Role.Support, Role.LicenseManager})
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public ScanLogMetadata getScanMetadata(@Parameter(required = true) @PathParam("scanId") final Integer scanId,
										   @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final ScanLogMetadata data = ScanLogMetadata.get(ScanLogMetadata.class, getConnection(), DbHelper.getSelect(ScanLogMetadata.class, Access.ADMIN, "id = ?"), scanId);
		if (data == null) {
			throw new NotFoundException();
		}
		return data;
	}

	/**
	 * Creates a {@link ScanLog}.
	 *
	 * @param scan The {@linkplain ScanLog} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a scan")
	@Valid
	@RequiredRoles(roles = {Role.Administrator})
	@RequiredPermissions(permissions = UserRolePermission.SCANS_VIEW_AND_MANAGE)
	public Response postScan(@Parameter(required = true) @Valid @NotNull final ScanLog scan,
							 @BeanParam final FieldSelectionParameters fieldSelectionParameters, @BeanParam final ReturnResultParameters returnResultParameters)
			throws URISyntaxException, SQLException {
		if (getUser() != null && getUser().hasLimitResources(ResourceType.SCANCONFIGURATION)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		scan.setCustomerId(getUser());
		scan.setCreatedById(getUpdaterId());

		final int scanId = (int) scan.save(getConnection());
		getConnection().commit();

		final ResponseBuilder response = createCreatedResponse(scanId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, fieldSelectionParameters, null, false, true, false).getObjects().get(0));
		}
		return response.build();
	}

	/**
	 * Stop a scan.
	 *
	 * @param scanId The scan to stop.
	 * @return The response.
	 */
	@POST
	@Path("{scanId: \\d+}/stop")
	@Operation(summary = "Stop a scan")
	@RequiredPermissions(permissions = UserRolePermission.SCANS_VIEW_AND_MANAGE)
	public Response stopScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId) throws SQLException {
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, null, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanLog scanLog = scanLogs.get(0);

		if (scanLog.getTemplate() == ScanTemplate.SCALE) {
			updateScanStatusToStop(scanLog, ScanServiceType.AppsecScale);
		}
		else if (scanLog.getTemplate() == ScanTemplate.DOCKER_SCAN) {
			updateScanStatusToStop(scanLog, ScanServiceType.DOCKER_SCAN);
		}
		else if (scanLog.getTemplate() == ScanTemplate.DOCKER_DISCOVERY) {
			updateScanStatusToStop(scanLog, ScanServiceType.DOCKER_DISCOVERY);
		}
		else if (scanLog.getTemplate() == ScanTemplate.CLOUDSEC) {
			updateScanStatusToStop(scanLog, ScanServiceType.CLOUDSEC);
		}
		else if (scanLog.getTemplate() == ScanTemplate.NETWORK_SCAN) {
			updateScanStatusToStop(scanLog, ScanServiceType.NETWORK_SCAN);
		}
		else if (scanLog.getTemplate() == ScanTemplate.NETWORK_DISCOVERY) {
			updateScanStatusToStop(scanLog, ScanServiceType.NETWORK_DISCOVERY);
		}
		else if (scanLog.getTemplate() == ScanTemplate.CLOUD_DISCOVERY) {
			updateScanStatusToStop(scanLog, ScanServiceType.CLOUD_DISCOVERY);
		}
		else if (scanLog.getTemplate() == ScanTemplate.WORKFLOW) {
			final String[] workflowScanServiceTypes = new String[] {
					ScanServiceType.AppsecScale.toString(),
					ScanServiceType.CLOUDSEC.toString(),
					ScanServiceType.NETWORK_SCAN.toString(),
					ScanServiceType.NETWORK_DISCOVERY.toString(),
					ScanServiceType.CLOUD_DISCOVERY.toString(),
					ScanServiceType.DOCKER_DISCOVERY.toString(),
					ScanServiceType.DOCKER_SCAN.toString()
			};

			DbObject.executeUpdate(getConnection(),
					"UPDATE tscanstatuss SET vcstatus = ?, bstop = 1, bsync = 1 WHERE xipxid IN (SELECT id FROM scanlogs "
							+ "WHERE customerid = ? AND workflowid = ? AND parentid = ? AND NOT status = ANY(?::scanstatus[])) AND xuserxid = ? AND vcservice = ANY(?) AND isstopped = 0",
					ScanStatuses.Stopping.toString(), getUser().getCustomerId(), scanLog.getWorkflowId(), scanLog.getId(),
					new ScanLogStatus[] {ScanLogStatus.FINISHED, ScanLogStatus.ISSUES, ScanLogStatus.FAILED, ScanLogStatus.STOPPED},
					getUser().getMainUserId(), workflowScanServiceTypes);

			DbObject.executeUpdate(getConnection(), "UPDATE scanlogs SET status = ? WHERE id = ?", ScanLogStatus.STOPPING, scanLog.getId());

			DbObject.executeUpdate(getConnection(), "DELETE FROM scanqueue WHERE customerid = ? AND workflowid = ? AND parentid = ?",
					getUser().getCustomerId(), scanLog.getWorkflowId(), scanLog.getId());
		}
		else {
			throw new InputValidationException("Stopping " + scanLog.getTemplate().name() + " scan currently not supported.");
		}

		getConnection().commit();

		final ScanEvent scanEvent = new ScanEvent(scanId, scanLog.getCustomerId(), null, scanLog.getScheduleId(), scanLog.getScanConfigurationId(), scanLog.getWorkflowId());
		ServiceProvider.getEventApiV2(getConnection()).handleEvent(scanEvent.stopped(), getUser());

		return null;
	}

	/**
	 * Get the crawled URLs for a scan.
	 *
	 * @param scanId The ID of the scan to get crawled URLs for.
	 * @return The crawled URLs.
	 */
	@GET
	@Path("{scanId: \\d+}/crawled-urls")
	@Produces(MediaType.TEXT_PLAIN)
	@Operation(summary = "Get the crawled URLs for a scan")
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getCrawledUrlsForScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId) throws SQLException {
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, null, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		final List<ScanData> data =
				ScanData.fetchObjects(ScanData.class, getConnection(), Access.ADMIN, "customerid = ? AND scanid = ? AND type = ?", getUser().getCustomerId(), scanId,
						DataType.CRAWLEDURLS);
		return Response.ok(data.isEmpty() ? "" : StringUtils.setEmpty(data.get(0).getData(), ""))
				.header("Content-Disposition", "attachment; filename=\"crawled-urls.txt\"")
				.build();
	}

	/**
	 * Get the discovery results for a scan.
	 *
	 * @param scanId The ID of the scan to get discovery results for.
	 * @return The discovery results.
	 */
	@GET
	@Path("{scanId: \\d+}/discovery")
	@Produces(MediaType.TEXT_PLAIN)
	@Operation(summary = "Get the discovery results for a scan")
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getDiscoveryForScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId) throws SQLException {
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, null, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanLog scanLog = scanLogs.get(0);

		final ResponseBuilder response = Response.ok();
		response.type(MediaType.APPLICATION_JSON);
		response.header("Content-Disposition", "attachment; filename=\"discovery.json\"");
		response.entity(new StreamingOutput() {
			@Override
			public void write(final OutputStream output) throws IOException, WebApplicationException {
				if (Configuration.isHiabEnabled()) {
					final File file = new File(Configuration.getProperty(ConfigurationKey.scan_cache) + scanLog.getSchema() + "/discovery.json");
					if (file.exists()) {
						try (final FileInputStream is = new FileInputStream(file)) {
							IOUtils.copy(is, output);
						}
					}
				}
				else {
					try {
						final ResourceIdentifier resourceId = TenantResourceIdentifier.builder()
								.domain(Domain.SCANS)
								.tenantUuid(getUser().getCustomerUuid())
								.key(scanLog.getJobId() + "/discovery.json")
								.build();
						final StorageService storageService = ServiceProvider.getStorageService();
						if (storageService.doesResourceExist(resourceId)) {
							storageService.downloadAndDecryptResource(resourceId, output);
						}
					}
					catch (final S3Exception ex) {
						if (ex.statusCode() == HttpStatus.SC_NOT_FOUND) {
							throw new NotFoundException();
						}
						throw ex;
					}
				}
			}
		});
		return response.build();
	}

	/**
	 * Get the issues for a scan.
	 *
	 * @param scanId The ID of the scan to get issues for.
	 * @return The issues.
	 */
	@GET
	@Path("{scanId: \\d+}/issues")
	@Operation(summary = "Get the issues for a scan",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getIssuesForScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId) throws SQLException {
		final List<ScanData> dataList =
				ScanData.fetchObjects(ScanData.class, getConnection(), Access.ADMIN, "customerid = ? AND scanid = ? AND type = ?", getUser().getCustomerId(), scanId,
						DataType.ISSUES);
		final List<String> issues = new ArrayList<>();
		for (final ScanData data : dataList) {
			final String originalData = StringUtils.setEmpty(data.getData(), "");
			final String transformedData = IssueTransformationProcessor.processTransformation(originalData);
			issues.add(transformedData);
		}

		return responseWithCount(new GenericType<List<String>>() {
		}, Pair.of((long) issues.size(), issues));
	}

	/**
	 * Get the log for a scan.
	 *
	 * @param scanId The ID of the scan to get log for.
	 * @return The log.
	 */
	@GET
	@Path("{scanId: \\d+}/log")
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "Get the log for a scan")
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response getLogForScan(@Parameter(required = true) @PathParam("scanId") final Integer scanId) throws SQLException {
		final List<ScanData> data =
				ScanData.fetchObjects(ScanData.class, getConnection(), Access.ADMIN, "customerid = ? AND scanid = ? AND type = ?", getUser().getCustomerId(), scanId,
						DataType.LOG);
		return Response.ok(data.isEmpty() ? "" : StringUtils.setEmpty(data.get(0).getData(), "")).header("Content-Disposition", "attachment; filename=\"log.json\"").build();
	}

	/**
	 * Request a blueprint.
	 *
	 * @param scanId The ID of the scan to get blueprint for.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return A key used for tracking the progress of generating the blueprint.
	 */
	@POST
	@Path("{scanId: \\d+}/blueprints")
	@Operation(summary = "Request a blueprint")
	@Valid
	@RequiredPermissions(permissions = {UserRolePermission.SCANS_VIEW, UserRolePermission.SCANS_VIEW_AND_MANAGE})
	public Response requestBlueprint(@Parameter(required = true) @PathParam("scanId") final Integer scanId, @BeanParam final ReturnResultParameters returnResultParameters)
			throws SQLException {
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, null, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanLog scanLog = scanLogs.get(0);
		if (!scanLog.isBlueprintAvailable()) {
			throw new NotFoundException();
		}

		if (isDuplicateRequest(scanLog.getId())) {
			throw new InputValidationException("_BLUEPRINT_ALREADY_DOWNLOADING");
		}

		final String key = UUID.randomUUID().toString();
		SendBluePrint.saveQueuedBlueprintEntry(getConnection(), getUser(), key, scanLog);

		final JSONObject json = new JSONObject();
		json.put("key", key);

		return Response.accepted(returnResultParameters.shouldReturnResult() ? json.toString() : null).build();
	}

	/**
	 * Request a blueprint, used by support and consultancy users.
	 *
	 * @param scanId The ID of the scan to get blueprint for.
	 * @param reason The reason for using the support endpoint to download a blueprint.
	 * @return A response where the data is stramed to.
	 */
	@POST
	@Path("{scanId: \\d+}/support-blueprint")
	@Operation(summary = "Request a blueprint")
	@Hidden
	@Valid
	public Response downloadSupportBlueprint(@Parameter(required = true) @PathParam("scanId") final Integer scanId, @Parameter(required = true) @Valid @NotNull final Reason reason)
			throws SQLException {
		if (!getUser().hasRoles(Role.Support) && !getUser().isConsultancyMode()) {
			throw new ForbiddenException();
		}
		final List<ScanLog> scanLogs = getScanLogsFromDb(getConnection(), getUser(), scanId, null, null, null, null, false, true, false).getObjects();
		if (scanLogs.isEmpty()) {
			throw new NotFoundException();
		}
		final ScanLog scanLog = scanLogs.get(0);
		if ((scanLog.getTemplate() != ScanTemplate.SCALE && scanLog.getTemplate() != ScanTemplate.NETWORK_SCAN) || scanLog.getScannerId() != Scanner.LOCAL_SCANNER || scanLog.getStarted().isBefore(Instant.now().minus(30, ChronoUnit.DAYS))) {
			throw new NotFoundException();
		}

		final Long scanStatusId = DbObject.getLong(getConnection(), "SELECT scanstatusid FROM scanstatus_schema WHERE schema = ? LIMIT 1", scanLog.getSchema());
		if (scanStatusId == null) {
			throw new NotFoundException();
		}

		final CustomerInterface customer = ServiceProvider.getCustomerService(getConnection()).getCustomer(scanLog.getCustomerId());
		if (customer == null) {
			throw new NotFoundException();
		}

		final ResponseBuilder response = Response.ok();
		response.type("application/gzip");
		response.header("Content-Disposition", "attachment; filename=\"" + scanLog.getSchema() + ".tar.gz\"");
		response.header("X-Accel-Buffering", "no");
		response.entity((StreamingOutput) output -> BlueprintDownloader.downloadBlueprint(customer.getUuid(), getUser(), reason.getReason(), scanStatusId, output));
		return response.build();
	}

	/**
	 * @param scanId {@link Integer} The scan id.
	 * @return {@code true} if the request is duplicate.
	 * @throws SQLException In case of error.
	 */
	private boolean isDuplicateRequest(final Integer scanId) throws SQLException {
		return BlueprintCacheEntry.get(BlueprintCacheEntry.class, getConnection(),
				DbHelper.getSelect(BlueprintCacheEntry.class, Access.ADMIN, "checksum = ? AND userid = ? AND subuserid = ? AND type = ?"),
				StringUtils.md5JsonSum(scanId), getUser().getMainUserId(), getUser().getSubUserId(), DownloadEntryType.BLUEPRINT) != null;
	}

	/**
	 * Get a list of scan logs from DB.
	 *
	 * @param conn The database connection.
	 * @param user The user to get the scan logs for.
	 * @param id The ID to get a particular scan log.
	 * @param paginationParameters The pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters The filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @param streamObjects Whether to get the objects as stream.
	 * @return List, with count if applicable.
	 */
	protected static ObjectsResponse<ScanLog> getScanLogsFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id,
																final PaginationParameters paginationParameters, final SortingParameters sortingParameters,
																final FieldSelectionParameters fieldSelectionParameters, final FilteringParameters filteringParameters,
																final boolean getCount, final boolean includeObjects, final boolean streamObjects) throws SQLException {
		final StringBuilder filter = new StringBuilder("deleted IS NULL");
		final List<Object> params = new ArrayList<>();

		if (user != null) {
			StringUtils.concatenateFilters(filter, "customerid = ?");
			params.add(user.getCustomerId());

			if (user.hasLimitResources(ResourceType.SCANCONFIGURATION)) {
				((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.SCANCONFIGURATION);
			}
		}
		else {
			throw new ForbiddenException();
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setCustomSorting(new String[][] {{"assetName", "human_sort(assetName)"}});

		if (!Configuration.isHiabEnabled()) {
			config.addColumnOverride("scannerName", "(CASE WHEN COALESCE(scannerid, 0) = 0 THEN 'Outscan' ELSE scannername END)");
		}

		return ScanLog.getObjects(ScanLog.class, conn, user, config, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, streamObjects, filter.toString(), params);
	}

	/**
	 * Updates scan status to stop.
	 *
	 * @param scanLog the scan log object.
	 * @param type the {@link ScanServiceType}
	 */
	private void updateScanStatusToStop(final ScanLog scanLog, final ScanServiceType type) throws SQLException {
		DbObject.executeUpdate(getConnection(),
				"UPDATE tscanstatuss SET vcstatus = ?, bstop = 1, bsync = 1 WHERE xipxid = ? AND xuserxid = ? AND vcservice = ? AND isstopped = 0",
				ScanStatuses.Stopping.toString(), scanLog.getId(), getUser().getMainUserId(), type.toString());
	}

	/**
	 * Updates multiple scan status to stop.
	 *
	 * @param connection DB connection
	 * @param user The user to get the scan logs for.
	 * @param customerId The id of customer.
	 * @param userId The id of customer (optional).
	 * @param templates List scan template that will be stopped.
	 * @param scanTypes List scan type that will be stopped.
	 * @return List of scan logs affected.
	 */
	protected static List<ScanLog> updateScanStatusesToStop(final Connection connection, final BaseLoggedOnUser user, final Integer userId, final Integer customerId, final ScanTemplate[] templates, final String[] scanTypes) throws SQLException {
		final ScanLogStatus[] finishedStatuses = new ScanLogStatus[]{ScanLogStatus.FINISHED, ScanLogStatus.ISSUES, ScanLogStatus.FAILED, ScanLogStatus.STOPPED};
		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);

		final StringBuilder filter = new StringBuilder("deleted IS NULL");
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(filter, "customerid = ?");
		params.add(customerId);

		StringUtils.concatenateFilters(filter, "status != ALL(?::scanstatus[])");
		params.add(finishedStatuses);

		if (templates.length > 0) {
			StringUtils.concatenateFilters(filter, "template = ANY(?::scantemplate[])");
			params.add(templates);
		}

		final List<ScanLog> scanLogs = ScanLog.getObjects(ScanLog.class, connection, user, config, null, null, null, null,
				null, false, true, false, filter.toString(), params).getObjects();

		final Integer[] scanLogIds = scanLogs.stream().map(ScanLog::getId).toArray(Integer[]::new);

		String updateQuery = "UPDATE tscanstatuss SET vcstatus = ?, bstop = 1, bsync = 1 WHERE xipxid = ANY(?) AND isstopped = 0 AND xuserxid = ?";
		final List<Object> updateParams = new ArrayList<>();
		updateParams.add(ScanStatuses.Stopping.toString());
		updateParams.add(scanLogIds);
		updateParams.add(userId);

		if (scanTypes.length > 0) {
			updateQuery += " AND vcservice = ANY(?)";
			updateParams.add(scanTypes);
		}

		DbObject.executeUpdate(connection, updateQuery, updateParams);
		return scanLogs;
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
