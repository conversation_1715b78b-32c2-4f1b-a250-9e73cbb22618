package com.chilicoders.rest.resources;

import static com.chilicoders.rest.annotations.RequestAuthentication.ServiceAccounts.SWAT;

import java.sql.SQLException;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.chilicoders.core.libellum.ServiceIdentity.Service;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.HiabStat;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.rest.annotations.RequestAuthentication;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.models.CertificateCreationRequest;
import com.chilicoders.rest.models.CertificateResponse;
import com.chilicoders.util.LibellumUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = CertificateResource.PATH)
@Path(CertificateResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class CertificateResource extends BaseResource {
	public static final String PATH = "certificate";

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	/**
	 * Renew a certificate.
	 *
	 * @param renewCertificate The {@linkplain CertificateCreationRequest} to create.
	 * @return The response.
	 */
	@POST
	@Path("renew")
	@Operation(summary = "Renews certificate")
	@Valid
	@RequestAuthentication(services = {Service.HIAB_SCHEDULER, Service.HIAB_SCANNER}, serviceAccounts = {SWAT})
	public Response renewCertificate(@Parameter(required = true) @Valid @NotNull final CertificateCreationRequest renewCertificate) throws SQLException {
		return Response.ok(getCertificate(renewCertificate)).build();
	}

	/**
	 * Get certificate using {@link LibellumUtils}
	 *
	 * @param certificateRequest the certificate creation request
	 * @return {@link CertificateResponse} containing certificate.
	 */
	private CertificateResponse getCertificate(final CertificateCreationRequest certificateRequest) throws SQLException {
		final HiabStat hiabStat = HiabStat.getByKey(getConnection(), certificateRequest.getKey(), true);
		if (hiabStat == null) {
			throw new InputValidationException("_KEY_INVALID");
		}
		if (hiabStat.isRevoked()) {
			throw new ForbiddenException("_APPLIANCE_IS_REVOKED");
		}
		final BaseLoggedOnUser user = getUser() != null ? getUser() : DbObject.getById(LoggedOnUser.class, getConnection(), hiabStat.getUserId());
		final CertificateResponse response = new CertificateResponse();
		final String certificateAsString = LibellumUtils.getCertificate(certificateRequest.getCertificateSigningRequest(), user.getCustomerUuid());
		response.setCertificate(certificateAsString);

		getConnection().commit();
		return response;
	}
}
