package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.ManagedReportBusiness;
import com.chilicoders.bl.ReportScheduleBusiness;
import com.chilicoders.bl.UserBusiness;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.reporting.api.model.ReportTemplateScanType;
import com.chilicoders.core.reporting.api.model.ReportType;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.ReportSchedule;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.InternalServerErrorException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.Pgp;
import com.chilicoders.util.StringUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = ReportSchedulesResource.PATH)
@Path(ReportSchedulesResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ReportSchedulesResource extends BaseResource {
	public static final String PATH = "report-schedules";

	/**
	 * Get header with total count of {@link ReportSchedule}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count (Deprecated: 2019-12-11)", deprecated = true, responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getReportScheduleHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getReportSchedulesFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of report schedules.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The report schedules.
	 */
	@GET
	@Operation(summary = "Get a list of report schedules (Deprecated: 2019-12-11)", deprecated = true, responses = @ApiResponse(description = DEFAULT_RESPONSE,
			content = @Content(array = @ArraySchema(schema = @Schema(implementation = ReportSchedule.class))),
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	public Response getReportScheduleList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<ReportSchedule>>() {
		}, getReportSchedulesFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a report schedule.
	 *
	 * @param reportScheduleId The ID of the report schedule to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The report schedule.
	 */
	@GET
	@Path("{reportScheduleId: \\d+}")
	@Operation(summary = "Get a report schedule (Deprecated: 2019-12-11)", deprecated = true)
	public ReportSchedule getReportSchedule(@Parameter(required = true) @PathParam("reportScheduleId") final long reportScheduleId,
											@BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException {
		final List<ReportSchedule> list =
				getReportSchedulesFromDb(getConnection(), getUser(), reportScheduleId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}
		return list.get(0);
	}

	/**
	 * Create a report schedule.
	 *
	 * @param schedule The report schedule to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a new report schedule (Deprecated: 2019-12-11)", deprecated = true)
	@Valid
	public Response postReportSchedule(
			@Parameter(required = true) @Valid @NotNull final ReportSchedule schedule,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws URISyntaxException, SQLException, JAXBException {
		final long id = save(schedule);

		final ResponseBuilder response = createCreatedResponse(id);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(ReportSchedule.getById(ReportSchedule.class, getConnection(), getUser(), null, fieldSelectionParameters, id, null));
		}
		return response.build();
	}

	/**
	 * Partially update a report schedule.
	 *
	 * @param reportScheduleId The ID of the report schedule to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{reportScheduleId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a report schedule (Deprecated: 2019-12-11)", deprecated = true)
	public Response patchReportSchedule(
			@Parameter(required = true) @PathParam("reportScheduleId") final long reportScheduleId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = ReportSchedule.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<ReportSchedule> list = getReportSchedulesFromDb(getConnection(), getUser(), reportScheduleId, null, null, null, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}

		final ReportSchedule newObject = ReportSchedule.patch(list.get(0), patch);
		newObject.setId(reportScheduleId);
		final long objectId = save(newObject);

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getReportSchedulesFromDb(getConnection(), getUser(), objectId, null, null, null, null, false, true).getRight().get(0)).build();
		}

		return null;
	}

	/**
	 * Execute schedule now.
	 *
	 * @param reportScheduleId The schedule to execute.
	 * @return The response.
	 */
	@POST
	@Path("{reportScheduleId: \\d+}/send")
	@Operation(summary = "Start scanning now (Deprecated: 2019-12-11)", deprecated = true)
	public Response sendReportSchedule(@Parameter(required = true) @PathParam("reportScheduleId") final long reportScheduleId) throws SQLException {
		final List<ReportSchedule> list = getReportSchedulesFromDb(getConnection(), getUser(), reportScheduleId, null, null, null, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}

		if (!ReportScheduleBusiness.checkActionAllowed(list.get(0).getScanType(), getUser())) {
			throw new ForbiddenException();
		}

		if (!ReportScheduleBusiness.sendReportSchedule(getUser(), reportScheduleId)) {
			throw new InternalServerErrorException();
		}

		return null;
	}

	/**
	 * Delete a report schedule.
	 *
	 * @param reportScheduleId The ID of the report schedule to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{reportScheduleId: \\d+}")
	@Operation(summary = "Delete a report schedule (Deprecated: 2019-12-11)", deprecated = true)
	public Response deleteReportSchedule(@Parameter(required = true) @PathParam("reportScheduleId") final long reportScheduleId) throws SQLException {
		final List<ReportSchedule> list = getReportSchedulesFromDb(getConnection(), getUser(), reportScheduleId, null, null, null, null, false, true).getRight();
		if (list.isEmpty()) {
			throw new NotFoundException();
		}

		if (DbObject.executeUpdate(getConnection(), "DELETE FROM treportschedules WHERE xid=?", reportScheduleId) == 1) {
			audit(reportScheduleId, AppName.REPORTSCHEDULE, AuditMode.DELETE, list.get(0).getName());
		}
		getConnection().commit();

		return null;
	}

	/**
	 * Get a list of report schedules from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get report schedules for.
	 * @param id Optional ID to get a particular report schedule.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List of report schedules, with count if applicable.
	 */
	private static Pair<Long, List<ReportSchedule>> getReportSchedulesFromDb(final Connection conn, final BaseLoggedOnUser user, final Long id,
																			 final PaginationParameters paginationParameters, final SortingParameters sortingParameters,
																			 final FieldSelectionParameters fieldSelectionParameters,
																			 final FilteringParameters filteringParameters, final boolean getCount,
																			 final boolean includeObjects) throws SQLException {
		final StringBuilder filter = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		if (!user.isSuperUser() && user.isSubUser()) {
			StringUtils.concatenateFilters(filter, "xsubuserxid = ?");
			params.add(user.getSubUserId());
		}

		final StringBuilder typeList = new StringBuilder();
		/* Currently we only support SWAT report schedules in the REST API.
		typeList.append(user.allowReporting(true) ? (typeList.length() == 0 ? "" : ", ") + ScanType.Normal.getNumericRepresentation() : "");
		typeList.append(user.allowPciReporting(true) ? (typeList.length() == 0 ? "" : ", ") + ScanType.PCI.getNumericRepresentation() : "");
		typeList.append(user.allowWasReporting(true) ? (typeList.length() == 0 ? "" : ", ") + ScanType.WAS.getNumericRepresentation() : "");
		typeList.append(user.allowComplianceAccess(true) ? (typeList.length() == 0 ? "" : ", ") + ScanType.Compliance.getNumericRepresentation() : "");
		*/
		typeList.append(user.allowSwatAccess(true) ? (typeList.length() == 0 ? "" : ", ") + ReportTemplateScanType.SWAT.getNumericRepresentation() : "");
		StringUtils.concatenateFilters(filter, StringUtils.isEmpty(typeList.toString()) ? "FALSE" : "scantype IN (" + typeList.toString() + ")");

		return ReportSchedule.getObjects(ReportSchedule.class, conn, user, null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters,
				id != null ? id.toString() : null, getCount, includeObjects, filter.toString(),
				params);
	}

	/**
	 * Save a new or updated report schedule in DB.
	 *
	 * @param schedule The schedule to save.
	 * @return The ID of the saved object.
	 */
	private long save(final ReportSchedule schedule) throws SQLException {
		if (!ReportScheduleBusiness.checkActionAllowed(schedule.getScanType(), getUser())) {
			throw new ForbiddenException();
		}

		schedule.setUserId(getUser().getMainUserId());
		if (getUser().isSubUser() && !(getUser().isSuperUser() || schedule.getId() <= 0)) {
			schedule.setSubUserId(getUser().getSubUserId());
		}

		if (schedule.getScanType() != ReportTemplateScanType.SWAT.getNumericRepresentation()) {
			throw new InputValidationException();
		}
		else if (schedule.getReportType() != ReportType.Technical.getNumber()) {
			throw new InputValidationException();
		}

		if (!StringUtils.isEmpty(schedule.getEncryptionKey())) {
			final String key = UserBusiness.getEncryptionKey(getConnection(), getUser().getMainUserId(), schedule.getEncryptionKey());
			if (!Pgp.getInstance(Configuration.getConfigService()).isValidKey(key) && !Pgp.UNENCRYPTED_KEY.equals(schedule.getEncryptionKey())) {
				throw new InputValidationException("_INVALID_PGP_KEY");
			}
		}

		if (((!Configuration.isHiabEnabled() && schedule.getRecipientType() != ReportScheduleBusiness.RECIPIENTTYPE_MANAGEDREPORTS)
				|| schedule.getRecipientType() == ReportScheduleBusiness.RECIPIENTTYPE_EMAIL) && schedule.getRecipient().length == 0 &&
				StringUtils.isEmpty(schedule.getRecipientEmail())) {
			throw new InputValidationException("Recipient is missing");
		}

		if (schedule.getRecipientType() == ReportScheduleBusiness.RECIPIENTTYPE_MANAGEDREPORTS) {
			if (StringUtils.isEmpty(schedule.getManagedReportTitle())
					|| StringUtils.isEmpty(schedule.getManagedReportToken())
					|| schedule.getManagedReportToken().length() != 64) {
				throw new InputValidationException();
			}
			if (!Configuration.isHiabEnabled()) {
				if (schedule.getManagedReportGroup() > 0
						&& DbObject.executeCountQuery(getConnection(), "SELECT xid FROM tmanagedreportgroups WHERE xid = ? AND xuserxid = ?", schedule.getManagedReportGroup(),
						schedule.getUserId()) < 1) {
					throw new InputValidationException();
				}
				final ManagedReportBusiness mrb = new ManagedReportBusiness();
				if (!mrb.checkTokenAccess(getConnection(), schedule.getManagedReportToken(), schedule.getUserId(), false)) {
					throw new ForbiddenException();
				}
			}
		}

		if (schedule.getRecipientType() == ReportScheduleBusiness.RECIPIENTTYPE_EMAIL) {
			final Long[] xids = schedule.getRecipient();
			if (xids.length == 1 && xids[0] == -1) {
				schedule.setRecipient(new Long[0]);
			}
		}

		final long id = schedule.save(getConnection());
		audit(id, AppName.REPORTSCHEDULE, schedule.wasUpdate() ? AuditMode.UPDATE : AuditMode.ADD);
		getConnection().commit();

		return id;
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}
}
