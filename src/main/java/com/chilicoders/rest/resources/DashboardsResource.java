package com.chilicoders.rest.resources;

import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.HEAD;
import javax.ws.rs.PATCH;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.model.UserRolePermission;
import com.chilicoders.rest.O24MediaType;
import com.chilicoders.rest.annotations.RequiredPermissions;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.Dashboard;
import com.chilicoders.rest.models.ResourceType;
import com.chilicoders.rest.models.Tag;
import com.chilicoders.rest.parameters.FieldSelectionParameters;
import com.chilicoders.rest.parameters.FilteringParameters;
import com.chilicoders.rest.parameters.PaginationParameters;
import com.chilicoders.rest.parameters.ReturnResultParameters;
import com.chilicoders.rest.parameters.SortingParameters;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

@io.swagger.v3.oas.annotations.tags.Tag(name = DashboardsResource.PATH)
@Path(DashboardsResource.PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class DashboardsResource extends TaggableResource {
	public static final String PATH = "dashboards";

	private static final Pair<String, String> TAG_LINK_TABLE = ImmutablePair.of("tag_dashboard", "dashboardid");

	/**
	 * Get header with total count of {@link Dashboard}s.
	 *
	 * @param filteringParameters Filtering parameters.
	 * @return total count in the header.
	 */
	@HEAD
	@Operation(summary = "Get header with count", responses = @ApiResponse(description = DEFAULT_RESPONSE,
			headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW, UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Response getDashboardHeader(@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		final Long count = getDashboardsFromDb(getConnection(), getUser(), null, null, null, null, filteringParameters, true, false).getLeft();
		return Response.ok().header("Count", count).build();
	}

	/**
	 * Get a list of {@link Dashboard}s.
	 *
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @return The {@link Dashboard}s.
	 */
	@GET
	@Operation(summary = "Get a list of dashboards",
			responses = @ApiResponse(description = DEFAULT_RESPONSE, content = @Content(array = @ArraySchema(schema = @Schema(implementation = Dashboard.class))),
					headers = @Header(name = "Count", description = HeaderDescriptions.COUNT, schema = @Schema(type = "integer"))))
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW, UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Response getDashboardList(
			@BeanParam final PaginationParameters paginationParameters,
			@BeanParam final SortingParameters sortingParameters,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final FilteringParameters filteringParameters) throws SQLException {
		return responseWithCount(new GenericType<List<Dashboard>>() {
		}, getDashboardsFromDb(getConnection(), getUser(), null, paginationParameters, sortingParameters, fieldSelectionParameters, filteringParameters, true, true));
	}

	/**
	 * Get a {@link Dashboard}.
	 *
	 * @param dashboardId The ID of the {@link Dashboard} to get.
	 * @param fieldSelectionParameters What fields to return.
	 * @return The {@link Dashboard}.
	 */
	@GET
	@Path("{dashboardId: \\d+}")
	@Operation(summary = "Get a dashboard")
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW, UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Dashboard getDashboard(@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId,
								  @BeanParam final FieldSelectionParameters fieldSelectionParameters) throws SQLException, IllegalAccessException {
		final List<Dashboard> dashboards = getDashboardsFromDb(getConnection(), getUser(), dashboardId, null, null, fieldSelectionParameters, null, false, true).getRight();
		if (dashboards.isEmpty()) {
			throw new NotFoundException();
		}
		return dashboards.get(0);
	}

	/**
	 * Create a {@link Dashboard}.
	 *
	 * @param dashboard The {@link Dashboard} to create.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the created object(s) or not.
	 * @return The response.
	 */
	@POST
	@Operation(summary = "Create a dashboard")
	@Valid
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Response postDashboard(
			@Parameter(required = true) @Valid @NotNull final Dashboard dashboard,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws URISyntaxException, SQLException, JAXBException {
		if (getUser().hasLimitResources(ResourceType.DASHBOARD)) {
			throw new ForbiddenException("_NOT_ALLOWED_CREATE_RESOURCE");
		}

		dashboard.setCustomerId(getUser().getCustomerId());
		dashboard.setCreatedById(getUpdaterId());
		final Integer dashboardId = (int) dashboard.save(getConnection());
		getConnection().commit();

		final ResponseBuilder response = createCreatedResponse(dashboardId);
		if (returnResultParameters.shouldReturnResult()) {
			response.entity(getDashboardsFromDb(getConnection(), getUser(), dashboardId, null, null, fieldSelectionParameters, null, false, true).getRight().get(0));
		}
		return response.build();
	}

	/**
	 * Partially update a {@link Dashboard}.
	 *
	 * @param dashboardId The ID of the {@link Dashboard} to update.
	 * @param patch The JSON specification of what to change.
	 * @param fieldSelectionParameters What fields to return.
	 * @param returnResultParameters Whether to return the updated object(s) or not.
	 * @return The response.
	 */
	@PATCH
	@Path("{dashboardId: \\d+}")
	@Consumes(O24MediaType.APPLICATION_MERGE_PATCH_JSON)
	@Operation(summary = "Partially update a dashboard")
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Response patchDashboard(
			@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId,
			@RequestBody(required = true, content = @Content(schema = @Schema(implementation = Dashboard.class))) final String patch,
			@BeanParam final FieldSelectionParameters fieldSelectionParameters,
			@BeanParam final ReturnResultParameters returnResultParameters) throws SQLException, JAXBException {
		final List<Dashboard> dashboards = getDashboardsFromDb(getConnection(), getUser(), dashboardId, null, null, null, null, false, true).getRight();
		if (dashboards.isEmpty()) {
			throw new NotFoundException();
		}

		final Dashboard newDashboard = Dashboard.patch(dashboards.get(0), patch);
		newDashboard.setId(dashboardId);
		newDashboard.setCustomerId(getUser().getCustomerId());
		newDashboard.setCreatedById(dashboards.get(0).getCreatedById());
		newDashboard.setUpdatedById(getUpdaterId());
		newDashboard.save(getConnection());
		getConnection().commit();

		if (returnResultParameters.shouldReturnResult()) {
			return Response.ok(getDashboardsFromDb(getConnection(), getUser(), dashboardId, null, null, fieldSelectionParameters, null, false, true).getRight().get(0))
					.build();
		}

		return null;
	}

	/**
	 * Delete a {@link Dashboard}.
	 *
	 * @param dashboardId The ID of the {@link Dashboard} to delete.
	 * @return The response.
	 */
	@DELETE
	@Path("{dashboardId: \\d+}")
	@Operation(summary = "Delete a dashboard")
	@RequiredPermissions(permissions = {UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE})
	public Response deleteDashboard(@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId) throws SQLException {
		final List<Dashboard> dashboards = getDashboardsFromDb(getConnection(), getUser(), dashboardId, null, null, null, null, false, true).getRight();
		if (dashboards.isEmpty()) {
			throw new NotFoundException();
		}

		final Dashboard dashboard = dashboards.get(0);
		dashboard.setCustomerId(getUser().getCustomerId());
		dashboard.setDeleted(getUpdaterId());
		dashboard.save(getConnection());

		getConnection().commit();

		return null;
	}

	/**
	 * Link a {@link Dashboard} to a {@link Tag}.
	 *
	 * @param dashboardId The {@link Dashboard} to link.
	 * @param tagId The {@link Tag} to link.
	 * @return The response.
	 */
	@PUT
	@Path("{dashboardId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Create a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE)
	public Response putDashboardTagLink(@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId,
										@Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException, URISyntaxException {
		modifyDashboardTagLinks(TagModifyInfo.builder()
				.resourceId(dashboardId)
				.addedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return createCreatedResponse(dashboardId + "/tags/" + tagId).build();
	}

	/**
	 * Delete a link between a {@link Dashboard} and a {@link Tag}.
	 *
	 * @param dashboardId The {@link Dashboard} to unlink.
	 * @param tagId The {@link Tag} to unlink.
	 * @return The response.
	 */
	@DELETE
	@Path("{dashboardId: \\d+}/tags/{tagId: \\d+}")
	@Operation(summary = "Delete a link to a tag")
	@RequiredPermissions(permissions = UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE)
	public Response deleteDashboardTagLink(@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId,
										   @Parameter(required = true) @PathParam("tagId") final Integer tagId)
			throws SQLException {
		modifyDashboardTagLinks(TagModifyInfo.builder()
				.resourceId(dashboardId)
				.deletedTags(new Integer[] {tagId}).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Modify (both add and delete) the set of {@link Tag} linked to a {@link Dashboard}
	 *
	 * @param dashboardId The {@link Dashboard} to modify
	 * @param tagIds The new set of {@link Tag}
	 * @return The response
	 */
	@PUT
	@Path("{dashboardId: \\d+}/tags")
	@Operation(summary = "Modify linked set of tags")
	@RequiredPermissions(permissions = UserRolePermission.DASHBOARDS_VIEW_AND_MANAGE)
	public Response modifyTagLinks(@Parameter(required = true) @PathParam("dashboardId") final Integer dashboardId,
								   @RequestBody(required = true,
										   content = @Content(array = @ArraySchema(arraySchema = @Schema(implementation = Integer.class)))) final String tagIds)
			throws SQLException {
		modifyDashboardTagLinks(TagModifyInfo.builder()
				.resourceId(dashboardId)
				.resultTags(TagsResource.extractTagIds(tagIds)).build());
		getConnection().commit();

		return null;
	}

	/**
	 * Validate dashboard tags and update the links
	 *
	 * @param tagModifyInfo info about added/deleted/modified tags
	 */
	private void modifyDashboardTagLinks(final TagModifyInfo tagModifyInfo) throws SQLException {
		final List<Dashboard> dashboards
				= getDashboardsFromDb(getConnection(), getUser(), tagModifyInfo.getResourceId(), null, null, null, null, false, true).getRight();
		if (dashboards.isEmpty()) {
			throw new NotFoundException();
		}

		if (modifyTagLinks(tagModifyInfo) > 0) {
			dashboards.get(0).setUpdatedById(getUpdaterId());
			dashboards.get(0).save(getConnection());
		}
	}

	/**
	 * Get a list of {@link Dashboard}s from DB.
	 *
	 * @param conn Database connection.
	 * @param user User to get them for.
	 * @param id Optional ID to get a particular one.
	 * @param paginationParameters Pagination parameters.
	 * @param sortingParameters What sorting to use.
	 * @param fieldSelectionParameters What fields to return.
	 * @param filteringParameters Filtering parameters.
	 * @param getCount Whether to count the total or not.
	 * @param includeObjects Whether to get the objects or not.
	 * @return List, with count if applicable.
	 */
	protected static Pair<Long, List<Dashboard>> getDashboardsFromDb(final Connection conn, final BaseLoggedOnUser user, final Integer id,
																	 final PaginationParameters paginationParameters,
																	 final SortingParameters sortingParameters, final FieldSelectionParameters fieldSelectionParameters,
																	 final FilteringParameters filteringParameters, final boolean getCount, final boolean includeObjects)
			throws SQLException {
		final StringBuilder filter = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		StringUtils.concatenateFilters(filter, "deleted IS NULL");
		StringUtils.concatenateFilters(filter, "customerid = ?");
		params.add(user.getCustomerId());

		if (user.hasLimitResources(ResourceType.DASHBOARD)) {
			((LoggedOnSubUser) user).addResourceFilter(filter, params, ResourceType.DASHBOARD);
		}

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		return Dashboard.getObjects(Dashboard.class, conn, user, config, paginationParameters, sortingParameters, null, filteringParameters, id != null ? id.toString() : null,
				getCount, includeObjects, filter.toString(), params);
	}

	@Override
	protected String getResourcePath() {
		return PATH;
	}

	@Override
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Pair<String, String> getTagLinkTable() {
		return TAG_LINK_TABLE;
	}
}
