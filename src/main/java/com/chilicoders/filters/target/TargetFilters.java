package com.chilicoders.filters.target;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.user.api.UserAttribute;
import com.chilicoders.filters.FieldDefinition;
import com.chilicoders.filters.FieldType;
import com.chilicoders.filters.FilteringData;
import com.chilicoders.filters.FilteringSqlUtils;
import com.chilicoders.filters.ListDataType;

/**
 * Target filter definitions.
 */
public class TargetFilters {

	public static final Map<String, FieldDefinition> FILTER_SCANLOG_FIELDS;

	static {
		final Map<String, FieldDefinition> filters = new HashMap<>();
		filters.put("IPADDRESS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "IPADDRESS::text", 30));
		filters.put("NETBIOS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 15));
		filters.put("SCANNERNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("HOSTNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("PLATFORM", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("LATESTSCANSTATUS", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC, "", "", "", "", 30));
		filters.put("COMPLIANT", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN));
		filters.put("HIGH_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC, null, null, null, "COALESCE(high_count, 0)"));
		filters.put("MEDIUM_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC, null, null, null, "COALESCE(medium_count, 0)"));
		filters.put("LOW_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC, null, null, null, "COALESCE(low_count, 0)"));
		filters.put("REACHABLE", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC, null, null, null, "COALESCE(reachable, 1)"));
		FILTER_SCANLOG_FIELDS = Collections.unmodifiableMap(filters);
	}

	public static final Map<String, FieldDefinition> FILTER_WAS_SCANLOG_FIELDS;

	static {
		final Map<String, FieldDefinition> filters = new HashMap<>();
		filters.put("HOSTNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "VCTARGET"));
		filters.put("CRAWLED", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, "", "", "",
				"SIGN(COALESCE((SELECT xid FROM treport_vulns WHERE fk_treportentrys_xid = re.xid AND vcvulnid IN (295843, 269510) LIMIT 1), 0))"));
		FILTER_WAS_SCANLOG_FIELDS = Collections.unmodifiableMap(filters);
	}

	/**
	 * Possible target filters, but without user attribute (dynamic) filters.
	 *
	 * @see TargetBusiness#getFilters
	 */
	public static final Map<String, FieldDefinition> TARGET_FILTER_FIELDS_WITHOUT_DYNAMICS;

	static {
		final Map<String, FieldDefinition> filters = new HashMap<>();
		filters.put("XID", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC));
		filters.put("IPADDRESS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "IPADDRESS", 30));
		filters.put("VIRTUALHOSTS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("HOSTNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("NETBIOS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 15));
		filters.put("SCANNERNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("PLATFORM", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("AWS_INSTANCE_ID", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("AWSARN", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("AGENTID", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("MACADDRESS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 20));
		filters.put("LASTDISCOVERYDATE", new FieldDefinition(FieldType.DATE, ListDataType.DATE, "", "", "", "", 255));
		filters.put("LATESTSCANDATE", new FieldDefinition(FieldType.DATE, ListDataType.DATE, "", "", "", "", 255));
		filters.put("LATESTSCANSTATUS", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC, "", "", "", "", 255));
		filters.put("TEMPLATEOVERRIDE", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, null, null, null, "(CASE WHEN TEMPLATEOVERRIDE > 0 THEN 1 ELSE 0 END)"));
		filters.put("NEXTSCANDATE", new FieldDefinition(FieldType.DATE, ListDataType.DATE, "", "", "", "", 255));
		filters.put("LATESTSUCCESSFULSCANDATE", new FieldDefinition(FieldType.DATE, ListDataType.DATE, "", "", "", "", 255));
		filters.put("CVSS_SR_AVAIL", new FieldDefinition(FieldType.LIST, ListDataType.STRING, "", "", "", "", 255));
		filters.put("CVSS_SR_INTEG", new FieldDefinition(FieldType.LIST, ListDataType.STRING, "", "", "", "", 255));
		filters.put("CVSS_SR_CONF", new FieldDefinition(FieldType.LIST, ListDataType.STRING, "", "", "", "", 255));
		filters.put("CVSS_CDP", new FieldDefinition(FieldType.LIST, ListDataType.STRING, "", "", "", "", 255));
		filters.put("CVSS_TD", new FieldDefinition(FieldType.LIST, ListDataType.STRING, "", "", "", "", 255));
		filters.put("PCI", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, "", "", "", "", 10));
		filters.put("CONFIRMED", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, "", "", "", "", 10));
		filters.put("HIDDENURLS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("REQUESTBODYBLACKLIST", new FieldDefinition(FieldType.STRING, ListDataType.STRING));
		filters.put("URLBLACKLIST", new FieldDefinition(FieldType.STRING, ListDataType.STRING));
		filters.put("PCICOMPLIANCE", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN));
		filters.put("HIGH_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC));
		filters.put("MEDIUM_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC));
		filters.put("LOW_COUNT", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC));
		filters.put("AUTHENTICATIONTYPE", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC));
		filters.put("AUTHENTICATIONRESULT", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC));
		filters.put("USESLICENSE", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN));
		filters.put("OUTOFSCOPE", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, null, null, null, "OUTOFSCOPE::integer"));
		filters.put("COMPLIANT", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN));
		filters.put("COMPLIANCESENABLED", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC));
		filters.put("SCANUPDATEAVAILIBLE", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, null, null, null, "SCANUPDATEAVAILIBLE::integer"));
		filters.put("SNNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING));
		filters.put("BUSINESSCRITICALITY", new FieldDefinition(FieldType.LIST, ListDataType.ENUM));
		filters.put("EXPOSED", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, true));
		filters.put("AGENTLASTSYNCHRONIZED", new FieldDefinition(FieldType.DATE, ListDataType.DATE, "", "", "", "", 255));
		filters.put("AGENTVERSION", new FieldDefinition(FieldType.STRING, ListDataType.STRING));
		filters.put("AGENTCALLHOMEFREQUENCY", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC, null, null, null,
				"(EXTRACT(EPOCH FROM COALESCE(agentcallhomefrequency, CASE WHEN agentid IS NOT NULL THEN 'PT1H' ELSE NULL END)::INTERVAL)/60)::BIGINT"));

		final String lastAgentVersionQuery = String.format(
				"(CASE WHEN agentid IS NOT NULL THEN COALESCE(agentversion, '') LIKE (SELECT COALESCE((SELECT value FROM datastore WHERE KEY='%s'), '')) || '%%' ELSE NULL END)",
				DataStoreEntry.DataStoreEntryKeys.LAST_AGENT_VERSION.name());
		filters.put("AGENTLASTVERSION", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, null, null, null, lastAgentVersionQuery, 10, true, null));
		final String agentRetiredQuery = "(CASE WHEN agentid IS NULL THEN NULL ELSE COALESCE(agentretired, false) END)";
		filters.put("AGENTRETIRED", new FieldDefinition(FieldType.BOOLEAN, ListDataType.BOOLEAN, null, null, null, agentRetiredQuery, 10, true, null));

		TARGET_FILTER_FIELDS_WITHOUT_DYNAMICS = Collections.unmodifiableMap(filters);
	}

	public static final Map<String, FieldDefinition> FILTER_INACTIVE_FIELDS;

	static {
		final Map<String, FieldDefinition> filters = new HashMap<>();
		filters.put("IPADDRESS", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "IPADDRESS", 255));
		filters.put("HOSTNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "IPADDRESS", 255));
		filters.put("SCANNERNAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		filters.put("LATESTSCANSTATUS", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC, "", "", "", "", 255));
		FILTER_INACTIVE_FIELDS = Collections.unmodifiableMap(filters);
	}

	/**
	 * Returns the target filters, including custom user attributes.
	 *
	 * @param userAttributes The users attributes.
	 * @return The filter settings for targets.
	 */
	public static Map<String, FieldDefinition> getFilters(final List<? extends UserAttribute> userAttributes) {
		if (userAttributes != null && userAttributes.size() > 0) {
			return FieldDefinition.addDynamicFilters(TARGET_FILTER_FIELDS_WITHOUT_DYNAMICS, userAttributes, null);
		}
		return TARGET_FILTER_FIELDS_WITHOUT_DYNAMICS;
	}

	/**
	 * Returns the compliance target filters, including custom user attributes.
	 *
	 * @param userAttributes The users attributes.
	 * @return The filter settings for targets.
	 */
	public static Map<String, FieldDefinition> getComplianceTargetFilters(final List<? extends UserAttribute> userAttributes) {
		final Map<String, FieldDefinition> filters = new HashMap<>(getFilters(userAttributes));
		filters.put("IPADDRESS",
				new FieldDefinition(FieldType.STRING, ListDataType.STRING, null, null, null, "COALESCE(HOST(ipaddress),aws_instance_id,agentid,netbios,hostname)", 30));
		final String compliantFilter = "GREATEST("
				+ "(SELECT MAX(CASE WHEN compliant AND NOT accepted THEN 1 WHEN compliant AND accepted THEN 2 ELSE 3 END) FROM vcompliancefindings cf WHERE v.xid=cf.xipxid AND reportid=?),"
				+ "(SELECT MAX(CASE WHEN compliant THEN 1 ELSE 3 END) FROM vcompliancequestions q WHERE q.xuserxid = v.xuserxid AND q.policyid = (SELECT policyid FROM tcompliancereports r WHERE r.xid=?))"
				+ ")";
		filters.put("COMPLIANT", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC, null, null, null, compliantFilter));
		return filters;
	}

	/**
	 * Return the target filters.
	 *
	 * @param compliance true if compliance
	 * @param targetFilteringData Target filter data
	 * @return Target filters
	 */
	public static String getTargetsForScanjobFilter(final boolean compliance, final FilteringData targetFilteringData) {
		Map<String, FieldDefinition> filters = TargetFilters.FILTER_SCANLOG_FIELDS;
		if (compliance) {
			filters = new HashMap<>(filters);
			filters.put("COMPLIANT", new FieldDefinition(FieldType.LIST, ListDataType.NUMERIC));
		}
		final String filter = FilteringSqlUtils.getFiltering(targetFilteringData, filters, null, null, new TargetCustomFilter(), false);
		return filter;
	}
}
