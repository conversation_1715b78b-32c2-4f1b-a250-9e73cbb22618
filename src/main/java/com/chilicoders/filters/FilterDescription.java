package com.chilicoders.filters;

import com.chilicoders.core.rendering.api.Renderer;

import lombok.Getter;

/**
 * Contains data to help display a description of a filter field.
 */
@Getter
public class FilterDescription {

	private final String fieldName;
	private final String fieldMessageKey;
	private final Renderer renderer;
	private final String appendToDescription;

	/**
	 * Constructs a filter description object.
	 *
	 * @param fieldName the filter field name
	 * @param fieldMessageKey the message key to be used to display the filter description
	 * @param renderer a renderer to be used for displaying the description
	 * @param appendToDescription a string value to append to the description
	 */
	public FilterDescription(final String fieldName, final String fieldMessageKey, final Renderer renderer, final String appendToDescription) {
		this.fieldName = fieldName;
		this.fieldMessageKey = fieldMessageKey;
		this.renderer = renderer;
		this.appendToDescription = appendToDescription;
	}

	/**
	 * Constructs a filter description object.
	 *
	 * @param fieldName the filter field name
	 * @param fieldMessageKey the message key to be used to display the filter description
	 * @param renderer a renderer to be used for displaying the description
	 */
	public FilterDescription(final String fieldName, final String fieldMessageKey, final Renderer renderer) {
		this(fieldName, fieldMessageKey, renderer, null);
	}

	public FilterDescription(final String fieldName, final String fieldMessageKey) {
		this(fieldName, fieldMessageKey, null, null);
	}
}
