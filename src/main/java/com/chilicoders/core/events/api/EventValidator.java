package com.chilicoders.core.events.api;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserPermissions;
import com.chilicoders.model.Event;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.util.StringUtils;

public class EventValidator {
	/**
	 * Validate if subuser has rights to receive event.
	 *
	 * @param event The event.
	 * @param subuser User object
	 * @return true if subuser should receive event, otherwise false
	 */
	public static boolean validateSubuserEvent(final Event event, final UserDetails subuser) {
		final UserPermissions permissions = subuser.getPermissions();
		if (event.getEvent().getId() >= O24Event.TargetAdd.getId() && event.getEvent().getId() <= O24Event.TargetDelete.getId() && (!permissions.allowTargetManagement(false)
				&& !permissions.allowPciScoping(false))) {
			return false;
		}

		if (event.getEvent() == O24Event.DiscoveryDone) {
			if (event.getProperties() == null) {
				if (!permissions.allowTargetManagement(false) && !permissions.allowWasReporting(false)) {
					return false;
				}
			}
			else {
				final boolean was = StringUtils.getBooleanValue(event.getProperties().getInternalProperties(event.getEvent()).get("ISWAS"));
				if ((!was && !permissions.allowTargetManagement(false)) || (was && !permissions.allowWasReporting(false))) {
					return false;
				}
			}
		}

		if (event.getEvent().getId() > O24Event.DiscoveryDone.getId() && event.getEvent().getId() <= O24Event.DiscoveryAdded.getId() && !permissions.allowTargetManagement(
				false)) {
			return false;
		}

		if (((event.getEvent().getId() >= O24Event.ScanStart.getId() && event.getEvent().getId() <= O24Event.ScanError.getId())
				|| event.getEvent() == O24Event.ScanScheduleStarted
				|| event.getEvent() == O24Event.ScanScheduleScheduled
				|| event.getEvent() == O24Event.TargetScanScheduled)
				&& (!permissions.allowScheduleManagement(false) && !permissions.allowPciScheduleManagement(false) && !permissions.allowWasGeneral(false))) {
			return false;
		}

		if (((event.getEvent().getId() >= O24Event.FindingInformation.getId() && event.getEvent().getId() <= O24Event.LargeReport.getId())
				|| event.getEvent() == O24Event.ScanDone
				|| event.getEvent() == O24Event.ExploitAvailable)
				&& (!permissions.allowReporting(false)
				&& !permissions.allowPciReporting(false)
				&& !permissions.allowWasReporting(false)
				&& !permissions.allowSwatAccess(true))) {
			return false;
		}

		if (event.getEvent() == O24Event.VerifyDone && (!permissions.allowReporting(false) || !permissions.allowVerifyScan(false))) {
			return false;
		}

		if (event.getEvent() == O24Event.ScanDone) {
			if (event.getProperties() == null) {
				if (!permissions.allowReceiveScanResultsEmail(false) && !permissions.allowWasReporting(false)) {
					return false;
				}
			}
			else {
				final boolean was = StringUtils.getBooleanValue(event.getProperties().getInternalProperties(event.getEvent()).get("ISWAS"));
				if ((!was && !permissions.allowReceiveScanResultsEmail(false)) || (was && !permissions.allowWasReporting(false))) {
					return false;
				}
			}
		}

		if (event.getEvent().isHiabOnly() && !permissions.allowHiabManagement(false)) {
			return false;
		}

		if (event.getEvent() == O24Event.ScannerMissing && (!permissions.allowHiabManagement(false) || (event.getProperties() != null &&
				!permissions.hasScannerAccess(StringUtils.getLongValue(event.getProperties().getInternalProperties(event.getEvent()).get("SCANNERID")))))) {
			return false;
		}

		return true;
	}
}
