package com.chilicoders.core.reporting.api.model;

/**
 * When the user makes a selection in either the findings or the ports area of the delta report interface,
 * the generated report doesn't contain the overview from the other section.
 */
public enum DeltaFindingsPortSelection {
	FINDINGS,
	PORTS;

	/**
	 * Get enum from int.
	 *
	 * @param deltaFindingsPort Enum value as int
	 * @return DeltaFindingsPortSelection enum
	 */
	public static DeltaFindingsPortSelection getFromInt(final int deltaFindingsPort) {
		switch (deltaFindingsPort) {
			case 0:
				return FINDINGS;
			case 1:
				return PORTS;
			default:
				return null;
		}
	}
}
