package com.chilicoders.core.reporting.api;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;

/**
 * A criteria used when listing findings. This criteria can be used to limit which findings to get.
 */
@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
@Getter
public class FindingCriteria {
	/**
	 * Value to only get new or old findings, null means no limit.
	 */
	private Boolean isNew;

	/**
	 * Value to limit to a specific id, null means no limit.
	 */
	private Long id;

	/**
	 * Value to limit to specific ids, null means no limit.
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Long[] ids;

	/**
	 * Value to only get findings from a specific scanjob, null means no limit.
	 */
	private Long scanJobId;

	/**
	 * Value to only get certain risk levels, null means no limit.
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Integer[] riskLevels;

	/**
	 * Value to only get certain finding types, null means no limit.
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Integer[] findingTypes;

	/**
	 * Value to only get certain report entry ids, null means no limit.
	 */
	private Long reportId;

	/**
	 * True if only getting fixed findings.
	 */
	private boolean fixedOnly;

	/**
	 * True if only getting findings with tickets.
	 */
	private boolean withTicketsOnly;

	/**
	 * True if removing family was/wasx and null values
	 */
	private boolean familyNoWasOrNull;
}
