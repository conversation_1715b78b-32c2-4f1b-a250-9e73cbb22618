package com.chilicoders.core.reporting.api.model;

/**
 * Holds information about a was finding.
 */
public interface WasFinding {
	/**
	 * Get URL.
	 *
	 * @return URL.
	 */
	String getURL();

	/**
	 * Get HTTP method used to call the URL.
	 *
	 * @return HTTP method.
	 */
	String getMethod();

	/**
	 * Get post data.
	 *
	 * @return Post data.
	 */
	String getPost();

	/**
	 * Get time it took to make request.
	 *
	 * @return Time.
	 */
	String getTime();

	/**
	 * Get data.
	 *
	 * @return Data.
	 */
	String[] getData();

	/**
	 * vhost || '_' || vcvulnid, not sure if its great to get this from the database.
	 *
	 * @return vhost || '_' || vcvulnid
	 */
	String getKey();

	/**
	 * Get rule id.
	 *
	 * @return Rule id.
	 */
	long getVulnerabilityId();

	/**
	 * Get path for request.
	 *
	 * @return Path.
	 */
	Long getPath();
}
