package com.chilicoders.core.reporting.impl.jpa;

import javax.persistence.Column;
import javax.persistence.Id;

import com.chilicoders.core.reporting.api.model.Ticket;
import com.chilicoders.model.Protocol;

import lombok.Getter;
import lombok.Setter;

@Setter
public class TicketImpl implements Ticket {

	@Id
	@Column(name = "xid")
	private long id;

	@Getter
	@Column(name = "xipxid")
	long targetId;

	@Getter
	@Column(name = "vcvulnid")
	long vulnId;

	@Getter
	@Column(name = "iport")
	long port;

	@Column(name = "iprotocol")
	long protocol;

	@Getter
	@Column
	int status;

	@Getter
	@Column
	long taskId;

	@Getter
	@Column
	String assignee;

	@Override
	public Protocol getProtocol() {
		return Protocol.fromValue((int) this.protocol);
	}
}
