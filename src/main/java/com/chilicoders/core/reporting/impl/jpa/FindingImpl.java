package com.chilicoders.core.reporting.impl.jpa;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;

import com.chilicoders.core.reporting.api.model.Finding;
import com.chilicoders.core.reporting.api.model.FindingType;
import com.chilicoders.core.reporting.api.model.VulnerabilityType;
import com.chilicoders.model.BusinessCriticality;
import com.chilicoders.model.Protocol;
import com.chilicoders.model.SolutionType;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Getter
@Entity
public class FindingImpl implements Finding {
	public static final int TYPE_INFORMATIONAL = 2;

	@Id
	@Column(name = "xid")
	private long id;

	@Column(name = "xuserxid")
	private long userId;

	@Column(name = "xipxid")
	private long targetId;

	@Column(name = "irisk")
	private long riskLevel;

	@Column(name = "vcname")
	@Setter
	private String name;

	@Column
	private double cvssScore;

	@Column(name = "ipcicvss")
	@Setter
	private int pciCvssScoreInteger;

	@Column
	private double age;

	@Column(name = "xscanjobxid")
	private long scanJobId;

	@Setter
	@Column(name = "xtemplate")
	private long templateId;

	@Setter
	@Column(name = "xsoxid")
	private Long scheduleId;

	@Column(name = "reportxid")
	@Setter
	private long reportId;

	@Column(name = "vctarget")
	@Setter
	private String target;

	@Column
	private String hostname;

	@Column
	private String netbios;

	@Column
	private String platform;

	@Column(name = "date")
	private Date reportDate;

	@Column(name = "vcvulnid")
	@Setter
	private long ruleId;

	@Column(name = "iport")
	@Setter
	private long port;

	@Column(name = "iprotocol")
	private int protocol;

	@Column(name = "vcfamily")
	private String family;

	@Column(name = "product")
	private String productName;

	@Column(name = "bfalsepos")
	@Setter
	private int falsePositive;

	// 0 = No, 1 = Yes, 2 = Yes, Carry forward to next scan
	@Column(name = "bfalseposvalue")
	private int falsePositiveValue;

	@Column
	@Setter
	private String falsePositiveComment;

	@Column
	@Setter
	private String falsePositiveBy;

	@Column(name = "falseposby")
	private Long falsePositiveById;

	@Column(name = "csol")
	@Setter
	private String solution;

	@Column
	@Enumerated(EnumType.ORDINAL)
	private SolutionType solutionType;

	@Column(name = "vccve")
	@Setter
	private String cve;

	@Column(name = "vcbug")
	private String bugTraq;

	@Column
	private int verified = 0;

	@Column(name = "dfirstseen")
	private Date firstSeen;

	@Column(name = "dlastseen")
	private Date lastSeen;

	@Setter
	@Column
	private boolean accepted;

	@Setter
	@Column
	private Date acceptExpires;

	@Column
	private long scannerId;

	@Column
	private boolean hasExploits;

	@Column(name = "pcicompliance")
	private int compliant;

	@Column
	@Setter
	private String serviceName;

	@Column(name = "itype")
	@Setter
	private long findingType;

	@Column
	@Setter
	private int originalRiskLevel;

	@Column
	@Enumerated(EnumType.STRING)
	private VulnerabilityType vulnerabilityType = VulnerabilityType.Unknown;

	@Column(name = "bnew")
	@Setter
	private int isNew;

	@Column(name = "cdata")
	@Setter
	private String data;

	@Column
	private String acceptedBy;

	@Column(name = "accepteduserxid")
	private long acceptedByUserId;

	@Column
	@Setter
	private Date acceptDate;

	@Column
	@Setter
	private int acceptedLength;

	@Column(name = "acceptcomment")
	@Setter
	private String acceptedComment;

	@Column
	@Setter
	private int specialNote;

	@Column
	@Setter
	private boolean wasFinding;

	@Column
	@Setter
	private boolean previouslyDetected;

	@Column(name = "findingdate")
	@Setter
	private Date dateAdded;

	@Column
	@Setter
	private String patchInformation;

	@Column
	@Setter
	private String cvssVector;

	@Column(name = "bpci")
	private boolean pci;

	@Column(name = "bpcifailed")
	@Setter
	private int pciFailed;

	@Column
	private boolean disputeAccepted;

	@Column
	@Setter
	private Date disputeDate;

	@Column(name = "potentialfalse")
	@Setter
	private int potentialFalsePositive;

	@Column(name = "cdesc")
	private String description;

	@Column
	private String productUrl;

	@Column
	private String recreationFlow;

	@Column
	private String explanation;

	@Column(name = "vcvhost")
	@Setter
	private String virtualHost;

	@Column
	private BigDecimal cyrating;

	@Column
	private BigDecimal exploitProbability;

	@Column
	private BigDecimal cyratingDelta;

	@Column
	private BigDecimal exploitProbabilityDelta;

	@Column
	private Date cyratingUpdated;

	@Column
	private Date cyratingLastSeen;

	@Setter
	@Column
	private Integer bluelivMentions;

	@Setter
	@Column
	private Integer bluelivThreatActors;

	@Setter
	@Column
	private Integer bluelivExploits;

	@Column
	private Integer stillPresent;

	@Column
	private Double cvssV3Score;

	@Column
	private String cvssV3Severity;

	@Column
	private String cvssV3Vector;

	@Column
	private double nvdCvssScore;

	@Column
	private int fixed;

	@Column
	private String overrideComment;

	@Column
	private String custom0;

	@Column
	private String custom1;

	@Column
	private String custom2;

	@Column
	private String custom3;

	@Column
	private String custom4;

	@Column
	private String custom5;

	@Column
	private String custom6;

	@Column
	private String custom7;

	@Column
	private String custom8;

	@Column
	private String custom9;

	@Column
	private String targetcustom0;

	@Column
	private String targetcustom1;

	@Column
	private String targetcustom2;

	@Column
	private String targetcustom3;

	@Column
	private String targetcustom4;

	@Column
	private String targetcustom5;

	@Column
	private String targetcustom6;

	@Column
	private String targetcustom7;

	@Column
	private String targetcustom8;

	@Column
	private String targetcustom9;

	@Column
	private BusinessCriticality businessCriticality;

	@Column
	private boolean exposed;

	@Column(name = "aws_instance_id")
	private String awsInstanceId;

	@Column
	private String awsarn;

	public Boolean isStillPresent() {
		return stillPresent == null ? null : stillPresent.equals(1);
	}

	public void setId(final Number id) {
		this.id = id.longValue();
	}

	public void setTargetId(final Number targetId) {
		this.targetId = targetId.longValue();
	}

	public void setRiskLevel(final Number riskLevel) {
		this.riskLevel = riskLevel.longValue();
	}

	public void setCvssScore(final Number cvssScore) {
		this.cvssScore = cvssScore.doubleValue();
	}

	/**
	 * Set CVSS score and risk level.
	 *
	 * @param score CVSS score
	 * @param changeRiskLevel true if risk level should be changed, otherwise false
	 */
	public void setCvssScore(final int score, final boolean changeRiskLevel) {
		this.cvssScore = score;
		if (changeRiskLevel) {
			if (cvssScore == 0) {
				findingType = TYPE_INFORMATIONAL;
				riskLevel = RiskLevel.INFORMATIONAL.getValue();
			}
			else if (cvssScore >= RiskLevel.HIGH.getMinCvss() * 10) {
				riskLevel = RiskLevel.HIGH.getValue();
			}
			else if (cvssScore >= RiskLevel.MEDIUM.getMinCvss() * 10) {
				riskLevel = RiskLevel.MEDIUM.getValue();
			}
			else {
				riskLevel = RiskLevel.LOW.getValue();
			}
		}
	}

	public void setCvssV3Score(final Number cvssScore) {
		this.cvssV3Score = cvssScore == null ? null : cvssScore.doubleValue();
	}

	public void setAge(final Number age) {
		this.age = age.doubleValue();
	}

	/**
	 * Set protocol.
	 *
	 * @param protocol Protocol id.
	 */
	public void setProtocol(final Number protocol) {
		if (protocol == null) {
			this.protocol = Protocol.GENERIC.value;
		}
		else {
			this.protocol = protocol.intValue();
		}
	}

	public boolean isNew() {
		return isNew > 0;
	}

	@Override
	public boolean isPciFailed() {
		return pciFailed > 0;
	}

	@Override
	public long getDisputedBy() {
		return falsePositiveById;
	}

	public boolean isVerified() {
		return verified > 0;
	}

	public boolean isCompliant() {
		return compliant > 0;
	}

	public boolean hasExploits() {
		return hasExploits;
	}

	@Override
	public boolean isFalsePositive() {
		return falsePositive > 0;
	}

	/**
	 * Set PCI.
	 *
	 * @param isPci If > 0 this is a PCI finding.
	 */
	public void setPci(final Number isPci) {
		pci = false;
		if (isPci != null) {
			pci = isPci.intValue() > 0;
		}
	}

	/**
	 * Set dispute accepted.
	 *
	 * @param isAccepted If > 0 this finding has an accepted dispute.
	 */
	public void setDisputeAccepted(final Number isAccepted) {
		disputeAccepted = false;
		if (isAccepted != null) {
			disputeAccepted = isAccepted.intValue() > 0;
		}
	}

	public Double getExploitProbabilityDelta() {
		return exploitProbabilityDelta != null ? exploitProbabilityDelta.doubleValue() : null;
	}

	public Double getExploitProbability() {
		return exploitProbability != null ? exploitProbability.doubleValue() : null;
	}

	public Double getCyrating() {
		return cyrating != null ? cyrating.doubleValue() : null;
	}

	public Double getCyratingDelta() {
		return cyratingDelta != null ? cyratingDelta.doubleValue() : null;
	}

	public void setCyratingDelta(final Number cyratingDelta) {
		this.cyratingDelta = cyratingDelta != null ? BigDecimal.valueOf(cyratingDelta.doubleValue()) : null;
	}

	public void setCyrating(final Number cyrating) {
		this.cyrating = cyrating != null ? BigDecimal.valueOf(cyrating.doubleValue()) : null;
	}

	public void setExploitProbability(final Number exploitProbability) {
		this.exploitProbability = exploitProbability != null ? BigDecimal.valueOf(exploitProbability.doubleValue()) : null;
	}

	public void setExploitProbabilityDelta(final Number exploitProbabilityDelta) {
		this.exploitProbabilityDelta = exploitProbabilityDelta != null ? BigDecimal.valueOf(exploitProbabilityDelta.doubleValue()) : null;
	}

	public FindingType getFindingType() {
		return FindingType.getByValue(findingType);
	}

	public long getScheduleId() {
		return scheduleId == null ? 0 : scheduleId;
	}

	@Override
	public double getPciCvssScore() {
		return pciCvssScoreInteger / 10.0;
	}

	public int getIsPotentialFalsePositive() {
		return potentialFalsePositive;
	}

	public boolean isFixed() {
		return fixed == 1;
	}

	public String getAwsArn() {
		return awsarn;
	}
}
