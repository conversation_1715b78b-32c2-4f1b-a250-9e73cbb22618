package com.chilicoders.core.reporting.impl.jpa.repository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import com.chilicoders.core.reporting.impl.jpa.entity.ReportDownloadEntry;

public interface ReportDownloadEntryRepository extends CrudRepository<ReportDownloadEntry, Long> {

	ReportDownloadEntry findByIdAndUserIdAndSubUserId(final Long id, final long userId, final long subuserId);

	@Transactional
	@Modifying
	void deleteByUserIdAndId(final long userId, final long id);
}
