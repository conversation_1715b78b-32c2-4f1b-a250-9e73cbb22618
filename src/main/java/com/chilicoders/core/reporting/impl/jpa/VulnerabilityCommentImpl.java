package com.chilicoders.core.reporting.impl.jpa;

import java.util.Date;

import javax.persistence.Column;

import com.chilicoders.core.reporting.api.model.VulnerabilityComment;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Getter
@Setter
public class VulnerabilityCommentImpl implements VulnerabilityComment {

	@Column
	private long scriptId;

	@Column
	private long findingId;

	@Column(name = "dcreated")
	private Date created;

	@Column
	private String name;

	@Column
	private String comment;

}
