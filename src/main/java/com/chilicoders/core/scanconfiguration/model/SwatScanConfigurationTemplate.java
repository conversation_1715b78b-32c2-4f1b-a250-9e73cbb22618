package com.chilicoders.core.scanconfiguration.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorValue;
import org.eclipse.persistence.oxm.annotations.XmlNameTransformer;

import com.chilicoders.model.ScanConfigurationInterface.BaseScanConfigurationTemplate;
import com.chilicoders.rest.ModelHyphenator;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlNameTransformer(ModelHyphenator.class)
@SuppressWarnings("unused")
@XmlDiscriminatorValue(value = "SWAT")
@SuppressFBWarnings(value = "UUF_UNUSED_FIELD")
public class SwatScanConfigurationTemplate extends BaseScanConfigurationTemplate {
	private String[] seedUrls;
	private Integer stopAfterSeconds;
	private Scope crawlScope;
	private Scope httpScope;
	private Integer nFetchers;
	private String cookiesTxt;
	private Integer httpBodyLimit;
	private String[] httpBlockedMimeBodies;
	private Integer depthLimit;
	private String abortPattern;

	@Schema(name = "SwatScanConfigurationTemplate.Scope")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlNameTransformer(ModelHyphenator.class)
	private static class Scope {
		private RequestPattern[] mustMatch;
		private RequestPattern[] cannotMatch;
		private AllowedDomain[] allowedDomains;
		private CountedRequestPattern[] nMatches;
	}

	@Schema(name = "SwatScanConfigurationTemplate.AllowedDomain")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlNameTransformer(ModelHyphenator.class)
	private static class AllowedDomain {
		private String domain;
		private Boolean allowSubdomains;
	}

	@Schema(name = "SwatScanConfigurationTemplate.RequestPattern")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlNameTransformer(ModelHyphenator.class)
	private static class RequestPattern {
		private String method;
		private String url;
		private String body;
		private String bodyType;
	}

	@Schema(name = "SwatScanConfigurationTemplate.CountedRequestPattern")
	@XmlAccessorType(XmlAccessType.FIELD)
	@XmlNameTransformer(ModelHyphenator.class)
	private static class CountedRequestPattern {
		private RequestPattern pattern;
		private Integer limit;
	}
}
