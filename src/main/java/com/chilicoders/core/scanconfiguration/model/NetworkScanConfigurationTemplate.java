package com.chilicoders.core.scanconfiguration.model;

import static com.chilicoders.core.scanner.api.Scanner.EXTERNAL_SCANNER_ID;
import static com.chilicoders.core.scanner.api.Scanner.LOCAL_SCANNER;

import java.sql.SQLException;
import java.time.Duration;
import java.util.Collections;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.apache.commons.lang3.ArrayUtils;
import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorValue;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.ScanConfigurationInterface.BaseScanConfigurationTemplate;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlDiscriminatorValue(value = "NETWORK_SCAN")
public class NetworkScanConfigurationTemplate extends BaseScanConfigurationTemplate {
	@NotNull
	private Integer scanPolicyId;
	private Integer overrideScanPolicyId;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Integer[] assetTagIds;
	@NotNull
	@Min(value = 7200)
	@Max(value = 86400)
	private Integer timeout;
	private boolean scanless = true;

	private boolean anyScanner;
	private boolean scanAll;
	private boolean allowCloudAssetWithoutCloudResolver;
	private Integer maxConcurrentScans = 0;
	private Integer maxConcurrentScansPerAsset = 1;

	/**
	 * Validate template.
	 *
	 * @param statementExecutor SQL statement executor
	 * @param user user object
	 * @param scannerId selected scanner id
	 * @param configService Configuration service
	 */
	public void validate(final NativeStatementExecutor statementExecutor, final UserDetails user, final Integer scannerId, final ConfigurationService configService) throws SQLException {
		final long longScannerId = scannerId == null ? LOCAL_SCANNER : scannerId.longValue();

		if (timeout > Duration.ofHours(12).getSeconds() && (longScannerId == EXTERNAL_SCANNER_ID || (longScannerId == LOCAL_SCANNER && !configService.isHiabEnabled()))) {
			throw new InputValidationException("_TIMELIMIT_OUT_OF_BOUNDS", ErrorCode.InputValidationFailed, Collections.singletonList("timeout: " + timeout));
		}

		if (!ArrayUtils.isEmpty(this.assetTagIds) && statementExecutor.getLong(new NativeSqlStatement(
				"SELECT COUNT(id) FROM tags WHERE customerid = ? AND id = ANY(?) AND deleted IS NULL",
				user.getCustomerId(), this.assetTagIds)) != this.assetTagIds.length) {
			throw new InputValidationException("_ID_REFERENCES_NONEXISTANT", ErrorCode.InputValidationFailed,
					Collections.singletonList("assetTagIds: " + StringUtils.join(this.assetTagIds, ",")));
		}

		if (this.scanPolicyId != null && statementExecutor.getLong(new NativeSqlStatement(
				"SELECT id FROM scanpolicies WHERE (customerid = ? OR system) AND id = ? AND deleted IS NULL",
				user.getCustomerId(), this.scanPolicyId)) == null) {
			throw new InputValidationException("_ID_REFERENCES_NONEXISTANT", ErrorCode.InputValidationFailed, Collections.singletonList("scanPolicyId: " + scanPolicyId));
		}

		if (this.overrideScanPolicyId != null && statementExecutor.getLong(new NativeSqlStatement(
				"SELECT id FROM scanpolicies WHERE (customerid = ? OR system) AND id = ? AND deleted IS NULL",
				user.getCustomerId(), this.overrideScanPolicyId)) == null) {
			throw new InputValidationException("_ID_REFERENCES_NONEXISTANT", ErrorCode.InputValidationFailed,
					Collections.singletonList("overrideScanPolicyId: " + overrideScanPolicyId));
		}
	}
}
