package com.chilicoders.core.scanconfiguration.model;

import java.sql.SQLException;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorValue;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.model.ScanConfigurationInterface.BaseScanConfigurationTemplate;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlDiscriminatorValue(value = "DOCKER_SCAN")
public class DockerScanConfigurationTemplate extends BaseScanConfigurationTemplate {
	private Integer maxConcurrentScans = 0;
	private Integer maxConcurrentScansPerAsset = 1;

	public void validate(final NativeStatementExecutor statementExecutor, final UserDetails user) throws SQLException {
	}
}
