package com.chilicoders.core.scanconfiguration.model;

import java.sql.SQLException;
import java.util.Arrays;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.eclipse.persistence.oxm.annotations.XmlDiscriminatorValue;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.model.AccountType;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.ScanConfigurationInterface.BaseScanConfigurationTemplate;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.StringUtils;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlDiscriminatorValue(value = "CLOUDSEC")
public class CloudsecScanConfigurationTemplate extends BaseScanConfigurationTemplate {
	@NotNull
	private Integer accountId;

	@NotNull
	private Integer policyId;

	private String regions;

	private Integer maxConcurrentScans = 0;

	/**
	 * Validate template.
	 *
	 * @param statementExecutor SQL statement executor.
	 * @param user user object
	 */
	public void validate(final NativeStatementExecutor statementExecutor, final UserDetails user) throws SQLException {
		if (statementExecutor.getLong(new NativeSqlStatement(
				"SELECT id FROM compliancepolicies WHERE customerid IS NULL AND id = ? AND deleted IS NULL",
				this.policyId)) == null) {
			throw new InputValidationException("_ID_REFERENCES_NONEXISTANT", ErrorCode.InputValidationFailed, Arrays.asList("policyId: " + this.policyId));
		}

		final String accountType = statementExecutor.getString(new NativeSqlStatement(
				"SELECT type::TEXT FROM accounts WHERE customerid = ? AND id = ? AND deleted IS NULL",
				user.getCustomerId(), this.accountId));
		if (accountType == null) {
			throw new InputValidationException("_ID_REFERENCES_NONEXISTANT", ErrorCode.InputValidationFailed, Arrays.asList("accountId: " + this.accountId));
		}

		if (AccountType.valueOf(accountType) == AccountType.AWS) {
			if (StringUtils.isEmpty(this.regions)) {
				throw new InputValidationException("_REGIONS_CANT_BE_EMPTY", ErrorCode.InputValidationFailed);
			}
		}
		else {
			this.regions = null;
		}
	}

}
