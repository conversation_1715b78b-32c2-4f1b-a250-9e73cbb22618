package com.chilicoders.core.ticketing.impl.jpa.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.chilicoders.core.ticketing.api.model.Ticket;
import com.chilicoders.core.ticketing.impl.jpa.TicketImpl;
import com.chilicoders.model.Protocol;

/**
 * Repository for getting tickets from our internal ticketing system.
 */
public interface TicketRepository extends JpaRepository<TicketImpl, Long> {

	/**
	 * Get ticket based on finding id.
	 *
	 * @param findingId Finding id.
	 * @return Ticket.
	 */
	Ticket findOneByFindingId(long findingId);

	/**
	 * Get ticket based on user and task id.
	 *
	 * @param taskId Task id.
	 * @param userId User id.
	 * @return Ticket.
	 */
	Ticket findOneByTaskIdAndUserId(long taskId, long userId);

	List<? extends Ticket> findByUserIdAndAndTargetIdAndVulnIdAndPortAndProtocolAndStatusIn(long mainUserId, long targetId, long ruleid, long port, Protocol protocol,
																							Object[] objects);

	List<? extends Ticket> findByUserIdAndTargetId(long mainUserId, long targetId);

	@Query(nativeQuery = true, value =
			"SELECT xid, findingid, port, protocol, xipxid, vcvulnid, assignee, status, taskid, iprotocol, xcreator, duedate, ipaddress, xuserxid, name, iport, priority, "
					+
					"xsubuserxid, type, xupdator, includevulinfo, scanjob, dreportdate, reportxid FROM vworkflow WHERE xuserxid = :userId AND findingid IN (SELECT xid FROM treport_vulns WHERE fk_treportentrys_xid = :reportId)")
	List<? extends TicketImpl> findByUserIdAndReportIdIn(@Param("userId") long mainUserId, @Param("reportId") long reportId);
}
