package com.chilicoders.core.ticketing.impl.jpa;

import java.sql.SQLException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.ticketing.api.model.ServiceNowServer;
import com.chilicoders.core.ticketing.api.model.Ticket;
import com.chilicoders.core.ticketing.impl.TicketingDao;
import com.chilicoders.core.ticketing.impl.jpa.repository.ServiceNowServerRepository;
import com.chilicoders.core.ticketing.impl.jpa.repository.TicketRepository;
import com.chilicoders.core.user.api.UserDetails;

/**
 * JPA/Hibernate implementation for ticketing Dao
 */
@Component
public class TicketingDaoImpl implements TicketingDao {

	@Autowired
	private ServiceNowServerRepository serviceNowRepository;

	@Autowired
	private TicketRepository ticketRepository;

	@Override
	public List<? extends Ticket> getSwatTickets(final UserDetails user, final long reportId) throws SQLException {
		return ticketRepository.findByUserIdAndReportIdIn(user.getMainUserId(), reportId);
	}

	@Override
	public List<? extends Ticket> getTargetTickets(final UserDetails user, final long targetId) throws SQLException {
		return ticketRepository.findByUserIdAndTargetId(user.getMainUserId(), targetId);
	}

	@Override
	public ServiceNowServer getServiceNowServer(final long id) throws SQLException {
		return serviceNowRepository.findById(id).orElse(null);
	}

	@Override
	public Ticket getTicket(final UserDetails user, final long ticketId) {
		return ticketRepository.findById(ticketId).orElse(null);
	}

}
