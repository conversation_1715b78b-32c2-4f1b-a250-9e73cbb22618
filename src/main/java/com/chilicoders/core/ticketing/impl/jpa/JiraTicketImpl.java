package com.chilicoders.core.ticketing.impl.jpa;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Transient;

import org.hibernate.annotations.Formula;

import com.chilicoders.core.ticketing.api.model.JiraTicket;
import com.chilicoders.core.ticketing.api.model.Ticket;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Ticket entity intended to be sent to a jira instance.
 */
@Entity(name = "jiratickets")
@Getter
@NoArgsConstructor
@SuppressFBWarnings("EI_EXPOSE_REP")
public class JiraTicketImpl extends BaseTicketImpl implements JiraTicket {

	/**
	 * Constructor.
	 *
	 * @param ticket An old ticket instance to copy data from.
	 */
	public JiraTicketImpl(final Ticket ticket) {
		super(ticket);
		if (ticket instanceof JiraTicket) {
			final JiraTicket jiraTicket = (JiraTicket) ticket;
			jiraKey = jiraTicket.getJiraKey();
			createTicket = jiraTicket.shouldCreateTicket();
			swat = jiraTicket.isSwat();
		}
	}

	@Column
	private String jiraKey;

	@Column
	private boolean createTicket;

	@Column(insertable = false, updatable = false)
	@Formula("(SELECT vjiratickets.dreportdate FROM vjiratickets WHERE vjiratickets.xid = xid)")
	protected Date reportDate;

	@Column(insertable = false, updatable = false)
	@Formula("(SELECT vjiratickets.reportxid FROM vjiratickets WHERE vjiratickets.xid = xid)")
	protected Long reportId;

	@Column
	protected long findingId;

	@Transient
	private boolean swat;

	@Setter
	@Column(name = "includevulinfo")
	protected boolean includeVulnInfo;

	@Column(name = "scanjob")
	protected Long scanjobId;

	@Override
	public void setScanjobId(final Number scanjobId) {
		if (scanjobId == null) {
			this.scanjobId = null;
		}
		else {
			this.scanjobId = scanjobId.longValue();
		}
	}

	@Override
	public boolean includeVulnerabilityInfo() {
		return includeVulnInfo;
	}

	@Override
	public void setFindingId(final Number findingId) {
		this.findingId = findingId.longValue();
	}

	@Override
	public String getJiraKey() {
		return jiraKey;
	}

	@Override
	public void setJiraKey(final String jiraKey) {
		this.jiraKey = jiraKey;
	}

	@Override
	public boolean shouldCreateTicket() {
		return createTicket;
	}

	@Override
	public void setCreateTicket(final boolean createTicket) {
		this.createTicket = createTicket;
	}

	@Override
	public boolean isSwat() {
		return swat;
	}

	@Override
	public void setSwat(final boolean swat) {
		this.swat = swat;
	}

}
