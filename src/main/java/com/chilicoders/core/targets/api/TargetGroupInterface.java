package com.chilicoders.core.targets.api;

/**
 * Base interface for target groups.
 */
public interface TargetGroupInterface {
	/**
	 * @return True if the target group is for PCI.
	 */
	boolean isPci();

	/**
	 * @return Id of the target group.
	 */
	long getId();

	/**
	 * @return User id of the target group.
	 */
	Long getUserId();

	/**
	 * @return Name of the target group.
	 */
	String getName();

	/**
	 * @return Parent group id.
	 */
	Long getParentId();

	/**
	 * @return true if this group is rule based.
	 */
	boolean isRuleBased();

	/**
	 * @return true if this group is based on a report rule.
	 */
	boolean isReportBased();
}
