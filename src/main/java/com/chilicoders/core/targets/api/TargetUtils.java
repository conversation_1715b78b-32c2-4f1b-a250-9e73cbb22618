package com.chilicoders.core.targets.api;

import java.util.Arrays;

import org.apache.commons.lang3.ArrayUtils;

public class TargetUtils {

	public static final long ALL_TARGETS_SELECTION = -1;
	public static final long UNGROUPED_TARGET_GROUP_SELECTION = -2;
	public static final long DELETED_TARGET_GROUP_SELECTION = -5;

	/**
	 * When fetching findings and the user has selected all targets and deselected a few the target string will contain something like
	 * -1,xid1,xid2
	 * This is translated to be that all targets should be included but not xid1 and xid2.
	 *
	 * @param targetIds The current target string.
	 * @return xid1, xid2... if an inverted selection exists, <code>null</code> otherwise.
	 */
	public static Long[] getInvertedSelection(final Long[] targetIds) {
		if (isAllTargetsSelected(targetIds) && targetIds.length != 1) {
			return Arrays.asList(targetIds).stream().filter(e -> e != ALL_TARGETS_SELECTION).toArray(Long[]::new);
		}
		return null;
	}

	/**
	 * Returns true if the "ALL_TARGETS_SELECTION" is present in the target ids
	 *
	 * @param targetIds the selected target ids.
	 * @return whether all targets were selected or not.
	 */
	public static boolean isAllTargetsSelected(final Long[] targetIds) {
		if (!ArrayUtils.isEmpty(targetIds)) {
			for (final long targetId : targetIds) {
				if (targetId == ALL_TARGETS_SELECTION) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Returns true if the "DELETED_TARGET_GROUP_SELECTION" is present in the target group ids
	 *
	 * @param targetGroupIds the selected target group ids
	 * @return whether DELETED_TARGET_GROUP_SELECTION is present in the array or not
	 */
	public static boolean isDeletedTargetsGroupSelected(final Long[] targetGroupIds) {
		if (!ArrayUtils.isEmpty(targetGroupIds)) {
			for (final long targetgroupId : targetGroupIds) {
				if (targetgroupId == DELETED_TARGET_GROUP_SELECTION) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Returns true if the "UNGROUPED_TARGET_GROUP_SELECTION" is present in the target group ids
	 *
	 * @param targetGroupIds the selected target group ids
	 * @return whether UNGROUPED_TARGET_GROUP_SELECTION is present in the array or not
	 */
	public static boolean isUngroupedTargetGroupSelected(final Long[] targetGroupIds) {
		if (!ArrayUtils.isEmpty(targetGroupIds)) {
			for (final long targetgroupId : targetGroupIds) {
				if (targetgroupId == UNGROUPED_TARGET_GROUP_SELECTION) {
					return true;
				}
			}
		}
		return false;
	}
}
