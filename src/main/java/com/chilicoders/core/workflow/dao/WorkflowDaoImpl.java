package com.chilicoders.core.workflow.dao;

import java.sql.SQLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.scandata.impl.jpa.PortalScanlog;
import com.chilicoders.core.scandata.impl.jpa.repository.PortalScanlogRepository;
import com.chilicoders.core.workflow.model.ScanQueue;
import com.chilicoders.core.workflow.model.ScheduledReportQueue;
import com.chilicoders.core.workflow.repository.ScanQueueRepository;
import com.chilicoders.core.workflow.repository.ScheduledReportQueueRepository;
import com.chilicoders.core.workflow.repository.WorkflowRepository;
import com.chilicoders.model.ScanLogEntryInterface;
import com.chilicoders.model.ScanQueueInterface;
import com.chilicoders.model.ScheduledReportQueueInterface;
import com.chilicoders.model.WorkflowInterface;

@Component
public class WorkflowDaoImpl implements WorkflowDao {
	@Autowired
	private PortalScanlogRepository portalScanlogRepository;

	@Autowired
	private ScanQueueRepository scanQueueRepository;

	@Autowired
	private ScheduledReportQueueRepository scheduledReportQueueRepository;

	@Autowired
	private WorkflowRepository workflowRepository;

	@Override
	public ScanLogEntryInterface getCompletedWorkflowScanlog() {
		return portalScanlogRepository.getCompletedWorkflow();
	}

	@Override
	public Integer writeWorkflowScanLog(final ScanLogEntryInterface scanlog) {
		return portalScanlogRepository.save((PortalScanlog) scanlog).getId();
	}

	@Override
	public ScanQueueInterface createScanQueue(final ScanLogEntryInterface scanlog, final Integer scanConfigurationId, final String scanData) {
		return ScanQueue.builder()
				.customerId(scanlog.getCustomerId())
				.scanConfigurationId(scanConfigurationId)
				.workflowId(scanlog.getWorkflowId())
				.parentId(scanlog.getParentId())
				.scheduleId(scanlog.getScheduleId())
				.scanData(scanData)
				.build();
	}

	@Override
	public Integer saveScanQueue(final ScanQueueInterface scanQueue) throws SQLException {
		return scanQueueRepository.save((ScanQueue) scanQueue).getId();
	}

	@Override
	public WorkflowInterface getWorkflowById(final Integer workflowId) throws SQLException {
		return workflowRepository.findById(workflowId).orElse(null);
	}

	@Override
	public ScheduledReportQueueInterface createScheduledReportQueue(final ScanLogEntryInterface scanlog, final Integer scheduledReportId, final String scanData) {
		return ScheduledReportQueue.builder()
				.customerId(scanlog.getCustomerId())
				.scheduledReportId(scheduledReportId)
				.workflowId(scanlog.getWorkflowId())
				.scanData(scanData)
				.build();
	}

	@Override
	public Integer saveScheduledReportQueue(final ScheduledReportQueueInterface scheduledReportQueue) throws SQLException {
		return scheduledReportQueueRepository.save((ScheduledReportQueue) scheduledReportQueue).getId();
	}
}
