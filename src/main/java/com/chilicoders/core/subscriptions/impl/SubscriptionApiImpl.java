package com.chilicoders.core.subscriptions.impl;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.ws.rs.HttpMethod;
import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBException;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.glassfish.jersey.client.HttpUrlConnectorProvider;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriUtils;
import org.yaml.snakeyaml.Yaml;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.metrics.api.MetricsService;
import com.chilicoders.core.subscriptions.api.SubscriptionApi;
import com.chilicoders.core.subscriptions.api.model.Subscription;
import com.chilicoders.core.subscriptions.api.model.SubscriptionTemplate;
import com.chilicoders.filters.FilterField;
import com.chilicoders.rest.RestClientFactory;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.MetricOnProcessingException;
import com.chilicoders.util.RuntimeEnvironment;
import com.chilicoders.util.SslUtils;

import com.google.common.collect.ImmutableList;

@Component
public class SubscriptionApiImpl implements SubscriptionApi {

	private static final Logger LOG = LogManager.getLogger(SubscriptionApiImpl.class);

	private static final String SUBSCRIPTION_API_CONNECTIONS_START_METER = "subscription.api.connections";
	private static final String SUBSCRIPTION_API_REQUEST_ERRORS_METER = "subscription.api.request.errors";
	private static final String SUBSCRIPTION_API_CONNECTIONS_FAILED_METER = "subscription.api.connections.failed";

	private static final String HEADER_CUSTOMER_UUID = "X-Customer-UUID";
	private static final String HEADER_USER_UUID = "X-User-UUID";
	private static final String HEADER_SUBSCRIPTION_COUNT = "Count";

	public static final String APPLICATION_MERGE_PATCH_JSON = "application/merge-patch+json";

	private static final String PATH_SUBSCRIPTIONS = "subscriptions";
	private static final String PATH_TEMPLATES = "templates";

	private static final char COMMA_DELIMITER = ',';

	private final Client client;

	private final String baseUrl;

	private final MetricOnProcessingException metricMark;

	private final String tokenFile;

	@Autowired
	public SubscriptionApiImpl(final ConfigurationService configService, final MetricsService metricsService) {
		this(configService, metricsService, configService.getProperty(ConfigurationKey.subscriptions_api_url));
	}

	/**
	 * Constructor
	 *
	 * @param configService configuration service
	 * @param metricsService metrics service
	 * @param subscriptionApiUrl Subscription service URL
	 */
	public SubscriptionApiImpl(final ConfigurationService configService, final MetricsService metricsService, final String subscriptionApiUrl) {
		this.client = RestClientFactory.createClient(configService, SslUtils.getO24DefaultSslContext());
		this.baseUrl = subscriptionApiUrl + "/rest/v0/";
		this.metricMark = () -> metricsService;
		tokenFile = RuntimeEnvironment.isKubernetesEnabled() || configService.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled) ? configService.getProperty(ConfigurationKey.kubernetes_token_path) : null;
	}

	@Override
	public long getSubscriptionCount(final String userUuid, final String customerUuid, final List<FilterField> filters) {
		WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS);
		if (filters != null) {
			for (final FilterField filter : filters) {
				target = target.queryParam("filter[" + filter.getFieldName() + "[" + filter.getComparison() + "]]", UriUtils.encode(filter.getValue(), StandardCharsets.UTF_8.name()));
			}
		}

		return getSubscriptionCount(userUuid, customerUuid, target);
	}

	/**
	 * Send HEAD request to subscription service to get subscription count.
	 *
	 * @param userUuid User UUID
	 * @param customerUuid Customer UUID
	 * @param target Target URI
	 * @return Number of subscription templates returned by subscription service
	 */
	private long getSubscriptionCount(final String userUuid, final String customerUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(StringUtils.isEmpty(userUuid) ? HEADER_CUSTOMER_UUID : HEADER_USER_UUID, StringUtils.isEmpty(userUuid) ? customerUuid : userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.head();
			if (response.getStatus() == Response.Status.OK.getStatusCode() || response.getStatus() == Response.Status.NO_CONTENT.getStatusCode()) {
				return Long.valueOf(response.getHeaderString(HEADER_SUBSCRIPTION_COUNT));
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			if (response.getStatus() == Response.Status.BAD_REQUEST.getStatusCode()) {
				throw new InputValidationException();
			}
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API HEAD request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public List<Subscription> getSubscriptions(final String userUuid, final String customerUuid, final List<FilterField> filters, final List<String> sorting,
			final Integer offset, final Integer limit, final Instant activatedAtBefore, final Instant activatedAtAfter,
											   final Instant activeUntilBefore, final Instant activeUntilAfter, final String... uuids) {
		WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS);

		if (uuids.length > 0) {
			target = target.queryParam("filter[uuid[in]]", StringUtils.join(uuids, COMMA_DELIMITER));
		}

		if (filters != null) {
			for (final FilterField filter : filters) {
				target = target.queryParam("filter[" + filter.getFieldName() + "[" + filter.getComparison() + "]]", UriUtils.encode(filter.getValue(), StandardCharsets.UTF_8.name()));
			}
		}

		if (sorting != null && !sorting.isEmpty()) {
			target = target.queryParam("sort", StringUtils.join(sorting, COMMA_DELIMITER));
		}

		if (offset != null) {
			target = target.queryParam("offset", offset);
		}

		if (limit != null) {
			target = target.queryParam("limit", limit);
		}

		if (activatedAtBefore != null) {
			target = target.queryParam("filter[activatedAt[lte]]", activatedAtBefore.toString());
		}

		if (activatedAtAfter != null) {
			target = target.queryParam("filter[activatedAt[gte]]", activatedAtAfter.toString());
		}

		if (activeUntilBefore != null) {
			target = target.queryParam("filter[activeUntil[lte]]", activeUntilBefore.toString());
		}

		if (activeUntilAfter != null) {
			target = target.queryParam("filter[activeUntil[gte]]", activeUntilAfter.toString());
		}

		return ImmutableList.copyOf(getSubscriptions(userUuid, customerUuid, target));
	}

	/**
	 * Send GET request to subscription service
	 *
	 * @param userUuid User UUID
	 * @param customerUuid customer UUID
	 * @param target target URI
	 * @return set of subscriptions returned by subscription service
	 */
	private Set<Subscription> getSubscriptions(final String userUuid, final String customerUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(StringUtils.isEmpty(userUuid) ? HEADER_CUSTOMER_UUID : HEADER_USER_UUID, StringUtils.isEmpty(userUuid) ? customerUuid : userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.get();
			if (response.getStatus() == Response.Status.OK.getStatusCode()) {
				final Set<Subscription> subscriptions = new LinkedHashSet<>();
				final String content = response.readEntity(String.class);
				if (StringUtils.isEmpty(content)) {
					return subscriptions;
				}
				final JSONArray json = new JSONArray(content);
				json.forEach(item -> {
					if ((((JSONObject)item).has("template"))) {
						((JSONObject)item).getJSONObject("template").put("variables", convertJsonToYaml(((JSONObject)item).getJSONObject("template").optJSONObject("variables")));
					}
					try {
						subscriptions.add(MarshallingUtils.unmarshal(Subscription.class, item.toString()));
					}
					catch (final JAXBException e) {
						throw new RuntimeException(e);
					}
				});
				return subscriptions;
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API GET request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public List<Subscription> getSubscriptionsByAssetGroupIds(final String customerUuid, final Integer... assetGroupIds) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS).queryParam("filter[associations.assetGroups[in]]", StringUtils.join(assetGroupIds, COMMA_DELIMITER));
		return ImmutableList.copyOf(getSubscriptions(null, customerUuid, target));
	}

	@Override
	public List<Subscription> getActiveSubscriptionsByAssetGroupId(final String customerUuid, final Integer assetGroupId) {
		final String now = Instant.now().toString();
		final WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS)
				.queryParam("filter[associations.assetGroups[in]]", assetGroupId)
				.queryParam("filter[activatedAt[lt]]", now)
				.queryParam("filter[activeUntil[gt]]", now);
		return ImmutableList.copyOf(getSubscriptions(null, customerUuid, target));
	}

	@Override
	public List<Subscription> getDeletedSubscriptions(final String customerUuid, final String... uuids) {
		WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS);
		if (uuids.length > 0) {
			target = target.queryParam("filter[uuid[in]]", StringUtils.join(uuids, COMMA_DELIMITER));
		}
		target = target.queryParam("filter[deleted[neq]]", "null");
		return ImmutableList.copyOf(getSubscriptions(null, customerUuid, target));
	}

	/**
	 * Send PATCH request to subscription service to update subscription.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @param patch Patch JSON
	 * @return The updated subscription
	 */
	private Subscription updateSubscription(final String userUuid, final WebTarget target, final String patch) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			try {
				final JSONObject json = new JSONObject(patch);
				if (json.has("template")) {
					json.getJSONObject("template").put("variables", convertYamlToJson(json.getJSONObject("template").optString("variables", null)));
				}

				target.property(HttpUrlConnectorProvider.SET_METHOD_WORKAROUND, true);
				final Response response = target.request()
						.header(HEADER_USER_UUID, userUuid)
						.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
						.method(HttpMethod.PATCH, Entity.entity(json.toString(), APPLICATION_MERGE_PATCH_JSON));

				if (response.getStatus() == Status.OK.getStatusCode()) {
					final JSONObject responseJson = new JSONObject(response.readEntity(String.class));
					if (responseJson.has("template")) {
						responseJson.getJSONObject("template").put("variables", convertJsonToYaml(responseJson.getJSONObject("template").optJSONObject("variables")));
					}
					return MarshallingUtils.unmarshal(Subscription.class, responseJson.toString());
				}

				metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
				throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
			}
			catch (final JAXBException ex) {
				throw new RuntimeException(ex);
			}
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API PATCH request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public Subscription updateSubscription(final String userUuid, final String uuid, final String patch) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS + "/" + uuid);
		return updateSubscription(userUuid, target, patch);
	}

	/**
	 * Send POST request to subscription service to add subscription.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @param subscription Subscription to create
	 * @return The created subscription
	 */
	private Subscription createSubscription(final String userUuid, final WebTarget target, final Subscription subscription) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			try {
				final JSONObject json = new JSONObject(MarshallingUtils.marshal(subscription));
				if (json.has("template")) {
					json.getJSONObject("template").put("variables", convertYamlToJson(json.getJSONObject("template").optString("variables", null)));
				}

				final Response response = target.request()
						.header(HEADER_USER_UUID, userUuid)
						.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
						.post(Entity.entity(json.toString(), MediaType.APPLICATION_JSON));

				if (response.getStatus() == Status.CREATED.getStatusCode()) {
					final JSONObject responseJson = new JSONObject(response.readEntity(String.class));
					if (responseJson.has("template")) {
						responseJson.getJSONObject("template").put("variables", convertJsonToYaml(responseJson.getJSONObject("template").optJSONObject("variables")));
					}
					return MarshallingUtils.unmarshal(Subscription.class, responseJson.toString());
				}

				metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
				throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
			}
			catch (final JAXBException ex) {
				throw new RuntimeException(ex);
			}
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API POST request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public Subscription createSubscription(final String userUuid, final Subscription subscription) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS);
		return createSubscription(userUuid, target, subscription);
	}

	/**
	 * Send DELETE request to subscription service to remove subscription.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @return true if subscription was removed
	 */
	private boolean deleteSubscription(final String userUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(HEADER_USER_UUID, userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.delete();

			if (response.getStatus() == Response.Status.OK.getStatusCode() || response.getStatus() == Response.Status.NO_CONTENT.getStatusCode()) {
				return true;
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API DELETE request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public boolean deleteSubscription(final String userUuid, final String uuid) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_SUBSCRIPTIONS + "/" + uuid);
		return deleteSubscription(userUuid, target);
	}

	/**
	 * Send PATCH request to subscription service to update template.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @param patch Patch JSON
	 * @return The updated subscription template
	 */
	private SubscriptionTemplate updateSubscriptionTemplate(final String userUuid, final WebTarget target, final String patch) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			try {
				final JSONObject json = new JSONObject(patch);
				json.put("variables", convertYamlToJson(json.optString("variables", null)));

				target.property(HttpUrlConnectorProvider.SET_METHOD_WORKAROUND, true);
				final Response response = target.request()
						.header(HEADER_USER_UUID, userUuid)
						.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
						.method(HttpMethod.PATCH, Entity.entity(json.toString(), APPLICATION_MERGE_PATCH_JSON));

				if (response.getStatus() == Status.OK.getStatusCode()) {
					final JSONObject responseJson = new JSONObject(response.readEntity(String.class));
					responseJson.put("variables", convertJsonToYaml(responseJson.optJSONObject("variables")));
					return MarshallingUtils.unmarshal(SubscriptionTemplate.class, responseJson.toString());
				}

				metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
				throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
			}
			catch (final JAXBException ex) {
				throw new RuntimeException(ex);
			}
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API PATCH request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public SubscriptionTemplate updateSubscriptionTemplate(final String userUuid, final String uuid, final String patch) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_TEMPLATES + "/" + uuid);
		return updateSubscriptionTemplate(userUuid, target, patch);
	}

	/**
	 * Send POST request to subscription service to add template.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @param subscriptionTemplate Subscription template to create
	 * @return The created subscription template
	 */
	private SubscriptionTemplate createSubscriptionTemplate(final String userUuid, final WebTarget target, final SubscriptionTemplate subscriptionTemplate) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			try {
				final JSONObject json = new JSONObject(MarshallingUtils.marshal(subscriptionTemplate));
				json.put("variables", convertYamlToJson(json.optString("variables", null)));

				final Response response = target.request()
						.header(HEADER_USER_UUID, userUuid)
						.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
						.post(Entity.entity(json.toString(), MediaType.APPLICATION_JSON));

				if (response.getStatus() == Status.CREATED.getStatusCode()) {
					final JSONObject responseJson = new JSONObject(response.readEntity(String.class));
					responseJson.put("variables", convertJsonToYaml(responseJson.optJSONObject("variables")));
					return MarshallingUtils.unmarshal(SubscriptionTemplate.class, responseJson.toString());
				}

				metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
				throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
			}
			catch (final JAXBException ex) {
				throw new RuntimeException(ex);
			}
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API POST request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public SubscriptionTemplate createSubscriptionTemplate(final String userUuid, final SubscriptionTemplate subscriptionTemplate) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_TEMPLATES);
		return createSubscriptionTemplate(userUuid, target, subscriptionTemplate);
	}

	/**
	 * Send DELETE request to subscription service to remove template.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @return true if template was removed
	 */
	private boolean deleteSubscriptionTemplate(final String userUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(HEADER_USER_UUID, userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.delete();

			if (response.getStatus() == Response.Status.OK.getStatusCode() || response.getStatus() == Response.Status.NO_CONTENT.getStatusCode()) {
				return true;
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API DELETE request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public boolean deleteSubscriptionTemplate(final String userUuid, final String uuid) {
		final WebTarget target = this.client.target(this.baseUrl + PATH_TEMPLATES + "/" + uuid);
		return deleteSubscriptionTemplate(userUuid, target);
	}

	/**
	 * Send HEAD request to subscription service to get template count.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @return Number of subscription templates returned by subscription service
	 */
	private long getSubscriptionTemplateCount(final String userUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(HEADER_USER_UUID, userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.head();

			if (response.getStatus() == Response.Status.OK.getStatusCode() || response.getStatus() == Response.Status.NO_CONTENT.getStatusCode()) {
				return Long.valueOf(response.getHeaderString(HEADER_SUBSCRIPTION_COUNT));
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			if (response.getStatus() == Response.Status.BAD_REQUEST.getStatusCode()) {
				throw new InputValidationException();
			}
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API HEAD request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public long getSubscriptionTemplateCount(final String userUuid, final List<FilterField> filters) {
		WebTarget target = this.client.target(this.baseUrl + PATH_TEMPLATES);
		if (filters != null) {
			for (final FilterField filter : filters) {
				target = target.queryParam("filter[" + filter.getFieldName() + "[" + filter.getComparison() + "]]", UriUtils.encode(filter.getValue(), StandardCharsets.UTF_8.name()));
			}
		}

		return getSubscriptionTemplateCount(userUuid, target);
	}

	/**
	 * Send GET request to subscription service to get templates.
	 *
	 * @param userUuid User UUID
	 * @param target Target URI
	 * @return Set of subscription templates returned by subscription service
	 */
	private Set<SubscriptionTemplate> getSubscriptionTemplates(final String userUuid, final WebTarget target) {
		return metricMark.executeWithMetric(SUBSCRIPTION_API_CONNECTIONS_START_METER, SUBSCRIPTION_API_CONNECTIONS_FAILED_METER, () -> {
			final Response response = target.request()
					.header(HEADER_USER_UUID, userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.get();

			if (response.getStatus() == Response.Status.OK.getStatusCode()) {
				final Set<SubscriptionTemplate> templates = new LinkedHashSet<>();
				final String content = response.readEntity(String.class);
				if (StringUtils.isEmpty(content)) {
					return templates;
				}
				final JSONArray json = new JSONArray(content);
				json.forEach(item -> {
					((JSONObject)item).put("variables", convertJsonToYaml(((JSONObject)item).optJSONObject("variables")));
					try {
						templates.add(MarshallingUtils.unmarshal(SubscriptionTemplate.class, item.toString()));
					}
					catch (final JAXBException e) {
						throw new RuntimeException(e);
					}
				});
				return templates;
			}

			metricMark.markMeter(SUBSCRIPTION_API_REQUEST_ERRORS_METER);
			throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing subscription service API GET request: " + target.getUri(), ex);
			throw ex;
		});
	}

	@Override
	public List<SubscriptionTemplate> getSubscriptionTemplates(final String userUuid, final List<FilterField> filters, final List<String> sorting,
			final Integer offset, final Integer limit, final String... uuids) {
		WebTarget target = this.client.target(this.baseUrl + PATH_TEMPLATES);
		if (uuids.length > 0) {
			target = target.queryParam("filter[uuid[in]]", StringUtils.join(uuids, COMMA_DELIMITER));
		}
		if (filters != null) {
			for (final FilterField filter : filters) {
				target = target.queryParam("filter[" + filter.getFieldName() + "[" + filter.getComparison() + "]]", UriUtils.encode(filter.getValue(), StandardCharsets.UTF_8.name()));
			}
		}
		if (sorting != null && !sorting.isEmpty()) {
			target = target.queryParam("sort", StringUtils.join(sorting, COMMA_DELIMITER));
		}
		if (offset != null) {
			target = target.queryParam("offset", offset);
		}
		if (limit != null) {
			target = target.queryParam("limit", limit);
		}

		return ImmutableList.copyOf(getSubscriptionTemplates(userUuid, target));
	}

	/**
	 * Convert JSON to YAML.
	 *
	 * @param json JSON to covert
	 * @return YAML
	 */
	private String convertJsonToYaml(final JSONObject json) {
		if (json == null) {
			return null;
		}
		final Yaml yamlParser = new Yaml();
		@SuppressWarnings("unchecked")
		final Map<String, Object> yamlData = (Map<String, Object>) yamlParser.load(json.toString().replaceAll("/", "\\\\/"));
		return yamlData.isEmpty() ? null : yamlParser.dumpAsMap(yamlData).replaceAll("\\\\/", "/").trim();
	}

	/**
	 * Convert YAML to JSON.
	 *
	 * @param yaml YAML to convert
	 * @return JSON
	 */
	private JSONObject convertYamlToJson(final String yaml) {
		if (yaml == null) {
			return null;
		}
		if (StringUtils.isEmpty(yaml)) {
			return new JSONObject();
		}
		final Yaml yamlParser = new Yaml();
		@SuppressWarnings("unchecked")
		final Map<String, Object> yamlData = (Map<String, Object>) yamlParser.load(yaml);
		return yamlData.isEmpty() ? null : new JSONObject(yamlData);
	}

	/**
	 * Handle exception and throw message as InputValidationException.
	 *
	 * @param exception Exception to handle
	 */
	private void handleException(final Exception exception) {
		if (exception instanceof InputValidationException) {
			throw (InputValidationException) exception;
		}
		final Matcher matcher = Pattern.compile("^(.*?)(\\{(.*)\\})(.*?)$").matcher(exception.getMessage());
		if (matcher.find()) {
			final JSONObject json = new JSONObject(matcher.group(2));
			if (json.has("message")) {
				throw new InputValidationException(json.getString("message"));
			}
		}
	}

	/**
	 * Gets the authorization header value, Bearer k8stoken.
	 *
	 * @return Authorization value.
	 */
	private String getAuthorizationValue() {
		if (!StringUtils.isEmpty(tokenFile)) {
			if (new File(tokenFile).exists()) {
				try {
					LOG.debug("Acquiring authorization value from token file '{}'", tokenFile);
					return "Bearer " + FileUtils.readFileToString(new File(tokenFile), StandardCharsets.UTF_8);
				}
				catch (final IOException e) {
					LOG.error("Error reading token file: {}", e.getMessage());
				}
			}
			else {
				LOG.error("Configured token file '{}' does not exist", tokenFile);
			}
		}
		else {
			LOG.debug("Token file disabled in configuration");
		}
		return null;
	}
}
