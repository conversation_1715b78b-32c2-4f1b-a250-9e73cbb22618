package com.chilicoders.core.authentication.thycotic.api;

import org.apache.commons.lang3.StringUtils;

import com.chilicoders.core.authentication.thycotic.model.api.ThycoticServerConfigurationInterface;

public class ThycoticValidator {

	/**
	 * Validates a server configuration.
	 *
	 * @param serverConfig the configuration to validate
	 * @return an error code, if invalid or null, if valid
	 */
	public static String validateServerConfiguration(final ThycoticServerConfigurationInterface serverConfig) {
		if (serverConfig.getUserId() <= 0) {
			return "_USER_NOT_FOUND";
		}
		if (StringUtils.isEmpty(serverConfig.getName())) {
			return "_THYCOTIC_NAME_REQUIRED";
		}
		if (StringUtils.isEmpty(serverConfig.getServerURL())) {
			return "_THYCOTIC_SERVERURL_REQUIRED";
		}
		if (StringUtils.isEmpty(serverConfig.getUsername())) {
			return "_THYCOTIC_USERNAME_REQUIRED";
		}
		if (StringUtils.isEmpty(serverConfig.getPassword())) {
			return "_THYCOTIC_PASSWORD_REQUIRED";
		}
		return null;
	}
}
