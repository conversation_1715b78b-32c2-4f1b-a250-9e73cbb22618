package com.chilicoders.core.appliance.impl;

/**
 * Bandwidth limitation information holder.
 */
public class BandwidthLimitation {
	private final String destination;

	private final long limit;

	protected BandwidthLimitation(final String destination, final long limit) {
		this.destination = destination;
		this.limit = limit;
	}

	public long getLimit() {
		return limit;
	}

	public String getDestination() {
		return destination;
	}
}

