package com.chilicoders.core.appliance.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.SQLException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.chilicoders.core.appliance.api.ApplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.util.Executor;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.NoArgsConstructor;

/**
 * Class to handle information about the appliance.
 */
@SuppressFBWarnings("EI_EXPOSE_REP2")
@Component
@Profile("!test")
@NoArgsConstructor
public class ApplianceServiceImpl implements ApplianceService {
	private static String mac;

	private static final Logger LOG = LogManager.getLogger(ApplianceServiceImpl.class);

	@Autowired
	private ApplianceInformation information;

	@Autowired
	private IpService ipService;

	@Autowired
	private ConfigurationService configService;

	private static final Object lock = new Object();

	/**
	 * Constructor.
	 *
	 * @param ipService Ip service.
	 * @param configService Configuration service.
	 */
	public ApplianceServiceImpl(final IpService ipService, final ConfigurationService configService) {
		this(ipService, configService, configService.isHiabEnabled() && !configService.isKubernetesEnabled()
				? ApplianceInformation.getInstance(configService) : null);
	}

	/**
	 * Constructor.
	 *
	 * @param ipService Ip service.
	 * @param configService Configuration service.
	 * @param information Appliance information.
	 */
	public ApplianceServiceImpl(final IpService ipService, final ConfigurationService configService, final ApplianceInformation information) {
		this.information = information;
		this.ipService = ipService;
		this.configService = configService;
	}

	@Override
	public String getIpAddress() {
		if (!configService.isHiabEnabled() || configService.isKubernetesEnabled()) {
			return "";
		}
		return information.getIpAddress();
	}

	@Override
	public String getMacAddress() {
		synchronized (lock) {
			if (!configService.isHiabEnabled()) {
				return "";
			}
			if (configService.isKubernetesEnabled()) {
				throw new RuntimeException("Local command execution disabled on kubernetes");
			}
			if (mac == null) {
				mac = Executor.execute(configService, "/usr/bin/connection.sh", "getMAC").getValue().trim();
			}
			return mac;
		}
	}

	@Override
	public boolean isHiabIp(final String ip, final long scannerId) throws SQLException {
		if (!configService.isHiabEnabled() || configService.isKubernetesEnabled()) {
			return false;
		}

		if (scannerId != 0) {
			return false;
		}

		String address = ip;
		if (ipService.isHostname(address)) {
			try {
				address = InetAddress.getByName(address).getHostAddress();
			}
			catch (final RuntimeException | UnknownHostException e) {
				LOG.info("Could not check if it is a hiab server", e);
			}
		}

		// TODO: Kubernetes, Should we skip something
		if (information != null && information.isLocalAddress(address)) {
			return true;
		}

		return false;
	}
}
