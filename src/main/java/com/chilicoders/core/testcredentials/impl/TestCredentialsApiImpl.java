package com.chilicoders.core.testcredentials.impl;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.metrics.api.MetricsService;
import com.chilicoders.core.testcredentials.api.TestCredentialsApi;
import com.chilicoders.core.testcredentials.api.model.TestCredentialsType;
import com.chilicoders.rest.RestClientFactory;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.MetricOnProcessingException;
import com.chilicoders.util.RuntimeEnvironment;
import com.chilicoders.util.SslUtils;

@Component
public class TestCredentialsApiImpl implements TestCredentialsApi {
	private static final Logger LOG = LogManager.getLogger(TestCredentialsApiImpl.class);
	private static final String TEST_CREDENTIALS_API_CONNECTIONS_START_METER = "test.credentials.api.connections";
	private static final String TEST_CREDENTIALS_API_REQUEST_ERRORS_METER = "test.credentials.api.request.errors";
	private static final String TEST_CREDENTIALS_API_CONNECTIONS_FAILED_METER = "test.credentials.api.connections.failed";
	private static final String HEADER_CUSTOMER_UUID = "X-Customer-UUID";
	private static final String HEADER_USER_UUID = "X-User-UUID";
	private final Client client;
	private final String baseUrl;
	private final MetricOnProcessingException metricMark;
	private final String tokenFile;

	@Autowired
	public TestCredentialsApiImpl(final ConfigurationService configService, final MetricsService metricsService) {
		this(configService, metricsService, configService.getProperty(ConfigKeys.ConfigurationKey.test_credentials_api_url));
	}

	/**
	 * Constructor.
	 *
	 * @param configService configuration service.
	 * @param metricsService metrics service.
	 * @param testCredentialsApiUrl test-credentials service url.
	 */
	public TestCredentialsApiImpl(final ConfigurationService configService, final MetricsService metricsService, final String testCredentialsApiUrl) {
		this.client = RestClientFactory.createClient(configService, SslUtils.getO24DefaultSslContext());
		this.baseUrl = testCredentialsApiUrl + "/rest/v0/";
		this.metricMark = () -> metricsService;
		tokenFile = RuntimeEnvironment.isKubernetesEnabled() || configService.getProperty(ConfigKeys.ConfigurationBooleanKey.kubernetes_enabled) ? configService.getProperty(
				ConfigKeys.ConfigurationKey.kubernetes_token_path) : null;
	}

	/**
	 * Send request to test-credentials service.
	 *
	 * @param userUuid User uuid.
	 * @param customerUuid Customer uuid.
	 * @param credentials The credentials to test.
	 * @param type A {@link TestCredentialsType} type.
	 * @return Credentials test result.
	 */
	public String testCredentials(final String userUuid, final String customerUuid, final String credentials, final TestCredentialsType type) {
		LOG.debug("Test credentials: {}, endpoint: {}, user uuid: {}, customer uuid: {}", credentials, type.getEndpoint(), userUuid, customerUuid);
		final WebTarget target = client.target(this.baseUrl + type.getEndpoint());
		return metricMark.executeWithMetric(TEST_CREDENTIALS_API_CONNECTIONS_START_METER, TEST_CREDENTIALS_API_CONNECTIONS_FAILED_METER, () -> {
			try (final Response response = target.request()
					.header(HEADER_CUSTOMER_UUID, customerUuid)
					.header(HEADER_USER_UUID, userUuid)
					.header(HttpHeaders.AUTHORIZATION, getAuthorizationValue())
					.post(Entity.entity(credentials, MediaType.APPLICATION_JSON))) {
				if (response.getStatus() == Response.Status.OK.getStatusCode()) {
					return response.readEntity(String.class);
				}

				metricMark.markMeter(TEST_CREDENTIALS_API_REQUEST_ERRORS_METER);
				throw new ProcessingException(StringUtils.join("Status ", response.getStatus(), ": ", response.readEntity(String.class)));
			}
		}, ex -> {
			handleException(ex);
			LOG.error("Error processing test-credentials service API POST request: {}", target.getUri(), ex);
			throw ex;
		});
	}

	/**
	 * Handle exception and throw message as InputValidationException.
	 *
	 * @param exception Exception to handle
	 */
	private void handleException(final Exception exception) {
		if (exception instanceof InputValidationException) {
			throw (InputValidationException) exception;
		}
		final Matcher matcher = Pattern.compile("^(.*?)(\\{(.*)\\})(.*?)$").matcher(exception.getMessage());
		if (matcher.find()) {
			final JSONObject json = new JSONObject(matcher.group(2));
			if (json.has("message")) {
				throw new InputValidationException(json.getString("message"));
			}
		}
	}

	/**
	 * Gets the authorization header value, Bearer k8s token.
	 *
	 * @return Authorization value.
	 */
	private String getAuthorizationValue() {
		if (!StringUtils.isEmpty(tokenFile)) {
			if (new File(tokenFile).exists()) {
				try {
					LOG.debug("Acquiring authorization value from token file '{}'", tokenFile);
					return "Bearer " + FileUtils.readFileToString(new File(tokenFile), StandardCharsets.UTF_8);
				}
				catch (final IOException e) {
					LOG.error("Error reading token file: {}", e.getMessage());
				}
			}
			else {
				LOG.error("Configured token file '{}' does not exist", tokenFile);
			}
		}
		else {
			LOG.debug("Token file disabled in configuration");
		}
		return null;
	}
}
