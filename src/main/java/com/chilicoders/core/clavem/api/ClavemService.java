package com.chilicoders.core.clavem.api;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;

public interface ClavemService {
	/**
	 * Replaces the SSL context used for the requests to Clavem service with one using the certificates from the encryption/decryption request.
	 *
	 * @param request The encryption/decryption request
	 */
	void setCertificates(final EncryptionDecryptionRequest request) throws Exception;

	/**
	 * Get a list of certificates from the Clavem service.
	 *
	 * @return A {@link String} with "CERTIFICATE" PEM blocks
	 */
	String getCertificates();

	/**
	 * Uses the Clavem service to decrypt a key.
	 *
	 * @param encryptedKey An "ENCRYPTED KEY" PEM block to decrypt
	 * @return A byte array of the decrypted symmetric key
	 */
	byte[] decryptKey(final String encryptedKey);

	/**
	 * Update the setting for the Clavem service.
	 *
	 * @param configService Configuration service
	 */
	void updateSettings(final ConfigurationService configService);
}
