package com.chilicoders.core.metrics.api;

import java.io.IOException;

/**
 * Service for handling metrics.
 */
public interface MetricsService {

	/**
	 * Starts a timer for a certain action. The timer is stopped by using the stopTimer method on the Context.
	 *
	 * @param clazz the class for which we are doing the measurement
	 * @param timedAction name of the timed action
	 * @return a timer context, used to stop the timer
	 */
	Context startTimer(final Class<?> clazz, final String timedAction);

	/**
	 * Registers a value for a metric.
	 *
	 * @param clazz the class for which we register the metric
	 * @param name the name of the metric
	 * @param value the value of the metric
	 */
	void registerMetricValue(final Class<?> clazz, final String name, long value);

	/**
	 * Registers a value for a metric.
	 *
	 * @param name the name of the metric
	 * @param valueCollector value collector
	 * @param <T> Class of gathered data
	 */
	<T> void registerValue(final String name, final ValueCollector<T> valueCollector);

	/**
	 * Register a meter for a metric. Meters are used for measuring rates of events, shows mean values and 1, 5 and 15 minutes averages.
	 *
	 * @param name the name of the metric
	 * @return the meter for this metric
	 */
	Meter registerMeter(final String name);

	/**
	 * Register a histogram for a metric. A histogram measures the statistical distribution of values in a stream of data.
	 *
	 * @param name the name of the metric
	 * @return the histogram for this metric
	 */
	Histogram registerHistogram(final String name);

	/**
	 * Send heartbeat. This method will immediately send a heartbeat and not wait for a synchronized batch send.
	 *
	 * @param name Name of heartbeat.
	 */
	void sendHeartBeat(final String name) throws IllegalStateException, IOException;

	/**
	 * Should be called when system is shutting down to terminate all threads.
	 */
	void close();

	/**
	 * Create a counter metrics.
	 *
	 * @param name Name
	 * @return Metrics
	 */
	Counter counter(final String name);

	/**
	 * Initialize the graphite metrics reporter. The reporter sends metric data to a graphite server.
	 * To use this ConfigurationKey.graphite_address must be defined & graphit server should be running otherwise no reporter will be started.
	 * Information will be sent once per minute.
	 */
	void initializeMetricsReporter();
}
