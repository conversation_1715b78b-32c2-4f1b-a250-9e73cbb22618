package com.chilicoders.core.assets.impl.jpa;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao;
import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao.AssetIdentifierQuery.AssetIdentifierQueryBuilder;
import com.chilicoders.core.assets.api.model.AssetIdentifier;
import com.chilicoders.core.reporting.impl.jpa.repository.AssetIdentifierRepository;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierLinkType;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.Source;
import com.chilicoders.util.StringUtils;

@Component
public class AssetIdentifierDaoImpl implements AssetIdentifierDao {
	@Autowired
	private AssetIdentifierRepository assetIdentifierRepository;

	@Autowired
	private NativeStatementExecutor statementExecutor;

	@PersistenceContext
	private EntityManager entityManager;

	@Override
	public AssetIdentifierInterface getAssetIdentifier(final String name, final AssetIdentifierType type, final Integer customerId, final Integer scannerId,
													   final Integer linkedAssetIdentifierId, final Source[] notFromSources, final Source source,
													   final Integer hostIdentifierId) throws SQLException {
		final AssetIdentifierQuery assetIdentifierQuery = AssetIdentifierQuery.builder()
				.name(name)
				.type(type)
				.customerId(customerId)
				.scannerId(scannerId)
				.linkedAssetIdentifierId(linkedAssetIdentifierId)
				.notFromSources(notFromSources)
				.source(source)
				.linkedHostIdentifierId(hostIdentifierId)
				.build();
		final Pair<StringBuilder, List<Object>> queryParam = buildQuery(assetIdentifierQuery, true);

		final List<? extends AssetIdentifierInterface> identifiers = statementExecutor.getObjectList(
				new NativeSqlStatement(queryParam.getLeft().toString(), queryParam.getRight().toArray()), AssetIdentifierImpl.class);
		return identifiers.isEmpty() ? null : identifiers.get(0);
	}

	@Override
	public AssetIdentifierInterface getAssetIdentifier(final AssetIdentifier identifierData, final Integer linkedAssetIdentifierId,
													   final Source[] notFromSources, final Source source, final Integer hostIdentifierId) throws SQLException {
		final AssetIdentifierQueryBuilder assetIdentifierQuery = AssetIdentifierQuery.builder()
				.name(identifierData.getName())
				.type(identifierData.getType())
				.customerId(identifierData.getCustomerId())
				.scannerId(identifierData.getScannerId())
				.linkedAssetIdentifierId(linkedAssetIdentifierId)
				.notFromSources(notFromSources)
				.source(source)
				.linkedHostIdentifierId(hostIdentifierId);

		if (!StringUtils.isEmpty(identifierData.getAgentUuid())) {
			assetIdentifierQuery.extraFilter("properties->>'uuid' = ?");
			assetIdentifierQuery.extraParams(Arrays.asList(identifierData.getAgentUuid()));
		}

		final Pair<StringBuilder, List<Object>> queryParam = buildQuery(assetIdentifierQuery.build(), true);

		final List<? extends AssetIdentifierInterface> identifiers = statementExecutor.getObjectList(
				new NativeSqlStatement(queryParam.getLeft().toString(), queryParam.getRight().toArray()), AssetIdentifierImpl.class);
		return identifiers.isEmpty() ? null : identifiers.get(0);
	}

	@Override
	public List<? extends AssetIdentifierInterface> getAssetIdentifiers(final AssetIdentifierQuery query) throws SQLException {
		final Pair<StringBuilder, List<Object>> queryParam = buildQuery(query, true);
		return statementExecutor.getObjectList(new NativeSqlStatement(queryParam.getLeft().toString(), queryParam.getRight().toArray()), AssetIdentifierImpl.class);
	}

	@Override
	public void linkAssetWithAccount(final Integer id, final Integer accountId) throws SQLException {
		if (!isAccountLinked(id, accountId)) {
			statementExecutor.execute(new NativeSqlStatement("INSERT INTO assetidentifier_account (assetidentifierid, accountid) VALUES (?, ?)", id, accountId));
		}
	}

	@Override
	public boolean isLinked(final Integer assetIdentifierId1, final Integer assetIdentifierId2, final AssetIdentifierLinkType type) throws SQLException {
		return statementExecutor.getLong(new NativeSqlStatement(
				"SELECT COUNT(assetidentifierid1) FROM assetidentifier_assetidentifier WHERE type = ? AND (assetidentifierid1 = ? AND assetidentifierid2 = ? OR assetidentifierid1 = ? AND assetidentifierid2 = ?)",
				type,
				assetIdentifierId1, assetIdentifierId2, assetIdentifierId2, assetIdentifierId1)) > 0;
	}

	@Override
	public AssetIdentifierInterface createAssetIdentifier(final Integer createdById, final Integer customerId, final AssetIdentifierType type, final String name) {
		return createAssetIdentifier(createdById, customerId, type, name, null);
	}

	@Override
	public AssetIdentifierInterface createAssetIdentifier(final Integer createdById, final Integer customerId, final AssetIdentifierType type,
														  final String name, final String presentableName) {
		return new AssetIdentifierImpl(createdById, customerId, type, name, presentableName);
	}

	@Override
	public Integer saveAssetIdentifier(final AssetIdentifierInterface identifier) {
		if (identifier.getId() == null || assetIdentifierRepository.getById(identifier.getId()) == null) {
			entityManager.persist((AssetIdentifierImpl) identifier);
		}
		else {
			entityManager.merge((AssetIdentifierImpl) identifier);
		}
		entityManager.flush();
		entityManager.clear();
		return identifier.getId();
	}

	@Override
	public List<? extends AssetIdentifierInterface> getByIds(final Integer[] assetIdentifierIds) {
		return assetIdentifierRepository.findByIds(Arrays.stream(assetIdentifierIds).filter(Objects::nonNull).toArray(Integer[]::new));
	}

	@Override
	public AssetIdentifierInterface getById(final Integer assetIdentifierId) throws SQLException {
		return assetIdentifierRepository.findById(assetIdentifierId).orElse(null);
	}

	private boolean isAccountLinked(final Integer assetIdentifierId, final Integer accountId) throws SQLException {
		return statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(assetidentifierid) FROM assetidentifier_account WHERE assetidentifierid = ? AND accountid = ?",
				assetIdentifierId, accountId)) > 0;
	}
}
