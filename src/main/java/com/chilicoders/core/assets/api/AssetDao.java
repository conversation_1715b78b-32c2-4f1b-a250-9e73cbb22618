package com.chilicoders.core.assets.api;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.AssetLinkInterface;
import com.chilicoders.model.Source;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;

public interface AssetDao {
	/**
	 * Get asset by uuid.
	 *
	 * @param assetUuid UUID.
	 * @return Asset from the database or null if not existing.
	 */
	AssetInterface getByUuid(final String assetUuid) throws SQLException;

	/**
	 * Get asset links from asset id and assetidentifiers.
	 *
	 * @param assetid Asset id
	 * @param assetIdentifierIds An array of asset identifier ids.
	 * @return A list of asset links.
	 */
	List<? extends AssetLinkInterface> getAssetLinks(final Integer assetid, final Integer[] assetIdentifierIds) throws SQLException;

	/**
	 * Get asset link by asset id and asset identifier id.
	 *
	 * @param assetId Asset id.
	 * @param assetIdentifierId Asset identifier id.
	 * @return Asset link.
	 */
	AssetLinkInterface getAssetLinkByAssetIdAndAssetIdentifierId(final Integer assetId, final Integer assetIdentifierId) throws SQLException;

	/**
	 * Create a new asset object.
	 *
	 * @return New asset object, not persisted to the database.
	 */
	AssetInterface createAsset();

	/**
	 * Save an asset to the database.
	 *
	 * @param asset Asset to save.
	 * @return Id of saved asset.
	 */
	Integer saveAsset(final AssetInterface asset) throws SQLException;

	/**
	 * Create a new asset link object.
	 *
	 * @return New object, not persisted to the database.
	 */
	AssetLinkInterface createAssetLink();

	/**
	 * Save asset link to database.
	 *
	 * @param assetLink Asset linkn to persist.
	 */
	void saveAssetLink(final AssetLinkInterface assetLink) throws SQLException;

	/**
	 * Get a list of assets by ids.
	 *
	 * @param ids An array of ids.
	 * @return List of assets.
	 */
	List<? extends AssetInterface> getByIds(final Integer[] ids) throws SQLException;

	/**
	 * Create a new asset identifier, not persisted to the database.
	 *
	 * @param createdById Id of creator.
	 * @param customerId Customer id.
	 * @param type Asset identifier type.
	 * @param name Name of identifier.
	 * @return Asset identifier object.
	 */
	AssetIdentifierInterface createAssetIdentifier(final Integer createdById, final Integer customerId, final AssetIdentifierType type, String name);

	/**
	 * Load an asset based on id.
	 *
	 * @param assetId Id to load.
	 * @return Asset from database.
	 */
	AssetInterface getById(Integer assetId) throws SQLException;

	/**
	 * Get Assets.
	 *
	 * @param query Assets query
	 * @return List of assets
	 */
	List<? extends AssetInterface> getAssets(AssetQuery query) throws SQLException;

	@Getter
	@Builder
	@SuppressFBWarnings({"EI_EXPOSE_REP2", "EI_EXPOSE_REP"})
	public static class AssetQuery {
		private Integer[] ids; // Array of ids
		@NotNull
		private Integer customerId; // Customer id
		private Integer[] tagIds; // Tag ids
		private Source[] notFromSources; // Sources that should not be included when getting assets
		private String extraFilter;
		private List<Object> extraParams;
		private String orderBy;
	}

	/**
	 * Create query to get assets.
	 *
	 * @param query Query
	 * @param addSelect true if select should be added to query
	 * @return Pair of query and parameters.
	 */
	default Pair<StringBuilder, List<Object>> buildQuery(final AssetQuery query, final boolean addSelect) {
		final StringBuilder sql = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		if (addSelect) {
			sql.append("SELECT * FROM assetsview WHERE ");
		}

		sql.append("customerid = ? AND deleted IS NULL");
		params.add(query.getCustomerId());

		if (query.getIds() != null) {
			StringUtils.concatenateFilters(sql, "id = ANY(?)");
			params.add(query.getIds());
		}

		if (query.getTagIds() != null) {
			StringUtils.concatenateFilters(sql, "(SELECT array_agg(id) FROM (SELECT (jsonb_array_elements(tags)->>'id')::INTEGER AS id) x) @> ?");
			params.add(query.getTagIds());
		}

		if (query.getNotFromSources() != null) {
			StringUtils.concatenateFilters(sql, "NOT source && ?::source[]");
			params.add(query.getNotFromSources());
		}

		if (query.getExtraFilter() != null) {
			StringUtils.concatenateFilters(sql, query.getExtraFilter());
			params.addAll(query.getExtraParams());
		}

		if (query.getOrderBy() != null) {
			sql.append(" ORDER BY ").append(query.getOrderBy());
		}

		return Pair.of(sql, params);
	}
}
