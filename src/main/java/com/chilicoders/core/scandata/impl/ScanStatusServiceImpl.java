package com.chilicoders.core.scandata.impl;

import java.sql.SQLException;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chilicoders.core.scandata.api.ScanStatusService;
import com.chilicoders.core.scandata.api.model.ScanStatus;
import com.chilicoders.core.scandata.api.model.ScanStatusInterface;
import com.chilicoders.core.scandata.impl.jpa.repository.AppcheckScanDataRepository;
import com.chilicoders.core.scandata.impl.jpa.repository.RunningVerifyScanRepository;
import com.chilicoders.core.scandata.impl.jpa.repository.ScanStatusRepository;
import com.chilicoders.core.scheduling.api.model.RunningVerifyScan;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.integrations.appcheck.model.AppcheckScanDataInterface;

@Service
public class ScanStatusServiceImpl implements ScanStatusService {

	@Autowired
	private ScanStatusRepository scanStatusRepository;

	@Autowired
	private RunningVerifyScanRepository verifyScanRepository;

	@Autowired
	private AppcheckScanDataRepository appcheckScanDataRepository;

	@Autowired
	private NativeStatementExecutor statementExecutor;

	@PersistenceContext
	private EntityManager entityManager;

	@Override
	public ScanStatusInterface writeScanStatus(final ScanStatusInterface scanStatus) throws SQLException {
		if (scanStatus.getId() < 1) {
			scanStatus.setId(statementExecutor.getLong(new NativeSqlStatement("SELECT nextval('tscanstatuss_seq')")));
		}
		return scanStatusRepository.save((ScanStatus) scanStatus);
	}

	@Override
	public List<? extends ScanStatusInterface> fetchInvalidScannerScans(final long scheduleId) {
		return scanStatusRepository.fetchInvalidScannerScans(scheduleId);
	}

	@Override
	public void deleteRunningVerifyById(final long userId, final long id) {
		scanStatusRepository.deleteByUserIdAndId(userId, id);
	}

	@Override
	public List<RunningVerifyScan> getRunningVerifyByUserIdAndId(final long userId, final long id) {
		return verifyScanRepository.getByUserIdAndId(userId, id);
	}

	@Override
	public ScanStatusInterface createScanStatus() {
		return new ScanStatus();
	}

	@Override
	public void deleteScanStatusById(final long id) {
		if (scanStatusRepository.existsById(id)) {
			scanStatusRepository.deleteById(id);
		}
	}

	@Override
	public void executeBatch() {
		entityManager.flush();
		entityManager.clear();
	}

	@Override
	public ScanStatusInterface getScanWithReadyReport() {
		final Long id = scanStatusRepository.getScanWithReadyReportId();
		if (id != null) {
			return scanStatusRepository.findById(id).orElseThrow(() -> new IllegalStateException("Could not find scanstatus for report"));
		}
		return null;
	}

	/**
	 * Gets the first entry in the appcheckscandata table that has a scan result.
	 * Finds the customer ID of the first unlocked row.
	 * Locks all rows with that customer ID that has a scan result.
	 * Returns the row with the lowest id of the locked rows for processing.
	 * The assumption is that com.chilicoders.integrations.appcheck.AppcheckScanFetcher inserts rows in the order they should be processed.
	 *
	 * @return The AppcheckScanData object to process
	 */
	@Override
	public AppcheckScanDataInterface getAppcheckScanData() {
		final List<Long> ids = appcheckScanDataRepository.findAllScanIdsByScanResultIsNotNullByUnlockedCustomerIdOrderByIdForUpdate();
		final Long id = ids == null || ids.isEmpty() ? null : ids.get(0);
		return id == null ? null : appcheckScanDataRepository.findById(id).orElse(null);
	}

	@Override
	public boolean isVerifyScanCompleted(final long userId, final long id) {
		return !verifyScanRepository.hasVerifyScanRunning(userId, id);
	}

	@Override
	public long createScanStatusId() throws SQLException {
		return statementExecutor.getLong(new NativeSqlStatement("SELECT nextval('tscanstatuss_seq')"));
	}
}
