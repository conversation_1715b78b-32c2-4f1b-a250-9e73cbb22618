package com.chilicoders.core.scandata.impl.jpa;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.xml.bind.JAXBException;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.model.ScanLogEntryInterface;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.util.MarshallingUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Entity(name = "scanlogs")
@TypeDefs(@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class))
public class PortalScanlog implements ScanLogEntryInterface {
	@Column
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Getter
	private Integer id;

	@Column
	@Setter
	@Getter
	@Type(type = "enum_type")
	@Enumerated(value = EnumType.STRING)
	private ScanLogStatus status;

	@Column
	@Getter
	@Setter
	private String statusDetails;

	@Column
	@Getter
	@Setter
	private Integer assetIdentifierId;

	@Column
	@Getter
	@Setter
	private Integer scanConfigurationId;

	@Column
	@Getter
	@Setter
	private Integer assetId;

	@Column
	@Getter
	@Setter
	private Integer scannerId;

	@Column
	@Getter
	@Setter
	private String schema;

	@Column
	@Getter
	private String attacker;

	@Column
	@Getter
	@Setter
	private boolean scanless;

	@Column
	@Getter
	@Setter
	private Integer parentId;

	@Column
	@Setter
	@Getter
	private Integer workflowId;

	@Column
	@Setter
	@Getter
	private Instant started;

	@Column
	@Setter
	@Getter
	private Instant ended;

	@Column(name = "template")
	@Type(type = "enum_type")
	@Enumerated(value = EnumType.STRING)
	private ScanTemplate source;

	@Column
	@Setter
	private Instant latestRuleDate;

	@Column
	@Setter
	@Getter
	private Integer customerId;

	@Column
	@Getter
	@Setter
	private String jobId;

	@Column
	@Getter
	@Setter
	private Integer scheduleId;

	@Column
	@Getter
	@Setter
	private Integer createdById;

	@Column
	@Getter
	@Setter
	private Instant expectedStart;

	@Column
	@Getter
	@Setter
	private Instant expectedEnd;

	@Column
	@Type(type = "jsonb")
	private String authentication;

	@Column
	@Type(type = "string-array")
	@Getter
	@Setter
	@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
	private String[] targets;

	@Column
	@Type(type = "string-array")
	@Getter
	@Setter
	@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
	private String[] virtualHosts;

	@Column
	@Getter
	@Setter
	private Integer invocationId;

	@Override
	public void setTemplate(final ScanTemplate template) {
		source = template;
	}

	@Override
	public ScanTemplate getTemplate() {
		return source;
	}

	@Override
	public String getAuthenticationString() {
		return authentication;
	}

	@Override
	public Authentication[] getAuthentication() throws JAXBException {
		return this.authentication == null ? null : MarshallingUtils.unmarshalList(Authentication.class, this.authentication).toArray(new Authentication[0]);
	}

	@Override
	public void setAuthentication(final Authentication[] authentication) throws JAXBException {
		this.authentication = authentication == null ? null : MarshallingUtils.marshalList(Authentication.class, Arrays.asList(authentication));
	}
}
