package com.chilicoders.core.scandata.impl.jpa.repository;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.chilicoders.core.scandata.api.model.CorePreference;

public interface CorePreferenceRepository extends CrudRepository<CorePreference, Long> {
	@Modifying
	@Transactional
	@Query(value = "DELETE FROM coreprefs")
	void deletePrefs();
}
