package com.chilicoders.core.scandata.impl.jpa.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.chilicoders.core.scandata.impl.jpa.ScanPolicyImpl;

/**
 * Repository for dealing with scan policies.
 */
public interface ScanPolicyRepository extends CrudRepository<ScanPolicyImpl, Long> {
	@Query("SELECT p FROM tsavedscanprefs p WHERE id = ?1 AND (userId = ?2 OR global = TRUE)")
	ScanPolicyImpl findOneByXidAndUserId(final long id, final long userId);

	ScanPolicyImpl findOneByTargetOverrideAndUserId(long targetId, long userId);
}
