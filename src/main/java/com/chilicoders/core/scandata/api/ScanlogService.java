package com.chilicoders.core.scandata.api;

import java.sql.SQLException;
import java.util.List;

import com.chilicoders.core.scandata.api.model.DataType;
import com.chilicoders.core.scandata.api.model.ScanDataInterface;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.db.objects.api.ScanLogInterface;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.model.ScanLogEntryInterface;

/**
 * Service for dealing with scanlogs.
 */
public interface ScanlogService {
	/**
	 * Get scan log entries.
	 *
	 * @param scanjobId Scan job id.
	 * @param scheduleId Schedule id.
	 * @param target Target.
	 * @return List of scanlogs.
	 */
	List<? extends ScanLogInterface> getScanLogs(final long scanjobId, final long scheduleId, final String target) throws SQLException;

	List<? extends ScanLogInterface> getScanLogs(final NativeSqlFragment scanFilter) throws SQLException;

	/**
	 * Get scan log entry.
	 *
	 * @param scanjobId Scan job id.
	 * @param targetId Target id.
	 * @return List of scanlogs.
	 */
	ScanLogInterface getScanLog(final long scanjobId, final long targetId) throws SQLException;

	/**
	 * Save a scanlog entry to the database.
	 *
	 * @param scanlog Scanlog entry.
	 * @param scanService Scan service for the scan.
	 * @param discovery True if the scan was a discovery.
	 * @return ID of saved scanlog entry.
	 */
	long writeScanLog(final ScanLogInterface scanlog, final ScanServiceType scanService, final boolean discovery) throws SQLException;

	/**
	 * Save a scanlog entry for portal (table scanlogs) to the database.
	 *
	 * @param scanlog Scan log entry to save.
	 * @return Id of the entry.
	 */
	int writeScanLog(final ScanLogEntryInterface scanlog) throws SQLException;

	/**
	 * Fetch a list of scanless scans to start.
	 *
	 * @return Scanlog entries to start.
	 */
	List<? extends ScanLogInterface> getScanlessScansToStart() throws SQLException;

	/**
	 * Gets the count of scanless scans to start.
	 *
	 * @return Count
	 */
	long getScanlessScanToStartCount() throws SQLException;

	/**
	 * Create a new scanlog entry.
	 *
	 * @return Empty scanlog entry.
	 */
	ScanLogInterface createScanlog();

	/**
	 * Create a new portal scanlog entry.
	 *
	 * @return Empty portal scanlog entry.
	 */
	ScanLogEntryInterface createPortalScanLog();

	/**
	 * Fetch a new id from the database.
	 *
	 * @return Id generated.
	 */
	long generateScanLogId() throws SQLException;

	/**
	 * Fetch a scanjob entry that no longer has any scans running, this is used to fetch the job when the final scan is completed.
	 *
	 * @return Scan job entry.
	 */
	ScanLogInterface getCompletedScanJob() throws SQLException;

	/**
	 * Fetch a portal scanlog entry from the scanlogs table.
	 *
	 * @param id Id of scanlog entry.
	 * @return Scan log entry.
	 */
	ScanLogEntryInterface getPortalScanLog(int id) throws SQLException;

	void updateScanLogAuthentication(final ScanLogEntryInterface scanLog) throws SQLException;

	boolean hasStillNotCompletedScanLogs(Integer scanConfigurationId, Integer id) throws SQLException;

	/**
	 * Create a new scan data item, not persisted yet.
	 *
	 * @return New scan data item.
	 */
	ScanDataInterface createScanData();

	/**
	 * Persist scan data to database.
	 *
	 * @param scanData Scan data to persist.
	 */
	void saveScanData(ScanDataInterface scanData) throws SQLException;

	ScanDataInterface getScanData(final Integer scanId, final DataType type) throws SQLException;
}
