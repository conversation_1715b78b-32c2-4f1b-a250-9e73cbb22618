package com.chilicoders.core.scandata.api;

import java.sql.SQLException;
import java.util.List;

import org.apache.logging.log4j.LogManager;

import com.chilicoders.core.agents.api.AgentsService;
import com.chilicoders.core.auditing.api.AuditingService;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.db.ArrayUtil;
import com.chilicoders.db.objects.api.ScheduleInterface;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.model.ScanConfigurationInterface;
import com.chilicoders.util.StringUtils;

/**
 * Service dealing with schedules.
 */
public interface ScheduleService {
	/**
	 * Get a schedule based on an id.
	 *
	 * @param id Id of schedule.
	 * @return Schedule information.
	 */
	ScheduleInterface getSchedule(final long id) throws SQLException;

	/**
	 * Get a schedule based on an id.
	 *
	 * @param id Id of schedule.
	 * @param userId User id.
	 * @return Schedule information.
	 */
	ScheduleInterface getSchedule(final long id, final long userId) throws SQLException;

	/**
	 * Get a schedule based on an id.
	 *
	 * @param xid Id of schedule.
	 * @param userId User id.
	 * @param subUserId subuser id.
	 * @return Schedule information.
	 */
	ScheduleInterface getSchedule(long userId, Long subUserId, long xid) throws SQLException;

	/**
	 * List schedules based on criteria.
	 *
	 * @param user User loading the schedules.
	 * @param criteria Criteria to match.
	 * @return List of schedules.
	 */
	List<? extends ScheduleInterface> listSchedules(final UserDetails user, final ScheduleListCriteria criteria) throws SQLException;

	/**
	 * Returns a schedule that should have started but did not start in time. The entry is locked for update so
	 * if some other service calls this method the returned entry will be skipped until is no longer locked.
	 *
	 * @return Schedule
	 */
	ScheduleInterface getTimedOutSchedule() throws SQLException;

	/**
	 * Save a scan schedule to the database.
	 *
	 * @param schedule Schedule to save.
	 */
	void saveSchedule(ScheduleInterface schedule) throws SQLException;

	/**
	 * Fetch a scan schedule that is scheduled to be started.
	 *
	 * @return Scan schedule.
	 */
	ScheduleInterface getScheduleToStart() throws SQLException;

	/**
	 * Fetch a count of scan schedules that should be started.
	 *
	 * @return Scan schedule count.
	 */
	long getSchedulesToStartCount() throws SQLException;

	/**
	 * Fetch a count of targets in schedules to be started, not including discovery scans.
	 *
	 * @return Target count.
	 */
	int getTargetsToStartCount() throws SQLException;

	/**
	 * Fetch a count of targets in the schedule, will currently only calculate based on scan mode so only existing targets.
	 *
	 * @param scheduleId Schedule id.
	 * @return Target count in schedule.
	 */
	int getScheduleTargetCount(long scheduleId) throws SQLException;

	/**
	 * Get agent schedules.
	 *
	 * @return List of schedules
	 */
	List<? extends ScheduleInterface> getAgentSchedules() throws SQLException;

	/**
	 * Get agent scan configuration.
	 *
	 * @return List of scan configurations
	 */
	List<? extends ScanConfigurationInterface> getAgentScanConfigurations() throws SQLException;

	/**
	 * Delete a schedule.
	 *
	 * @param schedule Schedule to delete.
	 * @param auditNote A note added to the audit.
	 * @param user User that deletes schedule.
	 * @return True if deleted, false otherwise.
	 */
	boolean deleteSchedule(final ScheduleInterface schedule, final String auditNote, final UserDetails user) throws SQLException;

	/**
	 * Delete a schedule. This method is a helper method for the actual implementations. A user of the interface should most likely call
	 *
	 * @param schedule Schedule to delete.
	 * @param auditNote A note added to the audit.
	 * @param user User that deletes schedule.
	 * @param statementExecutor SQL statement executor.
	 * @param userService User service.
	 * @param targetService Target service.
	 * @param agentsService Agents service.
	 * @param auditService Audit service.
	 * @return True if deleted, false otherwise.
	 * @see ScheduleService#deleteSchedule(ScheduleInterface, String, UserDetails)
	 */
	default boolean deleteSchedule(final ScheduleInterface schedule, final String auditNote, final UserDetails user, final NativeStatementExecutor statementExecutor,
								   final UserService userService, final TargetService targetService, final AgentsService agentsService, final AuditingService auditService)
			throws SQLException {
		final ScheduleListCriteria criteria = ScheduleListCriteria.builder().id(schedule.getId()).build();

		final List<? extends ScheduleInterface> schedules = listSchedules(user, criteria);
		if (schedules.size() != 1) {
			return false;
		}
		final ScheduleInterface loaded = schedules.get(0);
		if (loaded != null) {
			if (loaded.isPci() && StringUtils.isEmpty(auditNote) && user.needAuditTargetManagement()) {
				return false;
			}
			statementExecutor.execute(
					new NativeSqlStatement("UPDATE tscanstatuss SET bstop=1, vcstatus=?, bsync=1 WHERE NOT vcservice = ANY(?) AND xsoxid=?", ScanStatuses.Stopping.toString(),
							new String[] {
									ScanServiceType.Was.toString(),
									ScanServiceType.WasDiscovery.toString(),
									ScanServiceType.WasX.toString(),
									ScanServiceType.AppsecScale.toString(),
									ScanServiceType.DOCKER_SCAN.toString(),
									ScanServiceType.DOCKER_DISCOVERY.toString(),
									ScanServiceType.CLOUDSEC.toString(),
									ScanServiceType.NETWORK_DISCOVERY.toString(),
									ScanServiceType.CLOUD_DISCOVERY.toString(),
									ScanServiceType.NETWORK_SCAN.toString()
							}, loaded.getId()));
			if (loaded.isPci() && !StringUtils.isEmpty(loaded.getGroupList())) {
				final Long[] ids = ArrayUtil.createIdArray(loaded.getGroupList());
				final UserDetails mainUser = userService.getUserDetails(loaded.getUserId(), false);
				for (final Long id : ids) {
					if (!targetService.removeTargetGroup(mainUser, id, auditNote)) {
						return false;
					}
				}
			}

			if (loaded.getAgentScheduleId() != null) {
				try {
					agentsService.deleteSchedule(user.getCustomerUuid(), loaded.getAgentScheduleId());
				}
				catch (final Exception ex) {
					LogManager.getLogger(ScheduleService.class)
							.error("Tried to delete agent schedule "
									+ loaded.getAgentScheduleId()
									+ " when removing schedule "
									+ loaded.getId()
									+ " but an exception was thrown", ex);
				}
			}

			final int rowCount = statementExecutor.execute(new NativeSqlStatement("UPDATE schedules SET deleted = TRUE, agentscheduleid = NULL WHERE id = ?", loaded.getId()));
			if (rowCount > 0) {
				auditService.audit(auditService.createAudit(user, loaded.getId(), AppName.SCHEDULEOBJECT, AuditMode.DELETE, loaded.getName(), auditNote, loaded.isPci()));

				if (user.isConsultancyMode()) {
					auditService.subscriptionAudit(user.getConsultancyUser(), "Removed scan schedule: " + loaded.getName(), loaded.getUserId(), null);
				}
				return true;
			}
		}

		return false;
	}
}
