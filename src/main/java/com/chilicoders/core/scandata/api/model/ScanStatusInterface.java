package com.chilicoders.core.scandata.api.model;

import java.util.Date;

import com.chilicoders.core.scandata.api.ScanPriority;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.core.scheduling.api.model.ScanResult;
import com.chilicoders.model.ScanMode;
import com.chilicoders.model.ScanTypes;

public interface ScanStatusInterface extends ScanResultInterface {

	void setReason(String string);

	boolean isDiscovery();

	ScanTypes getScanType();

	ScanMode getScanMode();

	long getRemoteId();

	boolean isRemoveScanlog();

	default boolean isLookup() {
		return getService() == ScanServiceType.Lookup;
	}

	ScanServiceType getService();

	ScanResult getScanResult();

	long getTargetId();

	long getVerifyId();

	boolean isActivateCompliance();

	long getScheduleId();

	Long getSubUserId();

	long getScanJobId();

	String getAliveList();

	Date getScanEnd();

	Date getScanStart();

	Date getScanStarted();

	String getTarget();

	long getTemplateId();

	long getScannerId();

	String getScanSchema();

	long getScanlessReportId();

	long getAttackerId();

	String getReason();

	String getAttackerName();

	long getCount();

	long getReportEntryId();

	String getTemplateName();

	String getScheduleName();

	default boolean isWas() {
		return getService() == ScanServiceType.Was || getService() == ScanServiceType.WasDiscovery;
	}

	boolean isStopping();

	boolean isPausing();

	boolean isStopped();

	void setAssetId(final Integer assetId);

	void setIsPaused(final int isPaused);

	void setId(long scanStatusId);

	void setTarget(String scanstatusTarget);

	void setService(ScanServiceType netsec);

	void setSettings(String string);

	void setScannerId(long scannerid);

	void setTargetId(long id);

	void setUserId(long userId);

	void setSubUserId(Long subUserId);

	void setScheduleId(long id);

	void setStatus(String status);

	void setScanStart(Date parseTimeDate);

	void setScanEnd(Date scanEndDate);

	void setTemplateId(long templateId);

	void setTemplateName(String template);

	void setAttackerName(String property);

	void setScheduleName(String name);

	void setScanJobId(long scanJobId);

	void setScanWindows(int scanWindows);

	void setScanWindowDelay(int scanWindowDelay);

	void setTargetType(int ordinal);

	void setHostname(String hostname);

	void setScanSchema(String string);

	void setActivateCompliance(boolean activated);

	void setScanMode(int id);

	String getReport();

	void removeReport();

	void setPriority(ScanPriority priority);

	void setWakeOnLan(String macAddress);

	void setWakeOnLanDelay(long delay);

	void setScanlessReportId(long reportId);

	void setLookup(int lookup);

	String getIpAddress();

	long getOpenPorts();

	void setOpenPorts(long openPorts);

	void setScanResult(ScanResult scanResult);

	void setReportEntryId(long reportId);

	long getScanLogId();

	int getTargetType();

	String getHostname();

	void setScanlogId(long scanLogId);

	String getSettings();

	/**
	 * Sets the report value for this scan status.
	 *
	 * @param report Report.
	 */
	void setReport(String report);

	/**
	 * @return True if this scan is a docker discovery.
	 */
	default boolean isDockerDiscovery() {
		return getService() == ScanServiceType.DOCKER_DISCOVERY;
	}

	/**
	 * @return True if this scan is a network discovery.
	 */
	default boolean isNetworkDiscovery() {
		return getService() == ScanServiceType.NETWORK_DISCOVERY;
	}

	/**
	 * @return True if this scan is a cloud discovery.
	 */
	default boolean isCloudDiscovery() {
		return getService() == ScanServiceType.CLOUD_DISCOVERY;
	}

	boolean isLdapDiscovery();

	boolean isAmazonDiscovery();

	boolean isAgentDiscovery();

	void setAliveList(String data);

	String getCompanyName();

	String getIssues();

	void setIssues(String issues);

	void addIssue(IssueType issueType, String reason);

	void setWorkflowId(Number workflowId);

	default boolean isScanless() {
		return getScanlessReportId() > 0;
	}

	default boolean isPortalScan() {
		return getService() == ScanServiceType.DOCKER_DISCOVERY || getService() == ScanServiceType.DOCKER_SCAN ||
				getService() == ScanServiceType.AppsecScale || getService() == ScanServiceType.CLOUDSEC || isNetworkDiscovery() || isCloudDiscovery() || getService() == ScanServiceType.NETWORK_SCAN;
	}

	String getNetworkLookupData();

	void setNetworkLookupData(String networkLookupData);

}
