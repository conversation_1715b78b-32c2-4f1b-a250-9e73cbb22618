package com.chilicoders.core.scandata.api;

import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.util.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IssueTransformationFactory {
	private static final Logger LOG = LogManager.getLogger(IssueTransformationFactory.class);

	/**
	 * Creates an instance of {@link IssueTransformation} based on the given issue type.
	 *
	 * @param issueType The type of the issue for which the transformation is needed.
	 * @return An instance of {@link IssueTransformation} corresponding to the issue type,
	 *         or {@code null} if no transformation is available for the given type.
	 */
	public static IssueTransformation createTransformation(final String issueType) {
		if (StringUtils.isEmpty(issueType)) {
			return null;
		}
		try {
			final IssueType type = IssueType.valueOf(issueType);
			switch (type) {
				case NO_OPEN_PORTS_FOUND:
					return new NoOpenPortIssueTransformation();
				case TOO_MANY_OPEN_PORTS_FOUND:
					return new TooManyOpenPortIssueTransformation();
				case AUTHENTICATION_FAILED:
				case AUTHENTICATION_INSUFFICIENT:
					return new AuthenticationFailedIssueTransformation();
				case CYBERARK_RETRIEVAL_FAILED:
				case DELINEA_RETRIEVAL_FAILED:
					return new RetrievalFailedIssueTransformation();
				case COULD_NOT_GENERATE_REPORT:
				case COULD_NOT_START_SCAN:
				case SCAN_FAILED:
				case SCAN_TIMEOUT:
				case SCAN_WARNING:
					return new CommonIssueTransformation();
				case ISSUE_NO_REACHABLE_SEED:
				case ISSUE_EMPTY_SEED_LIST:
				case ISSUE_INVALID:
				case ISSUE_TERMINATION_SIGNAL_RECEIVED:
					return new SimpleIssueTransformation();
				case ISSUE_MATCHED_ABORT_PATTERN:
				case ISSUE_BLACKLISTED_SEED:
				case ISSUE_FAILED_SETUP_PROC:
				case ISSUE_MAX_IVS_REACHED:
					return new NormalIssueTransformation();
				case ISSUE_IMAGE_ARCHITECTURE_NOT_SUPPORTED:
				case ISSUE_IMAGE_OS_NOT_SUPPORTED:
				case ISSUE_IMAGE_PULL_FAILURE:
				case ISSUE_IMAGE_SCAN_FAILURE:
				case ISSUE_IMAGE_SCAN_NOT_SUPPORTED:
				case ISSUE_IMAGE_SIZE_NOT_SUPPORTED:
				case ISSUE_LOGIN_FAILURE:
					return new DockerScanIssueTransformation();
				default:
					return null;
			}
		}
		catch (final IllegalArgumentException ex) {
			LOG.debug("Error when getting issue type: {}", ex.getMessage(), ex);
			return null;
		}
	}
}
