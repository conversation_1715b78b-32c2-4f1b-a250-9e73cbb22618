package com.chilicoders.core.scandata.api.dao;

import java.sql.SQLException;

import com.chilicoders.core.scandata.api.model.DataType;
import com.chilicoders.core.scandata.api.model.ScanDataInterface;

public interface ScanDataDao {
	/**
	 * Create a new scan data item, not persisted yet.
	 *
	 * @return New scan data item.
	 */
	ScanDataInterface createScanData();

	/**
	 * Persist scan data to database.
	 *
	 * @param scanData Scan data to persist.
	 */
	void save(ScanDataInterface scanData) throws SQLException;

	ScanDataInterface findByScanIdAndType(Integer scanId, DataType type) throws SQLException;
}
