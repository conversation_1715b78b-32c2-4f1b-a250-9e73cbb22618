package com.chilicoders.core.scandata.api.dao;

import java.sql.SQLException;
import java.util.List;

import org.json.JSONObject;

import com.chilicoders.model.MatchInterface;
import com.chilicoders.model.MatchType;
import com.chilicoders.rest.models.Service;

public interface MatchDao {
	/**
	 * Load matches from the database.
	 *
	 * @param customerId Customer id.
	 * @param assetId Asset id.
	 * @return List of matches.
	 */
	List<? extends MatchInterface> listMatches(final Integer customerId, final Integer assetId) throws SQLException;

	/**
	 * Persist a match to the database.
	 *
	 * @param match Match to persist.
	 * @return Match id.
	 */
	int save(MatchInterface match) throws SQLException;

	/**
	 * Get match by id.
	 *
	 * @param matchId Id.
	 * @return Match from database.
	 */
	MatchInterface getById(int matchId) throws SQLException;

	/**
	 * List matches by type.
	 *
	 * @param customerId Customer id.
	 * @param type Type.
	 * @return List of matches.
	 */
	List<? extends MatchInterface> getByType(Integer customerId, MatchType type) throws SQLException;

	/**
	 * Create a new match object, not yet persisted.
	 *
	 * @param createdById Created by id.
	 * @param customerId Customer id.
	 * @param assetId Asset id.
	 * @param service Service.
	 * @param type Type of match.
	 * @param json Match json.
	 * @return New object.
	 */
	MatchInterface createMatch(final Integer createdById, final Integer customerId, final Integer assetId, final Service service, final MatchType type, final JSONObject json);

	/**
	 * Detach entry from persistenct context, only usable in JPA. Will not do anything otherwise.
	 *
	 * @param match Match to detach.
	 */
	default void detach(final MatchInterface match) {
	}
}
