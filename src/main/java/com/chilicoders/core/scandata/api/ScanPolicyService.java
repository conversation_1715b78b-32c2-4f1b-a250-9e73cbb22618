package com.chilicoders.core.scandata.api;

import java.sql.SQLException;
import java.util.Map;
import java.util.Vector;

import com.chilicoders.core.scandata.api.model.CorePreferenceInterface;
import com.chilicoders.core.scandata.api.model.PluginPreferences;
import com.chilicoders.core.scandata.api.model.ScanFamilySetting;
import com.chilicoders.db.objects.api.ScanPolicyInterface;
import com.chilicoders.model.Template;

/**
 * Policy for dealing with scan policies.
 */
public interface ScanPolicyService {

	String AUTH_CREDENTIALS_SETTING_ID = "auth_credentials";

	/**
	 * Fetch scan policy by id.
	 *
	 * @param id Id.
	 * @return Scan policy.
	 */
	ScanPolicyInterface getScanPolicy(final long id) throws SQLException;

	ScanPolicyInterface getScanPolicy(long policyId, long targetId, long userId) throws SQLException;

	/**
	 * Convert plugin settings to map.
	 *
	 * @param settings Plugin settings
	 * @param addType whether to include type attribute in prefset
	 * @return Map with scan settings
	 */
	Map<String, CorePreferenceInterface> convertPlugins(final String settings, final boolean addType);

	/**
	 * Get a map with the default core preferences.
	 *
	 * @return Map of default preferences.
	 */
	Map<String, CorePreferenceInterface> getDefaultCorePreferences();

	/**
	 * @return Gets scan family settings map.
	 */
	Map<String, ScanFamilySetting> getScanFamilySettings() throws SQLException;

	/**
	 * Setup settings.
	 *
	 * @param prefs List of settings from probe
	 * @param forceRefresh Force refresh of settings
	 */
	void initCorePreferences(Vector<PluginPreferences> prefs, boolean forceRefresh);

	static boolean isPciScan(final long scanPolicyId) {
		return scanPolicyId == Template.Pci.getId() || scanPolicyId == Template.PciPreview.getId();
	}

	static boolean isValidPciScan(final long template) {
		return template == Template.Pci.getId();
	}
}
