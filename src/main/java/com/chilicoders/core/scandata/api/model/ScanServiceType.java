package com.chilicoders.core.scandata.api.model;

public enum ScanServiceType {
	AmazonDiscovery("A"),
	AgentDiscovery("G"),
	LdapDiscovery("L"),
	Netsec("O"),
	NetsecDiscovery("P"),
	TestCredentials("T"),
	AppsecScale("AS"),
	WasX("X"),
	Lookup("R"),
	<PERSON><PERSON><PERSON><PERSON><PERSON>("C"),
	Was("W"),
	WasDiscovery("D"),
	DOCKER_SCAN("DS"),
	DOCKER_DISCOVERY("DD"),
	CLOUDSEC("CS"),
	NETWORK_DISCOVERY("ND"),
	NETWORK_LOOKUP("NL"),
	CLOUD_DISCOVERY("CD"),
	NETWORK_SCAN("NS"),
	PAC_SCAN("PAC");

	private final String service;

	ScanServiceType(final String service) {
		this.service = service;
	}

	@Override
	public String toString() {
		return this.service;
	}

	/**
	 * Get service from value.
	 *
	 * @param value String value for service
	 * @return Matching service or null if there is no match.
	 */
	public static ScanServiceType fromString(final String value) {
		for (final ScanServiceType service : values()) {
			if (service.toString().equals(value)) {
				return service;
			}
		}
		return null;
	}
}
