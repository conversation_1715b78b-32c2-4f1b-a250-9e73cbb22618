package com.chilicoders.core.agents.api.model;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.chilicoders.discover.Tag;
import com.chilicoders.model.assetidentifier.AgentMatch;
import com.chilicoders.rest.adapters.InstantXmlAdapter;
import com.chilicoders.rest.adapters.MapXmlAdapter;
import com.google.common.collect.ImmutableList;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

/**
 * Represents an agent discovered by the @{link AgentDiscoveryEngine}.
 */
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class Agent {
	private String uuid;
	private String customerUuid;
	private String os;
	private String version;
	private String hostname;
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant updated;
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant lastSynchronized;
	private String certificate;
	@XmlJavaTypeAdapter(MapXmlAdapter.class)
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Map<String, String> customAttributes;
	private List<Tag> tags;
	@XmlJavaTypeAdapter(InstantXmlAdapter.class)
	private Instant retired;
	private List<AgentMatch> matches;

	public List<AgentMatch> getMatches() {
		return matches == null ? null : ImmutableList.copyOf(matches);
	}

	public List<Tag> getTags() {
		return tags == null ? new ArrayList<>() : ImmutableList.copyOf(tags);
	}
}
