package com.chilicoders.core.agents.api;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.Response;

import org.json.JSONObject;

import com.chilicoders.core.agents.api.exception.AgentApiException;
import com.chilicoders.core.agents.api.model.AgentInstallerInfo;

/**
 * Interface to agents API.
 */
public interface AgentsApi {

	/**
	 * Returns whether the tenant exists or not.
	 *
	 * @param customerUuid the tenant ID
	 * @return <code>true</code> if the tenant exists
	 */
	boolean tenantExists(final String customerUuid);

	/**
	 * Creates a tenant using the given customer UUID if it does not already exist.
	 *
	 * @param customerUuid the tenant ID
	 * @return <code>true</code> if tenant was or is already created.
	 */
	boolean createTenant(final String customerUuid);

	/**
	 * Create an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param frequency How often the agents in the schedule should scan
	 * @param modules the modules to add to the schedule
	 * @return The ID of the created schedule
	 */
	String createSchedule(final String customerUuid, final Duration frequency, final Set<String> modules);

	/**
	 * Update an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to update
	 * @param frequency How often the agents in the schedule should scan
	 * @param modules the modules to add to the schedule
	 */
	void updateSchedule(final String customerUuid, final String scheduleId, final Duration frequency, final Set<String> modules);

	/**
	 * Get number of agents in an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to get the agents for
	 * @return The number of agents in the schedule
	 */
	int getNumberOfAgentsInSchedule(final String customerUuid, final String scheduleId);

	/**
	 * Get a list of agents in an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to get the agents for
	 * @return The result as InputStream
	 */
	InputStream getAgentsInSchedule(final String customerUuid, final String scheduleId);

	/**
	 * Update module data.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule
	 * @param module module to update
	 * @param moduleData the module data (it could be encrypted or not)
	 */
	void uploadModule(final String customerUuid, final String scheduleId, final String module, final InputStream moduleData);

	/**
	 * Delete an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to delete
	 */
	void deleteSchedule(final String customerUuid, final String scheduleId);

	/**
	 * Add an agent to a agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to add the agent to
	 * @param agentId The ID of the agent to add to the schedule
	 */
	void addAgentToSchedule(final String customerUuid, final String scheduleId, final String agentId);

	/**
	 * Remove an agent from an agent schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to remove the agent from
	 * @param agentId The ID of the agent to remove from the schedule
	 */
	void removeAgentFromSchedule(final String customerUuid, final String scheduleId, final String agentId);

	/**
	 * Update an agent configuration.
	 *
	 * @param customerUuid tenant ID
	 * @param agentId The ID of the agent to update config for
	 * @param frequency How often the agent should call home
	 */
	void updateAgentConfig(final String customerUuid, final String agentId, final Duration frequency);

	/**
	 * Retrieve default installer configuration for the given tenant.
	 * Utilized when dynamically generating an installer bundle is not possible.
	 *
	 * @param customerUuid tenant ID
	 * @param customAttributes The custom attributes, for instance {\"Dep\": \"Prod\", \"assetId\": 152, \"critical\": false}.
	 * @param tags The agent tags, for instance [{"key": "device", "value": "eth0"}, {"key": "dhcp", "value": true}, {"key": "os"}].
	 * @return Configuration JSON
	 */
	JSONObject getAgentInstallerConfig(final String customerUuid, final String customAttributes, final String tags);

	/**
	 * Get the latest version of the agent installer.
	 *
	 * @return The version in the format of major.minor.patch
	 */
	String getAgentInstallerVersion();

	/**
	 * Delete an agent.
	 *
	 * @param customerUuid tenant ID
	 * @param agentId The ID of the agent to delete
	 * @return Whether the deletion was successful or not
	 */
	boolean deleteAgent(final String customerUuid, final String agentId);

	/**
	 * Set a setting.
	 *
	 * @param customerUuid tenant ID
	 * @param key The name of the setting to set
	 * @param value The value to set for the setting
	 * @return Whether the setting was set or not
	 */
	boolean setSetting(final String customerUuid, final String key, final Object value);

	/**
	 * Unset a setting.
	 *
	 * @param customerUuid tenant ID
	 * @param key The name of the setting to unset
	 * @return Whether the setting was unset or not
	 */
	boolean unsetSetting(final String customerUuid, final String key);

	/**
	 * Get module results.
	 *
	 * @param customerUuid tenant ID
	 * @param agentId The ID of the agent to get the result for
	 * @param scheduleId The ID of the schedule to get the result for
	 * @param module The name of the module to get the result for
	 * @return The result as input stream
	 * @throws AgentApiException On errors.
	 */
	InputStream getModuleResult(final String customerUuid, final String agentId, final String scheduleId, final String module) throws AgentApiException;

	/**
	 * Get a list of the modules that are in a schedule.
	 *
	 * @param customerUuid tenant ID
	 * @param scheduleId The ID of the schedule to get the list of modules for
	 * @return The list of modules
	 */
	List<String> getModulesInSchedule(final String customerUuid, final String scheduleId);

	/**
	 * Get a list of available agent installers.
	 *
	 * @param customerUuid tenant ID
	 * @return The available installers.
	 */
	String getAvailableInstallers(final String customerUuid);

	/**
	 * Download an agent installer.
	 *
	 * @param customerUuid tenant ID
	 * @param platform The platform the installer should be for.
	 * @param architecture The architecture the installer should be for.
	 * @param packageType The package type the installer should be.
	 * @param customAttributes The custom attributes, for instance {\"Dep\": \"Prod\", \"assetId\": 152, \"critical\": false}.
	 * @param httpResponse Http Response to write the installer to.
	 * @return Whether we got a successful response from the agent API or not.
	 */
	boolean downloadInstaller(final String customerUuid, final String platform, final String architecture, final String packageType, final String customAttributes,
							  final HttpServletResponse httpResponse) throws IOException;

	/**
	 * Download an agent installer.
	 *
	 * @param customerUuid tenant ID
	 * @param platform The platform the installer should be for.
	 * @param architecture The architecture the installer should be for.
	 * @param packageType The package type the installer should be.
	 * @param customAttributes The custom attributes, for instance {\"Dep\": \"Prod\", \"assetId\": 152, \"critical\": false}.
	 * @param tags The agent tags, for instance [{"key": "device", "value": "eth0"}, {"key": "dhcp", "value": true}, {"key": "os"}].
	 * @return {@link Response} from the agent API.
	 */
	Response downloadInstaller(final String customerUuid, final String platform, final String architecture, final String packageType, final String customAttributes, final String tags);

	/**
	 * Create an agent installer
	 *
	 * @param customerUuid tenant ID
	 * @param agentInstallerInfo the installer information
	 * @return true if the request to create agent installer is accepted, false otherwise
	 */
	boolean createAgentInstaller(final String customerUuid, final AgentInstallerInfo agentInstallerInfo);

	/**
	 * Get a list of the logs that an agent has sent home.
	 *
	 * @param customerUuid tenant ID
	 * @param agentId The ID of the agent to get the list of logs for
	 * @return A list of log file names
	 */
	List<Object> getAgentLogs(final String customerUuid, final String agentId);

	/**
	 * Get a list of the logs that an agent has sent home.
	 *
	 * @param customerUuid tenant ID
	 * @param agentId The ID of the agent
	 * @param logFile the name of the log to get
	 * @return The log contents
	 */
	InputStream getAgentLog(final String customerUuid, final String agentId, final String logFile);

	/**
	 * Call the agent API to get a list of the tenant's agents.
	 *
	 * @param customerUuid tenant ID
	 * @return The result as InputStream
	 */
	InputStream getDiscoveredAgents(final String customerUuid);
}
