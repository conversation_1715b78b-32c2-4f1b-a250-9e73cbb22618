package com.chilicoders.core.swat.api;

import java.sql.SQLException;

import com.chilicoders.core.user.api.UserDetails;

public interface SwatService {

	/**
	 * Gets all the schedules 'under' the specified ids.
	 * Returns the specified ids and their children.
	 *
	 * @param user the user
	 * @param scheduleIds ids to 'expand' (find children ids for)
	 * @return the expanded schedule id set
	 */
	Long[] getSwatSchedulesWithChildren(final UserDetails user, final Long[] scheduleIds) throws SQLException;
}
