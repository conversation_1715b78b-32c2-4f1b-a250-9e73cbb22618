package com.chilicoders.core.argo;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public interface ArgoScanData {

	default Map<String, String> getParameters() {
		return Collections.emptyMap();
	}

	String getTemplate();

	List<ArgoUploadedFiles> getUploadedFiles();

	List<ArgoFileParameter> coreUploadFiles();

	default List<ArgoFileParameter> slsAndResumeFiles() {
		return Collections.emptyList();
	}
}
