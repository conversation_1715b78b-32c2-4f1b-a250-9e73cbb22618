package com.chilicoders.core.argo;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

import javax.net.ssl.SSLContext;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.chilicoders.core.argo.api.ArgoService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.provisioning.client.Operation;
import com.chilicoders.provisioning.client.PresignRequest;
import com.chilicoders.provisioning.client.PresignResponse;
import com.chilicoders.provisioning.client.ProvisioningClient;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XmlUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.argoproj.workflow.ApiClient;
import io.argoproj.workflow.ApiException;
import io.argoproj.workflow.JSON;
import io.argoproj.workflow.apis.WorkflowServiceApi;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1SubmitOpts;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1Workflow;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1WorkflowList;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1WorkflowResumeRequest;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1WorkflowStopRequest;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1WorkflowSubmitRequest;
import io.argoproj.workflow.models.IoArgoprojWorkflowV1alpha1WorkflowSuspendRequest;
import lombok.AllArgsConstructor;
import okhttp3.OkHttpClient;
import software.amazon.awssdk.utils.StringInputStream;

@AllArgsConstructor
@SuppressFBWarnings("EI_EXPOSE_REP2")
public class ArgoServiceImpl implements ArgoService {
	private static final Logger LOG = LogManager.getLogger(ArgoServiceImpl.class);

	private ConfigurationService configurationService;

	/**
	 * Create a work flow service.
	 *
	 * @return Argo work flow service.
	 */
	private WorkflowServiceApi getWorkflowServiceApi() {
		final OkHttpClient.Builder newBuilder = getHttpClientBuilder();
		final ApiClient apiClient = new ApiClient(newBuilder.build());
		final String apiKey = getToken(ConfigKeys.ConfigurationKey.argo_workflow_apikey);
		if (!Strings.isNullOrEmpty(apiKey)) {
			apiClient.setApiKey("Bearer " + apiKey);
		}
		if (configurationService.getProperty(ConfigKeys.ConfigurationBooleanKey.argo_ignore_certificates)) {
			apiClient.setVerifyingSsl(false);
		}
		apiClient.setBasePath(configurationService.getProperty(ConfigKeys.ConfigurationKey.argo_basepath));
		final WorkflowServiceApi result = new WorkflowServiceApi(apiClient);
		result.getApiClient().getJSON().setGson(
				createGSONParser());
		return result;
	}

	/**
	 * Create a OkHttpBuilder, can be set to ignore server certificate via the setting argo.ignore.certificates.
	 *
	 * @return OKHttpClient.Builder
	 */
	private OkHttpClient.Builder getHttpClientBuilder() {
		final OkHttpClient.Builder newBuilder = new OkHttpClient.Builder();
		if (configurationService.getProperty(ConfigKeys.ConfigurationBooleanKey.argo_ignore_certificates)) {
			final SSLContext sslContext = SslUtils.getO24GlobalSslContext();
			newBuilder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) SslUtils.getTrustAllTrustManager()[0]);
			newBuilder.hostnameVerifier((hostname, session) -> true);
		}
		return newBuilder;
	}

	/**
	 * Create a GSON parser.
	 *
	 * @return GSON parser
	 */
	private static Gson createGSONParser() {
		return new GsonBuilder().registerTypeAdapter(Date.class, new JSON.DateTypeAdapter())
				.registerTypeAdapter(java.sql.Date.class, new JSON.SqlDateTypeAdapter())
				.registerTypeAdapter(
						OffsetDateTime.class, new JSON.OffsetDateTimeTypeAdapter())
				.registerTypeAdapter(LocalDate.class, new JSON().new LocalDateTypeAdapter())
				.registerTypeAdapter(byte[].class, new JSON().new ByteArrayAdapter())
				.registerTypeAdapter(
						Instant.class, new InstantDeserializer())
				.create();
	}

	/**
	 * Sets the deadline_timestamp and max_runtime_seconds parameters for the argo wrokflow.
	 *
	 * @param opts Options are added here.
	 * @param scanEndDate Scan end time.
	 * @param scanSettings Settings from scan, used to pick up SCANTIMESETTINGS in order to calculate end time.
	 */
	private void setTimeoutSettings(final IoArgoprojWorkflowV1alpha1SubmitOpts opts, final Date scanEndDate, final String scanSettings) {
		final String pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'";
		final HashMap<String, Object> settings = Strings.isNullOrEmpty(scanSettings) ? new HashMap<>() : XmlUtils.getParams(new ParamList(scanSettings));
		if (!settings.containsKey("SCANTIMESETTINGS")) {
			opts.addParametersItem("deadline_timestamp=" + ZonedDateTime.ofInstant(scanEndDate.toInstant(), ZoneId.of("UTC")).format(DateTimeFormatter.ofPattern(pattern)));
			opts.addParametersItem("max_runtime_seconds=" + ((scanEndDate.getTime() - System.currentTimeMillis()) / 1000));
			return;
		}

		final JSONObject scanTimeSettings = new JSONObject((String) settings.get("SCANTIMESETTINGS"));
		final Instant blockedTime = scanTimeSettings.has("blockedTime") ? Instant.parse(scanTimeSettings.getString("blockedTime")) : null;
		final Instant scanWindowEnd = scanTimeSettings.has("scanWindowEnd") ? Instant.parse(scanTimeSettings.getString("scanWindowEnd")) : null;
		final int scanDuration = scanTimeSettings.getInt("scanDuration");

		Instant endDate = scanWindowEnd;
		if (endDate == null || (blockedTime != null && blockedTime.isBefore(scanWindowEnd))) {
			endDate = blockedTime;
		}
		if (endDate == null) {
			endDate = Instant.now().plus(scanDuration, ChronoUnit.SECONDS).plus(12, ChronoUnit.HOURS);
		}

		opts.addParametersItem("deadline_timestamp=" + ZonedDateTime.ofInstant(endDate, ZoneId.of("UTC")).format(DateTimeFormatter.ofPattern(pattern)));
		opts.addParametersItem("max_runtime_seconds=" + scanDuration);
	}

	@Override
	public IoArgoprojWorkflowV1alpha1Workflow startWorkflow(final String namespace,
															final ArgoScanData scanData, final String tenantUUID, final long scanId, final boolean isPaused,
															final long scanLessId, final Date scanEndDate, final String scanSettings, final boolean hasScandataToResumeFrom)
			throws ApiException {
		if (scanData == null) {
			LOG.error("scanData is null");
			throw new RuntimeException("scanData is null");
		}
		final WorkflowServiceApi workflowServiceApi = getWorkflowServiceApi();
		final IoArgoprojWorkflowV1alpha1WorkflowSubmitRequest body = new IoArgoprojWorkflowV1alpha1WorkflowSubmitRequest();
		body.setNamespace(namespace);
		body.setResourceKind("WorkflowTemplate");
		body.setResourceName(scanData.getTemplate());
		final IoArgoprojWorkflowV1alpha1SubmitOpts opts = new IoArgoprojWorkflowV1alpha1SubmitOpts();
		final ProvisioningClient provisioningClient =
				new ProvisioningClient(configurationService.getProperty(ConfigKeys.ConfigurationKey.provisioning_service_url) + "/rest/v0",
						configurationService.getProperty(ConfigKeys.ConfigurationKey.api_debug_token));
		opts.addParametersItem("scanid=" + scanId);
		opts.addParametersItem("tenantid=" + tenantUUID);

		setTimeoutSettings(opts, scanEndDate, scanSettings);

		final ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());

		scanData.getParameters().forEach((key, value) -> {
			if (value != null) {
				opts.addParametersItem(key + "=" + value);
			}
		});
		final UUID uuid = UUID.fromString(tenantUUID);
		final String prioritizedTenants = configurationService.getProperty(ConfigKeys.ConfigurationKey.argo_prioritized_tenants);
		if (!StringUtils.isEmpty(prioritizedTenants) && prioritizedTenants.contains(tenantUUID)) {
			opts.setPriority(999);
		}
		try {
			for (final ArgoUploadedFiles file : scanData.getUploadedFiles()) {
				final String key = "scan/" + scanId + "/" + file.parameter.getKey();
				final StringInputStream stream = new StringInputStream(file.data);
				final ProvisioningClient.Response response =
						provisioningClient.put(uuid, key, stream.available(), stream, Collections.singletonMap("type", "scandata"), null);
				if (response == null || response.getStatus().getStatusCode() != HttpStatus.SC_OK) {
					throw new ApiException("Error uploading file " + file.parameter.getKey() + ": " + (response != null ? response.getStatus() : "null"));
				}
				final PresignRequest request =
						PresignRequest.builder()
								.operation(Operation.GET)
								.key(key)
								.duration(Duration.ofDays(35))
								.tags(Collections.singletonMap("type", "scandata"))
								.build();
				final PresignResponse presign = provisioningClient.presign(uuid, request);
				opts.addParametersItem(file.getParameter().getParameterName() + "=" + objectMapper.writeValueAsString(presign));
			}
			for (final ArgoFileParameter uploadFiles : scanData.coreUploadFiles()) {
				final String key = "scan/" + scanId + "/" + uploadFiles.getKey();
				final PresignRequest request =
						PresignRequest.builder().operation(Operation.PUT).key(key).duration(Duration.ofDays(2)).tags(Collections.singletonMap("type", "scandata")).build();
				final PresignResponse presign = provisioningClient.presign(uuid, request);
				opts.addParametersItem(uploadFiles.getParameterName() + "=" + objectMapper.writeValueAsString(presign));
			}
			if (!configurationService.getProperty(ConfigKeys.ConfigurationBooleanKey.test_mode) && (scanLessId > 0 || (isPaused && hasScandataToResumeFrom))) {
				for (final ArgoFileParameter downloadFiles : scanData.slsAndResumeFiles()) {
					final String key = "scan/" + (scanLessId > 0 ? scanLessId : scanId) + "/" + downloadFiles.getKey();
					final PresignRequest getRequest =
							PresignRequest.builder()
									.operation(Operation.GET)
									.key(key)
									.duration(Duration.ofDays(35))
									.tags(Collections.singletonMap("type", "scandata"))
									.build();
					final PresignResponse getPresignedUrl = provisioningClient.presign(uuid, getRequest);
					opts.addParametersItem(downloadFiles.getParameterName() + "=" + objectMapper.writeValueAsString(getPresignedUrl));
				}
			}
		}
		catch (final JsonProcessingException e) {
			LOG.error(e);
			throw new ApiException(e);
		}
		finally {
			LOG.debug("Parameters that will be sent to argo workflow: {}", opts.getParameters());
		}
		body.setSubmitOptions(opts);
		return workflowServiceApi.workflowServiceSubmitWorkflow(namespace, body);
	}

	/**
	 * Fetch a token to use to communicate with other services in k8s.
	 *
	 * @param defaultKey Default key, can be used to override the pods token.
	 * @return Token value.
	 */
	private String getToken(final ConfigKeys.ConfigurationKey defaultKey) {
		String token = configurationService.getProperty(defaultKey);
		final File tokenFile = new File(configurationService.getProperty(ConfigKeys.ConfigurationKey.kubernetes_token_path));
		if (StringUtils.isEmpty(token) && tokenFile.exists()) {
			try {
				token = FileUtils.readFileToString(tokenFile, StandardCharsets.UTF_8);
			}
			catch (final IOException e) {
				LOG.error("Error reading token file: {}", e.getMessage());
			}
		}
		return token;
	}

	@Override
	public void stopWorkflow(final String namespace, final String name) throws ApiException {
		final WorkflowServiceApi workflowServiceApi = getWorkflowServiceApi();
		final IoArgoprojWorkflowV1alpha1WorkflowStopRequest body = new IoArgoprojWorkflowV1alpha1WorkflowStopRequest();
		workflowServiceApi.workflowServiceStopWorkflow(namespace, name, body);
	}

	@Override
	public void resumeWorkflow(final String namespace, final String name) throws ApiException {
		final WorkflowServiceApi workflowServiceApi = getWorkflowServiceApi();
		final IoArgoprojWorkflowV1alpha1WorkflowResumeRequest body = new IoArgoprojWorkflowV1alpha1WorkflowResumeRequest();
		workflowServiceApi.workflowServiceResumeWorkflow(namespace, name, body);
	}

	@Override
	public void suspendWorkflow(final String namespace, final String name) throws ApiException {
		final WorkflowServiceApi workflowServiceApi = getWorkflowServiceApi();
		final IoArgoprojWorkflowV1alpha1WorkflowSuspendRequest body = new IoArgoprojWorkflowV1alpha1WorkflowSuspendRequest();
		workflowServiceApi.workflowServiceSuspendWorkflow(namespace, name, body);
	}

	@Override
	public int getPendingWorkflows(final String namespace, final String nameFilter) throws ApiException {
		final WorkflowServiceApi workflowServiceApi = getWorkflowServiceApi();
		final IoArgoprojWorkflowV1alpha1WorkflowList list = workflowServiceApi.workflowServiceListWorkflows(namespace, "workflows.argoproj.io/phase in (Pending)", "metadata.name=" + nameFilter,
				null, null, null, null, null,
				Integer.toString(configurationService.getProperty(ConfigKeys.ConfigurationIntKey.argo_max_queued)), null, null, "items.metadata.name", "Contains");
		// Do not remove the list.getItems null check despite warning, it actually can return null, at least in 3.6.2
		return list == null || list.getItems() == null ? 0 : list.getItems().size();
	}

	private static class InstantDeserializer implements JsonDeserializer<Instant> {
		@Override
		public Instant deserialize(final JsonElement json, final Type typeOfT, final JsonDeserializationContext context) throws JsonParseException {
			return Instant.parse(json.getAsString());
		}
	}
}
