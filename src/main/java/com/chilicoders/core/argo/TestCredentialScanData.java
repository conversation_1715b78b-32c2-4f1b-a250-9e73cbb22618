package com.chilicoders.core.argo;

import static com.chilicoders.core.argo.ArgoFileParameter.fromPair;
import static com.chilicoders.core.argo.ResourceFileNames.CREDENTIALS_REPORT_FILENAME;

import java.util.Collections;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;

public class TestCredentialScanData implements ArgoScanData {
	private final String settings;
	private final String template;
	private final Map<String, String> parameters = new HashMap<>();

	/**
	 * Constructor.
	 *
	 * @param settings The scan settings.
	 * @param parameters The scan parameters, mostly "mode" to separate "ssh" and "smb".
	 * @param configService The {@link ConfigurationService} instance.
	 */
	public TestCredentialScanData(final String settings, final Map<String, String> parameters, final ConfigurationService configService) {
		this.settings = settings;
		template = configService.getProperty(ConfigKeys.ConfigurationKey.template_test_crendentials_scan);
		this.parameters.putAll(parameters);
	}

	@Override
	public Map<String, String> getParameters() {
		return new HashMap<>(parameters); // Return a new copy they can modify
	}

	@Override
	public List<ArgoUploadedFiles> getUploadedFiles() {
		final List<ArgoUploadedFiles> result = new ArrayList<>();
		result.add(new ArgoUploadedFiles(fromPair("settings-url", "setting"), settings));
		return result;
	}

	@Override
	public List<ArgoFileParameter> coreUploadFiles() {
		return Collections.singletonList(fromPair("result-upload-url", CREDENTIALS_REPORT_FILENAME));
	}

	@Override
	public String getTemplate() {
		return template;
	}
}
