package com.chilicoders.core.argo;

import static com.chilicoders.core.argo.ArgoFileParameter.fromPair;
import static com.chilicoders.core.argo.ResourceFileNames.COMPRESSED_ARCHIVE_FILENAME;
import static com.chilicoders.core.argo.ResourceFileNames.CRAWLED_URLS_FILENAME;
import static com.chilicoders.core.argo.ResourceFileNames.FINDINGS_FILENAME;
import static com.chilicoders.core.argo.ResourceFileNames.ISSUES_FILENAME;
import static com.chilicoders.core.argo.ResourceFileNames.RULEENGINE_SETTINGS_FILENAME;
import static com.chilicoders.core.argo.ResourceFileNames.WASX_SETTINGS_FILENAME;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;

public class WasxScanData implements ArgoScanData {
	private final String scanSettings;
	private final String ruleSettings;
	private final String template;

	/**
	 * Constructor.
	 *
	 * @param scanSettings Scan settings.
	 * @param ruleSettings Rule settings.
	 * @param configService The {@link ConfigurationService} instance.
	 */
	public WasxScanData(final String scanSettings, final String ruleSettings, final ConfigurationService configService) {
		this.scanSettings = scanSettings;
		this.ruleSettings = ruleSettings;
		template = configService.getProperty(ConfigKeys.ConfigurationKey.template_wasx_scan);
	}

	@Override
	public List<ArgoUploadedFiles> getUploadedFiles() {
		final List<ArgoUploadedFiles> result = new ArrayList<>();
		result.add(new ArgoUploadedFiles(fromPair("wasx-scan-settings-url", WASX_SETTINGS_FILENAME), scanSettings));
		result.add(new ArgoUploadedFiles(fromPair("ruleengine-settings-url", RULEENGINE_SETTINGS_FILENAME), ruleSettings));
		return result;
	}

	@Override
	public List<ArgoFileParameter> coreUploadFiles() {
		return Arrays.asList(fromPair("crawled-urls-upload-url", CRAWLED_URLS_FILENAME),
				fromPair("issues-upload-url", ISSUES_FILENAME),
				fromPair("compressed-archive-url", COMPRESSED_ARCHIVE_FILENAME),
				fromPair("findings-upload-url", FINDINGS_FILENAME));
	}

	@Override
	public String getTemplate() {
		return template;
	}
}
