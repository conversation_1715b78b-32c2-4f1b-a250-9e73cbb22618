package com.chilicoders.core.scheduling.api.model;

import javax.persistence.AttributeConverter;

public class ScheduleFrequencyConverter implements AttributeConverter<ScheduleFrequency, Integer> {

	@Override
	public Integer convertToDatabaseColumn(final ScheduleFrequency attribute) {
		return attribute.getId();
	}

	@Override
	public ScheduleFrequency convertToEntityAttribute(final Integer dbData) {
		if (dbData == null) {
			return null;
		}
		return ScheduleFrequency.getFromId(dbData);
	}

}
