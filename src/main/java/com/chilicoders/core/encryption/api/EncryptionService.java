package com.chilicoders.core.encryption.api;

import com.chilicoders.core.encryption.api.exception.EncryptionException;
import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;

public interface EncryptionService {
	void encrypt(final EncryptionDecryptionRequest request) throws EncryptionException;

	void decrypt(final EncryptionDecryptionRequest request) throws EncryptionException;
}
