package com.chilicoders.core.encryption.impl;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Security;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.interfaces.RSAPrivateCrtKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.asn1.DERNull;
import org.bouncycastle.asn1.nist.NISTObjectIdentifiers;
import org.bouncycastle.asn1.x509.AlgorithmIdentifier;
import org.bouncycastle.asn1.x509.DigestInfo;
import org.bouncycastle.cert.jcajce.JcaX509ExtensionUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;
import com.chilicoders.core.encryption.impl.io.PemOutputStream;
import com.chilicoders.core.encryption.impl.model.ClavemPemBlockType;
import com.chilicoders.core.encryption.impl.model.ClavemPemHeaderName;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.util.SslUtils;

public final class ClavemEncryptor {
	protected static final String SECURITY_PROVIDER = "BC";

	private static final String SYMMETRIC_KEY_ALGORITHM = "AES";
	private static final int SYMMETRIC_KEY_SIZE = 256;
	private static final String CHECKSUM_ALGORITHM = "SHA-512";

	private final EncryptionDecryptionRequest request;

	private final InputStream input;
	private final PemOutputStream output;

	/**
	 * Creates an object able to encrypt to the Clavem format.
	 *
	 * @param request The encryption request
	 */
	public ClavemEncryptor(final EncryptionDecryptionRequest request) throws IOException, GeneralSecurityException {
		Security.addProvider(new BouncyCastleProvider());

		this.request = request;

		this.input = new BufferedInputStream(this.request.getInputStream());
		this.output = new PemOutputStream(this.request.getOutputStream());
	}

	/**
	 * Performs the encryption.
	 */
	public void encrypt() throws IOException, GeneralSecurityException {
		try {
			final boolean symmetricEncryption = this.request.getSymmetricKey() != null;
			final byte[] symmetricKey = symmetricEncryption ? this.request.getSymmetricKey() : generateSymmetricKey();
			if (!symmetricEncryption) {
				writeEncryptedKeys(getPublicKeys(this.request.getRecipientCertificatesPath()), symmetricKey, this.request.getAllowedServices());
			}
			final byte[] checksum = writeEncryptedData(symmetricKey);
			writeChecksum(checksum);
			if (!symmetricEncryption && !this.request.isUnsigned()) {
				writeCertificates(this.request.getClientCertificatePath());
				writeSignature(this.request.getClientPrivateKeyPath(), this.request.getClientPrivateKeyPassphrase(), checksum);
			}
		}
		finally {
			output.flush();
		}
	}

	/**
	 * Generates a symmetric key.
	 *
	 * @return A symmetric key
	 */
	private static byte[] generateSymmetricKey() throws GeneralSecurityException {
		final KeyGenerator keyGenerator = KeyGenerator.getInstance(SYMMETRIC_KEY_ALGORITHM, SECURITY_PROVIDER);
		keyGenerator.init(SYMMETRIC_KEY_SIZE, SecureRandom.getInstanceStrong());
		return keyGenerator.generateKey().getEncoded();
	}

	/**
	 * Gets a {@link PrivateKey} from the given path using the optional passphrase.
	 *
	 * @param privateKeyPath Path to the file with the private key
	 * @param privateKeyPassphrase Passphrase for the key
	 * @return A private key object
	 */
	protected static PrivateKey getPrivateKey(final Path privateKeyPath, final String privateKeyPassphrase) throws IOException {
		try (final FileInputStream keyInputStream = new FileInputStream(privateKeyPath.toString())) {
			return SslUtils.getKeyFrom(keyInputStream, privateKeyPassphrase.toCharArray());
		}
	}

	/**
	 * Gets a {@link List} of {@link PublicKey} from the given path.
	 *
	 * @param certificatesPath Path to the file with certificates
	 * @return A list of public key objects
	 */
	protected static List<PublicKey> getPublicKeys(final Path certificatesPath) throws IOException, GeneralSecurityException {
		final List<PublicKey> publicKeys = new ArrayList<>();
		try (final InputStream certificatesInputStream = Files.newInputStream(certificatesPath)) {
			for (final Certificate certificate : SslUtils.getCertificatesFrom(certificatesInputStream)) {
				publicKeys.add(certificate.getPublicKey());
			}
		}
		return publicKeys;
	}

	protected static String getSki(final PublicKey publicKey) throws GeneralSecurityException {
		return Hex.toHexString(new JcaX509ExtensionUtils().createSubjectKeyIdentifier(publicKey).getKeyIdentifier()).toUpperCase();
	}

	/**
	 * Creates a checksum of the given PEM block headers, using the algorithm specified in the headers.
	 *
	 * @param headers PEM block headers
	 * @return A byte array with the checksum
	 */
	protected static byte[] checksumHeaders(final Map<String, String> headers) throws IOException, GeneralSecurityException {
		// the contract is for the checksum to be of the headers ordered by key, output as "key: value\n", including trailing newline
		final String headersString = new TreeMap<>(headers).entrySet().stream().map(header -> header.getKey() + ": " + header.getValue() + "\n").collect(Collectors.joining());
		return ClavemDecryptor.getDigester(headers.get(ClavemPemHeaderName.HEADER_ALGORITHM.getValue())).digest(headersString.getBytes(StandardCharsets.UTF_8));
	}

	/**
	 * Generates an input vector to use for encryption.
	 *
	 * @param size Size of the IV to generate
	 * @return A generated random IV
	 */
	private static byte[] generateIv(final int size) throws GeneralSecurityException {
		final byte[] iv = new byte[size];
		SecureRandom.getInstanceStrong().nextBytes(iv);
		return iv;
	}

	/**
	 * Encrypts the given symmetric key with each of the given public keys and writes ENCRYPTED KEY blocks to the output.
	 *
	 * @param publicKeys Public keys to encrypt the key with
	 * @param symmetricKey The symmetric key to encrypt
	 * @param allowedServices Which services are allowed to decrypt the key
	 */
	private void writeEncryptedKeys(final List<PublicKey> publicKeys, final byte[] symmetricKey, final Set<ServiceIdentity.Service> allowedServices)
			throws IOException, GeneralSecurityException {
		final ClavemPemBlockType blockType = ClavemPemBlockType.ENCRYPTED_KEY;

		final Map<String, String> headers = new HashMap<>();
		headers.put(ClavemPemHeaderName.ALGORITHM.getValue(), "RSA-OAEP-SHA512");
		headers.put(ClavemPemHeaderName.HEADER_ALGORITHM.getValue(), CHECKSUM_ALGORITHM);
		headers.put(ClavemPemHeaderName.KEY_SIZE.getValue(), "4096");
		headers.put(ClavemPemHeaderName.VERSION.getValue(), "v2");

		if (CollectionUtils.isNotEmpty(allowedServices)) {
			headers.put(ClavemPemHeaderName.ALLOWED_SERVICES.getValue(), allowedServices.stream()
					.map(service -> service.name())
					.collect(Collectors.joining(",")));
		}

		for (final PublicKey publicKey : publicKeys) {
			this.output.writeBegin(blockType.getValue());

			headers.put(ClavemPemHeaderName.KEY_IDENTIFIER.getValue(), getSki(publicKey));
			this.output.writeHeaders(headers);

			final Cipher cipher = Cipher.getInstance("RSA/NONE/OAEPWithSHA512AndMGF1Padding", SECURITY_PROVIDER);
			cipher.init(Cipher.ENCRYPT_MODE, publicKey);
			this.output.writePartialData(cipher.update(checksumHeaders(headers)));
			this.output.writeFinalData(cipher.doFinal(symmetricKey));

			this.output.writeEnd(blockType.getValue());
		}
	}

	/**
	 * Reads data from the input, digests it, encrypts it, and writes it to the output.
	 *
	 * @param symmetricKey The symmetric key to use for encryption
	 * @return A checksum of the unencrypted data
	 */
	private byte[] writeEncryptedData(final byte[] symmetricKey) throws IOException, GeneralSecurityException {
		final ClavemPemBlockType blockType = ClavemPemBlockType.ENCRYPTED_DATA;

		this.output.writeBegin(blockType.getValue());

		final Map<String, String> headers = new HashMap<>();
		headers.put(ClavemPemHeaderName.CIPHER.getValue(), "AES-256-CTR");
		headers.put(ClavemPemHeaderName.DIGEST_ALGORITHM.getValue(), CHECKSUM_ALGORITHM);
		this.output.writeHeaders(headers);

		final MessageDigest digester = MessageDigest.getInstance(CHECKSUM_ALGORITHM);
		final Cipher cipher = Cipher.getInstance("AES/CTR/NoPadding", SECURITY_PROVIDER);
		final byte[] iv = generateIv(cipher.getBlockSize());
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(symmetricKey, SYMMETRIC_KEY_ALGORITHM), new IvParameterSpec(iv));

		this.output.writePartialData(iv);

		final byte[] dataBuffer = new byte[1000];
		int readBytes = 0;
		while ((readBytes = this.input.read(dataBuffer)) != -1) {
			digester.update(dataBuffer, 0, readBytes);
			this.output.writePartialData(cipher.update(dataBuffer, 0, readBytes));
		}
		this.output.writeFinalData(cipher.doFinal());

		this.output.writeEnd(blockType.getValue());

		return digester.digest();
	}

	/**
	 * Writes the checksum of the data to the output.
	 *
	 * @param checksum The checksum
	 */
	private void writeChecksum(final byte[] checksum) throws IOException {
		final ClavemPemBlockType blockType = ClavemPemBlockType.DATA_CHECKSUM;

		this.output.writeBegin(blockType.getValue());

		final Map<String, String> headers = new HashMap<>();
		headers.put(ClavemPemHeaderName.ALGORITHM.getValue(), CHECKSUM_ALGORITHM);
		this.output.writeHeaders(headers);

		this.output.writeFinalData(checksum);

		this.output.writeEnd(blockType.getValue());
	}

	/**
	 * Write the chain of the certificate used to encrypt to the output.
	 *
	 * @param certificatesPath Path to the certificates file
	 */
	private void writeCertificates(final Path certificatesPath) throws IOException {
		// copy our certificate file verbatim into output
		try (final InputStream certificatesInputStream = Files.newInputStream(certificatesPath)) {
			IOUtils.copy(certificatesInputStream, this.output);
		}
	}

	/**
	 * Sign the checksum with the given private key and write to the output.
	 *
	 * @param privateKeyPath Path to the private key
	 * @param privateKeyPassphrase Passphrase of the private key
	 * @param digest The checksum to sign
	 */
	private void writeSignature(final Path privateKeyPath, final String privateKeyPassphrase, final byte[] digest) throws IOException, GeneralSecurityException {
		final RSAPrivateCrtKey privateKey = (RSAPrivateCrtKey) getPrivateKey(privateKeyPath, privateKeyPassphrase);
		final PublicKey publicKey = KeyFactory.getInstance("RSA").generatePublic(new RSAPublicKeySpec(privateKey.getModulus(), privateKey.getPublicExponent()));

		final ClavemPemBlockType blockType = ClavemPemBlockType.DATA_SIGNATURE;

		this.output.writeBegin(blockType.getValue());

		final Map<String, String> headers = new HashMap<>();
		headers.put(ClavemPemHeaderName.ALGORITHM.getValue(), "sha512WithRSAEncryption");
		headers.put(ClavemPemHeaderName.KEY_IDENTIFIER.getValue(), getSki(publicKey));
		headers.put(ClavemPemHeaderName.KEY_SIZE.getValue(), "4096");
		this.output.writeHeaders(headers);

		final DigestInfo digestInfo = new DigestInfo(new AlgorithmIdentifier(NISTObjectIdentifiers.id_sha512, DERNull.INSTANCE), digest);
		final Signature signer = Signature.getInstance("NONEwithRSA", SECURITY_PROVIDER);
		signer.initSign(privateKey, SecureRandom.getInstanceStrong());
		signer.update(digestInfo.toASN1Primitive().getEncoded("DER"));
		this.output.writeFinalData(signer.sign());

		this.output.writeEnd(blockType.getValue());
	}
}
