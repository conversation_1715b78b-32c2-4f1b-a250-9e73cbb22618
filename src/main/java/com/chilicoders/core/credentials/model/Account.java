package com.chilicoders.core.credentials.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.model.AccountInterface;
import com.chilicoders.model.AccountType;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity(name = "accounts")
@TypeDefs(@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class))
public class Account implements AccountInterface {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column
	private Integer id;

	@Column
	private Integer customerId;

	@Column
	private String name;

	@Column
	@Type(type = "enum_type")
	@Enumerated(value = EnumType.STRING)
	private AccountType type;

	@Column
	private Integer integrationId;
}
