package com.chilicoders.core.rendering.api;

import com.chilicoders.util.xml.XMLDoc;

/**
 * Interface for defining a renderer of data in a grid.
 */
public interface Renderer {

	/**
	 * Renders the data.
	 *
	 * @param value The incoming data value.
	 * @param dataIndex The dataindex that the data originates from.
	 * @param xml The entire xml object that the data originated from.
	 * @param html true if export format is html, otherwise false
	 * @return A new value to render.
	 */
	public String renderData(final String value, final String dataIndex, final XMLDoc xml, final boolean html);
}
