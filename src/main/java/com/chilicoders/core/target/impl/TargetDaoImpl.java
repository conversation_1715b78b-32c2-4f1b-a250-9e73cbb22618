package com.chilicoders.core.target.impl;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.target.impl.jpa.repository.TargetRepository;
import com.chilicoders.core.targets.api.TargetInfo;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.query.JpaNativeStatementExecutor;
import com.chilicoders.db.query.NativeSqlStatement;

/**
 * Data access object for dealing with target information.
 */
@Component
public class TargetDaoImpl implements TargetDao {
	private JpaNativeStatementExecutor statementExec;

	@Autowired
	private TargetRepository targetRepository;

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private ConfigurationService configurationService;

	@PostConstruct
	public void init() {
		statementExec = new JpaNativeStatementExecutor(entityManager, configurationService);
	}

	@Override
	public TargetInfo getById(final UserDetails user, final long id) throws SQLException {
		final TargetInfo target = targetRepository.getOneByUserIdInAndIdIn(Arrays.asList(0L, user.getMainUserId()), Collections.singletonList(id));
		if (target != null) {
			target.loadTemplateOverride(statementExec);
		}
		return target;
	}

	private TargetInfo getTarget(final NativeSqlStatement statement) {
		return statementExec.getSingleDtoResult(statement, TargetInfoImpl.class);
	}

	@Override
	public List<? extends TargetInfo> getTargets(final NativeSqlStatement sql) {
		return statementExec.executeDTO(sql, TargetInfoImpl.class);
	}

	@Override
	public TargetInfo saveTarget(final TargetInfo target) {
		return targetRepository.save((TargetInfoImpl) target);
	}

	@Override
	public Long getLastReportId(final long targetId) throws SQLException {
		return this.statementExec.getLong(new NativeSqlStatement("SELECT lastreportid FROM targetscandata WHERE targetid = ?", targetId));
	}

	@Override
	public Map<Long, String> getTargetHostnames(final Long[] targetIds, final long userId) {
		final Map<String, String> data = this.statementExec.getMappedValues(
				new NativeSqlStatement("SELECT xid, hostname FROM tuserdatas WHERE xid =ANY (?) AND xuserxid = ? AND hostname IS NOT NULL", targetIds, userId));
		return data.entrySet().stream().filter(item -> item.getValue() != null).collect(Collectors.toMap(entry -> Long.valueOf(entry.getKey()), Map.Entry::getValue));
	}

	@Override
	public TargetInfo getByAgentId(final String agentId) {
		final NativeSqlStatement statement = new NativeSqlStatement();
		statement.addSql("SELECT agentid, agentlastsynchronized FROM vuserdata WHERE agentid=?");
		statement.addParam(agentId);
		return getTarget(statement);
	}
}
