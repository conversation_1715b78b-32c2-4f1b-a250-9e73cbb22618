package com.chilicoders.core.target.impl;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

import com.chilicoders.integrations.CyberArkInterface;

import lombok.Setter;

@Setter
@Entity(name = "cyberarks")
public class CyberArk implements CyberArkInterface {
	@Column
	@Id
	private long id;

	@Column
	private String cyberarkHost;

	@Column
	private String cyberarkAppId;

	@Column
	private boolean cyberarkEnabled;

	@Column
	private int cyberarkPort;

	@Column
	private String cyberarkDefaultSafe;

	@Column
	private String cyberarkDefaultFolder;

	@Override
	public String getAppId() {
		return cyberarkAppId;
	}

	@Override
	public String getDefaultSafe() {
		return cyberarkDefaultSafe;
	}

	@Override
	public String getDefaultFolder() {
		return cyberarkDefaultFolder;
	}

	@Override
	public int getPort() {
		return cyberarkPort;
	}

	@Override
	public String getHost() {
		return cyberarkHost;
	}
}
