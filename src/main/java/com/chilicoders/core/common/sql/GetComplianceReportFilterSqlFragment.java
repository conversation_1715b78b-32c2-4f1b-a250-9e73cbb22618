package com.chilicoders.core.common.sql;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.criteria.TargetCriteria;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.filters.FilteringSqlUtils;
import com.chilicoders.filters.target.TargetFilters;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
public class GetComplianceReportFilterSqlFragment extends NativeSqlFragment {

	private UserDetails user;
	private long complianceReportId;
	private TargetCriteria criteria;
	private Long[] targetIds;

	@Override
	public void buildSql() {
		addSql("reportid=? ", complianceReportId);
		appendFilter(TargetFilteringSqlFragments.getTargetIdsFilter("xipxid", criteria.getTargetIds()));

		if (user.isSubUser() && !user.hasAllTargetsAccess()) {
			appendFilter("xipxid IN (SELECT sh.xipxid FROM vsubhost sh WHERE xsubxid=?)", user.getSubUserId());
		}

		final String targetFilter = FilteringSqlUtils.getFiltering(criteria.getTargetFilteringData(), TargetFilters.TARGET_FILTER_FIELDS_WITHOUT_DYNAMICS, user);
		if (!StringUtils.isEmpty(targetFilter)) {
			appendFilter("xipxid = ANY(?)");
			addParam(targetIds != null && targetIds.length > 0 ? targetIds : new Long[] {-1L});
		}
	}
}
