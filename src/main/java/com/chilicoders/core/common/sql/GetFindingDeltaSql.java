package com.chilicoders.core.common.sql;

import java.time.ZonedDateTime;
import java.util.List;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
public class GetFindingDeltaSql extends NativeSqlStatement {

	private UserDetails user;

	private ZonedDateTime deltaFrom;
	private ZonedDateTime deltaTo;

	private String rbacFilter;
	private List<Object> rbacParams;

	private String findingsFilter;
	private List<Object> findingsParams;

	private String filter;
	private List<Object> params;

	/**
	 * True for REST API calls
	 * False for Report Service calls
	 */
	@Builder.Default
	private boolean returnAssetObject = true;

	@Builder.Default
	private boolean isAssetGroupDelta = false;

	private NativeSqlFragment summarySql;

	@Override
	public void buildSql() {
		if (returnAssetObject) {
			addSql("SELECT id, asset, firstseen, lastseen, unchanged FROM ( ");
			addSql("SELECT MAX(id) AS id, json_build_object('id', assetid, 'name', assetname)::JSONB AS asset, ");
		}
		else if (isAssetGroupDelta) {
			addSql("SELECT ag.id AS id, ag.id AS assetgroupid, ag.name AS assetgroupname, ");
		}
		else {
			addSql("SELECT assetid AS id, assetid, assetname, ");
		}

		addSql("COUNT(CASE WHEN (date_trunc('second', f.firstseen) > ? AND date_trunc('second', f.firstseen) <= ?) THEN f.id ELSE NULL END)::INTEGER AS firstseen, ");
		addSql("COUNT(CASE WHEN (date_trunc('second', f.lastseen) >= ? AND date_trunc('second', f.lastseen) < ?) THEN f.id ELSE NULL END)::INTEGER AS lastseen, ");
		addSql("COUNT(CASE WHEN (date_trunc('second', f.firstseen) <= ? AND date_trunc('second', f.lastseen) >= ?) THEN f.id ELSE NULL END)::INTEGER AS unchanged ");
		addParams(deltaFrom, deltaTo);
		addParams(deltaFrom, deltaTo);
		addParams(deltaFrom, deltaTo);

		if (!returnAssetObject) {
			addSql(", MIN(f.firstseen) AS firstseendate ");
			addSql(", MAX(f.lastseen) AS lastseendate ");
			addSql(", array_to_string(ARRAY_AGG(CASE WHEN (date_trunc('second', f.firstseen) > ? AND date_trunc('second', f.firstseen) <= ?)"
					+ "THEN COALESCE(cvssv3severity, cvssv2severity) || ':' || COALESCE(f.checkid::TEXT, '') || ':' || f.name ELSE NULL END ORDER BY COALESCE(cvssv3severity, cvssv2severity) DESC, f.id), '||') AS addedrisks ");
			addSql(", array_to_string(ARRAY_AGG(CASE WHEN (date_trunc('second', f.lastseen) >= ? AND date_trunc('second', f.lastseen) < ?)"
					+ "THEN COALESCE(cvssv3severity, cvssv2severity) || ':' || COALESCE(f.checkid::TEXT, '') || ':' || f.name ELSE NULL END ORDER BY COALESCE(cvssv3severity, cvssv2severity) DESC, f.id), '||') AS removedrisks ");
			addParams(deltaFrom, deltaTo);
			addParams(deltaFrom, deltaTo);
		}

		if (isAssetGroupDelta) {
			addSql("FROM assetgroupsview ag ");
			addSql("LEFT JOIN assetgroups ag2 ON ag.id = ANY(ag2.path) ");
			addSql("LEFT JOIN assetgroup_asset aga ON ag2.id = aga.assetgroupid ");
			addSql("LEFT JOIN findingsview f ON aga.assetid = f.assetid ");
			addSql("WHERE ag.customerid = ? AND ag.deleted IS NULL ");
			addParam(user.getCustomerId());
		}
		else {
			addSql("FROM findingsview f ");
			addSql("WHERE customerid = ? AND deleted IS NULL AND migration IS NULL ");
			addParam(user.getCustomerId());
		}

		if (summarySql != null) {
			appendFilter(summarySql);
		}

		if (!StringUtils.isEmpty(rbacFilter)) {
			appendFilter(rbacFilter);
			addParams(rbacParams);
		}

		if (!StringUtils.isEmpty(findingsFilter)) {
			appendFilter(findingsFilter);
			addParams(findingsParams);
		}

		if (isAssetGroupDelta) {
			addSql(" GROUP BY ag.id, ag.name ");
		}
		else if (returnAssetObject) {
			addSql(" GROUP BY f.assetid, f.assetname) d ");
		}
		else {
			addSql(" GROUP BY f.assetid, f.assetname ");
		}

		if (!StringUtils.isEmpty(filter)) {
			addSql("WHERE true ");
			appendFilter(filter);
			addParams(params);
		}
	}

	@Override
	public String[] getReturnAliases() {
		if (returnAssetObject) {
			return new String[] {"id", "asset", "firstseen", "lastseen", "unchanged"};
		}

		return isAssetGroupDelta
				? new String[] {
						"id",
						"assetgroupid",
						"assetgroupname",
						"firstseen",
						"lastseen",
						"unchanged",
						"firstseendate",
						"lastseendate",
						"addedrisks",
						"removedrisks",
						"unchangedrisks"
				}
				: new String[] {
						"id",
						"assetid",
						"assetname",
						"firstseen",
						"lastseen",
						"unchanged",
						"firstseendate",
						"lastseendate",
						"addedrisks",
						"removedrisks",
						"unchangedrisks"
				};
	}
}
