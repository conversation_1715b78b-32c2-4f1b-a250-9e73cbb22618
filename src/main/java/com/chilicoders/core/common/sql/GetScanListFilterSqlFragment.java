package com.chilicoders.core.common.sql;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.ArrayUtil;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.model.Product;
import com.chilicoders.model.Template;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
public class GetScanListFilterSqlFragment extends NativeSqlFragment {

	private long userId;
	private UserDetails user;
	private boolean checkType;
	private Product product;
	private boolean onlyComplianceScans;
	private boolean hasReportingPermission;
	private boolean hasScheduleManagementPermission;
	private boolean hasTargetManagementPermission;
	private String itype;
	@Builder.Default
	private long scheduleId = -1;
	private boolean excludeempty;

	@Override
	public void buildSql() {
		appendFilter("xuserxid = ? AND itype < 65");
		addParam(userId);

		if (checkType) {
			if (product.isPci()) {
				appendFilter("xtemplate = ?");
				addParam(Template.Pci.getId());
			}
			else if (product.isWas()) {
				appendFilter("xtemplate = ?");
				addParam(Template.Was.getId());
			}
			else if (product.isAppsecScale()) {
				appendFilter("xtemplate = ?");
				addParam(Template.AppsecScale.getId());
			}
			else {
				appendFilter("xtemplate != ? AND xtemplate != ? AND xtemplate != ?");
				addParam(Template.Pci.getId());
				addParam(Template.Was.getId());
				addParam(Template.AppsecScale.getId());
			}
		}

		appendFilter("bdeleted = 0");

		if (onlyComplianceScans) {
			appendFilter("compliancescan = true");
		}

		if (user.isSubUser() && !product.isPci() && !product.isWas() && !product.isAppsecScale()
				&& !hasReportingPermission && !hasScheduleManagementPermission) {
			appendFilter("v.xsoxid =ANY ((SELECT ARRAY_AGG(id) FROM schedules WHERE subuserid IN (SELECT xid FROM tsubusers WHERE xpathup ~ ?))::BIGINT[])");
			addParam("," + user.getSubUserId() + ",");
		}

		if (!user.hasAllTargetsAccess() && !product.isPci() && !product.isWas() && !product.isAppsecScale()) {
			appendFilter(
					"((v.itype IN (11,12,13,14,15) AND v.xsoxid =ANY ((SELECT ARRAY_AGG(id) FROM schedules WHERE subuserid IN (SELECT xid FROM tsubusers WHERE xpathup ~ ?))::BIGINT[])) OR (v.itype IN (18,19,20) "
							+ "AND (" + (hasTargetManagementPermission
							? "v.xid =ANY ((SELECT ARRAY_AGG(xscanjobxid) FROM tscanlogs v3 WHERE v3.xuserxid=? AND v3.xtemplate != ? AND v3.xtemplate != ? AND v3.itype IN (11,12,13,14,15))::BIGINT[]) OR "
							: "") + "v.xid "
							+ "=ANY ((SELECT ARRAY_AGG(DISTINCT xscanjobxid) FROM tscanlogs v2 WHERE v2.xtemplate != ? AND v2.xtemplate != ? AND v2.xipxid = ANY (ARRAY(SELECT xipxid FROM vsubhost WHERE xsubxid = ?)))::BIGINT[]))) OR (v.itype <= 7 AND v.xipxid "
							+ "=ANY ((SELECT ARRAY_AGG(xipxid) FROM vsubhost WHERE xsubxid = ?)::BIGINT[])))");
			addParam("," + user.getSubUserId() + ",");
			if (hasTargetManagementPermission) {
				addParam(userId);
				addParam(Template.Pci.getId());
				addParam(Template.Was.getId());
			}
			addParam(Template.Pci.getId());
			addParam(Template.Was.getId());
			addParam(user.getSubUserId());
			addParam(user.getSubUserId());
		}

		if (!user.hasAllScannerAccess()) {
			appendFilter(
					"(v.scannerid IN (SELECT xid FROM tscanners WHERE (xid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?)) OR (groupxid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?))) "
							+ "OR v.itype in (18,19,20) AND v.xid IN (SELECT DISTINCT xscanjobxid FROM tscanlogs v3 WHERE xscanjobxid = v.xscanjobxid AND scannerid IN (SELECT xid FROM tscanners "
							+ "WHERE (xid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?)) OR (groupxid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?)))))");
			addParam(user.getSubUserId());
			addParam(user.getSubUserId());
			addParam(user.getSubUserId());
			addParam(user.getSubUserId());
		}

		if (product.isWas()) {
			appendFilter("itype IN (8,9,18,19,20,21,22,23,24)");
			if (!user.hasAllWasAccess()) {
				if (excludeempty) {
					appendFilter(
							"EXISTS (SELECT xid FROM treportentrys t WHERE t.xscanjobxid = v.xid AND hasWebGranted(t.vctarget, (SELECT grantedweb FROM tsubusers WHERE xid = ?)))");
					addParam(user.getSubUserId());
				}
				else {
					appendFilter("(v.xsoxid IN (SELECT id FROM schedules WHERE template=? AND subuserid IN (SELECT xid FROM tsubusers WHERE xpathup ~ ?)))");
					addParam(Template.Was.getId());
					addParam("," + user.getSubUserId() + ",");
				}
			}
		}

		if (!StringUtils.isEmpty(itype)) {
			appendFilter("itype =ANY (?)");
			addParam(ArrayUtil.createIdArray(itype));
		}

		if (scheduleId > 0) {
			appendFilter("xsoxid = ?");
			addParam(scheduleId);
		}
	}
}
