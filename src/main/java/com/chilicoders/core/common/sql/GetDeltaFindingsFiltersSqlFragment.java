package com.chilicoders.core.common.sql;

import com.chilicoders.core.reporting.api.model.DeltaFindingsType;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;

@SuppressFBWarnings("EI_EXPOSE_REP2")
@Builder
public class GetDeltaFindingsFiltersSqlFragment extends NativeSqlFragment {

	private long userId;
	private DeltaFindingsType type;
	private String filter;
	private String targetFilter;
	private boolean isConsultancyMode;

	@Override
	public void buildSql() {
		appendFilter("r.xuserxid=? AND r.benabled = 1");
		addParam(userId);

		if (type != null) {
			switch (type) {
				case ADDED:
					appendFilter("r.xid IN (SELECT xid FROM deltafindings3 f3 WHERE added = 1)");
					break;
				case REMOVED:
					appendFilter("r.xid IN (SELECT xid FROM deltafindings3 f3 WHERE removed = 1)");
					break;
				case UNCHANGED:
					appendFilter("r.xid IN (SELECT xid FROM deltafindings3 f3 WHERE unchanged = 1)");
					break;
				default:
					break;
			}
		}

		if (isConsultancyMode) {
			appendFilter("r.bsupport = 1 AND r.supportexpiration > now()");
		}

		if (!StringUtils.isEmpty(filter)) {
			appendFilter(filter);
		}

		if (!StringUtils.isEmpty(targetFilter)) {
			addSql(" AND r.xipxid IN (");
			addSql("SELECT xipxid FROM (SELECT r.vctarget, CASE WHEN (r.firstreportxid = r.lastreportxid) THEN NULL ELSE r.firstreportdate END AS firstreportdate, r.lastreportdate, ");
			addSql("r.lastreportxid AS xid, r.xipxid, COALESCE((SELECT name FROM tscanners WHERE xid = r.scannerid AND xuserxid = ?), 'Local') AS scannername, ");
			addSql("COALESCE(SUM(f.added), 0) AS added, COALESCE(SUM(f.removed), 0) AS removed, COALESCE(SUM(f.unchanged), 0) AS unchanged FROM (");
			addSql("SELECT COALESCE(f2.xid, f1.xid) AS xid, COALESCE(f1.xipxid, f2.xipxid) AS xipxid, ");
			addSql("CASE WHEN (f1.xid IS NULL AND f2.xid IS NOT NULL OR f1.reportxid = f2.reportxid) THEN 1 ELSE 0 END AS added, CASE WHEN (f2.xid IS NULL AND f1.xid IS NOT NULL) THEN 1 ELSE 0 END AS removed, ");
			addSql("CASE WHEN (f1.xid IS NOT NULL AND f2.xid IS NOT NULL AND f1.reportxid != f2.reportxid) THEN 1 ELSE 0 END AS unchanged, ");
			addSql("CASE WHEN (f2.irisk = 4) THEN 1 ELSE 0 END AS high, CASE WHEN (f2.irisk = 2) THEN 1 ELSE 0 END AS medium, CASE WHEN (f2.irisk = 1) THEN 1 ELSE 0 END AS low, ");
			addSql("f1.date AS fdate, f2.date AS ldate FROM deltafindings1 f1 ");
			addSql("FULL JOIN deltafindings2 f2 ON (f1.iport = f2.iport AND f1.iprotocol = f2.iprotocol AND f1.vcvulnid = f2.vcvulnid AND f1.xipxid = f2.xipxid AND f1.vcvhost = f2.vcvhost)");
			addSql(") f FULL JOIN deltareports r ON r.xipxid = f.xipxid ");
			addSql("GROUP BY f.xipxid, r.vctarget, r.firstreportxid, r.lastreportxid, r.firstreportdate, r.lastreportdate, r.xipxid, r.scannerid) AS tmp ");
			addSql("WHERE " + targetFilter + ")");

			addParam(userId);
		}
	}
}
