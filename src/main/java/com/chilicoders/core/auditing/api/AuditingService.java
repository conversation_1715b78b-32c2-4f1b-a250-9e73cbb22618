package com.chilicoders.core.auditing.api;

import java.sql.SQLException;
import java.util.HashMap;

import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditInterface;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.util.StringUtils;

public interface AuditingService {
	/**
	 * Save an audit entry to the database.
	 *
	 * @param audit Audit entry.
	 */
	void audit(final AuditInterface audit) throws SQLException;

	/**
	 * Audit multiple updates.
	 *
	 * @param user A logged on user.
	 * @param ids Ids updated.
	 * @param appName Name of audit class.
	 * @param mode What update was made.
	 * @param customMessage Custom text.
	 * @param pci True if the update was for pci objects.
	 * @param appendFindingName Appends the finding name to the custom field.
	 */
	void auditMultiple(final UserDetails user, final Long[] ids, final AppName appName, final AuditMode mode, final String customMessage,
					   final boolean pci, final boolean appendFindingName);

	/**
	 * Create an audit entry for the database.
	 *
	 * @param user User details.
	 * @param id Item id.
	 * @param appName Application name for tthe changed object.
	 * @param auditMode Audit mode.
	 * @param name Delete name for the object.
	 * @param custom A custom text added to the entry.
	 * @param pci True for pci entries.
	 * @return Audit entry.
	 */
	AuditInterface createAudit(UserDetails user, long id, AppName appName, AuditMode auditMode, String name, String custom, boolean pci);

	/**
	 * Create and store a subscription audit.
	 *
	 * @param user User details.
	 * @param action Action performed on the object.
	 * @param customerId Customer id.
	 * @param comment Comment for the audit entry.
	 */
	void subscriptionAudit(UserDetails user, String action, long customerId, String comment) throws SQLException;

	/**
	 * Add a log message to a log.
	 *
	 * @param log The message log.
	 * @param id Id to add the message under.
	 * @param message The message to log.
	 */
	default void addLogMessage(final HashMap<String, String> log, final String id, final String message) {
		if (log == null || StringUtils.isEmpty(id) || StringUtils.isEmpty(message)) {
			return;
		}

		String tmp = log.get(id);
		if (tmp == null) {
			tmp = "";
		}
		else {
			tmp += "\n";
		}

		tmp += message;

		log.put(id, tmp);
	}

}
