package com.chilicoders.core.rule.api;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.chilicoders.ruleengine.InstalledProductInfo;
import com.chilicoders.ruleengine.ProductResult;
import com.chilicoders.ruleengine.comparators.EvaluatorContextInterface;
import com.chilicoders.ruleengine.comparators.RuleContextInterface;
import com.chilicoders.util.StringUtils;

public interface RuleDefinitionInterface extends RuleEvaluator {
	int STRING_CMP = 0;
	int STRING_NOT_ZERO_LENGTH = 1;
	int STRING_REGEXP = 2;
	int FACT_GT = 3;
	int FACT_GTE = 4;
	int FACT_LT = 5;
	int FACT_LTE = 6;
	int FACT_MISSING = 7;
	int FACT_LT_BETWEEN = 8;
	int FACT_LTE_BETWEEN = 9;

	int VERSION_LT = 100;
	int VERSION_GT = 101;
	int VERSION_LTE = 102;
	int VERSION_EQ = 103;
	int VERSION_ANY = 104;
	int VERSION_GTE = 105;
	int VERSION_LT_IGNORE_BRANCH = 106;
	int VERSION_LTE_IGNORE_BRANCH = 107;
	int VERSION_BETWEEN = 109;
	int VERSION_BRANCH = 110;
	int VERSION_RE = 120;

	int SERVICE_AVAILABLE = 205;
	int ARCHITECTURE_CMP = 210;
	int MACHINECLASS_EQ = 211;
	int EDITION_EQ = 220;
	int SOFTWARE_EOL_AFTER = 290;
	int SOFTWARE_EOL_BEFORE = 291;

	/**
	 * Vulnerable if any of these patches are missing.
	 */
	int PATCH_ANY = 300;
	/**
	 * Vulnerable if all of these patches are missing.
	 */
	int PATCH_ALL = 301;
	/**
	 * Vulnerable if any of these patches are present.
	 */
	int PATCH_PRESENT_ANY = 303;
	/**
	 * Vulnerable if all of these patches are present.
	 */
	int PATCH_PRESENT_ALL = 304;
	int PATCH_GTE_ANY = 310;
	int PATCH_LT_ANY = 311;
	int PATCH_LTE_ANY = 312;
	int PATCH_NEQ_ANY = 313;
	int PATCH_EQ_ANY = 320;
	/**
	 * Not actually a comparison method, just a placeholder to know where patch comparisons end.
	 */
	int PATCH_LAST = 399;

	int CISCO_DEPRECATED = 400;
	int CISCO_LT = 401;
	int CISCO_LTE = 402;

	// The following comparisons are used by the compliance engine
	int PORT_IN_RANGE = 201;
	int PORT_NOT_IN_RANGE = 202;
	int PORT_EXACTLY = 203;
	int SERVICE_IN_RANGE = 206;
	int SERVICE_NOT_IN_RANGE = 207;
	int INSTALLED_APPLICATIONS = 230;
	int VALUE_AT_LEAST = 240;
	int VALUE_AT_MOST = 241;
	int VALUE_CHECKED = 242;
	int VALUE_EQUALS = 243;
	int VALUE_IN_LIST = 244;
	int VALUE_LIMIT_LIST = 245;
	int VALUE_OR = 246;
	int VALUE_NOT_SET = 247;
	int VALUE_DISABLED = 248;
	int REGISTRY_KEYS = 250;
	int RUNNING_SERVICES = 251;
	int BLACKLISTED_RUNNING_SERVICES = 252;
	int RUNNING_SERVICES_EXACTLY = 253;
	int REGISTRY_KEYS_DONTREPORT = 254;
	int VULNERABILITIES_FOUND = 260;
	int BLACKLISTED_FINDINGS = 261;
	int MISSING_PATCHES = 270;
	int FILECHECK = 280;
	int FILECONTENTCHECK = 281;
	int CHECKCONFIG = 282;
	int COMMANDEXECUTE = 283;
	int CHECKACCOUNT = 284;
	int CHECKWMIQUERY = 285;
	int CHECKDBQUERY = 286;

	int ANDORBRANCH = -1;

	/**
	 * Determines that the method of joining children is by an OR statement.
	 */
	byte BOOLEAN_OR = 0;
	/**
	 * Determines that the method of joining children is by an AND statement.
	 */
	byte BOOLEAN_AND = 1;

	int ANY_PORT = -2;

	long getRuleId();

	String getConditionKey();

	String getConditionType();

	String getConditionPath();

	int getComparisonType();

	String getConditionValue();

	String getGatheredInformationString();

	RuleEvaluator getParentEvaluator();

	default String getProductLowerCase() {
		return getProduct() == null ? null : getProduct().toLowerCase();
	}

	String getProduct();

	boolean isHideReport();

	boolean getPotentialFalsePositive();

	boolean isInformational();

	void setReportingHandledByParent(boolean value);

	void setId(long id);

	void setParent(long parentId);

	long getId();

	long getParent();

	String getConditionValue2();

	String getCheckPortExists();

	Object getGenericObject();

	boolean isCompliance();

	String getComplianceReason();

	/**
	 * Gets products to inspect.
	 *
	 * @param context Evaluator context.
	 * @param ruleContext Rule context.
	 * @return List of products results.
	 */
	default List<ProductResult> getProductsToInspect(final EvaluatorContextInterface context, final RuleContextInterface ruleContext) {
		if (ruleContext.getCurrentProducts().containsKey(getProductLowerCase())) {
			return ruleContext.getCurrentProducts().get(getProductLowerCase());
		}
		else {
			ruleContext.getCurrentProducts().put(getProductLowerCase(), new ArrayList<>());
			final List<InstalledProductInfo> infos = context.getProductCache().getProductInformation(getProductLowerCase());
			final List<ProductResult> res = new ArrayList<>();
			for (final InstalledProductInfo info : infos) {
				if (info.kernelPackage && context.onlyReportOnRunningKernel()) {
					if (info.isRunningKernelVersion) {
						res.add(new ProductResult(info));
					}
				}
				else {
					res.add(new ProductResult(info));
				}
			}
			return res;
		}
	}

	void setReport(boolean report);

	boolean shouldReport();

	boolean shouldEvaluateSamePort();

	String getHeader();

	/**
	 * Get hash value for comparison.
	 *
	 * @return Hash value
	 */
	default BigDecimal hashValue() {
		BigDecimal hash = new BigDecimal(0);
		if (getComparisonType() != ANDORBRANCH) {
			hash = new BigDecimal(Objects.hash(
					getComparisonType(),
					StringUtils.setEmpty(getProduct(), ""),
					getPotentialFalsePositive(),
					StringUtils.setEmpty(getConditionKey(), ""),
					StringUtils.setEmpty(getConditionType(), ""),
					StringUtils.setEmpty(getConditionPath(), ""),
					StringUtils.setEmpty(getConditionValue(), ""),
					StringUtils.setEmpty(getConditionValue2(), "")
			));
		}
		for (final RuleDefinitionInterface child : getChildren()) {
			hash = hash.add(child.hashValue());
		}
		return hash;
	}

	/**
	 * Creates a string of the rule definition.
	 *
	 * @param tabs Number of tabs to start the string.
	 * @return String representation.
	 */
	default String internalToString(final String tabs) {
		final StringBuilder res = new StringBuilder(tabs);
		if (getComparisonType() == ANDORBRANCH) {
			res.append(getBooleanMethod() == BOOLEAN_AND ? "AND" : "OR");
		}
		else {
			res.append("Comparison: " + getComparisonType());
			if (!StringUtils.isEmpty(getProduct())) {
				res.append(", Product: " + getProduct());
			}
			if (!StringUtils.isEmpty(getConditionKey())) {
				res.append(", Key: " + getConditionKey());
			}
			if (!StringUtils.isEmpty(getConditionType())) {
				res.append(", Type: " + getConditionType());
			}
			if (!StringUtils.isEmpty(getConditionPath())) {
				res.append(", Path: " + getConditionPath());
			}
			if (!StringUtils.isEmpty(getConditionValue())) {
				res.append(", Value: " + getConditionValue());
			}
			if (getPotentialFalsePositive()) {
				res.append(", FalsePositive");
			}
		}
		for (final RuleDefinitionInterface child : getChildren()) {
			res.append("\n").append(child.internalToString(tabs + "\t"));
		}
		return res.toString();
	}

	/**
	 * Gets gathered information.
	 *
	 * @param facts List of probe facts.
	 * @return Gathered information.
	 */
	default String getInformationTable(final List<? extends RuleFact> facts) {
		if (!StringUtils.isEmpty(getGatheredInformationString())) {
			final StringBuilder info = new StringBuilder();
			final Matcher matcher = Pattern.compile("(.*)(§\\[(.*?)\\])(.*)", Pattern.MULTILINE | Pattern.DOTALL).matcher(getGatheredInformationString());
			if (matcher.matches()) {
				int columns = 0;
				final String headers = StringUtils.setEmpty(matcher.group(3), "").trim();
				if (StringUtils.isEmpty(headers)) {
					for (final RuleFact fact : facts) {
						final String[] values = StringUtils.setEmpty(fact.getPath(), "").split(";");
						columns = Math.max(values.length, columns);
					}
				}
				else {
					columns = headers.split(";").length;
				}
				info.append("<rtab><columns>" + columns + "</columns>");
				if (!StringUtils.isEmpty(headers)) {
					info.append("<hdr>");
					for (final String header : headers.split(";")) {
						info.append("<col>" + header.trim() + "</col>");
					}
					info.append("</hdr>");
				}
				for (final RuleFact fact : facts) {
					final String[] values = StringUtils.setEmpty(fact.getPath(), "").split(";");
					info.append("<row>");
					for (int i = 0; i < columns; i++) {
						info.append("<col>" + (i < values.length ? values[i] : "") + "</col>");
					}
					info.append("</row>");
				}
				info.append("</rtab>");

				return getGatheredInformationString().replace(matcher.group(2), info.toString());
			}

			return getGatheredInformationString();
		}

		return null;
	}
}
