package com.chilicoders.core.rule.impl.jpa;

import java.sql.SQLException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.rule.api.RuleInterface;
import com.chilicoders.core.rule.impl.AbstractRuleDao;
import com.chilicoders.db.query.JpaNativeStatementExecutor;
import com.chilicoders.db.query.NativeSqlStatement;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Data access for rules, using JPA and hibernate.
 */
@SuppressFBWarnings("EI_EXPOSE_REP2")
@Component
public class RuleDaoImpl extends AbstractRuleDao {

	private JpaNativeStatementExecutor statementExec;

	@Autowired
	public RuleDaoImpl(final JpaNativeStatementExecutor statementExec) {
		this.statementExec = statementExec;
	}

	@Override
	protected List<? extends RuleInterface> getRules(final NativeSqlStatement statement) throws SQLException {
		return statementExec.executeDTO(statement, RuleImpl.class);
	}
}
