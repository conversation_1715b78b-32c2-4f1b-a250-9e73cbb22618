package com.chilicoders.core.rule.impl.jpa;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import com.chilicoders.core.reporting.api.SpecialNoteType;
import com.chilicoders.core.reporting.api.model.Finding;
import com.chilicoders.core.reporting.api.model.VulnerabilityType;
import com.chilicoders.core.rule.api.RuleFact;
import com.chilicoders.core.rule.api.RuleInterface;
import com.chilicoders.core.rule.api.VulnerabilityReference;
import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.db.StringArrayType;
import com.chilicoders.model.SolutionType;

import com.vladmihalcea.hibernate.type.array.EnumArrayType;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
@Getter
@Entity(name = "trules")
@TypeDefs({
		@TypeDef(name = "string-array", typeClass = StringArrayType.class),
		@TypeDef(name = "enum_type", typeClass = PostgreSQLEnumType.class),
		@TypeDef(name = "enum_array_type",
				typeClass = EnumArrayType.class,
				parameters = {
						@Parameter(
								name = EnumArrayType.SQL_ARRAY_TYPE,
								value = "specialnotetype"
						)
				}
		)
})
public class RuleImpl implements RuleInterface {
	@Id
	@Column(name = "ruleid")
	private long id;

	@Column
	private Short riskLevel;

	@Column
	private String solution;

	@Column
	private Short solutionType;

	@Column
	private String solutionProduct;

	@Column
	@Setter
	private String name;

	@Column
	@Temporal(TemporalType.TIMESTAMP)
	@Setter
	private Date created;

	@Column
	@Setter
	private String family;

	@Column
	private String cve;

	@Column
	private String bugTraq;

	@Column
	private Boolean hasExploits;

	@Column
	@Setter
	private String cvssVector;

	@Column
	private String cvssV3Vector;

	@Column
	private String nvdCvssVector;

	@Column
	private Double cvssScore;

	@Column
	private boolean informational;

	@Column
	private boolean pciFail;

	@Column
	@Setter
	private String description;

	@Column
	@Type(type = "enum_type")
	@Enumerated(EnumType.STRING)
	@Setter
	private VulnerabilityType vulnerabilityType = VulnerabilityType.Unknown;

	@Column
	private Integer cwe;

	@Column
	@Getter
	private boolean potentialFalsePositive;

	@Column
	@Getter
	@Type(type = "enum_type")
	@Enumerated(EnumType.STRING)
	@Setter
	private SpecialNoteType specialNote;

	@Column
	@Getter
	@Setter
	@Type(type = "enum_array_type")
	private SpecialNoteType[] specialNotes;

	@Column
	@Setter
	@Type(type = "string-array")
	private String[] requiredProduct;

	@Column
	@Getter
	@Setter
	private Timestamp updated;

	@Column(name = "isscript")
	private boolean script;

	@Column(name = "was_informational")
	private Boolean wasInformational;

	@Transient
	private final List<VulnerabilityReference> references = new ArrayList<>();

	public boolean hasExploits() {
		return hasExploits != null && hasExploits;
	}

	@Override
	public List<VulnerabilityReference> getXRefs() {
		throw new IllegalStateException("Not implemented in JPA");
	}

	@Override
	public void addVulnerabilityReference(final VulnerabilityReference vulnerabilityReference) {
		references.add(vulnerabilityReference);
	}

	@Override
	public boolean isCompliance() {
		return false;
	}

	@Override
	public boolean isCompliant() {
		return false;
	}

	@Override
	public boolean getWasInformation() {
		return wasInformational != null && wasInformational;
	}

	@Override
	public Collection<RuleFact> getComplianceFacts() {
		return new ArrayList<>();
	}

	public void setId(final Number id) {
		this.id = id.longValue();
	}

	public int getRiskLevel() {
		return riskLevel != null ? riskLevel : Finding.RiskLevel.INFORMATIONAL.getValue();
	}

	public int getSolutionType() {
		return solutionType != null ? solutionType : SolutionType.Unspecified.ordinal();
	}
}
