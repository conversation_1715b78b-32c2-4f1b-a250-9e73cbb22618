package com.chilicoders.core.rule.impl.jpa;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.core.reporting.impl.jpa.VulnerabilityReferenceImpl;
import com.chilicoders.core.rule.api.VulnerabilityInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Entity(name = "tvultexts")
@Getter
@Setter
@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
public class VulnerabilityImpl implements VulnerabilityInterface {
	@Id
	@Column(name = "xid")
	private long id;

	@Column(name = "vccvssvector")
	private String cvssVector;

	@Column(name = "icvss")
	private int cvss;

	@Column(name = "ipcicvss")
	private int pciCvss;

	@Column(name = "vcname")
	private String name;

	@Column(name = "vcfam")
	private String family;

	@Column(name = "irisk")
	private int risk;

	@Column(name = "icrc")
	private String crc;

	@Column(name = "dcreated")
	private Date created;

	private Date scriptCreated;

	@Column(name = "cdesc")
	private String description;

	private int solutionType;

	private String solutionProduct;

	private String solutionTitle;

	@Column(name = "csol")
	private String solution;

	@Column(name = "external")
	private boolean deleted;

	@Column(name = "vcbug")
	private String bugTraq;

	@Column(name = "vccve")
	private String cve;

	@Transient
	private List<VulnerabilityReferenceImpl> xrefs = new ArrayList<>();

	@Transient
	private List<Pair<String, String>> facts = new ArrayList<>();

	@Transient
	private List<String> packageFacts = new ArrayList<>();

	public void addXref(final VulnerabilityReferenceImpl reference) {
		xrefs.add(reference);
	}

	public void addFact(final Pair<String, String> fact) {
		facts.add(fact);
	}

	public void addPackageFact(final String packageFact) {
		packageFacts.add(packageFact);
	}
}
