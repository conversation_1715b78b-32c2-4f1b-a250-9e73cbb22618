package com.chilicoders.core.user.api;

/**
 * The existing OPI permissions.
 */
public enum Permission {
	COMPLIANCE("COMPL<PERSON>NCE_ENABLED"),
	MARK_COMPLIANCE_EXCEPTIONS("MARK<PERSON><PERSON><PERSON>IANCEEXCEPTIONS"),
	ANSWER_COMPLIANCE_QUESTIONS("ANSWERCOMPLIANCEQUESTIONS"),
	APPROVE_COMPLIANCE_QUESTIONS("APPROVE<PERSON>MPLIANCEQUESTIONS"),
	EDIT_COMPLIANCE_POLICIES("ALLOWEDITCOMPLIANCEPOLICIES"),
	REPORTING("BOREPORTS"),
	DELETE_REPORT("B<PERSON>ELETEREPORT"),
	REPORT_FALSE_POSITIVES("BODISABLE"),
	ACCEPT_RISK("BACCEPTRISK"),
	DASHBOARD("DASHBOARD"),
	PCI_SCHEDULING("PCISCHEDULING"),
	PCI_REPORTING("PCIREPORTING"),
	PCI_DISPUTING("PCIDISPUTING"),
	PCI_SCOPING("PCISCOPING"),
	PCI_ANY("PCIANY"),
	WAS_SCOPING("WEBAPPADMIN"),
	WAS_REPORTING("WEBAPPREPORTING"),
	WAS_DELETE_REPORT("WEBAPPDELETEREPORT"),
	WAS_GENERAL("WASGENERAL"),
	HIAB_MANAGEMENT("BHADMIN"),
	POLICY_MANAGEMENT("BOSETTINGS"),
	TICKETS("TICKETS"),
	TICKET_MANAGEMENT("BTICKETMANAGEMENT"),
	GRANT_ALL_TICKETS("GRANTALLTICKETS"),
	READ_AUDIT_LOGS("READ_AUDITLOGS"),
	READ_LICENSE("READLICENSE"),
	TARGET_MANAGEMENT("BOADMINGROUPS"),
	USER_MANAGEMENT("BSUBADMIN"),
	USER_ROLE_MANAGEMENT("BADMINUSERGROUP"),
	SCHEDULE_MANAGEMENT("BOSCHEDULES"),
	STOP_SCAN("STOPSCAN"),
	MANAGED_SERVICES_COMMENT("MANAGEDSERVICESCOMMENT"),
	VERIFY_SCAN("VERIFYSCAN"),
	VULNERABILITY_MANAGEMENT("BOVULTEXT"),
	SWAT("WEB"),
	SWAT_COMMENT("SWATCOMMENT"),
	SWAT_VERIFICATION("SWATVERIFICATION"),
	SWAT_DISCUSSION("SWATDISCUSSION"),
	SWAT_RISKS("SWATRISKS"),
	SUBMIT_SCOPING("SUBMITSCOPING"),
	EVENT_MANAGEMENT("EVENTMANAGEMENT"),
	MANAGED_SERVICES("MANAGEDSERVICES"),
	WASX("WASX"),
	RULE_MANAGEMENT("RULEMANAGEMENT"),
	RULE_ADMIN("RULEADMIN"),
	EDIT_RULES("EDITRULES");

	private String dbName;

	private Permission(final String dbName) {
		this.dbName = dbName;
	}

	/**
	 * Get a permission from database name.
	 *
	 * @param name Name to look for.
	 * @return Permission
	 */
	public static Permission getFromDatabaseName(final String name) {
		for (final Permission permission : values()) {
			if (permission.dbName.equals(name)) {
				return permission;
			}
		}
		return null;
	}
}