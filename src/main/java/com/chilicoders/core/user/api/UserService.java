package com.chilicoders.core.user.api;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.openpgp.PGPPublicKey;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.user.api.model.UserCertificate;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.Product;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.util.BCrypt;

/**
 * Service interface for user-related operations.
 */
public interface UserService {
	enum AttributeMode {
		ALL,
		SCHEDULE,
		TARGET
	}

	/**
	 * User id for HIAB user.
	 */
	long HIABUSERID = 11;

	/**
	 * Retrieves the user details for the specified user.
	 *
	 * @param userId the user id
	 * @param isSubUser specifies if the user is a subuser or not
	 * @return the user details
	 */
	UserDetails getUserDetails(final Long userId, final boolean isSubUser) throws SQLException;

	/**
	 * Retrieves a list of subusers.
	 *
	 * @param nativeSqlFragment SQL fragment to define which subusers to fetch.
	 * @return the subuser list
	 */
	List<? extends UserDetails> getSubusers(NativeSqlFragment nativeSqlFragment) throws SQLException;

	/**
	 * Retrieves the user details for the specified user.
	 *
	 * @param userId the user id
	 * @param isSubUser specifies if the user is a subuser or not
	 * @param isSwatReport specifies if the report is a swat report or not
	 * @param reportUserId - users with certain permissions are entitled to generate reports for another user, in certain circumstances.
	 * @return the user details
	 */
	UserDetails getReportUser(final Long userId, final boolean isSubUser, final boolean isSwatReport, final Long reportUserId) throws SQLException;

	/**
	 * Retrieves the user attributes for the specified user.
	 *
	 * @param userId the user id
	 * @param mode Mode of the attribute to fetch.
	 * @return a list of user attributes
	 */
	List<? extends UserAttribute> getUserAttributes(final Long userId, final AttributeMode mode) throws SQLException;

	/**
	 * Retrieves user features for the specified main user.
	 *
	 * @param userId the user id
	 * @return A set of features.
	 */
	UserFeatures getUserFeatures(final Long userId) throws SQLException;

	/**
	 * Get a users defined certificates.
	 *
	 * @param feature The feature to get certificate for.
	 * @param userId User id.
	 * @return User certificate.
	 * @throws SQLException On database errors.
	 */
	UserCertificate getUserCertificate(final String feature, final long userId) throws SQLException;

	/**
	 * Get email signature for a user.
	 *
	 * @param user User details.
	 * @param language Language to get signature in.
	 * @return Email signature.
	 * @throws SQLException On database errors.
	 */
	String getSignature(final UserDetails user, final String language) throws SQLException, IOException;

	/**
	 * Get a users settings for ticket.
	 *
	 * @param userId User id.
	 * @return Ticket settings.
	 */
	WorkflowSettings getTicketSettings(Long userId);

	/**
	 * Get a pgp public key for a user for sending emails.
	 *
	 * @param user User details.
	 * @param name Name of the pgp key.
	 * @return PGPPublicKey
	 */
	PGPPublicKey getEmailEncryptionKey(UserDetails user, String name);

	/**
	 * Retrieves the company name for a HIAB.
	 *
	 * @return the company name for an enrolled hiab or null, for an unenrolled hiab.
	 */
	String getHiabCompanyName() throws SQLException;

	/**
	 * Get number of scans left for a user.
	 *
	 * @param userId User id.
	 * @param product Product to scan.
	 * @param external Is the scan external.
	 * @return Number of scans left on account.
	 */
	long getScansLeft(final long userId, final Product product, final boolean external) throws SQLException;

	/**
	 * Used to set the ISECURITYLEFT or IPCISCANSLEFT field in the database for the user.
	 *
	 * @param userId The user id to deduct scans for.
	 * @param scans Number of scans to deduct from user.
	 * @param product The product to check.
	 * @param externalScan <code>true</code> when using Outscan as a scanner appliance, <code>false</code> otherwise.
	 */
	void setSecurityLeft(final long userId, final long scans, final Product product, final boolean externalScan);

	/**
	 * Encrypt password.
	 *
	 * @param configService Configuration service.
	 * @param bcrypt True if using bcrypt.
	 * @param pass Password to encrypt.
	 * @return Encrypted password.
	 */
	default String cryptPassword(final ConfigurationService configService, final boolean bcrypt, final String pass) {
		final Logger LOG = LogManager.getLogger(UserService.class);
		try {
			if (pass == null) {
				return null;
			}

			if (bcrypt) {
				return BCrypt.hashpw(pass, BCrypt.gensalt(configService.getProperty(ConfigurationIntKey.passwd_saltlength)), false);
			}

			final MessageDigest mdDigest = MessageDigest.getInstance("MD5");
			final byte[] result = mdDigest.digest(pass.getBytes(UTF_8));

			final ByteArrayInputStream in = new ByteArrayInputStream(result);
			final StringBuilder st = new StringBuilder(in.available());
			int index;
			while ((index = in.read()) != -1) {
				if (index < 16) {
					st.append("0");
				}
				st.append(Integer.toHexString(index));
			}
			final String newpass = st.toString().toUpperCase();

			LOG.debug("Password in hex: {}", newpass);

			return newpass;
		}
		catch (final NoSuchAlgorithmException e) {
			LOG.error("cryptPassword failed:", e);
			return null;
		}
	}

	/**
	 * Validates the license of the given user.
	 *
	 * @param configService Configuration service
	 * @param user The user
	 */
	default void checkLicense(final ConfigurationService configService, final UserDetails user) {
		final Logger LOG = LogManager.getLogger(UserService.class);

		if (configService.isHiabEnabled()) {
			if (!configService.isRegistered()) {
				LOG.info("Hiab license was not found!");
				throw new InputValidationException("_NO_HIAB_LICENSE", ErrorCode.InputValidationFailed);
			}
		}
		else {
			if (!user.hasOutscanLicense()) {
				LOG.info("Outscan license was not found!");
				throw new InputValidationException("_NO_OUTSCAN_LICENSE", ErrorCode.InputValidationFailed);
			}
		}
	}
}
