package com.chilicoders.core.user.api;

import java.time.Duration;
import java.util.Date;
import java.util.List;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.reporting.api.model.ReportTemplateScanType;
import com.chilicoders.core.user.api.model.FarsightProduct;
import com.chilicoders.db.objects.api.ResourceGroupInterface;
import com.chilicoders.rest.models.ResourceType;

/**
 * Details for a user.
 */
public interface UserDetails extends WorkflowSettings {

	/**
	 * @return User id.
	 */
	Long getId();

	/**
	 * @return Main user id.
	 */
	long getMainUserId();

	/**
	 * @return Subuser id.
	 */
	long getSubUserId();

	/**
	 * @return true if account is active.
	 */
	boolean isActive();

	/**
	 * @return true if user is a subuser.
	 */
	boolean isSubUser();

	/**
	 * @return Company name.
	 */
	String getCompany();

	/**
	 * @return User name in the system.
	 */
	String getUsername();

	/**
	 * @return User fullname, first + last
	 */
	String getFullName();

	/**
	 * @return Users first name.
	 */
	String getFirstName();

	/**
	 * @return Users sirname.
	 */
	String getLastName();

	/**
	 * @return User email.
	 */
	String getEmail();

	/**
	 * @return Country code of users country.
	 */
	String getCountryCode();

	/**
	 * @return Users country.
	 */
	String getCountry();

	/**
	 * @return Users state if applicable.
	 */
	String getState();

	/**
	 * @return true if user has AWS enabled.
	 */
	boolean isAwsEnabled();

	/**
	 * @return true if customer counts ports as vulnerabilities in exported reports.
	 */
	boolean isPortAsVulnerability();

	/**
	 * @return true if user is a superuser.
	 */
	boolean isSuperUser();

	/**
	 * @return Sales organization id.
	 */
	long getSalesOrganizationId();

	/**
	 * @return Custom company name for reports, configured by the user.
	 */
	String getCustomCompanyName();

	/**
	 * @return User language setting.
	 */
	String getLanguage();

	/**
	 * @return Users gmt offset.
	 */
	double getGmtOffset();

	/**
	 * @return Date format user has specified.
	 */
	String getDateFormat();

	/**
	 * @return Time format user has specified.
	 */
	String getTimeFormat();

	/**
	 * @return Allowed number of ips in Outscan,-1 is unlimited.
	 */
	long getOutscanIp();

	/**
	 * @return Allowed number of web apps.
	 */
	long getMaxWebapps();

	/**
	 * @param hiab True if hiab.
	 * @return Allowed number of appsec scale.
	 */
	long getMaxAppsecScale(final boolean hiab);

	/**
	 * @return Allowed number of appsec scale external.
	 */
	long getMaxAppsecScaleExternal();

	/**
	 * @return true if user has swat.
	 */
	boolean hasSwat();

	/**
	 * @return true if user can access all was.
	 */
	boolean hasAllWasAccess();

	/**
	 * @return Commaseparated list of user roles.
	 */
	String getRoles();

	/**
	 * @return Pci certificate number.
	 */
	String getPciCertificate();

	/**
	 * @return Name to put in report.
	 */
	String getReportCompany();

	long getRiskAge();

	/**
	 * @return true if user has access to all targets.
	 */
	boolean hasAllTargetsAccess();

	/**
	 * @return Password to be used for exported reports.
	 */
	String getReportPassword();

	/**
	 * @return Report header for exported reports.
	 */
	String getCustomReportHeader();

	/**
	 * @return Report footer for exported reports.
	 */
	String getCustomReportFooter();

	/**
	 * @return True if user has access to all scanners.
	 */
	boolean hasAllScannerAccess();

	/**
	 * @return User permissions for the user.
	 */
	UserPermissions getPermissions();

	/**
	 * Check if the user has a role.
	 *
	 * @param rolesRequired List of roles.
	 * @return true if the user has one of the roles.
	 */
	boolean hasRoles(final Role... rolesRequired);

	/**
	 * @return Number of scans left on license.
	 */
	long getNormalScansLeft();

	/**
	 * @return Number of external scans left on license.
	 */
	long getExternalScansLeft();

	/**
	 * @return Number of PCI scans left on license.
	 */
	long getPciScansLeft();

	/**
	 * @return Number of was scans left on license.
	 */
	long getWasScansLeft();

	/**
	 * @return Number of external was scans left on license.
	 */
	long getExternalWasScansLeft();

	/**
	 * @return Number of hiab appsec scale scans left on license.
	 */
	long getHiabAppsecScaleApps();

	/**
	 * @return Number of allowed wasx apps.
	 */
	long getOutscanWasXApps();

	/**
	 * @return true if user has acces to all of swat.
	 */
	boolean hasAllSwatAccess();

	/**
	 * @return true if user has enabled to calculate next scan date in target management.
	 */
	boolean calculateNextScanDate();

	/**
	 * @return Get customer information.
	 */
	CustomerInformation getCustomerInformation();

	/**
	 * @return HTML or text emails configuration for the user.
	 */
	EmailType getEmailType();

	/**
	 * @return Email signature to be added to emails.
	 */
	String getSignature();

	/**
	 * @return true to send alert from tasks.
	 */
	boolean sendTaskAlert();

	/**
	 * @return encryption key used for emails.
	 */
	String getEmailEncryptionKey();

	/**
	 * @return customer id.
	 */
	Integer getCustomerId();

	/**
	 * @return customer UUID.
	 */
	String getCustomerUuid();

	/**
	 * @param product Farsight product
	 * @return true if user has farsight.
	 */
	boolean hasFarsight(final FarsightProduct product);

	/**
	 * @return Allowed number of hiab external ips.
	 */
	long getHiabExternalIp();

	/**
	 * @return true if user has agents enabled.
	 */
	boolean isAgentsEnabled();

	String getSslClientCaFile();

	/**
	 * @return true if user has was license.
	 */
	default boolean hasWasLicense() {
		return getMaxWebapps() != 0;
	}

	/**
	 * @return date when user is no longer active.
	 */
	Date getExpireDate();

	/**
	 * @return If user is a pci trial customer.
	 */
	boolean isPciTrial();

	/**
	 * @return true if user is a trial customer.
	 */
	boolean isSecurityTrial();

	/**
	 * @return User id for sale of the customer.
	 */
	long getSalesPerson();

	/**
	 * @return truie if user configured to remove reports after sending emails.
	 */
	boolean isRemoveReport();

	long getOutscanScans();

	/**
	 * @return ALlowed number of PCI targets.
	 */
	long getPciTargetLimit();

	/**
	 * @return true if user has analytics.
	 */
	boolean hasAnalytics();

	default boolean hasLimitResources(final List<ResourceType> resourceTypes) {
		return hasLimitResources(resourceTypes.toArray(new ResourceType[0]));
	}

	/**
	 * @param types Resource types to check
	 * @return true if user has limited resources.
	 */
	default boolean hasLimitResources(final ResourceType... types) {
		return false;
	}

	default List<ResourceGroupInterface> getResourceGroups() {
		return null;
	}

	/**
	 * @return true if user is logged in in consultancy mode.
	 */
	boolean isConsultancyMode();

	boolean getConsultancyMode();

	void setConsultancyMode(final boolean consultancyMode);

	/**
	 * @return true if user has access to all aws arns.
	 */
	boolean hasAllAwsArnAccess();

	/**
	 * @param configurationService Configuration service.
	 * @return true if user is pci only customer.
	 */
	boolean isUserPciOnly(final ConfigurationService configurationService);

	long getHiabIp();

	long getPciIp();

	/**
	 * @return true if user has compliance.
	 */
	boolean hasCompliance();

	/**
	 * @return true if user has wasx schedule.
	 */
	boolean getHasWasxSchedule();

	/**
	 * @return true if user is logged on.
	 */
	boolean isLoggedOn();

	/**
	 * @return If in consultancy mode will return the consultancy user.
	 */
	UserDetails getConsultancyUser();

	/**
	 * @return If terms should be shown at login.
	 */
	boolean shouldShowTerms();

	Boolean isShowValidate();

	/**
	 * @return 2-factor authentication policy.
	 */
	int getPolicyTwoFactorAuthentication();

	/**
	 * @return Users 2-factor setting.
	 */
	int getTwoFactorAuthentication();

	void setShowValidate(boolean validate);

	/**
	 * @return Number of logons user has done.
	 */
	long getLogons();

	/**
	 * Set ip of last logon.
	 *
	 * @param ip IP
	 */
	void setLastLogonIp(String ip);

	String getCookieToken();

	/**
	 * Set user logged on state.
	 *
	 * @param loggedOn Logged on state.
	 */
	void setLoggedOn(boolean loggedOn);

	void setCookieToken(String token);

	/**
	 * Set consultance user.
	 *
	 * @param userDetails Consultancy user.
	 */
	void setConsultancyUser(UserDetails userDetails);

	/**
	 * @return Last user logon ip.
	 */
	String getLastLogonIp();

	/**
	 * Set if user has aws enabled.
	 *
	 * @param enabled New value.
	 */
	void setAwsEnabled(boolean enabled);

	/**
	 * @return Duration between agent callbacks.
	 */
	Duration getDefaultAgentCallHomeFrequency();

	/**
	 * Set if schedules cant be set on target level.
	 *
	 * @param forceGroupScheduling New value.
	 */
	void setForceGroupScheduling(Boolean forceGroupScheduling);

	void setGroupsInScheduling(boolean groupsInScheduling);

	void setConcurrentScans(long concurrentScans);

	void setTargetInactiveDiscoveries(long targetInactiveDiscoveries);

	/**
	 * Set users default scan policy.
	 *
	 * @param defaultScanPolicy Id of scan policy.
	 */
	void setDefaultScanPolicy(long defaultScanPolicy);

	/**
	 * Set default max scan time.
	 *
	 * @param defaultMaxScanTime New value.
	 */
	void setDefaultMaxScanTime(long defaultMaxScanTime);

	void setScanPolicyOwnerShip(int scanPolicyOwnerShip);

	void setDisableUnsafeChecks(boolean disableUnsafeChecks);

	/**
	 * Set if user wants audit on target management.
	 *
	 * @param auditTargetManagement New value.
	 */
	void setAuditTargetManagement(boolean auditTargetManagement);

	/**
	 * Set if user wants audit on schedule management.
	 *
	 * @param auditScheduleManagement New value.
	 */
	void setAuditScheduleManagement(boolean auditScheduleManagement);

	/**
	 * Set if user wants audit on scan policy management.
	 *
	 * @param auditScanPolicyManagement New value.
	 */
	void setAuditScanPolicyManagement(boolean auditScanPolicyManagement);

	void setAuditRiskAcceptance(boolean auditRiskAcceptance);

	void setAuditChangeRiskLevel(boolean auditChangeRiskLevel);

	void setManagedServicesLimited(boolean managedServicesLimited);

	/**
	 * Set if target management should calculate next scan time on targets.
	 *
	 * @param calculatenextscandate New value.
	 */
	void setCalculateNextScandate(boolean calculatenextscandate);

	/**
	 * Set if a target can exist in multiple target groups.
	 *
	 * @param allowTargetMultipleGroups New value.
	 */
	void setAllowTargetMultipleGroups(boolean allowTargetMultipleGroups);

	void setCompliancePolicyOwnership(boolean compliancePolicyOwnership);

	/**
	 * Set if user allows compliance only scanning.
	 *
	 * @param allowComplianceOnlyScanning New value.
	 */
	void setAllowComplianceOnlyScanning(boolean allowComplianceOnlyScanning);

	/**
	 * Set global scanning blacklist.
	 *
	 * @param globalIgnoreTargetList New value.
	 */
	void setGlobalIgnoreTargetList(String globalIgnoreTargetList);

	/**
	 * Set if global blacklist should be used by default.
	 *
	 * @param useGlobalIgnoreTargetListByDefault New value.
	 */
	void setUseGlobalIgnoreTargetListByDefault(boolean useGlobalIgnoreTargetListByDefault);

	void setSslClientCaFile(String sslClientCaFile);

	/**
	 * Set if password needs to be changed on logon.
	 *
	 * @param changePasswordOnLogon New value.
	 */
	void setChangePasswordOnLogon(boolean changePasswordOnLogon);

	/**
	 * @return User mobile number.
	 */
	String getMobilePhoneNumber();

	/**
	 * @return true if audit is needed for risk change level.
	 */
	boolean needAuditChangeRiskLevel();

	/**
	 * @return User parent id.
	 */
	long getParentId();

	/**
	 * @return true if audit is needed for risk acceptance.
	 */
	boolean needAuditRiskAcceptance();

	/**
	 * @return true if audit is needed for target management.
	 */
	boolean needAuditTargetManagement();

	/**
	 * @return true if password change is required.
	 */
	boolean isRequirePasswordChange();

	/**
	 * @return Users authentication method.
	 */
	int getAuthenticationMethod();

	/**
	 * Set if password change is required.
	 *
	 * @param requirePasswordChange New value.
	 */
	void setRequirePasswordChange(boolean requirePasswordChange);

	/**
	 * Set if csrf validation should be enabled.
	 *
	 * @param csrfValidation New value.
	 */
	void setCsrfValidation(boolean csrfValidation);

	/**
	 * Set a list of ips from which the user can login.
	 *
	 * @param loginIpRestriction New value.
	 */
	void setLoginIpRestriction(String loginIpRestriction);

	String getCredentialId();

	/**
	 * @return Get subuser parent.
	 */
	long getSubParentId();

	/**
	 * Set date when cookies are accepted.
	 *
	 * @param date New value.
	 */
	void setCookiesAccepted(Date date);

	/**
	 * Set if user is in admin mode.
	 *
	 * @param adminMode New value.
	 */
	void setAdminMode(boolean adminMode);

	/**
	 * @return Users last logon date.
	 */
	Date getLastLogonDate();

	/**
	 * @return User state code if applicable.
	 */
	String getStateCode();

	/**
	 * @param readOnly Readonly
	 * @return Trie if user is allowed user management.
	 */
	boolean allowUserManagement(boolean readOnly);

	/**
	 * @return User time zone.
	 */
	String getTimezone();

	/**
	 * @return True if guide should be shown.
	 */
	boolean showGuide();

	/**
	 * @return True if pci information should be shown.
	 */
	boolean showPciInfo();

	/**
	 * @return Start day of the week.
	 */
	int getStartDayOfWeek();

	/**
	 * @return True if in admin mode.
	 */
	boolean isAdminMode();

	/**
	 * @return Get language user is translator for.
	 */
	String getTranslation();

	/**
	 * @return Session timeout in minutes.
	 */
	int getSessionTimeout();

	/**
	 * @return true if audit is required for schedule management.
	 */
	boolean needAuditScheduleManagement();

	/**
	 * @return true if audit is required for scan policy management.
	 */
	boolean needAuditScanPolicyManagement();

	/**
	 * @return true if csrf validation is enabled.
	 */
	boolean hasCsrfValidation();

	long getHiabMaxWebapps();

	String getSalesOrganizations();

	int getTicketDay(int ticketPriority);

	int getTicketActive();

	/**
	 * @param ticketPriority Ticket priority
	 * @return Label for ticket priority.
	 */
	String getTicketLabel(int ticketPriority);

	boolean getIncludeTicketVulnInfo();

	boolean hasForceGroupScheduling(boolean readOnly);

	/**
	 * @return Get allowed number of concurrent scans.
	 */
	long getConcurrentScans();

	int getScanPolicyOwnerShip();

	boolean isCompliancePolicyOwnership();

	boolean isDisableUnsafeChecks();

	long getDefaultScanPolicy();

	long getDefaultMaxScanTime();

	long getTargetInactiveDiscoveries();

	boolean isGroupsInScheduling();

	boolean allowComplianceOnlyScanning();

	boolean dismissComplianceWarning();

	boolean getUseGlobalIgnoreTargetListByDefault();

	String getGlobalIgnoreTargetList();

	boolean isAllowTargetMultipleGroups();

	Date getCookiesAccepted();

	boolean hasServices();

	boolean isManagedServicesLimited();

	String getDefaultAcceptComment();

	long getDefaultAcceptedLength();

	int getDefaultAcceptTargets();

	boolean allowEnrollHiab();

	String getLastLogonVersion();

	void setLastLogonVersion(String property);

	long getMaxPenTestHiabs();

	boolean hasHiabSmallBusiness();

	boolean hasHiabPortable();

	void setComplianceEnabled(boolean hasCompliance);

	boolean isChangePasswordOnLogon();

	void setShowGuide(boolean showGuide);

	boolean hasAutomaticGmt();

	boolean allowHiabManagement(boolean readOnly);

	boolean allowPolicyManagement(boolean readOnly);

	boolean allowReporting(boolean readOnly);

	boolean allowScheduleManagement(boolean readOnly);

	boolean allowTargetManagement(boolean readOnly);

	boolean allowReportFalsePositives(boolean readOnly);

	boolean allowDeleteReport(boolean readOnly);

	boolean allowUserRoleManagement(boolean readOnly);

	boolean allowVulnerabilityManagement(boolean readOnly);

	boolean allowAcceptRisk(boolean readOnly);

	boolean allowVerifyScan(boolean readOnly);

	boolean allowWasScoping(boolean readOnly);

	boolean allowWasReporting(boolean readOnly);

	boolean allowWasDeleteReport(boolean readOnly);

	boolean allowStopScan(boolean readOnly);

	boolean allowComplianceAccess(boolean readOnly);

	boolean allowMarkComplianceExceptions(boolean readOnly);

	boolean allowAnswerComplianceQuestions(boolean readOnly);

	boolean allowApproveComplianceQuestions(boolean readOnly);

	boolean allowEditCompliancePolicies(boolean readOnly);

	boolean allowSwatAccess(boolean readOnly);

	boolean allowSwatComment(boolean readOnly);

	boolean allowSwatVerification(boolean readOnly);

	boolean allowSwatDiscussion(boolean readOnly);

	boolean allowSwatRisks(boolean readOnly);

	boolean allowSubmitScoping(boolean readOnly);

	boolean allowRuleManagement(boolean readOnly);

	boolean allowRuleAdmin(boolean readOnly);

	boolean allowEditRules(boolean readOnly);

	boolean allowTicketManagement(boolean readOnly);

	boolean grantAllTickets(boolean readOnly);

	boolean allowReadAuditLogs(boolean readOnly);

	boolean allowReadLicense(boolean readOnly);

	boolean allowReceiveScanResultsEmail(boolean readOnly);

	boolean allowDashboardAccess(boolean readOnly);

	boolean allowReceiveScanResultsSMS(boolean readOnly);

	boolean allowPciScoping(boolean readOnly);

	boolean allowPciScheduleManagement(boolean readOnly);

	boolean allowManagedServices(boolean readOnly);

	boolean allowManagedServicesComment(boolean readOnly);

	boolean allowWasGeneral(boolean readOnly);

	boolean allowPciReporting(boolean readOnly);

	boolean allowPciDisputing(boolean readOnly);

	String getPciEmailAddress();

	long getMaxVirtualHiabs();

	String getProduct();

	boolean isThycoticEnabled();

	String[] getFarsightProducts();

	long getHiabAppsecScaleExternal();

	long getDefaultScanner();

	/**
	 * @param configurationService {@link ConfigurationService}
	 * @param readOnly Read only flag
	 * @return {@code true} if web app scanning licence is active
	 */
	boolean allowWasX(final ConfigurationService configurationService, boolean readOnly);

	boolean hasRemoteSupport();

	long getOutscanInternalIp();

	String getScannerList();

	void setGmtOffset(double gmtOffset);

	void setLastConsumedOtpTimeIndex(long lastConsumedOtpTimeIndex);

	void setShowTerms(boolean showTerms);

	boolean isReseller();

	int getPasswordAge();

	int getMaximumAge();

	void setSignature(String trim);

	void setTwoFactorAuthentication(int type);

	void setMobilePhone(String value);

	void setCredentialId(String value);

	/**
	 * @return Number of failed logons.
	 */
	long getFailedLogons();

	long getLastConsumedOtpTimeIndex();

	/**
	 * Set number of failed logons.
	 *
	 * @param failedLogons New value.
	 */
	void setFailedLogons(long failedLogons);

	/**
	 * Set if thycotic is enabled.
	 *
	 * @param enabled New value.
	 */
	void setThycoticEnabled(boolean enabled);

	/**
	 * Get default report template.
	 *
	 * @param scanType The report type
	 * @return The report template
	 */
	default long getDefaultReportTemplate(final ReportTemplateScanType scanType) {
		switch (scanType) {
			case Normal:
				return getDefaultReportTemplate();
			case PCI:
				return getDefaultReportTemplatePci();
			case WAS:
				return getDefaultReportTemplateWas();
			case Compliance:
				return getDefaultReportTemplateCompliance();
			case SWAT:
				return getDefaultReportTemplateSwat();
			default:
		}
		return 0;
	}

	/**
	 * @return Default report template id.
	 */
	long getDefaultReportTemplate();

	/**
	 * @return Default swat report template id.
	 */
	long getDefaultReportTemplateSwat();

	/**
	 * @return Default compliance report template id.
	 */
	long getDefaultReportTemplateCompliance();

	/**
	 * @return Default was report template id.
	 */
	long getDefaultReportTemplateWas();

	/**
	 * @return Default PCI report template id.
	 */
	long getDefaultReportTemplatePci();

	/**
	 * Update ticket settings.
	 *
	 * @param settings New settings.
	 */
	void setTicketSettings(final WorkflowSettings settings);

	/**
	 * @return List of ips user can login from.
	 */
	String getLoginIpRestriction();

	/**
	 * @param scannerid Scanner id
	 * @return true if user has access to scanner.
	 */
	boolean hasScannerAccess(long scannerid);

	/**
	 * @return true if user has outscan license.
	 */
	boolean hasOutscanLicense();

	long getHiabScans();

	long getDiscoveryLimit();

	long getWasMaximumLinks();

	/**
	 * Set default report template.
	 *
	 * @param scanType Scan type.
	 * @param templateId Template id.
	 */
	void setDefaultReportTemplate(ReportTemplateScanType scanType, long templateId);

	/**
	 * Set default scanner id.
	 *
	 * @param id Scanner id.
	 */
	void setDefaultScanner(long id);

	/**
	 * @return Hashed password.
	 */
	String getPasswordHash();

	long getCreatorId();

	/**
	 * @return portal preferences
	 */
	String getPortalPreferences();

	/**
	 * @return snapshot subscriptions
	 */
	long getSnapshotSubscriptions();

	/**
	 * @return remaining snapshot subscriptions
	 */
	long getSnapshotSubscriptionsRemaining();

	/**
	 * @return swat subscriptions
	 */
	long getSwatSubscriptions();

	/**
	 * @return remaining swat subscriptions
	 */
	long getSwatSubscriptionsRemaining();

	/**
	 * @return the user's PGP public key
	 */
	String getPgpPublicKey();

	boolean isMigration();
}