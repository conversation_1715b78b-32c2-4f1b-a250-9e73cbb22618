package com.chilicoders.core.user.impl.jpa;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

import com.chilicoders.core.user.api.WorkflowSettings;

import lombok.Getter;
import lombok.Setter;

@Entity(name = "tworkflowsettings")
@Getter
@Setter
public class WorkflowSettingsImpl implements WorkflowSettings, Serializable {

	private static final long serialVersionUID = -5028971953002342798L;

	@Id
	@Column(name = "xuserxid")
	private long userId;

	@Column
	private String p1Label;

	@Column
	private String p2Label;

	@Column
	private String p3Label;

	@Column
	private String p4Label;

	@Column
	private String p5Label;

	@Column(name = "p1days")
	private Integer days1;

	@Column(name = "p2days")
	private Integer days2;

	@Column(name = "p3days")
	private Integer days3;

	@Column(name = "p4days")
	private Integer days4;

	@Column(name = "p5days")
	private Integer days5;

	@Column(name = "pactive")
	private Integer active = 31;

	@Column(name = "overduealert")
	private boolean sendTicketOverdueAlert;

	@Column
	private boolean includeVulInfo;

	@Override
	public Integer getTicketPriorityActive() {
		return active;
	}

	@Override
	public boolean isOverdueAlert() {
		return sendTicketOverdueAlert;
	}

	@Override
	public void copyFrom(final WorkflowSettings settings) {
		p1Label = settings.getP1Label();
		p2Label = settings.getP2Label();
		p3Label = settings.getP3Label();
		p4Label = settings.getP4Label();
		p5Label = settings.getP5Label();
		days1 = settings.getDays1();
		days2 = settings.getDays2();
		days3 = settings.getDays3();
		days4 = settings.getDays4();
		days5 = settings.getDays5();
		active = settings.getTicketPriorityActive();
		includeVulInfo = settings.isIncludeVulInfo();
		sendTicketOverdueAlert = settings.isOverdueAlert();
	}

	/**
	 * Gets default number of days due for different priorities.
	 *
	 * @param ticketPriority Ticket priority.
	 * @return Default number of days.
	 */
	public int getTicketDay(final int ticketPriority) {
		switch (ticketPriority) {
			case 1:
				return days1 != null ? days1 : 0;
			case 2:
				return days2 != null ? days2 : 0;
			case 3:
				return days3 != null ? days3 : 0;
			case 4:
				return days4 != null ? days4 : 0;
			case 5:
				return days5 != null ? days5 : 0;
			default:
				return 0;
		}
	}
}
