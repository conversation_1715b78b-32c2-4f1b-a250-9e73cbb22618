package com.chilicoders.core.keystore.provider.impl;

import java.security.Key;
import java.security.cert.Certificate;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@SuppressFBWarnings("EI_EXPOSE_REP2")
public class KeyStoreEntry {
	private final Key key;
	private final Certificate[] certificateChain;

	public KeyStoreEntry() {
		this(null, null);
	}

	/**
	 * Constructs the object.
	 *
	 * @param key the certificate key
	 * @param certificateChain array of certificates
	 */
	public KeyStoreEntry(final Key key, final Certificate[] certificateChain) {
		this.key = key;

		if (certificateChain != null && certificateChain.length > 0) {
			this.certificateChain = certificateChain;
		}
		else {
			this.certificateChain = null;
		}
	}

	public Key getKey() {
		return this.key;
	}

	public boolean isKey() {
		return this.key != null;
	}

	public Certificate[] getCertificateChain() {
		return this.certificateChain == null ? null : this.certificateChain.clone();
	}

	public Certificate getCertificate() {
		return this.certificateChain == null ? null : this.certificateChain[0];
	}

	public boolean isCertificate() {
		return this.certificateChain != null;
	}
}
