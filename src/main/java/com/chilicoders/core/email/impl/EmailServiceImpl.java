package com.chilicoders.core.email.impl;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.MessagingException;
import javax.mail.SendFailedException;
import javax.mail.internet.AddressException;
import javax.mail.internet.MimeMessage;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.openpgp.PGPPublicKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.server.MimeMappings;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import com.chilicoders.core.appliance.api.ApplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.email.api.EmailService;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.user.api.EmailType;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.core.user.impl.jpa.MainUserDetails;
import com.chilicoders.util.Pgp;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TemplateUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Service implementation for sending emails.
 */
@SuppressFBWarnings("EI_EXPOSE_REP2")
@Component
public class EmailServiceImpl implements EmailService {

	/**
	 * Logger used for this class.
	 */
	private static final Logger LOG = LogManager.getLogger(EmailServiceImpl.class);

	private static final UserDetails NOUSER = new MainUserDetails();

	private static final String UNENCRYPTED = "Unencrypted";

	@Autowired
	private MessageService messageService;

	@Autowired
	private ConfigurationService configService;

	@Autowired
	private ApplianceService applianceService;

	@Autowired
	private UserService userService;

	@Autowired
	public JavaMailSender emailSender;

	/**
	 * Constructor.
	 *
	 * @param messageService Message service.
	 * @param configService Config service.
	 * @param applianceService Appliance service.
	 * @param userService User service.
	 * @param emailSender Email sender.
	 */
	public EmailServiceImpl(final MessageService messageService, final ConfigurationService configService, final ApplianceService applianceService,
							final UserService userService, final JavaMailSender emailSender) {
		this.messageService = messageService;
		this.configService = configService;
		this.applianceService = applianceService;
		this.userService = userService;
		this.emailSender = emailSender;
	}

	@Override
	public String sendEmail(final String recipient, final String from, final UserDetails userIn, final String template,
							final Map<String, String> customTags, final String pubKey, final Map<String, DataSource> files) throws SQLException {
		try {
			if (StringUtils.isEmpty(recipient)) {
				LOG.info("Can't send to an empty email address");
				return null;
			}

			final UserDetails user = userIn == null ? NOUSER : userIn;
			final String language = customTags != null && !StringUtils.isEmpty(customTags.get("LANGUAGE")) ? customTags.get("LANGUAGE") : user.getLanguage();

			if (customTags != null) {
				for (final Entry<String, String> entry : customTags.entrySet()) {
					if (customTags.get(entry.getKey()) != null && customTags.get(entry.getKey()).startsWith("_")) {
						customTags.put(entry.getKey(), messageService.getMessage(entry.getValue(), language, "MESSAGES", user.getSalesOrganizationId()));
					}
				}
			}

			EmailType mailType = user.getEmailType();
			if (customTags != null && !StringUtils.isEmpty(customTags.get("MTYPE"))) {
				mailType = EmailType.values()[StringUtils.getIntValue(customTags.get("MTYPE"))];
			}

			if (customTags != null && !StringUtils.isEmpty(customTags.get("FIELDSBASEDMSG"))) {
				mailType = EmailType.PLAIN; // only text emails for customized text emails
			}

			String body = messageService.getMessage(template + ".body", language, "TEMPLATE", user.getSalesOrganizationId());

			if (customTags != null && "1".equalsIgnoreCase(customTags.get("CUSTOMCONFIG"))) {
				body = StringUtils.setEmpty(customTags.get("FIELDSBASEDMSG"), "");
			}

			if (body == null || body.equals("ERROR")) {
				LOG.info("Can't send email with no templates (" + template + ")");
				return null;
			}

			if (customTags != null && !StringUtils.isEmpty(customTags.get("CUSTOMTEXT"))) {
				body = "\n" + (mailType == EmailType.HTML ? StringEscapeUtils.escapeHtml4(customTags.get("CUSTOMTEXT")) : customTags.get("CUSTOMTEXT")) + "\n" + body;
			}

			if (customTags != null && !StringUtils.isEmpty(customTags.get("EVENTCREATEDBY"))) {
				body += messageService.getMessage("event.info", language, "TEMPLATE", user.getSalesOrganizationId());
			}

			final String header = messageService.getMessage("header", language, "TEMPLATE", user.getSalesOrganizationId());
			if (user != NOUSER) {
				body = header + body;
			}

			String htmlBody = null;
			if (mailType == EmailType.HTML) {
				htmlBody = messageService.getMessage(template + ".body.html", language, "TEMPLATE", user.getSalesOrganizationId());
				if (htmlBody != null && !htmlBody.equals("ERROR")) {
					if (customTags != null && !StringUtils.isEmpty(customTags.get("CUSTOMTEXT"))) {
						htmlBody = "<br>" + StringEscapeUtils.escapeHtml4(customTags.get("CUSTOMTEXT")).replaceAll("\n", "<br>") + "<br>" + htmlBody;
					}

					if (user != NOUSER) {
						htmlBody = header.replaceAll("\n", "<br>") + htmlBody;
					}
				}
			}

			String subject = messageService.getMessage(template + ".subject", language, "TEMPLATE", user.getSalesOrganizationId());
			if (customTags != null && !StringUtils.isEmpty(customTags.get("CUSTOMSUBJECT"))) {
				subject = customTags.get("CUSTOMSUBJECT");
			}

			if (subject == null || subject.equals("ERROR")) {
				LOG.info("Can't send email with no templates (" + template + ")");
				return null;
			}

			final String signature = userService.getSignature(user, language);
			subject = TemplateUtils.replaceTags(subject, user, customTags, false, language, configService, messageService, applianceService);
			if (htmlBody == null) {
				htmlBody = body.replaceAll("\n", "<br>");
			}
			body = TemplateUtils.replaceTags(body, user, customTags, false, language, configService, messageService, applianceService);

			MimeMessage mo = emailSender.createMimeMessage();
			final MimeMessageHelper mimeHelper = new MimeMessageHelper(mo, true, StandardCharsets.UTF_8.name());
			if (htmlBody != null) {
				htmlBody = TemplateUtils.replaceTags(htmlBody, user, customTags, true, language, configService, messageService, applianceService);
				htmlBody = addHtmlTemplate(htmlBody, user, customTags);
			}

			String sender = from;

			if (!configService.isHiabEnabled() && !StringUtils.isEmpty(configService.getProperty(ConfigurationKey.from, user.getSalesOrganizationId()))) {
				sender = configService.getProperty(ConfigurationKey.from, user.getSalesOrganizationId());
			}

			if (StringUtils.isEmpty(sender)) {
				if (configService.isHiabEnabled()) {
					final UserDetails hiabUser = userService.getUserDetails(UserService.HIABUSERID, false);
					if (hiabUser != null) {
						sender = "HIAB Administrator <" + hiabUser.getEmail() + ">";
					}
				}
				else {
					sender = configService.getProperty(ConfigurationKey.mail_fromaddress);
				}
			}

			mimeHelper.setFrom(sender);
			mimeHelper.setSubject(subject);

			if (configService.isHiabEnabled() && (customTags == null || !customTags.containsKey("KEEPFROM"))) {
				final String serverip = applianceService.getIpAddress();
				final String fromaddress = configService.getProperty(ConfigurationKey.hiab_fromemail);
				final boolean includeip = configService.getProperty(ConfigurationBooleanKey.hiab_fromincludeip);
				mimeHelper.setFrom("\"" + (configService.getProperty(ConfigurationKey.hiab_fromname) + " " + ((serverip == null || "".equals(serverip) || !includeip)
						? ""
						: "- " + serverip)).replaceAll("\"", "") + "\" <" + fromaddress + ">");
			}

			final long size = getAttachmentSize(files);
			final long sizelimit = configService.getProperty(ConfigurationIntKey.mail_maxmessagesize);
			if (size > sizelimit || (customTags != null && StringUtils.getBooleanValue(customTags.get("MESSAGETOOLARGE"), false))) {
				LOG.info("Attachment too large: " + size + "/" + sizelimit);
				files.clear();
				final String error = messageService.getMessage("messagetoolarge", language, "TEMPLATE", user.getSalesOrganizationId());
				body += "\n" + error;
				if (htmlBody != null) {
					htmlBody += "<br>" + error.replaceAll("\n", "<br>");
				}
			}

			if (!StringUtils.isEmpty(signature)) {
				body += signature;
			}

			if (htmlBody != null) {
				addFilesFromTemplate(body, htmlBody, mimeHelper);
			}
			else {
				mimeHelper.setText(body);
			}

			if (files != null) {
				for (final Entry<String, DataSource> entry : files.entrySet()) {
					mimeHelper.addAttachment(entry.getKey(), entry.getValue());
				}
			}

			mimeHelper.addTo(recipient);
			if (!StringUtils.isEmpty(pubKey)) {
				final PGPPublicKey encryptionKey = userService.getEmailEncryptionKey(user, pubKey);
				if (encryptionKey == null && !UNENCRYPTED.equals(pubKey)) {
					LOG.info("Email encryption requested but encryption key available, can not send uncrypted email if encryption is requested");
					return null;
				}
				if (encryptionKey != null) {
					mo = Pgp.getInstance(configService).encryptEmail(emailSender.createMimeMessage(), mo, encryptionKey);
				}
			}

			emailSender.send(mo);
			return body;
		}
		catch (final AddressException e) {
			LOG.info("Invalid email address detected: " + e.getMessage());
		}
		catch (final SendFailedException e) {
			LOG.info("Failed to send email: " + e.getMessage());
		}
		catch (final MessagingException | IOException e) {
			LOG.error("Error sending email", e);
		}
		return null;
	}

	@Override
	public void sendEmail(final String recipient, final String from, final String content, final String subject, final Map<String, String> customTags)
			throws MessagingException {

		final MimeMessage mo = emailSender.createMimeMessage();
		final MimeMessageHelper mimeHelper = new MimeMessageHelper(mo, true);
		if (!StringUtils.isEmpty(from)) {
			mimeHelper.setFrom(from);
			mimeHelper.setSubject(subject);
			mimeHelper.setText(content);
			mimeHelper.addTo(recipient);
			emailSender.send(mo);
		}
	}

	/**
	 * Add files from template to the mime message.
	 *
	 * @param plainText Plain text.
	 * @param html Current html text.
	 * @param mimeHelper Mime helper.
	 * @return New html text to be used.
	 */
	private String addFilesFromTemplate(final String plainText, final String html, final MimeMessageHelper mimeHelper) throws MessagingException {
		int position;
		int partCount = 0;
		final StringBuilder body = new StringBuilder(html);
		final Map<String, Pair<String, DataSource>> sources = new HashMap<>();
		while ((position = body.indexOf("file:///")) > -1) {
			int endPosition = body.indexOf("\"", position + 1);
			String fileName = body.substring(position + 7, endPosition);
			if (fileName.endsWith(");")) {
				fileName = fileName.substring(0, fileName.length() - 2);
				endPosition -= 2;
			}
			LOG.debug("Replacing file in html (" + position + ", " + endPosition + "):" + fileName);
			if (!"".equals(fileName)) {
				final File src = new File(fileName);
				if (src.exists()) {
					if (sources.get(fileName) != null) {
						body.replace(position, endPosition, "cid:" + sources.get(fileName).getKey());
					}
					else {
						final String classId = "part-" + (partCount++) + "." + System.currentTimeMillis();
						try {
							body.replace(position, endPosition, "cid:" + classId);
							final DataSource dataSource = new FileDataSource(fileName) {
								@Override
								public String getContentType() {
									return MimeMappings.DEFAULT.get(FilenameUtils.getExtension(getFile().getAbsolutePath()));
								}
							};
							sources.put(fileName, Pair.of(classId, dataSource));
						}
						catch (final Exception e) {
							LOG.error("generateMsg - HTML failed adding src:", e);
						}
					}
				}
				else {
					LOG.debug("file does not exists removes reference to it");
					body.replace(position, endPosition, "cid:");
				}
			}
		}
		mimeHelper.setText(plainText, body.toString());
		for (final Entry<String, Pair<String, DataSource>> source : sources.entrySet()) {
			mimeHelper.addInline(source.getValue().getKey(), source.getValue().getValue());
		}

		return body.toString();
	}

	/**
	 * Add html template.
	 *
	 * @param htmlBody Body to use in html message.
	 * @param user User details.
	 * @param customTags Custom tags to replace in the html template.
	 * @return Template with body inserted.
	 */
	private String addHtmlTemplate(final String htmlBody, final UserDetails user, final Map<String, String> customTags) throws IOException {
		final StringBuilder template = new StringBuilder(StringUtils.readTextFile(configService.getProperty(ConfigurationKey.mail_html_template, user.getSalesOrganizationId(),
				configService.getProperty(ConfigurationKey.mail_html_template,
						configService.getProperty(ConfigurationKey.email_template_folder) + "/" + (configService.isHiabEnabled() ? "hiab." : "") + "email.html"))));
		if (template.length() == 0) {
			return htmlBody;
		}
		final int pos = template.indexOf("[:TEXT]");
		template.replace(pos, pos + 7, htmlBody + (!StringUtils.isEmpty(user.getSignature()) ? user.getSignature().replaceAll("\n", "<br>") : ""));
		return TemplateUtils.replaceTags(template.toString(), user, customTags, true, user.getLanguage(), configService, messageService, applianceService);
	}

	/**
	 * Gets the size of attachments.
	 *
	 * @param files Files to be included in the email.
	 * @return Total size of all attachments.
	 */
	private static long getAttachmentSize(final Map<String, DataSource> files) {
		long size = 0;
		if (files != null) {
			for (final DataSource source : files.values()) {
				if (source instanceof FileDataSource) {
					size += ((FileDataSource) source).getFile().length();
				}
			}
		}
		return size;
	}
}
