package com.chilicoders.core.compliance.model;

import java.time.Instant;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import com.chilicoders.model.ComplianceRequirementInterface;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity(name = "compliancerequirements")
public class ComplianceRequirement implements ComplianceRequirementInterface {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	private String key;

	private Integer customerId;

	private Instant deleted;
}
