package com.chilicoders.core.compliance.api.model;

import lombok.Setter;

@Setter
public abstract class RequirementBase {
	public abstract String getProbeString();

	public abstract String getRowTag();

	public abstract String getRow();

	private String description;

	private long policyId;

	public String getDescription() {
		return description;
	}

	public long getPolicyId() {
		return policyId;
	}

	/**
	 * Escape requirement values.
	 *
	 * @param value Value to escape
	 * @param removeSemicolon true if semicolons should be removed, otherwise false
	 * @return The escaped value
	 */
	protected String escapeCheckValue(final String value, final boolean removeSemicolon) {
		if (value == null) {
			return "";
		}
		return value.replace(";", removeSemicolon ? "" : "%3B");
	}
}
