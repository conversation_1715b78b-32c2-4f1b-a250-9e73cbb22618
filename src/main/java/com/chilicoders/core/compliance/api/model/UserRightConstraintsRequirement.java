package com.chilicoders.core.compliance.api.model;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@SuppressFBWarnings("UUF_UNUSED_FIELD")
public class UserRightConstraintsRequirement extends RequirementBase {
	@SuppressWarnings("unused")
	private String type;
	@SuppressWarnings("unused")
	private int comparison;
	@SuppressWarnings("unused")
	private String value;

	@Override
	public String getProbeString() {
		return null;
	}

	public String getId() {
		return null;
	}

	@Override
	public String getRowTag() {
		return "constraints";
	}

	@Override
	public String getRow() {
		return "constraint";
	}
}
