package com.chilicoders.core.compliance.api.model;

import lombok.Setter;

/**
 * This class is not saved to the database but converted to/from xml.
 */
@Setter
public class CheckConfigRequirement extends RequirementBase {
	private String service;
	private String levels;
	private boolean status;

	@Override
	public String getProbeString() {
		return escapeCheckValue(service, true).trim() + ";" + escapeCheckValue(levels, true) + ";" + (status ? "ON" : "OFF") + ";";
	}

	public String getId() {
		return null;
	}

	@Override
	public String getRowTag() {
		return "checkconfig";
	}

	@Override
	public String getRow() {
		return "config";
	}

	public String getService() {
		return service;
	}

}
