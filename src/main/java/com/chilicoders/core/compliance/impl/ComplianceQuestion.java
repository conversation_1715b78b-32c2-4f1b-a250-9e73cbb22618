package com.chilicoders.core.compliance.impl;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;

import org.hibernate.annotations.Formula;

import com.chilicoders.core.compliance.api.model.ComplianceQuestionInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Entity(name = "tcompliancequestions")
@Getter
public class ComplianceQuestion implements ComplianceQuestionInterface {
	@Column(name = "xid")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "compliancequestion_seq_generator")
	@SequenceGenerator(name = "compliancequestion_seq_generator", sequenceName = "tcompliancequestions_seq", allocationSize = 1)
	@Id
	private Long id;

	@Column(name = "xuserxid")
	@Setter
	private long userId;

	@Formula("(SELECT re.name FROM tcompliancerequirements re WHERE re.identifier = identifier AND re.policyid = requirementpolicy AND re.requirementtype = 1)")
	private String name;

	@Formula("(SELECT re.solution FROM tcompliancerequirements re WHERE re.identifier = identifier AND re.policyid = requirementpolicy AND re.requirementtype = 1)")
	private String question;

	@Formula("(SELECT re.settings FROM tcompliancerequirements re WHERE re.identifier = identifier AND re.policyid = requirementpolicy AND re.requirementtype = 1)")
	private String answers;

	@Column
	@Setter
	private long policyId;

	@Column
	@Setter
	private String identifier;

	@Column
	private String answer;

	@Formula("(SELECT re.description FROM tcompliancerequirements re WHERE re.identifier = identifier AND re.policyid = requirementpolicy AND re.requirementtype = 1)")
	private String description;

	@Column
	private Date answerDate;

	@Column
	private long answerBy;

	@Column
	private int approved = -1;

	@Column
	private Date approveDate;

	@Column
	private long approvedBy;

	@Column
	@Setter
	private long requirementPolicy;

	@Formula("(SELECT string_agg((f.xid::text || '§'::text) || f.name, ','::text) FROM tcompliancequestionfiles f WHERE f.questionid = xid)")
	private String files;

	@Override
	public boolean isCompliant() {
		return answerDate != null && approved == 1;
	}
}
