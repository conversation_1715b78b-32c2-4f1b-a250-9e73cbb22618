package com.chilicoders.core.translation.api;

import java.sql.SQLException;
import java.util.Map;

/**
 * Service for translation operations.
 */
public interface TranslationService {

	final int VULNERABILITY_TYPE_NAME = 1;
	final int VULNERABILITY_TYPE_DESCRIPTION = 2;
	final int VULNERABILITY_TYPE_SOLUTION = 3;
	final int VULNERABILITY_TYPE_SOLUTIONTITLE = 4;

	/**
	 * Get vulnerability translations.
	 *
	 * @param ruleIds Rule ids to get translations for.
	 * @param language Language to translate to.
	 * @return Map with translations.
	 */
	Map<String, String> getVulnerabilityTranslations(final Long[] ruleIds, final String language);

	/**
	 * Gets translations for OWASP.
	 *
	 * @param language to use for translations
	 * @return a map with translations
	 */
	Map<String, String> getOWASPTranslations(final String language) throws SQLException;

	/**
	 * Gets a translated string.
	 *
	 * @param key key of the string to translate
	 * @param type type of the string to translate (ex. MESSAGE, REPORT, ERROR)
	 * @param language the language for the translation
	 * @return the translated message
	 */
	String getStringTranslation(final String key, final String type, final String language) throws SQLException;
}
