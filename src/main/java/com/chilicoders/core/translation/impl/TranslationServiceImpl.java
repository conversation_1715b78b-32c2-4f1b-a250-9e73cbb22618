package com.chilicoders.core.translation.impl;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.rule.api.RuleInterface;
import com.chilicoders.core.rule.impl.RuleDao;
import com.chilicoders.core.translation.api.TranslationService;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;

@Component
public class TranslationServiceImpl implements TranslationService {

	private static final Logger LOG = LogManager.getLogger(TranslationServiceImpl.class);

	final NativeStatementExecutor statementExec;
	final RuleDao ruleDao;
	final MessageService messageService;

	/**
	 * Create TranslationServiceImpl.
	 *
	 * @param statementExecutor Statement executor
	 * @param ruleDao RuleDao
	 * @param messageService Message service
	 */
	@Autowired
	public TranslationServiceImpl(final NativeStatementExecutor statementExecutor, final RuleDao ruleDao, final MessageService messageService) {
		this.statementExec = statementExecutor;
		this.ruleDao = ruleDao;
		this.messageService = messageService;
	}

	@Override
	public Map<String, String> getVulnerabilityTranslations(final Long[] ruleIds, final String language) {
		final Map<String, String> translations = new HashMap<>();

		try {
			List<? extends RuleInterface> rules = ruleDao.getRulesForVulnerabilities(ruleIds, language);
			for (final RuleInterface rule : rules) {
				translations.put(rule.getId() + "_" + rule.getRiskLevel(), rule.getSolution());
			}

			rules = ruleDao.getRulesForVulnerabilitiesPatchOrUpdate(ruleIds, language);
			for (final RuleInterface rule : rules) {
				if (rule.getRiskLevel() == VULNERABILITY_TYPE_SOLUTION) {
					String solution = rule.getSolution();
					if (rule.getSolutionType() == 7) {
						solution = messageService.getMessage("_APPLY_LATEST_PATCHES_FOR", language) + rule.getSolutionProduct();
					}
					translations.put(rule.getId() + "_" + rule.getRiskLevel(), solution);
				}
				else if (rule.getRiskLevel() == VULNERABILITY_TYPE_SOLUTIONTITLE) {
					String solution = rule.getSolution();
					if (rule.getSolutionType() == 7) {
						solution = messageService.getMessage("_APPLY_LATEST_PATCHES_FOR", language) + rule.getSolutionProduct();
					}
					translations.put(rule.getId() + "_" + rule.getRiskLevel(), solution);
				}
			}

			return translations;
		}
		catch (final RuntimeException | SQLException e) {
			LOG.error(e);
			return translations;
		}
	}

	@Override
	public Map<String, String> getOWASPTranslations(final String language) throws SQLException {
		return statementExec.getMappedValues(new NativeSqlStatement("SELECT key, value FROM ttranslations WHERE key LIKE 'OWASP_%' AND language = ?", language));
	}

	@Override
	public String getStringTranslation(final String key, final String type, final String language) throws SQLException {
		return statementExec.getString(new NativeSqlStatement("SELECT value FROM ttranslations WHERE language=? AND key=?", language, type + "_" + key));
	}

}
