package com.chilicoders.config;

import javax.persistence.EntityManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.impl.ConfigurationServiceImpl;
import com.chilicoders.db.query.JpaNativeStatementExecutor;
import com.chilicoders.db.query.TransactionalJpaNativeStatementExecutor;
import com.chilicoders.util.StringUtils;

@Configuration
@Profile("!scandatatest & !test")
public class ScanDataImportConfig {
	private static final String CONFIG_FOLDER = "config.folder";

	@Autowired
	private Environment environment;

	@Autowired(required = false)
	private BuildProperties buildProperties;

	/**
	 * Provision a configuration service based on the folder location.
	 * If the folder location is missing, the default value will be used.
	 *
	 * @return a configuration service instance.
	 */
	@Bean
	public ConfigurationService configurationServiceImplBean() {
		final String configFolder = environment.getProperty(CONFIG_FOLDER);
		final ConfigurationService configService;
		if (!StringUtils.isEmpty(configFolder)) {
			configService = new ConfigurationServiceImpl(null, configFolder);
		}
		else {
			configService = new ConfigurationServiceImpl();
		}
		if (buildProperties != null) {
			configService.setProperty(ConfigKeys.ConfigurationKey.TAG.getConfigKey(), buildProperties.getVersion());
		}
		return configService;
	}

	@Primary
	@Bean
	public TransactionalJpaNativeStatementExecutor transactionalPpaNativeStatementExecutor(final EntityManager entityManager, final ConfigurationService configService) {
		return new TransactionalJpaNativeStatementExecutor(entityManager, new JpaNativeStatementExecutor(entityManager, configService));
	}
}