package com.chilicoders.scan.impl;

import static com.chilicoders.core.scanner.api.Scanner.EXTERNAL_SCANNER_ID;
import static com.chilicoders.core.scanner.api.Scanner.LOCAL_SCANNER;
import static com.chilicoders.core.targets.api.TargetService.TARGET_TYPE_AGENT;
import static com.chilicoders.core.targets.api.TargetService.TARGET_TYPE_CLOUD;
import static com.chilicoders.core.targets.api.TargetService.TARGET_TYPE_HOSTNAME;
import static com.chilicoders.core.targets.api.TargetService.TARGET_TYPE_IP;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.xml.bind.JAXBException;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.chilicoders.compliance.ComplianceDaoImpl;
import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao;
import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao.AssetIdentifierQuery;
import com.chilicoders.core.assets.api.AssetDao;
import com.chilicoders.core.assets.api.AssetDao.AssetQuery;
import com.chilicoders.core.compliance.dao.ComplianceDao;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.credentials.CredentialService;
import com.chilicoders.core.credentials.model.CredentialInterface;
import com.chilicoders.core.customer.api.CustomerInterface;
import com.chilicoders.core.customer.api.CustomerService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.scanconfiguration.model.AgentScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.CloudsecScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerDiscoveryScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.DockerScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkDiscoveryConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.NetworkScanConfigurationTemplate;
import com.chilicoders.core.scanconfiguration.model.ScaleScanConfigurationTemplate;
import com.chilicoders.core.scandata.api.ScanSettingKeys;
import com.chilicoders.core.scandata.api.ScanStatusService;
import com.chilicoders.core.scandata.api.ScanlogService;
import com.chilicoders.core.scandata.api.model.DataType;
import com.chilicoders.core.scandata.api.model.PluginPreferences;
import com.chilicoders.core.scandata.api.model.PluginPreferences.PluginPreferencesType;
import com.chilicoders.core.scandata.api.model.ScanDataInterface;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatusInterface;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.core.scanner.api.ScannerDao;
import com.chilicoders.core.scanpolicy.dao.ScanPolicyDao;
import com.chilicoders.core.scanpolicy.model.ScanPolicySettings;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.model.Feature;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.discover.TargetResolver;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.rest.events.ScanEvent;
import com.chilicoders.model.AccountInterface;
import com.chilicoders.model.AccountType;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.AssetLinkInterface;
import com.chilicoders.model.AuthenticationType;
import com.chilicoders.model.BaseCloudConfigurationTemplate;
import com.chilicoders.model.CloudDiscoveryAwsConfigurationTemplate;
import com.chilicoders.model.CloudDiscoveryAzureConfigurationTemplate;
import com.chilicoders.model.CompliancePolicyInterface;
import com.chilicoders.model.IntegrationType;
import com.chilicoders.model.ScanConfigurationInterface;
import com.chilicoders.model.ScanConfigurationInterface.BaseScanConfigurationTemplate;
import com.chilicoders.model.ScanLogEntryInterface;
import com.chilicoders.model.ScanLogEntryInterface.Authentication;
import com.chilicoders.model.ScanLogStatus;
import com.chilicoders.model.ScanPolicyInterface;
import com.chilicoders.model.ScanSelectionType;
import com.chilicoders.model.ScanTemplate;
import com.chilicoders.model.ScheduleInterface;
import com.chilicoders.model.Source;
import com.chilicoders.model.Template;
import com.chilicoders.model.WorkflowInterface;
import com.chilicoders.model.AssetIdentifierInterface.DockerImageProperties;
import com.chilicoders.model.WorkflowInterface.Configuration.ConfigurationType;
import com.chilicoders.rest.exceptions.ForbiddenException;
import com.chilicoders.rest.exceptions.InputValidationException;
import com.chilicoders.rest.exceptions.InternalServerErrorException;
import com.chilicoders.rest.models.AssetIdentifier;
import com.chilicoders.rest.models.NetworkDiscoveryProtocol;
import com.chilicoders.rest.models.RestObject;
import com.chilicoders.rest.models.ScanPolicy;
import com.chilicoders.scan.StartScanService;
import com.chilicoders.scanpolicy.ScanPolicyDaoImpl;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.dao.AssetDaoImpl;
import com.chilicoders.service.dao.AssetIdentifierDaoImpl;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;
import com.chilicoders.service.dao.ScannerDaoImpl;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.PortUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XmlUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

public class StartScanServiceImpl implements StartScanService {
	private static final Logger LOG = LogManager.getLogger(StartScanServiceImpl.class);

	private static final int UNLIMITED_SCALE_APPS = -1;

	private static final int DEFAULT_SCAN_TIME = (int) Duration.ofHours(12).getSeconds();

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection conn;

	private final ScanConfigurationInterface scanConfiguration;

	private final WorkflowInterface workflow;

	private final UserDetails user;

	private final ScheduleInterface schedule;

	private final Instant startTime;

	private final Instant scanWindowStart;

	private final boolean reScheduled;

	private final Integer parentId;

	private final JSONObject scanData;

	private final CredentialService credentialService;

	private final ScanPolicyDao scanPolicyDao;

	private final TransactionalNativeStatementExecutor statementExecutor;

	private final CustomerService customerService;

	private final IpService ipService;

	private final ScannerDao scannerDao;

	private final ScanlogService scanlogService;

	private final ScanStatusService scanStatusService;

	private final ConfigurationService configurationService;

	private final ComplianceDao complianceDao;

	private final AssetDao assetDao;

	private final AssetIdentifierDao assetIdentifierDao;

	/**
	 * Constructor.
	 *
	 * @param conn Database connection
	 * @param startScanRequest StartScanRequest
	 */
	public StartScanServiceImpl(final Connection conn, final StartScanRequest startScanRequest) {
		this.conn = conn;
		this.workflow = startScanRequest.getWorkflow();
		this.scanConfiguration = startScanRequest.getScanConfiguration();
		this.user = startScanRequest.getUser();
		this.schedule = startScanRequest.getSchedule();
		this.startTime = startScanRequest.getStartTime() != null ? startScanRequest.getStartTime() : Instant.now();
		this.scanWindowStart = startScanRequest.getScanWindowStart() != null ? startScanRequest.getScanWindowStart() : this.startTime;
		this.reScheduled = startScanRequest.isReScheduled();
		this.parentId = startScanRequest.getParentId();
		this.scanData = startScanRequest.getScanData();
		this.credentialService = ServiceProvider.getCredentialService(conn);
		this.scanPolicyDao = new ScanPolicyDaoImpl(conn);
		this.statementExecutor = new DbLayerNativeStatementExecutor(conn);
		this.customerService = ServiceProvider.getCustomerService(conn);
		this.ipService = ServiceProvider.getIpService(conn);
		this.scannerDao = new ScannerDaoImpl(conn);
		this.scanlogService = ServiceProvider.getScanlogService(conn);
		this.scanStatusService = ServiceProvider.getScanStatusService(conn);
		this.configurationService = ServiceProvider.getConfigService();
		this.complianceDao = new ComplianceDaoImpl(conn);
		this.assetDao = new AssetDaoImpl(conn);
		this.assetIdentifierDao = new AssetIdentifierDaoImpl(conn);
	}

	@Override
	public Integer startScaleScan(final List<Event> eventList) throws SQLException, JAXBException, MalformedURLException {
		final Integer scannerId = getScannerId();

		final long maxAppsecScale = scannerId == EXTERNAL_SCANNER_ID ? getAppsecScaleExternalLicense() : getAppsecScaleLicense();

		if (this.scanData == null) {
			checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId());
		}

		if (this.configurationService.isHiabEnabled() && scannerId == LOCAL_SCANNER && !isWorkflowWithNetworkScanAndScale(this.workflow)) {
			throw new ForbiddenException("_CANNOT_RUN_ON_LOCAL");
		}

		final ScaleScanConfigurationTemplate configuration = (ScaleScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final JSONObject jsonConfiguration = new JSONObject().put("scans", new JSONArray().put(new JSONObject(MarshallingUtils.marshal(configuration))));
		jsonConfiguration.getJSONArray("scans").getJSONObject(0).put("name", this.scanConfiguration.getName());

		final Map<String, List<String>> seeds = configuration.getSeedUrls(this.scanData, this.ipService);
		if (seeds.isEmpty()) {
			throw new InputValidationException("_NO_SEED_URL");
		}

		final Set<AuthenticationType> authenticationTypes = new HashSet<>();
		if (configuration.getBasicAuth() != null) {
			authenticationTypes.add(AuthenticationType.BASIC);
		}
		else if (!StringUtils.isEmpty(configuration.getSideScript())) {
			authenticationTypes.add(AuthenticationType.SELENIUM);
		}
		else if (!StringUtils.isEmpty(configuration.getSetupProc())) {
			authenticationTypes.add(AuthenticationType.LUA);
		}

		int scansStarted = 0;

		for (final List<String> urls : seeds.values()) {
			if (urls.isEmpty()) {
				throw new InputValidationException("_NO_SEED_URL");
			}

			checkAppsecScaleLicense(maxAppsecScale, scannerId, urls);

			final URL url = new URL(urls.get(0));
			final String host = url.getHost().toLowerCase();
			jsonConfiguration.getJSONArray("scans").getJSONObject(0).put("seeds", urls.toArray(new String[0]));

			if (configuration.isSpaCrawling() && !user.getCustomerInformation().hasFeature(Feature.SPA_CRAWLER)) {
				jsonConfiguration.getJSONArray("scans").getJSONObject(0).put("spa-crawling", false);
			}

			final ScanTimeSettings scanTimeSettings = getScanTimeSettings(configuration.getNseconds());
			if (scanTimeSettings == null) {
				throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
			}

			final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
			scanStatus.setId(this.scanStatusService.createScanStatusId());

			final String scanSchema = "scan__"
					+ this.user.getCustomerId()
					+ "_"
					+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
					+ "_"
					+ scanStatus.getId();

			scanStatus.setScanSchema(scanSchema);

			final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
			scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
			scanLog.setTemplate(ScanTemplate.SCALE);
			scanLog.setScanConfigurationId(this.scanConfiguration.getId());
			scanLog.setSchema(scanSchema);
			scanLog.setScannerId(scannerId);
			scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
			scanLog.setCustomerId(this.user.getCustomerId());
			scanLog.setExpectedStart(scanTimeSettings.getStartTime());
			scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
			scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
			scanLog.setParentId(this.parentId);
			scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
			setAuthentication(scanLog, new ArrayList<>(authenticationTypes));

			final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
			addScanStartedEvent(eventList, scanLogId);

			final HashMap<String, Object> scanSettings = new HashMap<>();
			scanSettings.put("SETTINGS", jsonConfiguration.toString());

			scanSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

			if (scanTimeSettings.getScheduleId() != null) {
				final JSONObject reScheduleData = new JSONObject();
				reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
				reScheduleData.put("scanWindowStart", this.scanWindowStart);
				reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
				reScheduleData.putOpt("parentId", this.parentId);
				reScheduleData.put("urls", StringUtils.join(urls, ","));
				scanSettings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
			}

			scanSettings.put("TARGET", host);
			if (this.ipService.isHostname(host)) {
				scanStatus.setTargetType(TARGET_TYPE_HOSTNAME);
			}
			else if (this.ipService.isIp(host)) {
				scanStatus.setTargetType(TARGET_TYPE_IP);
			}

			if (configuration.isInfrastructureScan()) {
				String prop = "" + new PluginPreferences("scan", "schema_name", "entry", scanSchema);
				prop += "\n" + new PluginPreferences("global", "plugin timeout", "entry", "" + configuration.getNseconds() * 60);
				if (this.ipService.isHostname(host)) {
					prop += "\n" + new PluginPreferences("http", "hostname", "entry", host).toProbeSetting();
				}

				scanSettings.put("RUNINFRASTRUCTURESCAN", "1");
				scanSettings.put("SCANSCHEMA", scanSchema);
				scanSettings.put("PROP", prop);
			}

			scanStatus.setTarget(this.scanConfiguration.getName());
			scanStatus.setService(ScanServiceType.AppsecScale);
			scanStatus.setSettings(XmlUtils.createParam(scanSettings).toString());
			scanStatus.setScannerId(scannerId);
			scanStatus.setTargetId(scanLogId);
			scanStatus.setUserId(this.user.getMainUserId());
			scanStatus.setSubUserId(this.user.getSubUserId());
			scanStatus.setScheduleId(this.scanConfiguration.getId());
			scanStatus.setStatus(scanTimeSettings.isPending()
					? ScanStatuses.WaitingScanWindow.toString()
					: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
			scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
			scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
			scanStatus.setTemplateId(Template.AppsecScale.getId());
			scanStatus.setTemplateName(Template.Normal.name());
			scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
			scanStatus.setScheduleName(this.scanConfiguration.getName());
			if (this.workflow != null) {
				scanStatus.setWorkflowId(this.workflow.getId());
			}
			this.scanStatusService.writeScanStatus(scanStatus);
			this.scanStatusService.executeBatch();
			scansStarted++;
		}

		return scansStarted;
	}

	/**
	 * Check appsec scale license for user.
	 *
	 * @param maxAppsecScale Number of appsec scale applications user can have
	 * @param scannerId Scanner id
	 * @param urls List of urls to start scan on
	 */
	private void checkAppsecScaleLicense(final long maxAppsecScale, final Integer scannerId, final List<String> urls) throws SQLException {
		if ((this.scanConfiguration.getLastScan() != null && this.workflow == null) || maxAppsecScale == UNLIMITED_SCALE_APPS) {
			return;
		}

		final String scannerFilter = scannerId == EXTERNAL_SCANNER_ID
				? "COALESCE(scannerid, 0) = " + EXTERNAL_SCANNER_ID
				: "COALESCE(scannerid, 0) != " + EXTERNAL_SCANNER_ID;

		final Long activeApps = this.statementExecutor.getLong(new NativeSqlStatement(
				"SELECT COUNT(DISTINCT(LOWER(SUBSTRING(url FROM '^https?://([^/]*?)(:[0-9]*)?/') || "
						+ "COALESCE(SUBSTRING(url FROM '^https?://.*?:([0-9]+)/')::INTEGER, "
						+ "CASE WHEN url ILIKE 'https://%' THEN 443 ELSE 80 END)))) AS result FROM "
						+ "(SELECT JSONB_ARRAY_ELEMENTS_TEXT(configuration::JSONB->'seeds') AS url FROM scanconfigurationsview "
						+ "WHERE deleted IS NULL AND template = 'SCALE' AND "
						+ scannerFilter
						+ " AND (lastsuccessfulscan IS NOT NULL OR id = ?) AND customerid = ?"
						+ " UNION ALL SELECT UNNEST(?) AS url) AS cnt",
				this.workflow == null ? this.scanConfiguration.getId() : 0, this.user.getCustomerId(), urls.toArray(new String[0])));

		if (maxAppsecScale < activeApps) {
			throw new InputValidationException("_TOO_MANY_APPSEC_SCALE_ASSETS");
		}
	}

	/**
	 * @return Allowed number of configured appsec scale scans
	 * @throws ForbiddenException when license is exceeded
	 */
	private long getAppsecScaleLicense() {
		final long maxAppsecScale = this.user.getMaxAppsecScale(this.configurationService.isHiabEnabled());
		if (maxAppsecScale < -1 || maxAppsecScale == 0) {
			throw new ForbiddenException("_APPSEC_LICENSE_NO_SCANS");
		}
		return maxAppsecScale;
	}

	/**
	 * @return Allowed number of configured external appsec scale scans
	 * @throws ForbiddenException when license is exceeded
	 */
	private long getAppsecScaleExternalLicense() {
		final long maxAppsecScaleExternal = this.user.getMaxAppsecScaleExternal();
		if (maxAppsecScaleExternal < -1 || maxAppsecScaleExternal == 0) {
			throw new ForbiddenException("_APPSEC_SCALE_EXTERNAL_LICENSE_NO_SCANS");
		}

		return maxAppsecScaleExternal;
	}

	@Override
	public Integer startCloudsecScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException {
		if (this.configurationService.isHiabEnabled()) {
			throw new ForbiddenException("_SCAN_NOT_ALLOWED_HIAB");
		}

		checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId());

		final CloudsecScanConfigurationTemplate configuration = (CloudsecScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		configuration.validate(this.statementExecutor, this.user);

		final CustomerInterface customer = this.customerService.getCustomer(this.user.getCustomerId());
		if (customer == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final CompliancePolicyInterface policy = this.complianceDao.getCompliancePolicy(configuration.getPolicyId(), this.user.getCustomerId());
		if (policy == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final AccountInterface account = this.credentialService.getAccount(configuration.getAccountId(), this.user.getCustomerId());
		if (account == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();
		final List<List<JSONObject>> configsList =
				this.credentialService.getCloudsecScanCredentials(customer, account, policy.getKey(), configuration.getRegions(), integrationIssues);

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(DEFAULT_SCAN_TIME);
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		for (final List<JSONObject> configs : configsList) {
			final StringBuilder configsString = new StringBuilder();
			String region = "";
			for (final JSONObject conf : configs) {
				if (conf.getString("key").equals("awsRegion")) {
					region = ((String[]) conf.get("value"))[0];
				}
				configsString.append(conf).append("\n");
			}

			final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
			scanStatus.setId(this.scanStatusService.createScanStatusId());

			final String scanSchema = "scan__"
					+ this.user.getCustomerId()
					+ "_"
					+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
					+ "_"
					+ scanStatus.getId();
			scanStatus.setScanSchema(scanSchema);

			final HashMap<String, Object> scanSettings = new HashMap<>();
			scanSettings.put("SETTINGS", configsString.toString());
			if (!StringUtils.isEmpty(region)) {
				scanSettings.put("REGION", region);
			}

			scanSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

			if (scanTimeSettings.getScheduleId() != null) {
				final JSONObject reScheduleData = new JSONObject();
				reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
				reScheduleData.put("scanWindowStart", this.scanWindowStart);
				reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
				reScheduleData.putOpt("parentId", this.parentId);
				scanSettings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
			}

			// Set max concurrent scans settings
			final Boolean isLimitConcurrentScan = setMaxConcurrentScanSettings(configuration, scanSettings, null);

			addIntegrationIssues(scanStatus, integrationIssues);

			final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
			scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
			scanLog.setTemplate(ScanTemplate.CLOUDSEC);
			scanLog.setScanConfigurationId(this.scanConfiguration.getId());
			scanLog.setSchema(scanSchema);
			scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
			scanLog.setCustomerId(this.user.getCustomerId());
			scanLog.setExpectedStart(scanTimeSettings.getStartTime());
			scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
			scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
			scanLog.setParentId(this.parentId);
			scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
			setAuthentication(scanLog, Arrays.asList(AuthenticationType.valueOf(account.getType().name())));

			final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
			addScanStartedEvent(eventList, scanLogId);

			scanStatus.setTarget(this.scanConfiguration.getName());
			scanStatus.setService(ScanServiceType.CLOUDSEC);
			scanStatus.setSettings(XmlUtils.createParam(scanSettings).toString());
			scanStatus.setTargetId(scanLogId);
			scanStatus.setUserId(this.user.getMainUserId());
			scanStatus.setSubUserId(this.user.getSubUserId());
			scanStatus.setScheduleId(this.scanConfiguration.getId());
			scanStatus.setStatus(scanTimeSettings.isPending() ? ScanStatuses.WaitingScanWindow.toString() : ScanStatuses.WaitingFreeSlot.toString());
			scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
			scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
			scanStatus.setTemplateId(Template.Cloudsec.getId());
			scanStatus.setTemplateName(Template.Normal.name());
			scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
			scanStatus.setScheduleName(this.scanConfiguration.getName());
			if (isLimitConcurrentScan) {
				scanStatus.setIsPaused(3);
				scanStatus.setStatus(ScanStatuses.WaitingConcurrent.toString());
			}
			if (this.workflow != null) {
				scanStatus.setWorkflowId(this.workflow.getId());
			}
			this.scanStatusService.writeScanStatus(scanStatus);
			this.scanStatusService.executeBatch();
		}

		return configsList.size();
	}

	@Override
	public Integer startNetworkScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException {
		final StringBuilder scanProperties = new StringBuilder();
		final HashMap<String, Object> scanSettings = new HashMap<>();

		final Integer assetId = this.scanData != null && this.scanData.has("assetId") ? this.scanData.getInt("assetId") : null;
		final Integer assetIdentifierId = this.scanData != null && this.scanData.has("assetIdentifierId") ? this.scanData.getInt("assetIdentifierId") : null;
		checkRunningScanWithAsset(this.user.getCustomerId(), this.scanConfiguration.getId(), assetId);

		final NetworkScanConfigurationTemplate configuration = (NetworkScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			LOG.info("NetsecScanConfigurationTemplate is null");
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		if (this.scanData != null) {
			configuration.setAssetTagIds(null);
		}
		configuration.validate(this.statementExecutor, this.user, scanConfiguration.getScannerId(), ServiceProvider.getConfigService());

		if (this.scanData == null && ArrayUtils.isEmpty(configuration.getAssetTagIds())) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}
		// Flag for getting asset identifier from system information: IP, Hostname, mac, serial machine/product/disk id
		scanSettings.put("IDENTIFYINGINFORMATION", true);

		final ScanPolicyInterface policy = this.scanPolicyDao.getById(configuration.getScanPolicyId(), this.user.getCustomerId());
		if (policy == null) {
			LOG.info("ScanPolicy is null");
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final ScanPolicySettings mergedSettings;
		final ScanPolicyInterface overridePolicy;
		if (configuration.getOverrideScanPolicyId() == null) {
			mergedSettings = policy.getSettings();
			overridePolicy = null;
		}
		else {
			overridePolicy = this.scanPolicyDao.getById(configuration.getOverrideScanPolicyId(), this.user.getCustomerId());
			mergedSettings = combineSettings(policy, overridePolicy);
		}

		final Integer[] accountIds = combineAccounts(policy, overridePolicy);
		final List<? extends AccountInterface> accounts = this.credentialService.getAccounts(accountIds,
				new AccountType[] {AccountType.SSH, AccountType.SMB, AccountType.VSPHERE, AccountType.AWS, AccountType.AZURE}, this.user.getCustomerId());

		final List<? extends CredentialInterface> credentials = accounts.isEmpty() ? null :
				this.credentialService.getCredentials(this.user.getCustomerId(), accountIds);

		final HashMap<String, Object> fileContentMap = new HashMap<>();
		buildPropertiesFromSettings(mergedSettings, scanProperties, fileContentMap);

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(configuration.getTimeout());
		if (scanTimeSettings == null) {
			LOG.info("ScanTimeSettings is null");
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		if (this.workflow != null) {
			scanSettings.put("SAVESCANSERVICES", true);
		}

		final CustomerInterface customer = this.customerService.getCustomer(this.user.getCustomerId());
		if (customer == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final List<? extends AssetInterface> assets;
		if (assetId != null) {
			assets = this.assetDao.getByIds(new Integer[] {assetId});
		}
		else if (this.scanData == null) {
			final AssetQuery assetQuery = AssetQuery.builder()
					.customerId(this.user.getCustomerId())
					.tagIds(ArrayUtils.isEmpty(configuration.getAssetTagIds()) ? new Integer[0] : configuration.getAssetTagIds())
					.notFromSources(Source.getSwatSources().toArray(new Source[0]))
					.build();
			assets = this.assetDao.getAssets(assetQuery);
		}
		else {
			final Integer[] assetIds = this.scanData.has("assetIds") ? this.scanData.optJSONArray("assetIds").toList().toArray(new Integer[0]) : null;
			assets = ArrayUtils.isEmpty(assetIds) ? new ArrayList<>() : this.assetDao.getByIds(assetIds);
		}

		if (assets.isEmpty()) {
			throw new InputValidationException("_COULDNT_START_SCAN_NO_ASSETS");
		}

		int scansStarted = 0;

		final StringBuilder targetResolverProperties = new StringBuilder();
		final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();
		this.credentialService.buildNetworkScanDiscoverCloudCredentialsProperties(accounts, credentials, targetResolverProperties, customer, integrationIssues);

		final JSONArray ipToTargetsMapJson = this.scanData != null ? this.scanData.getJSONArray("ipToTargetsMap") : null;
		final ScanServiceType source = this.scanData != null ? this.scanData.getEnum(ScanServiceType.class, "source") : null;
		final boolean shouldStartNetworkLookup = source != ScanServiceType.NETWORK_LOOKUP && (ipToTargetsMapJson == null || ipToTargetsMapJson.isEmpty());

		final Map<Integer, List<AssetIdentifierInterface>> assetIdentifierMap = new HashMap<>();
		final Map<Integer, List<String>> virtualHostMap = new HashMap<>();
		for (final AssetInterface asset : assets) {
			if (shouldStartNetworkLookup) {
				final List<? extends AssetIdentifierInterface> assetIdentifiers;
				if (assetIdentifierId != null) {
					assetIdentifiers = this.assetIdentifierDao.getByIds(new Integer[] {assetIdentifierId});
				}
				else {
					final AssetIdentifierQuery assetIdentifierQuery = AssetIdentifierQuery.builder()
							.customerId(this.user.getCustomerId())
							.notFromSources(Source.getSwatSources().toArray(new Source[0]))
							.ids(asset != null ? Arrays.stream(asset.getAssetIdentifiers()).map(AssetIdentifierInterface::getId).toArray(Integer[]::new) : null)
							.orderBy("type DESC, lastseen DESC")
							.build();
					assetIdentifiers = this.assetIdentifierDao.getAssetIdentifiers(assetIdentifierQuery);
				}

				// filter asset identifiers
				final List<AssetIdentifierInterface> filteredAssetIdentifiers = new ArrayList<>();
				final List<String> virtualHosts = new ArrayList<>();
				for (final AssetIdentifierInterface assetIdentifier : assetIdentifiers) {
					final Integer scannerId = assetIdentifier.getScannerId() == null ? Integer.valueOf((int) LOCAL_SCANNER) : assetIdentifier.getScannerId();
					// if not allow any scanner
					if (!configuration.isAnyScanner() && !isValidScannerId(scannerId, getScannerId())) {
						LOG.info("Skipping starting scan for asset identifier {} since associated scanner ID {} does not match with scanner ID {} in workflow {}",
								assetIdentifier.getName(), scannerId, getScannerId(), this.workflow != null ? this.workflow.getName() : "");
						continue;
					}

					final AssetLinkInterface assetLink = this.assetDao.getAssetLinkByAssetIdAndAssetIdentifierId(asset.getId(), assetIdentifier.getId());

					// if not included, skip
					if (assetLink.getScanSelectionType() != ScanSelectionType.INCLUDED) {
						LOG.info("Skipping asset identifier {} since it is not included", assetIdentifier.getName());
						continue;
					}

					// add item to virtual host list
					if (assetLink.getScanSelectionType() == ScanSelectionType.INCLUDED || assetLink.getScanSelectionType() == ScanSelectionType.VHOST_ONLY) {
						if (assetIdentifier.getType() == AssetIdentifierType.HOSTNAME && mergedSettings.getVirtualHostsHostName()) {
							virtualHosts.add(assetIdentifier.getName());
						}
					}

					filteredAssetIdentifiers.add(assetIdentifier);
				}

				final List<AssetIdentifierInterface> cloudAssetIdentifiers = getAssetIdentifiers(filteredAssetIdentifiers, Arrays.asList(AssetIdentifierType.AWS_INSTANCE_ID, AssetIdentifierType.MAZ_RESOURCE, AssetIdentifierType.MAZ_SUBSCRIPTION));
				final List<AssetIdentifierInterface> hostnameIpAssetIdentifiers = getAssetIdentifiers(filteredAssetIdentifiers, Arrays.asList(AssetIdentifierType.HOSTNAME, AssetIdentifierType.IP));

				// prioritize cloud asset identifiers with cloud credentials
				// if no cloud asset identifiers are found or there are cloud asset identifiers without cloud credentials, but allowCloudAssetWithoutCloudResolver is enabled,
				// then use hostname/ip asset identifiers
				List<AssetIdentifierInterface> targetAssetIdentifiers = new ArrayList<>();
				if (!cloudAssetIdentifiers.isEmpty()) {
					if (accounts.stream().anyMatch(a -> a.getType() == AccountType.AWS || a.getType() == AccountType.AZURE)) { // has cloud credentials
						targetAssetIdentifiers = cloudAssetIdentifiers;
					}
					else if (configuration.isAllowCloudAssetWithoutCloudResolver()) { // no cloud credentials and is allowCloudAssetWithoutCloudResolver
						targetAssetIdentifiers = hostnameIpAssetIdentifiers;
					}
				}
				else {
					targetAssetIdentifiers = hostnameIpAssetIdentifiers;
				}

				// if there is no target asset identifiers, skip
				if (targetAssetIdentifiers.isEmpty()) {
					LOG.info("There is no NOT-SWAT source Asset Identifier with type {AWS_INSTANCE_ID, MAZ_RESOURCE, HOSTNAME, IP} linked to Asset: " + asset.getName());
					continue;
				}

				virtualHostMap.put(asset.getId(), virtualHosts);
				assetIdentifierMap.put(asset.getId(), targetAssetIdentifiers);
				scansStarted++;
				continue;
			}

			// Get resolved IPs from the network lookup job
			final JSONArray ipTargetInfoMap = findIpTargetInfoForAsset(ipToTargetsMapJson, asset.getId());
			if (ipTargetInfoMap == null || ipTargetInfoMap.isEmpty()) {
				LOG.info("No IP addresses found for resolved asset identifiers from asset {}", asset.getId());
				continue;
			}

			final Map<String, TargetResolver.IpTargetInfo> ipToTargetsMap = new HashMap<>();
			for (int i = 0; i < ipTargetInfoMap.length(); i++) {
				final JSONObject ipInfo = ipTargetInfoMap.getJSONObject(i);

				final JSONArray targets = ipInfo.getJSONArray("targets");
				final List<String> targetsList = targets.toList().stream()
						.map(Object::toString)
						.collect(Collectors.toList());

				final JSONArray discoveredHostnames = ipInfo.getJSONArray("discoveredHostnames");
				final List<String> discoveredHostnamesList = discoveredHostnames.toList().stream()
						.map(Object::toString)
						.collect(Collectors.toList());

				final TargetResolver.IpTargetInfo ipTargetInfo = new TargetResolver.IpTargetInfo();
				ipTargetInfo.targets = targetsList;
				ipTargetInfo.discoveredHostnames = discoveredHostnamesList;
				ipToTargetsMap.put(ipInfo.getString("ip"), ipTargetInfo);
			}

			final List<String> virtualHosts = getVirtualHostsForAsset(this.scanData.getJSONArray("virtualHostMap"), asset.getId());

			final List<AssetIdentifier> targetAssetIdentifiers = getAssetIdentifiersForAsset(this.scanData.getJSONArray("assetIdentifierMap"), asset.getId());

			// launch scan for each IP
			for (final Map.Entry<String, TargetResolver.IpTargetInfo> entry : ipToTargetsMap.entrySet()) {
				// If not scanning all IPs, only process the first one
				if (!configuration.isScanAll() && scansStarted > 0) {
					break;
				}

				final String ip = entry.getKey();
				final TargetResolver.IpTargetInfo targetInfo = entry.getValue();
				// find asset identifier from targets
				final AssetIdentifierInterface assetIdentifier = targetAssetIdentifiers.stream()
						.filter(ai -> targetInfo.targets.contains(ai.getName()))
						.findFirst()
						.orElse(null);
				if (assetIdentifier == null) {
					LOG.info("No asset identifier found with targetInfo {}", targetInfo);
					continue;
				}

				final Integer scannerId = assetIdentifier.getScannerId() == null ? Integer.valueOf((int) LOCAL_SCANNER) : assetIdentifier.getScannerId();

				@SuppressWarnings("unchecked")
				final HashMap<String, Object> assetSettings = (HashMap<String, Object>) scanSettings.clone();
				final StringBuilder assetProperties = new StringBuilder(scanProperties);
				if (scanTimeSettings.getScheduleId() != null) {
					final JSONObject reScheduleData = new JSONObject();
					reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
					reScheduleData.put("scanWindowStart", this.scanWindowStart);
					reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
					reScheduleData.putOpt("parentId", this.parentId);
					reScheduleData.put("assetIdentifierId", assetIdentifier.getId());
					reScheduleData.put("assetId", asset.getId());
					assetSettings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
				}

				assetSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

				final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
				scanStatus.setId(this.scanStatusService.createScanStatusId());

				final String scanSchema = "scan__"
						+ this.user.getCustomerId()
						+ "_"
						+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
						+ "_"
						+ scanStatus.getId();
				assetSettings.put("SCANSCHEMA", scanSchema);
				assetProperties.append("\n").append(new PluginPreferences("scan", "schema_name", PluginPreferencesType.ENTRY.getType(), scanSchema));

				final Set<AuthenticationType> authenticationTypes = new HashSet<>();
				final HashMap<String, Object> assetFileContentMap = new HashMap<>();
				this.credentialService.buildNetworkScanCredentialsProperties(asset, accounts, credentials, assetProperties, assetFileContentMap, mergedSettings,
						integrationIssues, authenticationTypes);

				assetFileContentMap.putAll(fileContentMap);
				if (!assetFileContentMap.isEmpty()) {
					final ParamList params = XmlUtils.createParam(assetFileContentMap);
					assetSettings.put(ScanSettingKeys.TARGET_FILELIST, params.toString());
				}

				final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
				scanLog.setSchema(scanSchema);
				scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
				scanLog.setTemplate(ScanTemplate.NETWORK_SCAN);
				scanLog.setScanConfigurationId(this.scanConfiguration.getId());
				scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
				scanLog.setCustomerId(this.user.getCustomerId());
				scanLog.setAssetId(asset.getId());
				scanLog.setAssetIdentifierId(assetIdentifier.getId());
				scanLog.setScannerId(scannerId);
				scanLog.setExpectedStart(scanTimeSettings.getStartTime());
				scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
				scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
				scanLog.setParentId(this.parentId);
				scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
				scanLog.setInvocationId(this.scanData.getInt("invocationId"));
				setAuthentication(scanLog, new ArrayList<>(authenticationTypes));

				// targets contains the IP and the identifier that got resolved to the IP, IP last
				targetInfo.targets.add(ip);
				scanLog.setTargets(targetInfo.targets.toArray(new String[0]));

				// add virtual hosts to scan log, sort lexicaly based on name
				final List<String> targetVirtualHosts = new ArrayList<>(virtualHosts);
				if (mergedSettings.getVirtualHostsIp() != null && mergedSettings.getVirtualHostsIp()) {
					targetVirtualHosts.add(ip);
				}
				targetVirtualHosts.sort(String.CASE_INSENSITIVE_ORDER);
				scanLog.setVirtualHosts(targetVirtualHosts.toArray(new String[0]));

				// add virtual hosts to assetProperties
				if (!targetVirtualHosts.isEmpty()) {
					assetProperties.append("\n")
							.append(new PluginPreferences("http", "virtualhosts", PluginPreferencesType.ENTRY.getType(), StringUtils.join(targetVirtualHosts, ",")));
				}

				final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
				addScanStartedEvent(eventList, scanLogId);

				addIntegrationIssues(scanStatus, integrationIssues);

				assetSettings.put(DataType.PROP.name(), PortUtils.validatePorts(assetProperties.toString()));

				// Set max concurrent scans settings, asset id
				final Boolean isLimitConcurrentScan = setMaxConcurrentScanSettings(configuration, assetSettings, asset.getId());

				scanStatus.setScannerId(scannerId);
				scanStatus.setTarget(ip);
				scanStatus.setTargetType(TARGET_TYPE_IP);
				scanStatus.setScanSchema(scanSchema);
				scanStatus.setService(ScanServiceType.NETWORK_SCAN);
				scanStatus.setSettings(XmlUtils.createParam(assetSettings).toString());
				scanStatus.setTargetId(scanLogId);
				scanStatus.setUserId(this.user.getMainUserId());
				scanStatus.setSubUserId(this.user.getSubUserId());
				scanStatus.setScheduleId(this.scanConfiguration.getId());
				scanStatus.setStatus(scanTimeSettings.isPending()
						? ScanStatuses.WaitingScanWindow.toString()
						: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
				scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
				scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
				scanStatus.setTemplateId(Template.Netsec.getId());
				scanStatus.setTemplateName(configuration.getTemplate().name());
				scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
				scanStatus.setScheduleName(this.scanConfiguration.getName());
				if (isLimitConcurrentScan) {
					scanStatus.setIsPaused(3);
					scanStatus.setStatus(ScanStatuses.WaitingConcurrent.toString());
					scanStatus.setAssetId(asset.getId());
				}
				if (this.workflow != null) {
					scanStatus.setWorkflowId(this.workflow.getId());
				}
				this.scanStatusService.writeScanStatus(scanStatus);
				this.scanStatusService.executeBatch();
				scansStarted++;
			}
		}

		if (shouldStartNetworkLookup) {
			startNetworkLookup(getScannerId(), assetIdentifierMap, virtualHostMap, targetResolverProperties.toString(), eventList);
		}

		return scansStarted;
	}

	@Override
	public Integer startAgentScan(final String scheduleUuid, final String agentUuid, final List<String> modules, final List<Event> eventList)
			throws SQLException, JAXBException {
		final StringBuilder scanProperties = new StringBuilder();
		final HashMap<String, Object> scanSettings = new HashMap<>();

		final AgentScanConfigurationTemplate configTemplate = (AgentScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configTemplate == null) {
			LOG.info("AgentScanConfigurationTemplate is null");
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}
		configTemplate.validate(this.statementExecutor, this.user);
		final ScanTimeSettings scanTimeSettings = getScanTimeSettings((int) Duration.ofHours(this.configurationService.getProperty(ConfigurationIntKey.agent_maxscantime)).getSeconds());
		if (scanTimeSettings == null) {
			LOG.info("ScanTimeSettings is null");
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		if (CollectionUtils.isNotEmpty(modules)) {
			scanSettings.put("AGENTMODULES", StringUtils.join(modules, ","));
		}

		scanSettings.put("AGENTAPIURL", this.configurationService.getProperty(ConfigurationKey.agent_api_url));
		scanSettings.put("AGENTTENANT", this.user.getCustomerUuid());
		scanSettings.put("AGENTSCHEDULEID", scheduleUuid);

		// Flag for getting asset identifier from system information: IP, Hostname, mac, serial machine/product/disk id
		scanSettings.put("IDENTIFYINGINFORMATION", true);

		scanSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

		final AssetIdentifierQuery assetIdentifierQuery = AssetIdentifierQuery.builder()
				.customerId(this.user.getCustomerId())
				.type(AssetIdentifierType.AGENT)
				.notFromSources(Source.getSwatSources().toArray(new Source[0]))
				.extraFilter("properties->>'uuid'=?")
				.extraParams(Arrays.asList(agentUuid))
				.orderBy("type DESC, lastseen DESC")
				.build();
		final List<? extends AssetIdentifierInterface> assetIdentifiers = this.assetIdentifierDao.getAssetIdentifiers(assetIdentifierQuery);
		if (assetIdentifiers.isEmpty()) {
			LOG.info(String.format("There is no NOT-SWAT source Asset Identifier type %s with agentUuid %s", AssetIdentifierType.AGENT, agentUuid));
			return 0;
		}
		final AssetIdentifierInterface assetIdentifier = assetIdentifiers.get(0);

		final AssetQuery assetQuery = AssetQuery.builder()
				.customerId(this.user.getCustomerId())
				.tagIds(ArrayUtils.isEmpty(configTemplate.getAssetTagIds()) ? new Integer[0] : configTemplate.getAssetTagIds())
				.notFromSources(Source.getSwatSources().toArray(new Source[0]))
				.ids(assetIdentifier.getAssetIds())
				.build();
		final List<? extends AssetInterface> assets = this.assetDao.getAssets(assetQuery);
		if (assets.isEmpty()) {
			LOG.info(String.format("There is no Asset with id in %s and related Asset Tag id in %s",
					Arrays.toString(assetIdentifier.getAssetIds()), Arrays.toString(configTemplate.getAssetTagIds())));
			return 0;
		}

		checkRunningScanWithAsset(this.user.getCustomerId(), this.scanConfiguration.getId(), assets.get(0).getId());

		final StringBuilder assetProperties = new StringBuilder(scanProperties);

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());

		final String scanSchema = "scan__"
				+ this.user.getCustomerId()
				+ "_"
				+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
				+ "_"
				+ scanStatus.getId();
		scanSettings.put("SCANSCHEMA", scanSchema);
		assetProperties.append("\n").append(new PluginPreferences("scan", "schema_name", PluginPreferencesType.ENTRY.getType(), scanSchema));

		final Integer scannerId = assetIdentifier.getScannerId() == null ? Integer.valueOf((int) LOCAL_SCANNER) : assetIdentifier.getScannerId();

		final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
		scanLog.setSchema(scanSchema);
		scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
		scanLog.setTemplate(ScanTemplate.AGENT_SCAN);
		scanLog.setScanConfigurationId(this.scanConfiguration.getId());
		scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
		scanLog.setCustomerId(this.user.getCustomerId());
		scanLog.setAssetId(assets.get(0).getId());
		scanLog.setAssetIdentifierId(assetIdentifier.getId());
		scanLog.setScannerId(scannerId);
		scanLog.setExpectedStart(scanTimeSettings.getStartTime());
		scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
		scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);

		final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
		addScanStartedEvent(eventList, scanLogId);
		scanStatus.setTargetType(TARGET_TYPE_AGENT);

		scanSettings.put(DataType.PROP.name(), assetProperties.toString());

		scanStatus.setScannerId(scannerId);
		scanStatus.setTarget(assetIdentifier.getName());
		scanStatus.setScanSchema(scanSchema);
		scanStatus.setService(ScanServiceType.NETWORK_SCAN);
		scanStatus.setSettings(XmlUtils.createParam(scanSettings).toString());
		scanStatus.setTargetId(scanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setScheduleId(this.scanConfiguration.getId());
		scanStatus.setStatus(scanTimeSettings.isPending()
				? ScanStatuses.WaitingScanWindow.toString()
				: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
		scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
		scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
		scanStatus.setTemplateId(Template.Netsec.getId());
		scanStatus.setTemplateName(configTemplate.getTemplate().name());
		scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
		scanStatus.setScheduleName(this.scanConfiguration.getName());
		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();
		return 1;
	}

	/**
	 * Start a scanless scan. It's triggered/ran from a NETWORK_SCAN scan configuration.
	 *
	 * @param scanLog {@link ScanLogEntryInterface}
	 * @return Number of started scans
	 */
	@Override
	public Integer startScanlessScan(final ScanLogEntryInterface scanLog, final List<Event> eventList) throws SQLException, ExecutionException {
		LOG.debug("in startScanlessScan");
		if (scanLog.getAssetId() == null || scanLog.getAssetIdentifierId() == null) {
			LOG.debug("Asset id or asset identifier id not set for scan: {}", scanLog.getId());
			return 0;
		}
		final AssetInterface asset = this.assetDao.getById(scanLog.getAssetId());
		if (asset == null) {
			LOG.info("No found asset with id: " + scanLog.getAssetId() + " for scan: " + scanLog.getId());
			return 0;
		}

		final AssetIdentifierInterface identifier = this.assetIdentifierDao.getById(scanLog.getAssetIdentifierId());
		if (identifier == null) {
			LOG.info("No found asset identifier with id: " + scanLog.getAssetIdentifierId() + " for scan: " + scanLog.getId());
			return 0;
		}

		final String identifierName = identifier.getName();
		final AssetIdentifierType identifierType = identifier.getType();
		final Integer scannerId = identifier.getScannerId() == null ? Integer.valueOf((int) LOCAL_SCANNER) : identifier.getScannerId();
		final Instant endTime = this.startTime.plus(this.configurationService.getProperty(ConfigurationIntKey.scanless_maxscantime), ChronoUnit.HOURS);

		final ScanDataInterface scanData = this.scanlogService.getScanData(scanLog.getId(), DataType.PROP);
		if (scanData == null) {
			LOG.info("No found PROP scandata with scan: " + scanLog.getId());
			return 0;
		}

		LOG.info("Start ScanlessScan for: " + identifierName);
		final StringBuilder properties = new StringBuilder(StringUtils.setEmpty(scanData.getData(), ""));

		// CAT_DETECT is change to CAT_END just to make sure scanjob does not run anything until VR fixes their scripts
		addPreference(properties, "scan", "start_cat", "CAT_END", false);

		final Map<String, Object> settings = new HashMap<>();
		settings.put(DataType.PROP.name(), properties.toString());
		settings.put("RULESONLY", "1");

		final ScanLogEntryInterface newScanLog = this.scanlogService.createPortalScanLog();
		newScanLog.setSchema(scanLog.getSchema());
		newScanLog.setStatus(ScanLogStatus.QUEUED);
		newScanLog.setTemplate(ScanTemplate.NETWORK_SCAN);
		newScanLog.setScanConfigurationId(this.scanConfiguration.getId());
		newScanLog.setCreatedById(RestObject.SYSTEM_USER);
		newScanLog.setCustomerId(this.user.getCustomerId());
		newScanLog.setAssetId(scanLog.getAssetId());
		newScanLog.setAssetIdentifierId(scanLog.getAssetIdentifierId());
		newScanLog.setScannerId(scannerId);
		newScanLog.setExpectedStart(this.startTime);
		newScanLog.setExpectedEnd(endTime);
		newScanLog.setScanless(true);
		newScanLog.setParentId(scanLog.getId());
		newScanLog.setWorkflowId(scanLog.getWorkflowId());

		final Integer newScanLogId = this.scanlogService.writeScanLog(newScanLog);
		addScanStartedEvent(eventList, newScanLogId);

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());

		final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();

		if (identifierType == AssetIdentifierType.AWS_INSTANCE_ID) {
			this.credentialService.buildCloudCredentials(this.user.getCustomerId(), asset, identifier.getAccountIds(), this.user.getCustomerUuid(), settings,
					integrationIssues);
			scanStatus.setTargetType(TARGET_TYPE_CLOUD);
		}
		else if (identifierType == AssetIdentifierType.HOSTNAME) {
			scanStatus.setTargetType(TARGET_TYPE_HOSTNAME);
		}
		else if (identifierType == AssetIdentifierType.IP) {
			scanStatus.setTargetType(TARGET_TYPE_IP);
		}

		addIntegrationIssues(scanStatus, integrationIssues);

		scanStatus.setTarget(identifierName);
		scanStatus.setService(ScanServiceType.NETWORK_SCAN);
		scanStatus.setSettings(XmlUtils.createParam(settings).toString());
		scanStatus.setScannerId(newScanLog.getScannerId());
		scanStatus.setTargetId(newScanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setScheduleId(this.scanConfiguration.getId());
		scanStatus.setScheduleName(this.scanConfiguration.getName());
		scanStatus.setStatus(scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString());
		scanStatus.setScanStart(Date.from(this.startTime));
		scanStatus.setScanEnd(Date.from(endTime));
		scanStatus.setTemplateId(Template.Netsec.getId());
		scanStatus.setAttackerName(scanLog.getAttacker());
		scanStatus.setScanSchema(scanLog.getSchema());
		scanStatus.setScanlessReportId(scanLog.getId());
		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();

		return 1;
	}

	/**
	 * Get all {@link AssetIdentifier}s from Asset by the {@link AssetIdentifierType}.
	 *
	 * @param assetIdentifiers {@link AssetIdentifierInterface}s
	 * @param types List of {@link AssetIdentifierType}.
	 * @return List of {@link AssetIdentifierInterface}.
	 **/
	private List<AssetIdentifierInterface> getAssetIdentifiers(final List<? extends AssetIdentifierInterface> assetIdentifiers, final List<AssetIdentifierType> types) {
		if (assetIdentifiers.isEmpty()) {
			return Collections.emptyList();
		}
		return assetIdentifiers.stream()
				.filter(x -> types.contains(x.getType()))
				.collect(Collectors.toList());
	}

	/**
	 * Combine the settings of a Policy and override Policy
	 *
	 * @param customPolicy The {@link ScanPolicy}.
	 * @param overridePolicy The override {@link ScanPolicy}.
	 * @return The {@link ScanPolicySettings} objects.
	 */
	private ScanPolicySettings combineSettings(final ScanPolicyInterface customPolicy, final ScanPolicyInterface overridePolicy) throws JAXBException {
		final ScanPolicySettings baseSettings = customPolicy.getSettings();
		final ScanPolicySettings mergedSettings = baseSettings != null ? baseSettings : new ScanPolicySettings();

		if (overridePolicy == null || overridePolicy.getSettings() == null) {
			return mergedSettings;
		}

		final ScanPolicySettings overrideConfig = overridePolicy.getSettings();
		for (final ScanPolicySettings.DefaultField field : ScanPolicySettings.DefaultField.values()) {
			try {
				if (overrideConfig.getValue(field) == null) {
					continue;
				}
				mergedSettings.setValue(field, overrideConfig.getValue(field));
			}
			catch (final NoSuchFieldException | IllegalAccessException e) {
				LOG.info("There was an error when getting scan policy settings field's value");
				throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
			}
		}

		return mergedSettings;
	}

	/**
	 * Combine the Accounts of a Policy and override Policy
	 *
	 * @param customPolicy The {@link ScanPolicy}.
	 * @param overridePolicy The override {@link ScanPolicy}.
	 * @return The merged account Ids.
	 */
	private Integer[] combineAccounts(final ScanPolicyInterface customPolicy, final ScanPolicyInterface overridePolicy) throws JAXBException {
		final boolean baseUseCustom = customPolicy.getSettings() != null && BooleanUtils.isTrue(customPolicy.getSettings().getUseCustomCredentials());

		// Case override policy config's useCustomCredentials is null, ignore its accountIds
		if (overridePolicy == null || overridePolicy.getSettings() == null || overridePolicy.getSettings().getUseCustomCredentials() == null) {
			return baseUseCustom ? customPolicy.getAccountIds() : null;
		}

		if (!overridePolicy.getSettings().getUseCustomCredentials()) {
			return null;
		}

		final Integer[] overrideAccountIds = overridePolicy.getAccountIds();
		return overrideAccountIds != null ? overrideAccountIds : customPolicy.getAccountIds();
	}

	/**
	 * Adds a preference to probe.
	 *
	 * @param prop Current settings for probe.
	 * @param group Property group.
	 * @param name Property name.
	 * @param value Property value.
	 * @param isFile true if this is a file.
	 */
	private void addPreference(final StringBuilder prop, final String group, final String name, final String value, final boolean isFile) {
		if (value == null) {
			return;
		}
		final String type = isFile ? PluginPreferencesType.FILE.getType() : PluginPreferencesType.ENTRY.getType();
		prop.append(new PluginPreferences(group, name, type, value)).append("\n");
	}

	/**
	 * Build scan setting properties from the {@link ScanPolicySettings}.
	 *
	 * @param settings The {@link ScanPolicySettings}.
	 * @param properties Setting properties
	 * @param fileContentMap Map of random UUID file name and its content
	 */
	private void buildPropertiesFromSettings(final ScanPolicySettings settings, final StringBuilder properties, final HashMap<String, Object> fileContentMap) {
		if (settings == null) {
			LOG.info("Settings is null");
			return;
		}

		for (final ScanPolicySettings.DefaultField defaultField : ScanPolicySettings.DefaultField.values()) {
			try {
				if (!settings.hasUpdated(defaultField) || StringUtils.isEmpty(defaultField.getOldKey())) {
					continue;
				}
				final PluginPreferences pp = new PluginPreferences();
				pp.setGroup(defaultField.getGroup());
				pp.setKey(defaultField.getOldKey());
				pp.setType(defaultField.getType().getType());
				final String value = settings.getValue(defaultField) == null ? null : settings.getValue(defaultField).toString();
				pp.setValue(PluginPreferencesType.CHECKBOX == defaultField.getType() ? Boolean.parseBoolean(value) ? "yes" : "no" : value);

				if (PluginPreferencesType.FILE == defaultField.getType()) {
					if (StringUtils.isEmpty(value)) {
						continue;
					}
					final String probeFileName = UUID.randomUUID().toString();
					fileContentMap.put(probeFileName, value.getBytes(StandardCharsets.UTF_8));
					pp.setValue(probeFileName);
				}
				properties.append(pp.toProbeSetting()).append("\n");
			}
			catch (final NoSuchFieldException | IllegalAccessException e) {
				LOG.info("There was an error when getting scan policy settings field's value");
				throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
			}
		}
	}

	@Override
	public Integer startDockerScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException {
		final Integer assetIdentifierId = this.scanData != null && this.scanData.has("assetIdentifierId") ? this.scanData.getInt("assetIdentifierId") : null;
		checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId(), assetIdentifierId);

		final DockerScanConfigurationTemplate configuration = (DockerScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		configuration.validate(this.statementExecutor, this.user);

		final List<? extends AssetIdentifierInterface> assetIdentifiers;
		if (assetIdentifierId != null) {
			assetIdentifiers = this.assetIdentifierDao.getByIds(new Integer[] {assetIdentifierId});
		}
		else {
			final Integer[] assetIds;
			if (this.scanData == null) {
				assetIds = this.scanConfiguration.getAssetIds();
			}
			else {
				assetIds = this.scanData.has("assetIds") ? this.scanData.optJSONArray("assetIds").toList().toArray(new Integer[0]) : null;
			}
			if (ArrayUtils.isEmpty(assetIds)) {
				throw new InputValidationException("_NEED_TO_LINK_ASSET_TO_SCAN");
			}

			final AssetIdentifierQuery assetIdentifierQuery = AssetIdentifierQuery.builder()
					.customerId(this.user.getCustomerId())
					.type(AssetIdentifierType.DOCKER_IMAGE)
					.linkedAssetIds(ArrayUtils.isEmpty(assetIds) ? new Integer[0] : assetIds)
					.build();
			assetIdentifiers = this.assetIdentifierDao.getAssetIdentifiers(assetIdentifierQuery);
		}

		if (assetIdentifiers.isEmpty()) {
			// Just in case. This should never happen since an asset cannot exist without underlying asset identifier(s).
			throw new InputValidationException("_ASSET_COMPOSITION_INVALID");
		}

		final Set<Integer> accountIds = new HashSet<>();
		for (final AssetIdentifierInterface assetIdentifier : assetIdentifiers) {
			if (ArrayUtils.isEmpty(assetIdentifier.getAccountIds())) {
				throw new InternalServerErrorException("_NEED_TO_LINK_ASSET_IDENTIFIER_TO_ACCOUNT");
			}
			accountIds.addAll(Arrays.stream(assetIdentifier.getAccountIds()).collect(Collectors.toSet()));
		}

		final List<? extends AccountInterface> accounts = this.credentialService.getAccounts(accountIds.toArray(new Integer[0]), this.user.getCustomerId());
		final List<? extends CredentialInterface> credentialList = this.credentialService.getCredentials(this.user.getCustomerId(), accountIds.toArray(new Integer[0]));

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(DEFAULT_SCAN_TIME);
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		int scansStarted = 0;

		for (final AssetIdentifierInterface assetIdentifier : assetIdentifiers) {
			final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();
			final AccountInterface account = accounts.stream().filter(item -> item.getId().equals(assetIdentifier.getAccountIds()[0])).findFirst().get();
			final Map<Integer, CredentialInterface> credentials = credentialList.stream()
					.filter(credential -> credential.getAccountId().equals(account.getId()))
					.collect(Collectors.toMap(CredentialInterface::getClassId, Function.identity()));
			final JSONObject jsonConfiguration = this.credentialService.getDockerScanCredentials(account, credentials, integrationIssues);

			final DockerImageProperties properties = (DockerImageProperties) assetIdentifier.getProperties();

			jsonConfiguration.remove("architecture");
			if (properties != null && !StringUtils.isEmpty(properties.getArchitecture())) {
				jsonConfiguration.put("architecture", properties.getArchitecture());
			}

			jsonConfiguration.remove("os");
			if (properties != null && !StringUtils.isEmpty(properties.getOs())) {
				jsonConfiguration.put("os", properties.getOs());
			}

			jsonConfiguration.remove("size");
			if (properties != null && properties.getSize() != null) {
				jsonConfiguration.put("size", properties.getSize());
			}

			String image = assetIdentifier.getName();
			if (properties != null && properties.getTag() != null) {
				image += ":" + properties.getTag();
			}
			jsonConfiguration.put("image", image);

			final Integer scannerId = assetIdentifier.getScannerId() == null ? Integer.valueOf((int) LOCAL_SCANNER) : assetIdentifier.getScannerId();
			if (!this.configurationService.isHiabEnabled() && scannerId == LOCAL_SCANNER) {
				//continue;
			}


			final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
			scanStatus.setId(this.scanStatusService.createScanStatusId());

			final String scanSchema = "scan__"
					+ this.user.getCustomerId()
					+ "_"
					+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
					+ "_"
					+ scanStatus.getId();
			scanStatus.setScanSchema(scanSchema);

			final HashMap<String, Object> scanSettings = new HashMap<>();
			scanSettings.put("SETTINGS", jsonConfiguration.toString());

			scanSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

			if (scanTimeSettings.getScheduleId() != null) {
				final JSONObject reScheduleData = new JSONObject();
				reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
				reScheduleData.put("scanWindowStart", this.scanWindowStart);
				reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
				reScheduleData.putOpt("parentId", this.parentId);
				reScheduleData.put("assetIdentifierId", assetIdentifier.getId());
				scanSettings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
			}

			// Set max concurrent scans settings, asset id
			final Integer assetId = assetIdentifier.getAssetIds() != null && assetIdentifier.getAssetIds().length != 0 ? assetIdentifier.getAssetIds()[0] : null;
			final Boolean isLimitConcurrentScan = setMaxConcurrentScanSettings(configuration, scanSettings, assetId);

			addIntegrationIssues(scanStatus, integrationIssues);

			final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
			scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
			scanLog.setTemplate(ScanTemplate.DOCKER_SCAN);
			scanLog.setScanConfigurationId(this.scanConfiguration.getId());
			scanLog.setScannerId(scannerId);
			scanLog.setSchema(scanSchema);
			scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
			scanLog.setCustomerId(this.user.getCustomerId());
			scanLog.setExpectedStart(scanTimeSettings.getStartTime());
			scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
			scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
			scanLog.setParentId(this.parentId);
			scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
			scanLog.setAssetIdentifierId(assetIdentifier.getId());
			scanLog.setAssetId(assetId);
			setAuthentication(scanLog, Arrays.asList(AuthenticationType.DOCKER));

			final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
			addScanStartedEvent(eventList, scanLogId);

			scanStatus.setTarget(assetIdentifier.getName());
			scanStatus.setService(ScanServiceType.DOCKER_SCAN);
			scanStatus.setSettings(XmlUtils.createParam(scanSettings).toString());
			scanStatus.setScannerId(scannerId);
			scanStatus.setTargetId(scanLogId);
			scanStatus.setUserId(this.user.getMainUserId());
			scanStatus.setSubUserId(this.user.getSubUserId());
			scanStatus.setScheduleId(this.scanConfiguration.getId());
			scanStatus.setStatus(scanTimeSettings.isPending()
					? ScanStatuses.WaitingScanWindow.toString()
					: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
			scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
			scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
			scanStatus.setTemplateId(Template.DockerScan.getId());
			scanStatus.setTemplateName(Template.Normal.name());
			scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
			scanStatus.setScheduleName(this.scanConfiguration.getName());
			if (isLimitConcurrentScan) {
				scanStatus.setIsPaused(3);
				scanStatus.setStatus(ScanStatuses.WaitingConcurrent.toString());
				scanStatus.setAssetId(assetId);
			}
			if (this.workflow != null) {
				scanStatus.setWorkflowId(this.workflow.getId());
			}
			this.scanStatusService.writeScanStatus(scanStatus);
			this.scanStatusService.executeBatch();
			scansStarted++;
		}

		return scansStarted;
	}

	@Override
	public Integer startDockerDiscoveryScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException {
		checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId());

		final Integer scannerId = getScannerId();
		if (!this.configurationService.isHiabEnabled() && scannerId == LOCAL_SCANNER) {
			throw new ForbiddenException("_NOT_ALLOWED_SCAN_LOCAL_SCANNER");
		}

		final DockerDiscoveryScanConfigurationTemplate configuration = (DockerDiscoveryScanConfigurationTemplate) this.scanConfiguration.getConfiguration();
		configuration.validate(this.statementExecutor, this.user);

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(DEFAULT_SCAN_TIME);
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final AccountInterface account = this.credentialService.getAccount(configuration.getAccountId(), this.user.getCustomerId());
		if (account == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();
		final JSONObject credentialsJson = this.credentialService.getDockerDiscoveryCredentials(account, integrationIssues);

		final HashMap<String, Object> scanSettings = new HashMap<>();
		scanSettings.put("SETTINGS", credentialsJson);

		scanSettings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

		if (scanTimeSettings.getScheduleId() != null) {
			final JSONObject reScheduleData = new JSONObject();
			reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
			reScheduleData.put("scanWindowStart", this.scanWindowStart);
			reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
			reScheduleData.putOpt("parentId", this.parentId);
			scanSettings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
		}

		final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
		scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
		scanLog.setTemplate(ScanTemplate.DOCKER_DISCOVERY);
		scanLog.setScanConfigurationId(this.scanConfiguration.getId());
		scanLog.setScannerId(scannerId);
		scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
		scanLog.setCustomerId(this.user.getCustomerId());
		scanLog.setExpectedStart(scanTimeSettings.getStartTime());
		scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
		scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
		scanLog.setParentId(this.parentId);
		scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
		setAuthentication(scanLog, Arrays.asList(AuthenticationType.DOCKER));

		final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
		addScanStartedEvent(eventList, scanLogId);

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());
		addIntegrationIssues(scanStatus, integrationIssues);
		scanStatus.setTarget(this.scanConfiguration.getName());
		scanStatus.setService(ScanServiceType.DOCKER_DISCOVERY);
		scanStatus.setSettings(XmlUtils.createParam(scanSettings).toString());
		scanStatus.setScannerId(scannerId);
		scanStatus.setTargetId(scanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setScheduleId(this.scanConfiguration.getId());
		scanStatus.setStatus(scanTimeSettings.isPending()
				? ScanStatuses.WaitingScanWindow.toString()
				: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
		scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
		scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
		scanStatus.setTemplateId(Template.DockerDiscovery.getId());
		scanStatus.setScheduleName(this.scanConfiguration.getName());
		scanStatus.setScanlogId(scanLogId);
		if (this.workflow != null) {
			scanStatus.setWorkflowId(this.workflow.getId());
		}
		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();
		return 1;
	}

	@Override
	public Integer startNetworkDiscoveryScan(final List<Event> eventList) throws SQLException, JAXBException {
		checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId());

		final NetworkDiscoveryConfigurationTemplate configuration = (NetworkDiscoveryConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		configuration.validate(this.statementExecutor, this.user, ServiceProvider.getConfigService());

		final Integer scannerId = getScannerId();
		final Scanner scanner = this.scannerDao.getById(scannerId);
		if (scanner == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		if (scanner.isScanningDisabled()) {
			throw new InputValidationException("_SCANNING_DISABLED_THAT_AWS");
		}

		final List<NetworkDiscoveryProtocol> protocols = Arrays.asList(configuration.getProtocols());
		String prop = "\n" + new PluginPreferences("peer detection", "arp enabled", "checkbox",
				protocols.contains(NetworkDiscoveryProtocol.ARP) ? "yes" : "no").toProbeSetting();
		prop += "\n" + new PluginPreferences("peer detection", "icmp enabled", "checkbox",
				protocols.contains(NetworkDiscoveryProtocol.ICMP) ? "yes" : "no").toProbeSetting();
		prop += "\n" + new PluginPreferences("peer detection", "tcp enabled", "checkbox",
				protocols.contains(NetworkDiscoveryProtocol.TCP) ? "yes" : "no").toProbeSetting();
		prop += "\n" + new PluginPreferences("peer detection", "udp enabled", "checkbox",
				protocols.contains(NetworkDiscoveryProtocol.UDP) ? "yes" : "no").toProbeSetting();

		final String discoveryPorts = configuration.getTcpPorts();
		if (!StringUtils.isEmpty(discoveryPorts)) {
			prop += "\n" + new PluginPreferences("peer detection", "tcp ports", "entry", discoveryPorts).toProbeSetting();
		}

		prop += "\n" + new PluginPreferences("peer detection", "target list", "entry", configuration.getTargetList()).toProbeSetting();

		if (!StringUtils.isEmpty(configuration.getReportFilters())) {
			prop += "\n" + new PluginPreferences("peer detection", "report bpf filter", "entry", configuration.getReportFilters()).toProbeSetting();
		}

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(configuration.getTimeout());
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final HashMap<String, Object> settings = new HashMap<>();
		settings.put("PROP", prop);
		settings.put("IPLIST", StringUtils.setEmpty(configuration.getTargetList(), ""));

		if (!StringUtils.isEmpty(configuration.getIgnoreTargetList())) {
			settings.put("BLACKLIST", configuration.getIgnoreTargetList());
		}

		settings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

		if (scanTimeSettings.getScheduleId() != null) {
			final JSONObject reScheduleData = new JSONObject();
			reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
			reScheduleData.put("scanWindowStart", this.scanWindowStart);
			reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
			reScheduleData.putOpt("parentId", this.parentId);
			settings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
		}

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());

		final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
		if (this.configurationService.isHiabEnabled()) {
			final String scanSchema = "scan__"
					+ this.user.getCustomerId()
					+ "_"
					+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
					+ "_"
					+ scanStatus.getId();
			scanLog.setSchema(scanSchema);
		}
		else {
			scanLog.setJobId(UUID.randomUUID().toString());
		}
		scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
		scanLog.setTemplate(ScanTemplate.NETWORK_DISCOVERY);
		scanLog.setScanConfigurationId(this.scanConfiguration.getId());
		scanLog.setScannerId(scannerId);
		scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
		scanLog.setCustomerId(this.user.getCustomerId());
		scanLog.setExpectedStart(scanTimeSettings.getStartTime());
		scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
		scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
		scanLog.setParentId(this.parentId);
		scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);

		final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
		addScanStartedEvent(eventList, scanLogId);

		scanStatus.setTarget(this.scanConfiguration.getName());
		scanStatus.setService(ScanServiceType.NETWORK_DISCOVERY);
		scanStatus.setSettings(XmlUtils.createParam(settings).toString());
		scanStatus.setScannerId(scannerId);
		scanStatus.setTargetId(scanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setScheduleId(this.scanConfiguration.getId());
		scanStatus.setStatus(scanTimeSettings.isPending()
				? ScanStatuses.WaitingScanWindow.toString()
				: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
		scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
		scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
		scanStatus.setTemplateId(Template.NetworkDiscovery.getId());
		scanStatus.setTemplateName(Template.Normal.name());
		scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
		scanStatus.setScheduleName(this.scanConfiguration.getName());
		if (this.workflow != null) {
			scanStatus.setWorkflowId(this.workflow.getId());
		}
		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();
		return 1;
	}

	@Override
	public Integer startCloudDiscoveryScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException {
		checkRunningScan(this.user.getCustomerId(), this.scanConfiguration.getId());

		final CloudDiscoveryConfigurationTemplate configuration = (CloudDiscoveryConfigurationTemplate) this.scanConfiguration.getConfiguration();
		if (configuration == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		configuration.validate(this.statementExecutor, this.user);

		final CustomerInterface customer = this.customerService.getCustomer(this.user.getCustomerId());
		if (customer == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final AccountInterface account = this.credentialService.getAccount(configuration.getAccountId(), this.user.getCustomerId());
		if (account == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final Integer scannerId = getScannerId();
		final Scanner scanner = this.scannerDao.getById(scannerId);
		if (scanner == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		if (scanner.isScanningDisabled()) {
			throw new InputValidationException("_SCANNING_DISABLED_THAT_AWS");
		}

		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(DEFAULT_SCAN_TIME);
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final Map<IntegrationType, List<String>> integrationIssues = new HashMap<>();
		final HashMap<String, Object> settings = this.credentialService.getCloudDiscoveryScanCredentials(customer, account, integrationIssues);
		settings.putAll(configureCloudSettings(configuration.getCloudConfiguration()));
		settings.put("IMPORTEXTERNALTAGS", configuration.isImportExternalTags());

		settings.put("SCANTIMESETTINGS", scanTimeSettings.getTimeSettings().toString());

		if (scanTimeSettings.getScheduleId() != null) {
			final JSONObject reScheduleData = new JSONObject();
			reScheduleData.put("scheduleId", scanTimeSettings.getScheduleId());
			reScheduleData.put("scanWindowStart", this.scanWindowStart);
			reScheduleData.putOpt("workflowId", this.workflow != null ? this.workflow.getId() : null);
			reScheduleData.putOpt("parentId", this.parentId);
			settings.put("RESCHEDULETIMEOUT", reScheduleData.toString());
		}

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());
		addIntegrationIssues(scanStatus, integrationIssues);

		final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
		if (this.configurationService.isHiabEnabled()) {
			final String scanSchema = "scan__"
					+ this.user.getCustomerId()
					+ "_"
					+ DateUtils.formatTimeDate(new Date()).replace(' ', '_').replace("-", "").replace(":", "")
					+ "_"
					+ scanStatus.getId();
			scanLog.setSchema(scanSchema);
		}
		else {
			scanLog.setJobId(UUID.randomUUID().toString());
		}
		scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
		scanLog.setTemplate(ScanTemplate.CLOUD_DISCOVERY);
		scanLog.setScanConfigurationId(this.scanConfiguration.getId());
		scanLog.setScannerId(scannerId);
		scanLog.setCreatedById(this.schedule != null ? RestObject.SYSTEM_USER : RestObject.getUpdaterId(this.user));
		scanLog.setCustomerId(this.user.getCustomerId());
		scanLog.setExpectedStart(scanTimeSettings.getStartTime());
		scanLog.setExpectedEnd(scanTimeSettings.getEndTime());
		scanLog.setScheduleId(this.schedule != null ? this.schedule.getId() : null);
		scanLog.setParentId(this.parentId);
		scanLog.setWorkflowId(this.workflow != null ? this.workflow.getId() : null);
		setAuthentication(scanLog, Arrays.asList(AuthenticationType.valueOf(account.getType().name())));

		final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
		addScanStartedEvent(eventList, scanLogId);

		scanStatus.setTarget(this.scanConfiguration.getName());
		scanStatus.setService(ScanServiceType.CLOUD_DISCOVERY);
		scanStatus.setSettings(XmlUtils.createParam(settings).toString());
		scanStatus.setScannerId(scannerId);
		scanStatus.setTargetId(scanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setScheduleId(this.scanConfiguration.getId());
		scanStatus.setStatus(scanTimeSettings.isPending()
				? ScanStatuses.WaitingScanWindow.toString()
				: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
		scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
		scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));
		scanStatus.setTemplateId(Template.CloudDiscovery.getId());
		scanStatus.setTemplateName(Template.Normal.name());
		scanStatus.setAttackerName(this.configurationService.getProperty(ConfigKeys.ConfigurationKey.https_globalname));
		scanStatus.setScheduleName(this.scanConfiguration.getName());
		if (this.workflow != null) {
			scanStatus.setWorkflowId(this.workflow.getId());
		}
		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();
		return 1;
	}

	/**
	 * Start a background discovery scan to get the resolve IPs.
	 * @param scannerId The scanner id.
	 * @param assetIdentifierMap The asset identifiers from each asset.
	 * @param virtualHostMap The virtual hosts associated with each asset.
	 * @param targetResolverProperties The target resolver properties.
	 * @param eventList The event list.
	 */
	private void startNetworkLookup(final Integer scannerId, final Map<Integer, List<AssetIdentifierInterface>> assetIdentifierMap,
									final Map<Integer, List<String>> virtualHostMap, final String targetResolverProperties, final List<Event> eventList)
			throws SQLException, JAXBException {
		final ScanTimeSettings scanTimeSettings = getScanTimeSettings(43200);
		if (scanTimeSettings == null) {
			throw new InternalServerErrorException("_COULDNT_QUEUE_SCAN");
		}

		final ScanLogEntryInterface scanLog = this.scanlogService.createPortalScanLog();
		scanLog.setStatus(scanTimeSettings.isPending() ? ScanLogStatus.PENDING : ScanLogStatus.QUEUED);
		scanLog.setTemplate(ScanTemplate.NETWORK_LOOKUP);
		scanLog.setScannerId((int) LOCAL_SCANNER);
		scanLog.setCreatedById(RestObject.getUpdaterId(this.user));
		scanLog.setCustomerId(this.user.getCustomerId());
		scanLog.setExpectedStart(scanTimeSettings.getStartTime());
		scanLog.setExpectedEnd(scanTimeSettings.getEndTime());

		// Get invocation ID
		final long invocationId = this.statementExecutor.getLong(new NativeSqlStatement("SELECT nextval('tscanlogs_seq')"));
		scanLog.setInvocationId((int) invocationId);

		final Integer scanLogId = this.scanlogService.writeScanLog(scanLog);
		addScanStartedEvent(eventList, scanLogId);

		final ScanStatusInterface scanStatus = this.scanStatusService.createScanStatus();
		scanStatus.setId(this.scanStatusService.createScanStatusId());
		scanStatus.setService(ScanServiceType.NETWORK_LOOKUP);
		scanStatus.setScannerId(scannerId);
		scanStatus.setTargetId(scanLogId);
		scanStatus.setUserId(this.user.getMainUserId());
		scanStatus.setSubUserId(this.user.getSubUserId());
		scanStatus.setStatus(scanTimeSettings.isPending()
				? ScanStatuses.WaitingScanWindow.toString()
				: (scannerId > 0 ? ScanStatuses.WaitingScanner.toString() : ScanStatuses.WaitingFreeSlot.toString()));
		scanStatus.setScanStart(Date.from(scanTimeSettings.getStartTime()));
		scanStatus.setScanEnd(Date.from(scanTimeSettings.getEndTime()));

		final JSONArray assetIdentifiersArray = new JSONArray();
		for (final Map.Entry<Integer, List<AssetIdentifierInterface>> entry : assetIdentifierMap.entrySet()) {
			final JSONObject assetEntry = new JSONObject();
			assetEntry.put("assetId", entry.getKey());
			assetEntry.put("assetIdentifiers", MarshallingUtils.marshalList(AssetIdentifierInterface.class, entry.getValue()));
			assetIdentifiersArray.put(assetEntry);
		}

		final JSONArray virtualHostArray = new JSONArray();
		for (final Map.Entry<Integer, List<String>> entry : virtualHostMap.entrySet()) {
			final JSONObject virtualHostEntry = new JSONObject();
			virtualHostEntry.put("assetId", entry.getKey());
			virtualHostEntry.put("virtualHosts", MarshallingUtils.marshalList(String.class, entry.getValue()));
			virtualHostArray.put(virtualHostEntry);
		}

		final JSONObject networkLookupData = new JSONObject();
		networkLookupData.put("assetIdentifierMap", assetIdentifiersArray);
		networkLookupData.put("virtualHostMap", virtualHostArray);
		networkLookupData.put("targetResolverProperties", targetResolverProperties);
		networkLookupData.put("customerId", this.user.getCustomerId());
		networkLookupData.put("scanConfigurationId", this.scanConfiguration.getId());
		networkLookupData.put("invocationId", invocationId);
		scanStatus.setNetworkLookupData(networkLookupData.toString());

		this.scanStatusService.writeScanStatus(scanStatus);
		this.scanStatusService.executeBatch();
	}

	/**
	 * Find ip target info for a specific asset id from the ipToTargetsMap JSONArray.
	 *
	 * @param ipToTargetsMap The JSONArray containing the mapping.
	 * @param targetAssetId The assetId to find.
	 * @return JSONArray containing ip target info for the asset.
	 */
	private JSONArray findIpTargetInfoForAsset(final JSONArray ipToTargetsMap, final Integer targetAssetId) {
		if (ipToTargetsMap != null && !ipToTargetsMap.isEmpty()) {
			for (int i = 0; i < ipToTargetsMap.length(); i++) {
				final JSONObject assetEntry = ipToTargetsMap.getJSONObject(i);
				if (assetEntry.getInt("assetId") == targetAssetId) {
					return assetEntry.getJSONArray("ipTargetInfo");
				}
			}
		}
		return null;
	}

	/**
	 * Get asset identifiers list for a specific asset id from a JSONArray.
	 *
	 * @param assetIdentifiersArray The JSONArray containing asset identifiers data.
	 * @param targetAssetId The asset id to find identifiers for.
	 * @return List of AssetIdentifier objects for the specified asset id.
	 * @throws JAXBException If there's an error unmarshalling the JSON.
	 */
	private List<AssetIdentifier> getAssetIdentifiersForAsset(final JSONArray assetIdentifiersArray, final Integer targetAssetId) throws JAXBException {
		for (int i = 0; i < assetIdentifiersArray.length(); i++) {
			final JSONObject entry = assetIdentifiersArray.getJSONObject(i);

			if (entry.getInt("assetId") == targetAssetId) {
				final JSONArray assetIdentifiersJson = new JSONArray(entry.getString("assetIdentifiers"));
				return MarshallingUtils.unmarshalList(AssetIdentifier.class, assetIdentifiersJson.toString());
			}
		}

		return Collections.emptyList();
	}

	/**
	 * Get virtualHosts list for a specific asset id from the virtualHostMap JSONArray.
	 *
	 * @param virtualHostMapArray The JSONArray containing the virtualHostMap data.
	 * @param targetAssetId The asset id to find virtualHosts for.
	 * @return List of virtualHosts for the specified asset id.
	 * @throws JAXBException If there's an error unmarshalling the JSON.
	 */
	private List<String> getVirtualHostsForAsset(final JSONArray virtualHostMapArray, final Integer targetAssetId) throws JAXBException {
		for (int i = 0; i < virtualHostMapArray.length(); i++) {
			final JSONObject entry = virtualHostMapArray.getJSONObject(i);

			if (entry.getInt("assetId") == targetAssetId) {
				final JSONArray virtualHostsJson = new JSONArray(entry.getString("virtualHosts"));
				return MarshallingUtils.unmarshalList(String.class, virtualHostsJson.toString());
			}
		}

		return Collections.emptyList();
	}

	/**
	 * Get detailed cloud settings based on kind of cloud credentials
	 *
	 * @param config Cloud config template
	 * @return detailed settings map
	 */
	private Map<String, Object> configureCloudSettings(final BaseCloudConfigurationTemplate config) {
		final Map<String, Object> settings = new HashMap<>();
		switch (config.getCloudDiscoveryType()) {
			case AWS:
				settings.put("REGIONS", ((CloudDiscoveryAwsConfigurationTemplate) config).getRegions());
				break;
			case AZURE:
				settings.put("SUBSCRIPTIONS", ((CloudDiscoveryAzureConfigurationTemplate) config).getSubscriptions());
				break;
			default:
		}

		return settings;
	}

	/**
	 * Check if scan is running for scan configuration.
	 *
	 * @param customerId Customer id
	 * @param scanConfigurationId Scan configuration id
	 */
	private void checkRunningScan(final Integer customerId, final Integer scanConfigurationId) throws SQLException {
		if (this.statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM scanlogs "
				+ "WHERE customerid = ? AND scanconfigurationid = ? AND status NOT IN (?, ?, ?, ?)",
				customerId, scanConfigurationId, ScanLogStatus.FINISHED, ScanLogStatus.ISSUES, ScanLogStatus.FAILED, ScanLogStatus.STOPPED)) > 0) {
			throw new ForbiddenException("_SCAN_ALREADY_RUNNING");
		}
	}

	/**
	 * Check if scan is running for scan configuration and asset identifier.
	 *
	 * @param customerId Customer id
	 * @param scanConfigurationId Scan configuration id
	 * @param assetIdentifierId Scan asset identifier id
	 */
	private void checkRunningScan(final Integer customerId, final Integer scanConfigurationId, final Integer assetIdentifierId) throws SQLException {
		if (assetIdentifierId == null) {
			checkRunningScan(customerId, scanConfigurationId);
			return;
		}

		if (this.statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM scanlogs "
				+ "WHERE customerid = ? AND scanconfigurationid = ? AND assetidentifierid = ? AND status NOT IN (?, ?, ?, ?)",
				customerId, scanConfigurationId, assetIdentifierId, ScanLogStatus.FINISHED, ScanLogStatus.ISSUES, ScanLogStatus.FAILED, ScanLogStatus.STOPPED)) > 0) {
			throw new ForbiddenException("_SCAN_ALREADY_RUNNING");
		}
	}

	/**
	 * Check if scan is running for scan configuration and asset.
	 *
	 * @param customerId Customer id
	 * @param scanConfigurationId Scan configuration id
	 * @param assetId Asset id
	 */
	private void checkRunningScanWithAsset(final Integer customerId, final Integer scanConfigurationId, final Integer assetId) throws SQLException {
		if (assetId == null) {
			checkRunningScan(customerId, scanConfigurationId);
			return;
		}

		if (this.statementExecutor.getLong(new NativeSqlStatement("SELECT COUNT(*) FROM scanlogs "
				+ "WHERE customerid = ? AND scanconfigurationid = ? AND assetid = ? AND status NOT IN (?, ?, ?, ?)",
				customerId, scanConfigurationId, assetId, ScanLogStatus.FINISHED, ScanLogStatus.ISSUES, ScanLogStatus.FAILED, ScanLogStatus.STOPPED)) > 0) {
			throw new ForbiddenException("_SCAN_ALREADY_RUNNING");
		}
	}

	@Getter
	@Setter
	public static class ScanTimeSettings {
		private Instant startTime;

		private Instant endTime;

		private Integer scheduleId;

		private boolean pending;

		@SuppressFBWarnings({"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
		private JSONObject timeSettings;
	}

	/**
	 * Get scan time settings.
	 *
	 * @param maxTime Max scan time
	 * @return Scan time settings or null if no time could be set
	 */
	public ScanTimeSettings getScanTimeSettings(final Integer maxTime) {
		final ZoneId zoneId = this.schedule == null ? ZoneOffset.UTC : ZoneId.of(this.schedule.getTimezone());
		Instant start = this.startTime;
		Instant end = null;
		boolean startTimeBlocked = false;
		final Integer scanWindowDuration = this.schedule == null ? null : this.schedule.getScanWindowDuration();
		final Instant scanWindowEnd = scanWindowDuration == null ? null : this.scanWindowStart.plus(scanWindowDuration, ChronoUnit.HOURS);
		final Integer scanWindowMaxTime = scanWindowDuration != null ? Integer.valueOf((int) Duration.ofHours(scanWindowDuration).getSeconds()) : maxTime;
		for (int i = 0; i < Duration.ofDays(7).toHours(); i++) {
			final boolean isBlocked = isTimeBlocked(start.atZone(zoneId));
			if (isBlocked) {
				// Start time is blocked, move to next full hour
				start = start.plus(1, ChronoUnit.HOURS).truncatedTo(ChronoUnit.HOURS);
				startTimeBlocked = true;
				continue;
			}
			end = calculateScanEndTime(start, scanWindowMaxTime);
			if (end == null) {
				// End time is not blocked, set it to scan window end or max time
				end = start.plus(scanWindowMaxTime, ChronoUnit.SECONDS);
			}

			if (scanWindowEnd != null) {
				if (!scanWindowEnd.isAfter(start)) {
					// Scan could not start in scan window
					return null;
				}

				if (scanWindowEnd.isBefore(end)) {
					// Scan window ends before max scan time, use scan window end
					end = scanWindowEnd;
				}
			}

			final Duration duration = Duration.between(start, end);
			if (scanWindowMaxTime <= duration.getSeconds()) {
				// Scan time is less than time to next blocked slot, use current times
				break;
			}

			if (duration.getSeconds() > (long) this.configurationService.getProperty(ConfigurationIntKey.min_scanwindow)) {
				// Scan time is longer than minimum, use current times
				break;
			}

			start = start.plus(1, ChronoUnit.HOURS);
		}

		if (end == null) {
			// No end time is set, no free slot found in a week
			return null;
		}

		final ScanTimeSettings settings = new ScanTimeSettings();
		settings.setStartTime(start);
		settings.setEndTime(end);
		settings.setPending(startTimeBlocked);

		final JSONObject timeSettings = new JSONObject();
		final Instant nextBlockedTime = this.schedule != null ? calculateScanEndTime(start, scanWindowMaxTime) : null;
		timeSettings.put("blockedTime", nextBlockedTime);
		timeSettings.put("scanWindowEnd", scanWindowEnd);
		timeSettings.put("scanDuration", maxTime);
		settings.setTimeSettings(timeSettings);

		if (this.schedule != null && !this.reScheduled) {
			settings.setScheduleId(this.schedule.getId());
		}

		return settings;
	}

	/**
	 * Check if time is within blocked time slot.
	 *
	 * @param time The scan start time
	 * @return true if time is blocked
	 */
	private boolean isTimeBlocked(final ZonedDateTime time) {
		final JSONObject json = this.schedule == null ? null : this.schedule.getBlockedTimeSlotsJson();
		if (json == null) {
			return false;
		}
		final JSONArray blockedDay = json.optJSONArray(time.getDayOfWeek().name().toLowerCase());
		if (blockedDay != null) {
			for (int i = 0; i < blockedDay.length(); i++) {
				if (blockedDay.getInt(i) == time.getHour()) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Calculate scan end time until blocked time slot or max time.
	 *
	 * @param startTime The scan start time
	 * @param maxTime Max scan time in seconds
	 * @return Scan end time or null if no times are blocked
	 */
	private Instant calculateScanEndTime(final Instant startTime, final Integer maxTime) {
		final JSONObject json = this.schedule == null ? null : this.schedule.getBlockedTimeSlotsJson();
		if (json == null) {
			return null;
		}

		final ZoneId zoneId = ZoneId.of(this.schedule.getTimezone());
		final ZonedDateTime start = startTime.atZone(zoneId);
		final ZonedDateTime max = start.plusSeconds(maxTime);
		ZonedDateTime time = start;

		while (time.isBefore(max)) {
			if (isTimeBlocked(time)) {
				return time.truncatedTo(ChronoUnit.HOURS).withZoneSameInstant(ZoneOffset.UTC).toInstant();
			}
			time = time.plusHours(1);
		}

		if (isTimeBlocked(time)) {
			final ZonedDateTime end = time.truncatedTo(ChronoUnit.HOURS);
			if (end.isBefore(max)) {
				return end.withZoneSameInstant(ZoneOffset.UTC).toInstant();
			}
		}

		return null;
	}

	/**
	 * Add a scan started event to the list
	 *
	 * @param eventList Event list.
	 * @param scanLogId Scan log id.
	 */
	private void addScanStartedEvent(final List<Event> eventList, final long scanLogId) {
		final ScanEvent scanEvent = new ScanEvent((int) scanLogId, this.user.getCustomerId(), null, this.schedule != null
				? this.schedule.getId() : null, this.scanConfiguration.getId(), this.workflow != null ? this.workflow.getId() : null);
		eventList.add(scanEvent.started());
	}

	/**
	 * Add issues text to scan settings.
	 *
	 * @param scanStatus Scan status object
	 * @param issues Map with list of issues for each integration type
	 */
	private void addIntegrationIssues(final ScanStatusInterface scanStatus, final Map<IntegrationType, List<String>> issues) {
		if (issues.isEmpty()) {
			return;
		}

		final List<JSONObject> issueInformation = new ArrayList<>();
		for (final Map.Entry<IntegrationType, List<String>> entry : issues.entrySet()) {
			final List<String> issueList = entry.getValue();
			if (issueList.isEmpty()) {
				continue;
			}
			for (final String issueString : issueList) {
				issueInformation.add(new JSONObject(issueString));
			}
		}

		if (!issueInformation.isEmpty()) {
			scanStatus.setIssues(new JSONArray(issueInformation).toString());
		}
	}

	/**
	 * Get scanner id for scan, workflow is used first, then scan configuration.
	 *
	 * @return Scanner id
	 */
	private Integer getScannerId() {
		final Integer localScannerId = (int) LOCAL_SCANNER;
		if (this.workflow != null) {
			return this.workflow.getScannerId() == null ? localScannerId : this.workflow.getScannerId();
		}
		if (this.scanConfiguration != null) {
			return this.scanConfiguration.getScannerId() == null ? localScannerId : this.scanConfiguration.getScannerId();
		}
		return localScannerId;
	}

	/**
	 * Validates if scanner ID associated to asset-identifier is same as of workflow OR exists in the scanner group associated to the workflow.
	 *
	 * @param assetIdentifierScannerId The scanner ID associated to asset identifier
	 * @param workFlowScannerId The scanner ID associated to the workflow
	 * @return {@link Boolean} to represent the state of validation
	 * @throws SQLException Thrown when there is a database error
	 */
	protected boolean isValidScannerId(final Integer assetIdentifierScannerId, final Integer workFlowScannerId) throws SQLException {
		if (assetIdentifierScannerId.equals(workFlowScannerId)) {
			return true;
		}

		// If asset identifier scanner does not match to workflow scanner then possibly "scanner group" is selected in workflow
		final Scanner workFlowScanner = this.scannerDao.getByIdAndUserId(workFlowScannerId, this.user.getMainUserId());
		if (workFlowScanner == null) { // Scanner selected in workflow does not exist anymore
			LOG.info("Failed to start scan for workflow {} since selected scanner ID {} does not exist", workflow.getName(), workFlowScannerId);
			throw new InputValidationException("_SCANNER_NOT_FOUND");
		}

		if (workFlowScanner.isGroup()) {
			final Scanner assetIdentifierScannerInGroup = this.scannerDao.getByIdAndUserIdAndGroupId(assetIdentifierScannerId, this.user.getMainUserId(), workFlowScannerId);
			return assetIdentifierScannerInGroup != null && !assetIdentifierScannerInGroup.isGroup(); // Asset identifier scanner must exist in scanner group selected in workflow
		}

		return false; // Neither same as asset-identifier, nor is a group or exists in the scanner group
	}

	/**
	 * Check if workflow has Networkscan and scale steps.
	 *
	 * @param workflow the workflow to verify, may have id null.
	 * @return true if workflow has Networkscan and scale steps
	 */
	private boolean isWorkflowWithNetworkScanAndScale(final WorkflowInterface workflow) throws JAXBException, SQLException {
		if (workflow != null) {
			final Integer[] scanConfigurationIds = Arrays.stream(workflow.getConfigurations())
					.filter(n -> n.getType() == ConfigurationType.SCAN_CONFIGURATION)
					.map(WorkflowInterface.Configuration::getId)
					.toArray(Integer[]::new);
			final Long count = this.statementExecutor.getLong(new NativeSqlStatement(
					"SELECT COUNT(*) FROM scanconfigurations WHERE id = ANY(?) AND template IN (?, ?) AND deleted IS NULL",
					scanConfigurationIds, ScanTemplate.NETWORK_SCAN, ScanTemplate.SCALE));
			return count != null && count >= 2;
		}
		return false;
	}

	/**
	 * Set authentication on scan log based on added authentication types.
	 *
	 * @param scanLog Scan log to update
	 * @param authenticationTypes List of authentication types
	 */
	private void setAuthentication(final ScanLogEntryInterface scanLog, final List<AuthenticationType> authenticationTypes) throws JAXBException {
		if (authenticationTypes.isEmpty()) {
			return;
		}

		final List<Authentication> authenticationList = new ArrayList<>();
		for (final AuthenticationType type : authenticationTypes) {
			final Authentication authentication = new Authentication();
			authentication.setType(type);
			authenticationList.add(authentication);
		}

		scanLog.setAuthentication(authenticationList.toArray(new Authentication[0]));
	}

	/**
	 * Set max concurrent scans settings and asset id for scan settings.
	 *
	 * @param configuration The scan configuration template
	 * @param scanSettings The scan settings map to update
	 * @param assetId The asset id to set
	 * @return true if concurrent scan limits were set
	 * @throws JAXBException if there is an error accessing workflow settings
	 */
	private boolean setMaxConcurrentScanSettings(final BaseScanConfigurationTemplate configuration, final Map<String, Object> scanSettings, final Integer assetId) throws JAXBException {
		Integer maxConcurrentScans = 0;
		Integer maxConcurrentScansPerAsset = 0;
		if (configuration.getTemplate() == ScanTemplate.NETWORK_SCAN) {
			final NetworkScanConfigurationTemplate networkScanConfiguration = (NetworkScanConfigurationTemplate) configuration;
			maxConcurrentScans = networkScanConfiguration.getMaxConcurrentScans();
			maxConcurrentScansPerAsset = networkScanConfiguration.getMaxConcurrentScansPerAsset();
		}
		else if (configuration.getTemplate() == ScanTemplate.DOCKER_SCAN) {
			final DockerScanConfigurationTemplate dockerScanConfiguration = (DockerScanConfigurationTemplate) configuration;
			maxConcurrentScans = dockerScanConfiguration.getMaxConcurrentScans();
			maxConcurrentScansPerAsset = dockerScanConfiguration.getMaxConcurrentScansPerAsset();
		}
		else if (configuration.getTemplate() == ScanTemplate.CLOUDSEC) {
			final CloudsecScanConfigurationTemplate cloudsecScanConfiguration = (CloudsecScanConfigurationTemplate) configuration;
			maxConcurrentScans = cloudsecScanConfiguration.getMaxConcurrentScans();
		}

		// Prefer to get max concurrent scans from workflow settings
		if (this.workflow != null) {
			maxConcurrentScans = this.workflow.getSettings().getMaxConcurrentScans();
			maxConcurrentScansPerAsset = this.workflow.getSettings().getMaxConcurrentScansPerAsset();
		}

		final Boolean isLimitConcurrentScan = maxConcurrentScans > 0 || maxConcurrentScansPerAsset > 0;
		if (isLimitConcurrentScan) {
			final JSONObject maxConcurrentSettings = new JSONObject();
			maxConcurrentSettings.put("maxConcurrentScans", maxConcurrentScans);
			if (assetId != null) {
				maxConcurrentSettings.put("maxConcurrentScansPerAsset", maxConcurrentScansPerAsset);
			}
			scanSettings.put(ScanSettingKeys.MAX_CONCURRENT_SETTINGS, maxConcurrentSettings.toString());
		}

		return isLimitConcurrentScan;
	}
}
