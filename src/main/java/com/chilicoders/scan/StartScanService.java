package com.chilicoders.scan;

import com.chilicoders.event.model.Event;
import com.chilicoders.model.ScanLogEntryInterface;

import java.net.MalformedURLException;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.ExecutionException;

import javax.xml.bind.JAXBException;

public interface StartScanService {
	Integer startScaleScan(final List<Event> eventList) throws SQLException, JAXBException, MalformedURLException;

	Integer startCloudsecScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException;

	Integer startDockerScan(final List<Event> eventList) throws <PERSON><PERSON>Exception, JAXBException, ExecutionException;

	Integer startDockerDiscoveryScan(final List<Event> eventList) throws S<PERSON>Exception, JAXBException, ExecutionException;

	Integer startNetworkDiscoveryScan(final List<Event> eventList) throws S<PERSON>Exception, JAXBException;

	Integer startCloudDiscoveryScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException;

	Integer startNetworkScan(final List<Event> eventList) throws SQLException, JAXBException, ExecutionException;

	Integer startScanlessScan(final ScanLogEntryInterface scanLog, final List<Event> eventList) throws SQLException, ExecutionException;

	Integer startAgentScan(final String scheduleUuid, final String agentUuid, final List<String> modules, final List<Event> eventList) throws SQLException, JAXBException;
}
