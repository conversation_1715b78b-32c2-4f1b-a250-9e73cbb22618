package com.chilicoders.task.impl;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.task.BackgroundTask;
import com.chilicoders.task.BackgroundTaskService;
import com.chilicoders.task.BackgroundTaskStatus;
import com.chilicoders.task.BackgroundTaskType;

class BackgroundTaskServiceImpl implements BackgroundTaskService {

	private final Connection conn;

	public BackgroundTaskServiceImpl(final Connection conn) {
		this.conn = conn;
	}

	@Override
	public List<BackgroundTask> getTasks(final UserDetails user) throws SQLException {
		final String sqlStatement = DbHelper.getSelect(BackgroundTask.class, Access.ADMIN, "userid = ? AND subuserid = ? ORDER BY id");
		return DbObject.fetchObjects(BackgroundTask.class, conn, sqlStatement, user.getMainUserId(), user.getSubUserId());
	}

	@Override
	public long startTask(final UserDetails user, final BackgroundTaskType taskType) throws SQLException {
		final BackgroundTask task = BackgroundTask.builder()
				.userId(user.getMainUserId())
				.subuserId(user.getSubUserId())
				.taskType(taskType.getId())
				.status(BackgroundTaskStatus.ONGOING.getLabel())
				.created(new Date())
				.build();
		final long id = task.save(conn);
		conn.commit();
		return id;
	}

	@Override
	public void markTaskDone(final long taskId) throws SQLException {
		DbObject.execute(conn, "UPDATE tasks SET status = ?, updated = now() WHERE id = ?", BackgroundTaskStatus.DONE.getLabel(), taskId);
		conn.commit();
	}

	@Override
	public void markTaskError(final long taskId) throws SQLException {
		DbObject.execute(conn, "UPDATE tasks SET status = ?, updated = now() WHERE id = ?", BackgroundTaskStatus.ERROR.getLabel(), taskId);
		conn.commit();
	}

	@Override
	public void clearTasks(final UserDetails user) throws SQLException {
		DbObject.execute(conn, "DELETE FROM tasks WHERE userid = ? and subuserid = ? AND status != ?", user.getMainUserId(), user.getSubUserId(),
				BackgroundTaskStatus.ONGOING.getLabel());
		conn.commit();
	}
}
