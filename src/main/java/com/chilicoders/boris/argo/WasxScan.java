package com.chilicoders.boris.argo;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;

import javax.xml.bind.JAXBException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.chilicoders.bl.MessageBusiness;
import com.chilicoders.boris.objects.Scan;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.scanconfiguration.model.ScaleScanConfigurationTemplate;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.IpUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XmlUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
public class WasxScan {
	private static final Logger LOG = LogManager.getLogger(WasxScan.class);
	@Getter
	private final Scan scan;
	@Getter
	private String settings;
	@Getter
	private String scanTarget;
	private final HashMap<String, Object> attackObject;

	/**
	 * Constructor.
	 *
	 * @param scan The {@link Scan} instance.
	 */
	@SuppressFBWarnings(value = "CT_CONSTRUCTOR_THROW")
	public WasxScan(final Scan scan) {
		this.scan = scan;
		this.attackObject = XmlUtils.getParams(new ParamList(scan.getSettings()));

		this.settings = (String) this.attackObject.get("SETTINGS");
		final JSONObject settingsJson = new JSONObject(this.settings);
		JSONArray blacklist = settingsJson.getJSONArray("scans").getJSONObject(0).optJSONArray("addr-blacklist");
		if (blacklist == null) {
			blacklist = new JSONArray();
		}
		for (final String blacklistItem : ServiceProvider.getIpService(null).getBlacklist()) {
			blacklist.put(blacklistItem);
		}
		settingsJson.getJSONArray("scans").getJSONObject(0).put("addr-blacklist", blacklist);
		this.settings = settingsJson.toString();

		this.scanTarget = (String) this.attackObject.get("TARGET");
	}

	/**
	 * Check whether wasx scan includes infrastructure scan.
	 *
	 * @return True if wasx scan includes infrastructure scan. False otherwise.
	 */
	public boolean includeInfrastructureScan() {
		return StringUtils.getBooleanValue((String) this.attackObject.get("RUNINFRASTRUCTURESCAN"), false);
	}

	/**
	 * Perform a hostname lookup.
	 *
	 * @return True upon success, false otherwise.
	 */
	public boolean doHostnameLookup() throws SQLException {
		int targetType = this.scan.getTargetType();
		String reason = null;

		final XMLDoc xml = new XMLDoc(this.scan.getSettings(), "PARAMLIST", "PARAM");
		if (xml != null) {
			final String settings = xml.getValueOfWithValue("VALUE", "NAME", "SETTINGS");
			if (settings != null) {
				final JSONObject json = new JSONObject(settings);

				if (json.has("scans")) {
					final JSONArray scans = json.getJSONArray("scans");
					if (!scans.isEmpty()) {
						try {
							final ScaleScanConfigurationTemplate template =
									MarshallingUtils.unmarshal(ScaleScanConfigurationTemplate.class, scans.getJSONObject(0).toString());
							if (template.getHostMap() != null) {
								for (final ScaleScanConfigurationTemplate.HostMap hostMap : template.getHostMap()) {
									if (scanTarget.equals(hostMap.getFrom())) {
										this.scan.setIpAddress(hostMap.getTo()[0]);
										return true;
									}
								}
							}
						}
						catch (final JSONException | JAXBException e) {
							LOG.warn("Error parsing template: {}", scans.getJSONObject(0), e);
						}
					}
				}
			}
		}

		if (targetType == TargetService.TARGET_TYPE_NETBIOS) {
			if (!Configuration.isNetbiosEnabled()) {
				reason = MessageBusiness.getMessageString("_NO_WINS_SERVER_DEFINED", "en");
			}
			else {
				final String ip = IpUtils.convertNetbiosToIp(this.scanTarget);
				if (StringUtils.isEmpty(ip) || ip.equals(this.scanTarget)) {
					reason = MessageBusiness.getMessageString("_FAILED_NETBIOS_LOOKUP", "en");
				}
				else {
					this.scan.setIpAddress(ip);
					reason = null;
				}
			}

			if (!StringUtils.isEmpty(reason) && !StringUtils.isEmpty(this.scan.getHostname())) {
				targetType = TargetService.TARGET_TYPE_HOSTNAME;
				this.scanTarget = this.scan.getHostname();
			}
		}

		if (targetType == TargetService.TARGET_TYPE_HOSTNAME) {
			try (final Connection conn = DbAccess.getInstance().getConnection()) {
				final String ip = ServiceProvider.getIpService(conn).convertHostnameToIp(this.scanTarget);
				if (StringUtils.isEmpty(ip) || ip.equals(this.scanTarget)) {
					reason = MessageBusiness.getMessageString("_REASON_HOSTNAME_FAILED", "en");
				}
				else {
					this.scan.setIpAddress(ip);
					reason = null;
				}
			}
		}

		if (!StringUtils.isEmpty(reason)) {
			LOG.info("Could not find IP for target: {}", this.scanTarget);
			this.scan.setReason(reason);
			this.scan.setScanResult(Scan.ScanResult.Failed);
			this.scan.setScanStatus(ScanStatuses.Stopped.toString());
			this.scan.addIssue(IssueType.SCAN_FAILED, this.scan.getReason());
			return false;
		}

		return true;
	}
}
