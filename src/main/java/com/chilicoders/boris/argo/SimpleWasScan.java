package com.chilicoders.boris.argo;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Arrays;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.chilicoders.boris.objects.Scan;
import com.chilicoders.core.scandata.api.model.PluginPreferences;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

@Getter
@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
public class SimpleWasScan {
	private static final Logger LOG = LogManager.getLogger(SimpleWasScan.class);
	private final Scan scan;
	private ArrayList<PluginPreferences> settings = null;

	public SimpleWasScan(final Scan scan) {
		this.scan = scan;
		readSettings();
	}

	/**
	 * Read settings.
	 */
	public void readSettings() {
		try {
			if (!StringUtils.isEmpty(getScan().getSettings())) {
				final ParamList pl = new ParamList(getScan().getSettings());
				this.settings = new ArrayList<>(Arrays.asList(PluginPreferences.getPreferences(pl.getValueOfWithValue("VALUE", "NAME", "PROP"))));
			}
		}
		catch (final Exception e) {
			LOG.error("Could not read settings for webapp scan.", e);
		}
	}

	/**
	 * Get wasx preferences.
	 *
	 * @return wasx preferences.
	 * @throws IOException A {@link IOException} exception.
	 */
	public String getWasXPreferences() throws IOException {
		final String scanSchema = "was_" + getScan().getId();

		final long timeLeft = getScan().getScanEnd().getTime() - System.currentTimeMillis();
		final JSONObject settings = new JSONObject();
		final JSONArray scans = new JSONArray();
		final JSONObject scan = new JSONObject();
		final JSONArray cannotMatch = new JSONArray();
		scan.put("name", scanSchema);
		scan.put("nseconds", timeLeft / 1000);
		scan.put("fuzzing", true);
		scan.put("addr-blacklist", new JSONArray(ServiceProvider.getIpService(null).getBlacklist()));
		scans.put(scan);
		settings.put("scans", scans);
		if (!StringUtils.isEmpty(getSetting("was", "url-blacklist"))) {
			for (final String reg : getSetting("was", "url-blacklist").replaceAll(",", "\n").split("\n")) {
				final JSONObject m = new JSONObject();
				m.put("url", reg);
				cannotMatch.put(m);
			}
		}
		if (!StringUtils.isEmpty(getSetting("was", "body-blacklist"))) {
			for (final String reg : getSetting("was", "body-blacklist").replaceAll(",", "\n").split("\n")) {
				final JSONObject m = new JSONObject();
				m.put("body", reg);
				cannotMatch.put(m);
			}
		}
		if (!cannotMatch.isEmpty()) {
			scan.put("cannot-match", cannotMatch);
		}
		try (final Writer fw = new StringWriter()) {
			settings.write(fw);
			return fw.toString();
		}
	}

	/**
	 * Get was setting.
	 *
	 * @param group Setting group
	 * @param key Setting key
	 * @return Setting value
	 */
	private String getSetting(final String group, final String key) {
		if (this.settings != null) {
			for (final PluginPreferences pref : this.settings) {
				if (key.equals(pref.getKey()) && (group == null || group.equals(pref.getGroup()))) {
					return pref.getValue();
				}
			}
		}
		return "";
	}
}
