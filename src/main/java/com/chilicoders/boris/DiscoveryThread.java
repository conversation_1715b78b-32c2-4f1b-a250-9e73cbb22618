package com.chilicoders.boris;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import javax.json.Json;
import javax.json.stream.JsonParser;
import javax.json.stream.JsonParsingException;
import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.chilicoders.bl.LdapBusiness;
import com.chilicoders.boris.objects.Scan;
import com.chilicoders.boris.objects.Scan.ScanResult;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.agents.api.model.Agent;
import com.chilicoders.core.scandata.api.model.Lookup;
import com.chilicoders.core.scandata.api.model.PluginPreferences;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.discover.AmazonDiscoveryEngine;
import com.chilicoders.discover.AmazonDiscoveryResult;
import com.chilicoders.discover.AzureDiscoveryEngine;
import com.chilicoders.discover.AzureDiscoveryResult;
import com.chilicoders.discover.CloudFrontDistribution;
import com.chilicoders.discover.DiscoveryEngine;
import com.chilicoders.discover.DiscoveryListener;
import com.chilicoders.discover.DiscoveryResult;
import com.chilicoders.discover.DiscoveryResult.DiscoveryResultType;
import com.chilicoders.discover.DockerDiscoveryEngine;
import com.chilicoders.discover.LoadBalancer;
import com.chilicoders.discover.MazData;
import com.chilicoders.discover.MazResource;
import com.chilicoders.discover.TargetResolver;
import com.chilicoders.model.docker.DockerDiscoveryResult;
import com.chilicoders.rest.models.AssetIdentifier;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.IpUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XmlUtils;

import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.services.ec2.model.Instance;
import software.amazon.awssdk.services.ec2.model.Tag;

public class DiscoveryThread extends AttackThread implements DiscoveryListener {
	private static final int RUNNING_PERCENTAGE = 27;
	private static final Logger LOG = LogManager.getLogger(DiscoveryThread.class);

	private DiscoveryEngine de;

	/**
	 * Constructor.
	 *
	 * @param outscanThread Outscanthread to send status to.
	 * @param scan Scan information.
	 */
	public DiscoveryThread(final OutscanThread outscanThread, final Scan scan) {
		super(outscanThread, scan);

		if (Configuration.isDevMode()) {
			final File localScanSchema = new File("/tmp/discovery.report");
			if (localScanSchema.exists()) {
				this.localScanSchema = localScanSchema.getAbsolutePath();
			}
		}
	}

	private final List<DiscoveryResult> result = new ArrayList<>();
	private DockerDiscoveryResult dockerDiscoveryResult;
	private final List<String> matchResult = new ArrayList<>();
	private String localScanSchema;
	private JSONObject networkLookupReport;

	/**
	 * Run a discovery of agents.
	 */
	private void runAgentDiscovery() {
		try {
			final XMLDoc settings = new XMLDoc(getScan().getSettings(), "PARAMLIST", "PARAM");
			final String agentApiUrl = settings.getValueOfWithValue("VALUE", "NAME", "AGENTAPIURL");
			final String tenant = settings.getValueOfWithValue("VALUE", "NAME", "AGENTTENANT");

			sendStatus("Running", RUNNING_PERCENTAGE);

			final Pair<String, InputStream> runDiscoveryResult = ServiceProvider.getAgentsService(agentApiUrl).runDiscovery(tenant);
			final String error = runDiscoveryResult.getLeft();

			if (error != null) {
				getScan().setReason(error);
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}

			if (runDiscoveryResult.getRight().available() > 0) {
				final JsonParser parser = Json.createParser(runDiscoveryResult.getRight());
				while (parser.hasNext()) {
					if (parser.next() == JsonParser.Event.START_OBJECT) {
						final Agent discoveredAgent = MarshallingUtils.unmarshal(Agent.class, parser.getValue().asJsonObject().toString());
						this.targetDiscovered(DiscoveryResult.builder()
								.hostnames(discoveredAgent.getHostname())
								.agentId(discoveredAgent.getUuid())
								.agentVersion(discoveredAgent.getVersion())
								.agentCustomAttributes(discoveredAgent.getCustomAttributes())
								.agentLastSynchronized(discoveredAgent.getLastSynchronized())
								.agentRetired(discoveredAgent.getRetired())
								.valid(true)
								.alive(true)
								.type(DiscoveryResultType.AGENT)
								.build());
					}
				}
			}

			sendReport(DiscoveryEngine.OK);
		}
		catch (final JAXBException | JsonParsingException | IOException ex) {
			LOG.error("Exception caught during agent discovery", ex);
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run a discovery in amazon cloud.
	 */
	private void runAmazonDiscovery() {
		try {
			final AmazonDiscoveryEngine amazonDiscoveryEngine;
			String roleArn = null;
			if (!Configuration.isAwsScannerModeEnabled()) {
				final XMLDoc settings = new XMLDoc(getScan().getSettings(), "PARAMLIST", "PARAM");
				roleArn = settings.getValueOfWithValue("VALUE", "NAME", "AWSARN");
				final String externalId = settings.getValueOfWithValue("VALUE", "NAME", "AWSEXTERNALID");
				if (StringUtils.isEmpty(roleArn) || StringUtils.isEmpty(externalId)) {
					throw AwsServiceException.builder().message("Invalid AWS credentials").build();
				}
				amazonDiscoveryEngine = new AmazonDiscoveryEngine(roleArn.trim(), externalId.trim());
			}
			else {
				amazonDiscoveryEngine = new AmazonDiscoveryEngine();
			}

			sendStatus("Running", RUNNING_PERCENTAGE);

			final AmazonDiscoveryResult amazonResult = amazonDiscoveryEngine.runDiscovery();

			if (!amazonResult.getFailuresInRegions().isEmpty()) {
				LOG.info("List of Amazon regions where failures occured: " + String.join(", ", amazonResult.getFailuresInRegions()));
				getScan().setReason("Warning failed regions: " + String.join(", ", amazonResult.getFailuresInRegions()) + ". ARN: " + amazonDiscoveryEngine.getArn());
			}
			if (amazonResult.isFailuresInAllRegions()) {
				LOG.info("AWS failures occurred in all regions");
				getScan().setReason("AWS failures occurred in all regions.");
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}

			for (final Entry<String, List<Instance>> entry : amazonResult.getFoundInstances().entrySet()) {
				for (final Instance instance : entry.getValue()) {
					final String ipAddress = !Configuration.isAwsScannerModeEnabled()
							? instance.publicIpAddress()
							: instance.privateIpAddress(); // scan public from outscan, private from AWS scanners
					this.targetDiscovered(DiscoveryResult.builder()
							.ip(ipAddress)
							.hostnames(instance.instanceId())
							.type(DiscoveryResultType.AWS_INSTANCE)
							.awsArn(roleArn)
							.valid(amazonDiscoveryEngine.isInstanceValid(instance))
							.alive(true)
							.build());
				}
			}

			for (final Entry<String, List<LoadBalancer>> entry : amazonResult.getFoundLoadBalancers().entrySet()) {
				for (final LoadBalancer loadBalancer : entry.getValue()) {
					this.targetDiscovered(DiscoveryResult.builder()
							.ip(loadBalancer.getPublicIp())
							.hostnames(loadBalancer.getHostname())
							.type(DiscoveryResultType.AWS_INSTANCE)
							.awsArn(roleArn)
							.valid(true)
							.alive(true)
							.build());
				}
			}

			for (final CloudFrontDistribution cfDistribution : amazonResult.getFoundCloudFrontDistributions()) {
				this.targetDiscovered(DiscoveryResult.builder()
						.ip(cfDistribution.getIp())
						.hostnames(cfDistribution.getHostname())
						.type(DiscoveryResultType.AWS_INSTANCE)
						.awsArn(roleArn)
						.valid(true)
						.alive(true)
						.build());
			}

			sendReport(DiscoveryEngine.OK);
		}
		catch (final AwsServiceException e) {
			final String errorMessage = e.awsErrorDetails() != null ? e.awsErrorDetails().errorMessage() : e.getMessage();
			LOG.warn("Couldn't start Amazon discovery scan caught exception: {}", errorMessage);
			getScan().setReason(errorMessage);
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
		catch (final Exception e) {
			LOG.warn("Couldn't start Amazon discovery scan caught exception: {}", e.getMessage());
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Perform a lookup discovery scan.
	 */
	private void runLookupDiscovery() {
		try {
			final XMLDoc settings = new XMLDoc(getScan().getSettings(), "PARAMLIST", "PARAM");
			final String[] targets = StringUtils.setEmpty(settings.getValueOfWithValue("VALUE", "NAME", "targets"), "").split("\n");
			final StringBuilder targetList = new StringBuilder();
			for (final String target : targets) {
				if (!StringUtils.isEmpty(target)) {
					final String ip = target.split("\\|", 3)[1];
					final String hostname = target.split("\\|", 3)[2];
					if (!StringUtils.isEmpty(ip)) {
						StringUtils.addXid(targetList, ip, "\n");
					}
					else if (!StringUtils.isEmpty(hostname)) {
						StringUtils.addXid(targetList, hostname, "\n");
					}
				}
			}

			sendStatus("Running", RUNNING_PERCENTAGE);

			final List<String> params = new ArrayList<>();
			params.add("-r");
			params.add("-d");
			params.add("icmp,tcp,udp");
			params.add("-w");
			params.add("10000");

			this.de = new DiscoveryEngine(this);
			final int exitCode = this.de.runPdetect(targetList.toString(), params, 5L * 60 * 1000);
			sendReport(exitCode);
		}
		catch (final RuntimeException e) {
			LOG.error("Error occured during discovery", e);
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run a Docker Registry discovery
	 * and get the full list of Docker images.
	 */
	private void runDockerDiscovery() {
		try {
			if (!StringUtils.isEmpty(this.localScanSchema)) {
				sendReport(DiscoveryEngine.OK);
				return;
			}

			sendStatus("Running", RUNNING_PERCENTAGE);

			final DockerDiscoveryEngine dockerDiscoveryEngine = new DockerDiscoveryEngine(getScan());
			this.dockerDiscoveryResult = dockerDiscoveryEngine.runDiscovery();

			sendReport(dockerDiscoveryResult.getDiscoveryEngineStatus());
		}
		catch (final Exception e) {
			LOG.error("Docker discovery scan caught exception: " + e.getMessage());
			getScan().setReason("An error occurred during discovery");
			if (StringUtils.isEmpty(getScan().getIssues())) {
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			}
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run the discovery.
	 */
	private void runDiscovery() {
		try {
			if (!StringUtils.isEmpty(this.localScanSchema)) {
				sendReport(DiscoveryEngine.OK);
				return;
			}

			final ParamList sl = new ParamList(getScan().getSettings());
			final PluginPreferences[] preferences = PluginPreferences.getPreferences(sl.getValueOfWithValue("VALUE", "NAME", "PROP"));
			final String iplist = StringUtils.setEmpty(sl.getValueOfWithValue("VALUE", "NAME", "IPLIST"), "");
			final String blacklist = StringUtils.setEmpty(sl.getValueOfWithValue("VALUE", "NAME", "BLACKLIST"), "");
			String targets = iplist;
			try (final Connection conn = DbAccess.getInstance().getConnection()) {
				if (!StringUtils.isEmpty(blacklist)) {
					targets = ServiceProvider.getIpService(conn).excludeTargets(iplist, blacklist, false);
				}
				targets = IpUtils.cleanTargets(conn, targets);

				final Set<String> invalidTargets = ServiceProvider.getIpService(conn).getInvalidTargets(targets);
				if (!invalidTargets.isEmpty()) {
					final String blacklistedTargets = StringUtils.concatHashValues(invalidTargets, ",", false);
					LOG.info("Target list contained IP's not allowed : " + blacklistedTargets);
					getScan().setReason("Invalid or blacklisted target(s) found in the target list: " + blacklistedTargets);
					sendReport(DiscoveryEngine.GENERAL_ERROR);
					return;
				}
			}

			if (StringUtils.isEmpty(targets)) {
				getScan().setReason("No targets found");
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}
			targets = targets.replaceAll(" ", "\n");

			final String[] protocols = new String[] {"arp", "icmp", "udp", "tcp"};
			final StringBuilder disabledProtocols = new StringBuilder();
			for (final String protocol : protocols) {
				final boolean enabled = StringUtils.getBooleanValue(PluginPreferences.getValue(preferences, "peer detection", protocol + " enabled"));
				if (!enabled) {
					StringUtils.addXid(disabledProtocols, protocol);
				}
			}

			final List<String> params = new ArrayList<>();
			final String portSettings = PluginPreferences.getValue(preferences, "peer detection", "tcp ports");
			if (!StringUtils.isEmpty(portSettings)) {
				params.add("-p");
				params.add(portSettings);
			}

			if (disabledProtocols.length() > 0) {
				params.add("-d");
				params.add(disabledProtocols.toString());
			}

			if (StringUtils.getBooleanValue(PluginPreferences.getValue(preferences, "peer detection", "tcp-rst enabled"))) {
				params.add("-f");
				params.add("tcp-rst");
			}

			final String bpfFilter = PluginPreferences.getValue(preferences, "peer detection", "report bpf filter");
			if (!StringUtils.isEmpty(bpfFilter)) {
				params.add("-m");
				params.add(bpfFilter);
			}

			sendStatus("Running", RUNNING_PERCENTAGE);

			final String filename = getFilename(Long.toString(getScan().getId()), getScan().getScanStartDate());
			try (final BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(Paths.get(filename)), UTF_8))) {
				this.de = new DiscoveryEngine(this, writer);
				final int exitCode = this.de.runPdetect(targets, params, getScan().getScanEnd().getTime() - new Date().getTime());
				sendReport(exitCode);
			}
		}
		catch (final RuntimeException | SQLException | IOException e) {
			LOG.warn("Couldn't start discovery scan caught exception: " + e.getMessage());
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run the network discovery.
	 */
	private void runNetworkDiscovery() {
		try {
			if (!StringUtils.isEmpty(this.localScanSchema)) {
				sendReport(DiscoveryEngine.OK);
				return;
			}

			final ParamList paramList = new ParamList(getScan().getSettings());
			final PluginPreferences[] preferences = PluginPreferences.getPreferences(paramList.getValueOfWithValue("VALUE", "NAME", "PROP"));
			final String ipList = StringUtils.setEmpty(paramList.getValueOfWithValue("VALUE", "NAME", "IPLIST"), "");
			final String blacklist = StringUtils.setEmpty(paramList.getValueOfWithValue("VALUE", "NAME", "BLACKLIST"), "");
			String targets = ipList;
			try (final Connection conn = DbAccess.getInstance().getConnection()) {
				if (!StringUtils.isEmpty(blacklist)) {
					targets = ServiceProvider.getIpService(conn).excludeTargets(ipList, blacklist, false);
				}
				targets = IpUtils.cleanTargets(conn, targets);

				final Set<String> invalidTargets = ServiceProvider.getIpService(conn).getInvalidTargets(targets);
				if (!invalidTargets.isEmpty()) {
					final String blacklistedTargets = StringUtils.concatHashValues(invalidTargets, ",", false);
					LOG.info("Target list contained IP's not allowed : " + blacklistedTargets);
					getScan().setReason("Invalid or blacklisted target(s) found in the target list: " + blacklistedTargets);
					getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
					sendReport(DiscoveryEngine.GENERAL_ERROR);
					return;
				}
			}

			if (StringUtils.isEmpty(targets)) {
				getScan().setReason("No targets found");
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}
			targets = targets.replaceAll(" ", "\n");

			final String[] protocols = new String[] {"arp", "icmp", "udp", "tcp"};
			final StringBuilder disabledProtocols = new StringBuilder();
			for (final String protocol : protocols) {
				final boolean enabled = StringUtils.getBooleanValue(PluginPreferences.getValue(preferences, "peer detection", protocol + " enabled"));
				if (!enabled) {
					StringUtils.addXid(disabledProtocols, protocol);
				}
			}

			final List<String> params = new ArrayList<>();
			final String portSettings = PluginPreferences.getValue(preferences, "peer detection", "tcp ports");
			if (!StringUtils.isEmpty(portSettings)) {
				params.add("-p");
				params.add(portSettings);
			}

			if (disabledProtocols.length() > 0) {
				params.add("-d");
				params.add(disabledProtocols.toString());
			}

			final String bpfFilter = PluginPreferences.getValue(preferences, "peer detection", "report bpf filter");
			if (!StringUtils.isEmpty(bpfFilter)) {
				params.add("-b");
				params.add(bpfFilter);
			}

			sendStatus("Running", RUNNING_PERCENTAGE);

			final String filename = getFilename(Long.toString(getScan().getId()), getScan().getScanStartDate());
			try (final BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(Paths.get(filename)), UTF_8))) {
				this.de = new DiscoveryEngine(this, writer);
				final int exitCode = this.de.runNetworkDiscovery(targets, params, getScan().getScanEnd().getTime() - new Date().getTime());
				sendReport(exitCode);
			}
		}
		catch (final RuntimeException | SQLException | IOException e) {
			LOG.warn("Couldn't start network discovery scan caught exception: " + e.getMessage());
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run the network lookup.
	 */
	private void runNetworkLookup() {
		sendStatus("Running", RUNNING_PERCENTAGE);

		try {
			final String networkLookupData = getScan().getNetworkLookupData();

			final JSONObject networkLookupDataJson = new JSONObject(networkLookupData);

			// Get the asset identifier map to be resolved
			final Map<Integer, List<AssetIdentifier>> assetIdentifierMap = new HashMap<>();
			final JSONArray assetIdentifiersArray = networkLookupDataJson.getJSONArray("assetIdentifierMap");
			for (int i = 0; i < assetIdentifiersArray.length(); i++) {
				final JSONObject entry = assetIdentifiersArray.getJSONObject(i);
				final Integer assetId = entry.getInt("assetId");
				final JSONArray assetIdentifiersJson = new JSONArray(entry.getString("assetIdentifiers"));

				assetIdentifierMap.put(assetId, MarshallingUtils.unmarshalList(AssetIdentifier.class, assetIdentifiersJson.toString()));
			}

			// Get the virtual host map
			final Map<Integer, List<String>> virtualHostMap = new HashMap<>();
			final JSONArray virtualHostArray = networkLookupDataJson.getJSONArray("virtualHostMap");
			for (int i = 0; i < virtualHostArray.length(); i++) {
				final JSONObject entry = virtualHostArray.getJSONObject(i);
				final Integer assetId = entry.getInt("assetId");
				final JSONArray virtualHostsJson = new JSONArray(entry.getString("virtualHosts"));
				virtualHostMap.put(assetId, MarshallingUtils.unmarshalList(String.class, virtualHostsJson.toString()));
			}

			final String targetResolverProperties = networkLookupDataJson.getString("targetResolverProperties");
			final TargetResolver targetResolver = new TargetResolver();

			// Resolve IPs from the asset identifiers for each asset
			final JSONArray ipJsonArray = new JSONArray();
			for (final Map.Entry<Integer, List<AssetIdentifier>> assetEntry : assetIdentifierMap.entrySet()) {
				final Map<String, TargetResolver.IpTargetInfo> ipToTargetsMap = targetResolver.resolveIps(assetEntry.getValue(), targetResolverProperties);
				if (ipToTargetsMap.isEmpty()) {
					LOG.info("No IP addresses found for resolved asset identifiers {}", assetEntry.getValue());
					continue;
				}

				// check if there is any new discovered hostnames
				final List<String> virtualHosts = virtualHostMap.get(assetEntry.getKey());
				for (final Map.Entry<String, TargetResolver.IpTargetInfo> entry : ipToTargetsMap.entrySet()) {
					if (!entry.getValue().discoveredHostnames.isEmpty()) {
						for (final String hostname : entry.getValue().discoveredHostnames) {
							if (!virtualHosts.contains(hostname)) {
								virtualHosts.add(hostname);
							}
						}
					}
				}
				virtualHostMap.put(assetEntry.getKey(), virtualHosts);

				final JSONObject assetEntryJson = new JSONObject();
				assetEntryJson.put("assetId", assetEntry.getKey());

				final JSONArray ipTargetJsonArray = new JSONArray();
				for (final Map.Entry<String, TargetResolver.IpTargetInfo> ipEntry : ipToTargetsMap.entrySet()) {
					final JSONObject ipEntryJson = new JSONObject();
					ipEntryJson.put("ip", ipEntry.getKey());
					ipEntryJson.put("targets", ipEntry.getValue().targets);
//					ipEntryJson.put("discoveredHostnames", ipEntry.getValue().discoveredHostnames);
					ipTargetJsonArray.put(ipEntryJson);
				}
				assetEntryJson.put("ipTargetInfo", ipTargetJsonArray);

				ipJsonArray.put(assetEntryJson);
			}

			// Get the virtual host map
			final JSONArray virtualHostJsonArray = new JSONArray();
			for (final Map.Entry<Integer, List<String>> entry : virtualHostMap.entrySet()) {
				final JSONObject virtualHostEntry = new JSONObject();
				virtualHostEntry.put("assetId", entry.getKey());
				virtualHostEntry.put("virtualHosts", MarshallingUtils.marshalList(String.class, entry.getValue()));
				virtualHostJsonArray.put(virtualHostEntry);
			}

			final JSONObject report = new JSONObject();
			report.put("customerId", networkLookupDataJson.getInt("customerId"));
			report.put("scanConfigurationId", networkLookupDataJson.getInt("scanConfigurationId"));
			report.put("ipToTargetsMap", ipJsonArray);
			report.put("assetIdentifierMap", assetIdentifiersArray);
			report.put("virtualHostMap", virtualHostJsonArray);
			report.put("invocationId", networkLookupDataJson.getInt("invocationId"));
			this.networkLookupReport = report;

			sendReport(DiscoveryEngine.OK);
		}
		catch (final JAXBException e) {
			LOG.warn("Couldn't start network lookup caught exception: " + e.getMessage());
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run the cloud discovery.
	 */
	private void runCloudDiscovery() {
		if (!StringUtils.isEmpty(this.localScanSchema)) {
			sendReport(DiscoveryEngine.OK);
			return;
		}

		final XMLDoc settings = new XMLDoc(getScan().getSettings(), "PARAMLIST", "PARAM");

		final String regions = StringUtils.setEmpty(settings.getValueOfWithValue("VALUE", "NAME", "REGIONS"), "");

		if (!regions.isEmpty()) {
			runAwsDiscovery(settings, regions);
		}
		else {
			runAzureDiscovery(settings);
		}
	}

	/**
	 * Run Amazon discovery
	 *
	 * @param settings Settings
	 * @param regions Regions
	 */
	private void runAwsDiscovery(final XMLDoc settings, final String regions) {
		try {
			final AmazonDiscoveryEngine amazonDiscoveryEngine = new AmazonDiscoveryEngine(true);

			final String arn = settings.getValueOfWithValue("VALUE", "NAME", "ARN");
			if (!StringUtils.isEmpty(arn)) {
				amazonDiscoveryEngine.setArn(arn);
				amazonDiscoveryEngine.setExternalId(settings.getValueOfWithValue("VALUE", "NAME", "ARNEXTERNALID"));
			}
			amazonDiscoveryEngine.setAccessKey(settings.getValueOfWithValue("VALUE", "NAME", "ACCESSKEY"));
			amazonDiscoveryEngine.setSecretKey(settings.getValueOfWithValue("VALUE", "NAME", "SECRETKEY"));
			amazonDiscoveryEngine.setFilterRegions(
					new HashSet<>(Arrays.asList(regions.split(","))));
			amazonDiscoveryEngine.setImportExternalTagsEnabled(StringUtils.getBooleanValue(settings.getValueOfWithValue("VALUE", "NAME", "IMPORTEXTERNALTAGS"), false));

			sendStatus("Running", RUNNING_PERCENTAGE);

			final AmazonDiscoveryResult amazonResult = amazonDiscoveryEngine.runDiscovery();

			if (amazonResult.isFailuresInAllRegions()) {
				LOG.info("Failures occurred in all selected regions: " + String.join(", ", amazonResult.getFailuresInRegions()));
				getScan().setReason("Failures occurred in all selected regions: " + String.join(", ", amazonResult.getFailuresInRegions()));
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}

			String statusDetails = "";
			if (!amazonResult.getFailuresInRegions().isEmpty()) {
				LOG.info("List of regions where failures occured: {}", String.join(", ", amazonResult.getFailuresInRegions()));
				statusDetails = "Failed regions: " + String.join(", ", amazonResult.getFailuresInRegions());
			}

			if (!amazonResult.getWarningMessages().isEmpty()) {
				LOG.info("Warnings: {}", String.join(", ", amazonResult.getWarningMessages()));
				if (!StringUtils.isEmpty(statusDetails)) {
					statusDetails += "\n ";
				}
				statusDetails += "Warnings: " + String.join("\n ", amazonResult.getWarningMessages());
			}
			getScan().setReason(StringUtils.isEmpty(statusDetails) ? null : statusDetails);
			if (!StringUtils.isEmpty(getScan().getReason())) {
				getScan().addIssue(IssueType.SCAN_WARNING, getScan().getReason());
			}

			for (final Entry<String, List<Instance>> entry : amazonResult.getFoundInstances().entrySet()) {
				final String region = entry.getKey();
				for (final Instance instance : entry.getValue()) {
					final JSONObject match = new JSONObject();
					match.put("ip", instance.publicIpAddress());
					match.put("instanceId", instance.instanceId());
					match.put("hostname", StringUtils.setEmpty(instance.publicDnsName(), instance.privateDnsName()));
					match.put("valid", amazonDiscoveryEngine.isInstanceValid(instance));
					match.put("region", region);
					match.put("accountId", amazonResult.getAccountId());
					final JSONArray tags = new JSONArray();
					for (final Tag tag : instance.tags()) {
						tags.put(new JSONObject().put("key", tag.key()).put("value", tag.value()));
					}
					match.put("tags", tags);
					this.addMatch(match.toString());
				}
			}

			for (final Entry<String, List<LoadBalancer>> entry : amazonResult.getFoundLoadBalancers().entrySet()) {
				final String region = entry.getKey();
				for (final LoadBalancer loadBalancer : entry.getValue()) {
					final JSONObject match = new JSONObject();
					match.put("ip", loadBalancer.getPublicIp());
					match.put("hostname", loadBalancer.getHostname());
					match.put("valid", true);
					match.put("region", region);
					match.put("accountId", amazonResult.getAccountId());
					match.put("tags", loadBalancer.getTagsAsJson());
					this.addMatch(match.toString());
				}
			}

			for (final CloudFrontDistribution cfDistribution : amazonResult.getFoundCloudFrontDistributions()) {
				final JSONObject match = new JSONObject();
				match.put("ip", cfDistribution.getIp());
				match.put("hostname", cfDistribution.getHostname());
				match.put("valid", true);
				match.put("accountId", amazonResult.getAccountId());
				match.put("tags", cfDistribution.getTagsAsJson());
				this.addMatch(match.toString());
			}

			sendReport(DiscoveryEngine.OK);
		}
		catch (final AwsServiceException e) {
			final String errorMessage = e.awsErrorDetails() != null ? e.awsErrorDetails().errorMessage() : e.getMessage();
			LOG.info("Couldn't start cloud discovery scan caught exception: {}", errorMessage);
			getScan().setReason(errorMessage);
			getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
		catch (final Exception e) {
			LOG.info("Couldn't start cloud discovery scan caught exception: {}", e.getMessage());
			getScan().setReason("An error occurred during discovery");
			if (StringUtils.isEmpty(getScan().getIssues())) {
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			}
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Run azure discovery.
	 *
	 * @param settings Settings
	 */
	private void runAzureDiscovery(final XMLDoc settings) {
		try {
			final AzureDiscoveryEngine azureDiscoveryEngine = new AzureDiscoveryEngine();

			azureDiscoveryEngine.setClientId(settings.getValueOfWithValue("VALUE", "NAME", "CLIENTID"));
			azureDiscoveryEngine.setSecretKey(settings.getValueOfWithValue("VALUE", "NAME", "SECRETKEY"));
			azureDiscoveryEngine.setTenantId(settings.getValueOfWithValue("VALUE", "NAME", "TENANTID"));
			azureDiscoveryEngine.setSubscriptions(settings.getValueOfWithValue("VALUE", "NAME", "SUBSCRIPTIONS"));
			azureDiscoveryEngine.setScanId(Long.toString(getScan().getId()));

			sendStatus("Running", RUNNING_PERCENTAGE);

			final AzureDiscoveryResult azureResult = azureDiscoveryEngine.runDiscovery();

			if (!azureResult.getErrorMessages().isEmpty()) {
				LOG.info("Errors when running azure discovery: {}", String.join(", ", azureResult.getErrorMessages()));
				getScan().setReason(String.join(", ", azureResult.getErrorMessages()));
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
				sendReport(DiscoveryEngine.GENERAL_ERROR);
				return;
			}

			if (!azureResult.getWarningMessages().isEmpty()) {
				LOG.info("Warnings when running azure discovery: {}", String.join(", ", azureResult.getWarningMessages()));
				getScan().setReason(String.join("\n ", azureResult.getWarningMessages()));
				getScan().addIssue(IssueType.SCAN_WARNING, getScan().getReason());
			}

			if (azureResult.isDiscoverSubscriptionsOnly()) {
				final JSONObject match = new JSONObject();
				match.put("valid", true);
				match.put("discoverSubscriptionsOnly", true);
				match.put("subscriptions", azureResult.getSubscriptions().stream().map(MazData::toJSON).collect(Collectors.toList()));
				this.addMatch(match.toString());
				sendReport(DiscoveryEngine.OK);
				return;
			}

			for (final MazResource resource : azureResult.getMazResources()) {
				final JSONObject match = new JSONObject();
				match.put("tenantId", resource.getTenantId() == null ? null : resource.getTenantId().toJSON());
				match.put("subscription", resource.getSubscription() == null ? null : resource.getSubscription().toJSON());
				match.put("resourceGroup", resource.getResourceGroup() == null ? null : resource.getResourceGroup().toJSON());
				match.put("resource", resource.toJSON());
				match.put("ip", resource.getIp());
				match.put("ipv6", resource.getIpv6());
				match.put("mac", resource.getMac());
				match.put("hostname", resource.getHostname());
				match.put("hostnameIpv6", resource.getHostnameIpv6());
				match.put("tags", resource.getTags());
				match.put("valid", true);
				match.put("discoverSubscriptionsOnly", false);
				this.addMatch(match.toString());
			}
			sendReport(DiscoveryEngine.OK);
		}
		catch (final Exception ex) {
			LOG.error("Failed to execute azure discovery: " + ex.getMessage(), ex);
			sendReport(DiscoveryEngine.GENERAL_ERROR);
		}
	}

	/**
	 * Runs a LDAP discovery.
	 */
	private void runLdapDiscovery() {
		sendStatus("Running", 0);
		final LdapBusiness lb = new LdapBusiness();
		final int exitCode = lb.runLdapDiscovery(this);
		sendReport(exitCode);
	}

	@Override
	public void run() {
		if (Configuration.isAwsScannerModeEnabled()) {
			if (getScan().getService() == ScanServiceType.AmazonDiscovery && Configuration.isAwsScanningEnabled()) {
				runAmazonDiscovery();
			}
			else {
				LOG.warn("Couldn't start Amazon discovery scan: scanning is disabled on this instance");
				getScan().setReason("Scanning is disabled on this AWS instance");
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
				sendReport(DiscoveryEngine.GENERAL_ERROR);
			}
		}
		else if (getScan().getService() == ScanServiceType.AmazonDiscovery) {
			runAmazonDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.AgentDiscovery) {
			runAgentDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.LdapDiscovery) {
			runLdapDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.Lookup) {
			runLookupDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.DOCKER_DISCOVERY) {
			runDockerDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.NETWORK_DISCOVERY) {
			runNetworkDiscovery();
		}
		else if (getScan().getService() == ScanServiceType.NETWORK_LOOKUP) {
			runNetworkLookup();
		}
		else if (getScan().getService() == ScanServiceType.CLOUD_DISCOVERY) {
			runCloudDiscovery();
		}
		else {
			runDiscovery();
		}
	}

	@Override
	public void stopScan() {
		setShouldStop(true);
		if (this.de != null) {
			this.de.shouldNotRun = true;
		}
	}

	@Override
	public void pauseScan() {
		setShouldPause(true);
	}

	@Override
	public void sendStatus(final String state, final int percent) {
		final String finalState = (percent == 100 ? "Report" : state);
		if (percent == 100) {
			getScan().setScanStatus(ScanStatuses.WaitingReport.toString());
		}
		else {
			getScan().setScanStatus(ScanStatuses.Running.toString());
		}

		if (percent != getScan().getScanPercent() || !finalState.equals(getScan().getScanState())) {
			getScan().setScanPercent(percent);
			getScan().setScanState(finalState);
			getScan().setUpdateStatus(true);
		}
	}

	@Override
	public synchronized void targetDiscovered(final DiscoveryResult target) {
		this.result.add(target);
	}

	@Override
	public void addMatch(final String match) {
		this.matchResult.add(match);
	}

	/**
	 * Send the report to outscanthread.
	 *
	 * @param errorCode An error code.
	 */
	private void sendReport(final int errorCode) {
		if (errorCode == DiscoveryEngine.OK) {
			sendStatus("Report", 100);
			if (createReport()) {
				getScan().setScanResult(ScanResult.OK);
			}
			else {
				getScan().setScanResult(ScanResult.Failed);
			}
		}
		else if (errorCode == DiscoveryEngine.STOPPED) {
			getScan().setScanStatus(ScanStatuses.Stopped.toString());
			getScan().setScanResult(ScanResult.Stopped);
		}
		else if (errorCode == DiscoveryEngine.TIMEOUT) {
			getScan().setScanStatus(ScanStatuses.Stopped.toString());
			getScan().setScanResult(ScanResult.Timeout);
			getScan().setReason("Discovery did not finish in the scheduled discovery window");
			getScan().addIssue(IssueType.SCAN_TIMEOUT, getScan().getReason());
		}
		else {
			getScan().setScanStatus(ScanStatuses.Stopped.toString());
			getScan().setScanResult(ScanResult.Failed);
			if (StringUtils.isEmpty(getScan().getReason())) {
				getScan().setReason("An error occured during discovery, error code: " + errorCode);
				getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			}
		}

		getScan().setUpdateStatus(true);
		getOutscanThread().signoffScan(getScan(), getScan().getScanResult() == ScanResult.OK);
	}

	/**
	 * Create the report for the discovery scan.
	 *
	 * @return True on success.
	 */
	private boolean createReport() {
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			final Map<String, String> lookups = new HashMap<>();
			if (!getScan().isLookup()
					&& getScan().getLookup() != Lookup.ALL.getNumber()
					&& !getScan().isDockerDiscovery()
					&& !getScan().isNetworkDiscovery()
					&& !getScan().isCloudDiscovery()
					&& !getScan().isNetworkLookup()) {
				final String iplist = new XMLDoc(getScan().getSettings(), "PARAMLIST", "PARAM").getValueOfWithValue("VALUE", "NAME", "IPLIST");
				if (iplist != null) {
					final String[] targets = iplist.split("\n");
					for (final String t : targets) {
						final String val = t.trim();
						if (ServiceProvider.getIpService(conn).isNetbios(val, true)) {
							final String ip = IpUtils.convertHostnamesToIp(conn, val);
							if (!val.equals(ip.trim())) {
								lookups.put(ip.trim(), val);
							}
						}
					}
				}
			}

			if (Configuration.isNetbiosEnabled() && !Arrays.asList(new ScanServiceType[] {
					ScanServiceType.Lookup, ScanServiceType.LdapDiscovery, ScanServiceType.AmazonDiscovery, ScanServiceType.AgentDiscovery,
					ScanServiceType.NETWORK_DISCOVERY, ScanServiceType.CLOUD_DISCOVERY, ScanServiceType.NETWORK_LOOKUP
			}).contains(getScan().getService())) {
				synchronized (this.result) {
					for (final DiscoveryResult result : this.result) {
						if (!shouldStop() && (getScan().getLookup() & Lookup.NETBIOS.getNumber()) == Lookup.NETBIOS.getNumber()) {
							final String netbios = IpUtils.convertIpToNetbios(result.getIp());
							if (!StringUtils.isEmpty(netbios) && !netbios.equals(result.getIp()) && XmlUtils.isValidXml(netbios)) {
								result.setNetbios(netbios);
							}
						}
						else if (lookups.containsKey(result.getIp())) {
							final String netbios = lookups.get(result.getIp());
							if (ServiceProvider.getIpService(conn).isNetbios(netbios, false) && XmlUtils.isValidXml(netbios)) {
								result.setNetbios(netbios);
							}
						}
					}
				}
			}

			final String jsonResult;
			if (!StringUtils.isEmpty(this.localScanSchema)) {
				jsonResult = StringUtils.readTextFile(this.localScanSchema);
			}
			else if (ScanServiceType.DOCKER_DISCOVERY == getScan().getService()) {
				jsonResult = MarshallingUtils.marshal(this.dockerDiscoveryResult);
			}
			else if (getScan().isNetworkDiscovery() || getScan().isCloudDiscovery()) {
				StringUtils.sortJsonStringsByNodePath(this.matchResult, "data", "from");
				jsonResult = this.matchResult.isEmpty() ? "{}" : StringUtils.join(this.matchResult, "\n");
			}
			else if (getScan().isNetworkLookup()) {
				jsonResult = this.networkLookupReport.toString();
			}
			else {
				synchronized (this.result) {
					jsonResult = MarshallingUtils.marshalList(DiscoveryResult.class, this.result);
				}
			}

			try (final FileOutputStream out = new FileOutputStream(OutscanThread.getReportFile(getScan().getId(), false, false, false), false);
					final GZIPOutputStream gzip = new GZIPOutputStream(out)) {
				gzip.write(jsonResult.getBytes(UTF_8));
			}

			return true;
		}
		catch (final RuntimeException | IOException | SQLException | JAXBException e) {
			LOG.error("Error createReport", e);
			return false;
		}
	}
}
