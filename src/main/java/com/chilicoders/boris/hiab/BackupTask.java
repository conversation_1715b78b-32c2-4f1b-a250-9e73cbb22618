package com.chilicoders.boris.hiab;

import java.util.Calendar;
import java.util.Date;
import java.util.TimerTask;

import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.thread.TimerHandler;

/**
 * A task that starts a HIAB backup and then reschedules itself for later execution.
 */
public class BackupTask extends TimerTask {
	/**
	 * Reschedule that task to a later date, depending on the setup of the HIAB.
	 */
	private void rescheduleTask() {
		final int freq = Configuration.getProperty(ConfigurationIntKey.hiab_backup_ftp_frequency);
		final Date nextDate = DateUtils.parseTimeDate(Configuration.getProperty(ConfigurationKey.hiab_backup_ftp_nextdate));
		final Calendar cal = Calendar.getInstance();
		cal.setTime(nextDate);
		do {
			switch (freq) {
				case 2:
					cal.add(Calendar.MONTH, 1);
					break;
				case 3:
					cal.add(Calendar.MONTH, 3);
					break;
				case 4:
					cal.add(Calendar.DAY_OF_YEAR, 14);
					break;
				case 5:
					cal.add(Calendar.DAY_OF_YEAR, 1);
					break;
				default:
					cal.add(Calendar.DAY_OF_YEAR, 7);
					break;
			}
		}
		while (cal.before(Calendar.getInstance()));
		TimerHandler.schedule(TimerHandler.Task.BackupTask, cal.getTime());
		Configuration.setProperty(ConfigurationKey.hiab_backup_ftp_nextdate, DateUtils.formatTimeDate(cal.getTime()), true);
	}

	/**
	 * @see java.util.TimerTask#run()
	 */
	public void run() {
		rescheduleTask();
		new Backup((String) null).start();
	}
}
