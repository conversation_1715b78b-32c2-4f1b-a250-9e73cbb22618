package com.chilicoders.boris;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPOutputStream;

import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.chilicoders.bl.ReportingBusiness;
import com.chilicoders.boris.objects.Scan;
import com.chilicoders.boris.objects.Scan.ScanResult;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.encryption.api.exception.EncryptionException;
import com.chilicoders.core.encryption.api.model.EncryptionDecryptionRequest;
import com.chilicoders.core.libellum.ServiceIdentity.Service;
import com.chilicoders.core.reporting.api.SpecialNoteType;
import com.chilicoders.core.rule.RuleEngineListener;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatuses;
import com.chilicoders.core.scandata.api.model.issue.IssueType;
import com.chilicoders.model.ReportData;
import com.chilicoders.model.ReportDataList;
import com.chilicoders.model.ReportEntryTypes;
import com.chilicoders.model.Rules;
import com.chilicoders.model.Template;
import com.chilicoders.model.WasTypes;
import com.chilicoders.ruleengine.ProductInformation;
import com.chilicoders.ruleengine.Rule;
import com.chilicoders.ruleengine.RuleDef;
import com.chilicoders.ruleengine.RuleEngine;
import com.chilicoders.ruleengine.RuleException;
import com.chilicoders.ruleengine.ScanDataCollector;
import com.chilicoders.ruleengine.WasXDataCollector;
import com.chilicoders.ruleengine.db.PatchSupersedence;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.Executor;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XmlUtils;

import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Scope;
import co.elastic.apm.api.Transaction;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

public class WasXThread extends AttackThread {
	private static final Logger LOG = LogManager.getLogger(WasXThread.class);

	private boolean runNormalScan = false;
	private String settings;
	private String scanTarget;
	@Getter
	private String localWasSchema;
	private String localReportPath;

	/**
	 * Constructor.
	 *
	 * @param outscanThread Outscanthread used for notifying about scan results.
	 * @param scan Scan to perform.
	 */
	@SuppressFBWarnings(value = "CT_CONSTRUCTOR_THROW")
	public WasXThread(final OutscanThread outscanThread, final Scan scan) {
		super(outscanThread, scan);

		if (!StringUtils.isEmpty(getScan().getSettings())) {
			setAttackObject(XmlUtils.getParams(new ParamList(getScan().getSettings())));
			this.settings = (String) getAttackObject().get("SETTINGS");

			if (scan.isWasXScan()) {
				final JSONObject settings = new JSONObject((String) getAttackObject().get("SETTINGS"));
				JSONArray blacklist = settings.getJSONArray("scans").getJSONObject(0).optJSONArray("addr-blacklist");
				if (blacklist == null) {
					blacklist = new JSONArray();
				}

				for (final String blacklistItem : ServiceProvider.getIpService(null).getBlacklist()) {
					blacklist.put(blacklistItem);
				}
				settings.getJSONArray("scans").getJSONObject(0).put("addr-blacklist", blacklist);
				this.settings = settings.toString();
			}

			this.runNormalScan = StringUtils.getBooleanValue((String) getAttackObject().get("RUNINFRASTRUCTURESCAN"), false);
			this.scanTarget = (String) getAttackObject().get("TARGET");
		}

		if (Configuration.isDevMode()) {
			final File localScanSchema = new File("/tmp/was.report");
			if (localScanSchema.exists()) {
				this.localWasSchema = localScanSchema.getAbsolutePath();
				this.localReportPath = "/tmp";
			}
		}
	}

	@Override
	public void stopScan() {
		Executor.execute(Configuration.getConfigService(), Configuration.getProperty(ConfigurationKey.scanjob_path), "stop", getScan().getProbeId());
		setShouldStop(true);
	}

	@Override
	public void pauseScan() {
		// WasX scans cannot be paused
	}

	@Override
	public void run() {
		final Transaction transaction = ElasticApm.startTransaction();
		try (final Scope ignored = transaction.activate()) {
			transaction.setName("WasXThread#runScan");
			transaction.setType(Transaction.TYPE_REQUEST);
			transaction.setLabel("scan", getScan().getId());

			getOutscanThread().updateRunningWasX(1);
			final String reportPath = Configuration.getProperty(ConfigurationKey.wasx_cache) + getScan().getScanSchema();

			if (getScan().isWasXScan() && !StringUtils.isEmpty(this.scanTarget)) {
				try {
					if (!doHostnameLookup(this.scanTarget)) {
						getScan().setReason(null);
					}
				}
				catch (final SQLException e) {
					// Ignore
				}
			}

			if (StringUtils.isEmpty(this.localWasSchema)) {
				if (getScan().getProbeId() == null && new File(reportPath + "/probeid").exists()) {
					final String probeId = new String(Files.readAllBytes(Paths.get(reportPath, "probeid")), UTF_8);
					if (!StringUtils.isEmpty(probeId)) {
						getScan().setProbeId(probeId.trim());
					}
				}
				final boolean isScanRunning = getScan().getProbeId() != null
						&& Executor.execute(Configuration.getConfigService(), Configuration.getProperty(ConfigurationKey.scanjob_path), "is-running", getScan().getProbeId()).getKey() == 0;
				if (!isScanRunning && !Paths.get(reportPath, "report").toFile().exists()) {
					try {
						if (new File(reportPath).exists()) {
							FileUtils.deleteDirectory(new File(reportPath));
						}
						if (!new File(reportPath).mkdirs()) {
							LOG.info("Path not created: {}", reportPath);
						}
						final String settingsFileName = reportPath + "/settings.json";
						try (final Writer fw = new OutputStreamWriter(Files.newOutputStream(Paths.get(settingsFileName)), UTF_8)) {
							fw.write(this.settings);
						}
					}
					catch (final IOException e) {
						LOG.error("Error writing settings to file", e);
						transaction.captureException(e);
						getScan().setScanResult(ScanResult.Failed);
						getScan().setScanStatus(ScanStatuses.Stopped.toString());
						getScan().setUpdateStatus(true);
						getScan().setReason("Could not write settings to file.");
						getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
						getOutscanThread().signoffScan(getScan(), false);
						return;
					}

					final long timeDiff = getScan().getScanEnd().getTime() - System.currentTimeMillis();
					final long timeDiffMinutes = TimeUnit.MILLISECONDS.toMinutes(timeDiff);
					final Pair<Integer, String> result = Executor.execute(Configuration.getConfigService(), Configuration.getProperty(ConfigurationKey.scanjob_path),
							"start", "-timeout", (timeDiffMinutes + 10) + "m", "-env", "APM_TRANSACTION_ID=" + transaction.getId(),
							"/usr/bin/wasx", "scan", "-settings", reportPath + "/settings.json", "-report", reportPath, "-workdir", reportPath, "-journald-log");
					final int exitCode = result.getKey();
					if (exitCode != 0) {
						getScan().setScanResult(ScanResult.Failed);
						getScan().setScanStatus(ScanStatuses.Stopped.toString());
						getScan().setUpdateStatus(true);
						getScan().setReason("Could not start was scan.");
						getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
						getOutscanThread().signoffScan(getScan(), false);
						return;
					}

					getScan().setProbeId(result.getValue().trim());
					Files.write(Paths.get(reportPath, "probeid"), getScan().getProbeId().getBytes(UTF_8));
					getScan().setUpdateStatus(true);
				}

				do {
					try {
						Thread.sleep(Configuration.getProperty(ConfigurationIntKey.wasx_sleep));

						final String progressString = StringUtils.readTextFile(reportPath + "/scandata/progress.json");
						if (!StringUtils.isEmpty(progressString)) {
							final JSONArray progress = new JSONArray(progressString);
							int latestSegmentIndex = -1;
							String latestSegment = "";
							for (int i = 0; i < progress.length(); i++) {
								final int segmentIndex = progress.getJSONObject(i).optInt("segment-index", -1);
								if (segmentIndex > -1 && segmentIndex > latestSegmentIndex) {
									latestSegmentIndex = segmentIndex;
									latestSegment = progress.getJSONObject(i).optString("segment", "Unknown");
								}
							}
							if (latestSegmentIndex > -1) {
								getScan().setScanStatus(latestSegment);
								getScan().setUpdateStatus(true);
							}
						}
					}
					catch (final InterruptedException e) {
						transaction.captureException(e);
						LOG.info("Sleep failed");
					}
				}
				while (Executor.execute(Configuration.getConfigService(), new String[] {Configuration.getProperty(ConfigurationKey.scanjob_path), "is-running", getScan().getProbeId()}).getKey() == 0);

				encryptFile(reportPath, "/settings.json");
				if (Files.exists(Paths.get(reportPath + "/scandata.tar.xz"))) {
					encryptFile(reportPath, "/scandata.tar.xz");
				}
				else {
					LOG.warn("Could not find {}/scandata.tar.xz after scan finished", reportPath);
				}
			}

			if (shouldStop()) {
				getScan().setScanResult(ScanResult.Stopped);
				getScan().setScanStatus(ScanStatuses.Stopped.toString());
				getScan().setUpdateStatus(true);
				getOutscanThread().signoffScan(getScan(), false);
			}
			else {
				saveReport(reportPath, true, true);

				if (this.runNormalScan) {
					getScan().setProbeId(null);
					getScan().setTarget(this.scanTarget);
					final ScanThread scan = new ScanThread(getOutscanThread(), getScan(), this);
					scan.setWasReportPath(StringUtils.isEmpty(this.localWasSchema) ? (reportPath + "/report") : this.localWasSchema);
					scan.runScanThread();
					return;
				}

				getScan().setScanResult(ScanResult.OK);
				getOutscanThread().signoffScan(getScan(), true);
			}
		}
		catch (final RuntimeException | IOException e) {
			LOG.error("Error occured during SCALE scan", e);
			transaction.captureException(e);
			getScan().setScanResult(ScanResult.Failed);
			getScan().setScanStatus(ScanStatuses.Stopped.toString());
			getScan().setUpdateStatus(true);
			getScan().setReason("An error occured during scan");
			getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			getOutscanThread().signoffScan(getScan(), false);
		}
		finally {
			getOutscanThread().updateRunningWasX(-1);
			transaction.end();
		}
	}

	/**
	 * Encrypts a file.
	 *
	 * @param reportPath Folder path to file.
	 * @param name File name.
	 */
	private void encryptFile(final String reportPath, final String name) {
		try {
			ServiceProvider.getEncryptionService().encrypt(EncryptionDecryptionRequest.builder()
					.input(Paths.get(reportPath + name))
					.output(Paths.get(reportPath + name + ".clvm"))
					.allowedService(Service.SUPPORT)
					.build());
			Files.delete(Paths.get(reportPath + name));
		}
		catch (final EncryptionException | IOException ex) {
			LOG.error("Couldn't encrypt file {}{} after Scale scan finished", reportPath, name);
		}
	}

	/**
	 * Writes element to xml.
	 *
	 * @param writer Xml writer
	 * @param name Name of element
	 * @param value Element value
	 */
	private void writeElement(final XMLStreamWriter writer, final String name, final Object value) throws XMLStreamException {
		writer.writeStartElement(name);
		writer.writeCharacters(value.toString());
		writer.writeEndElement();
	}

	/**
	 * Run ruleengine for scan.
	 *
	 * @param wasConnection A database connection to the sqlite file created by wasx report.
	 * @param reportPath Path to folder with scan issues and progress.
	 * @param runRules True to run rules for report
	 */
	private void runRuleEngine(final Connection wasConnection, final String reportPath, final boolean runRules) throws RuleException, IOException {
		final ReportDataList report = new ReportDataList();

		if (runRules) {
			try (final Connection conn = DbAccess.getInstance().getConnection();
					final ScanDataCollector collector = new WasXDataCollector(wasConnection, new DbLayerNativeStatementExecutor(conn), ProductInformation.class, PatchSupersedence.class, RuleDef.class, Rule.class)) {
				final Set<Long> ruleIds = new HashSet<>();
				RuleEngine.executeRules(collector, new RuleEngineListener(report, 1, false), ruleIds, false, false, false, false, false, null, false, false);
				addBaseFindings(report, collector, new HashMap<>(), getScan(), getAttackTarget());
			}
			catch (final Exception e) {
				throw new RuleException(e);
			}
		}

		addSystemInformation(report, null, getScan(), getOutscanThread(), null);

		if (getScan().getService() == ScanServiceType.AppsecScale) {
			addAssetIdentifiersFromLookup(report, getScan(), this.scanTarget);
		}

		final String filePath = StringUtils.isEmpty(this.localReportPath) ? reportPath : this.localReportPath;
		if (new File(filePath + "/issues.json").exists()) {
			report.getReportData()
					.add(ReportData.builder()
							.type(ReportEntryTypes.REPORT_SETUP.getId())
							.protocol(-1)
							.port("-1")
							.data(StringUtils.readTextFile(filePath + "/issues.json"))
							.host(getScan().getTarget())
							.userId(getScan().getUserId())
							.scheduleId(
									getScan().getScheduleId())
							.ruleId("WASISSUES")
							.build());
		}

		if (new File(filePath + "/progress.json").exists()) {
			report.getReportData()
					.add(ReportData.builder()
							.type(ReportEntryTypes.REPORT_SETUP.getId())
							.protocol(-1)
							.port("-1")
							.data(StringUtils.compress(StringUtils.readTextFile(filePath + "/progress.json")))
							.host(getScan().getTarget())
							.userId(getScan().getUserId())
							.scheduleId(
									getScan().getScheduleId())
							.ruleId("WASXPROGRESSLOG")
							.build());
		}

		if (new File(filePath + "/crawled-urls.txt.0").exists()) {
			report.getReportData()
					.add(ReportData.builder()
							.type(ReportEntryTypes.REPORT_SETUP.getId())
							.protocol(-1)
							.port("-1")
							.data(StringUtils.compress(StringUtils.readTextFile(filePath + "/crawled-urls.txt.0")))
							.host(getScan().getTarget())
							.userId(getScan().getUserId())
							.scheduleId(
									getScan().getScheduleId())
							.ruleId("WASXCRAWLEDURLS")
							.build());
		}

		try (final FileOutputStream out = new FileOutputStream(OutscanThread.getReportFile(getScan().getId(), false, false, false), false)) {
			try (final GZIPOutputStream gzip = new GZIPOutputStream(out)) {
				try (final Writer writer = new OutputStreamWriter(gzip, UTF_8)) {
					writer.write(new XmlMapper().writeValueAsString(report));
				}
			}
		}
	}

	/**
	 * Saves an XML report from the sqlite file created by wasx.
	 *
	 * @param reportPath Folder path to sqlite report.
	 * @param runRuleEngine True to run ruleengine
	 * @param copyFile True to copy file
	 */
	public void saveReport(final String reportPath, final boolean runRuleEngine, final boolean copyFile) {
		final XMLOutputFactory factory = XMLOutputFactory.newInstance();

		try (final Connection connection = DriverManager.getConnection(
				"jdbc:sqlite:" + (StringUtils.isEmpty(this.localWasSchema) ? (reportPath + "/report") : this.localWasSchema))) {
			if (copyFile && StringUtils.isEmpty(this.localWasSchema)) {
				FileUtils.copyFile(new File(reportPath + "/report"), new File(OutscanThread.getReportFile(getScan().getId(), false, false, true)));
			}
			if (runRuleEngine) {
				runRuleEngine(connection, reportPath, !this.runNormalScan);
			}
			try (final FileOutputStream fos = new FileOutputStream(OutscanThread.getReportFile(getScan().getId(), true, true, false))) {
				try (final GZIPOutputStream gz = new GZIPOutputStream(fos)) {
					final XMLStreamWriter writer = factory.createXMLStreamWriter(gz);
					writer.writeStartDocument();
					writer.writeStartElement(ReportingBusiness.ROWSET_TAG);
					writer.writeStartElement("REPORT");
					writeElement(writer, "TYPE", WasTypes.Info.getId());
					writeElement(writer, "KEY", "VERSION");
					writeElement(writer, "VALUE", "1.1");
					writer.writeEndElement();

					if (getScan().getScanTemplateId() != Template.AppsecScale.getId()) {
						final String urlFile = reportPath + "/crawled-urls.txt.0";
						if (new File(urlFile).exists()) {
							for (final String line : Files.readAllLines(Paths.get(urlFile))) {
								writer.writeStartElement("REPORT");
								writeElement(writer, "URL", line);
								writeElement(writer, "TYPE", WasTypes.Crawled.getId());
								writer.writeEndElement();
							}
						}
					}

					final List<String> paymentsPaths = SimpleWasDataCollector.getAuxDataValues(connection, Rules.PaymentsPage.getId(), "script");
					try {
						if (SimpleWasDataCollector.tableExists(connection, "findings")) {
							try (final Statement s = connection.createStatement()) {
								try (final ResultSet rs = s.executeQuery("SELECT * FROM findings")) {
									while (rs.next()) {
										final String url = rs.getString("url");
										final String method = rs.getString("method");
										final long vulnid = rs.getInt("finding_type_id");
										final String body = rs.getString("body");
										final String auxData = rs.getString("aux_data");
										final String match = rs.getString("match_context");
										final int wasFindingId = rs.getInt("id");
										writer.writeStartElement(ReportingBusiness.ROW_TAG);
										writeElement(writer, "URL", XmlUtils.cleanXML(url));
										writeElement(writer, "TYPE", WasTypes.Finding.getId());
										writeElement(writer, "ID", vulnid);
										writeElement(writer, "METHOD", method);
										writeElement(writer, "WASFINDINGID", wasFindingId);
										String content = (body != null ? body : "");
										if (content.length() > 8192) {
											content = content.substring(0, 8192);
										}
										writeElement(writer, "POST", XmlUtils.cleanXML(content));
										if (!StringUtils.isEmpty(auxData)) {
											final JSONArray array = new JSONArray(auxData);
											final XMLDoc xml = new XMLDoc(ReportingBusiness.ROWSET_TAG, ReportingBusiness.ROW_TAG);
											for (int i = 0; i < array.length(); i++) {
												final JSONArray a2 = array.getJSONArray(i);
												xml.setNewValueOf("KEY", a2.getString(0));
												xml.setValueOf("VALUE", a2.getString(1));
											}
											writeElement(writer, "AUXDATA", StringUtils.setEmpty(StringUtils.compress(xml.toString())));
										}
										if (match != null) {
											writeElement(writer, "MATCH", XmlUtils.replaceInvalidXmlCharacters(match));
											writeElement(writer, "MATCHSTART", rs.getInt("match_start"));
											writeElement(writer, "MATCHLENGTH", rs.getInt("match_length"));
										}

										if (!StringUtils.isEmpty(url) && paymentsPaths.contains(url)) {
											writeElement(writer, "SPECIALNOTES", SpecialNoteType.PAYMENT_PAGE.name());
										}

										writer.writeEndElement();
									}
								}
							}
						}
					}
					finally {
						writer.writeEndElement();
						writer.writeEndDocument();
						writer.close();
					}
				}
			}
		}
		catch (final SQLException | IOException | XMLStreamException | RuleException e) {
			LOG.error("Error generating wasx report", e);
			getScan().setScanResult(ScanResult.Failed);
			getScan().setScanStatus(ScanStatuses.Stopped.toString());
			getScan().setUpdateStatus(true);
			getScan().setReason("Could not generate was report");
			getScan().addIssue(IssueType.SCAN_FAILED, getScan().getReason());
			if (getOutscanThread() != null) {
				getOutscanThread().signoffScan(getScan(), false);
			}
		}
	}
}
