package com.chilicoders.boris;

import java.io.IOException;
import java.net.URL;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONArray;
import org.json.JSONException;

import com.chilicoders.model.Protocol;
import com.chilicoders.ruleengine.InstalledProductInfo;
import com.chilicoders.ruleengine.ProbeFact;
import com.chilicoders.ruleengine.ProductInformation;
import com.chilicoders.ruleengine.Rule;
import com.chilicoders.ruleengine.RuleDef;
import com.chilicoders.ruleengine.SQLiteCollector;
import com.chilicoders.ruleengine.db.PatchSupersedence;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public final class SimpleWasDataCollector extends SQLiteCollector {
	private Map<String, List<InstalledProductInfo>> productCache;

	private Map<String, List<String>> auxDataCache = new HashMap<>();

	private static final int MAX_ALLOWED_VERSION_LENGTH = 100;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection wasConnection;

	/**
	 * Create new SimpleWasDataCollector.
	 *
	 * @param conn Database connection
	 * @param scanSchema Name of scan schema
	 * @param databaseFile Database file for the normal scan.
	 * @param wasDbConnection Connection to was database
	 */
	public SimpleWasDataCollector(final Connection conn, final String scanSchema, final String databaseFile, final Connection wasDbConnection) throws SQLException {
		super(new DbLayerNativeStatementExecutor(conn), scanSchema, false, databaseFile, ProductInformation.class, PatchSupersedence.class, RuleDef.class, Rule.class,
				ProbeFact.class);
		this.wasConnection = wasDbConnection;
	}

	@Override
	public String getMachineClass() throws SQLException {
		return null;
	}

	@Override
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Map<String, List<InstalledProductInfo>> addProductsInstalled() throws SQLException, IOException {
		if (productCache != null) {
			return productCache;
		}
		final Map<String, List<InstalledProductInfo>> result = super.addProductsInstalled();
		if (tableExists(wasConnection, "components")) {
			try (final Statement s = wasConnection.createStatement()) {
				try (final ResultSet rs = s.executeQuery("SELECT * FROM components")) {
					while (rs.next()) {
						final String productName = rs.getString("name");
						final URL url = new URL(rs.getString("url"));
						final int port = url.getPort() >= 0 ? url.getPort() : url.getDefaultPort();
						final String version = rs.getString("version");
						addProduct(result, productName, Protocol.TCP, port, url.getHost(), url.getPath(),
								version != null && version.length() < MAX_ALLOWED_VERSION_LENGTH ? version : "", null, null, null, false, false, null, false, false,
								false,
								rs.getString("match_context"), rs.getInt("match_start"), rs.getInt("match_length"), rs.getString("url"), null, null, null, null);
					}
				}
			}
		}

		runProductChain(result);
		runBackportedProducts(result);

		productCache = result;
		return result;
	}

	@Override
	public List<String> getAuxDataValues(final long ruleId, final String key) {
		if (auxDataCache.containsKey(ruleId + key)) {
			return auxDataCache.get(ruleId + key);
		}
		auxDataCache.put(ruleId + key, getAuxDataValues(this.wasConnection, ruleId, key));
		return auxDataCache.get(ruleId + key);
	}

	/**
	 * Get values from aux data from findings.
	 *
	 * @param connection Connection to sqlite file
	 * @param ruleId Rule id for findings
	 * @param key Key to get values for
	 * @return List of values
	 */
	public static List<String> getAuxDataValues(final Connection connection, final long ruleId, final String key) {
		final Set<String> paths = new HashSet<>();

		try {
			if (tableExists(connection, "findings")) {
				try (final Statement statement = connection.createStatement()) {
					try (final ResultSet result = statement.executeQuery("SELECT * FROM findings WHERE finding_type_id = " + ruleId)) {
						while (result.next()) {
							final String url = result.getString("url");
							if (!StringUtils.isEmpty(url)) {
								paths.add(url);
							}
							final String auxData = result.getString("aux_data");
							if (!StringUtils.isEmpty(auxData)) {
								try {
									final JSONArray dataArray = new JSONArray(auxData);
									for (int i = 0; i < dataArray.length(); i++) {
										final JSONArray rowArray = dataArray.getJSONArray(i);
										if (key.equals(rowArray.getString(0))) {
											final String value = rowArray.getString(1);
											if (!StringUtils.isEmpty(value)) {
												paths.add(value);
											}
										}
									}
								}
								catch (final JSONException e) {
									continue;
								}
							}
						}
					}
				}
			}
		}
		catch (final SQLException e) {
			// Ignore
		}

		return Arrays.asList(paths.toArray(new String[0]));
	}
}
