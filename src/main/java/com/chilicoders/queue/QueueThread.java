package com.chilicoders.queue;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import javax.mail.Address;
import javax.mail.MessagingException;
import javax.mail.SendFailedException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import javax.net.ssl.HttpsURLConnection;
import javax.xml.bind.JAXBException;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.bl.AuditBusiness;
import com.chilicoders.bl.LoggingBusiness;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.integrations.impl.SplunkSenderImpl;
import com.chilicoders.core.reporting.api.model.Finding;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.Audit;
import com.chilicoders.db.objects.Splunk;
import com.chilicoders.integrations.SplunkSender;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.model.EventType;
import com.chilicoders.model.events.properties.EventFindingProperties;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.Syslog;
import com.chilicoders.util.SyslogException;
import com.chilicoders.util.thread.TimestampThread;
import com.chilicoders.util.xml.ParamList;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XmlUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.sun.mail.smtp.SMTPAddressFailedException;
import com.sun.mail.smtp.SMTPSendFailedException;

import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Transaction;

public class QueueThread extends TimestampThread {
	private static final int SMTP_MESSAGE_TO_SIZE_ERRORCODE = 552;

	/**
	 * Used for logging.
	 */
	private static final Logger LOG = LogManager.getLogger(QueueThread.class);

	/**
	 * A local copy of the queuehandler instance so we can tell it that we have died later on.
	 */
	private final QueueHandler queueHandler;

	/**
	 * the id of the thread.
	 */
	private final String threadid;

	/**
	 * message type of the thread.
	 */
	private final String threadType;

	/**
	 * instance of dbaccess.
	 */
	private final DbAccess dba;

	/**
	 * Max number of retries.
	 */
	private final int maxRetries;

	private static final Cache<Long, SplunkSenderImpl> splunkCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).maximumSize(100).build();
	private static final SplunkSenderImpl NO_SENDER = new SplunkSenderImpl(null, null);

	/**
	 * Constructor with params.
	 *
	 * @param qh A reference back to the initiator of this class in order to be able for that instance to keep track of how may threads that are alive.
	 * @param threadid What id this thread should have
	 * @param threadType Type of thread
	 * @param maxRetries Max number of retries for a queue entry.
	 */
	protected QueueThread(final QueueHandler qh, final String threadid, final String threadType, final int maxRetries) {
		super("QueueThread (" + threadid + ")", null, "QueueThread_" + threadid);
		// init some variables
		this.queueHandler = qh;
		this.threadid = threadid;
		this.threadType = threadType;
		this.maxRetries = maxRetries;
		this.dba = DbAccess.getInstance();
	}

	/**
	 * Send out an sms
	 *
	 * @param userId The user ID.
	 * @param data The message data.
	 * @return The tracking ID upon success, null otherwise.
	 */
	public static String sendSms(final long userId, final String data) {
		if (Configuration.getProperty(ConfigurationBooleanKey.dev_sysout_sms)) {
			System.out.println(data);
			return "";
		}
		final String retval;
		final XMLDoc sms = new XMLDoc(data, "TOC", "SMS");

		LOG.info("sendSms: Sending SMS to {} for user {}.", hideSmsDestinationNumber(sms.getValueOf("DESTINATION")), userId);
		try {
			if (StringUtils.isEmpty(data)) {
				LOG.warn("sendSms: SMS data empty");
				return null;
			}

			final int size = StringUtils.isEmpty(sms.getValueOf("TEXT")) ? 0 : sms.getValueOf("TEXT").length();
			final int sizelimit = Configuration.getProperty(ConfigurationIntKey.sms_maxmessagesize);
			if (size > sizelimit) {
				LOG.warn("sendSms: SMS too large: " + size + "/" + sizelimit);
				return "";
			}

			retval = sendSpiriusSms(sms.getValueOf("DESTINATION"), Long.toString(userId), sms.getValueOf("TEXT"));

			LOG.debug("sendSms: Sent SMS to {} for user {} with the return value {}.", hideSmsDestinationNumber(sms.getValueOf("DESTINATION")), userId, retval);
		}
		catch (final Exception e) {
			LOG.error("sendSms: ", e);
			return null;
		}

		if (retval != null) {
			return retval;
		}
		LOG.error("sendSms: Something went wrong sending SMS to {}.", hideSmsDestinationNumber(sms.getValueOf("DESTINATION")));

		return null;
	}

	/**
	 * Send an SMS using the Spirius POST interface.
	 *
	 * @param to Number to send to (MSISDN international format).
	 * @param extId External ID to set.
	 * @param message The message to send.
	 * @return Response ID from Spirius.
	 */
	private static String sendSpiriusSms(final String to, final String extId, final String message) throws IOException {
		final JSONObject request = new JSONObject();
		request.put("User", Configuration.getProperty(ConfigurationKey.sms_uid));
		request.put("Pass", Configuration.getProperty(ConfigurationKey.sms_pwd));
		request.put("To", to);
		// Changing "From" setting from "Outpost24" require a lot of preparations since some telephone operators require this to be pre-registered. Contact NSOC before changing this.
		request.put("From", "Outpost24");
		request.put("FromType", "A");
		request.put("Msg", message);
		request.put("ExtId", extId);

		final URL url = new URL(Configuration.getProperty(ConfigurationKey.sms_gateway));
		final HttpsURLConnection connection = SslUtils.getTrustAllHttpsConnection(url);
		connection.setRequestMethod("POST");
		connection.setDoOutput(true);
		connection.setDoInput(true);
		connection.addRequestProperty("Content-Type", "application/json;charset=UTF-8");
		connection.getOutputStream().write(request.toString().getBytes(StandardCharsets.UTF_8));

		final int responseCode = connection.getResponseCode();
		final String responseString = IOUtils.toString(connection.getInputStream(), StandardCharsets.UTF_8);

		connection.disconnect();

		if (responseCode < HttpStatus.SC_OK || responseCode >= HttpStatus.SC_BAD_REQUEST) {
			request.put("To", hideSmsDestinationNumber(to));
			LOG.error("Got response code: " + responseCode + ", URL: " + url + ", request: " + request + ", response: " + responseString);
			return null;
		}

		return responseString.split(",")[1].trim();
	}

	/**
	 * Hide the destination number except the first 4 and last 2 characters
	 *
	 * @param destination Destination number
	 * @return partially hidden destination number
	 */
	private static String hideSmsDestinationNumber(final String destination) {
		return destination.replaceAll("(?<!^.{0,3}).(?=.{2})", "*");
	}

	/**
	 * Send out email.
	 *
	 * @param session SMTP session.
	 * @param msg Email message.
	 * @param queueXid Queue id.
	 * @return True upon success.
	 */
	public static String sendEmail(final Session session, final String msg, final long queueXid) {
		try {
			// Create a MimeMessage from the string msg
			final javax.mail.Message mmsg = new MimeMessage(session, new java.io.ByteArrayInputStream(msg.getBytes(XMLAPI.charset))) {
				@Override
				protected void updateMessageID() throws MessagingException {
					setHeader("Message-ID", queueXid + "_" + System.currentTimeMillis());
				}

				@Override
				// Needed to prevent Spotbugs warning
				protected final void finalize() {
					// Do nothing
				}
			};

			Transport.send(mmsg);
			if (!Configuration.getProperty(ConfigurationBooleanKey.queuehandler_cleanemails)) {
				return "Success";
			}

			if (mmsg.getContent() instanceof MimeMultipart) {
				return IOUtils.toString(MimeUtility.decode(((MimeMultipart) (mmsg.getContent())).getBodyPart(0).getInputStream(), "quoted-printable"), StandardCharsets.UTF_8);
			}
			return "Could not get message: " + mmsg.getContent().getClass().getName();
		}
		catch (final IOException e) {
			return "Could not get message: " + e.getMessage();
		}
		catch (final MessagingException mex) {
			if (!(mex.getCause() instanceof SMTPAddressFailedException)) {
				int returnCode = 0;
				if (mex instanceof SMTPSendFailedException) {
					returnCode = ((SMTPSendFailedException) mex).getReturnCode();
				}
				else if (mex.getCause() instanceof SMTPSendFailedException) {
					returnCode = ((SMTPSendFailedException) mex.getCause()).getReturnCode();
				}
				LOG.log(returnCode == SMTP_MESSAGE_TO_SIZE_ERRORCODE ? Level.INFO : Level.ERROR, "Exception handling in send mail, error code: " + returnCode, mex);
			}

			Exception ex = mex;
			do {
				if (ex instanceof SendFailedException) {
					final SendFailedException sfex = (SendFailedException) ex;
					final Address[] invalid = sfex.getInvalidAddresses();
					if (invalid != null) {
						LOG.info("    ** Invalid Addresses");
						for (final Address address : invalid) {
							LOG.info("         " + address);
						}
					}
					final Address[] validUnsent = sfex.getValidUnsentAddresses();
					if (validUnsent != null) {
						LOG.info("    ** ValidUnsent Addresses");
						for (final Address address : validUnsent) {
							LOG.info("         " + address);
						}
					}
					final Address[] validSent = sfex.getValidSentAddresses();
					if (validSent != null) {
						LOG.info("    ** ValidSent Addresses");
						for (final Address address : validSent) {
							LOG.info("         " + address);
						}
					}
				}

				if (ex instanceof MessagingException) {
					ex = ((MessagingException) ex).getNextException();
				}
				else {
					ex = null;
				}
			}
			while (ex != null);
		}

		return null;
	}

	/**
	 * Sends out a log event.
	 *
	 * @param userId The user for which to look for event listener entries.
	 * @param data The data that forms the request.
	 * @param vctype The type of log event to send.
	 * @param eventId Id of event.
	 * @return <code>true</code> upon success, <code>false</code> otherwise.
	 */
	@SuppressWarnings("unchecked")
	private boolean sendLogging(final long userId, final String data, final String vctype, final long eventId) {
		try (final Connection conn = dba.getConnection()) {
			final LoggingBusiness lb = new LoggingBusiness();
			final HashMap<String, Object> params = XmlUtils.getParams(new ParamList(data));
			if ("Logging".equals(vctype)) {
				lb.dosendLogging(conn, userId, (HashMap<String, String>) params.get("HM"), O24Event.get(((Integer) params.get("REF")).intValue()),
						(String) params.get("TARGETID"), (String) params.get("IPADDRESS"),
						Boolean.valueOf((String) params.get("EMAIL")), Boolean.valueOf((String) params.get("SMS")), Boolean.valueOf((String) params.get("SMS")),
						Boolean.valueOf((String) params.get("SNMP")),
						Boolean.valueOf((String) params.get("SPLUNK")), EventType.get((Integer) params.get("TYPE")), eventId);
			}
			else if ("LoggingFindings".equals(vctype)) {
				final EventFindingProperties properties = MarshallingUtils.unmarshal(EventFindingProperties.class, (String) params.get("PROPERTIES"));
				final long subuserId = StringUtils.getLongValue((String) params.get("SUBUSERID"));
				lb.dosendLoggingFindings(conn, userId, (HashMap<Finding.RiskLevel, ArrayList<String>>) params.get("FINDINGS"), (String) params.get("TARGETID"),
						EventType.get((Integer) params.get("TYPE")), eventId, properties, subuserId);
			}
			else if ("LoggingList".equals(vctype)) {
				try {
					try (final ObjectInputStream ois = new ObjectInputStream(
							new ByteArrayInputStream((Base64.decodeBase64(((String) params.get("LISTS")).getBytes(XMLAPI.charset)))))) {
						final HashMap<String, HashMap<String, HashMap<String, String>>> lists = (HashMap<String, HashMap<String, HashMap<String, String>>>) ois.readObject();
						lb.dosendLoggingList(conn, userId, O24Event.get(((Integer) params.get("REF")).intValue()), (HashMap<String, String>) params.get("HM"), lists,
								EventType.get((Integer) params.get("TYPE")), eventId);
					}
				}
				catch (final IOException | ClassNotFoundException e) {
					LOG.error("Error sending logging", e);
					return false;
				}
			}
			conn.commit();
		}
		catch (final SQLException | JAXBException e) {
			LOG.error("Error sending logging", e);
			return false;
		}
		return true;
	}

	/**
	 * Sends out a syslog message.
	 *
	 * @param conn Database connection
	 * @param userId User id.
	 * @param data Data in the audit log entry.
	 * @param vctype Type of entry.
	 * @param syslog True to send to syslog.
	 * @return <code>true</code> upon success, <code>false</code> otherwise.
	 */
	public static boolean sendAuditlog(final Connection conn, final long userId, final String data, final String vctype, final boolean syslog) {
		try {
			final int priority = Syslog.LOG_INFO;
			final String prefix = Configuration.getProperty(ConfigurationKey.hiab_syslog_prefix);

			final ArrayList<String> messages;
			// List of messages to send
			if (syslog && Syslog.getInstance(conn, userId).isArcsightEnabled()) {
				messages = buildArcsightAuditlog(conn, userId, data, vctype);
			}
			else {
				messages = buildSyslogAuditlog(conn, userId, data, vctype);
			}

			try {
				if (syslog) {
					for (final String msg : messages) {
						Syslog.getInstance(conn, userId).syslog(conn, userId, priority, (StringUtils.isEmpty(prefix) ? "" : prefix + " ") + msg.replaceAll("\n", " "));
					}
				}
				else {
					final SplunkSender sender = splunkCache.get(userId, () -> {
						final Splunk splunk = Splunk.getById(Splunk.class, conn, Access.ADMIN, userId);
						if (splunk != null && splunk.isSplunkEnabled()) {
							return new SplunkSenderImpl(Configuration.getConfigService(), splunk);
						}
						return NO_SENDER;
					});
					if (sender != NO_SENDER) {
						for (final String msg : messages) {
							sender.send(msg);
						}
					}
				}
			}
			catch (final RuntimeException | SyslogException | ExecutionException | JsonProcessingException e) {
				LOG.info("Could not send out audit msg to " + (syslog ? "syslog" : "splunk"), e);
			}
		}
		catch (final SQLException e) {
			LOG.error("Error sending audit msg to " + (syslog ? "syslog" : "splunk"), e);
			return false;
		}

		return true;
	}

	/**
	 * Build a syslog entry.
	 *
	 * @param conn A database connection.
	 * @param userId User id.
	 * @param log Log entry text.
	 * @param vctype Type of audit entry.
	 * @return List of text messages.
	 */
	private static ArrayList<String> buildSyslogAuditlog(final Connection conn, final long userId, final String log, final String vctype) throws SQLException {
		final AuditBusiness ab = new AuditBusiness();

		// List of messages to send
		final ArrayList<String> messages = new ArrayList<>();

		String data = log;

		if ("Audit".equals(vctype)) {
			// List of audit xids to send
			final List<Audit> auditList = Audit.fetchObjects(Audit.class, conn, Access.ADMIN, "xuserxid=? AND xid = ANY(?)", userId, (Object[]) DbHelper.createIdArray(data));
			for (final Audit audit : auditList) {
				final StringBuilder msg = new StringBuilder();
				msg.append(ab.getDataTypeString(audit.getApp(), "en").toUpperCase()).append(" - ").append(ab.getModeString(audit.getMode(), "en").toUpperCase());
				if (!StringUtils.isEmpty(audit.getName())) {
					msg.append(": ").append(audit.getName());
				}
				msg.append(" - USER: ").append(audit.getFirstName()).append(" ").append(audit.getLastName());
				msg.append(" - TIME: ").append(audit.getTime());

				data = StringUtils.setEmpty(audit.getCustom(), "");
				if (data.length() > 0
						&& (AppName.TARGETGROUP.getDbName().equals(audit.getApp()) || AppName.REPORTING.getDbName().equals(audit.getApp()))
						&& 1 == audit.getMode()) {
					// Split list of targets and send each target as new message
					final String[] parts = data.split(":", 2);
					final String text = parts[0];
					final String[] targets = (parts.length == 2 ? parts[1] : "").split(",");
					for (final String target : targets) {
						final StringBuilder newmsg = new StringBuilder(msg.toString());
						if (!StringUtils.isEmpty(text) || !StringUtils.isEmpty(target.trim())) {
							newmsg.append(" - ");
						}
						newmsg.append(text).append(!StringUtils.isEmpty(target.trim()) ? ": " : "");
						newmsg.append(target.trim());
						messages.add(newmsg.toString());
					}
				}
				else {
					if ((msg.length() + data.length()) > 1021) {
						data = data.substring(0, 1021 - msg.length());
					}
					if (!StringUtils.isEmpty(data)) {
						msg.append(" - ").append(data);
					}
					messages.add(msg.toString());
				}
			}
		}
		else {
			messages.add(data);
		}
		return messages;
	}

	/**
	 * Build a arcsight entry.
	 *
	 * @param conn A database connection.
	 * @param userId User id.
	 * @param log Log entry text.
	 * @param vctype Type of audit entry.
	 * @return List of text messages.
	 */
	private static ArrayList<String> buildArcsightAuditlog(final Connection conn, final long userId, final String log, final String vctype) throws SQLException {
		final AuditBusiness ab = new AuditBusiness();

		// List of messages to send
		final ArrayList<String> messages = new ArrayList<>();

		String data = log;

		if ("Audit".equals(vctype)) {
			// List of audit xids to send
			final List<Audit> auditList = Audit.fetchObjects(Audit.class, conn, Access.ADMIN, "xuserxid=? AND xid = ANY(?)", userId, (Object[]) DbHelper.createIdArray(data));
			for (final Audit audit : auditList) {
				final StringBuilder msg = new StringBuilder();
				String message = "";
				message += "CEF:0|Outpost24|" + Syslog.getInstance(conn, userId).getIdent() + "|" + Configuration.getProperty(ConfigurationKey.TAG, "Version Unknown")
						.replaceAll("\\|", "\\\\|") + "|"; // Version, Device Vendor, Device Product, Device Version
				message += ab.getDataTypeString(audit.getApp(), "en").replaceAll("\\|", "\\\\|").replace(" ", "") + ab.getModeString(audit.getMode(), "en")
						.replaceAll("\\|", "\\\\|")
						.replace(" ", "") + "|"; // Signature ID
				message += ab.getDataTypeString(audit.getApp(), "en").replaceAll("\\|", "\\\\|") + (" - ") + ab.getModeString(audit.getMode(), "en"); // Name
				message += "|0|"; // Severity

				msg.append(message.replaceAll("\r", ""));

				message = "";
				message += "User: " + audit.getFirstName() + " " + audit.getLastName() + " ";

				final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sdf.applyPattern("MMM dd yyyy HH:mm:ss");
				message += " Time: " + sdf.format(audit.getTime()).substring(0, 1).toUpperCase() + sdf.format(audit.getTime()).substring(1);
				msg.append("msg=").append(message.replaceAll("\\=", "\\\\="));

				message = "";
				if (!StringUtils.isEmpty(audit.getName())) {
					message = audit.getName();
				}

				data = StringUtils.setEmpty(audit.getCustom(), "");
				if (data.length() > 0
						&& (AppName.TARGETGROUP.getDbName().equals(audit.getApp()) || AppName.REPORTING.getDbName().equals(audit.getApp()))
						&& 1 == audit.getMode()) {
					// Split list of targets and send each target as new message
					final String[] parts = data.split(":", 2);
					final String text = parts[0];
					final String[] targets = (parts.length == 2 ? parts[1] : "").split(",");
					for (final String target : targets) {
						final StringBuilder newmsg = new StringBuilder(msg.toString());
						if (!StringUtils.isEmpty(text) || !StringUtils.isEmpty(target.trim())) {
							newmsg.append(" - ");
						}
						newmsg.append(text).append(!StringUtils.isEmpty(target.trim()) ? ": " : "");
						newmsg.append(target.trim());
						messages.add(newmsg.toString().replaceAll("\\=", "\\\\="));
					}
				}
				else {
					if ((msg.length() + data.length()) > 1021) {
						data = data.substring(0, 1021 - msg.length());
					}
					if (!StringUtils.isEmpty(data)) {
						msg.append(" Data: ").append(data).append(message);
					}
					else if (!message.equals("")) {
						msg.append(" Data: ").append(message);
					}
					messages.add(msg.toString());
				}
			}
		}
		else {
			messages.add(data);
		}
		return messages;
	}

	@Override
	public void run() {
		try (final Connection conn = dba.getConnection()) {
			tellRunning();

			Transport transport = null;

			try (final PreparedStatement stmt = conn.prepareStatement(
					"SELECT xid, xuserxid, xthreadid, itype, binvalid, dcreated, dlastcheck, iretries, vctype, cdata FROM tqueues WHERE xthreadid=? FOR UPDATE SKIP LOCKED LIMIT 1",
					ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_UPDATABLE)) {
				stmt.setString(1, threadType);
				boolean keepGoing;
				do {
					keepGoing = false;
					try (final ResultSet rs = stmt.executeQuery()) {
						while (rs.next()) {
							String trackingid = null;
							boolean status = false;
							String emailText = null;
							keepGoing = true;

							final Transaction transaction = ElasticApm.startTransaction();
							try {
								transaction.setType(Transaction.TYPE_REQUEST);
								transaction.setLabel("user", rs.getString("XUSERXID"));

								switch (com.chilicoders.core.events.model.EventType.fromValue(rs.getInt("ITYPE"))) {
									case EMAIL:
										transaction.setName("QueueThread#sendEmail");

										final Session session = queueHandler.getSession();

										if (transport == null) {
											transport = session.getTransport("smtp");
											transport.connect();
										}

										emailText = sendEmail(session, rs.getString("CDATA"), rs.getLong("XID"));
										status = emailText != null;
										break;
									case SMS:
										transaction.setName("QueueThread#sendSms");

										trackingid = sendSms(StringUtils.getLongValue(rs.getString("XUSERXID")), rs.getString("CDATA"));
										if (trackingid != null) {
											status = true;
										}
										break;
									case LOG:
										transaction.setName("QueueThread#sendLogging");
										status = sendLogging(StringUtils.getLongValue(rs.getString("XUSERXID")), rs.getString("CDATA"), rs.getString("VCTYPE"),
												rs.getLong("XID"));
										break;
									case SPLUNK:
									case SYSLOG:
										transaction.setName("QueueThread#sendAuditlog");
										status = sendAuditlog(conn, StringUtils.getLongValue(rs.getString("XUSERXID")), rs.getString("CDATA"), rs.getString("VCTYPE"),
												"7".equals(rs.getString("ITYPE")));
										break;
									default:
										throw new IllegalStateException("Invalid event type: " + rs.getInt("ITYPE"));
								}
							}
							catch (final RuntimeException e) {
								transaction.captureException(e);
								throw e;
							}
							finally {
								transaction.end();
							}

							if (status) {
								if (com.chilicoders.core.events.model.EventType.fromValue(rs.getInt("ITYPE")) != com.chilicoders.core.events.model.EventType.LOG) {
									if (emailText != null && Configuration.getProperty(ConfigurationBooleanKey.queuehandler_cleanemails)) {
										DbObject.executeUpdate(conn,
												"INSERT INTO tqueuelogs (xid, xuserxid, subuserid, xthreadid, itype, binvalid, dcreated, dlastcheck, iretries, vctype, cdata, dsent, trackingid, event, parentid) "
														+ "SELECT xid, xuserxid, subuserid, xthreadid, itype, binvalid, dcreated, dlastcheck, iretries, vctype, ?, CURRENT_TIMESTAMP AS dsent, ? AS trackingid, event, parentid FROM tqueues WHERE xid=?",
												emailText, StringUtils.setEmpty(trackingid, ""), rs.getLong("XID"));
									}
									else {
										DbObject.executeUpdate(conn,
												"INSERT INTO tqueuelogs (xid, xuserxid, subuserid, xthreadid, itype, binvalid, dcreated, dlastcheck, iretries, vctype, cdata, dsent, trackingid, event, parentid) "
														+ "SELECT xid, xuserxid, subuserid, xthreadid, itype, binvalid, dcreated, dlastcheck, iretries, vctype, cdata, CURRENT_TIMESTAMP AS dsent, ? AS trackingid, event, parentid FROM tqueues WHERE xid=?",
												StringUtils.setEmpty(trackingid, ""), rs.getLong("XID"));
									}
								}
								DbObject.executeUpdate(conn, "DELETE FROM tqueues WHERE xid=?", rs.getLong("XID"));
								rs.deleteRow();
							}
							else {
								rs.updateNull("XTHREADID");

								final int retries = rs.getInt("IRETRIES") + 1;
								rs.updateInt("IRETRIES", retries);

								if (retries == this.maxRetries) {
									rs.updateInt("BINVALID", 1);
								}

								rs.updateRow();
							}

							conn.commit();
						}
					}
				}
				while (keepGoing);

				if (transport != null && transport.isConnected()) {
					transport.close();
				}

				// tell the caller that we are done...
				this.queueHandler.signoffThread(this.threadid);
			}
		}
		catch (final RuntimeException | MessagingException | SQLException e) {
			LOG.error("in run():", e);
			try {
				this.queueHandler.signoffThread(this.threadid);    // try to shutdown this thread if it dies, so a new one can be started
			}
			catch (final Exception e2) {
				LOG.error("in run() sign-off : ", e2);
			}
		}

		tellStopped();
	}

}
