package com.chilicoders.db;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
public @interface DbChild {
	/**
	 * @return Whether to delete child objects before saving parent.
	 */
	boolean deleteOnSave() default true;

	/**
	 * @return Name of the parent id column.
	 */
	String parentLink();

	/**
	 * @return Used for filtering
	 */
	String xmlField();

	/**
	 * @return Xml row name
	 */
	String row();

	/**
	 * @return Xml row set name
	 */
	String rowset();
}
