package com.chilicoders.db.query;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.Instant;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Supplier;

import javax.persistence.Column;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.Enumerated;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.message.MapMessage;
import org.hibernate.ScrollMode;
import org.hibernate.Session;
import org.hibernate.TypeHelper;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.hibernate.type.CustomType;
import org.hibernate.type.EnumType;
import org.hibernate.type.InstantType;
import org.hibernate.type.SingleColumnType;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.Type;
import org.hibernate.type.UUIDCharType;
import org.hibernate.type.spi.TypeConfiguration;
import org.hibernate.usertype.UserType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.db.AssetIdentifierArrayType;
import com.chilicoders.db.BigintArrayUserType;
import com.chilicoders.db.DateTimeUserType;
import com.chilicoders.db.IntegerArrayType;
import com.chilicoders.db.IntegerArrayUserType;
import com.chilicoders.db.LongArrayType;
import com.chilicoders.db.NullValueWithType;
import com.chilicoders.db.PostgreSQLEnumType;
import com.chilicoders.db.ShortArrayType;
import com.chilicoders.db.SourceArrayType;
import com.chilicoders.db.StringArrayType;
import com.chilicoders.db.StringArrayUserType;
import com.chilicoders.db.objects.api.DataSetInterface;
import com.chilicoders.db.transformers.DtoResultTransformers;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.Source;
import com.chilicoders.util.StringUtils;

import com.vladmihalcea.hibernate.type.array.EnumArrayType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Component
public class JpaNativeStatementExecutor implements NativeStatementExecutor {

	private static final Logger LOG = LogManager.getLogger(JpaNativeStatementExecutor.class);

	private EntityManagerFactory entityManagerFactory;
	private final long maxQueryTime;
	@Getter
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private EntityManager entityManager;

	/**
	 * Default non-transactional statement executor.
	 *
	 * @param entityManagerFactory an entity manager factory
	 * @param configService configuration service
	 */
	@Autowired
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public JpaNativeStatementExecutor(final EntityManagerFactory entityManagerFactory, final ConfigurationService configService) {
		this.entityManagerFactory = entityManagerFactory;
		maxQueryTime = configService.getProperty(ConfigurationIntKey.db_slow_sql_warning) * 1000L;
	}

	/**
	 * Use this constructor when you want to inject an entity manager, for example a persistence context.
	 *
	 * @param entityManager the entity manager
	 * @param configService a configuration service
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public JpaNativeStatementExecutor(final EntityManager entityManager, final ConfigurationService configService) {
		this.entityManager = entityManager;
		maxQueryTime = configService.getProperty(ConfigurationIntKey.db_slow_sql_warning) * 1000L;
	}

	@SuppressFBWarnings("EI_EXPOSE_REP")
	public EntityManagerFactory getEntityManagerFactory() {
		return entityManagerFactory;
	}

	/**
	 * Execute an sql statement and the result is a mapped entity class.
	 *
	 * @param sqlStatement an sql statement to execute.
	 * @param clazz Class to create result objects from.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	public <T> List<T> execute(final NativeSqlStatement sqlStatement, final Class<T> clazz) {
		return executeWithEntityManager(em -> execute(em, sqlStatement, clazz));
	}

	/**
	 * Execute an sql statement and the result is a mapped entity class.
	 *
	 * @param em an entity manager.
	 * @param sqlStatement an sql statement to execute.
	 * @param clazz Class to create result objects from.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	@SuppressWarnings("unchecked")
	public <T> List<T> execute(final EntityManager em, final NativeSqlStatement sqlStatement, final Class<T> clazz) {
		return (List<T>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, sqlStatement);
			return query.getKey().addEntity(clazz).list();
		}, sqlStatement);
	}

	/**
	 * Execute an sql statement and the result is based on the specified mapper.
	 *
	 * @param sqlStatement an sql statement to execute.
	 * @param resultMapper A result mapper name.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	public <T> List<T> execute(final NativeSqlStatement sqlStatement, final String resultMapper) {
		return executeWithEntityManager(em -> execute(em, sqlStatement, resultMapper));
	}

	/**
	 * Execute an sql statement and the result is based on the specified mapper.
	 *
	 * @param em an entity manager.
	 * @param sqlStatement an sql statement to execute.
	 * @param resultMapper A result mapper name.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	@SuppressWarnings("unchecked")
	public <T> List<T> execute(final EntityManager em, final NativeSqlStatement sqlStatement, final String resultMapper) {
		return (List<T>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, sqlStatement);
			if (resultMapper != null) {
				query.getKey().setResultSetMapping(resultMapper);
			}
			return query.getKey().list();
		}, sqlStatement);
	}

	/**
	 * Execute an sql statement and the result is a scalar.
	 *
	 * @param sqlStatement an sql statement to execute.
	 * @param scalarColumnAlias Column alias for the scalar to be retrieved.
	 * @param scalarType Primitive type.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	public <T> List<T> execute(final NativeSqlStatement sqlStatement, final String scalarColumnAlias, final SingleColumnType<T> scalarType) {
		return executeWithEntityManager(em -> execute(em, sqlStatement, scalarColumnAlias, scalarType));
	}

	/**
	 * Execute an sql statement and the result is a scalar.
	 *
	 * @param em an entity manager.
	 * @param sqlStatement an sql statement to execute.
	 * @param scalarColumnAlias Column alias for the scalar to be retrieved.
	 * @param scalarType Primitive type.
	 * @param <T> The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	@SuppressWarnings("unchecked")
	public <T> List<T> execute(final EntityManager em, final NativeSqlStatement sqlStatement, final String scalarColumnAlias, final SingleColumnType<T> scalarType) {
		return (List<T>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, sqlStatement);
			return query.getKey().addScalar(scalarColumnAlias, scalarType).list();
		}, sqlStatement);
	}

	/**
	 * Execute an sql statement and the result is a scalar.
	 *
	 * @param sqlStatement an sql statement to execute.
	 */
	@Override
	public int execute(final NativeSqlStatement sqlStatement) {
		return executeWithNewEntityManager(em -> execute(em, sqlStatement));
	}

	/**
	 * Executes an update statement.
	 *
	 * @param em an entity manager
	 * @param sqlStatement the sql statement
	 * @return Number of rows updated/deleted
	 */
	public int execute(final EntityManager em, final NativeSqlStatement sqlStatement) {
		return executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecution(em, sqlStatement);
			return query.getKey().executeUpdate();
		}, sqlStatement);
	}

	/**
	 * Execute an sql statement and the result is based on the specified DTO class. Use "addAllScalars" parameter true when custom types are
	 * used in the DTO (or the query) that are not recognized automatically by the Dialect.
	 *
	 * @param sqlStatement an sql statement to execute.
	 * @param <T> The class that the result should be mapped to.
	 * @param dtoClass The class that the result should be mapped to.
	 * @return A list of objects.
	 */
	public <T> List<T> executeDTO(final NativeSqlStatement sqlStatement, final Class<T> dtoClass) {
		return executeDTO(sqlStatement, dtoClass, false);
	}

	/**
	 * Execute an sql statement and the result is based on the specified DTO class. Use "addAllScalars" parameter true when custom types are
	 * used in the DTO (or the query) that are not recognized automatically by the Dialect.
	 *
	 * @param sqlStatement an sql statement to execute.
	 * @param <T> The class that the result should be mapped to.
	 * @param dtoClass The class that the result should be mapped to.
	 * @param addAllScalars - true if all column annotated with @Column in the DTO should be added as scalars to the query. You would need to do
	 *     this if the query involves a db type that is unknown to the Hibernate dialect (for ex. PG inet), in order to register its type.
	 * @return A list of objects.
	 */
	public <T> List<T> executeDTO(final NativeSqlStatement sqlStatement, final Class<T> dtoClass, final boolean addAllScalars) {
		return executeWithEntityManager(em -> executeDTO(em, sqlStatement, dtoClass, addAllScalars));
	}

	/**
	 * Execute an sql statement and the result is based on the specified mapper.
	 *
	 * @param em an entity manager.
	 * @param sqlStatement an sql statement to execute.
	 * @param <T> The class that the result should be mapped to.
	 * @param dtoClass The class that the result should be mapped to.
	 * @param addAllScalars - true if all column annotated with @Column in the DTO should be added as scalars to the query. You would need to do
	 *     this if the query involves a db type that is unknown to the Hibernate dialect (for ex. PG inet), in order to register its type.
	 * @return A list of objects.
	 */
	@SuppressWarnings({"unchecked", "deprecation"})
	public <T> List<T> executeDTO(final EntityManager em, final NativeSqlStatement sqlStatement, final Class<T> dtoClass, final boolean addAllScalars) {
		return (List<T>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, sqlStatement);
			query.getKey().setResultTransformer(DtoResultTransformers.getTransformer(dtoClass));
			if (addAllScalars) {
				addScalarsToQuery(dtoClass, query.getKey(), query.getValue());
			}
			return query.getKey().list();
		}, sqlStatement);
	}

	/**
	 * Adds all of the @Column annotated fields as scalar to the query. This is useful in case the query involves a db type that is unknown to
	 * the Hibernate dialect (for ex. PG inet), in order to register its type.
	 *
	 * @param <T> the resulting class
	 * @param dtoClass the resulting class
	 * @param query the hibernate SQL query
	 * @param typeHelper typehelper for hibernate
	 */
	private <T> void addScalarsToQuery(final Class<T> dtoClass, final NativeQuery<?> query, final TypeHelper typeHelper) {
		final Field[] classFields = dtoClass.getDeclaredFields();
		for (final Field field : classFields) {
			final Column columnInfo = field.getAnnotation(Column.class);
			if (columnInfo != null) {
				final String alias = StringUtils.setEmpty(columnInfo.name(), field.getName());
				if (Long.class.equals(field.getType()) || "long".equals(field.getType().getName())) {
					query.addScalar(alias, StandardBasicTypes.LONG);
				}
				else if (Instant.class.equals(field.getType())) {
					query.addScalar(alias, new InstantType());
				}
				else if ((Set.class.equals(field.getType()) && String.class.equals(getGenericType(field))) || String[].class.equals(field.getType())) {
					query.addScalar(alias, new StringArrayType());
				}
				else if (Set.class.equals(field.getType()) && Long.class.equals(getGenericType(field))) {
					query.addScalar(alias, new LongArrayType());
				}
				else if (Integer[].class.equals(field.getType())) {
					query.addScalar(alias, new IntegerArrayType());
				}
				else if (Short[].class.equals(field.getType())) {
					query.addScalar(alias, new ShortArrayType());
				}
				else if (Enum[].class.isAssignableFrom(field.getType())) {
					query.addScalar(alias, new EnumArrayType(field.getType(), EnumArrayType.SQL_ARRAY_TYPE));
				}
				else if (UUID.class.equals(field.getType())) {
					query.addScalar(alias, new UUIDCharType());
				}
				else if (field.getType().isEnum()) {
					final Properties params = new Properties();
					params.put("enumClass", field.getType().getName());
					final Enumerated annotation = field.getAnnotation(Enumerated.class);
					if (annotation != null && annotation.value() == javax.persistence.EnumType.ORDINAL) {
						params.put("type", "" + Types.INTEGER);
					}
					else {
						params.put("type", "" + Types.VARCHAR);
					}
					query.addScalar(alias, typeHelper.custom(EnumType.class, params));
				}
				else {
					if (String.class.equals(field.getType()) && field.getAnnotation(org.hibernate.annotations.Type.class) != null) {
						final org.hibernate.annotations.Type type = field.getAnnotation(org.hibernate.annotations.Type.class);
						if (type.type().equals("json")) {
							query.addScalar(alias, new JsonBinaryType(String.class));
						}
						else {
							query.addScalar(alias);
						}
					}
					else {
						query.addScalar(alias);
					}
				}
			}
		}
	}

	/**
	 * Gets the generic type argument for a field.
	 *
	 * @param field the field
	 * @return the first generic type argument, if any, or null.
	 */
	private java.lang.reflect.Type getGenericType(final Field field) {
		final ParameterizedType genericType = (ParameterizedType) field.getGenericType();
		final java.lang.reflect.Type[] actualTypeArguments = genericType.getActualTypeArguments();
		if (actualTypeArguments.length > 0) {
			return actualTypeArguments[0];
		}
		return null;
	}

	/**
	 * Execute an sql to obtain a single value.
	 *
	 * @param sqlStatement the sql statement to execute
	 * @return The query result.
	 */
	public Object executeSingleResult(final NativeSqlStatement sqlStatement) {
		return executeWithEntityManager(em -> executeSingleResult(em, sqlStatement));
	}

	/**
	 * Execute an sql to obtain a single value.
	 *
	 * @param em An entity manager.
	 * @param sqlStatement an sql statement
	 * @return the query result
	 */
	public Object executeSingleResult(final EntityManager em, final NativeSqlStatement sqlStatement) {
		return executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, sqlStatement);
			return query.getKey().uniqueResult();
		}, sqlStatement);
	}

	/**
	 * Gets the result from a count query.
	 *
	 * @param sqlStatement the count sql statement.
	 * @return the result of the count, as a long
	 */
	public Long executeCountQuery(final NativeSqlStatement sqlStatement) {
		final Number result = (Number) executeSingleResult(sqlStatement);
		return result == null ? null : result.longValue();
	}

	/**
	 * Setup sql for execution by creating a SQLQuery and adding all parameters to it. The read-only hint is also set, which should improve
	 * performance, so this method is appropriate only for reading data.
	 *
	 * @param em An entity manager, can be null.
	 * @param sqlStatement statement to execute
	 * @return SQLQuery to execute.
	 */
	private Pair<NativeQuery<?>, TypeHelper> setupExecutionReadOnly(final EntityManager em, final NativeSqlStatement sqlStatement) {
		final Pair<NativeQuery<?>, TypeHelper> query = setupExecution(em, sqlStatement);
		query.getKey().setReadOnly(true);
		return query;
	}

	/**
	 * Setup sql for execution by creating a SQLQuery and adding all parameters to it.
	 *
	 * @param em An entity manager, can be null.
	 * @param sqlStatement statement to execute
	 * @return SQLQuery to execute.
	 */
	private Pair<NativeQuery<?>, TypeHelper> setupExecution(final EntityManager em, final NativeSqlStatement sqlStatement) {
		final Session session = em.unwrap(Session.class);
		final NativeQuery<?> query = session.createNativeQuery(sqlStatement.getSQL().replaceAll(":\\:", "\\\\:\\\\:"));
		setupParameters(query, sqlStatement);
		return Pair.of(query, session.getTypeHelper());
	}

	/**
	 * Setup the query parameters.
	 *
	 * @param query the query to set the parameters on
	 * @param sqlStatement the native sql statement specifications
	 */
	@SuppressWarnings("deprecation")
	private void setupParameters(final Query<?> query, final NativeSqlStatement sqlStatement) {
		int position = 1;
		for (final Object p : sqlStatement.getParameters()) {
			final Type customParamType = getCustomParamType(p, sqlStatement, position);
			if (customParamType != null) {
				query.setParameter(position++, p instanceof NullValueWithType ? null : p, customParamType);
			}
			else if (p instanceof HibernateArray) {
				query.setParameterList(position++, ((HibernateArray) p).data);
			}
			else {
				query.setParameter(position++, p);
			}
		}
		final Set<Entry<String, Object>> namedParamsSet = sqlStatement.getNamedParameters().entrySet();
		for (final Entry<String, Object> entry : namedParamsSet) {
			final String paramName = entry.getKey();
			final Object paramValue = entry.getValue();

			if (paramValue instanceof Collection<?>) {
				query.setParameterList(paramName, (Collection<?>) paramValue);
			}
			else {
				final Type customParamType = getCustomParamType(paramValue, sqlStatement, -1);
				if (customParamType != null) {
					query.setParameter(paramName, paramValue, customParamType);
				}
				else {
					query.setParameter(paramName, paramValue);
				}
			}
		}
	}

	/**
	 * Gets a custom hibernate parameter type.
	 *
	 * @param paramValue the parameter value
	 * @param sqlStatement the native sql statement specifications
	 * @param index Index of parameter
	 * @return the custom parameter type
	 */
	private Type getCustomParamType(final Object paramValue, final NativeSqlStatement sqlStatement, final int index) {
		if (paramValue instanceof NullValueWithType) {
			return new CustomType(((NullValueWithType) paramValue).getType());
		}
		if (paramValue instanceof Long[]) {
			return new CustomType(new BigintArrayUserType());
		}
		else if (paramValue instanceof Integer[]) {
			return new CustomType(new IntegerArrayUserType());
		}
		else if (paramValue instanceof String[]) {
			return new CustomType(new StringArrayUserType());
		}
		else if (paramValue instanceof Date) {
			return new CustomType(new DateTimeUserType());
		}
		else if (paramValue instanceof AssetIdentifierType[]) {
			return new CustomType(new AssetIdentifierArrayType());
		}
		else if (paramValue instanceof Source[]) {
			return new CustomType(new SourceArrayType());
		}
		else if (paramValue != null && paramValue.getClass().isEnum()) {
			final Properties params = new Properties();
			params.put("enumClass", paramValue.getClass().getName());
			params.put("type", "12");
			final EnumType<?> enumType = new PostgreSQLEnumType();
			enumType.setTypeConfiguration(new TypeConfiguration());
			enumType.setParameterValues(params);
			return new CustomType(enumType);
		}
		return sqlStatement.getParameterType(index);
	}

	public Long[] getLongArray(final NativeSqlStatement statement, final String columnName) {
		return execute(statement, columnName, new LongArrayType()).get(0);
	}

	@Override
	public String[] getStringArray(final NativeSqlStatement statement, final String columnAlias) {
		return executeWithEntityManager(em -> getStringArray(em, statement, columnAlias));
	}

	/**
	 * Retrieves an id array
	 *
	 * @param em an entity manager
	 * @param statement the statement to execute
	 * @param columnAlias the id column alias
	 * @return the id array
	 */
	public String[] getStringArray(final EntityManager em, final NativeSqlStatement statement, final String columnAlias) {
		final List<String[]> result = execute(em, statement, columnAlias, new StringArrayType());
		if (!result.isEmpty() && result.get(0) != null) {
			return result.get(0);
		}
		return new String[] {};
	}

	@Override
	public String getString(final NativeSqlStatement statement) throws SQLException {
		return (String) executeSingleResult(statement);
	}

	public String getString(final EntityManager em, final NativeSqlStatement statement) {
		return (String) executeSingleResult(em, statement);
	}

	@Override
	public Map<String, String> getMappedValues(final NativeSqlStatement statement) {
		return executeWithEntityManager(em -> getMappedValues(em, statement));
	}

	/**
	 * Gets the results of a statement as a map.
	 *
	 * @param em the entity manager
	 * @param statement the statement to execute
	 * @return the result of the statement as a map
	 */
	public Map<String, String> getMappedValues(final EntityManager em, final NativeSqlStatement statement) {
		final List<Object[]> tupleResult = getTupleResult(em, statement);
		final Map<String, String> mappedResult = new HashMap<>();
		for (final Object[] tuple : tupleResult) {
			mappedResult.put(tuple[0].toString(), tuple[1] != null ? tuple[1].toString() : null);
		}
		return mappedResult;
	}

	/**
	 * Get result as a list of tuples.
	 *
	 * @param em Entity manager.
	 * @param statement SQL statement.
	 * @return List of tuples.
	 */
	@SuppressWarnings("unchecked")
	private List<Object[]> getTupleResult(final EntityManager em, final NativeSqlStatement statement) {
		return (List<Object[]>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, statement);
			return query.getKey().list();
		}, statement);
	}

	@Override
	public Long getLong(final NativeSqlStatement statement) throws SQLException {
		return executeCountQuery(statement);
	}

	public Long getLong(final EntityManager em, final NativeSqlStatement statement) throws SQLException {
		final Object result = executeSingleResult(em, statement);
		return result == null ? null : ((Number) result).longValue();
	}

	@Override
	public Boolean getBoolean(final NativeSqlStatement statement) throws SQLException {
		return (Boolean) executeSingleResult(statement);
	}

	public Boolean getBoolean(final EntityManager entityManager, final NativeSqlStatement statement) throws SQLException {
		return ((Boolean) executeSingleResult(entityManager, statement));
	}

	@Override
	public Date getDate(final NativeSqlStatement statement) throws SQLException {
		return ((Date) executeSingleResult(statement));
	}

	public Date getDate(final EntityManager em, final NativeSqlStatement statement) throws SQLException {
		return ((Date) executeSingleResult(em, statement));
	}

	@Override
	public Timestamp getTimestamp(final NativeSqlStatement statement) {
		return ((Timestamp) executeSingleResult(statement));
	}

	public Timestamp getTimestamp(final EntityManager em, final NativeSqlStatement statement) {
		return ((Timestamp) executeSingleResult(em, statement));
	}

	@Override
	public List<Map<String, Object>> getGenericObjects(final NativeSqlStatement statement) throws SQLException {
		return executeWithEntityManager(em -> getGenericObjects(em, statement));
	}

	/**
	 * See {@link #getGenericObjects(NativeSqlStatement)}.
	 *
	 * @param em an entity manager used to execute the query
	 * @param statement the sql query
	 * @return a list of maps, representing the results, where the key is the column alias and the values is the returned value.
	 */
	@SuppressWarnings({"unchecked", "deprecation"})
	public List<Map<String, Object>> getGenericObjects(final EntityManager em, final NativeSqlStatement statement) {
		return (List<Map<String, Object>>) executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, statement);
			query.getKey().setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
			return query.getKey().list();
		}, statement);
	}

	@Override
	public DataSetInterface getScrollableResult(final NativeSqlStatement statement) {
		return getScrollableResult(statement, null);
	}

	/**
	 * Returns result iterator that fetches the data in predefined chunks instead of all at once.
	 * Note: The result is AutoCloseable, make sure to always use this in a try-with-resources or a try with finally block that closes this resource.
	 *
	 * @param <T> An entity class to load from the database
	 * @param statement the sql statement to execute
	 * @param clazz an entity class that the result iterator will be able to produce from the result by calling the get() method on the dataset
	 * @return the result iterator
	 */
	public <T> DataSetInterface getScrollableResult(final NativeSqlStatement statement, final Class<T> clazz) {
		return executeTimedOperation(() -> {
			final EntityManager em = entityManagerFactory.createEntityManager();
			try {
				final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, statement);
				final Session session = em.unwrap(Session.class);
				session.doWork(connection -> connection.setAutoCommit(false));
				query.getKey().setFetchSize(50);
				query.getKey().setCacheable(false);
				if (clazz != null) {
					query.getKey().addEntity(clazz);
				}
				statement.addScalarsToQuery(query.getKey());
				return new ScrollableDataSetResult(statement.getReturnAliases(), query.getKey().scroll(ScrollMode.FORWARD_ONLY), em);
			}
			catch (final Throwable e) {
				em.close();
				throw(e);
			}
		}, statement);
	}

	/**
	 * See {@link #getScrollableResult(NativeSqlStatement)}.
	 *
	 * @param em an entity manager used to execute the query
	 * @param statement the sql query
	 * @return a result iterator.
	 */
	public DataSetInterface getScrollableResult(final EntityManager em, final NativeSqlStatement statement) {
		return executeTimedOperation(() -> {
			final Pair<NativeQuery<?>, TypeHelper> query = setupExecutionReadOnly(em, statement);
			query.getKey().setFetchSize(50);
			query.getKey().setCacheable(false);
			statement.addScalarsToQuery(query.getKey());
			return new ScrollableDataSetResult(statement.getReturnAliases(), query.getKey().scroll(ScrollMode.FORWARD_ONLY));
		}, statement);
	}

	/**
	 * Executes a native sql query that is expected to return one result. If there are many results retrieved, the method returns null.
	 *
	 * @param statement the sql statement to execute
	 * @param dtoClass the object type to retrieve
	 * @param <T> the object type
	 * @return the retrieved object or null if the query returns no result or returns multiple results.
	 */
	public <T> T getSingleDtoResult(final NativeSqlStatement statement, final Class<T> dtoClass) {
		return executeWithEntityManager(em -> getSingleDtoResult(em, statement, dtoClass));
	}

	/**
	 * See {@link #getSingleDtoResult(NativeSqlStatement, Class)}.
	 *
	 * @param <T> Class
	 * @param em and entity manager
	 * @param statement the sql statement
	 * @param dtoClass the result type
	 * @return the populated dto
	 */
	public <T> T getSingleDtoResult(final EntityManager em, final NativeSqlStatement statement, final Class<T> dtoClass) {
		final List<T> res = executeDTO(em, statement, dtoClass, false);
		if (res.size() == 1) {
			return res.get(0);
		}
		return null;
	}

	@Override
	public void freeResources() {
		// nothing to do here
	}

	/**
	 * Executes an operation providing a new entity manger. After the operation is done, the entity manager is closed.
	 *
	 * @param operation the operation to execute
	 * @param <T> Class of returned value.
	 * @return the operation execution result
	 */
	private <T> T executeWithEntityManager(final Function<EntityManager, T> operation) {
		EntityManager localEntityManager = null;
		try {
			if (this.entityManager != null) {
				localEntityManager = this.entityManager;
			}
			else {
				localEntityManager = entityManagerFactory.createEntityManager();
			}
			return operation.apply(localEntityManager);
		}
		finally {
			if (this.entityManager == null && localEntityManager != null) {
				localEntityManager.close();
			}
		}
	}

	/**
	 * Executes an update operation providing a new entity manger. After the operation is done, the entity manager is closed.
	 *
	 * @param operation the operation to execute
	 * @return Number of rows updated/deleted
	 */
	private int executeWithNewEntityManager(final Function<EntityManager, Integer> operation) {
		EntityManager localEntityManager = null;
		try {
			if (this.entityManager != null) {
				localEntityManager = this.entityManager;
			}
			else {
				localEntityManager = entityManagerFactory.createEntityManager();
			}
			return operation.apply(localEntityManager);
		}
		finally {
			if (this.entityManager == null && localEntityManager != null) {
				localEntityManager.close();
			}
		}
	}

	/**
	 * Executes an operation and times it. If the execution took more than the configured limit, a slow sql is logged.
	 *
	 * @param operation the operation to execute
	 * @param sqlStatement the sql statement that is being executed
	 * @param <T> Class of returned value.
	 * @return the operation execution result
	 */
	private <T> T executeTimedOperation(final Supplier<T> operation, final NativeSqlStatement sqlStatement) {
		final long start = System.currentTimeMillis();
		try {
			return operation.get();
		}
		finally {
			final long executionTime = System.currentTimeMillis() - start;
			if (executionTime > maxQueryTime) {
				logSlowSQL(sqlStatement, executionTime);
			}
		}
	}

	/**
	 * Logs a slow sql.
	 *
	 * @param sql the sql statement
	 * @param executionTime the execution time
	 */
	protected static void logSlowSQL(final NativeSqlStatement sql, final long executionTime) {
		if (sql != null) {
			final StringWriter stacktrace = new StringWriter();
			new Throwable().printStackTrace(new PrintWriter(stacktrace));
			final MapMessage<?, Object> mapMessage = new MapMessage<>()
					.with("message", "Slow SQL query: " + sql.getSQL())
					.with("milliseconds", executionTime)
					.with("parameters", sql.getParametersAsArray())
					.with("stacktrace", stacktrace);
			LOG.error(mapMessage);
		}
	}

	@Override
	public Integer[] getIntegerArray(final NativeSqlStatement statement, final String columnAlias) {
		return executeWithEntityManager(em -> getIntegerArray(em, statement, columnAlias));
	}

	/**
	 * Retrieves an id array
	 *
	 * @param em an entity manager
	 * @param statement the statement to execute
	 * @param columnAlias the id column alias
	 * @return the id array
	 */
	public Integer[] getIntegerArray(final EntityManager em, final NativeSqlStatement statement, final String columnAlias) {
		final Integer[] ids = execute(em, statement, columnAlias, new IntegerArrayType()).get(0);
		if (ids != null) {
			return ids;
		}
		return new Integer[] {};
	}

	@Override
	public Long[] getIdsArray(final NativeSqlStatement statement, final String columnAlias) {
		return executeWithEntityManager(em -> getIdsArray(em, statement, columnAlias));
	}

	/**
	 * Retrieves an id array
	 *
	 * @param em an entity manager
	 * @param statement the statement to execute
	 * @param columnAlias the id column alias
	 * @return the id array
	 */
	public Long[] getIdsArray(final EntityManager em, final NativeSqlStatement statement, final String columnAlias) {
		final List<Long[]> result = execute(em, statement, columnAlias, new LongArrayType());
		if (result != null && !result.isEmpty()) {
			final Long[] ids = result.get(0);
			if (ids != null) {
				return ids;
			}
		}
		return new Long[] {};
	}

	@Override
	public <T> List<T> getObjectList(final NativeSqlStatement statement, final Class<T> clazz) throws SQLException {
		return executeDTO(statement, clazz, true);
	}

	public <T> List<T> getObjectList(final EntityManager em, final NativeSqlStatement statement, final Class<T> clazz) throws SQLException {
		return executeDTO(em, statement, clazz, true);
	}

	@Override
	public Object getNullValueObject(final Object value, final UserType userType) {
		return value != null ? value : new NullValueWithType(userType);
	}

	@Override
	public Object createArrayOf(final String type, final String[] data) {
		return new HibernateArray(data);
	}

	public Session getSession() {
		return entityManager.unwrap(Session.class);
	}

	@SuppressFBWarnings("EI_EXPOSE_REP2")
	@AllArgsConstructor
	public static class HibernateArray {
		final String[] data;
	}
}
