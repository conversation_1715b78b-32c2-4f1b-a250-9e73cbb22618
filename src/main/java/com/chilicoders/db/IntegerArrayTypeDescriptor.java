package com.chilicoders.db;

public class IntegerArrayTypeDescriptor extends AbstractArrayTypeDescriptor<Integer[]> {

	private static final long serialVersionUID = 1975681070099609904L;
	public static final IntegerArrayTypeDescriptor INSTANCE = new IntegerArrayTypeDescriptor();

	public IntegerArrayTypeDescriptor() {
		super(Integer[].class);
	}

	@Override
	protected String getSqlArrayType() {
		return "integer";
	}
}
