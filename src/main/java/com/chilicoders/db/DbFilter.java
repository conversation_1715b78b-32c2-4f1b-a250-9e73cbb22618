package com.chilicoders.db;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlSeeAlso;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.db.exceptions.InvalidValueException;

import com.google.common.collect.ImmutableMap;

public abstract class DbFilter {

	public interface CustomFiltering {
	}

	protected static final Map<String, String> COMPARATORS = ImmutableMap.of(
			"eq", "=",
			"ne", "!=",
			"gt", ">",
			"lt", "<",
			"gte", ">=",
			"lte", "<="
	);

	/**
	 * Add filter to filtering query for normal field.
	 *
	 * @param javaField Field to add filtering on
	 * @param operator Operator to use for filter
	 * @param params Parameters
	 * @param value Value to filter on
	 * @param customFiltering Custom filtering
	 * @param gmtOffset Gmt offset
	 * @param filterOverrides Filter overrides
	 * @return Filter as string
	 */
	public String addFilter(final Field javaField, final String operator, final List<Object> params, final String value, final CustomFiltering customFiltering,
							final double gmtOffset, final Map<String, String> filterOverrides) throws InvalidValueException {
		return addFilter(javaField, getDbFieldName(javaField, filterOverrides), operator, params, value, gmtOffset);
	}

	protected abstract String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value,
										final double gmtOffset)
			throws IllegalStateException, InvalidValueException;

	/**
	 * Add next filter to filtering query.
	 *
	 * @param javaField Field to add filtering on
	 * @param operator Operator to use for filter
	 * @param filter Filtering query to be appended to
	 * @param params Parameters
	 * @param value Value to filter on
	 * @param customFiltering Custom filtering
	 * @param gmtOffset Gmt offset
	 * @param filterOverrides Filter overrides
	 * @param filterParams Filter parameters
	 */
	public void addNextFilter(final Field javaField, final String operator, final StringBuilder filter, final List<Object> params, final String value,
							  final CustomFiltering customFiltering,
							  final double gmtOffset, final Map<String, String> filterOverrides, final Map<String, List<Object>> filterParams)
			throws IllegalStateException, InvalidValueException {
		int paramSize = params.size();

		final String nextFilter = addFilter(javaField, operator, params, value, customFiltering, gmtOffset, filterOverrides);

		if (!isEmpty(nextFilter)) {
			if (filterParams != null && filterParams.containsKey(javaField.getName())) {
				for (final Object obj : filterParams.get(javaField.getName())) {
					params.add(paramSize++, obj);
				}
			}
			if (filter.length() > 0) {
				filter.append(" AND ");
			}

			String specialFilterPrefix = "";
			String specialFilterPostfix = "";
			final Class<?> declaringClass = javaField.getDeclaringClass();
			if (declaringClass != null && declaringClass.getAnnotation(DbTable.class) != null) {
				specialFilterPrefix = declaringClass.getAnnotation(DbTable.class).specialFilterPrefix();
				specialFilterPostfix = declaringClass.getAnnotation(DbTable.class).specialFilterPostfix();
			}
			if (javaField.getAnnotation(DbField.class) != null) {
				specialFilterPrefix = isEmpty(specialFilterPrefix) ? javaField.getAnnotation(DbField.class).specialFilterPrefix() : specialFilterPrefix;
				specialFilterPostfix = isEmpty(specialFilterPostfix) ? javaField.getAnnotation(DbField.class).specialFilterPostfix() : specialFilterPostfix;
			}

			filter.append(specialFilterPrefix);
			filter.append(nextFilter);
			filter.append(specialFilterPostfix);
		}
	}

	protected String strip(final String value, final String remove) {
		return value.replaceAll("^" + remove, "").replaceAll(remove + "$", "");
	}

	/**
	 * Joins strings.
	 *
	 * @param strings Strings to join
	 * @param delimiter Delimiter to use.
	 * @return Joined string.
	 */
	protected String join(final String[] strings, final String delimiter) {
		if (strings == null) {
			return "";
		}

		final StringBuilder buffer = new StringBuilder();
		for (int i = 0; i < strings.length; i++) {
			buffer.append(i == 0 ? "" : delimiter);
			buffer.append(strings[i]);
		}
		return buffer.toString();
	}

	protected boolean isEmpty(final String value) {
		return value == null || value.isEmpty();
	}

	/**
	 * Get database field name
	 *
	 * @param field Field to check
	 * @param filterOverrides Filter overrides
	 * @return Name of db field
	 */
	protected String getDbFieldName(final Field field, final Map<String, String> filterOverrides) {
		final DbField annotation = field.getAnnotation(DbField.class);

		final String name = annotation != null && !annotation.name().isEmpty() ? annotation.name() : field.getName();
		if (annotation != null && !annotation.specialFilter().isEmpty()) {
			return annotation.specialFilter();
		}
		if (filterOverrides != null && filterOverrides.containsKey(field.getName().toLowerCase())) {
			return filterOverrides.get(field.getName().toLowerCase());
		}
		return name;
	}

	/**
	 * Parses a string so it can be saved in a field
	 *
	 * @param field Field to save to
	 * @param value String to parse
	 * @return Parsed object
	 */
	protected Object parseData(final Field field, final String value) throws InvalidValueException {
		try {
			if (field.getType() == Long.class
					|| field.getType() == long.class
					|| (field.getAnnotation(DbField.class) != null && field.getAnnotation(DbField.class).filterClass() == Long.class)
					|| (field.getAnnotation(DbField.class) != null && field.getAnnotation(DbField.class).filterClass() == DbNumericFilter.class)) {
				if (field.getType() == boolean.class && !field.getAnnotation(DbField.class).listfilter()) {
					return DbHelper.getBooleanValue(value) ? 1 : 0;
				}
				if (field.getType().isEnum()) {
					try {
						Object enumObject = null;
						for (final Object obj : field.getType().getEnumConstants()) {
							final XmlEnumValue xmlEnumValue = field.getType().getField(obj.toString()).getAnnotation(XmlEnumValue.class);
							if ((xmlEnumValue != null && xmlEnumValue.value().equals(value)) || obj.toString().equals(value)) {
								enumObject = obj;
								break;
							}
						}
						if (enumObject == null) {
							return null;
						}
						return ((Integer) field.getType().getMethod("toInt").invoke(enumObject)).longValue();
					}
					catch (final NoSuchMethodException | NoSuchFieldException | IllegalAccessException | InvocationTargetException ex) {
						return null;
					}
				}
				return Long.parseLong(value);
			}
			else if (field.getType() == Boolean.class || field.getType() == boolean.class) {
				return field.getAnnotation(DbField.class).saveAsInteger() ? (DbHelper.getBooleanValue(value) ? 1 : 0) : DbHelper.getBooleanValue(value);
			}
			else if (field.getType() == Double.class || field.getType() == double.class || field.getType() == Float.class || field.getType() == float.class) {
				return Double.parseDouble(value);
			}
			else if (field.getType() == Integer.class || field.getType() == int.class || field.getAnnotation(DbField.class).filterClass() == DbTimeFilter.class) {
				return Integer.parseInt(value);
			}
		}
		catch (final NumberFormatException e) {
			throw new InvalidValueException(value + " is invalid value for " + field.getName());
		}
		throw new IllegalStateException("Type not implemented: " + field.getType().getName());
	}

	public static class DbBooleanFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value,
								   final double gmtOffset) {
			final StringBuilder filter = new StringBuilder();
			final DbField annotation = javaField.getAnnotation(DbField.class);
			filter.append(annotation.reservedName() ? "\"" : "").append(fieldName).append(annotation.reservedName() ? "\"" : "").append("=?");
			params.add(annotation.saveAsInteger() && !annotation.forceBooleanFilter() ? (DbHelper.getBooleanValue(value) ? 1 : 0) : DbHelper.getBooleanValue(value));

			return filter.toString();
		}
	}

	public static class DbDateFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value,
								   final double gmtOffset) {
			final StringBuilder filter = new StringBuilder();
			final String comparator = COMPARATORS.getOrDefault(operator, "=");
			final boolean isToday = "today".equals(operator);
			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);
			final Date parsedDate = parseDate(value);
			final int gmtMinutes = (int) (gmtOffset * 60);
			if (parsedDate == null && !isToday && !isNull && !isNotNull) {
				return null;
			}
			if (isNull) {
				filter.append(fieldName).append(" IS NULL ");
			}
			else if (isNotNull) {
				filter.append(fieldName).append(" IS NOT NULL ");
			}
			else if (operator != null && operator.matches("range-.*")) { // when days filter within date filter
				throw new IllegalStateException("Feature not yet implemented");
				//String f = field.replaceAll(orgField, "(abs(date_part('days', (now() - " + orgField + "::date))))");
				//addNumericFilter(filter, f, maxlen, ftype, fvalue, fcomparison.replace("range-", ""));
			}
			else if ("today".equals(operator) || "eq".equals(operator)) {
				filter.append("(")
						.append(fieldName)
						.append(">=(")
						.append(isToday ? "NOW()::DATE" : "?::TIMESTAMP")
						.append("- (? || ' MINUTES')::INTERVAL) AND ")
						.append(fieldName);
				filter.append("<(")
						.append(isToday ? "NOW()::DATE" : "?::TIMESTAMP")
						.append("+ '1 day'::INTERVAL - (? || ' MINUTES')::INTERVAL))")
						.append(javaField.getAnnotation(DbField.class) != null ? javaField.getAnnotation(DbField.class).specialFilterPostfix() : "");
				addParameters(params, isToday, parsedDate, gmtMinutes);
				addParameters(params, isToday, parsedDate, gmtMinutes);
			}
			else {
				filter.append(fieldName).append(comparator).append("(?::TIMESTAMP - (? || ' MINUTES')::INTERVAL)");
				params.add(parsedDate);
				params.add(gmtMinutes);
			}
			return filter.toString();
		}

		/**
		 * Add parameters to list
		 *
		 * @param params List to add to.
		 * @param isToday If date should be added.
		 * @param parsedDate Date to add
		 * @param gmtMinutes Offset in minutes from GMT
		 */
		private void addParameters(final List<Object> params, final boolean isToday, final Date parsedDate, final int gmtMinutes) {
			if (!isToday) {
				params.add(parsedDate);
			}
			params.add(gmtMinutes);
		}

		/**
		 * Parse a date.
		 *
		 * @param value String to parse
		 * @return Parsed date, null on error.
		 */
		private Date parseDate(final String value) {
			if (value == null) {
				return null;
			}
			try {
				return new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(strip(value.trim(), "'"));
			}
			catch (final ParseException e) {
				try {
					return new SimpleDateFormat("yyyy-MM-dd").parse(strip(value.trim(), "'"));
				}
				catch (final ParseException e1) {
					return null;
				}
			}
		}
	}

	public static class DbZonedDateTimeFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value, final double gmtOffset)
				throws InvalidValueException {
			final StringBuilder filter = new StringBuilder();
			final String comparator = COMPARATORS.getOrDefault(operator, "=");
			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);

			if (isNull) {
				filter.append(fieldName).append(" IS NULL ");
			}
			else if (isNotNull) {
				filter.append(fieldName).append(" IS NOT NULL ");
			}
			else {
				final ZonedDateTime[] dateValues = parseDate(value, javaField, comparator);
				if (dateValues == null) {
					throw new InvalidValueException("Missing value for " + javaField.getName());
				}

				final String filterField = fieldName.trim() + "::TIMESTAMP WITH TIME ZONE";
				if (dateValues.length == 2) {
					filter.append(comparator.equals("!=") ? "NOT " : "")
							.append("(").append(filterField).append(">").append("?").append(" AND ").append(filterField).append("<").append("?").append(")");
					params.add(dateValues[0]);
					params.add(dateValues[1]);

				}
				else {
					filter.append(filterField).append(comparator).append("?");
					params.add(dateValues[0]);
				}
			}

			return filter.toString();
		}

		/**
		 * Parse dates.
		 *
		 * @param value String to parse
		 * @param javaField Field to add filtering on
		 * @param comparator Comparator to use for filter
		 * @return Array of parsed dates
		 */
		private ZonedDateTime[] parseDate(final String value, final Field javaField, final String comparator) throws InvalidValueException {
			if (value == null) {
				return null;
			}

			try {
				final ZonedDateTime zonedDateTime = DateTimeFormatter.ISO_ZONED_DATE_TIME.parse(value, ZonedDateTime::from);
				return new ZonedDateTime[] {zonedDateTime};
			}
			catch (final DateTimeParseException e) {
				try {
					final LocalDate localDate = DateTimeFormatter.ISO_LOCAL_DATE.parse(value, LocalDate::from);
					if (comparator.equals("<") || comparator.equals(">")) {
						final ZonedDateTime zonedDateTime = ZonedDateTime.of(localDate, LocalTime.parse(comparator.equals(">") ? "23:59:59" : "00:00:00"), ZoneOffset.UTC);
						return new ZonedDateTime[] {zonedDateTime};
					}
					else if (comparator.equals("<=") || comparator.equals(">=")) {
						throw new InvalidValueException("Comparison is invalid for " + value);
					}
					else {
						final ZonedDateTime startZonedDateTime = ZonedDateTime.of(localDate, LocalTime.parse("23:59:59"), ZoneOffset.UTC).minusDays(1);
						final ZonedDateTime endZonedDateTime = ZonedDateTime.of(localDate, LocalTime.parse("00:00:00"), ZoneOffset.UTC).plusDays(1);
						return new ZonedDateTime[] {startZonedDateTime, endZonedDateTime};
					}
				}
				catch (final DateTimeParseException ex) {
					throw new InvalidValueException(value + " is invalid value for " + javaField.getName());
				}
			}
		}
	}

	public static class DbTimeFilter extends DbNumericFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value, final double gmtOffset)
				throws InvalidValueException {
			final String[] values = value.split(",");
			for (int i = 0; i < values.length; i++) {
				final String[] ranges = values[i].split("-", 2);
				for (int ii = 0; ii < ranges.length; ii++) {
					final String[] times = ranges[ii].split(":");
					long time = 0;
					if (times.length == 1) {
						time = (Integer) parseData(javaField, times[0]);
					}
					else if (times.length == 2) {
						time = ((Integer) parseData(javaField, times[0]) * 60) + (Integer) parseData(javaField, times[1]);
					}
					ranges[ii] = "" + time;
				}
				values[i] = ranges.length == 2 ? join(ranges, "-") : ranges[0];
			}
			return super.addFilter(javaField, fieldName, operator, params, join(values, ","), gmtOffset);
		}
	}

	public static class DbNumericFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value, final double gmtOffset)
				throws InvalidValueException {
			final StringBuilder filter = new StringBuilder();
			final String comparator = COMPARATORS.getOrDefault(operator, "=");
			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);

			if (isNull) {
				filter.append(fieldName).append(" IS NULL ");
			}
			else if (isNotNull) {
				filter.append(fieldName).append(" IS NOT NULL ");
			}
			else if ("eq".equals(operator) || "ne".equals(operator)) {
				final String[] items = value.split(",");

				int count = 0;
				for (final String s : items) {
					final String item = s.trim();
					if (!isEmpty(item)) {
						final int index = item.indexOf("-", 1);
						if (index != -1) {
							final Object rangestart = parseData(javaField, item.substring(0, index));
							final Object rangeend = parseData(javaField, item.substring(index + 1));
							if (rangestart != null && rangeend != null) {
								filter.append(count == 0 ? " ( " : ("eq".equals(operator) ? " OR " : " AND "));
								filter.append("((").append(fieldName);
								filter.append("eq".equals(operator) ? " >= " : " < ");
								filter.append("?)");
								params.add(rangestart);
								filter.append("eq".equals(operator) ? " AND " : " OR ");
								filter.append("(").append(fieldName);
								filter.append("eq".equals(operator) ? " <= " : " > ");
								filter.append("?))");
								params.add(rangeend);
								count++;
							}
						}
						else {
							final Object numericValue = parseData(javaField, item);
							if (numericValue != null) {
								filter.append(count == 0 ? " ( " : ("eq".equals(operator) ? " OR " : " AND "));
								filter.append(fieldName).append(comparator).append("?");
								params.add(numericValue);
								count++;
							}
						}
					}
				}

				filter.append(count != 0 ? ") " : " true ");
			}
			else {
				final Object numericValue = parseData(javaField, value);
				if (numericValue != null) {
					filter.append(fieldName).append(comparator).append("?");
					params.add(numericValue);
				}
			}
			return filter.toString();
		}
	}

	public static class DbListFilter extends DbFilter {
		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String parValue,
								   final double gmtOffset)
				throws InvalidValueException {
			final StringBuilder filter = new StringBuilder();
			final boolean stringType = javaField.getType() == String.class || javaField.getAnnotation(DbField.class).filterClass() == String.class;
			final boolean longType =
					javaField.getType() == Long.class || javaField.getType() == long.class || javaField.getType() == Integer.class || javaField.getType() == int.class ||
							javaField.getAnnotation(DbField.class).filterClass() == Long.class || javaField.getAnnotation(DbField.class).filterClass() == Integer.class;
			final boolean enumType = isEnum(javaField);

			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);
			if (isNull || isNotNull) {
				return fieldName + " IS " + (isNotNull ? "NOT " : "") + "NULL";
			}

			if (!stringType && !longType) {
				throw new IllegalStateException("Type not implemented: " + javaField.getType().getName());
			}
			if (isEmpty(parValue)) {
				return null;
			}

			if (enumType && stringType) {
				boolean exists;
				final String[] values = parValue.split(",|\\|");
				for (final String value : values) {
					exists = isEnumExist(javaField, value.trim());
					if (!exists) {
						return null;
					}
				}
			}

			if (longType) {
				final String[] values = parValue.split(",|\\|");
				for (final String value : values) {
					if (!DbHelper.isLong(value.trim())) {
						return null;
					}
				}
			}

			if (javaField.getType().isArray() || Collection.class.isAssignableFrom(javaField.getType())) {
				return createArrayFilter(javaField, fieldName, operator, params, parValue, filter);
			}
			return createListFilter(javaField, fieldName, operator, params, parValue, filter, stringType, longType, enumType);
		}

		/**
		 * Checks if field is an enum.
		 *
		 * @param field the field to be checked.
		 * @return true if field is an enum.
		 */
		private boolean isEnum(final Field field) {
			if (field.getType().isArray()) {
				return field.getType().getComponentType().isEnum();
			}
			else if (Collection.class.isAssignableFrom(field.getType())) {
				return ((Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0]).isEnum();
			}
			return field.getType().isEnum();
		}

		/**
		 * Checks if enum exists
		 *
		 * @param field the field to fetch the enum constants.
		 * @param value the vlaue to be chekced.
		 * @return true if value exists otherwise false.
		 */
		private boolean isEnumExist(final Field field, final String value) {
			boolean exists = false;
			final Object[] enumConstants = getEnums(field);
			for (final Object enumConstant : enumConstants) {
				if (value.equals(enumConstant.toString())) {
					exists = true;
					break;
				}
			}
			return exists;
		}

		/**
		 * Get all enum constants
		 *
		 * @param field the field to fetch the enum constants.
		 * @return Enum constatns as object array.
		 */
		private Object[] getEnums(final Field field) {
			if (field.getType().isArray()) {
				return field.getType().getComponentType().getEnumConstants();
			}
			else if (Collection.class.isAssignableFrom(field.getType())) {
				return ((Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0]).getEnumConstants();
			}
			return field.getType().getEnumConstants();
		}

		/**
		 * Builds filtering query for array.
		 *
		 * @param javaField for fetch the field information.
		 * @param fieldName field name
		 * @param operator filter operator
		 * @param params the list of parameter values.
		 * @param parValue the input value.
		 * @param filter the filter string to be appended.
		 * @return filtering string.
		 */
		private String createArrayFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String parValue, final StringBuilder filter) {
			final String filterType =
					isEmpty(javaField.getAnnotation(DbField.class).enumFilterName()) ? "" : ("::" + javaField.getAnnotation(DbField.class).enumFilterName() + "[]");
			final StringBuilder result = new StringBuilder();
			final String[] orValues = parValue.split("\\|");
			for (final String orValue : orValues) {
				if (result.length() != 0) {
					result.append(" OR ");
				}
				result.append("(").append("ne".equals(operator) ? "NOT " : "").append(fieldName).append(" @> ?").append(filterType).append(")");
				params.add(Arrays.stream(orValue.split(",")).map(String::trim).toArray(String[]::new));
			}
			filter.append("(").append(result).append(")");
			return filter.toString();
		}

		/**
		 * Builds filtering query for list.
		 *
		 * @param javaField for fetch the field information.
		 * @param fieldName field name
		 * @param operator filter operator
		 * @param params the list of parameter values.
		 * @param value the input value.
		 * @param filter the filter string to be appended.
		 * @param stringType to check if the parameter values is of string type.
		 * @param longType to check if the parameter values is of long type.
		 * @param enumType to check if the parameter values is of enum type.
		 * @return filtering string.
		 */
		private String createListFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value,
										final StringBuilder filter,
										final boolean stringType, final boolean longType, final boolean enumType) throws InvalidValueException {
			filter.append(stringType && !enumType ? "LOWER(" + fieldName + ")" : fieldName).append("ne".equals(operator) ? " NOT " : "").append(" IN (");
			final StringTokenizer st = new StringTokenizer(value, ",");
			int count = 0;
			while (st.hasMoreTokens()) {
				final String tmpvalue = st.nextToken();
				if (stringType) {
					filter.append(count++ > 0 ? ", " : "").append(enumType ? "?::" + javaField.getAnnotation(DbField.class).enumFilterName() : "LOWER(?)");
					params.add(tmpvalue);
				}
				else if (longType) {
					final Object numeric = parseData(javaField, tmpvalue);
					filter.append(count++ > 0 ? ", ?" : "?");
					params.add(numeric);
				}
			}
			filter.append(") ");
			return filter.toString();
		}
	}

	public static class DbStringFilter extends DbFilter {
		@Override
		protected String addFilter(final Field field, final String fieldName, final String operator, final List<Object> params, final String value, final double gmtOffset) {
			final StringBuilder filter = new StringBuilder();

			final boolean isNull = "null".equals(operator);
			final boolean isNotNull = "notnull".equals(operator);

			if (isNull) {
				filter.append(fieldName).append(" IS NULL ");
			}
			else if (isNotNull) {
				filter.append(fieldName).append(" IS NOT NULL ");
			}
			else {
				final List<String> paramsList = new ArrayList<>();
				final Pattern p = "eq".equals(operator) || "ne".equals(operator) ? Pattern.compile("^.*$") : Pattern.compile("[^\\s\"']+|\".*?\"($|\\s)|'[^']*'");
				final Matcher m = p.matcher(value);
				filter.append("COALESCE(").append(fieldName).append(",'')");
				if ("all".equals(operator) || operator == null) {
					filter.append(" ILIKE ALL (?)");
				}
				else if ("none".equals(operator)) {
					filter.append(" NOT ILIKE ALL (?)");
				}
				else if ("any".equals(operator)) {
					filter.append(" ILIKE ANY (?)");
				}
				else if ("eq".equals(operator) || "ne".equals(operator)) {
					filter.append("ne".equals(operator) ? " NOT " : " ").append("ILIKE ?");
				}
				while (m.find()) {
					final boolean emptyFilter = ("\"\"".equals(m.group(0)));
					final boolean exactMatch = m.group(0).trim().startsWith("\"");
					final String filterValue = strip(m.group(0).trim(), "\"").replaceAll("_", "\\\\_").replaceAll("\\?", "_");
					paramsList.add(emptyFilter ? "" : (exactMatch ? "" : "%") + filterValue + (exactMatch ? "" : "%"));
				}
				if (!paramsList.isEmpty()) {
					if ("eq".equals(operator) || "ne".equals(operator)) {
						params.add(paramsList.get(0));
					}
					else {
						params.add(paramsList.toArray(new String[0]));
					}
				}
				else {
					filter.setLength(0);
				}
			}
			return filter.toString();
		}
	}

	public static class DbJsonFieldFilter extends DbFilter {
		private static final Logger LOG = LogManager.getLogger(DbJsonFieldFilter.class.getName());

		private final String fieldPath;

		public DbJsonFieldFilter(final String fieldPath) {
			this.fieldPath = fieldPath;
		}

		/**
		 * Get a Json path as psql representation for a json field
		 * e.g fieldA.fieldB.fieldC = (fieldA->fieldB->>fieldC)::castType
		 *
		 * @param field Json field
		 * @param pathElements full json field path like fieldA.fieldB.fieldC
		 * @return Json path as psql representation
		 */
		public static String getJsonPathField(final Field field, final String pathElements) {

			final String[] paths = pathElements.split("\\.");
			if (paths[0].startsWith("-")) {
				paths[0] = paths[0].substring(1);
			}

			if (paths.length == 1) {
				return paths[0];
			}

			final StringBuilder jsonPath = new StringBuilder("(");

			// get nested json object with "->" (e.g fieldA->fieldB)
			jsonPath.append(paths[0]); // main field so no quote
			for (int i = 1; i < paths.length - 1; i++) {
				jsonPath.append("->");
				jsonPath.append("'");
				jsonPath.append(paths[i]);
				jsonPath.append("'");
			}

			jsonPath.append(field.getType().isArray() ? "->" : "->>");
			jsonPath.append("'");
			jsonPath.append(paths[paths.length - 1]);
			jsonPath.append("'");

			// Case array in jsonb field, format will be fieldA: [valA, valB, valC]
			// SELECT id, classifications FROM classifications t ARRAY(SELECT value FROM jsonb_array_elements_text(t.classifications -> 'owasp2010'))::int[]
			if (field.getType().isArray()) {
				jsonPath.insert(0, "ARRAY(SELECT value FROM jsonb_array_elements_text");
				jsonPath.append(")");
			}
			jsonPath.append(")");

			final DbField annotation = field.getAnnotation(DbField.class);
			if (annotation != null && !annotation.specialFilter().isEmpty()) {
				jsonPath.setLength(0);
				jsonPath.append(annotation.specialFilter());
			}
			else if (field.getType().isArray()) {
				jsonPath.append(annotation == null || annotation.enumFilterName().isEmpty() ? "" : "::" + annotation.enumFilterName() + "[]");
			}
			else if (field.getType().isAssignableFrom(Instant.class)) {
				jsonPath.append("::TIMESTAMP");
			}
			else if (field.getType().isAssignableFrom(Date.class)) {
				jsonPath.append("::DATE");
			}
			else if (field.getType().isAssignableFrom(Double.class)) {
				jsonPath.append("::DOUBLE PRECISION");
			}
			else if (field.getType().isAssignableFrom(Long.class)) {
				jsonPath.append("::BIGINT");
			}
			else if (field.getType().isAssignableFrom(Float.class)) {
				jsonPath.append("::FLOAT");
			}
			else if (field.getType().isAssignableFrom(Integer.class)) {
				jsonPath.append("::INT");
			}
			else if (field.getType().isAssignableFrom(Boolean.class)) {
				jsonPath.append("::BOOLEAN");
			}
			else {
				jsonPath.append("::TEXT");
			}
			jsonPath.append(" ");
			return jsonPath.toString();
		}

		/**
		 * Get final leaf field of JSON object
		 * e.g fieldA.fieldB.fieldC => get fieldC
		 *
		 * @param cls class to get top level fieldA
		 * @param fieldName dot notation string field like fieldA.fieldB.fieldC
		 * @param jsonClassOverrides JSON class overrides, used instead of the field annotation
		 * @return leaf field of JSON object
		 */
		public static Field getNestedField(final Class<? extends DbObject> cls, final String fieldName,
				final Map<String, Class<?>> jsonClassOverrides) throws NoSuchFieldException {
			LOG.debug("Get nested field for: {}", fieldName);

			final String[] fieldNames = fieldName.split("\\.");
			if (fieldNames[0].startsWith("-")) {
				fieldNames[0] = fieldNames[0].substring(1);
			}

			final Field topLevelField = cls.getDeclaredField(fieldNames[0]);

			// Try to find other node to leaf node
			final DbField dbField = topLevelField.getAnnotation(DbField.class);
			if (dbField.jsonClass().equals(Object.class)) {
				throw new IllegalStateException("jsonClass is not implemented in field: " + topLevelField.getName());
			}

			Class<?> jsonClass = dbField.jsonClass();
			if (jsonClassOverrides != null && jsonClassOverrides.containsKey(fieldNames[0])) {
				jsonClass = jsonClassOverrides.get(fieldNames[0]);
			}

			for (int i = 1; i < fieldNames.length - 1; i++) {
				try {
					jsonClass = jsonClass.getDeclaredField(fieldNames[i]).getType();
				}
				catch (final NoSuchFieldException ignored) {
					LOG.debug("No found field: {} in class: {}", fieldNames[i], jsonClass);
					jsonClass = getFieldInSubClass(fieldNames[i], jsonClass).getType();
				}
			}

			// Return leaf node
			try {
				return jsonClass.getDeclaredField(fieldNames[fieldNames.length - 1]);
			}
			catch (final NoSuchFieldException ignored) {
				LOG.debug("No found field: {} in class: {}", fieldNames[fieldNames.length - 1], jsonClass);
				return getFieldInSubClass(fieldNames[fieldNames.length - 1], jsonClass);
			}
		}

		/**
		 * Get the field with the specified name in the subclass of the given base class.
		 * Base class should contain {@link XmlSeeAlso} annotation, otherwise will throw NoSuchFieldException.
		 *
		 * @param fieldName The name of the field to be retrieved.
		 * @param baseClass The base class in which to search for the field.
		 * @return The first found or throw {@link NoSuchFieldException} exception.
		 * @throws NoSuchFieldException If the field with the specified name is not found in the subclass.
		 */
		private static Field getFieldInSubClass(final String fieldName, final Class<?> baseClass) throws NoSuchFieldException {
			if (baseClass.getAnnotation(XmlSeeAlso.class) == null) {
				throw new NoSuchFieldException();
			}

			final Class<?>[] subclasses = baseClass.getAnnotation(XmlSeeAlso.class).value();

			for (final Class<?> subclass : subclasses) {
				try {
					return subclass.getDeclaredField(fieldName);
				}
				catch (final NoSuchFieldException ignored) {
					LOG.debug("No found field: {} in class: {}", fieldName, subclass);
				}
			}
			throw new NoSuchFieldException();
		}

		@Override
		protected String addFilter(final Field javaField, final String fieldName, final String operator, final List<Object> params, final String value, final double gmtOffset)
				throws InvalidValueException {
			final String leafFieldName = DbJsonFieldFilter.getJsonPathField(javaField, fieldPath);
			final DbFilter dbFilter = getFilterObject(javaField);

			return dbFilter.addFilter(javaField, leafFieldName, operator, params, value, gmtOffset);
		}

		/**
		 * Get the type of filter being used.
		 *
		 * @param javaField Field to add filtering on
		 * @return Class of filter to use
		 */
		public static DbFilter getFilterObject(final Field javaField) {
			final Class<?> fieldType = javaField.getType();
			final DbField annotation = javaField.getAnnotation(DbField.class);

			if (annotation != null && annotation.listfilter()) {
				return new DbFilter.DbListFilter();
			}
			else if (fieldType.isAssignableFrom(Instant.class) || fieldType.isAssignableFrom(Date.class)) {
				return new DbZonedDateTimeFilter();
			}
			else if (fieldType.isAssignableFrom(Double.class)
					|| fieldType.isAssignableFrom(Long.class)
					|| fieldType.isAssignableFrom(Integer.class)
					|| fieldType.isAssignableFrom(Float.class)) {
				return new DbNumericFilter();
			}
			else if (fieldType.isAssignableFrom(Boolean.class)) {
				return new DbBooleanFilter();
			}
			return new DbStringFilter();
		}
	}
}
