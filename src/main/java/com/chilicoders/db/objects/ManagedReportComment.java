package com.chilicoders.db.objects;

import java.util.Date;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

@DbTable(name = "tmanagedreportscomment")
public class ManagedReportComment extends XmlAble {
	@DbField(id = true)
	private long xid;

	@DbField
	private long reportxid;

	@DbField
	private String comment;

	@DbField
	private Date date;

	@DbField(xmlName = "author", dontSave = true,
			convertName = "(SELECT vcfullname FROM tusers WHERE xid = author UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * author) AS authorname")
	private String authorName;

	@DbField(xmlName = "authorid", defaultIntValue = 0)
	private long author;

	@DbField(dontSave = true, convertName = "(author=?) AS editable")
	private boolean editable;

	public void setAuthor(final long mainUserId) {
		author = mainUserId;
	}
}
