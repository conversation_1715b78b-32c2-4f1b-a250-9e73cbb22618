package com.chilicoders.db.objects;

import java.util.Date;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
@DbTable(name = "tmanagedreports", view = "tmanagedreports r", sequence = "tmanagedreports_seq")
public class ManagedReport extends XmlAble {
	@Getter
	@DbField(set = "list", name = "xid", id = true)
	private long id;

	@Getter
	@Setter
	@DbField(set = "list", name = "xuserxid", isUserId = true)
	private long userId;

	@Getter
	@DbField(set = "list", filterable = true)
	private Date date;

	@Getter
	@Setter
	@DbField(set = "list")
	private String name;

	@Getter
	@Setter
	@DbField(set = "list", filterable = true)
	private String title;

	@Getter
	@DbField(set = "list", filterable = true)
	private Date latestDownload;

	@Getter
	@Setter
	@DbField(set = "list")
	private long reportGroupId;

	@Getter
	@Setter
	@DbField(saveCast = "uuid", convertName = "uuid::text")
	private String uuid;
}
