package com.chilicoders.db.objects;

import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.api.reportexport.model.ReportTextInterface;

import lombok.Getter;
import lombok.Setter;

@DbTable(name = "treporttexts", view = "vreporttext")
public class ReportText extends XmlAble implements ReportTextInterface {

	@DbField(id = true, name = "xid")
	@Getter
	private long id;

	@DbField(isUserId = true, name = "xuserxid", access = AccessType.ADMIN)
	@Setter
	@Getter
	private long userId;

	@DbField(isSubuserId = true, noDefault = true, name = "xsubuserxid", access = AccessType.ADMIN)
	@Setter
	@Getter
	private Long subUserId;

	@DbField(name = "itype")
	@Getter
	@Setter
	private int type;

	@DbField(name = "ilocation", filterable = true)
	@Getter
	private int location;

	@DbField(name = "isort", defaultIntValue = 1, filterable = true)
	private int sort = 1;

	@DbField(name = "vcheadline", filterable = true)
	@Getter
	@Setter
	private String headline;

	@DbField(name = "txtext")
	@Getter
	@Setter
	private String text;

	@DbField(name = "xcreator", access = AccessType.ADMIN)
	private long creatorId;

	@DbField(name = "xupdator", access = AccessType.ADMIN)
	@Setter
	private long updatorId;

	@DbField
	@Getter
	private long reportTemplate;

	@DbField(readViewOnly = true)
	private String reportTemplateName;

	@DbField(defaultIntValue = -1)
	@Getter
	private int reportLevel = -1;

	@DbField(filterable = true)
	private boolean isPublic = false;
}
