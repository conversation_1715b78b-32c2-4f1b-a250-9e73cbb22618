package com.chilicoders.db.objects;

import java.util.Date;

import javax.validation.constraints.NotEmpty;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "ManagedReportGroup")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "tmanagedreportgroups", view = "tmanagedreportgroups g", prefix = "g", sequence = "tmanagedreportgroup_seq")
public class ManagedReportGroup extends XmlAble {
	@DbField(name = "xid", id = true)
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long id = -1;

	@DbField(name = "xuserxid")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long userId;

	@DbField
	@XmlElement
	@NotEmpty
	private String name;

	@DbField(name = "xiparentid")
	@XmlElement
	private long parentId;

	@DbField(name = "dupdated")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Date updated;

	@DbField(name = "dcreated")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private Date created;

	@DbField(readViewOnly = true, notInDefaultSet = true, access = AccessType.ADMIN, set = "notdefault")
	private int sortOrder;

	@DbField(name = "icount", readViewOnly = true, notInDefaultSet = true, set = "notdefault")
	@XmlElement
	@XmlWriteOnly
	@Schema(readOnly = true)
	private long count;

	public long getId() {
		return id;
	}

	public void setId(final long id) {
		this.id = id;
	}

	public void setUserId(final long userId) {
		this.userId = userId;
	}

	public long getUserId() {
		return userId;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	public void setUpdated(final Date updated) {
		this.updated = updated;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	public void setCreated(final Date created) {
		this.created = created;
	}

	public String getName() {
		return name;
	}

	public void setName(final String name) {
		this.name = name;
	}

	public long getParentId() {
		return parentId;
	}

	public void setParentId(final long parentId) {
		this.parentId = parentId;
	}

	public void setCount(final long count) {
		this.count = count;
	}
}
