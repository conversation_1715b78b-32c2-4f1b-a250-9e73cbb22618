package com.chilicoders.db.objects;

import java.util.Date;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@DbTable(name = "tverifyscans")
public class VerifyScan extends XmlAble {
	@DbField(name = "xid", id = true)
	private long id;

	@DbField(name = "xuserxid", isUserId = true)
	private long userId;

	@DbField(name = "xsubuserxid", isSubuserId = true)
	private long subUserId;

	@DbField(saveAsInteger = true)
	private boolean pci;

	@DbField
	private long treport_vulns_xid;

	@DbField(name = "nextscandate")
	private Date date;

	@DbField
	private long verifyxid;

	public long getId() {
		return id;
	}

	public long getUserId() {
		return userId;
	}

	public Long getSubUserId() {
		return subUserId;
	}

	public boolean isPci() {
		return pci;
	}

	public long getFindingId() {
		return treport_vulns_xid;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Date getDate() {
		return date;
	}

	public long getVerifyxid() {
		return verifyxid;
	}
}
