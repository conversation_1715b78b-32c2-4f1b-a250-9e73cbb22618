package com.chilicoders.db.objects;

import com.chilicoders.bl.DashboardBusiness.Countable;
import com.chilicoders.db.DbField;

public class TopSolution extends Countable {
	@DbField
	private int solutionType;

	@DbField
	private String solutionProduct;

	@DbField
	private String solutionTitle;

	@DbField
	private String csol;

	@DbField
	private String product;

	@DbField
	private long highrisk;

	@DbField
	private long mediumrisk;

	@DbField
	private long lowrisk;

	@DbField
	private long targets;

	@DbField
	public long count;

	@DbField
	private long translationvulnid;

	@Override
	public long getCount() {
		return count;
	}

	public Long getTranslationVulnId() {
		return translationvulnid;
	}

	public void setSolutionTitle(final String title) {
		solutionTitle = title;
	}

	public String getProduct() {
		return product;
	}
}
