package com.chilicoders.db.objects.api;

public interface SpecialNoteInterface {

	long getFindingId();

	String getSpecialNote();

	String getDescription();

	boolean isSecure();

	String getItem();

	void setItem(final String item);

	long getTargetId();

	void setTargetId(final long targetId);

	String getScanComponent();

	void setScanComponent(final String scanComponent);

	long getVulnId();

	void setVulnId(final long vulnId);

	void setUserId(final Number userId);

	void setFindingId(final Number findingId);

	void setSpecialNote(final String text);
}
