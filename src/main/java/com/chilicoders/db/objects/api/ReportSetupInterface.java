package com.chilicoders.db.objects.api;

import java.util.List;

public interface ReportSetupInterface {

	long getReportId();

	String getType();

	String getModifications();

	void setModifications(final String modifications);

	/**
	 * Get report setup from list.
	 *
	 * @param setups List of report setups
	 * @param type Setup type
	 * @return ReportSetup object
	 */
	static ReportSetupInterface getSetup(final List<? extends ReportSetupInterface> setups, final String type) {
		return setups.stream().filter(item -> item.getType().equals(type)).findFirst().orElse(null);
	}
}
