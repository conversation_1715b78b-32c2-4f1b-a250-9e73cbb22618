package com.chilicoders.db.objects.api;

/**
 * Holds information about wasl url.
 */
public interface WasUrl {

	final int STATUS_UNCHANGED = 0;
	final int STATUS_NEW = 1;
	final int STATUS_REMOVED = 2;

	/**
	 * Get virtual host.
	 *
	 * @return Virtual host.
	 */
	String getVirtualHost();

	/**
	 * Get delta information, 0=unchanged, 1=new and 2=removed.
	 *
	 * @return Delta status.
	 */
	int getDelta();

	/**
	 * Check if url has been fuzzed.
	 *
	 * @return True if fuzzed.
	 */
	boolean isFuzzed();

	/**
	 * Get URL.
	 *
	 * @return URL.
	 */
	String getUrl();

	/**
	 * Get HTTP method used for URL.
	 *
	 * @return HTTP method.
	 */
	String getMethod();

	/**
	 * Get post data.
	 *
	 * @return Post data.
	 */
	String getPostData();

	/**
	 * Get HTTP response code.
	 *
	 * @return Response code.
	 */
	int getHttpResponseCode();
}
