package com.chilicoders.db.objects.api;

import com.chilicoders.model.ReportFileType;

public interface ReportFileInterface {

	long getFindingId();

	String getName();

	String getFileName();

	ReportFileType getType();

	long getAppendix();

	void setAppendix(final long appendix);

	String getContent();

	void setContent(final String content);

	byte[] getData();

	void setData(final byte[] data);

}
