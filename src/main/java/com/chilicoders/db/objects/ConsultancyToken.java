package com.chilicoders.db.objects;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.DbTable;

@DbTable(name = "consultancytokens")
public class ConsultancyToken extends DbObject {
	@DbField
	private String token;

	@DbField
	private long owner;

	@DbField
	private long userId;

	@DbField
	private long subUserId;

	public long getUserId() {
		return userId;
	}

	public long getOwner() {
		return owner;
	}

	public long getSubUserId() {
		return subUserId;
	}
}
