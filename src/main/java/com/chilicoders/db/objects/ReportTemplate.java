package com.chilicoders.db.objects;

import com.chilicoders.core.reporting.api.model.ReportTemplateScanType;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

@DbTable(name = "treporttemplates")
public class ReportTemplate extends XmlAble {
	@DbField(id = true)
	private long xid;

	@DbField(name = "xuserxid", isUserId = true)
	private long userId;

	@DbField(name = "xsubuserxid", noDefault = true)
	private Long subUserId;

	@DbField(filterable = true)
	private String name;

	@DbField(saveAsInteger = true)
	private boolean isPublic;

	@DbField
	private String state;

	@DbField
	private String serverFilter;

	@DbField
	private String targets;

	@DbField
	private String targetgroups;

	@DbField
	private long scheduleXid;

	@DbField
	private int scanType;

	@DbField
	private long compliancePolicy;

	@DbField(xmlOnly = true)
	private boolean isDefault;

	@DbField
	private String comment;

	public void setDefault() {
		isDefault = true;
	}

	public long getId() {
		return xid;
	}

	public void setSubUserId(final Long subUserId) {
		this.subUserId = subUserId;
	}

	public void setUserId(final long userId) {
		this.userId = userId;
	}

	public ReportTemplateScanType getScanType() {
		return ReportTemplateScanType.getInstance(this.scanType);
	}

	public void setCompliancePolicy(final long policyId) {
		compliancePolicy = policyId;
	}

	public boolean isDefault() {
		return isDefault;
	}

	public String getName() {
		return name;
	}

	public String getTargets() {
		return targets;
	}

	public String getTargetGroups() {
		return targetgroups;
	}

	public long getScheduleId() {
		return scheduleXid;
	}

	public long getCompliancePolicy() {
		return compliancePolicy;
	}

	public String getServerFilter() {
		return serverFilter;
	}

	public String getState() {
		return state;
	}
}
