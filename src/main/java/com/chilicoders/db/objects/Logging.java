package com.chilicoders.db.objects;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.eclipse.persistence.oxm.annotations.XmlWriteOnly;

import com.chilicoders.core.events.model.UiSource;
import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;
import com.chilicoders.model.SplunkFormat;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(name = "Logging")
@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "")
@DbTable(name = "tloggings", view = "vloggings l", prefix = "l")
public class Logging extends XmlAble implements Cloneable {
	@DbField(id = true, name = "xid")
	@XmlElement
	@XmlWriteOnly
	@Schema(accessMode = Schema.AccessMode.READ_ONLY)
	private long id;

	@DbField(name = "xuserxid", isUserId = true)
	@Getter
	@Setter
	private long userId;

	@DbField(name = "itype")
	@Getter
	@Setter
	@XmlElement
	private int logType = 3;

	@DbField(name = "xrefid")
	@Getter
	@Setter
	@XmlElement
	private long event;

	@DbField(name = "ipriority", defaultIntValue = 6)
	@Getter
	@Setter
	private int priority = 6;

	@DbField
	@Setter
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private Long[] assignee;

	@DbField
	@Getter
	@Setter
	private int ticketPriority;

	@DbField
	@Getter
	@Setter
	@XmlElement
	private String recipient;

	@DbField(name = "xsubuserxid", defaultIntValue = -1)
	@Getter
	@Setter
	private long subUserId = -1;

	@DbField(saveAsInteger = true)
	@Getter
	@Setter
	private boolean myScans;

	@DbField
	@Getter
	@Setter
	private int newFindings;

	@DbField
	@Getter
	@Setter
	private int scanFormat;

	@DbField(name = "encryptionkey")
	@Setter
	private String reportEncryptionKey;

	@DbField(saveAsInteger = true)
	@Getter
	@Setter
	private boolean attachReport;

	@DbField
	@Getter
	@Setter
	private int reportType;

	@DbField(defaultIntValue = 15)
	@Getter
	@Setter
	private int scanType = 15;

	@DbField
	@Getter
	@Setter
	@XmlElement
	private String eventName;

	@DbField
	@Getter
	private String ticketSummary;

	@DbField
	@Getter
	@Setter
	private long syslogFields;

	@DbField
	@Getter
	private long dblogFields;

	@DbField(saveAsInteger = true, defaultBoolValue = true)
	@Getter
	@Setter
	private boolean targetInformation = true;

	@DbField
	@Getter
	@Setter
	private String reportPassword;

	@DbField
	@Getter
	@Setter
	private String customSubject;

	@DbField
	@Getter
	@Setter
	private String customText;

	@DbField
	@Getter
	private long duedateNumber;

	@DbField
	@Getter
	@Setter
	private long duedateType;

	@DbField
	private boolean onlyOnPreviouslyActive;

	@DbField
	@Getter
	@Setter
	private String comments;

	@DbField
	@Setter
	@XmlElement
	private String emailEncryptionKey;

	@DbField(saveAsInteger = true)
	private boolean bactive;

	@DbField
	@Getter
	@Setter
	private long attributeFields;

	@DbField
	@Getter
	@Setter
	private boolean customConfig;

	@DbField(readViewOnly = true)
	@Getter
	@Setter
	private String targetList;

	@DbField(readViewOnly = true)
	@Getter
	@Setter
	private String targetGroupList;

	@DbField(readViewOnly = true)
	@Getter
	@Setter
	private String swatList;

	@DbField(readViewOnly = true)
	private String xassignee;

	@DbField
	@Getter
	@Setter
	private int daysInAdvance;

	@DbField
	@Getter
	@Setter
	private String scriptId;

	@DbField
	@Getter
	@Setter
	private long reportTemplate;

	@DbField(readViewOnly = true, notInDefaultSet = true)
	private String username;

	@DbField(readViewOnly = true, notInDefaultSet = true)
	private String subuserName;

	@DbField(saveAsInteger = true)
	private SplunkFormat splunkFormat;

	@DbField
	@Setter
	@XmlElement
	private UiSource uiSource;

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public long getId() {
		return id;
	}

	public void setId(final long xid) {
		this.id = xid;
	}

	/**
	 * Get assignee for event.
	 *
	 * @return List of assignee ids
	 */
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Long[] getAssignee() {
		if (assignee == null) {
			this.assignee = new Long[0];
		}
		return assignee;
	}

	public boolean isNewFindingsNotAccepted() {
		return newFindings == 3;
	}

	public boolean isNewFindings() {
		return newFindings == 1;
	}

	public boolean isSlsFindings() {
		return newFindings == 2;
	}

	public String getReportEncryptionKey() {
		return reportEncryptionKey;
	}

	public void setEncryptionKey(final String reportEncryptionKey) {
		this.reportEncryptionKey = reportEncryptionKey;
	}

	public boolean isOnlyOnPreviouslyActive() {
		return onlyOnPreviouslyActive;
	}

	public void setOnlyOnPreviouslyActive(final boolean onlyOnPreviouslyActive) {
		this.onlyOnPreviouslyActive = onlyOnPreviouslyActive;
	}

	public String getEmailEncryptionKey() {
		return emailEncryptionKey;
	}

	public boolean isActive() {
		return bactive;
	}

	public void setActive(final boolean active) {
		this.bactive = active;
	}

	public String getAssigneeNames() {
		return xassignee;
	}

	public void setAssigneeName(final String xassignee) {
		this.xassignee = xassignee;
	}

	public int getDiskUsage() {
		return daysInAdvance;
	}

	public void setDiskUsage(final int diskUsage) {
		this.daysInAdvance = diskUsage;
	}

	public SplunkFormat getSplunkFormat() {
		return splunkFormat;
	}

	/**
	 * Get name of user that created the event.
	 *
	 * @return Name of user
	 */
	public String getCreatedBy() {
		if (!StringUtils.isEmpty(subuserName)) {
			return subuserName;
		}
		return username;
	}
}