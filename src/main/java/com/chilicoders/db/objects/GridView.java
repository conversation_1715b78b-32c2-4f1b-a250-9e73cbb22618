package com.chilicoders.db.objects;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

@DbTable(name = "tgridviews", view = "vgridview")
public class GridView extends XmlAble {
	@DbField(id = true)
	private long xid;

	@DbField(name = "xuserxid")
	private long userId;

	@DbField(name = "xsubuserxid")
	private long subUserId;

	@DbField(filterable = true)
	private String name;

	@DbField
	private String stateId;

	@DbField
	private String state;

	public GridView() {
	}

	public long getId() {
		return xid;
	}

	public long getUserId() {
		return userId;
	}

	public long getSubUserId() {
		return subUserId;
	}

	public String getName() {
		return name;
	}

	public String getStateId() {
		return stateId;
	}

	public String getState() {
		return state;
	}

	public void setId(final long id) {
		this.xid = id;
	}

	public void setUserId(final long userId) {
		this.userId = userId;
	}

	public void setSubUserId(final long subUserId) {
		this.subUserId = subUserId;
	}

	public void setName(final String name) {
		this.name = name;
	}

	public void setStateId(final String stateId) {
		this.stateId = stateId;
	}

	public void setState(final String state) {
		this.state = state;
	}
}
