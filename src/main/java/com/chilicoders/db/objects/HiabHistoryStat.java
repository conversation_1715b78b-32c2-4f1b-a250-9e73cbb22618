package com.chilicoders.db.objects;

import java.util.Date;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

@DbTable(name = "thiabstatshistory")
public class HiabHistoryStat extends XmlAble {
	@DbField(id = true)
	private long id;

	@DbField(saveAsInteger = true)
	private boolean scheduler;

	@DbField(name = "hwaddr")
	private String mac;

	@DbField
	private String version;

	@DbField(saveAsInteger = true)
	private boolean virtual;

	@DbField
	private boolean revoked;

	@DbField(name = "xtime", xmlName = "dupdate", filterable = true)
	private Date lastUpdate;

	@DbField(name = "iips")
	private long ipsDefined;

	@DbField(name = "itest")
	private long scansDone;

	@DbField(name = "scansleft")
	private long scansLeft;

	@DbField(name = "webappsused")
	private long wasDefined;

	@DbField(name = "webappscansused")
	private long wasDone;

	@DbField(name = "webappscansleft")
	private long wasLeft;

	@DbField
	private String os;
}
