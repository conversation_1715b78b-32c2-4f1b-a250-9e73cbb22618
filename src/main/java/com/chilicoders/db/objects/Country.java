package com.chilicoders.db.objects;

import com.chilicoders.db.DbField;
import com.chilicoders.db.DbTable;

@DbTable(name = "tcountryl")
public class Country extends XmlAble {

	@DbField(name = "xid")
	private String id;

	@DbField(name = "vcname", xmlName = "vcname")
	private String name;

	@DbField(name = "vcareacode", xmlName = "vcareacode")
	private String areaCode;

	@DbField
	private String timezone;

	public String getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public String getTimezone() {
		return timezone;
	}
}
