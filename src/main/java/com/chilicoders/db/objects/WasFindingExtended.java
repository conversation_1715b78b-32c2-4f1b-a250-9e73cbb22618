package com.chilicoders.db.objects;

import com.chilicoders.db.DbField;

public class WasFindingExtended extends WasFindingImpl {
	@DbField
	private long vcvulnid;

	@DbField
	private String requestHeaders;

	@DbField
	private String responseHeaders;

	@DbField
	private String content;

	public long getVulnId() {
		return vcvulnid;
	}

	public String getRequestHeaders() {
		return requestHeaders;
	}

	public String getResponseHeaders() {
		return responseHeaders;
	}

	public String getContent() {
		return content;
	}
}
