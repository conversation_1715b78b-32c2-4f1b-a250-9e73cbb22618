package com.chilicoders.db;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Objects;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.UserType;

public class IntegerUserType implements UserType {

	@Override
	public int[] sqlTypes() {
		return new int[] {Types.INTEGER};
	}

	@Override
	public Class<Integer> returnedClass() {
		return Integer.class;
	}

	@Override
	public boolean equals(final Object firstValue, final Object secondValue) throws HibernateException {
		return Objects.equals(firstValue, secondValue);
	}

	@Override
	public int hashCode(final Object value) throws HibernateException {
		return Objects.hashCode(value);
	}

	@Override
	public Object nullSafeGet(final ResultSet resultSet, final String[] names, final SharedSessionContractImplementor session, final Object owner)
			throws HibernateException, SQLException {
		final int value = resultSet.getInt(names[0]);
		return resultSet.wasNull() ? null : value;
	}

	@Override
	public void nullSafeSet(final PreparedStatement statement, final Object value, final int index, final SharedSessionContractImplementor session)
			throws HibernateException, SQLException {
		if (value == null) {
			statement.setNull(index, Types.INTEGER);
		}
		else {
			statement.setInt(index, (Integer) value);
		}
	}

	@Override
	public Object deepCopy(final Object value) throws HibernateException {
		return value;
	}

	@Override
	public boolean isMutable() {
		return false;
	}

	@Override
	public Serializable disassemble(final Object value) throws HibernateException {
		return (Serializable) value;
	}

	@Override
	public Object assemble(final Serializable cached, final Object owner) throws HibernateException {
		return cached;
	}

	@Override
	public Object replace(final Object original, final Object target, final Object owner) throws HibernateException {
		return original;
	}
}
