package com.chilicoders.db;

import java.sql.SQLException;
import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A helper class that keeps track of database object information.
 */
public class DbHelper {
	/**
	 * Information about the different classes that can be handled in the database.
	 */
	private static final Map<Class<? extends DbObject>, DbObjectInformation> objects = new HashMap<>();

	/**
	 * Get object information for a specific class. Will cache the result for later use.
	 *
	 * @param cls The class to get information for.
	 * @return A <code>DbObjectInformatio</code> object containing mappings and information for the class.
	 */
	public static DbObjectInformation getObjectInformation(final Class<? extends DbObject> cls) {
		if (objects.containsKey(cls)) {
			return objects.get(cls);
		}
		return createInformation(cls);
	}

	/**
	 * Creates object information for a specific class.
	 *
	 * @param cls The class to get information for.
	 * @return A <code>DbObjectInformatio</code> object containing mappings and information for the class.
	 */
	private static DbObjectInformation createInformation(final Class<? extends DbObject> cls) {
		synchronized (objects) {
			if (objects.containsKey(cls)) {
				return objects.get(cls);
			}

			final DbObjectInformation result = new DbObjectInformation(cls);
			objects.put(cls, result);

			return result;
		}
	}

	/**
	 * Add where clauses to sql.
	 *
	 * @param sb String to add to
	 * @param firstRun True if WHERE should be added
	 * @param whereClauses Where clauses to add
	 */
	private static void addWhereClause(final StringBuilder sb, final boolean firstRun, final Object... whereClauses) {
		if (whereClauses != null && whereClauses.length > 0 && (whereClauses.length != 1 || whereClauses[0] != null)) {
			if (firstRun) {
				sb.append(" WHERE ");
			}
			for (final Object clause : whereClauses) {
				if (clause instanceof String) {
					sb.append(" ").append(clause);
				}
				else if (clause instanceof Collection<?>) {
					addWhereClause(sb, false, ((Collection<?>) clause).toArray());
				}
			}
		}
	}

	/**
	 * Get a select query for an object.
	 *
	 * @param cls The class to get the SQL select for.
	 * @param selectOverride Override select
	 * @param tableOverride Override table
	 * @param access The access rights to use for the query.
	 * @param params List of parameters
	 * @param columnParams Parameters for column query
	 * @param columnOverrides Override columns
	 * @param whereClauses Where clauses to be added.
	 * @return An SQL select statement to get the object.
	 */
	public static String getObjectSelect(final Class<? extends DbObject> cls, final String selectOverride, final String tableOverride, final Access access,
										 final List<Object> params, final Map<String, List<Object>> columnParams,
										 final Map<String, String> columnOverrides, final Object... whereClauses) {
		final DbObjectInformation objectInformation = getObjectInformation(cls);

		final StringBuilder sb = new StringBuilder(
				setEmpty(selectOverride, objectInformation.getSelect(access, params, columnParams, columnOverrides)) + " FROM " + setEmpty(tableOverride,
						objectInformation.getReadViewOrTable()));
		addWhereClause(sb, true, whereClauses);
		return sb.toString();
	}

	/**
	 * Get a select query for an object.
	 *
	 * @param cls The class to get the SQL select for.
	 * @param access The access rights to use for the query.
	 * @param whereClauses Where clauses to be added.
	 * @return An SQL select statement to get the object.
	 */
	public static String getSelect(final Class<? extends DbObject> cls, final Access access, final Object... whereClauses) {
		return getObjectSelect(cls, null, null, access, null, null, null, whereClauses);
	}

	public static String getObjectCountSelect(final Class<? extends DbObject> cls, final String tableOverride, final Access access) {
		return "SELECT COUNT(*) FROM " + setEmpty(tableOverride, getObjectInformation(cls).getReadViewOrTable());
	}

	public static String getObjectCountSelect(final Class<? extends DbObject> cls, final String tableOverride, final Access access, final Object... whereClauses) {
		return getObjectSelect(cls, "SELECT COUNT(*)", tableOverride, access, null, null, null, whereClauses);
	}

	public static String getObjectCountSelectWithValueOverride(final Class<? extends DbObject> cls, final String tableOverride, final Access access,
															   final String valueOverride, final Object... whereClauses) {
		return getObjectSelect(cls, "SELECT COUNT(" + valueOverride + ")", tableOverride, access, null, null, null, whereClauses);
	}

	/**
	 * Parse a string as an commaseparated list of ids
	 *
	 * @param text String to parse
	 * @return Array of ids
	 */
	public static Long[] createIdArray(final String text) throws SQLException {
		if (isEmpty(text)) {
			return new Long[0];
		}
		final String[] splitted = text.trim().split(",");
		final List<Long> res = new ArrayList<>();

		try {
			for (int i = 0; i < splitted.length; i++) {
				if (splitted[i].trim().length() > 0) {
					res.add(Long.parseLong(splitted[i].trim()));
				}
			}
		}
		catch (final NumberFormatException e) {
			throw new SQLException(e);
		}
		return res.toArray(new Long[0]);
	}

	public static final String getSequence(final Class<? extends DbObject> cls) {
		final DbObjectInformation objectInformation = getObjectInformation(cls);
		return objectInformation.getSequence();
	}

	/**
	 * Function to test a string empty
	 *
	 * @param in Value to check.
	 * @return <code>true</code> if value is null or a string with only whitespaces, <code>false</code> otherwise.
	 */
	public static boolean isEmpty(final String in) {
		return (in == null || "".equals(in.trim()));
	}

	/**
	 * Checks if a string is a long.
	 *
	 * @param inStr The string to check if it's a long
	 * @return true if inStr can be parsed as a long.
	 */
	public static boolean isLong(final String inStr) {
		try {
			Long.parseLong(inStr);
			return true;
		}
		catch (final Exception ignored) {
			return false;
		}
	}

	/**
	 * Function to return a default string if empty
	 *
	 * @param in Value to check.
	 * @param defstr Default value used if empty.
	 * @return in parameter or defstr if in is <code>null</code>.
	 */
	public static String setEmpty(final String in, final String defstr) {
		if (isEmpty(in)) {
			return defstr;
		}
		return in;
	}

	/**
	 * Returns the first non empty value.
	 *
	 * @param params The string to check.
	 * @return First non empty value, if no nonempty value exists then selects the first parameter value.
	 */
	public static String setEmpty(final String... params) {
		for (final String value : params) {
			if (!isEmpty(value)) {
				return value;
			}
		}
		return params[0];
	}

	/**
	 * Parse as long value.
	 *
	 * @param in Text to parse
	 * @return Value parsed
	 */
	public static long getLongValue(final String in) {
		if (isEmpty(in)) {
			return -1;
		}
		try {
			return Long.parseLong(in);
		}
		catch (final NumberFormatException e) {
			return -1;
		}
	}

	/**
	 * Compare two strings, null safe.
	 *
	 * @param one String to compare
	 * @param two Other string to compare
	 * @return true if equal or both null
	 */
	public static boolean compare(final String one, final String two) {
		if (one == null && two == null) {
			return true;
		}
		else if (one != null) {
			return one.equals(two);
		}
		return false;
	}

	/**
	 * Parse text as int.
	 *
	 * @param in Text to parse
	 * @return Int value
	 */
	public static int getIntValue(final String in) {
		if (isEmpty(in)) {
			return -1;
		}
		try {
			return Integer.parseInt(in);
		}
		catch (final NumberFormatException e) {
			return -1;
		}
	}

	/**
	 * Parse text as double.
	 *
	 * @param in Text to parse
	 * @return Double value
	 */
	public static double getDoubleValue(final String in) {
		if (isEmpty(in)) {
			return -1;
		}
		try {
			return Double.parseDouble(in);
		}
		catch (final NumberFormatException e) {
			return -1;
		}
	}

	/**
	 * Parse text as boolean.
	 *
	 * @param in Text to parse
	 * @return Boolean value
	 */
	public static boolean getBooleanValue(final String in) {
		if (isEmpty(in)) {
			return false;
		}

		if ("1".equals(in) || "true".equals(in) || "on".equals(in) || "yes".equals(in)) {
			return true;
		}

		return false;
	}

	/**
	 * Parse text as date.
	 *
	 * @param date Text to parse
	 * @return Date value
	 */
	public static synchronized Date parseDate(final String date) {
		if (date == null) {
			return null;
		}
		try {
			return new SimpleDateFormat("yyyy-MM-dd").parse(date);
		}
		catch (final ParseException e) {
			return null;
		}
	}

	/**
	 * Parse text as Time.
	 *
	 * @param date Text to parse
	 * @return Time value
	 */
	public static synchronized Time parseTime(final String date) {
		try {
			return new Time(new SimpleDateFormat("HH:mm").parse(date).getTime());
		}
		catch (final ParseException e1) {
			return null;
		}
	}

	/**
	 * Parse text as Date.
	 *
	 * @param date Text to parse
	 * @return Date value
	 */
	public static synchronized Date parseTimeDate(final String date) {
		if (date == null) {
			return null;
		}
		try {
			return new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(date);
		}
		catch (final ParseException e) {
			return null;
		}
	}

	/**
	 * Write date as string
	 *
	 * @param date Date to write
	 * @param isoDateFormat True to add timezone
	 * @return Text version of date
	 */
	public static synchronized String formatTimeDate(final Date date, final Boolean isoDateFormat) {
		if (date == null) {
			return null;
		}
		return new SimpleDateFormat(isoDateFormat != null && isoDateFormat ? "yyyy-MM-dd'T'HH:mm'Z'" : "yyyy-MM-dd HH:mm").format(date);
	}

	/**
	 * Write time as string
	 *
	 * @param date Time to write
	 * @return Text version of time
	 */
	public static synchronized String formatTime(final Time date) {
		if (date == null) {
			return null;
		}
		return new SimpleDateFormat("HH:mm").format(date);
	}

	/**
	 * Adds an id to commaseparated list of ids.
	 *
	 * @param buffer List to write to
	 * @param xid New id
	 */
	public static void addXid(final StringBuilder buffer, final String xid) {
		if (buffer.length() > 0) {
			buffer.append(',');
		}
		buffer.append(xid);
	}

	public static String[] getColumns(final Class<? extends DbObject> cls, final Set<String> ignoreColumns) {
		final DbObjectInformation objectInformation = getObjectInformation(cls);
		return objectInformation.getColumns(ignoreColumns);
	}

	public static String[] getFields(final Class<? extends DbObject> cls, final Set<String> ignoreFields) {
		final DbObjectInformation objectInformation = getObjectInformation(cls);
		return objectInformation.getFields(ignoreFields);
	}
}
