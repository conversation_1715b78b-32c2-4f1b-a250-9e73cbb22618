package com.chilicoders.db;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.chilicoders.util.StringUtils;

/**
 * <AUTHOR>
 */
public class ArrayUtil {

	/**
	 * Copy array.
	 *
	 * @param <T> Class
	 * @param objectArray Array
	 * @return Array
	 */
	@SuppressWarnings({"rawtypes", "unchecked"})
	public static <T> T deepCopy(final Object objectArray) {
		final Class arrayClass = objectArray.getClass();

		if (boolean[].class.equals(arrayClass)) {
			final boolean[] array = (boolean[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (byte[].class.equals(arrayClass)) {
			final byte[] array = (byte[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (short[].class.equals(arrayClass)) {
			final short[] array = (short[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (int[].class.equals(arrayClass)) {
			final int[] array = (int[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (long[].class.equals(arrayClass)) {
			final long[] array = (long[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (float[].class.equals(arrayClass)) {
			final float[] array = (float[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (double[].class.equals(arrayClass)) {
			final double[] array = (double[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else if (char[].class.equals(arrayClass)) {
			final char[] array = (char[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
		else {
			final Object[] array = (Object[]) objectArray;
			return (T) Arrays.copyOf(array, array.length);
		}
	}

	/**
	 * Wrap array.
	 *
	 * @param objectArray Array
	 * @return Array
	 */
	@SuppressWarnings("rawtypes")
	public static Object[] wrapArray(final Object objectArray) {
		final Class arrayClass = objectArray.getClass();

		if (boolean[].class.equals(arrayClass)) {
			final boolean[] fromArray = (boolean[]) objectArray;
			final Boolean[] array = new Boolean[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (byte[].class.equals(arrayClass)) {
			final byte[] fromArray = (byte[]) objectArray;
			final Byte[] array = new Byte[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (short[].class.equals(arrayClass)) {
			final short[] fromArray = (short[]) objectArray;
			final Short[] array = new Short[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (int[].class.equals(arrayClass)) {
			final int[] fromArray = (int[]) objectArray;
			final Integer[] array = new Integer[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (long[].class.equals(arrayClass)) {
			final long[] fromArray = (long[]) objectArray;
			final Long[] array = new Long[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (float[].class.equals(arrayClass)) {
			final float[] fromArray = (float[]) objectArray;
			final Float[] array = new Float[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (double[].class.equals(arrayClass)) {
			final double[] fromArray = (double[]) objectArray;
			final Double[] array = new Double[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else if (char[].class.equals(arrayClass)) {
			final char[] fromArray = (char[]) objectArray;
			final Character[] array = new Character[fromArray.length];
			for (int i = 0; i < fromArray.length; i++) {
				array[i] = fromArray[i];
			}
			return array;
		}
		else {
			return (Object[]) objectArray;
		}
	}

	/**
	 * Unwrap array.
	 *
	 * @param <T> Class
	 * @param objectArray Array
	 * @param arrayClass Class
	 * @return Array
	 */
	@SuppressWarnings("unchecked")
	public static <T> T unwrapArray(final Object[] objectArray, final Class<T> arrayClass) {

		if (boolean[].class.equals(arrayClass)) {
			final boolean[] array = new boolean[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Boolean) objectArray[i] : Boolean.FALSE;
			}
			return (T) array;
		}
		else if (byte[].class.equals(arrayClass)) {
			final byte[] array = new byte[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Byte) objectArray[i] : 0;
			}
			return (T) array;
		}
		else if (short[].class.equals(arrayClass)) {
			final short[] array = new short[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Short) objectArray[i] : 0;
			}
			return (T) array;
		}
		else if (int[].class.equals(arrayClass)) {
			final int[] array = new int[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Integer) objectArray[i] : 0;
			}
			return (T) array;
		}
		else if (long[].class.equals(arrayClass)) {
			final long[] array = new long[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Long) objectArray[i] : 0L;
			}
			return (T) array;
		}
		else if (float[].class.equals(arrayClass)) {
			final float[] array = new float[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Float) objectArray[i] : 0f;
			}
			return (T) array;
		}
		else if (double[].class.equals(arrayClass)) {
			final double[] array = new double[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Double) objectArray[i] : 0d;
			}
			return (T) array;
		}
		else if (char[].class.equals(arrayClass)) {
			final char[] array = new char[objectArray.length];
			for (int i = 0; i < objectArray.length; i++) {
				array[i] = objectArray[i] != null ? (Character) objectArray[i] : 0;
			}
			return (T) array;
		}
		else {
			return (T) objectArray;
		}
	}

	/**
	 * Create Array from string.
	 *
	 * @param <T> Class
	 * @param string String
	 * @param arrayClass Class
	 * @return Array
	 */
	@SuppressWarnings("unchecked")
	public static <T> T fromString(final String string, final Class<T> arrayClass) {
		final String stringArray = string.replaceAll("[\\[\\]]", "");
		final String[] tokens = stringArray.split(",");

		final int length = tokens.length;

		if (boolean[].class.equals(arrayClass)) {
			final boolean[] array = new boolean[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Boolean.valueOf(tokens[i]);
			}
			return (T) array;
		}
		else if (byte[].class.equals(arrayClass)) {
			final byte[] array = new byte[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Byte.valueOf(tokens[i]);
			}
			return (T) array;
		}
		else if (short[].class.equals(arrayClass)) {
			final short[] array = new short[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Short.valueOf(tokens[i]);
			}
			return (T) array;
		}
		else if (int[].class.equals(arrayClass)) {
			final int[] array = new int[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Integer.parseInt(tokens[i]);
			}
			return (T) array;
		}
		else if (long[].class.equals(arrayClass)) {
			final long[] array = new long[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Long.parseLong(tokens[i]);
			}
			return (T) array;
		}
		else if (float[].class.equals(arrayClass)) {
			final float[] array = new float[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Float.parseFloat(tokens[i]);
			}
			return (T) array;
		}
		else if (double[].class.equals(arrayClass)) {
			final double[] array = new double[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = Double.parseDouble(tokens[i]);
			}
			return (T) array;
		}
		else if (char[].class.equals(arrayClass)) {
			final char[] array = new char[length];
			for (int i = 0; i < tokens.length; i++) {
				array[i] = tokens[i].length() > 0 ? tokens[i].charAt(0) : Character.MIN_VALUE;
			}
			return (T) array;
		}
		else {
			return (T) tokens;
		}
	}

	/**
	 * Check if arrays are equal.
	 *
	 * @param firstArray Array
	 * @param secondArray Array
	 * @return <code>true</code> if equals
	 */
	public static boolean isEquals(final Object firstArray, final Object secondArray) {
		if (firstArray.getClass() != secondArray.getClass()) {
			return false;
		}
		@SuppressWarnings("rawtypes")
		final Class arrayClass = firstArray.getClass();

		if (boolean[].class.equals(arrayClass)) {
			return Arrays.equals((boolean[]) firstArray, (boolean[]) secondArray);
		}
		else if (byte[].class.equals(arrayClass)) {
			return Arrays.equals((byte[]) firstArray, (byte[]) secondArray);
		}
		else if (short[].class.equals(arrayClass)) {
			return Arrays.equals((short[]) firstArray, (short[]) secondArray);
		}
		else if (int[].class.equals(arrayClass)) {
			return Arrays.equals((int[]) firstArray, (int[]) secondArray);
		}
		else if (long[].class.equals(arrayClass)) {
			return Arrays.equals((long[]) firstArray, (long[]) secondArray);
		}
		else if (float[].class.equals(arrayClass)) {
			return Arrays.equals((float[]) firstArray, (float[]) secondArray);
		}
		else if (double[].class.equals(arrayClass)) {
			return Arrays.equals((double[]) firstArray, (double[]) secondArray);
		}
		else if (char[].class.equals(arrayClass)) {
			return Arrays.equals((char[]) firstArray, (char[]) secondArray);
		}
		else {
			return Arrays.equals((Object[]) firstArray, (Object[]) secondArray);
		}
	}

	/**
	 * Extracts an array of long ids from a string.
	 *
	 * @param text the string containing the comma-separated numeric ids.
	 * @return an array of the ids in the text
	 * @throws IllegalArgumentException if the text format is invalid
	 */
	public static Long[] createIdArray(final String text) throws IllegalArgumentException {
		if (StringUtils.isEmpty(text)) {
			return new Long[0];
		}
		final String[] splitted = text.replaceAll("'", "").trim().split(",");
		final List<Long> res = new ArrayList<>();

		for (int i = 0; i < splitted.length; i++) {
			if (splitted[i].trim().length() > 0) {
				res.add(Long.parseLong(splitted[i].trim()));
			}
		}

		return res.toArray(new Long[0]);
	}

	/**
	 * Convert ID Long array to a comma-separated string.
	 *
	 * @param ids Long array of IDs.
	 * @return Comma-separated string list of IDs.
	 */
	public static String convertIdArrayToString(final Long[] ids) {
		if (ids == null) {
			return null;
		}

		final StringBuilder sb = new StringBuilder();
		for (final Long id : ids) {
			sb.append((sb.length() == 0 ? "" : ",") + id.toString());
		}
		return sb.toString();
	}

}
