package com.chilicoders.discover;

import java.util.List;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * An Asset Identifier Resource that will be parsed from the given JSON report data which retrieved from authentication scan
 * Sample json data
 * {"type":"ASSET_IDENTIFIER","data":{"type":"SERIAL_DISK_ID","identifier":"97cbf70a-ced0-44b0-a8d6-0b75f24ac8e8","from":{"type":"IP","identifier":"*******","relation":"CONTAINS"}}}
 * {"type":"ASSET_IDENTIFIER","data":{"type":"IP","identifier":"*******","from":{"type":"IP","identifier":"*******","relation":"CONTAINS"}}}
 * {"type":"ASSET_IDENTIFIER","data":{"type":"SERIAL_DISK_ID","identifier":"0b75f24a-ced0-44b0-a8d6-0b75f24ac8e8","from":{"type":"IP","identifier":"*******","relation":"CONTAINS"}}}
 * Epic: DEV-8483
 */
@Data
@NoArgsConstructor
@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
public class IdentifierResource {
	private List<String> ips;
	private String hostname;
	private List<String> macs;
	private String serialMachineId;
	private String serialProductId;
	private List<String> serialDiskIds;
}
