package com.chilicoders.migration;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.net.ssl.SSLContext;
import javax.ws.rs.core.MediaType;

import org.apache.commons.io.FilenameUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.storage.api.StorageService;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.GenericResourceIdentifier;
import com.chilicoders.core.storage.api.model.ResourceIdentifier;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.ManagedReportGroup;
import com.chilicoders.db.objects.User;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.rest.models.ManagedReport;
import com.chilicoders.rest.models.RestObject;
import com.chilicoders.service.ScanDataListener;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import software.amazon.awssdk.services.s3.model.S3Exception;

public class ManagedReportMigration {
	private static final Logger LOG = LogManager.getLogger(ManagedReportMigration.class);

	private static final int LIMIT = 10;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection connection;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final User user;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Customer customer;

	private final Integer migratedTagId;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Customer.Migration migration;

	private final StorageService storageService;

	private final ConfigurationService configService;

	private RestTemplate restTemplate;

	private final Map<Long, ManagedReportGroup> managedReportGroups = new HashMap<>();

	private final Map<Long, Integer> managedReportGroupsTags = new HashMap<>();

	/**
	 * Constructor.
	 *
	 * @param connection Database connection
	 * @param user User
	 * @param customer Customer
	 * @param migratedTagId Id of the common migration tag.
	 * @param migration Migration object
	 */
	public ManagedReportMigration(final Connection connection, final User user, final Customer customer, final Integer migratedTagId, final Customer.Migration migration) {
		this.connection = connection;
		this.user = user;
		this.customer = customer;
		this.migratedTagId = migratedTagId;
		this.migration = migration;

		storageService = ServiceProvider.getStorageService();
		configService = ServiceProvider.getConfigService();

		final SSLContext sslContext = SslUtils.getO24DefaultSslContext();
		final CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier())).build();
		final HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
		httpRequestFactory.setConnectTimeout(10000);
		httpRequestFactory.setReadTimeout(10000);
		restTemplate = new RestTemplate(httpRequestFactory);
	}

	/**
	 * Migrate managed reports.
	 */
	public void migrate() throws SQLException {
		final List<ManagedReportGroup> managedReportGroups = ManagedReportGroup.fetchObjects(ManagedReportGroup.class, connection, Access.ADMIN,
				"xuserxid = ?", user.getMainUserId());
		for (final ManagedReportGroup managedReportGroup : managedReportGroups) {
			this.managedReportGroups.put(managedReportGroup.getId(), managedReportGroup);
		}

		List<com.chilicoders.db.objects.ManagedReport> managedReports;
		int count = 0;
		int offset = 0;
		do {
			managedReports = com.chilicoders.db.objects.ManagedReport.fetchObjects(com.chilicoders.db.objects.ManagedReport.class, connection, -1, offset, LIMIT, "xid",
					DbHelper.getSelect(com.chilicoders.db.objects.ManagedReport.class, Access.ADMIN, MigrationUtil.getManagedReportQuery()),
					user.getMainUserId());
			LOG.debug("Managed reports: " + managedReports.size());
			offset = offset + LIMIT;
			for (final com.chilicoders.db.objects.ManagedReport managedReport : managedReports) {
				migrate(managedReport);
				count++;
			}
			migration.setMigratedManagedReports(count);
			MigrationUtil.updateMigration(connection, migration);
			connection.commit();
		}
		while (!managedReports.isEmpty());
	}

	/**
	 * Migrate a managed report.
	 *
	 * @param managedReport managed report
	 */
	private void migrate(final com.chilicoders.db.objects.ManagedReport managedReport) throws SQLException {
		final ResourceIdentifier destinationResourceId = TenantResourceIdentifier.builder()
				.domain(Domain.MANAGED_REPORTS)
				.tenantUuid(user.getCustomerUuid())
				.key(managedReport.getUuid()).build();

		if (Configuration.isKubernetesEnabled()) {
			final ResourceIdentifier sourceResourceId = new GenericResourceIdentifier(user.getCustomerUuid(), managedReport.getUuid());

			if (!migrateManagedReport(sourceResourceId)) {
				LOG.info("Failed to migrate managed report file: " + managedReport.getId());
				return;
			}
		}
		else {
			final ResourceIdentifier sourceResourceId = TenantResourceIdentifier.builder()
					.domain(Domain.LEGACY_MANAGED_REPORTS)
					.tenantUuid(user.getCustomerUuid())
					.key(managedReport.getUuid()).build();

			try {
				storageService.copyResource(sourceResourceId, destinationResourceId);
			}
			catch (final S3Exception e) {
				LOG.info("Failed to copy managed report file: " + managedReport.getId() + ", " + e.getMessage());
				return;
			}
		}

		final ManagedReport newManagedReport = new ManagedReport();
		newManagedReport.setCustomerId(customer.getId());
		newManagedReport.setCreatedById(RestObject.SYSTEM_USER);

		final String extension = FilenameUtils.getExtension(managedReport.getName());
		String name = StringUtils.setEmpty(managedReport.getTitle(), managedReport.getName());
		if (!StringUtils.isEmpty(extension) && !name.toLowerCase().endsWith(extension.toLowerCase())) {
			name = name + "." + extension;
		}

		newManagedReport.setName(name);
		newManagedReport.setMigration((int) managedReport.getId());
		final Integer managedReportId = (int) newManagedReport.save(connection);

		DbObject.executeUpdate(connection, "UPDATE managedreports SET uuid = ?::UUID, created = ?, lastdownloaded = ? WHERE id = ?",
				managedReport.getUuid(), managedReport.getDate(), managedReport.getLatestDownload(), managedReportId);

		// Link to migration tag
		DbObject.executeUpdate(connection,
				"INSERT INTO tag_managedreport (tagid, managedreportid) VALUES (?, ?) ON CONFLICT DO NOTHING",
				migratedTagId, managedReportId);

		// Managed report group tag
		final Integer managedReportGroupTagId = getManagedReportGroupTag(managedReport.getReportGroupId());
		if (managedReportGroupTagId != null) {
			DbObject.executeUpdate(connection,
					"INSERT INTO tag_managedreport (tagid, managedreportid) VALUES (?, ?) ON CONFLICT DO NOTHING",
					managedReportGroupTagId, managedReportId);
		}
	}

	/**
	 * Create a managed report group tag.
	 *
	 * @param managedReportGroupId Managed report group id
	 * @return Tag id
	 */
	private Integer getManagedReportGroupTag(final long managedReportGroupId) throws SQLException {
		if (managedReportGroupId < 0) {
			return null;
		}

		if (managedReportGroupsTags.containsKey(managedReportGroupId)) {
			return managedReportGroupsTags.get(managedReportGroupId);
		}

		final List<String> managedReportGroupNames = new ArrayList<>();
		getManagedReportGroupNames(managedReportGroupId, managedReportGroupNames);
		if (managedReportGroupNames.isEmpty()) {
			return null;
		}
		Collections.reverse(managedReportGroupNames);
		final String name = MigrationUtil.sanitizeTag(StringUtils.join(managedReportGroupNames, "/"));
		final Integer tagId = MigrationUtil.createTag(connection, customer.getId(), name, null, 0);
		managedReportGroupsTags.put(managedReportGroupId, tagId);
		return tagId;
	}

	/**
	 * Get managed report group names.
	 *
	 * @param managedReportGroupId Managed report group id
	 * @param names List to add managed report group names to
	 */
	private void getManagedReportGroupNames(final long managedReportGroupId, final List<String> names) {
		if (managedReportGroupId < 0) {
			return;
		}
		final ManagedReportGroup managedReportGroup = managedReportGroups.get(managedReportGroupId);
		if (managedReportGroup == null) {
			return;
		}
		names.add(managedReportGroup.getName());
		getManagedReportGroupNames(managedReportGroup.getParentId(), names);
	}

	/**
	 * Call provisioning service and migrate managed report.
	 *
	 * @param resourceId Resource to migrate
	 * @return true if managed report was migrated
	 */
	private boolean migrateManagedReport(final ResourceIdentifier resourceId) {
		try {
			final String token = ScanDataListener.getToken(this.configService);

			final MultiValueMap<String, String> requestHeaders = new LinkedMultiValueMap<>();
			requestHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
			requestHeaders.add(HttpHeaders.AUTHORIZATION, "Bearer " + token);
			final HttpEntity<String> entity = new HttpEntity<>(resourceId.toString(), requestHeaders);

			final ResponseEntity<String> response = this.restTemplate.exchange(this.configService.getProperty(ConfigurationKey.vmapi_service_url) + "provisioning-service/migrate-managed-report",
					HttpMethod.POST, entity, String.class);

			return response.getStatusCode().is2xxSuccessful();
		}
		catch (final HttpClientErrorException e) {
			LOG.info("Migrate managed report request failed: " + e.getMessage());
			return false;
		}
	}
}
