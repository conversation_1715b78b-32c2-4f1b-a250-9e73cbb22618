package com.chilicoders.migration;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.xml.bind.JAXBException;

import org.apache.commons.lang.ArrayUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.UserBusiness;
import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao;
import com.chilicoders.core.assetidentifier.dao.AssetIdentifierDao.AssetIdentifierQuery;
import com.chilicoders.core.assets.api.AssetDao;
import com.chilicoders.core.user.api.AttributeType;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.objects.Attribute;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.db.objects.Target;
import com.chilicoders.db.objects.User;
import com.chilicoders.exception.InvalidCVSSException;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierLinkType;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.AssetLinkInterface;
import com.chilicoders.model.Source;
import com.chilicoders.rest.models.Asset;
import com.chilicoders.rest.models.AssetIdentifier;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.rest.models.RestObject;
import com.chilicoders.service.dao.AssetDaoImpl;
import com.chilicoders.service.dao.AssetIdentifierDaoImpl;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.CvssV2;
import com.chilicoders.util.CvssV3;
import com.chilicoders.util.IpUtils;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

public class TargetMigration {
	private static final Logger LOG = LogManager.getLogger(TargetMigration.class);

	private static final int LIMIT = 50;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection connection;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final User user;

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Customer customer;

	private final AssetDao assetDao;

	private final AssetIdentifierDao assetIdentifierDao;

	private final Integer migratedTagId;

	private List<Attribute> attributes;

	private final Map<String, Integer> createdTags = new HashMap<>();

	private final Map<Long, Integer> scannerTags = new HashMap<>();

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Customer.Migration migration;

	@Getter
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private final Map<Long, Integer> migratedTargets = new HashMap<>();

	/**
	 * Constructor.
	 *
	 * @param connection Database connection
	 * @param user User
	 * @param customer Customer
	 * @param migratedTagId Id of the common migration tag.
	 * @param migration Migration object
	 */
	public TargetMigration(final Connection connection, final User user, final Customer customer, final Integer migratedTagId, final Customer.Migration migration) {
		this.connection = connection;
		this.user = user;
		this.customer = customer;
		this.migratedTagId = migratedTagId;
		this.migration = migration;

		assetDao = new AssetDaoImpl(connection);
		assetIdentifierDao = new AssetIdentifierDaoImpl(connection);
	}

	/**
	 * Migrate targets.
	 */
	public void migrate() throws SQLException, JAXBException {
		attributes = UserBusiness.getAttributes(connection, user.getMainUserId())
				.stream().filter(a -> a.onTarget() && a.isActive()).collect(Collectors.toList());

		List<Target> targets;
		int count = 0;
		int offset = 0;
		do {
			targets = Target.fetchObjects(Target.class, connection, -1, offset, LIMIT, "xid",
					DbHelper.getSelect(Target.class, Access.createAccess(AccessType.ADMIN, "target"), MigrationUtil.getTargetQuery()),
					user.getMainUserId());
			LOG.debug("Targets: " + targets.size());
			offset = offset + LIMIT;
			for (final Target target : targets) {
				migrate(target);
				count++;
			}
			migration.setMigratedTargets(count);
			MigrationUtil.updateMigration(connection, migration);
			connection.commit();
		}
		while (!targets.isEmpty());
	}

	/**
	 * Migrate a target.
	 *
	 * @param target Target
	 */
	private void migrate(final Target target) throws SQLException, JAXBException {
		final String ip = StringUtils.setEmpty(target.getIpAddress(), target.getLookupIpAddress());
		final String hostname = target.getHostname();

		final Instant firstSeen = getFirstSeen(target);
		final Instant lastSeen = getLastSeen(target);

		final List<AssetIdentifierInterface> assetIdentifiers = new ArrayList<>();

		AssetIdentifierInterface targetAssetIdentifier = null;

		// AWS Instance
		if (!StringUtils.isEmpty(target.getAwsInstanceId())) {
			if (target.getAwsInstanceId().startsWith("i-")) {
				final AssetIdentifierInterface instanceAssetIdentifier = createAssetIdentifier(target.getAwsInstanceId(),
						AssetIdentifierType.AWS_INSTANCE_ID, target, firstSeen, lastSeen);
				assetIdentifiers.add(instanceAssetIdentifier);
				targetAssetIdentifier = instanceAssetIdentifier;
			}
			else if (IpUtils.isHostname(connection, target.getAwsInstanceId())) {
				final AssetIdentifierInterface hostAssetIdentifier = createAssetIdentifier(target.getAwsInstanceId().toLowerCase(),
						AssetIdentifierType.HOSTNAME, target, firstSeen, lastSeen);
				assetIdentifiers.add(hostAssetIdentifier);
				targetAssetIdentifier = hostAssetIdentifier;
			}

			if (targetAssetIdentifier != null) {
				final String accountId = getAwsAccountId(target.getAwsArn());
				if (!StringUtils.isEmpty(accountId)) {
					final AssetIdentifierInterface accountIdAssetIdentifier = createAssetIdentifier(accountId,
							AssetIdentifierType.AWS_ACCOUNT_ID, target, firstSeen, lastSeen);
					assetIdentifiers.add(accountIdAssetIdentifier);
					linkAssetIdentifiers(accountIdAssetIdentifier.getId(), targetAssetIdentifier.getId(),
							firstSeen, lastSeen, AssetIdentifierLinkType.CONTAINS);
				}
			}
		}
		// Agent
		else if (!StringUtils.isEmpty(target.getAgentId())) {
			final AssetIdentifierInterface agentAssetIdentifier = createAssetIdentifier(target.getAgentId(),
					AssetIdentifierType.AGENT, target, firstSeen, lastSeen);
			assetIdentifiers.add(agentAssetIdentifier);
			targetAssetIdentifier = agentAssetIdentifier;
		}
		// IP, Hostname
		else {
			AssetIdentifierInterface ipAssetIdentifier = null;
			if (!StringUtils.isEmpty(ip)) {
				ipAssetIdentifier = createAssetIdentifier(ip.toLowerCase(),
						AssetIdentifierType.IP, target, firstSeen, lastSeen);
				assetIdentifiers.add(ipAssetIdentifier);
				targetAssetIdentifier = ipAssetIdentifier;
			}

			AssetIdentifierInterface hostAssetIdentifier = null;
			if (!StringUtils.isEmpty(hostname) && IpUtils.isHostname(connection, hostname)) {
				hostAssetIdentifier = createAssetIdentifier(hostname.toLowerCase(),
						AssetIdentifierType.HOSTNAME, target, firstSeen, lastSeen);
				assetIdentifiers.add(hostAssetIdentifier);
				if (targetAssetIdentifier == null) {
					targetAssetIdentifier = hostAssetIdentifier;
				}
			}

			if (ipAssetIdentifier != null && hostAssetIdentifier != null) {
				linkAssetIdentifiers(hostAssetIdentifier.getId(), ipAssetIdentifier.getId(),
						firstSeen, lastSeen, AssetIdentifierLinkType.RESOLVES_TO);
			}
		}

		if (targetAssetIdentifier == null) {
			return;
		}

		// MAC
		if (!StringUtils.isEmpty(target.getMacAddress())) {
			final AssetIdentifierInterface macAssetIdentifier = createAssetIdentifier(target.getMacAddress().toLowerCase(),
					AssetIdentifierType.MAC, target, firstSeen, lastSeen);
			assetIdentifiers.add(macAssetIdentifier);
			if (targetAssetIdentifier.getType() == AssetIdentifierType.IP || targetAssetIdentifier.getType() == AssetIdentifierType.AGENT) {
				linkAssetIdentifiers(targetAssetIdentifier.getId(), macAssetIdentifier.getId(), firstSeen, lastSeen,
						targetAssetIdentifier.getType() == AssetIdentifierType.AGENT ? AssetIdentifierLinkType.CONTAINS : AssetIdentifierLinkType.ARP);
			}
		}

		// Virtual Hosts
		if (targetAssetIdentifier.getType() != AssetIdentifierType.AGENT && !StringUtils.isEmpty(target.getVirtualHosts())) {
			final StringTokenizer st = new StringTokenizer(target.getVirtualHosts(), ",\n");
			while (st.hasMoreTokens()) {
				final String virtualHost = st.nextToken().trim();
				if (!StringUtils.isEmpty(virtualHost) && IpUtils.isHostname(connection, virtualHost)) {
					final AssetIdentifierInterface virtualHostAssetIdentifier = createAssetIdentifier(virtualHost.toLowerCase(),
							AssetIdentifierType.HOSTNAME, target, firstSeen, lastSeen);
					assetIdentifiers.add(virtualHostAssetIdentifier);
					if (targetAssetIdentifier.getType() == AssetIdentifierType.IP) {
						linkAssetIdentifiers(virtualHostAssetIdentifier.getId(), targetAssetIdentifier.getId(),
								firstSeen, lastSeen, AssetIdentifierLinkType.RESOLVES_TO);
					}
				}
			}
		}

		final Integer assetId;
		if (targetAssetIdentifier.getType() == AssetIdentifierType.AGENT && !ArrayUtils.isEmpty(targetAssetIdentifier.getAssetIds())) {
			assetId = targetAssetIdentifier.getAssetIds()[0];
		}
		else {
			final Asset asset = new Asset();
			asset.setCustomerId(customer.getId());
			asset.setCreatedById(RestObject.SYSTEM_USER);
			asset.setName(AssetInterface.deriveName(assetIdentifiers));
			asset.setSource(AssetInterface.deriveSources(assetIdentifiers));
			setCvssV2EnvironmentalVector(asset, target);
			setCvssV3EnvironmentalVector(asset, target);
			asset.setMigration((int) target.getId());
			assetId = (int) asset.save(connection);

			final Set<Integer> linkedAssetIdentifiers = new HashSet<>();

			// Link asset identifiers to asset
			for (final AssetIdentifierInterface assetIdentifier : assetIdentifiers) {
				if (linkedAssetIdentifiers.contains(assetIdentifier.getId())) {
					continue;
				}
				final AssetLinkInterface assetLink = assetDao.createAssetLink();
				assetLink.setAssetId(assetId);
				assetLink.setAssetIdentifierId(assetIdentifier.getId());
				assetLink.setFirstSeen(firstSeen);
				assetLink.setLastSeen(lastSeen);
				assetDao.saveAssetLink(assetLink);
				linkedAssetIdentifiers.add(assetIdentifier.getId());
			}
		}
		LOG.debug("asset: " + assetId);

		// Link to migration tag
		DbObject.executeUpdate(connection,
				"INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?) ON CONFLICT DO NOTHING",
				migratedTagId, assetId);

		// Attributes
		for (final Attribute attribute : attributes) {
			final Integer attributeTagId = getAttributeTag(attribute, target);
			if (attributeTagId != null) {
				DbObject.executeUpdate(connection,
						"INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?) ON CONFLICT DO NOTHING",
						attributeTagId, assetId);
			}
		}

		// Scanner
		final Integer scannerTagId = getScannerTag(target);
		if (scannerTagId != null) {
			DbObject.executeUpdate(connection,
					"INSERT INTO tag_asset (tagid, assetid) VALUES (?, ?) ON CONFLICT DO NOTHING",
					scannerTagId, assetId);
		}

		migratedTargets.put(target.getId(), assetId);
	}

	/**
	 * Link two asset identifiers.
	 *
	 * @param assetIdentifierId1 Id of first asset identifier
	 * @param assetIdentifierId2 Id of second asset identifier
	 * @param firstSeen First seen
	 * @param lastSeen Last seen
	 * @param type Link type
	 */
	private void linkAssetIdentifiers(final Integer assetIdentifierId1, final Integer assetIdentifierId2, final Instant firstSeen, final Instant lastSeen, final AssetIdentifierLinkType type)
			throws SQLException {
		if (DbObject.getLong(connection,
				"SELECT COUNT(assetidentifierid1) FROM assetidentifier_assetidentifier WHERE type = ? AND (assetidentifierid1 = ? AND assetidentifierid2 = ? OR assetidentifierid1 = ? AND assetidentifierid2 = ?)",
				type, assetIdentifierId1, assetIdentifierId2, assetIdentifierId2, assetIdentifierId1) > 0) {
			DbObject.executeUpdate(connection,
					"UPDATE assetidentifier_assetidentifier SET firstseen = least(firstseen, ?), lastseen = greatest(lastseen, ?) WHERE type = ? AND (assetidentifierid1 = ? AND assetidentifierid2 = ? OR assetidentifierid1 = ? AND assetidentifierid2 = ?)",
					firstSeen, lastSeen, type, assetIdentifierId1, assetIdentifierId2, assetIdentifierId2, assetIdentifierId1);
		}
		else {
			DbObject.executeUpdate(connection,
					"INSERT INTO assetidentifier_assetidentifier (assetidentifierid1, assetidentifierid2, firstseen, lastseen, type) VALUES (?, ?, ?, ?, ?)",
					assetIdentifierId1, assetIdentifierId2, firstSeen, lastSeen, type);
		}
	}

	/**
	 * Create a scanner tag.
	 *
	 * @param target Target
	 * @return Tag id
	 */
	private Integer getScannerTag(final Target target) throws SQLException {
		if (scannerTags.containsKey(target.getScannerId())) {
			return scannerTags.get(target.getScannerId());
		}

		scannerTags.put(target.getScannerId(), null);

		final ScannerImpl scanner = ScannerImpl.getById(ScannerImpl.class, connection, Access.ADMIN, target.getScannerId());
		if (scanner == null) {
			return null;
		}

		if (scanner.getId() == 0 && !Configuration.isHiabEnabled()) {
			scanner.setName("Outscan");
		}

		final String key = "Scanner";
		final String value = MigrationUtil.sanitizeTag(scanner.getName());

		final String scannerKey = StringUtils.setEmpty(key, "") + ":" + StringUtils.setEmpty(value, "");
		if (createdTags.containsKey(scannerKey)) {
			final Integer tagId = createdTags.get(scannerKey);
			scannerTags.put(target.getScannerId(), tagId);
			return tagId;
		}

		final Integer tagId = MigrationUtil.createTag(connection, customer.getId(), key, value, 0);
		createdTags.put(scannerKey, tagId);
		scannerTags.put(target.getScannerId(), tagId);
		return tagId;
	}

	/**
	 * Create an attribute tag.
	 *
	 * @param attribute Attribute
	 * @param target Target
	 * @return Tag id
	 */
	private Integer getAttributeTag(final Attribute attribute, final Target target) throws SQLException {
		final String key = MigrationUtil.sanitizeTag(attribute.getName());
		String value = MigrationUtil.sanitizeTag(target.getCustom((int) attribute.getColumnId()));
		if (StringUtils.isEmpty(value)) {
			return null;
		}
		if (attribute.getType() == AttributeType.Checkbox) {
			value = StringUtils.getBooleanValue(value) ? "Yes" : "No";
		}

		final String attributeKey = StringUtils.setEmpty(key, "") + ":" + StringUtils.setEmpty(value, "");
		if (createdTags.containsKey(attributeKey)) {
			return createdTags.get(attributeKey);
		}

		final Integer tagId = MigrationUtil.createTag(connection, customer.getId(), key, value, 0);
		createdTags.put(attributeKey, tagId);
		return tagId;
	}

	/**
	 * Create asset identifier.
	 *
	 * @param name Name
	 * @param type Type
	 * @param target Target
	 * @param firstSeen First seen date
	 * @param lastSeen Last seen date
	 * @return Created asset identifier.
	 */
	private AssetIdentifierInterface createAssetIdentifier(final String name, final AssetIdentifierType type,
			final Target target, final Instant firstSeen, final Instant lastSeen) throws SQLException, JAXBException {
		final AssetIdentifierQuery assetIdentifierQuery = AssetIdentifierQuery.builder()
				.name(name.trim())
				.type(type)
				.customerId(customer.getId())
				.scannerId((int) target.getScannerId())
				.notFromSources(Source.getSwatSources().toArray(new Source[0]))
				.build();

		final AssetIdentifier assetIdentifier;
		final List<? extends AssetIdentifierInterface> assetIdentifiers = assetIdentifierDao.getAssetIdentifiers(assetIdentifierQuery);
		if (assetIdentifiers.isEmpty()) {
			assetIdentifier = new AssetIdentifier(RestObject.SYSTEM_USER, customer.getId(), type, name.trim(), null);
			assetIdentifier.setScannerId((int) target.getScannerId());
			assetIdentifier.setFirstSeen(firstSeen);
			assetIdentifier.setMigration((int) target.getId());
			if (type == AssetIdentifierType.AGENT) {
				final AssetIdentifierInterface.AgentProperties properties = new AssetIdentifierInterface.AgentProperties();
				properties.setUuid(name.trim());
				properties.setType(type);
				if (target.getAgentLastSynchronized() != null) {
					properties.setLastSynchronized(target.getAgentLastSynchronized().toInstant());
				}
				if (target.getAgentRetired() != null && target.getAgentRetired()) {
					properties.setRetired(Instant.now());
				}
				properties.setVersion(StringUtils.setEmpty(target.getAgentVersion(), null));
				assetIdentifier.setProperties(properties);
			}
		}
		else {
			assetIdentifier = (AssetIdentifier) assetIdentifiers.get(0);
			if (assetIdentifier.getFirstSeen() == null || assetIdentifier.getFirstSeen().isAfter(firstSeen)) {
				assetIdentifier.setFirstSeen(firstSeen);
			}
		}

		setPlatform(assetIdentifier, target);
		assetIdentifier.addSource(Source.NETSEC);
		assetIdentifier.setUpdatedById(RestObject.SYSTEM_USER);
		if (assetIdentifier.getLastSeen() == null || assetIdentifier.getLastSeen().isBefore(lastSeen)) {
			assetIdentifier.setLastSeen(lastSeen);
		}
		final Integer assetIdentifierId = assetIdentifierDao.saveAssetIdentifier(assetIdentifier);
		assetIdentifier.setId(assetIdentifierId);
		LOG.debug("assetIdentifier: " + type + " " + name + " : " + assetIdentifierId);

		// Link to migration tag if asset identifier was migrated
		if (assetIdentifiers.isEmpty()) {
			DbObject.executeUpdate(connection,
					"INSERT INTO tag_assetidentifier (tagid, assetidentifierid) VALUES (?, ?) ON CONFLICT DO NOTHING",
					migratedTagId, assetIdentifierId);
		}

		return assetIdentifier;
	}

	/**
	 * Set platform on asset identifier.
	 *
	 * @param assetIdentifier Asset identifier
	 * @param target Target
	 */
	private void setPlatform(final AssetIdentifier assetIdentifier, final Target target) {
		if (!StringUtils.isEmpty(target.getPlatform()) && !target.getPlatform().equals("NOTDETECTED") && !target.getPlatform().equals("Unknown")) {
			assetIdentifier.setPlatform(target.getPlatform());
		}
	}

	/**
	 * Set cvss v2 environmental vector on asset;
	 *
	 * @param asset Asset
	 * @param target Target
	 */
	private void setCvssV2EnvironmentalVector(final Asset asset, final Target target) {
		final String cdp = StringUtils.setEmpty(target.getCvssCdp(), "ND");
		final String td = StringUtils.setEmpty(target.getCvssTd(), "ND");
		final String cr = StringUtils.setEmpty(target.getCvssSrConf(), "ND");
		final String ir = StringUtils.setEmpty(target.getCvssSrInteg(), "ND");
		final String ar = StringUtils.setEmpty(target.getCvssSrAvail(), "ND");

		final String vector = "CDP:" + cdp + "/TD:" + td + "/CR:" + cr + "/IR:" + ir + "/AR:" + ar;
		if (vector.equals("CDP:ND/TD:ND/CR:ND/IR:ND/AR:ND")) {
			return;
		}

		try {
			final CvssV2 cvss = new CvssV2();
			cvss.parseEnvironmentalVector(vector);
			asset.setCvssV2EnvironmentalVector(vector);
		}
		catch (final InvalidCVSSException e) {
			LOG.info("Invalid cvss v2 vector: " + vector);
		}
	}

	/**
	 * Set cvss v3 environmental vector on asset;
	 *
	 * @param asset Asset
	 * @param target Target
	 */
	private void setCvssV3EnvironmentalVector(final Asset asset, final Target target) {
		final String cr = StringUtils.setEmpty(target.getCvssSrConf(), "X").replaceAll("ND", "X");
		final String ir = StringUtils.setEmpty(target.getCvssSrInteg(), "X").replaceAll("ND", "X");
		final String ar = StringUtils.setEmpty(target.getCvssSrAvail(), "X").replaceAll("ND", "X");

		final String vector = "CR:" + cr + "/IR:" + ir + "/AR:" + ar;
		if (vector.equals("CR:X/IR:X/AR:X")) {
			return;
		}

		try {
			final CvssV3 cvss = new CvssV3();
			cvss.parseEnvironmentalVector(vector);
			asset.setCvssV3EnvironmentalVector(vector);
		}
		catch (final InvalidCVSSException e) {
			LOG.info("Invalid cvss v3 vector: " + vector);
		}
	}

	/**
	 * Get AWS account id from arn.
	 *
	 * @param arn Arn
	 * @return AWS account id
	 */
	private String getAwsAccountId(final String arn) {
		if (StringUtils.isEmpty(arn)) {
			return null;
		}
		final Pattern pattern = Pattern.compile("^arn:aws:iam::(.*?):role(.*)$");
		final Matcher matcher = pattern.matcher(arn);
		if (matcher.find()) {
			return matcher.group(1);
		}
		return null;
	}

	/**
	 * Get first seen from target.
	 *
	 * @param target Target
	 * @return Date
	 */
	private Instant getFirstSeen(final Target target) {
		final Optional<Date> firstDate = Arrays.asList(target.getLastDiscoveryDate(), target.getLatestScanDate(), target.getLatestSuccessfulScanDate())
				.stream().filter(x -> x != null).max(Comparator.naturalOrder());
		return firstDate.orElse(new Date()).toInstant();
	}

	/**
	 * Get last seen from target.
	 *
	 * @param target Target
	 * @return Date
	 */
	private Instant getLastSeen(final Target target) {
		final Optional<Date> firstDate = Arrays.asList(target.getLastDiscoveryDate(), target.getLatestScanDate(), target.getLatestSuccessfulScanDate())
				.stream().filter(x -> x != null).min(Comparator.naturalOrder());
		return firstDate.orElse(new Date()).toInstant();
	}
}
