package com.chilicoders.service;

import java.io.IOException;
import java.io.LineNumberReader;
import java.io.StringReader;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.transaction.Transactional;
import javax.xml.bind.JAXBException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import com.chilicoders.api.EventApiInterface;
import com.chilicoders.api.EventApiInterfaceV2;
import com.chilicoders.core.auditing.api.AuditingService;
import com.chilicoders.core.compliancefinding.api.ComplianceService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.email.api.EmailService;
import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.message.api.MessageService;
import com.chilicoders.core.reporting.api.ReportingService;
import com.chilicoders.core.rule.api.RuleService;
import com.chilicoders.core.scandata.api.ScanPolicyService;
import com.chilicoders.core.scandata.api.ScanStatusService;
import com.chilicoders.core.scandata.api.ScanlogService;
import com.chilicoders.core.scandata.api.ScheduleService;
import com.chilicoders.core.scandata.api.model.ScanResultInterface;
import com.chilicoders.core.scandata.api.model.ScanServiceType;
import com.chilicoders.core.scandata.api.model.ScanStatusInterface;
import com.chilicoders.core.scanner.api.Scanner;
import com.chilicoders.core.scanner.api.ScannerDao;
import com.chilicoders.core.scheduling.api.ScanSchedulingService;
import com.chilicoders.core.scheduling.api.model.ScanResult;
import com.chilicoders.core.targets.api.TargetInfo;
import com.chilicoders.core.targets.api.TargetService;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;
import com.chilicoders.discover.DiscoveryResult;
import com.chilicoders.discover.model.AwsDiscoveryResult;
import com.chilicoders.event.rest.events.ScanConfigurationEvent;
import com.chilicoders.event.rest.events.ScanEvent;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.Event;
import com.chilicoders.model.EventType;
import com.chilicoders.model.ReportData;
import com.chilicoders.model.ReportDataList;
import com.chilicoders.model.ScanLogEntryInterface;
import com.chilicoders.model.ScanTypes;
import com.chilicoders.model.events.properties.EventLargeReportProperties;
import com.chilicoders.report.AppcheckScanImporter;
import com.chilicoders.report.ComplianceReportImporter;
import com.chilicoders.report.CredentialTestReportImporter;
import com.chilicoders.report.DiscoveryReportImporter;
import com.chilicoders.report.LookupReportImporter;
import com.chilicoders.report.NetsecReportImporter;
import com.chilicoders.report.ReportCreateService;
import com.chilicoders.report.SaveReportService;
import com.chilicoders.report.VerifyReportImporter;
import com.chilicoders.ruleengine.RuleException;
import com.chilicoders.util.BaseTimestampThread;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TemplateUtils;

import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Scope;
import co.elastic.apm.api.Transaction;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@Service
@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
public class ScanDataService implements BaseTimestampThread {
	private static final Logger LOG = LogManager.getLogger(ScanDataService.class);

	private final ScanStatusService scanStatusService;

	private final UserService userService;

	private final ScanlogService scanlogService;

	private final TransactionalNativeStatementExecutor statementExecutor;

	private final SaveReportService saveReportService;

	private final ScheduleService scheduleService;

	private final ScanSchedulingService scanSchedulingService;

	private final ReportCreateService reportCreateService;

	private final EmailService emailService;

	private final TargetService targetService;

	private final IpService ipService;

	private final EventApiInterface eventApi;

	private final EventApiInterfaceV2 eventApiV2;

	private final ReportingService reportingService;

	private final AuditingService auditService;

	private final ScannerDao scannerDao;

	private final ConfigurationService configService;

	private final MessageService messageService;

	private final ComplianceService complianceService;

	private final RuleService ruleService;

	private final ScanPolicyService scanPolicyService;

	/**
	 * Creates a scan data service.
	 *
	 * @param scanStatusService Scan status service
	 * @param userService User service
	 * @param scanlogService Scanlog service
	 * @param statementExecutor SQL statement executor
	 * @param saveReportService Save report service
	 * @param scheduleService Schedule service
	 * @param scanSchedulingService Scan scheduling service
	 * @param reportCreateService Report create service
	 * @param emailService Email service
	 * @param targetService Target service
	 * @param ipService Ip service
	 * @param eventApi Event api
	 * @param reportingService Reporting service
	 * @param auditService Auditing service
	 * @param scannerDao Scanner DAO
	 * @param configService Configuration service
	 * @param messageService Messages service
	 * @param complianceService Compliance service
	 * @param ruleService Rule service
	 * @param scanPolicyService Scan policy service
	 * @param eventApiV2 Event api V2
	 */
	@Autowired
	public ScanDataService(final ScanStatusService scanStatusService, final UserService userService,
						   final ScanlogService scanlogService, final TransactionalNativeStatementExecutor statementExecutor, final SaveReportService saveReportService,
						   final ScheduleService scheduleService, final ScanSchedulingService scanSchedulingService, final ReportCreateService reportCreateService,
						   final EmailService emailService, final TargetService targetService, final IpService ipService, final EventApiInterface eventApi,
						   final ReportingService reportingService, final AuditingService auditService, final ScannerDao scannerDao,
						   final ConfigurationService configService, final MessageService messageService, final ComplianceService complianceService,
						   final RuleService ruleService, final ScanPolicyService scanPolicyService,
						   final EventApiInterfaceV2 eventApiV2) {
		this.scanStatusService = scanStatusService;
		this.userService = userService;
		this.scanlogService = scanlogService;
		this.statementExecutor = statementExecutor;
		this.saveReportService = saveReportService;
		this.scheduleService = scheduleService;
		this.scanSchedulingService = scanSchedulingService;
		this.reportCreateService = reportCreateService;
		this.emailService = emailService;
		this.targetService = targetService;
		this.ipService = ipService;
		this.eventApi = eventApi;
		this.eventApiV2 = eventApiV2;
		this.reportingService = reportingService;
		this.auditService = auditService;
		this.scannerDao = scannerDao;
		this.configService = configService;
		this.messageService = messageService;
		this.complianceService = complianceService;
		this.ruleService = ruleService;
		this.scanPolicyService = scanPolicyService;
	}

	/**
	 * Fetches a single report that is ready for being processed.
	 *
	 * @return Scan status to handle, null if not available.
	 */
	@Transactional
	public ScanResultInterface getReport() throws SQLException, ParamValidationException, JAXBException, IOException {
		final ScanStatusInterface result = scanStatusService.getScanWithReadyReport();
		if (result != null) {
			statementExecutor.execute(new NativeSqlStatement(
					"UPDATE tscanstatuss SET reportattempted = NOW() + ((900 * random())::INTEGER || ' SECONDS')::INTERVAL WHERE xid = ?", result.getId()));
		}
		return result;
	}

	public ScanResultInterface getAppcheckScan() throws SQLException {
		return scanStatusService.getAppcheckScanData();
	}

	/**
	 * Handles a report ready for being processed.
	 *
	 * @param scanstatus Scan status
	 */
	@Transactional
	public void handleReport(final ScanResultInterface scanstatus) throws ParamValidationException, SQLException, JAXBException, IOException {
		LOG.debug("Saving report, id: {}", scanstatus.getId());
		final List<com.chilicoders.event.model.Event> eventList = new ArrayList<>();
		final List<Event> eventV1List = new ArrayList<>();
		saveReport(scanstatus, eventList, eventV1List);
		LOG.debug("Saving report complete, id: {}", scanstatus.getId());
		TransactionSynchronizationManager.registerSynchronization(
				new TransactionSynchronization() {
					@Override
					public void afterCommit() {
						for (final Event event : eventV1List) {
							eventApi.handleEvent(event);
						}
						final Map<String, Boolean> hasSubscriberCache = new HashMap<>();
						for (final com.chilicoders.event.model.Event event : eventList) {
							eventApiV2.handleEvent(event, null, hasSubscriberCache);
						}
					}
				});
	}

	/**
	 * Generate report.
	 *
	 * @param scanResult Scan object
	 * @param eventList Event list
	 * @param eventV1List Event v1 list
	 */
	public void saveReport(final ScanResultInterface scanResult, final List<com.chilicoders.event.model.Event> eventList, final List<Event> eventV1List) throws ParamValidationException, JAXBException, SQLException, IOException {
		final Transaction transaction = ElasticApm.startTransaction();
		try (final Scope ignored = transaction.activate()) {
			transaction.setType(Transaction.TYPE_REQUEST);
			transaction.setLabel("user", scanResult.getUserId());

			if (scanResult.getScanEnded() == null) {
				scanResult.setScanEnded(new Date());
			}

			if (scanResult.isDiscovery()) {
				transaction.setName("ScanImport-discovery");
				processDiscoveryScan(scanResult, eventList);
			}
			else if (scanResult.isLookup()) {
				transaction.setName("ScanImport-lookup");
				processLookupScan(scanResult);
			}
			else if (scanResult.isAppcheckScan()) {
				transaction.setName("ScanImport-appcheckscan");
				AppcheckScanImporter.saveScan(scanResult, statementExecutor, saveReportService, eventList);
			}
			else if (scanResult.getService() == ScanServiceType.NETWORK_LOOKUP) {
				transaction.setName("ScanImport-networklookup");
				processNetworkLookup(scanResult, eventList);
			}
			else {
				transaction.setName(String.format("ScanImport-scan-%s", scanResult.getService()));
				if (processScan(scanResult, eventList, eventV1List)) {
					return;
				}
			}

			statementExecutor.commitTransaction();
		}
		catch (final RuntimeException e) {
			transaction.captureException(e);
			throw e;
		}
		finally {
			transaction.end();
		}
	}

	/**
	 * Used to save the report.
	 *
	 * @param scan Scan object
	 * @param report Report data
	 * @param eventList Event list
	 * @param eventV1List Event v1 list
	 * @return Report result
	 */
	private long saveReport(final ScanStatusInterface scan, final ReportDataList report, final List<com.chilicoders.event.model.Event> eventList, final List<Event> eventV1List) {
		LOG.debug("{} in saveReport: {}", Thread.currentThread().getName(), scan.getId());
		try {
			// If it is a verify scan we will add verification information to existing report
			if (scan.getVerifyId() != 0) {
				if (scan.isActivateCompliance()) {
					ComplianceReportImporter.saveComplianceUpdate(statementExecutor, scanlogService, complianceService, scan, report);
				}
				else {
					VerifyReportImporter.saveVerifyResult(reportCreateService, reportingService, userService, statementExecutor, scan, report);
					statementExecutor.commitTransaction();
				}
			}
			else if (scan.getScanlessReportId() > 0 && scan.getService() != ScanServiceType.NETWORK_SCAN) {
				final long result = NetsecReportImporter.updateReport(configService, reportingService, userService, targetService, ipService, ruleService, scanPolicyService,
						scheduleService, reportCreateService, messageService, statementExecutor, scan, report, eventV1List);
				statementExecutor.commitTransaction();
				return result;
			}
			else if (scan.getService() == ScanServiceType.TestCredentials) {
				CredentialTestReportImporter.credentialTestCompleted(statementExecutor, scan, report);
				statementExecutor.commitTransaction();
			}
			else {
				return NetsecReportImporter.saveReport(configService, saveReportService, statementExecutor, userService, complianceService, scanlogService, scanStatusService,
						ipService, targetService, ruleService, reportingService, scanPolicyService, reportCreateService, scan, report, eventList, this, eventV1List);
			}
		}
		catch (final SQLException | RuntimeException e) {
			LOG.error("{} - saveReport failed: {}", Thread.currentThread().getName(), scan.getId(), e);
		}

		return 1;
	}

	/**
	 * Processes a scan report
	 *
	 * @param scanResult {@link ScanStatusInterface} the scan report
	 * @param eventList A {@link List} of {@link com.chilicoders.event.model.Event}s to be published
	 * @param eventV1tList A {@link List} of {@link Event}s to be published
	 * @return {@code true} if scan failed, {@code false} otherwise
	 * @throws IOException In case of error
	 * @throws SQLException In case of error
	 */
	private boolean processScan(final ScanResultInterface scanResult, final List<com.chilicoders.event.model.Event> eventList, final List<Event> eventV1tList) throws IOException, SQLException {
		final ScanStatusInterface scan = (ScanStatusInterface) scanResult;
		final ReportDataList report = getReportDataList(scan);
		scan.setReport(null);

		final int maxPortsTerminate = configService.getProperty(ConfigKeys.ConfigurationIntKey.report_maxports_terminate);
		if (scan.getOpenPorts() > maxPortsTerminate) {
			reportTooManyOpenPorts(scan);
		}
		else {
			final long reportId = saveReport(scan, report, eventList, eventV1tList);
			if (reportId < 0) {
				scan.setScanResult(reportId == -2 ? ScanResult.Failed : ScanResult.Timeout);
				scanSchedulingService.writeScanlog(scan, null, true);
				return true;
			}
			else if (reportId > 0) {
				scan.setScanResult(ScanResult.OK);
				try {
					scanSchedulingService.writeScanlog(scan, null, true);
					final TargetInfo target = targetService.getById(userService.getUserDetails(scan.getUserId(), false), scan.getTargetId());
					if (target != null && !StringUtils.isEmpty(target.getCompliancesEnabled())) {
						final UserDetails user = userService.getUserDetails(scan.getUserId(), false);
						ComplianceReportImporter.sendCompliance(statementExecutor, complianceService, eventApi, configService, user, scan.getUserId(), target,
								scan.getScanJobId());
					}
				}
				catch (final SQLException | RuleException | IOException e) {
					LOG.error("{} - Compliance report not created", Thread.currentThread().getName(), e);
				}
			}
			// 0 means it was a WAS scan and the scanlogs have already been written
		}
		LOG.debug("{} - Scan is done for host : {}", Thread.currentThread().getName(), scan.getTarget());
		if (scan.getService() == ScanServiceType.AppsecScale
				|| scan.getService() == ScanServiceType.DOCKER_SCAN
				|| scan.getService() == ScanServiceType.CLOUDSEC
				|| scan.getService() == ScanServiceType.NETWORK_SCAN
				|| scan.getService() == ScanServiceType.TestCredentials) {
			addScanEventDone(eventList, scan);
		}
		return false;
	}

	/**
	 * Reports too many open ports failure
	 *
	 * @param scan {@link ScanStatusInterface} The scan result
	 */
	private void reportTooManyOpenPorts(final ScanStatusInterface scan) {
		final EventLargeReportProperties properties = EventLargeReportProperties.builder()
				.policyName(scan.getTemplateName())
				.target(scan.getTarget())
				.startTime(scan.getScanStarted())
				.endTime(scan.getScanEnded())
				.targetId(scan.getTargetId())
				.portCount(scan.getOpenPorts())
				.subuserId(scan.getSubUserId() == null ? -1 : scan.getSubUserId())
				.build();

		sendNotification("A target with "
				+ scan.getOpenPorts()
				+ " ports has been detected. Due to the likelihood of this being an anomaly, the scanner has been terminated "
				+ "and no report will be available for this target.\n\n"
				+ "Customer : "
				+ scan.getUserId()
				+ "\n"
				+ (!StringUtils.isEmpty(scan.getCompanyName()) ? "Customer name : " + scan.getCompanyName() + "\n" : "")
				+ "Host : "
				+ scan.getTarget()
				+ "\n");

		try {
			scanSchedulingService.writeScanlog(scan, ScanTypes.KilledLarge, true);
			final Event event = new Event(Event.O24Event.LargeReport, properties, EventType.Unspecified, scan.getUserId());
			eventApi.handleEvent(event);
		}
		catch (final IOException e) {
			LOG.error("Error saving large report", e);
		}
	}

	/**
	 * Processes a Lookup scan
	 *
	 * @param scanResult {@link ScanResultInterface} the scan result
	 * @throws JAXBException In case of error
	 * @throws IOException In case of error
	 */
	private void processLookupScan(final ScanResultInterface scanResult) throws JAXBException, IOException {
		final ScanStatusInterface scan = (ScanStatusInterface) scanResult;
		final List<DiscoveryResult> discoveryResults = unmarshalDiscoveryResults(scan.getReport(), scan);
		scan.setReport(null);
		try {
			LookupReportImporter.saveLookupResult(statementExecutor, scan, discoveryResults);
			scanSchedulingService.writeScanlog(scan, ScanTypes.Ok, true);
		}
		catch (final IOException e) {
			LOG.error("Error saving lookup result", e);
		}
	}

	/**
	 * Processes s Discovery scan
	 *
	 * @param scanResult {@link ScanResultInterface} the scan result
	 * @param eventList A {@link List} of {@link com.chilicoders.event.model.Event}s to be published
	 * @throws JAXBException In case of error
	 * @throws SQLException In case of error
	 * @throws ParamValidationException In case of error
	 * @throws IOException In case of error
	 */
	private void processDiscoveryScan(final ScanResultInterface scanResult, final List<com.chilicoders.event.model.Event> eventList)
			throws JAXBException, SQLException, ParamValidationException, IOException {
		final ScanStatusInterface scan = (ScanStatusInterface) scanResult;
		final String reportData = scan.getReport();
		scan.setReport(null);

		if (scan.isNetworkDiscovery() || scan.isCloudDiscovery() || scan.isDockerDiscovery()) {
			DiscoveryReportImporter.saveDiscoveryReport(statementExecutor, saveReportService, scanlogService, scanStatusService, scan, reportData, eventList);
			LOG.debug("{} - Discovery is done for xid:{}", Thread.currentThread().getName(), scan.getId());
			addScanEventDone(eventList, scan);
		}
		else {
			final List<DiscoveryResult> discoveryResults;
			if (configService.isKubernetesEnabled() && scan.isAmazonDiscovery()) {
				LOG.debug("Handle scan result for Aws discovery.");
				discoveryResults = handleAmazonData(reportData, scan);
				if (discoveryResults == null) {
					return;
				}
			}
			else if (scan.isAgentDiscovery()) {
				LOG.debug("Handle scan result for agent discovery.");
				discoveryResults = MarshallingUtils.unmarshalList(DiscoveryResult.class, reportData);
			}
			else {
				discoveryResults = unmarshalDiscoveryResults(reportData, scan);
			}
			LOG.debug("Discovery results: {}", discoveryResults);
			final long reportId =
					new DiscoveryReportImporter().saveNetsecDiscoveryReport(statementExecutor, scheduleService, userService, reportCreateService, emailService,
							targetService, ipService, eventApi, reportingService, auditService, scannerDao, configService, messageService, this, scan,
							discoveryResults);
			scan.setReportEntryId(reportId);
			if (reportId > 0) {
				scanSchedulingService.writeScanlog(scan, ScanTypes.DiscoveryDone, true);
				LOG.debug("{} - Discovery is done for xid: {}", Thread.currentThread().getName(), scan.getTarget());
			}
		}
	}

	/**
	 * Processes a network lookup scan.
	 *
	 * @param scanResult {@link ScanResultInterface} the scan result.
	 * @param eventList A {@link List} of {@link com.chilicoders.event.model.Event}s to be published.
	 * @throws SQLException In case of error.
	 */
	private void processNetworkLookup(final ScanResultInterface scanResult, final List<com.chilicoders.event.model.Event> eventList) throws SQLException {
		final ScanStatusInterface scan = (ScanStatusInterface) scanResult;
		final String reportData = scan.getReport();

		final JSONObject reportDataJson = new JSONObject(reportData);

		// Extract customer ID and scan configuration ID from the report data
		final int customerId = reportDataJson.getInt("customerId");
		final int scanConfigurationId = reportDataJson.getInt("scanConfigurationId");

		final Set<Integer> assetIds = new HashSet<>();
		final JSONArray assetIdentifierMap = reportDataJson.getJSONArray("assetIdentifierMap");
		for (int i = 0; i < assetIdentifierMap.length(); i++) {
			final JSONObject entry = assetIdentifierMap.getJSONObject(i);
			assetIds.add(entry.getInt("assetId"));
		}

		// Create a scan data JSON object from the report data
		final JSONObject scanData = new JSONObject();
		scanData.put("source", ScanServiceType.NETWORK_LOOKUP);
		scanData.put("ipToTargetsMap", reportDataJson.getJSONArray("ipToTargetsMap"));
		scanData.put("assetIds", assetIds.toArray(new Integer[0]));
		scanData.put("assetIdentifierMap", reportDataJson.getJSONArray("assetIdentifierMap"));
		scanData.put("virtualHostMap", reportDataJson.getJSONArray("virtualHostMap"));
		scanData.put("invocationId", reportDataJson.getInt("invocationId"));

		// Insert the scan data into the scanqueue table so the network scan can be re-triggered
		statementExecutor.execute(new NativeSqlStatement(
				"INSERT INTO scanqueue (customerid, scanconfigurationid, scandata) VALUES (?, ?, ?::jsonb)",
				customerId, scanConfigurationId, scanData.toString()));

		addScanEventDone(eventList, scan);
	}

	/**
	 * Handle amazon data discovery results.
	 *
	 * @param report The scan report.
	 * @param scan The {@link ScanStatusInterface} instance.
	 * @return A list of discoveryresults or null if all regions failed.
	 */
	public List<DiscoveryResult> handleAmazonData(final String report, final ScanStatusInterface scan) throws JsonProcessingException {
		final AwsDiscoveryResult awsDiscoveryResult = AwsDiscoveryResult.deserialize(report);
		if (Boolean.TRUE.equals(awsDiscoveryResult.getFailuresInAllRegions())) {
			LOG.info("AWS failures occurred in all regions");
			scan.setReason("AWS failures occurred in all regions.");
			scan.setScanResult(ScanResult.Failed);
			return null;
		}
		if (!CollectionUtils.isEmpty(awsDiscoveryResult.getFailuresInRegions())) {
			LOG.info("List of Amazon regions where failures occured: {}", String.join(", ", awsDiscoveryResult.getFailuresInRegions()));
			scan.setReason("Warning failed regions: " + String.join(", ", awsDiscoveryResult.getFailuresInRegions()) + ". ARN: " + awsDiscoveryResult.getRoleArn());
		}
		return awsDiscoveryResult.convertToDiscoveryResults();

	}

	/**
	 * Unmarshals netsec discovery data. Data comes in different formats from argo workflow scans and rpm-based scans.
	 * This is taken care of here so other methods can use the DiscoveryResult class to handle results.
	 *
	 * @param reportData Report data recieved from the scan.
	 * @param scan Scan for which to parse data.
	 * @return List of DiscoveryResults.
	 */
	public List<DiscoveryResult> unmarshalDiscoveryResults(final String reportData, final ScanStatusInterface scan) throws JAXBException, IOException {
		if (StringUtils.isEmpty(reportData)) {
			LOG.debug("Scan report data is empty");
			return Collections.emptyList();
		}
		if (configService.isKubernetesEnabled() && scan.getScannerId() == Scanner.LOCAL_SCANNER) {
			final LineNumberReader reader = new LineNumberReader(new StringReader(reportData));
			String line = reader.readLine();
			final List<DiscoveryResult> result = new ArrayList<>();
			while (line != null) {
				result.add(new DiscoveryResult(line));
				line = reader.readLine();
			}
			return result;
		}
		return MarshallingUtils.unmarshalList(DiscoveryResult.class, reportData);
	}

	@Override
	public void heartbeat(final String status) {
	}

	@Override
	public void sendStatus() {
	}

	/**
	 * Used to send a message to the administrator.
	 *
	 * @param msg The message to send to the administrator
	 */
	private void sendNotification(final String msg) {
		LOG.debug("in sendNotification()");
		if (configService.getProperty(ConfigKeys.ConfigurationIntKey.reportthread_count) > 0) {
			try {
				final String from = configService.isHiabEnabled()
						? configService.getProperty(ConfigKeys.ConfigurationKey.mail_fromaddresshiab)
						: configService.getProperty(ConfigKeys.ConfigurationKey.mail_fromaddress);
				String email = configService.getProperty(ConfigKeys.ConfigurationKey.mail_supportemail);

				if (configService.isHiabEnabled()) {
					final UserDetails user = userService.getUserDetails(UserService.HIABUSERID, false);
					if (user != null) {
						email = user.getEmail();
					}
				}

				final Map<String, String> tags = new HashMap<>();
				tags.put("ORGANIZATION", configService.getProperty(ConfigKeys.ConfigurationKey.company));
				final String signature = TemplateUtils.replaceTags(messageService.getMessage("signature", "en", "TEMPLATE"), tags);
				emailService.sendEmail(email, from, msg + (StringUtils.isEmpty(signature) ? "" : signature), "Report notification message:", tags);
			}
			catch (final Exception e) {
				LOG.error("sendNotification failed", e);
			}
		}
	}

	/**
	 * Add scan event done to the event list
	 *
	 * @param eventList the event list
	 * @param scan the scan status
	 */
	private void addScanEventDone(final List<com.chilicoders.event.model.Event> eventList, final ScanStatusInterface scan) throws SQLException {
		final ScanLogEntryInterface scanLog = scanlogService.getPortalScanLog((int) scan.getTargetId());
		if (scanLog != null) {
			final ScanEvent scanEvent = new ScanEvent(scanLog.getId(), scanLog.getCustomerId(), null, scanLog.getScheduleId(),
					scanLog.getScanConfigurationId(), scanLog.getWorkflowId());
			eventList.add(scanEvent.done());
			if (!scanlogService.hasStillNotCompletedScanLogs(scanLog.getScanConfigurationId(), scanLog.getId())) {
				final ScanConfigurationEvent scanConfigurationEvent = new ScanConfigurationEvent(scanLog.getScanConfigurationId(), scanLog.getCustomerId(),
						null, scanLog.getScheduleId(), scanLog.getWorkflowId());
				eventList.add(scanConfigurationEvent.done());
			}
		}
	}

	/**
	 * Process an AppCheck scan
	 * Must be performed within a transaction to avoid concurrency issues
	 *
	 * @return {@code true} if a scan result was found, code {@code false} otherwise
	 */
	@Transactional
	public boolean processAppcheckScan() throws SQLException, ParamValidationException, JAXBException, IOException {
		final ScanResultInterface scan = getAppcheckScan();
		if (scan != null) {
			handleReport(scan);
			return true;
		}
		return false;
	}

	/**
	 * Build a {@link ReportDataList} instance from scan report.
	 *
	 * @param scan The {@link ScanStatusInterface} instance.
	 * @return A {@link ReportDataList} instance.
	 * @throws IOException The {@link IOException} exception.
	 */
	private ReportDataList getReportDataList(final ScanStatusInterface scan) throws IOException {
		if (scan.getReport() == null) {
			return new ReportDataList();
		}
		if (configService.isKubernetesEnabled() && scan.getService() == ScanServiceType.CLOUDSEC) {
			final ReportDataList report = new ReportDataList();
			report.getReportData().add(ReportData.builder().result(scan.getReport()).build());
			return report;
		}
		return new XmlMapper().readValue(scan.getReport(), ReportDataList.class);
	}
}
