package com.chilicoders.service.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.usertype.UserType;

import com.chilicoders.bl.objects.DataSet;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.db.objects.api.DataSetInterface;
import com.chilicoders.db.query.NativeSqlStatement;
import com.chilicoders.db.query.NativeStatementExecutor;
import com.chilicoders.db.query.TransactionalNativeStatementExecutor;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Support for executing native sql statement using dblayer.
 */
public class DbLayerNativeStatementExecutor implements TransactionalNativeStatementExecutor, NativeStatementExecutor {

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection conn;

	public DbLayerNativeStatementExecutor(final Connection conn) {
		this.conn = conn;
	}

	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	public Connection getConnection() {
		return conn;
	}

	@Override
	public int execute(final NativeSqlStatement statement) throws SQLException {
		return DbObject.execute(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public String getString(final NativeSqlStatement statement) throws SQLException {
		return DbObject.getString(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Map<String, String> getMappedValues(final NativeSqlStatement statement) throws SQLException {
		final Map<String, String> valuesAsStrings = new HashMap<>();
		final Map<Object, Object> valuesMap = DbObject.getMappedValues(conn, statement.getSQL(), statement.getParameters());
		valuesMap.forEach((key, value) -> valuesAsStrings.put(key.toString(), value.toString()));
		return valuesAsStrings;
	}

	@Override
	public Boolean getBoolean(final NativeSqlStatement statement) throws SQLException {
		return DbObject.getBoolean(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Long getLong(final NativeSqlStatement statement) throws SQLException {
		return DbObject.executeCountQuery(conn, statement.getSQL(), statement.getParameters());
	}

	public Long[] getLongArray(final NativeSqlStatement statement) throws SQLException {
		return DbObject.getLongArray(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Integer[] getIntegerArray(final NativeSqlStatement statement, final String xidColumnAlias) throws SQLException {
		return getIntegerArray(statement);
	}

	public Integer[] getIntegerArray(final NativeSqlStatement statement) throws SQLException {
		return DbObject.getIntegerArray(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Date getDate(final NativeSqlStatement statement) throws SQLException {
		return (Date) DbObject.getObject(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Timestamp getTimestamp(final NativeSqlStatement statement) throws SQLException {
		return (Timestamp) DbObject.getObject(conn, statement.getSQL(), statement.getParameters());
	}

	public <T> Set<T> getPrimitiveSetResult(final NativeSqlStatement statement) throws SQLException {
		return DbObject.fetchSet(conn, statement.getSQL(), statement.getParameters());
	}

	/**
	 * Executes a native sql query that is expected to return one result.
	 * If there are many results retrieved, the method returns null.
	 *
	 * @param statement the sql statement to execute
	 * @param type the object type to retrieve
	 * @param <T> ...
	 * @return the retrieved object or null if the query returns no result or returns multiple results.
	 */
	public <T extends DbObject> T getSingleResult(final NativeSqlStatement statement, final Class<T> type) throws SQLException {
		final List<T> res = getListResult(statement, type);
		if (res.size() == 1) {
			return res.get(0);
		}
		return null;
	}

	public <T extends DbObject> List<T> getListResult(final NativeSqlStatement statement, final Class<T> type) throws SQLException {
		return DbObject.fetchObjects(type, conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public List<Map<String, Object>> getGenericObjects(final NativeSqlStatement statement) throws SQLException {
		return XmlAble.getGenericObjects(getConnection(), statement.getSQL(), statement.getParameters());
	}

	@Override
	public void startTransaction() {
		// nothing to to, we manipulate the connection directly
	}

	@Override
	public void commitTransaction() throws SQLException {
		conn.commit();
	}

	@Override
	public void rollbackTransaction() throws SQLException {
		conn.rollback();
	}

	@Override
	public void restartTransaction() throws SQLException {
		conn.rollback();
		conn.setAutoCommit(false);
	}

	@Override
	public DataSetInterface getScrollableResult(final NativeSqlStatement statement) {
		final DataSet ds = new DataSet(conn);
		ds.load(statement.getSQL(), statement.getParameters());
		return ds;
	}

	@Override
	public void freeResources() {
		DbAccess.getInstance().freeConnection(conn);
	}

	@Override
	public Long[] getIdsArray(final NativeSqlStatement statement, final String xidColumnAlias) throws SQLException {
		return getLongArray(statement);
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> List<T> getObjectList(final NativeSqlStatement statement, final Class<T> clazz) throws SQLException {
		if (DbObject.class.isAssignableFrom(clazz)) {
			final Class<? extends DbObject> clz = (Class<? extends DbObject>) clazz;
			return (List<T>) DbObject.fetchObjects(clz, conn, statement.getSQL(), statement.getParameters());
		}
		return DbObject.fetchList(conn, statement.getSQL(), statement.getParameters());
	}

	@Override
	public Object getNullValueObject(final Object value, final UserType userType) {
		return value;
	}

	@Override
	public Object createArrayOf(final String type, final String[] data) throws SQLException {
		return conn.createArrayOf(type, data);
	}

	@Override
	public String[] getStringArray(final NativeSqlStatement statement, final String xidColumnAlias) throws SQLException {
		return DbObject.getStringArray(conn, statement.getSQL(), statement.getParameters());
	}
}
