package com.chilicoders.service.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.json.JSONObject;

import com.chilicoders.core.scandata.api.dao.MatchDao;
import com.chilicoders.db.Access;
import com.chilicoders.model.MatchInterface;
import com.chilicoders.model.MatchType;
import com.chilicoders.rest.models.Match;
import com.chilicoders.rest.models.Service;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class MatchDaoImpl implements MatchDao {
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection conn;

	public MatchDaoImpl(final Connection conn) {
		this.conn = conn;
	}

	@Override
	public List<? extends MatchInterface> listMatches(final Integer customerId, final Integer assetId) throws SQLException {
		return Match.fetchObjects(Match.class, this.conn, Access.ADMIN, "customerid = ? AND assetid = ? AND deleted IS NULL ORDER BY lastseen DESC",
				customerId, assetId);
	}

	@Override
	public int save(final MatchInterface match) throws SQLException {
		return (int) ((Match) match).save(conn);
	}

	@Override
	public MatchInterface getById(final int matchId) throws SQLException {
		return Match.getById(Match.class, this.conn, matchId);
	}

	@Override
	public List<? extends MatchInterface> getByType(final Integer customerId, final MatchType type) throws SQLException {
		return Match.fetchObjects(Match.class, this.conn, Access.ADMIN, "customerid = ? AND type = ? AND deleted IS NULL",
				customerId, type);
	}

	@Override
	public MatchInterface createMatch(final Integer createdById, final Integer customerId, final Integer assetId, final Service service, final MatchType type,
									  final JSONObject json) {
		return new Match(createdById, customerId, assetId, service, type, json);
	}
}
