package com.chilicoders.service.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.tuple.Pair;

import com.chilicoders.bl.BusinessObject;
import com.chilicoders.core.assets.api.AssetDao;
import com.chilicoders.db.Access;
import com.chilicoders.model.AssetIdentifierInterface;
import com.chilicoders.model.AssetIdentifierType;
import com.chilicoders.model.AssetInterface;
import com.chilicoders.model.AssetLinkInterface;
import com.chilicoders.rest.models.Asset;
import com.chilicoders.rest.models.AssetIdentifier;
import com.chilicoders.rest.models.AssetLink;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class AssetDaoImpl implements AssetDao {
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final Connection connection;

	public AssetDaoImpl(final Connection connection) {
		this.connection = connection;
	}

	@Override
	public AssetInterface getByUuid(final String assetUuid) throws SQLException {
		return Asset.getByField(Asset.class, connection, Access.ADMIN, "uuid", UUID.fromString(assetUuid));
	}

	@Override
	public List<? extends AssetLinkInterface> getAssetLinks(final Integer assetId, final Integer[] assetIdentifierIds) throws SQLException {
		return AssetLink.fetchObjects(AssetLink.class, connection, Access.ADMIN, "assetid = ? AND assetidentifierid =ANY (?)", assetId, assetIdentifierIds);
	}

	@Override
	public AssetLinkInterface getAssetLinkByAssetIdAndAssetIdentifierId(final Integer assetId, final Integer assetIdentifierId) throws SQLException {
		final List<Object> params = new ArrayList<>();
		params.add(assetId);
		params.add(assetIdentifierId);
		return AssetLink.getObject(AssetLink.class, connection, null, new BusinessObject.DbObjectSendConfiguration(Access.ADMIN), null, null, "assetid = ? AND assetidentifierid = ?", params);
	}

	@Override
	public AssetInterface createAsset() {
		return new Asset();
	}

	@Override
	public Integer saveAsset(final AssetInterface asset) throws SQLException {
		return (int) ((Asset) asset).save(connection);
	}

	@Override
	public AssetLinkInterface createAssetLink() {
		return new AssetLink();
	}

	@Override
	public void saveAssetLink(final AssetLinkInterface assetLink) throws SQLException {
		((AssetLink) assetLink).save(connection);
	}

	@Override
	public List<? extends AssetInterface> getByIds(final Integer[] ids) throws SQLException {
		return Asset.fetchObjects(Asset.class, connection, Access.ADMIN, "id =ANY (?)", (Object) ids);
	}

	@Override
	public AssetIdentifierInterface createAssetIdentifier(final Integer createdById, final Integer customerId, final AssetIdentifierType type, final String name) {
		return new AssetIdentifier(createdById, customerId, type, name);
	}

	@Override
	public AssetInterface getById(final Integer assetId) throws SQLException {
		return Asset.getById(Asset.class, connection, assetId);
	}

	@Override
	public List<? extends AssetInterface> getAssets(final AssetQuery query) throws SQLException {
		final Pair<StringBuilder, List<Object>> queryParam = buildQuery(query, false);
		return Asset.fetchObjects(Asset.class, this.connection, Access.ADMIN, queryParam.getLeft().toString(), queryParam.getRight());
	}
}
