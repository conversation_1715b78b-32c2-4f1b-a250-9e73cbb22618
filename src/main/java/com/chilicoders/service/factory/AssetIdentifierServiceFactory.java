package com.chilicoders.service.factory;

import java.sql.Connection;

import com.chilicoders.core.assetidentifier.api.AssetIdentifierService;
import com.chilicoders.core.assets.impl.AssetIdentifierServiceImpl;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.dao.AssetDaoImpl;
import com.chilicoders.service.dao.AssetIdentifierDaoImpl;
import com.chilicoders.service.dao.DbLayerNativeStatementExecutor;

public class AssetIdentifierServiceFactory {

	public static AssetIdentifierService newAssetIdentifierService(final Connection connection, final ConfigurationService configService) {
		return new AssetIdentifierServiceImpl(new AssetIdentifierDaoImpl(connection), configService,
				new DbLayerNativeStatementExecutor(connection), ServiceProvider.getAssetService(connection), new AssetDaoImpl(connection));
	}

}
