package com.chilicoders.util;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

public class Scp {
	/**
	 * Logger.
	 */
	private static final Logger LOG = LogManager.getLogger(Scp.class);
	/**
	 * Error message when doing backups.
	 */
	private static final String BACKUP_FILE_NOT_FOUND = "Could not find local file";
	/**
	 * SCP acknowledgment not received.
	 */
	private static final String SCP_ACKNOWLEDGEMENT_NOT_RECEIVED = "Did not get scp acknowledgement";
	/**
	 * Not properly configured.
	 */
	private static final String SCP_NOT_CONFIGURED = "Not properly configured";

	/**
	 * Configuration for scp connection.
	 * HOST
	 * PORT
	 * USERNAME
	 * PASSWORD
	 * KEY PASSWORD
	 * PRIVATE KEY
	 */
	private Map<String, String> configuration = null;

	/**
	 * Scp session.
	 */
	private JSch jsch = null;

	private Session session = null;

	public Scp(final Map<String, String> configuration) {
		this.configuration = configuration == null ? null : Collections.unmodifiableMap(configuration);
	}

	/**
	 * Connect to the remote server.
	 *
	 * @return Error message or null on success.
	 */
	private String setupConnection() {
		if (this.configuration == null) {
			return SCP_NOT_CONFIGURED;
		}

		try {
			final String host = StringUtils.setEmpty(this.configuration.get("HOST"), "");
			final int port = StringUtils.getIntValue(this.configuration.get("PORT"), 22);
			final String username = StringUtils.setEmpty(this.configuration.get("USERNAME"), "");
			final String password = StringUtils.setEmpty(this.configuration.get("PASSWORD"), "");
			final String keypassword = StringUtils.setEmpty(this.configuration.get("KEYPASSWORD"), "");
			final String key = StringUtils.setEmpty(this.configuration.get("PRIVATE KEY"), "");

			jsch = new JSch();

			if (!StringUtils.isEmpty(key)) {
				jsch.addIdentity(username, key.getBytes(UTF_8), null, StringUtils.isEmpty(keypassword) ? null : keypassword.getBytes(UTF_8));
			}

			session = jsch.getSession(username, host, port);
			session.setConfig("StrictHostKeyChecking", "no");

			if (StringUtils.isEmpty(key) && !StringUtils.isEmpty(password)) {
				session.setPassword(password);
			}

			LOG.info("Connecting to: " + host);
			session.connect(30 * 1000);
		}
		catch (final JSchException e) {
			final String error = e.getMessage();
			if (error.equals("Auth fail")) {
				LOG.info("Error authenticating SCP connection");
			}
			else if (error.startsWith("java.net.ConnectException")) {
				LOG.info("Error connecting to SCP server");
			}
			else {
				LOG.info("Error setting up SCP connection", e);
			}
			return e.getMessage();
		}

		return null;
	}

	/**
	 * Close the connection.
	 */
	private void closeConnection() {
		if (session != null) {
			session.disconnect();
			session = null;
		}

		if (jsch != null) {
			jsch = null;
		}
	}

	/**
	 * List remote files.
	 *
	 * @param dir Directory to list.
	 * @return A list of files.
	 */
	public String[] listFiles(final String dir) {
		if (setupConnection() == null) {
			ChannelExec channel = null;
			try {
				final String command = "ls -F " + StringUtils.setEmpty(dir, ".");
				LOG.debug("Reading files in: " + command);

				channel = (ChannelExec) session.openChannel("exec");
				channel.setCommand(command);

				final InputStream in = channel.getInputStream();

				channel.connect(30 * 1000);

				final ArrayList<String> files = new ArrayList<>();

				String line = null;
				try (final BufferedReader br = new BufferedReader(new InputStreamReader(in, UTF_8))) {
					while ((line = br.readLine()) != null) {
						line = line.trim();
						if (line.endsWith("*")) {
							// Remove asterisk for executable files
							line = line.substring(0, line.length() - 1);
						}
						else if (line.endsWith("@") || line.endsWith("/")) {
							// Skip linked files and directories
							continue;
						}
						files.add(line.trim());
					}
				}

				return files.toArray(new String[0]);
			}
			catch (final IOException | JSchException e) {
				LOG.info("Error listing files", e);
			}
			finally {
				if (channel != null) {
					channel.disconnect();
				}

				closeConnection();
			}
		}

		return null;
	}

	/**
	 * Check response from server.
	 *
	 * @param in Inputstream to read data from.
	 * @return 0 Success, 1 Error, 2 Fatal error
	 */
	private int checkAck(final InputStream in) throws IOException {
		final int byteRead = in.read();
		if (byteRead == 1 || byteRead == 2) {
			final StringBuilder sb = new StringBuilder();
			int character = 0;
			do {
				character = in.read();
				sb.append((char) character);
			}
			while (character != '\n');
			if (sb.length() != 0) {
				LOG.info("Error: " + sb.toString());
			}
		}

		return byteRead;
	}

	/**
	 * Send '\0'.
	 *
	 * @param out Outputstream to write to.
	 */
	private void sendNull(final OutputStream out) throws IOException {
		final byte[] buf = new byte[1];
		buf[0] = 0;
		out.write(buf, 0, 1);
		out.flush();
	}

	/**
	 * Retrieve a file.
	 *
	 * @param localname Local file name.
	 * @param remotename Remote file name.
	 * @return Error message or null on success.
	 */
	public String getFile(final String localname, final String remotename) {
		final String setupError = setupConnection();
		if (setupError == null) {
			ChannelExec channel = null;
			try {
				final String command = "scp -f " + remotename;
				LOG.debug("Retrieving file: " + command);

				channel = (ChannelExec) session.openChannel("exec");
				channel.setCommand(command);

				final OutputStream out = channel.getOutputStream();
				final InputStream in = channel.getInputStream();

				channel.connect(30 * 1000);

				final byte[] buf = new byte[1024];

				// Send '\0'
				sendNull(out);
				if (checkAck(in) != 'C') {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				// Read '0644 '
				if (in.read(buf, 0, 5) < 0) {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				// Read file size
				long filesize = 0L;
				while (true) {
					if (in.read(buf, 0, 1) < 0) {
						break;
					}
					if (buf[0] == ' ') {
						break;
					}
					filesize = filesize * 10L + (long) (buf[0] - '0');
				}
				LOG.debug("Filesize: " + filesize);

				// Read file name
				String filename = null;
				for (int i = 0; ; i++) {
					if (in.read(buf, i, 1) < 0) {
						break;
					}
					if (buf[i] == (byte) 0x0a) {
						filename = new String(buf, 0, i, UTF_8);
						break;
					}
				}
				LOG.debug("File: " + filename);

				// Send '\0'
				sendNull(out);

				// Read file content
				try (final OutputStream fos = new FileOutputStream(localname)) {
					int size;
					while (true) {
						if (buf.length < filesize) {
							size = buf.length;
						}
						else {
							size = (int) filesize;
						}
						size = in.read(buf, 0, size);
						if (size < 0) {
							break;
						}
						fos.write(buf, 0, size);
						filesize -= size;
						if (filesize == 0L) {
							break;
						}
					}
				}

				if (checkAck(in) != 0) {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				// Send '\0'
				sendNull(out);

				return null;
			}
			catch (final RuntimeException | IOException | JSchException e) {
				LOG.info("Error retrieving file", e);
				return e.getMessage();
			}
			finally {
				try {
					if (channel != null) {
						channel.disconnect();
					}

					closeConnection();
				}
				catch (final Exception e) {
					// Ignore
				}
			}
		}

		return setupError;
	}

	/**
	 * Upload a file.
	 *
	 * @param localname Local file name.
	 * @param remotename Remote file name.
	 * @return Error message or null on success.
	 */
	public String sendFile(final String localname, final String remotename) {
		final File file = new File(localname);
		if (!file.exists()) {
			LOG.error("File not found: " + localname);
			return BACKUP_FILE_NOT_FOUND;
		}

		final String setupError = setupConnection();
		if (setupError == null) {
			FileInputStream fis = null;
			ChannelExec channel = null;
			try {
				String command = "scp -p -t " + remotename;
				LOG.debug("Sending file: " + command);

				channel = (ChannelExec) session.openChannel("exec");
				channel.setCommand(command);

				final OutputStream out = channel.getOutputStream();
				final InputStream in = channel.getInputStream();

				channel.connect(30 * 1000);

				if (checkAck(in) != 0) {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				// Send "C0644 file size filename", where filename should not include '/'
				final long filesize = file.length();
				command = "C0644 " + filesize + " ";
				if (localname.lastIndexOf("/") > 0) {
					command += localname.substring(localname.lastIndexOf("/") + 1);
				}
				else {
					command += localname;
				}
				command += "\n";
				out.write(command.getBytes(UTF_8));
				out.flush();

				if (checkAck(in) != 0) {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				// Send file content
				fis = new FileInputStream(file);
				final byte[] buf = new byte[1024];
				while (true) {
					final int size = fis.read(buf, 0, buf.length);
					if (size <= 0) {
						break;
					}
					out.write(buf, 0, size);
					out.flush();
				}
				fis.close();
				fis = null;

				// Send '\0'
				sendNull(out);

				if (checkAck(in) != 0) {
					return SCP_ACKNOWLEDGEMENT_NOT_RECEIVED;
				}

				return null;
			}
			catch (final Exception e) {
				LOG.info("Error sending file", e);
			}
			finally {
				try {
					if (fis != null) {
						fis.close();
					}

					if (channel != null) {
						channel.disconnect();
					}

					closeConnection();
				}
				catch (final Exception e) {
					// Ignore
				}
			}
		}

		return setupError;
	}
}
