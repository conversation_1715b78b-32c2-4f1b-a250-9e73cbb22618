package com.chilicoders.util;

import java.sql.Connection;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.db.DbObject;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.thread.TimerHandler;

/**
 * Recurring/scheduled task to hard delete notifications following retention policy.
 * Retention policy requires to delete any notifications that are either:
 * 		1. older than the configured retention period (defaulted to 30 days);
 * 		2. over the configured retention limit per user, keeping the newest ones (defaulted to 500)
 */
public class NotificationRetentionHandlingTask extends TimerTask {

	private static final Logger LOG = LogManager.getLogger(NotificationRetentionHandlingTask.class);

	@Override
	public void run() {
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			// First, delete all notifications older than 30 days
			final Instant retentionCutoff = Instant.now().minus(Duration.parse(ServiceProvider.getConfigService().getProperty(ConfigKeys.ConfigurationKey.notification_retention_period)));
			DbObject.executeUpdate(conn, "DELETE FROM notifications WHERE created < ?", retentionCutoff);
			conn.commit();

			// Iterate through users that have over 500 notifications remaining and delete those
			final int retentionLimit = ServiceProvider.getConfigService().getProperty(ConfigKeys.ConfigurationIntKey.notification_retention_limit);
			final Long[] byUserIds
					= DbObject.getLongArray(conn, "SELECT ARRAY_AGG(userid) FROM (SELECT COUNT(*), userid FROM notifications GROUP BY 2 HAVING COUNT(*) > ?) x", retentionLimit);
			final String userDeleteQuery
					= "DELETE FROM notifications WHERE id IN (SELECT id FROM (SELECT id, RANK() OVER (ORDER BY created DESC) FROM notifications WHERE userid = ?) retention WHERE rank > ?)";
			for (final Long userId : byUserIds) {
				DbObject.executeUpdate(conn, userDeleteQuery, userId, retentionLimit);
			}

			final Long[] bySubUserIds
					= DbObject.getLongArray(conn, "SELECT ARRAY_AGG(subuserid) FROM (SELECT COUNT(*), subuserid FROM notifications GROUP BY 2 HAVING COUNT(*) > ?) x", retentionLimit);
			final String subUserDeleteQuery
					= "DELETE FROM notifications WHERE id IN (SELECT id FROM (SELECT id, RANK() OVER (ORDER BY created DESC) FROM notifications WHERE subuserid = ?) retention WHERE rank > ?)";
			for (final Long subUserId : bySubUserIds) {
				DbObject.executeUpdate(conn, subUserDeleteQuery, subUserId, retentionLimit);
			}
			conn.commit();
		}
		catch (final Throwable e) {
			LOG.error("Error when purging notifications", e);
		}
		finally {
			scheduleTask(true);
		}
	}

	/**
	 * Schedules the task to run. Will run once per hour.
	 *
	 * @param reschedule True if this task has already been executed.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (reschedule) {
			final Duration interval = Duration.parse(ServiceProvider.getConfigService().getProperty(ConfigKeys.ConfigurationKey.notification_retention_purging_interval));
			TimerHandler.schedule(TimerHandler.Task.NotificationRetentionHandlingTask, Date.from(Instant.now().plus(interval)));
		}
		else {
			TimerHandler.schedule(TimerHandler.Task.NotificationRetentionHandlingTask, new Date());
		}
	}
}
