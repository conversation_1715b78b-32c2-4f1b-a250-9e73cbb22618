package com.chilicoders.util;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Timer;
import java.util.TimerTask;

import javax.net.ssl.SNIHostName;
import javax.net.ssl.SNIServerName;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;

import org.apache.commons.io.IOUtils;
import org.apache.http.Consts;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.impl.client.ProxyAuthenticationStrategy;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import com.chilicoders.app.Hiab;
import com.chilicoders.app.NetworkInformationInterface;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.thread.TimestampThread;
import com.chilicoders.util.xml.XmlUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class HttpsThread extends Thread {
	private static final Logger LOG = LogManager.getLogger(HttpsThread.class);

	private HttpPost post;
	private static PoolingHttpClientConnectionManager ccm;
	private static final Timer timer = new Timer();

	private final String url;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final String[][] fileNames;
	private final Map<String, String> postParams;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final String[][] headers;
	private final boolean useProxy;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final OutputStream outputStream;
	private final int timeoutInSeconds;
	private final SSLContext sslContext;

	private boolean finished = false;
	private String resultData = null;
	private final HashMap<String, String> resultHeaders = new HashMap<>();
	private String errorOccured = null;
	private Header contentType;
	private int statusCode;

	public HttpsThread(final String url, final Map<String, String> postParameters) {
		this(url, null, postParameters, null, false, null, -1, null);
	}

	public HttpsThread(final String url, final String[][] fileNames, final Map<String, String> postParameters, final String[][] headers) {
		this(url, fileNames, postParameters, headers, false, null, -1, null);
	}

	public HttpsThread(final String url, final Map<String, String> postParameters, final boolean useProxy, final OutputStream binaryStream) {
		this(url, null, postParameters, null, useProxy, binaryStream, -1, null);
	}

	public HttpsThread(final String url, final Map<String, String> postParameters, final OutputStream binaryStream, final SSLContext sslContext) {
		this(url, null, postParameters, null, false, binaryStream, -1, sslContext);
	}

	/**
	 * Creates a httpsthread that communicates with a server.
	 *
	 * @param url The URL to connect to.
	 * @param fileNames Name of files each pair consists of {path to filesystem, name to send to server}
	 * @param postParameters The post parameters.
	 * @param useProxy Proxy to user.
	 * @param binaryStream A stream to write the result to.
	 * @param timeoutInSeconds Timeout in seconds, -1 indicates default.
	 * @param headers Headers to use.
	 * @param sslContext SSL context (client and trusted certificates) to use.
	 */
	public HttpsThread(final String url, final String[][] fileNames, final Map<String, String> postParameters, final String[][] headers, final boolean useProxy,
					   final OutputStream binaryStream, final int timeoutInSeconds, final SSLContext sslContext) {
		super("HttpsThread: [" + url + "]");
		this.url = url;
		this.fileNames = fileNames;
		this.postParams = postParameters == null ? null : Collections.unmodifiableMap(postParameters);
		this.headers = headers;
		this.useProxy = useProxy;
		this.outputStream = binaryStream;
		this.timeoutInSeconds = timeoutInSeconds;
		this.sslContext = sslContext;
	}

	/**
	 * Gets the data read from the remote host. Unless the data was already grabbed into an outputstream, in which case an empty string will be returned.
	 *
	 * @return The data read
	 */
	public synchronized String getData() {
		// If we grabbed the data to an output stream, just return an empty string.
		if (outputStream != null) {
			return "";
		}
		return resultData;
	}

	/**
	 * Waits for the httpsthread to complete.
	 *
	 * @param scheduler A schedulerthread to send heartbeats to during waiting.
	 */
	private synchronized void waitForCompletion(final TimestampThread scheduler) {
		while (!finished) {
			try {
				if (scheduler != null) {
					wait(10 * 1000);
					scheduler.heartbeat("httpsthread:waitforcompletion");
				}
				else {
					wait();
				}
			}
			catch (final InterruptedException e) {
				continue;
			}
		}
	}

	/**
	 * A socket factory to create sockets via proxy.
	 */
	private static class MySchemeSocketFactory implements ConnectionSocketFactory {
		private final String host;
		private final int port;

		private MySchemeSocketFactory(final String host, final int port) {
			this.host = host;
			this.port = port;
		}

		@Override
		public Socket connectSocket(final int timeout, final Socket socket, final HttpHost host, final InetSocketAddress remote, final InetSocketAddress local,
									final HttpContext params) throws IOException {
			final InetSocketAddress socksaddr = new InetSocketAddress(this.host, port);
			final Proxy proxy = new Proxy(Proxy.Type.SOCKS, socksaddr);
			final Socket sock = new Socket(proxy);

			if (local != null) {
				sock.bind(local);
			}
			try {
				sock.connect(remote, timeout);
				final SSLSocketFactory factory = SslUtils.getO24DefaultSslContext().getSocketFactory();
				final SSLSocket secureSock = (SSLSocket) factory.createSocket(sock, remote.getAddress().getHostAddress(), remote.getPort(), true);
				secureSock.setSSLParameters(createSslParameters());
				return secureSock;
			}
			catch (final SocketTimeoutException ex) {
				ex.printStackTrace();
				throw new ConnectTimeoutException("Connect to " + remote.getHostName() + "/" + remote.getAddress() + " timed out");
			}
			catch (final IOException e) {
				e.printStackTrace();
				throw (e);
			}
		}

		@Override
		public Socket createSocket(final HttpContext arg0) throws IOException {
			return new Socket();
		}
	}

	/**
	 * Starting a socks proxy is done with "ssh -N -D 2001 markh@**************"
	 * Where 2001 is the port number to use.
	 */
	private synchronized void performRequest() {
		boolean ok = false;

		try {
			final long start = System.currentTimeMillis();
			final HttpClient client = HttpsThread.getNewHttpClient(useProxy, sslContext, false);

			post = new HttpPost(this.url);
			addHttpEntity();

			final RequestConfig.Builder configBuilder = RequestConfig.custom();
			configBuilder.setSocketTimeout(timeoutInSeconds <= 0 ? Configuration.getProperty(ConfigurationIntKey.https_timeout) * 1000 : timeoutInSeconds * 1000);
			configBuilder.setConnectTimeout(Configuration.getProperty(ConfigurationIntKey.https_connect_timeout) * 1000);
			configBuilder.setConnectionRequestTimeout(Configuration.getProperty(ConfigurationIntKey.https_timeout) * 1000);
			post.setConfig(configBuilder.build());
			HMACKey.signRequest(post);
			final TimerTask timerTask = createAbortTimer();
			timer.schedule(timerTask, Configuration.getProperty(ConfigurationIntKey.https_timeout) * 1000L * 10L);
			try {
				final HttpResponse response = client.execute(post);
				LOG.info("HttpResponse code: "
						+ response.getStatusLine()
						+ " for URL: "
						+ url
						+ " in "
						+ (System.currentTimeMillis() - start)
						+ " data size "
						+ post.getEntity().getContentLength());
				contentType = response.getFirstHeader("Content-type");
				statusCode = response.getStatusLine().getStatusCode();
				if (response.getStatusLine().getStatusCode() >= 200 && response.getStatusLine().getStatusCode() < 400) {
					ok = true;
					for (final Header header : response.getAllHeaders()) {
						this.resultHeaders.put(header.getName(), header.getValue());
					}
					if (outputStream != null) {
						IOUtils.copy(response.getEntity().getContent(), outputStream);
					}
					else {
						this.resultData = EntityUtils.toString(response.getEntity());
					}
				}
			}
			finally {
				timerTask.cancel();
			}
		}
		catch (final Throwable e) {
			LOG.warn("Error reading data", e);
			this.errorOccured = e.getMessage();
		}
		finally {
			close();
		}

		if (!ok) {
			LOG.info("No connection could be done to URL : " + url);
		}

		// tell the caller that we are done...
		synchronized (this) {
			this.finished = true;
			notifyAll();
		}
	}

	@Override
	public void run() {
		performRequest();
	}

	/**
	 * Create abort timer.
	 *
	 * @return TimerTask object
	 */
	private TimerTask createAbortTimer() {
		final TimerTask timerTask = new TimerTask() {
			@Override
			public void run() {
				if (post != null) {
					final Iterator<Entry<String, String>> it2 = HttpsThread.this.postParams.entrySet().iterator();
					LOG.info("Abort parameters");
					while (it2.hasNext()) {
						final Entry<String, String> entry = it2.next();
						LOG.info(entry.getKey() + ": " + StringUtils.maxLength(entry.getValue(), 100));
					}
					LOG.warn("Aborting request: " + url);
					post.abort();
				}
			}
		};
		return timerTask;
	}

	/**
	 * Get header from response.
	 *
	 * @param header Header name
	 * @return Header value
	 */
	public String getHeader(final String header) {
		return resultHeaders.get(header);
	}

	/**
	 * Add http entity to request.
	 */
	private void addHttpEntity() throws UnsupportedEncodingException {
		String headerAction = null;
		if (headers != null) {
			for (int i = 0; i < headers.length; i++) {
				if ("ACTION".equals(headers[i][0])) {
					headerAction = headers[i][1];
				}
			}
		}
		LOG.info("Building request for action: " + StringUtils.setEmpty(this.postParams.get("ACTION"), headerAction));

		if (CertificateUtils.shouldUseApplianceSni(ServiceProvider.getLibellumConfig())) {
			post.setHeader("Host", Configuration.getProperty(ConfigurationKey.appliance_sni));
		}

		if (fileNames != null) {
			final String boundary = "-------------" + System.currentTimeMillis();

			post.setHeader("Content-type", "multipart/form-data; boundary=" + boundary);
			if (headers != null) {
				for (int i = 0; i < headers.length; i++) {
					post.setHeader(headers[i][0], headers[i][1]);
				}
			}
			final MultipartEntityBuilder builder = MultipartEntityBuilder.create();
			builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
			builder.setBoundary(boundary);
			final Iterator<Entry<String, String>> it = this.postParams.entrySet().iterator();
			while (it.hasNext()) {
				final Entry<String, String> entry = it.next();
				builder.addTextBody(entry.getKey(), entry.getValue(), ContentType.TEXT_PLAIN);
			}
			for (int i = 0; i < fileNames.length; i++) {
				builder.addBinaryBody(fileNames[i][1], new File(fileNames[i][0]), ContentType.APPLICATION_OCTET_STREAM, fileNames[i][1]);
			}
			post.setEntity(builder.build());
		}
		else {
			final List<NameValuePair> postParams = new ArrayList<>();
			final Iterator<Entry<String, String>> it = this.postParams.entrySet().iterator();
			while (it.hasNext()) {
				final Entry<String, String> entry = it.next();
				postParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
			post.setEntity(new UrlEncodedFormEntity(postParams, "UTF-8"));
		}
	}

	/**
	 * Checks if an error occured during communication.
	 *
	 * @return <code>true</code> if error occured during the operation.
	 */
	public boolean hasErrorOccured() {
		return errorOccured != null;
	}

	public String getErrorMessage() {
		return errorOccured;
	}

	public int getStatusCode() {
		return statusCode;
	}

	/**
	 * Waits for a response from the http thread.
	 *
	 * @param scheduler Scheduler thread
	 * @return The result received from the recipient. If <code>null</code> the request failed.
	 */
	public synchronized String getResponse(final TimestampThread scheduler) {
		waitForCompletion(scheduler);
		return getData();
	}

	/**
	 * Get content type.
	 *
	 * @return Content type
	 */
	public String getContentType() {
		if (contentType != null) {
			return contentType.getValue();
		}
		return "application/text";
	}

	/**
	 * Get PoolingHttpClientConnectionManager.
	 *
	 * @param proxy true if proxy should be used, otherwise false
	 * @param sslContext the SSL context to use
	 * @return PoolingHttpClientConnectionManager object
	 */
	private static synchronized PoolingHttpClientConnectionManager getConnectionPoolManager(final boolean proxy, final SSLContext sslContext)
			throws NoSuchAlgorithmException, KeyManagementException, UnrecoverableKeyException, KeyStoreException {
		if (!proxy && ccm != null && sslContext == null) {
			return ccm;
		}

		final RegistryBuilder<ConnectionSocketFactory> regBuilder = RegistryBuilder.create();
		regBuilder.register("http", PlainConnectionSocketFactory.getSocketFactory());
		final ConnectionSocketFactory connectionSocketFactory;
		if (proxy) {
			final NetworkInformationInterface networkInfo = Hiab.getInstance().getNetworkInformation();
			connectionSocketFactory = new MySchemeSocketFactory(networkInfo.getProxyHost(), networkInfo.getProxyPort());
		}
		else {
			connectionSocketFactory = new NoVerifySSLSocketFactory(sslContext != null ? sslContext : SslUtils.getO24DefaultSslContext(), NoopHostnameVerifier.INSTANCE);
		}
		regBuilder.register("https", connectionSocketFactory);
		final PoolingHttpClientConnectionManager result = new PoolingHttpClientConnectionManager(regBuilder.build());
		if (!proxy) {
			result.setMaxTotal(100);
			result.setDefaultMaxPerRoute(3);
			if (sslContext == null) {
				ccm = result;
			}
		}
		return result;
	}

	public static synchronized void clearClientConnectionManager() {
		ccm = null;
	}

	/**
	 * Create SSLParameters with SNIHostName
	 *
	 * @return the ssl parameters.
	 */
	public static SSLParameters createSslParameters() {
		final SSLParameters sslParams = new SSLParameters();
		if (CertificateUtils.shouldUseApplianceSni(ServiceProvider.getLibellumConfig())) {
			final SNIHostName serverNameIndication = new SNIHostName(Configuration.getProperty(ConfigurationKey.appliance_sni));
			final List<SNIServerName> asList = Arrays.asList(serverNameIndication);
			sslParams.setServerNames(asList);
		}
		return sslParams;
	}

	/**
	 * Get a new http client and extend SSL context if needed
	 * @param useProxy Set to true if use a proxy for this request.
	 * @return HttpClient with optional extended SSL context
	 */
	public static HttpClient getNewHttpClient(final boolean useProxy) throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException, KeyManagementException {
		return getNewHttpClient(useProxy, false);
	}

	/**
	 * Get a new http client and extend SSL context if needed
	 * @param useProxy Set to true if use a proxy for this request.
	 * @param skipExtendedIpaCa Skip the use of extended ipaca for this http client.
	 * @return HttpClient with optional extended SSL context
	 */
	public static HttpClient getNewHttpClient(final boolean useProxy, final boolean skipExtendedIpaCa) throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException, KeyManagementException {
		final boolean isExtendCertIpaCa = !skipExtendedIpaCa && Configuration.getConfigService().getProperty(ConfigKeys.ConfigurationBooleanKey.certificate_extend_ssl_context_ipa_ca);

		final SSLContext sslContext = isExtendCertIpaCa ? SslUtils.createExtendedSSLContext(Configuration.getConfigService().getProperty(ConfigKeys.ConfigurationKey.root_certificate_k8s)) : null;
		return getNewHttpClient(useProxy, sslContext, skipExtendedIpaCa);
	}

	/**
	 * Creates a new http client.
	 *
	 * @param useProxy Set to <code>true</code> to use a proxy for this connection.
	 * @param sslContext the SSL context to use
	 * @param skipExtendedIpaCa Skip the use of extended ipaca for this http client
	 * @return Creates a new http client that does not verify certificates.
	 */
	public static HttpClient getNewHttpClient(final boolean useProxy, final SSLContext sslContext, final boolean skipExtendedIpaCa) {
		HttpClient result = null;
		try {
			final HttpClientBuilder clientBuilder = HttpClientBuilder.create();
			boolean ccmProxyNeeded = false;

			if (useProxy) {
				final NetworkInformationInterface networkInfo = Hiab.getInstance().getNetworkInformation();
				if (!"0".equals(networkInfo.getProxyType()) && !StringUtils.isEmpty(networkInfo.getProxyHost()) && networkInfo.getProxyPort() > 0) {
					if ("h".equals(networkInfo.getProxyType()) || "t".equals(networkInfo.getProxyType())) {
						clientBuilder.setProxy(
								new HttpHost(networkInfo.getProxyHost(), networkInfo.getProxyPort(), "h".equals(networkInfo.getProxyType()) ? "https" : "http"));
						clientBuilder.setProxyAuthenticationStrategy(new ProxyAuthenticationStrategy());
						// Check if we have both a domain and username, if so split it
						String username = networkInfo.getProxyUser();
						String domain = "";
						final int domainSeparator = username.indexOf('\\');
						if (domainSeparator > -1) {
							domain = username.substring(0, domainSeparator);
							username = username.substring(domainSeparator + 1);
						}
						final CredentialsProvider credsProvider = new BasicCredentialsProvider();
						credsProvider.setCredentials(new AuthScope(networkInfo.getProxyHost(), networkInfo.getProxyPort()),
								new NTCredentials(username, networkInfo.getProxyPassword(), networkInfo.getProxyHost(), domain));
						clientBuilder.setDefaultCredentialsProvider(credsProvider);
					}
					else if ("a".equals(networkInfo.getProxyType()) || "4".equals(networkInfo.getProxyType()) || "5".equals(networkInfo.getProxyType())) {
						ccmProxyNeeded = true;
					}
				}
			}
			final ConnectionConfig connConfig = ConnectionConfig.custom().setCharset(Consts.UTF_8).build();
			clientBuilder.setConnectionManager(getConnectionPoolManager(ccmProxyNeeded, sslContext));
			clientBuilder.setDefaultConnectionConfig(connConfig);

			if (!skipExtendedIpaCa && Configuration.getConfigService().getProperty(ConfigKeys.ConfigurationBooleanKey.certificate_extend_ssl_context_ipa_ca)) {
				final SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
						sslContext,
						new String[]{"TLSv1.2"},	// We need this to properly disable SNI
						null,						// Default cipher suites
						new NoopHostnameVerifier()
				);
				clientBuilder.setSSLSocketFactory(sslSocketFactory);
			}
			if (Configuration.isHiabEnabled()) {
				clientBuilder.setRedirectStrategy(new LaxRedirectStrategy());
			}

			result = clientBuilder.build();
		}
		catch (final RuntimeException | KeyStoreException | UnrecoverableKeyException | KeyManagementException | NoSuchAlgorithmException e) {
			LOG.error("Error creating http client", e);
			result = HttpClientBuilder.create().build();
		}
		return result;
	}

	/**
	 * Determines if a message from the other end is a success or not.
	 *
	 * @param message The data in the message.
	 * @param json <code>true</code> if the message is a JSON message.
	 * @return <code>true</code> if the message was a success.
	 */
	public static boolean isSuccessResponse(final String message, final boolean json) {
		if (StringUtils.isEmpty(message)) {
			return false;
		}

		if (json) {
			try {
				final JSONObject obj = new JSONObject(message);
				return "true".equals(obj.get("success").toString());
			}
			catch (final JSONException e) {
				LOG.warn("Bad JSON message: " + e.getMessage());
				return false;
			}
		}
		try {
			final Document xml = XmlUtils.getDocument(message, true);
			if (xml != null) {
				final Node root = xml.getDocumentElement();
				Node row = root.getFirstChild();
				while (row != null) {
					final String element = row.getNodeName();
					if ("SUCCESS".equals(element)) {
						return StringUtils.getBooleanValue(row.getFirstChild().getNodeValue(), false);
					}

					row = row.getNextSibling();
				}
			}
		}
		catch (final Exception e) {
			LOG.warn("Bad XML message: " + e.getMessage());
			return false;
		}

		return false;
	}

	/**
	 * Gets the {@code<MESSAGE>} value from a XML document.
	 *
	 * @param xml Xml document
	 * @param tag Xml tag to return
	 * @return Message value
	 */
	private static String getMessageResponse(final String xml, final String tag) {
		if (StringUtils.isEmpty(xml)) {
			return null;
		}

		try {
			final Document doc = XmlUtils.getDocument(xml, true);
			if (doc != null) {
				final Node root = doc.getDocumentElement();
				Node row = root.getFirstChild();
				while (row != null) {
					final String element = row.getNodeName();
					if (tag.equals(element)) {
						if (row.getFirstChild() != null) {
							return row.getFirstChild().getNodeValue();
						}
						return "";
					}

					row = row.getNextSibling();
				}
			}
		}
		catch (final Exception e) {
			LOG.warn("Bad XML message: " + e.getMessage());
			return null;
		}

		return null;
	}

	public static String getMessageResponse(final String xml) {
		return getMessageResponse(xml, "MESSAGE");
	}

	public static ErrorCode getErrorCodeResponse(final String xml) {
		return ErrorCode.get(StringUtils.getIntValue(getMessageResponse(xml, "ERRORCODE"), -1));
	}

	/**
	 * Close thread.
	 */
	private void close() {
		try {
			if (post != null) {
				post.releaseConnection();
			}
		}
		catch (final Exception e) {
			LOG.error("Error closing HttpThread", e);
		}
	}

	public static void destroyTimer() {
		timer.cancel();
	}
}
