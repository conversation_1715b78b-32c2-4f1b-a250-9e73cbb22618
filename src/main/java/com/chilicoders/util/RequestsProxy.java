package com.chilicoders.util;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.chilicoders.app.MultipartHttpServletRequest;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.AppAccess;

public class RequestsProxy {
	private static final Logger LOG = LogManager.getLogger(RequestsProxy.class);

	/**
	 * Proxy request to another server. Will create a temporary appaccesstoken which will be deleted afterwards. By using
	 * that token the other server will use it and we can be logged in as that user.
	 *
	 * @param request Request to proxy.
	 * @param response Response to write response to.
	 * @param userId User id.
	 * @param conn Database connection.
	 */
	public static void proxyRequests(final HttpRequestWrapper request, final HttpServletResponse response, final long userId, final Connection conn) throws SQLException {
		final AppAccess accessToken = userId > 0 ? new AppAccess() : null;
		long accessTokenId = -1;
		if (userId > 0) {
			accessToken.setUserId(userId);
			accessToken.setActive(true);
			accessToken.setTokenKey(UUID.randomUUID().toString());
			accessToken.setName("Temporary key: " + accessToken.getTokenKey());
			accessTokenId = accessToken.save(conn);
		}

		try {
			if (accessToken != null) {
				DbObject.executeUpdate(conn, "UPDATE tappaccess SET tokenkey=? WHERE xid=? AND xuserxid=?", accessToken.getTokenKey(), accessTokenId, userId);
				conn.commit();
			}
			if (request instanceof MultipartHttpServletRequest) {
				doMultiPartProxy((MultipartHttpServletRequest) request, response, accessToken);
			}
			else {
				doProxy(request, response, accessToken);
			}
		}
		catch (final IOException | URISyntaxException | SQLException e) {
			LOG.error("Error forwarding request", e);
		}
		finally {
			if (accessToken != null) {
				AppAccess.execute(conn, "DELETE FROM tappaccess WHERE xid = ?", accessTokenId);
				conn.commit();
			}
		}
	}

	/**
	 * Performs the request proxy for a non multipart request.
	 *
	 * @param request Http request
	 * @param response Http response
	 * @param accessToken Access token to be used in the request to ensure the request is considered logged in
	 */
	private static void doProxy(final HttpRequestWrapper request, final HttpServletResponse response, final AppAccess accessToken) throws URISyntaxException, IOException {
		final URIBuilder uriBuilder = new URIBuilder(Configuration.getProperty(ConfigKeys.ConfigurationKey.hiab_request_forward_address));
		final HttpURLConnection httpsConn;
		if ("OUTSCAN_UPDATEHIAB".equals(request.getParameter("ACTION")) || "OUTSCAN_ENROLLHIAB".equals(request.getParameter("ACTION"))) {
			httpsConn = doProxyUrlEncoded(request, uriBuilder);
		}
		else {
			request.getParameterMap().forEach((key, value) -> uriBuilder.setParameter(key, value[0]));
			if (accessToken != null) {
				uriBuilder.setParameter("APPTOKEN", accessToken.getTokenKey());
			}
			httpsConn = SslUtils.getTrustAllHttpsConnection(uriBuilder.build().toURL());
			httpsConn.setRequestProperty("connection", "close");
			httpsConn.setDoOutput(true);
			httpsConn.setDoInput(true);
			if (request.getHeader("Content-Length") != null) {
				httpsConn.setRequestProperty("Content-Length", request.getHeader("Content-Length"));
			}
			if (request.getHeader("Content-Type") != null) {
				httpsConn.setRequestProperty("Content-Type", request.getHeader("Content-Type"));
			}

			IOUtils.copy(request.getInputStream(), httpsConn.getOutputStream());
		}
		response.setContentType(httpsConn.getContentType());
		response.setStatus(httpsConn.getResponseCode());
		if (!StringUtils.isEmpty(httpsConn.getHeaderField("content-Disposition"))) {
			response.setHeader("Content-Disposition", httpsConn.getHeaderField("content-Disposition"));
		}
		IOUtils.copy(httpsConn.getInputStream(), response.getOutputStream());
		httpsConn.disconnect();
	}

	/**
	 * Perform a proxy request where the data is url-encoded. Meaning that the POST parameters are not sent in the URL
	 * but instead in the body. Otherwise the URL will grow too large and a HTTP 414 is received instead.
	 *
	 * @param request Http request made.
	 * @param uriBuilder URI builder for the proxy target.
	 * @return A http url conection.
	 */
	private static HttpURLConnection doProxyUrlEncoded(final HttpRequestWrapper request, final URIBuilder uriBuilder) throws IOException, URISyntaxException {
		final HttpURLConnection httpsConn;
		httpsConn = SslUtils.getTrustAllHttpsConnection(uriBuilder.build().toURL());
		httpsConn.setDoOutput(true);
		httpsConn.setDoInput(true);
		httpsConn.setRequestMethod("POST");
		httpsConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		httpsConn.setRequestProperty("charset", "utf-8");
		final StringBuilder postData = new StringBuilder();
		for (final Map.Entry<String, String[]> param : request.getParameterMap().entrySet()) {
			if (postData.length() != 0) {
				postData.append('&');
			}
			postData.append(URLEncoder.encode(param.getKey(), "UTF-8"));
			postData.append('=');
			postData.append(URLEncoder.encode(param.getValue()[0], "UTF-8"));
		}
		httpsConn.setRequestProperty("Content-Length", Integer.toString(postData.length()));
		httpsConn.setUseCaches(false);
		try (final OutputStream wr = httpsConn.getOutputStream()) {
			wr.write(postData.toString().getBytes(UTF_8));
		}
		return httpsConn;
	}

	/**
	 * Performs the request proxy for a non multipart request.
	 *
	 * @param request Http request
	 * @param response Http response
	 * @param accessToken Access token to be used in the request to ensure the request is considered logged in
	 */
	private static void doMultiPartProxy(final MultipartHttpServletRequest request, final HttpServletResponse response, final AppAccess accessToken) throws IOException {
		final RestTemplate restTemplate = new RestTemplate();
		final HttpHeaders headers = new HttpHeaders();
		headers.setContentType(org.springframework.http.MediaType.MULTIPART_FORM_DATA);
		final MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		for (final FileItem file : request.getItems()) {
			if (file.getFieldName().equals("ACTION")) {
				headers.add("ACTION", file.getString());
			}
			body.add(file.getFieldName(), file.get());
		}
		if (accessToken != null) {
			body.add("APPTOKEN", accessToken.getTokenKey());
		}
		final HttpEntity<MultiValueMap<String, Object>> requestEntity
				= new HttpEntity<>(body, headers);
		try {
			final MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
			converter.setSupportedMediaTypes(Collections.singletonList(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM));
			restTemplate.getMessageConverters().add(converter);
			final ResponseEntity<Resource> proxyResponse =
					restTemplate.postForEntity(Configuration.getProperty(ConfigKeys.ConfigurationKey.hiab_request_forward_address), requestEntity, Resource.class);
			response.setStatus(proxyResponse.getStatusCodeValue());
			if (proxyResponse.hasBody()) {
				final Resource proxyResponseBody = proxyResponse.getBody();
				if (proxyResponseBody != null) {
					try (final InputStream responseInputStream = proxyResponseBody.getInputStream()) {
						IOUtils.copy(responseInputStream, response.getOutputStream());
					}
				}
			}
		}
		catch (final HttpClientErrorException e) {
			LOG.error("Error forwarding request", e);
			response.setStatus(e.getRawStatusCode());
			response.getOutputStream().write(e.getResponseBodyAsByteArray());
		}
	}

}
