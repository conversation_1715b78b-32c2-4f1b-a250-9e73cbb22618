package com.chilicoders.util;

import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.core.templaterendering.api.TemplateRenderer;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.event.objects.EmailEventItem;
import com.chilicoders.event.objects.EventMetadata;
import com.chilicoders.rest.exceptions.NotFoundException;
import com.chilicoders.rest.models.EmailTemplate;
import com.chilicoders.service.ServiceProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Map;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Builder;
import lombok.Getter;

public class EmailUtils {

	private static final Logger LOG = LogManager.getLogger(EmailUtils.class);

	/**
	 * Email request class containing fields used to compose Kafka email objects.
	 * We can either email a list of user IDs or to a list of email addresses.
	 */
	@Getter
	@Builder
	@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"}, justification = "Intentionally exposing internal representation")
	public static class SendEmailRequest {
		private BaseLoggedOnUser currentUser;
		private Integer customerId;
		private Map<String, Object> bindings;
		private String templateName;
		private String toEmailAddresses;
		private Integer[] userIdList;
		private boolean isHtmlContent;
		private String subject;
		private String content;
		private boolean logToQueue;
	}

	/**
	 * Build an email object with the input Jinja template and send it to Kafka email topic.
	 *
	 * @param conn The database connection.
	 * @param sendEmailRequest The email request.
	 */
	public static void composeAndSendEmailToKafka(final Connection conn, final SendEmailRequest sendEmailRequest) throws SQLException {
		final String emailSubject;
		final String emailContent;
		if (sendEmailRequest.getTemplateName() == null) {
			emailContent = sendEmailRequest.getContent();
			emailSubject = sendEmailRequest.getSubject();
		}
		else {
			final EmailTemplate emailTemplate = EmailTemplate.getByField(EmailTemplate.class, conn, Access.ADMIN, "name", sendEmailRequest.getTemplateName());
			if (emailTemplate == null) {
				throw new NotFoundException();
			}

			final TemplateRenderer templateRenderer = ServiceProvider.getTemplateRenderer();
			emailContent = JinjaUtils.renderJinjaTemplate(templateRenderer, emailTemplate.getContent(), sendEmailRequest.getBindings());
			emailSubject = JinjaUtils.renderJinjaTemplate(templateRenderer, emailTemplate.getSubject(), sendEmailRequest.getBindings());
		}

		final BaseLoggedOnUser sender = (sendEmailRequest.getCurrentUser() == null ? BaseLoggedOnUser.NOUSER : sendEmailRequest.getCurrentUser());

		final Integer customerId = sendEmailRequest.getCustomerId() == null ? sender.getCustomerId() : sendEmailRequest.getCustomerId();

		final EmailEventItem emailEventItem = EmailEventItem.builder()
				.subject(emailSubject)
				.userEmailAddresses(!StringUtils.isEmpty(sendEmailRequest.getToEmailAddresses()) ? sendEmailRequest.getToEmailAddresses().split(",") : new String[0])
				.senderEmailAddress(createFromEmailAddress(sender))
				.userList(sendEmailRequest.getUserIdList())
				.customerId(customerId)
				.logToQueue(sendEmailRequest.isLogToQueue())
				.build();

		if (sendEmailRequest.isHtmlContent()) {
			emailEventItem.setHtmlContent(emailContent != null ? emailContent.replaceAll("\n", "<br>") : "");
		}
		else {
			emailEventItem.setContent(emailContent);
		}
		final EventMetadata eventMetadataEmail = EventMetadata.builder()
				.customerId(customerId)
				.timestamp(Instant.now())
				.build();
		LOG.debug("Sending email event: {}", eventMetadataEmail);
		ServiceProvider.getKafkaEmailService().sendEmailToKafka(emailEventItem, eventMetadataEmail);
	}

	/**
	 * Constructs an email address based on the sender's email address or system default email address.
	 *
	 * @param sender The sender's information.
	 * @return Returns the sender's email if provided, else return the default email address based on the system configuration.
	 */
	public static String createFromEmailAddress(final BaseLoggedOnUser sender) {
		// If the sender's email is not provided, use the default email address based on the system configuration.
		if (StringUtils.isEmpty(sender.getEmail())) {
			return ServiceProvider.getConfigService().getProperty(Configuration.isHiabEnabled()
					? ConfigKeys.ConfigurationKey.mail_fromaddresshiab
					: ConfigKeys.ConfigurationKey.mail_fromaddress);
		}

		return String.format("\"%s %s, %s\" <%s>",
				sender.getFirstName(),
				sender.getLastName(),
				sender.getCompany(),
				sender.getEmail());
	}
}