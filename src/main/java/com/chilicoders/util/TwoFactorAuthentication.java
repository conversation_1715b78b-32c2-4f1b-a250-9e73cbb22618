package com.chilicoders.util;

import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base32;

import com.chilicoders.bl.MessageBusiness;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.core.user.api.model.TwoFactorType;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class TwoFactorAuthentication {

	private String error = "";
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private UserDetails user = null;
	private HashMap<String, String> errors = new HashMap<>();

	/**
	 * Constructor.
	 *
	 * @param user Usr logging into the system.
	 */
	public TwoFactorAuthentication(final UserDetails user) {
		this.user = user;

		this.errors.put("4000", "Disabled credential ID has been enabled, please try again");
		this.errors.put("4001", "Locked credential ID has been unlocked, please try again");
		this.errors.put("4879", "The service is temporarily unavailable");
		this.errors.put("4990", "Bad Token State");
		this.errors.put("49b5", "Invalid security code");
		this.errors.put("49f2", "Token ID not found");
		this.errors.put("4e01", "Service Internal Error");
		this.errors.put("4e11", "Token ID has been revoked");
	}

	public String getErrorMessage() {
		return errors.get(error);
	}

	// Google Authenticator

	private long getGoogleAuthenticatorTimeIndex() {
		return System.currentTimeMillis() / 1000 / 30;
	}

	/**
	 * Get a google autenticator code.
	 *
	 * @param secret Secret key.
	 * @param timeIndex Time index.
	 * @return Google key code.
	 */
	private long getGoogleAuthenticatorCode(final byte[] secret, final long timeIndex) throws NoSuchAlgorithmException, InvalidKeyException {
		final SecretKeySpec signKey = new SecretKeySpec(secret, "HmacSHA1");
		final ByteBuffer buffer = ByteBuffer.allocate(8);
		buffer.putLong(timeIndex);
		final byte[] timeBytes = buffer.array();
		final Mac mac = Mac.getInstance("HmacSHA1");
		mac.init(signKey);
		final byte[] hash = mac.doFinal(timeBytes);
		final int offset = hash[19] & 0xf;
		long truncatedHash = hash[offset] & 0x7f;
		for (int i = 1; i < 4; i++) {
			truncatedHash <<= 8;
			truncatedHash |= hash[offset + i] & 0xff;
		}
		return (truncatedHash %= 1000000);
	}

	public long getGoogleAuthenticatorCode(final String secret) throws InvalidKeyException, NoSuchAlgorithmException {
		final byte[] secretBytes = new Base32().decode(secret);
		return getGoogleAuthenticatorCode(secretBytes, getGoogleAuthenticatorTimeIndex());
	}

	/**
	 * Verify a google code.
	 *
	 * @param conn Database connection
	 * @param secret Secret key.
	 * @param code Code to verify.
	 * @param timeIndex Time index to use.
	 * @param variance How much to change time index and retry.
	 * @return True if valid.
	 */
	private boolean verifyGoogleAuthenticatorCode(final Connection conn, final String secret, final long code, final long timeIndex, final int variance) {
		try {
			if (StringUtils.isEmpty(secret)) {
				return false;
			}

			final byte[] secretBytes = new Base32().decode(secret);
			final long lastConsumedOtpTimeIndex =
					DbObject.executeCountQuery(conn, "SELECT lastconsumedotptimeindex FROM " + (user.isSubUser() ? "tsubusers" : "tusers") + " WHERE xid=?",
							user.isSubUser() ? user.getSubUserId() : user.getMainUserId());
			for (int i = -variance; i <= variance; i++) {
				if (lastConsumedOtpTimeIndex >= timeIndex + i) {
					continue;
				}
				if (getGoogleAuthenticatorCode(secretBytes, timeIndex + i) == code) {
					this.user.setLastConsumedOtpTimeIndex(timeIndex + i);
					return true;
				}
			}
			return false;
		}
		catch (final RuntimeException | InvalidKeyException | SQLException | NoSuchAlgorithmException e) {
			return false;
		}
	}

	//

	/**
	 * Activate two factor authentication.
	 *
	 * @param conn Database connection
	 * @param type Type of authentication.
	 * @param token Token.
	 * @param code1 Code 1.
	 * @param code2 Code 2.
	 * @return Error message from activation.
	 */
	public String activate(final Connection conn, final TwoFactorType type, final String token, final String code1, final String code2) {
		if (type == TwoFactorType.TOTP) {
			return validate(conn, type, token, code1);
		}
		else {
			return null;
		}
	}

	/**
	 * @return User language.
	 */
	private String getLanguage() {
		if (this.user == null || this.user == BaseLoggedOnUser.NOUSER) {
			return "en";
		}
		return this.user.getLanguage();
	}

	/**
	 * Validate a twofactor code.
	 *
	 * @param conn Database connection
	 * @param type Type of validation.
	 * @param token Token used.
	 * @param code Code to validate.
	 * @return Error message or null if no error.
	 */
	public String validate(final Connection conn, final TwoFactorType type, final String token, final String code) {
		if (type == TwoFactorType.TOTP) {
			final boolean result = verifyGoogleAuthenticatorCode(conn, token, StringUtils.getLongValue(code), getGoogleAuthenticatorTimeIndex(), 5);
			if (!result) {
				this.error = "49b5";
			}
			else {
				return null;
			}
		}
		else {
			return null;
		}

		final String msg = getErrorMessage();
		return MessageBusiness.getMessageString("_ERROR_VALIDATE_CREDENTIAL_ID", getLanguage(), "MESSAGES") + (StringUtils.isEmpty(msg) ? "" : (", " + msg));
	}
}
