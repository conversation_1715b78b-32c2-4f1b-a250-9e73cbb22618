package com.chilicoders.util.mail;

import javax.mail.internet.ContentType;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * A subclass of MimeMultipart which allows for a custom content type.
 */
public class EncryptedMultiPart extends MimeMultipart {
	private static final Logger LOG = LogManager.getLogger(EncryptedMultiPart.class);
	private ContentType contentType = null;

	/**
	 * Makes a Multipart that uses the given content type, but otherwise
	 * is exactly like a multipart/mixed.
	 *
	 * @param ct Content type
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public EncryptedMultiPart(final ContentType ct) {
		super("mixed");
		contentType = ct;
	}

	/**
	 * Returns the content type given in the constructor.
	 *
	 * @return The content type
	 */
	public String getContentType() {
		try {
			final ContentType newCtype = new ContentType(super.getContentType());
			final ContentType retCtype = new ContentType(contentType.toString());
			retCtype.setParameter("boundary", newCtype.getParameter("boundary"));
			return retCtype.toString();
		}
		catch (final Exception e) {
			LOG.error("Error occurred getting content type;", e);
		}

		return "null";
	}
}
