package com.chilicoders.util;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.validator.routines.DomainValidator;
import org.apache.commons.validator.routines.EmailValidator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.util.IPAddress;
import org.bouncycastle.util.io.pem.PemObject;
import org.json.JSONArray;
import org.json.JSONObject;
import org.pcap4j.core.BpfProgram;
import org.pcap4j.core.PcapNativeException;
import org.pcap4j.core.Pcaps;
import org.pcap4j.packet.namednumber.DataLinkType;

import com.chilicoders.core.ip.api.IpService;
import com.chilicoders.core.user.api.UserAttribute;
import com.chilicoders.core.user.api.UserService;
import com.chilicoders.exception.InvalidCVSSException;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.util.xml.XMLObject;

import com.googlecode.ipv6.IPv6Address;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

@SuppressFBWarnings("EI_EXPOSE_REP")
public class ParamValidator {
	private static final Logger LOG = LogManager.getLogger(ParamValidator.class);

	public static final String TARGETLIST_COMMENTS_REG_EXP = "((?s)%.*)";
	public static final int MAX_PORT_NUMBER = 65535;

	public enum Type {
		DigitOnly,
		DoubleOnly,
		DigitList,
		Boolean,
		AlphaNumeric,
		Printable,
		ListOfValues,
		ListOfValuesCaseInsensitive,
		Ascii,
		IP,
		IPList,
		IPListCommaSeparted,
		HostList,
		HOST_OR_IP_LIST,
		HOST_OR_IP_LIST_WITH_COMMENTS,
		Domain,
		URL,
		CIDR_SYNTAX,
		Date,
		Time,
		HostIp,
		Mac,
		Email,
		EmailList,
		Hex,
		HTML,
		XML,
		JSON,
		CvssVector,
		RegEx,
		RegExList,
		Host,
		Netbios,
		File,
		Or,
		Any,
		BPF,
		Timezone,
		PemCert
	}

	public enum Method {
		List,
		Update,
		Delete
	}

	public enum DynamicValidatorLocation {
		Target,
		Finding,
		User,
		Vulnerability,
		Schedule;

		/**
		 * Checks if a user attribute is active for the dynamic validator location.
		 *
		 * @param attribute User attribute.
		 * @return True if active.
		 */
		public boolean isActive(final UserAttribute attribute) {
			return (this == Target && attribute.onTarget()) ||
					(this == Finding && attribute.onFinding()) ||
					(this == User && attribute.onUser()) ||
					(this == Vulnerability && attribute.onVulnerability()) ||
					(this == Schedule && attribute.onScheduling());
		}
	}

	private final Type type;

	@Getter
	final String name;

	private final long minValue;

	private final long maxValue;

	private final int maxLength;

	private final String allowedCharacters;

	private final String disallowCharacters;

	private final Set<String> allowedValues;

	private final boolean allowScanner;

	private final boolean allowVirtualHost;

	private final boolean allowComments;

	private final boolean allowhostname;

	private final boolean allownetbios;

	private final boolean alltargets;

	private final int maxips;

	final HashSet<Method> methods;

	private final String regex;

	private final ParamValidator[] validators;

	final boolean cleanInput;

	public static ParamValidator mac(final String name, final Method... methods) {
		return new ParamValidator(Type.Mac, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator or(final String name, final ParamValidator... validators) {
		return new ParamValidator(name, validators);
	}

	public static ParamValidator netbios(final String name, final int maxLength) {
		return new ParamValidator(Type.Netbios, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator host(final String name, final int maxLength) {
		return new ParamValidator(Type.Host, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator regex(final String name, final String regex, final int maxLength, final Method... methods) {
		return new ParamValidator(Type.RegEx, name, -1, -1, maxLength, null, null, null, regex, false, methods);
	}

	public static ParamValidator regexList(final String name) {
		return new ParamValidator(Type.RegExList, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator any(final String name, final Method... methods) {
		return new ParamValidator(Type.Any, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator cvssVector(final String name, final Method... methods) {
		return new ParamValidator(Type.CvssVector, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator html(final String name, final int maxLength) {
		return new ParamValidator(Type.HTML, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator file(final String name, final Method... methods) {
		return new ParamValidator(Type.File, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator hex(final String name) {
		return new ParamValidator(Type.Hex, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator xml(final String name) {
		return new ParamValidator(Type.XML, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator json(final String name, final Method... methods) {
		return new ParamValidator(Type.JSON, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator date(final String name, final Method... methods) {
		return new ParamValidator(Type.Date, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator time(final String name) {
		return new ParamValidator(Type.Time, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator hostlist(final String name, final int maxLength) {
		return new ParamValidator(Type.HostList, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator hostIp(final String name, final Method... methods) {
		return new ParamValidator(Type.HostIp, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator alphanumeric(final String name) {
		return new ParamValidator(Type.AlphaNumeric, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator alphanumeric(final String name, final int maxLength, final String allowedCharacters, final Method... methods) {
		return new ParamValidator(Type.AlphaNumeric, name, -1, -1, maxLength, allowedCharacters, null, null, null, false, methods);
	}

	public static ParamValidator booleanValue(final String name, final Method... methods) {
		return new ParamValidator(Type.Boolean, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator digitOnly(final String name, final Method... methods) {
		return new ParamValidator(Type.DigitOnly, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator digitOnly(final String name, final long minValue, final long maxValue, final Method... methods) {
		return new ParamValidator(Type.DigitOnly, name, minValue, maxValue, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator doubleOnly(final String name) {
		return new ParamValidator(Type.DoubleOnly, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator doubleOnly(final String name, final long minValue, final long maxValue, final Method... methods) {
		return new ParamValidator(Type.DoubleOnly, name, minValue, maxValue, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator printable(final String name, final Method... methods) {
		return new ParamValidator(Type.Printable, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator printable(final String name, final int maxLength) {
		return new ParamValidator(Type.Printable, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator printable(final String name, final int maxLength, final String allowedCharacters, final String disallowCharacters, final Method... methods) {
		return new ParamValidator(Type.Printable, name, -1, -1, maxLength, allowedCharacters, disallowCharacters, null, null, false, methods);
	}

	public static ParamValidator printable(final String name, final int maxLength, final String allowedCharacters, final String disallowCharacters, final boolean cleanInput,
										   final Method... methods) {
		return new ParamValidator(Type.Printable, name, -1, -1, maxLength, allowedCharacters, disallowCharacters, null, null, cleanInput, methods);
	}

	public static ParamValidator ip(final String name, final Method... methods) {
		return new ParamValidator(Type.IP, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator domain(final String name, final Method... methods) {
		return new ParamValidator(Type.Domain, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator url(final String name, final Method... methods) {
		return new ParamValidator(Type.URL, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator cidrSyntax(final String name, final Method... methods) {
		return new ParamValidator(Type.CIDR_SYNTAX, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator ipList(final String name, final Method... methods) {
		return new ParamValidator(Type.IPList, name, false, false, false, false, false, true, -1, methods);
	}

	public static ParamValidator ipListCommaSeparted(final String name, final Method... methods) {
		return new ParamValidator(Type.IPListCommaSeparted, name, false, false, false, false, false, true, -1, methods);
	}

	public static ParamValidator ipHostList(final String name, final int maxHosts, final Method... methods) {
		return new ParamValidator(Type.IPList, name, false, true, true, false, false, true, maxHosts, methods);
	}

	public static ParamValidator ipHostListOrIpAndHost(final String name, final int maxHosts) {
		return new ParamValidator(Type.IPList, name, true, true, true, false, true, true, maxHosts);
	}

	public static ParamValidator ipHostListOrIpAndHostAll(final String name, final int maxIps) {
		return new ParamValidator(Type.IPList, name, true, true, false, true, true, true, maxIps);
	}

	public static ParamValidator ipHostListOrIpAndHostAllNoVirtualHost(final String name, final int maxIps) {
		return new ParamValidator(Type.IPList, name, true, true, true, true, false, true, maxIps);
	}

	public static ParamValidator ipHostListOrIpAndHostAllNoVirtualHostNoComments(final String name, final int maxIps) {
		return new ParamValidator(Type.IPList, name, true, true, true, true, false, false, maxIps);
	}

	public static ParamValidator email(final String name, final int maxLength) {
		return new ParamValidator(Type.Email, name, -1, -1, maxLength, null, null, null, null, false);
	}

	public static ParamValidator emailList(final String name) {
		return new ParamValidator(Type.EmailList, name, -1, -1, -1, null, null, null, null, false);
	}

	public static ParamValidator ascii(final String name, final Method... methods) {
		return new ParamValidator(Type.Ascii, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator ascii(final String name, final int maxLength, final String allowedCharacters, final String disallowedCharacters, final Method... methods) {
		return new ParamValidator(Type.Ascii, name, -1, -1, maxLength, allowedCharacters, disallowedCharacters, null, null, false, methods);
	}

	public static ParamValidator digitList(final String name, final Method... methods) {
		return new ParamValidator(Type.DigitList, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator digitList(final String name, final String allowedCharacters, final String disallowedCharacters, final String[] allowedValues,
										   final Method... methods) {
		return new ParamValidator(Type.DigitList, name, -1, -1, -1, allowedCharacters, disallowedCharacters, allowedValues, null, false, methods);
	}

	public static ParamValidator listOfValues(final String name, final String[] strings, final Method... methods) {
		return new ParamValidator(Type.ListOfValues, name, -1, -1, -1, null, null, strings, null, false, methods);
	}

	public static ParamValidator listOfValuesCaseInsensitive(final String name, final String[] strings, final Method... methods) {
		return new ParamValidator(Type.ListOfValuesCaseInsensitive, name, -1, -1, -1, null, null, strings, null, false, methods);
	}

	public static ParamValidator port(final String name) {
		return new ParamValidator(Type.DigitOnly, name, 0, MAX_PORT_NUMBER, -1, null, null, null, null, false);
	}

	public static ParamValidator bpf(final String name, final Method... methods) {
		return new ParamValidator(Type.BPF, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator timezone(final String name, final Method... methods) {
		return new ParamValidator(Type.Timezone, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	public static ParamValidator pemCert(final String name, final Method... methods) {
		return new ParamValidator(Type.PemCert, name, -1, -1, -1, null, null, null, null, false, methods);
	}

	/**
	 * Constructor.
	 *
	 * @param type Type of validator.
	 * @param name Name of parameter.
	 * @param allowhostname Is hostname allowed.
	 * @param allownetbios Is netbios allowed.
	 * @param allowScanner Is scanner allowed.
	 * @param alltargets Are all targets allowed.
	 * @param allowVirtualHosts Is virtual hosts allowed.
	 * @param allowComments Is comments allowed.
	 * @param maxIps Max number of ips.
	 * @param methods Methods when the parameter is required.
	 */
	private ParamValidator(final Type type, final String name, final boolean allowhostname, final boolean allownetbios, final boolean allowScanner, final boolean alltargets,
						   final boolean allowVirtualHosts, final boolean allowComments, final int maxIps, final Method... methods) {
		this.type = type;
		this.name = name;
		this.minValue = 0;
		this.maxValue = 0;
		this.maxLength = 0;
		this.allowedCharacters = null;
		this.allowedValues = null;
		this.allowhostname = allowhostname;
		this.allownetbios = allownetbios;
		this.allowScanner = allowScanner;
		this.allowComments = allowComments;
		this.alltargets = alltargets;
		this.maxips = maxIps;
		this.allowVirtualHost = allowVirtualHosts;
		this.disallowCharacters = null;
		this.regex = null;
		this.validators = null;
		this.cleanInput = false;
		this.methods = new HashSet<>(Arrays.asList(methods));
	}

	/**
	 * Constructor.
	 *
	 * @param type Type of validator.
	 * @param name Name of parameter.
	 * @param minValue Minimum value for numbers.
	 * @param maxValue Maximum value for numbers.
	 * @param maxLength Max length in characters.
	 * @param allowedCharacters A set of allowed characters.
	 * @param disallowedCharacters A set of disallowed characters.
	 * @param allowedValues A list of allowed values, if null this is ignored.
	 * @param regex A regex to match the data against.
	 * @param cleanInput If input should be cleaned instead of invalidated.
	 * @param methods Methods when this parameter is required.
	 */
	private ParamValidator(final Type type, final String name, final long minValue, final long maxValue, final int maxLength, final String allowedCharacters,
						   final String disallowedCharacters, final String[] allowedValues,
						   final String regex, final boolean cleanInput, final Method... methods) {
		this.type = type;
		this.name = name;
		this.minValue = minValue;
		this.maxValue = maxValue;
		this.maxLength = maxLength;
		this.allowedCharacters = allowedCharacters;
		this.disallowCharacters = disallowedCharacters;
		if (allowedValues != null) {
			this.allowedValues = new LinkedHashSet<>(Arrays.asList(allowedValues));
		}
		else {
			this.allowedValues = null;
		}
		allowhostname = false;
		allownetbios = false;
		allowScanner = false;
		alltargets = false;
		allowVirtualHost = false;
		allowComments = true;
		maxips = 0;
		this.methods = new HashSet<>(Arrays.asList(methods));
		this.regex = regex;
		this.validators = null;
		this.cleanInput = cleanInput;
	}

	/**
	 * Constructor for an or statement.
	 *
	 * @param name Name of the parameter.
	 * @param validators A list of validators.
	 */
	private ParamValidator(final String name, final ParamValidator... validators) {
		this.name = name;
		this.validators = validators;
		maxValue = 0;
		this.type = Type.Or;
		this.minValue = 0;
		this.allowedCharacters = null;
		this.allowedValues = null;
		this.allowhostname = false;
		this.allownetbios = false;
		this.allowScanner = false;
		this.alltargets = false;
		this.maxips = -1;
		this.allowVirtualHost = false;
		this.allowComments = true;
		this.methods = null;
		this.disallowCharacters = null;
		this.regex = null;
		this.maxLength = -1;
		this.cleanInput = false;
	}

	private static final Pattern pboolean = Pattern.compile("^0|1|on|off|true|false|yes|no$", Pattern.CASE_INSENSITIVE); // Pattern to validate boolean
	// Pattern to validate html tags
	private static final Pattern pmac = Pattern.compile("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
	private static final Pattern phtml = Pattern.compile("^a|h[1-5]{1}|b|span|div|font|u|i|ol|li|ul|p|em|big|strong|code|img|table|tbody|tr|td|br$");

	/**
	 * Delimiter to separate out the hostname.
	 */
	public static final String HOSTNAMEDELIMITER = "|";
	/**
	 * Used when splitting over HOSTNAMEDELIMITER.
	 */
	private static final String HOSTNAMEDELIMITER_SPLIT = "\\|";

	/**
	 * Checks an input.
	 *
	 * @param ipService IP service.
	 * @param validators A list of possible validators
	 * @param key Name of the parameter.
	 * @param value Value to check.
	 * @return An error string or null if value is ok.
	 */
	public static String check(final IpService ipService, final ParamValidator[] validators, final String key, final String value) {
		for (final ParamValidator validator : validators) {
			if (validator.getName().equals(key)) {
				return check(ipService, value, validator, Method.List);
			}
		}
		return null;
	}

	/**
	 * Checks input in JSON object.
	 *
	 * @param ipService IP service.
	 * @param validators A list of possible validators
	 * @param json JSON object
	 */
	public static void check(final IpService ipService, final ParamValidator[] validators, final JSONObject json) throws ParamValidationException {
		final List<String> messages = new ArrayList<>();
		final List<String> failedParams = new ArrayList<>();
		for (final ParamValidator validator : validators) {
			final String parameter = json.has(validator.name) ? json.get(validator.name).toString() : null;
			final String message = ParamValidator.check(ipService, parameter, validator, Method.List);
			if (message != null) {
				messages.add(message);
				failedParams.add(validator.name);
			}
		}
		if (!messages.isEmpty()) {
			throw new ParamValidationException(StringUtils.concatListValues(messages, "\n"), "", failedParams);
		}
	}

	/**
	 * Checks an input.
	 *
	 * @param ipService IP service.
	 * @param value Value to check.
	 * @param validator Validator to use.
	 * @param method Method being called to check if parameter is required.
	 * @return An error string or null if value is ok.
	 */
	public static String check(final IpService ipService, final String value, final ParamValidator validator, final Method method) {
		if (StringUtils.isEmpty(value)) {
			return (validator.methods != null && validator.methods.contains(method)) ? getErrorMessage(validator, "Must have a value") : null;
		}
		if (validator.maxLength > 0 && value.length() > validator.maxLength) {
			return getErrorMessage(validator, "Should be at most " + validator.maxLength + " characters");
		}

		if (validator.disallowCharacters != null) {
			for (int i = 0; i < validator.disallowCharacters.length(); i++) {
				if (value.indexOf(validator.disallowCharacters.charAt(i)) >= 0) {
					return getErrorMessage(validator,
							"Can not contain " + (Character.isSpaceChar(validator.disallowCharacters.charAt(i)) ? "white spaces" : validator.disallowCharacters.charAt(i)));
				}
			}
		}

		switch (validator.type) {
			case DigitOnly:
				return checkDigitOnly(value, validator);
			case DoubleOnly:
				return checkDoubleOnly(value, validator);
			case Boolean:
				return checkBoolean(value, validator);
			case AlphaNumeric:
				return checkAlphaNumeric(value, validator);
			case Printable:
				return checkPrintable(value, validator, false);
			case DigitList:
				return checkDigitList(value, validator);
			case ListOfValues:
				return checkListOfValues(value, validator);
			case ListOfValuesCaseInsensitive:
				return checkListOfValuesCaseInsensitive(value, validator);
			case Ascii:
				return checkAscii(value, validator);
			case Mac:
				return checkMac(value, validator);
			case IP:
				return checkIp(ipService, value, validator);
			case Domain:
				return checkDomain(value, validator);
			case URL:
				return checkUrl(value, validator);
			case CIDR_SYNTAX:
				return checkCidrSyntax(ipService, value, validator);
			case IPList:
				return checkIpList(ipService, value, validator);
			case IPListCommaSeparted:
				return checkIpCommaSeparated(ipService, value, validator);
			case HostList:
				return checkHostList(ipService, value, validator);
			case HOST_OR_IP_LIST:
			case HOST_OR_IP_LIST_WITH_COMMENTS:
				return checkIpList(ipService, value, validator);
			case HostIp:
				return checkHostIp(ipService, value, validator);
			case Date:
				return checkDate(value, validator);
			case Time:
				return checkTime(value, validator);
			case Email:
				return checkEmail(value, validator);
			case EmailList:
				return checkEmailList(value, validator);
			case Hex:
				return checkHex(value, validator);
			case XML:
				return checkXML(value, validator);
			case HTML:
				return checkHTML(value, validator);
			case File:
				return checkFile(value, validator);
			case CvssVector:
				return checkCvssVector(value, validator);
			case RegEx:
				return checkRegEx(value, validator);
			case RegExList:
				return checkRegExList(value, validator);
			case Host:
				return checkHost(ipService, value, validator);
			case Netbios:
				return checkNetbios(ipService, value, validator);
			case Or:
				return checkOr(ipService, value, validator, method);
			case Any:
				return null;
			case JSON:
				return checkJSON(value, validator);
			case BPF:
				return checkBpf(value, validator);
			case Timezone:
				return checkTimezone(value, validator);
			default:
		}
		return "No validator type could be found";
	}

	/**
	 * Clean input.
	 *
	 * @param value Value to clean.
	 * @param validator Validator to use, current only printable is supported.
	 * @param method Method being used for validation.
	 * @return A cleaned string.
	 */
	public static String clean(final String value, final ParamValidator validator, final Method method) {
		switch (validator.type) {
			case Printable:
				return checkPrintable(value, validator, true);
			default:
				return null;
		}
	}

	/**
	 * Checks an or statement validation.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @param method Method being used for validation.
	 * @return An error string or null if value is ok.
	 */
	private static String checkOr(final IpService ipService, final String input, final ParamValidator validator, final Method method) {
		final StringBuilder res = new StringBuilder();
		for (final ParamValidator pv : validator.validators) {
			final String r = check(ipService, input, pv, method);
			if (r == null) {
				return null;
			}
			if (res.length() > 0) {
				res.append(" or ");
			}
			res.append(r);
		}
		return res.toString();
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkNetbios(final IpService ipService, final String input, final ParamValidator validator) {
		if (!ipService.isNetbios("\\\\" + input, false)) {
			return getErrorMessage(validator, "Is not a valid netbios name");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkHost(final IpService ipService, final String input, final ParamValidator validator) {
		if (!ipService.isHostname(input)) {
			return getErrorMessage(validator, "Is not a valid hostname");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkRegEx(final String input, final ParamValidator validator) {
		try {
			final Pattern pip = Pattern.compile(validator.regex);

			if (!pip.matcher(input).matches()) {
				return getErrorMessage(validator, "Does not match: " + validator.regex);
			}
		}
		catch (final Exception e) {
			return getErrorMessage(validator, "Regex failed: " + validator.regex);
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkRegExList(final String input, final ParamValidator validator) {
		final StringTokenizer st = new StringTokenizer(input, "\n");
		while (st.hasMoreTokens()) {
			final String token = st.nextToken().trim();
			if (!StringUtils.isEmpty(token)) {
				try {
					Pattern.compile(token);
				}
				catch (final Exception e) {
					return getErrorMessage(validator, "Is not a valid regex");
				}
			}
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkCvssVector(final String input, final ParamValidator validator) {
		try {
			if (input.toLowerCase().startsWith("cvss:3.0/") || input.toLowerCase().startsWith("cvss:3.1/")) {
				CvssUtils.cvss_score(input);
				return null;
			}
			final Matcher m = Pattern.compile("(\\(.*?\\))\\s*(\\(.*?\\))").matcher(input);
			if (m.matches()) {
				CvssUtils.cvss_score(input);
				return null;
			}
			CvssUtils.cvss_score(input, null, null, true, false);
			return null;
		}
		catch (final InvalidCVSSException e) {
			return getErrorMessage(validator, "Is not a valid cvss vector");
		}
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkXML(final String input, final ParamValidator validator) {
		try {
			XMLObject.createNewDocumentBuilder(false, true).parse(new ByteArrayInputStream(input.getBytes(UTF_8)));
			return null;
		}
		catch (final Exception e) {
			return getErrorMessage(validator, "Is not a valid xml document");
		}
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkJSON(final String input, final ParamValidator validator) {
		try {
			if (input.startsWith("[")) {
				new JSONArray(input);
			}
			else {
				new JSONObject(input);
			}
			return null;
		}
		catch (final Exception e) {
			return getErrorMessage(validator, "Is not a valid json document");
		}
	}

	private static String checkFile(final String input, final ParamValidator validator) {
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkHTML(final String input, final ParamValidator validator) {
		boolean intag = false;
		boolean tagadded = false;
		StringBuilder htmltag = new StringBuilder();
		final ArrayList<String> tags = new ArrayList<>();
		final char[] cin = input.toCharArray();
		final String allow = validator.allowedCharacters;
		final String deny = validator.disallowCharacters;

		for (int i = 0; i < cin.length; i++) {
			if (cin[i] < 32 || (cin[i] > 126 && cin[i] < 192) || cin[i] > 255 || (deny != null && deny.indexOf(cin[i]) > -1)) {
				if ("\r\n\t".indexOf(cin[i]) == -1 && (allow == null || allow.indexOf(cin[i]) == -1)) {
					return getErrorMessage(validator, "Invalid characters found: " + cin[i]);
				}

			}
			else {
				if (intag) {
					if (cin[i] == 62) { // Got > then check content of htmltag
						if (!tagadded && htmltag.charAt(0) != '/') { // Add tag if not already added i.e. <b>
							tags.add(htmltag.toString().toLowerCase());
						}

						final String tag = tags.size() > 0 ? tags.get(tags.size() - 1) : null;
						if (tag == null) {
							return getErrorMessage(validator, "Invalid tag found");
						}

						if (htmltag.charAt(0) == '/') { // If end tag then check we got start tag
							if (!htmltag.substring(1).equalsIgnoreCase(tag)) {
								return getErrorMessage(validator, "Invalid end tag found: " + htmltag.substring(1));
							}
							else {
								tags.remove(tags.size() - 1);
							}
						}
						else {
							// Check the attributes
							if (htmltag.indexOf(" ") != -1) {
								if (htmltag.toString().toLowerCase().indexOf(" on") != -1) {
									return getErrorMessage(validator, "Tag found with an on attribute: " + htmltag);
								}
							}

							// Check we got a valid start tag
							// Specials case for br and img
							if ("br".equals(tag) || "img".equals(tag)) {
								tags.remove(tags.size() - 1);
							}
							else {
								if ("/".equals(tag.substring(tag.length() - 1)) && phtml.matcher(tag.substring(0, tag.length() - 1)).matches()) {
									tags.remove(tags.size() - 1);
								}
								else if (!phtml.matcher(tag).matches()) { // Check tag with list of valid tags
									return getErrorMessage(validator, "Invalida tag found: " + tag);
								}
								// Todo valid other attributes in htmltag
							}
						}

						intag = false;
					}
					else {
						if (cin[i] == 32 && htmltag.length() > 0 && htmltag.charAt(0) != '/' && !tagadded) { // Save tag in list to verify we have both start and end tag
							tags.add(htmltag.toString());
							tagadded = true;
						}

						htmltag.append(cin[i]);
					}
				}

				if (cin[i] == 60) { // If < found then we must be in tag mode
					if (intag) { // We are already in a html tag
						return getErrorMessage(validator, "Already in a tag");
					}
					intag = true;
					tagadded = false;
					htmltag = new StringBuilder();
				}
			}
		}

		if (intag || tags.size() > 0) { // If still in end tag then some is wrong or if tags is greater than 0 then we miss a end tag
			return getErrorMessage(validator, "Tags not closed properly");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkHex(final String value, final ParamValidator validator) {
		try {
			if (value.startsWith("0x")) {
				Long.parseLong(value.substring(2), 16);
			}
			else {
				Long.parseLong(value, 16);
			}
			return null;
		}
		catch (final NumberFormatException e) {
			return getErrorMessage(validator, "Is not a valid hex value");
		}
	}

	/**
	 * Validates the input.
	 * Not guaranteed to completely adhere to RFC 822.
	 *
	 * @param input Comma-separated list of email addresses, whose emails will be validated separately.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkEmailList(final String input, final ParamValidator validator) {
		final StringTokenizer st = new StringTokenizer(input, ",");
		while (st.hasMoreTokens()) {
			final String token = st.nextToken().trim();
			final String result;
			if ((result = checkEmail(token, validator)) != null) {
				return result;
			}
		}
		return null;
	}

	/**
	 * Validates the input.
	 * Not guaranteed to completely adhere to RFC 822.
	 *
	 * @param input Email address to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkEmail(final String input, final ParamValidator validator) {
		// allowLocal = true makes us allow foo@localhost
		// allowTld = false makes us exclude foo@.se
		final EmailValidator ev = EmailValidator.getInstance(true, false);
		if (!ev.isValid(input)) {
			return getErrorMessage(validator, input + " is not a valid email");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	private static String checkDate(final String input, final ParamValidator validator) {
		String dateformat = "yyyy-MM-dd";
		if (input.length() == 16) {
			dateformat = "yyyy-MM-dd HH:mm";
		}
		else if (input.length() == 19) {
			dateformat = "yyyy-MM-dd HH:mm:ss";
		}

		final SimpleDateFormat sdf = new SimpleDateFormat(dateformat);
		sdf.setLenient(false);

		try {
			sdf.parse(input);
		}
		catch (final ParseException e) {
			return getErrorMessage(validator, "Is not a valid date");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	private static String checkTime(final String input, final ParamValidator validator) {
		String timeformat = "HH:mm";
		if (input.length() == 8) {
			timeformat = "HH:mm:ss";
		}

		final SimpleDateFormat sdf = new SimpleDateFormat(timeformat);
		sdf.setLenient(false);

		try {
			sdf.parse(input);
		}
		catch (final ParseException e) {
			return getErrorMessage(validator, "Is not a valid time");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkHostList(final IpService ipService, final String input, final ParamValidator validator) {
		final StringTokenizer st = new StringTokenizer(input, "\n");
		while (st.hasMoreTokens()) {
			final String token = st.nextToken().replaceAll("\r", "").trim();
			if (token.contains(",")) { // Comma separated list of hostnames
				final StringTokenizer cst = new StringTokenizer(token, ",");
				while (cst.hasMoreTokens()) {
					final String subtoken = cst.nextToken().trim();
					if (subtoken.length() > 255 || !ipService.isHostname(subtoken)) {
						return getErrorMessage(validator, "Invalid host name found");
					}
				}
			}
			else {
				if (token.length() > 255 || !ipService.isHostname(token)) { // Single host
					return getErrorMessage(validator, "Invalid host name found");
				}
			}
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkIpList(final IpService ipService, final String input, final ParamValidator validator) {
		try {
			int count = 0;
			final StringTokenizer st = new StringTokenizer(input, "\n");
			while (st.hasMoreTokens()) {
				String token = st.nextToken().replaceAll("\r", "").trim();
				if (validator.allowComments) {
					token = token.replaceAll(TARGETLIST_COMMENTS_REG_EXP, "").trim();
				}
				if (!StringUtils.isEmpty(token)) {
					if (validator.allowScanner && token.contains("<")) {
						token = token.split("<")[0]; // Remove scanner
					}

					int pos = 0;
					if (validator.allowVirtualHost && token.contains(HOSTNAMEDELIMITER)) { // Virtualhost
						if (!(ipService.isValid(token.split(HOSTNAMEDELIMITER_SPLIT)[0]) || (validator.allowhostname && ipService.isHostname(
								token.split(HOSTNAMEDELIMITER_SPLIT)[0])) ||
								(validator.allownetbios && ipService.isNetbios(token.split(HOSTNAMEDELIMITER_SPLIT)[0], true)))) {
							return token;
						}

						if (token.split(":").length > 1) {
							final String msg = check(ipService, token.split(HOSTNAMEDELIMITER_SPLIT)[1], ParamValidator.hostlist(validator.name, 2048), null);
							if (msg != null) {
								return token;
							}
							count++;
						}
					}
					else if (ipService.isAgentId(token)) {
						if (check(ipService, token, ParamValidator.printable(validator.name, 2048), null) != null) {
							return token;
						}
						count++;
					}
					else if (ipService.isInstanceId(token)) {
						String msg;
						final boolean hasAttachedIdentifier = token.contains("§");
						if (hasAttachedIdentifier) {
							for (final String part : token.split("§")) {
								msg = check(ipService, part, ParamValidator.printable(validator.name, 2048), null);
								if (msg != null) {
									return token;
								}
							}
						}
						else {
							msg = check(ipService, token, ParamValidator.printable(validator.name, 2048), null);
							if (msg != null) {
								return token;
							}
						}

						count++;
					}
					else if (token.contains("-")) { // IP range *************-************* (or any hostname with dash signs)
						pos = token.indexOf("-");
						final String sip = token.substring(0, pos);
						final String eip = token.substring(pos + 1);

						boolean notvalid = StringUtils.isEmpty(sip) || !ipService.isValid(sip);
						notvalid = notvalid || StringUtils.isEmpty(eip) || !ipService.isValid(eip);

						if (!StringUtils.isEmpty(sip) && ipService.isValid(sip) && notvalid && !StringUtils.isEmpty(eip) && eip.matches("[\\.\\d]+")) {
							return getErrorMessage(validator, "Is not a valid ip: " + token);
						}
						if (!notvalid && ipService.isValidIPv6(sip)) {
							notvalid = IPv6Address.fromString(sip).compareTo(IPv6Address.fromString(eip)) > 0;
							if (notvalid) {
								return getErrorMessage(validator, "Is not a valid ip: " + token);
							}
						}
						else if (!notvalid && ipService.isValidIPv4(sip)) {
							notvalid = ipService.getIpValue(sip) > ipService.getIpValue(eip);
							if (notvalid) {
								return getErrorMessage(validator, "Is not a valid ip: " + token);
							}
						}

						if (notvalid) {
							if (!(ipService.isValid(token) || (validator.allowhostname && ipService.isHostname(token)) || (validator.allownetbios
									&& ipService.isNetbios(token, true)))) {
								return getErrorMessage(validator, "Is not a valid target: " + token);
							}
							count++;
						}
						else {
							count += ipService.getIpCount(token);
						}
					}
					else if (token.contains("/")) { // IP network *************/24
						pos = token.indexOf("/");
						final String sip = token.substring(0, pos);
						final int net = StringUtils.getIntValue(token.substring(pos + 1), -1);

						if (StringUtils.isEmpty(sip) || !ipService.isValid(sip)) {
							return getErrorMessage(validator, "Is not a valid ip: " + token);
						}

						final boolean isIpv6 = ipService.isValidIPv6(sip);
						if (((validator.alltargets && net < 0) || (!validator.alltargets && net < 1)) || net > (isIpv6 ? 128 : 32)) {
							return getErrorMessage(validator, "Is not a valid ip: " + token);
						}
						count += ipService.getIpCount(token);
					}
					else if (token.contains(",")) { // Comma separated list of IP
						final StringTokenizer cst = new StringTokenizer(token, ",");
						while (cst.hasMoreTokens()) {
							final String t = cst.nextToken().trim();
							if (!(ipService.isValid(t) || (validator.allowhostname && ipService.isHostname(t)) || (validator.allownetbios
									&& ipService.isNetbios(t, true)))) {
								return getErrorMessage(validator, "Is not a valid target: " + token);
							}
							count++;
						}
					}
					else {
						if (!(ipService.isValid(token) || (validator.allowhostname && ipService.isHostname(token)) || (validator.allownetbios
								&& ipService.isNetbios(token, true)))) {
							return getErrorMessage(validator, "Is not a valid target: " + token);
						}
						count++;
					}
					if (validator.maxips > 0 && count > validator.maxips) {
						return getErrorMessage(validator, "Contains too many ips, max is " + validator.maxips);
					}
				}
			}
		}
		catch (final Exception e) {
			return null;
		}
		return null;
	}

	/**
	 * Validates comma separated IP list.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkIpCommaSeparated(final IpService ipService, final String input, final ParamValidator validator) {
		final String updatedInput = input.replace(",", "\n");
		return checkIpList(ipService, updatedInput, validator);
	}

	/**
	 * Validates a list of IP or hostnames input. input can be a ',' or '\n' separated list.
	 *
	 * @param ipService IP service.
	 * @param input Value to be validated.
	 * @return An error string or null if input is ok.
	 */
	public static String checkHostOrIpList(final IpService ipService, final String input) throws SQLException {
		final String[] hostsOrIps = input.split("\n|,");
		final List<String> invalidInputs = new ArrayList<>();
		for (final String hostOrIp : hostsOrIps) {
			final String trimmedHostOrIp = hostOrIp.trim();
			if (!ipService.isHostname(trimmedHostOrIp) && !(ipService.isValid(trimmedHostOrIp) && !IPAddress.isValidWithNetMask(trimmedHostOrIp))) {
				invalidInputs.add(trimmedHostOrIp + " is not a valid hostname or ip address");
			}
		}

		String result = null;
		if (!invalidInputs.isEmpty()) {
			result = String.join(",", invalidInputs);
		}
		return result;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkMac(final String input, final ParamValidator validator) {
		if (!pmac.matcher(input).matches()) {
			return getErrorMessage(validator, input + " is not a valid mac address");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkIp(final IpService ipService, final String input, final ParamValidator validator) {
		if (!ipService.isValid(input) && !IPAddress.isValidWithNetMask(input)) {
			return getErrorMessage(validator, input + " is not a valid ip address");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkDomain(final String input, final ParamValidator validator) {
		if (!DomainValidator.getInstance().isValid(input)) {
			return getErrorMessage(validator, input + " is not a valid domain");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkUrl(final String input, final ParamValidator validator) {
		try {
			final int portNumber = new URL(input).getPort();
			if (portNumber > MAX_PORT_NUMBER || portNumber == 0) {
				return getErrorMessage(validator, input + " is not a valid URL: port out of range");
			}
		}
		catch (final MalformedURLException ex) {
			return getErrorMessage(validator, input + " is not a valid URL");
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkCidrSyntax(final IpService ipService, final String input, final ParamValidator validator) {
		final String error = getErrorMessage(validator, input + " is not a valid CIDR range");
		if (input == null || !input.contains("/")) {
			return error;
		}

		try {
			final String maxIp = ipService.getMaxIp(input);
			final String lowIp = ipService.getLowIp(input);
			if (lowIp == null || maxIp == null || !(ipService.isValidIPv6(maxIp) || ipService.isValidIPv4(maxIp)) || !(ipService.isValidIPv6(lowIp) || ipService.isValidIPv4(
					lowIp))) {
				return error;
			}
		}
		catch (final SQLException ex) {
			return error;
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkListOfValues(final String input, final ParamValidator validator) {
		if (!validator.allowedValues.contains(input)) {
			return getErrorMessage(validator, "Can only be of the following values: " + StringUtils.concatHashValues(validator.allowedValues, "|", false));
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	private static String checkListOfValuesCaseInsensitive(final String input, final ParamValidator validator) {
		if (!validator.allowedValues.contains(input != null ? input.toLowerCase() : null)) {
			return getErrorMessage(validator, "Can only be of the following values: " + StringUtils.concatHashValues(validator.allowedValues, "|", false));
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkDigitList(final String input, final ParamValidator validator) {
		final StringTokenizer tokenizer = new StringTokenizer(input, ",");
		while (tokenizer.hasMoreTokens()) {
			final String token = tokenizer.nextToken();
			try {
				Long.parseLong(token);
			}
			catch (final NumberFormatException e) {
				return getErrorMessage(validator, "Can only contain a comma-separated list of digits");
			}
		}

		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkAlphaNumeric(final String value, final ParamValidator validator) {
		for (int i = value.length() - 1; i >= 0; i--) {
			if (!Character.isDigit(value.charAt(i)) && !Character.isLetter(value.charAt(i))) {
				if (validator.allowedCharacters == null || validator.allowedCharacters.indexOf(value.charAt(i)) < 0) {
					return getErrorMessage(validator, "Can only contain digits and characters");
				}
			}
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	private static String checkBoolean(final String value, final ParamValidator validator) {
		if (!pboolean.matcher(value).matches()) {
			return getErrorMessage(validator, "Should be a boolean value");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkDigitOnly(final String value, final ParamValidator validator) {
		try {
			final long longValue = Long.parseLong(value);
			if (validator != null && (validator.minValue != -1 || validator.maxValue != -1)) {
				if (longValue < validator.minValue) {
					return getErrorMessage(validator, "Is less than " + validator.minValue);
				}
				if (longValue > validator.maxValue) {
					return getErrorMessage(validator, "Is more than " + validator.maxValue);
				}
			}
		}
		catch (final NumberFormatException e) {
			return getErrorMessage(validator, "Should be a number only");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	private static String checkDoubleOnly(final String value, final ParamValidator validator) {
		try {
			final Double doubleValue = Double.parseDouble(value);
			if (validator != null && (validator.minValue != -1 || validator.maxValue != -1)) {
				if (doubleValue < validator.minValue) {
					return getErrorMessage(validator, "Is less than " + validator.minValue);
				}
				if (doubleValue > validator.maxValue) {
					return getErrorMessage(validator, "Is more than " + validator.maxValue);
				}
			}
		}
		catch (final NumberFormatException e) {
			return getErrorMessage(validator, "Should be a number only");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @param cleanInput True if input should be cleaned instead of invalidated.
	 * @return An error string or null if value is ok.
	 */
	public static String checkPrintable(final String value, final ParamValidator validator, final boolean cleanInput) {
		final StringBuilder cleaned = new StringBuilder();

		for (int offset = 0; offset < value.length(); ) {
			final int codePoint = value.codePointAt(offset);
			offset += Character.charCount(codePoint);

			final int type = Character.getType(codePoint);
			if ((type == Character.CONTROL || type == Character.FORMAT || type == Character.PRIVATE_USE || type == Character.SURROGATE || type == Character.UNASSIGNED) &&
					(codePoint != 0x202b && codePoint != 0x202c && codePoint != '\n' && codePoint != '\r' && codePoint != '\t')) {
				if (!cleanInput) {
					return getErrorMessage(validator, "Can only contain printable characters");
				}
			}
			else if (cleanInput) {
				cleaned.appendCodePoint(codePoint);
			}
		}

		return cleanInput ? cleaned.toString() : null;
	}

	/**
	 * Validates the input.
	 *
	 * @param value Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkAscii(final String value, final ParamValidator validator) {
		final char[] chars = value.toCharArray();

		for (int i = 0; i < chars.length; i++) {
			if (chars[i] < 32 || (chars[i] > 126 && chars[i] < 192) || chars[i] > 255) {
				if (validator.allowedCharacters == null || validator.allowedCharacters.indexOf(chars[i]) < 0) {
					return getErrorMessage(validator, "Can only contain ascii characters");
				}
			}
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param ipService IP service.
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkHostIp(final IpService ipService, final String input, final ParamValidator validator) {
		if (!ipService.isHostname(input) && !ipService.isValid(input)) {
			return getErrorMessage(validator, input + " is not a valid host/ip.");
		}
		return null;
	}

	/**
	 * Validates the input.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkBpf(final String input, final ParamValidator validator) {
		try {
			final Inet4Address netmask = (Inet4Address) InetAddress.getByAddress(new byte[] {127, 0, 0, 1});
			Pcaps.compileFilter(65536, DataLinkType.NULL, input, BpfProgram.BpfCompileMode.NONOPTIMIZE, netmask);
		}
		catch (final PcapNativeException e) {
			return getErrorMessage(validator, input + " is not a valid BPF expression");
		}
		catch (final Throwable t) {
			LOG.error("Failed to validate BPF expression", t);
			return getErrorMessage(validator, "Failed to validate BPF expression");
		}

		return null;
	}

	/**
	 * Validates the timezone.
	 *
	 * @param input Value to validate.
	 * @param validator Validator to use.
	 * @return An error string or null if value is ok.
	 */
	public static String checkTimezone(final String input, final ParamValidator validator) {

		if (!ZoneId.getAvailableZoneIds().contains(input)) {
			return getErrorMessage(validator, input + " is not a valid timezone");
		}

		return null;
	}

	private static String getErrorMessage(final ParamValidator validator, final String error) {
		return (validator == null || StringUtils.isEmpty(validator.name) ? "" : (validator.name + " - ")) + error;
	}

	public static ParamValidator[] getDynamicValidators(final UserService userService, final long userId, final DynamicValidatorLocation location) throws SQLException {
		return getDynamicValidators(userService, userId, location, "");
	}

	/**
	 * Gets a dynamic validator.
	 *
	 * @param userService User service.
	 * @param userId Userid of the logged on user.
	 * @param location A location of dynamic validators.
	 * @param prefix Prefix to user to the parameter name.
	 * @return A set of validators.
	 */
	public static ParamValidator[] getDynamicValidators(final UserService userService, final long userId, final DynamicValidatorLocation location, final String prefix)
			throws SQLException {
		if (userId <= 0) {
			return new ParamValidator[0];
		}

		final List<ParamValidator> res = new ArrayList<>();

		final List<? extends UserAttribute> attributes = userService.getUserAttributes(userId, UserService.AttributeMode.ALL);
		for (final UserAttribute attribute : attributes) {
			if (attribute.isActive() && location.isActive(attribute)) {
				switch (attribute.getType()) {
					case Text:
						res.add(ParamValidator.printable(prefix + "CUSTOM" + attribute.getColumnId(), 1024, null, null, attribute.isRequired() ? Method.Update : null));
						break;
					case Numeric:
						final Pattern pattern = Pattern.compile("(-?\\d+)-(-?\\d+)");
						final Matcher matcher = pattern.matcher(StringUtils.setEmpty(attribute.getAcceptableValues(), ""));
						if (matcher.matches()) {
							res.add(ParamValidator.digitOnly(prefix + "CUSTOM" + attribute.getColumnId(), Integer.parseInt(matcher.group(1)),
									Integer.parseInt(matcher.group(2)), attribute.isRequired() ? Method.Update : null));
						}
						else {
							res.add(ParamValidator.digitOnly(prefix + "CUSTOM" + attribute.getColumnId(), -1, -1, attribute.isRequired() ? Method.Update : null));
						}
						break;
					case Checkbox:
						res.add(ParamValidator.booleanValue(prefix + "CUSTOM" + attribute.getColumnId()));
						break;
					case Combo:
						res.add(ParamValidator.listOfValues(prefix + "CUSTOM" + attribute.getColumnId(),
								StringUtils.setEmpty(attribute.getAcceptableValues(), "").split("\\|"), attribute.isRequired() ? Method.Update : null));
						break;
					case Date:
						res.add(ParamValidator.date(prefix + "CUSTOM" + attribute.getColumnId()));
						break;
					default:
						res.remove(res.size() - 1);
				}
			}
		}
		return res.toArray(new ParamValidator[0]);
	}

	/**
	 * Check validity of certificate's format
	 *
	 * @param pemCert base16 file content
	 * @param validator param validator
	 * @return boolean
	 */
	public static String checkPEMCertificate(final String pemCert, final ParamValidator validator) {
		if (StringUtils.isEmpty(pemCert)) {
			return null;
		}
		try {
			java.security.Security.addProvider(new BouncyCastleProvider());
			try (final PEMParser parser = new PEMParser(new InputStreamReader(new ByteArrayInputStream(pemCert.getBytes(UTF_8)), UTF_8))) {
				final PemObject object = parser.readPemObject();
				if (object == null) {
					return getErrorMessage(validator, "No valid pem certificate found");
				}
			}
		}
		catch (final IOException exception) {
			LOG.error("Failed to validate pem certificate", exception);
			return getErrorMessage(validator, "Failed to validate pem certificate");
		}
		return null;
	}
}