package com.chilicoders.util;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.UserBusiness;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.util.thread.TimerHandler;

public class PciDssQaTask extends TimerTask {
	private static final Logger LOG = LogManager.getLogger(PciDssQaTask.class);

	/**
	 * Schedules the update task, depending on server configuration.
	 *
	 * @param reschedule If <code>true</code> the task will be scheduled at next time, but if <code>false</code> will run at first possible opportunity.
	 */
	public static void scheduleTask(final boolean reschedule) {
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, 1);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		TimerHandler.schedule(TimerHandler.Task.PciDssQa, cal.getTime());
	}

	/**
	 * @see java.util.TimerTask#run()
	 */
	@Override
	public void run() {
		LOG.info("Starting Pci Dss Qa Task");
		try {
			final HashMap<String, String> hm = new HashMap<>();

			final int limit = Configuration.getProperty(ConfigurationIntKey.pci_dss_qa_limit);
			final List<Map<String, Object>> targets = XmlAble.getGenericObjects(
					"SELECT vctarget, dreportdate, xuserxid, (SELECT vccompany FROM tusers u WHERE u.xid = r.xuserxid) AS company FROM treportentrys r "
							+ "WHERE xtemplate = 10 AND dreportdate > NOW() - '1 month'::INTERVAL AND reachable = 1 ORDER BY RANDOM() LIMIT " + limit);
			final StringBuilder message = new StringBuilder();
			int count = 0;
			for (final Map<String, Object> target : targets) {
				message.append("Customer: ").append("[:CUSTOMER_" + count + "]\n");
				hm.put("CUSTOMER_" + count, (String) target.get("company"));
				message.append("Date: ").append("[:DATE_" + count + "]\n");
				hm.put("DATE_" + count, DateUtils.formatLongTimeDate((Date) target.get("dreportdate")));
				message.append("Target: ").append("[:TARGET_" + count + "]\n");
				hm.put("TARGET_" + count, (String) target.get("vctarget"));
				message.append("\n");
				count++;
			}
			if (message.length() > 0) {
				hm.put("TARGETS", message.toString());
			}

			final StringBuilder disputes = new StringBuilder();
			final List<Map<String, Object>> statistics = XmlAble.getGenericObjects(
					"SELECT TO_CHAR(disputedate, 'yyyy-mm') AS disputedate, COUNT(*) AS disputes FROM treport_vulns v LEFT JOIN treportentrys e ON e.xid = v.fk_treportentrys_xid "
							+ "WHERE e.bpci = 1 AND v.disputedate > NOW() - '6 month'::INTERVAL GROUP BY TO_CHAR(disputedate, 'yyyy-mm') ORDER BY 1");
			for (final Map<String, Object> statistic : statistics) {
				disputes.append("Month: " + (String) statistic.get("disputedate") + ", Disputes: " + (Long) statistic.get("disputes"));
				disputes.append("\n");
			}
			if (disputes.length() > 0) {
				hm.put("DISPUTES", disputes.toString());
			}

			if (message.length() > 0 || disputes.length() > 0) {
				UserBusiness.sendEmailtoUser(Configuration.getProperty(ConfigurationKey.pci_dss_qa_mail), null, null, "pci.dss.qa", hm);
			}
		}
		catch (final Exception e) {
			LOG.error("Error Pci Dss Qa Task", e);
		}

		scheduleTask(true);
	}
}
