package com.chilicoders.util;

import java.util.Calendar;
import java.util.Date;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.AgentBusiness;
import com.chilicoders.util.thread.TimerHandler;

/**
 * Used to update pending sales.
 */
public class UpdatePendingSalesTask extends TimerTask {
	/**
	 * Our logger instance.
	 */
	private static final Logger LOG = LogManager.getLogger(UpdatePendingSalesTask.class);

	/**
	 * Schedules the update task, depending on server configuration.
	 *
	 * @param reschedule If <code>true</code> the task will be scheduled at next time, but if <code>false</code> will run at first possible opportunity.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (reschedule) {
			final Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DAY_OF_MONTH, 1);
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 15);
			TimerHandler.schedule(TimerHandler.Task.UpdatePendingSales, cal.getTime());
		}
		else {
			TimerHandler.schedule(TimerHandler.Task.UpdatePendingSales, new Date());
		}
	}

	/**
	 * @see java.util.TimerTask#run()
	 */
	@Override
	public void run() {
		LOG.info("Starting pending sales update");
		try {
			final AgentBusiness agent = new AgentBusiness();
			agent.updatePendingSales();
		}
		catch (final Exception e) {
			LOG.error("Error updating pending sales", e);
		}
		scheduleTask(true);
	}
}
