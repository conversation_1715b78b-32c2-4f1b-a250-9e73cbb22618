package com.chilicoders.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.cert.CertificateException;
import java.security.spec.InvalidKeySpecException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;

import javax.net.ssl.SSLHandshakeException;

import org.apache.http.client.HttpClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.exceptions.Base64DecodingException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.chilicoders.app.Hiab;
import com.chilicoders.bl.WorkflowBusiness;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.FindingImpl;
import com.chilicoders.db.objects.JiraIssue;
import com.chilicoders.db.objects.JiraServer;
import com.chilicoders.db.objects.JiraTicket;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.integrations.api.IntegrationKeyService;
import com.chilicoders.integrations.model.IntegrationKey;
import com.chilicoders.model.KeyType;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.service.UserServiceImpl;
import com.google.api.client.auth.oauth.OAuthAuthorizeTemporaryTokenUrl;
import com.google.api.client.auth.oauth.OAuthCredentialsResponse;
import com.google.api.client.auth.oauth.OAuthGetAccessToken;
import com.google.api.client.auth.oauth.OAuthGetTemporaryToken;
import com.google.api.client.auth.oauth.OAuthRsaSigner;
import com.google.api.client.http.BasicAuthentication;
import com.google.api.client.http.ByteArrayContent;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.HttpResponseException;
import com.google.api.client.http.apache.v2.ApacheHttpTransport;

public class Jira {

	static final String JIRA_OAUTH_REQUEST_TOKEN = "/plugins/servlet/oauth/request-token";
	static final String JIRA_OAUTH_AUTHORIZE = "/plugins/servlet/oauth/authorize";
	static final String JIRA_OAUTH_ACCESS_TOKEN = "/plugins/servlet/oauth/access-token";

	private static final Logger LOG = LogManager.getLogger(Jira.class);
	private HttpClient httpClient = null;
	private String uri;
	private String username;
	private String password;
	private int authenticationType;
	private String oauthToken;
	private String oauthConsumerKey;
	private long userId;
	@SuppressWarnings("unused")
	private long customerId;
	private String feature = "JIRA";
	private String finishedStatus;
	private String issueType;
	private String projectKey;
	private boolean linkOldIssues;

	private boolean instanceBroken = false;
	private static HashMap<Long, Jira> _instance = new HashMap<>();

	private enum JIRAVariables {
		Project("project"),
		Id("id"),
		Key("key"),
		Description("description"),
		Priority("priority"),
		Status("status"),
		Summary("summary"),
		Subtasks("subtasks"),
		Parent("parent"),
		IssueType("issuetype"),
		Name("name"),
		Reporter("reporter"),
		Username("username"),
		Fields("fields"),
		Issues("issues"),
		Type("type"),
		InwardIssue("inwardIssue"),
		OutwardIssue("outwardIssue"),
		Errors("errors"),
		ErrorMessages("errorMessages");

		private String text;

		private JIRAVariables(final String text) {
			this.text = text;
		}

		@Override
		public String toString() {
			return text;
		}
	}

	/**
	 * Create new Jira object.
	 *
	 * @param conn Database connection
	 * @param userId User id
	 */
	private Jira(final Connection conn, final long userId) {
		this.userId = userId;
		try {
			this.httpClient = SslUtils.getHttpClient(new UserServiceImpl(conn), Configuration.getConfigService(), userId, this.feature);
			final JiraServer jira = JiraServer.getById(JiraServer.class, conn, Access.ADMIN, userId);
			this.uri = jira.getUri();
			this.username = jira.getUsername();
			this.password = jira.getPassword();
			this.authenticationType = jira.getAuthenticationType();
			this.oauthToken = jira.getOAuthToken();
			this.oauthConsumerKey = jira.getOAuthConsumerKey();
			this.finishedStatus = jira.getFinishedStatus();
			this.issueType = jira.getIssueType();
			this.projectKey = jira.getProjectKey();
			this.linkOldIssues = jira.isLinkOldIssues();
		}
		catch (final Exception e) {
			LOG.warn("Failed initialize JIRA", e);
			this.instanceBroken = true;
		}
	}

	/**
	 * Get Jira instance.
	 *
	 * @param conn Database connection
	 * @param userId User id
	 * @return Jira object
	 */
	public static synchronized Jira getInstance(final Connection conn, final long userId) {
		if (_instance.get(userId) == null || _instance.get(userId).instanceBroken) {
			_instance.put(userId, new Jira(conn, userId));
		}
		return _instance.get(userId);
	}

	public static synchronized void destroy(final long userId) {
		_instance.remove(userId);
	}

	/**
	 * Send a GET request to JIRA and return the result. Longest query tested and worked is 100 different conditions.
	 *
	 * @param conn Database connection
	 * @param query Jql query to execute to retrieve objects. Can not be combined with metaData.
	 * @param metaData Metadata to fetch. Can not be combined with query.
	 * @return JSONObject of the request response where JIRAVariables.Result.toString() is either a single object or an array. Will return null if the request wasn't properly executed.
	 */
	private String getAction(final Connection conn, final String query, final String metaData) throws UnsupportedEncodingException {
		HttpRequest req = null;
		try {
			if (this.httpClient == null) {
				this.httpClient = SslUtils.getHttpClient(new UserServiceImpl(conn), Configuration.getConfigService(), this.userId, this.feature);
			}
			HttpRequestInitializer params = null;
			if (this.isOAuth()) {
				final IntegrationKeyService keyService = ServiceProvider.getIntegrationKeyService(conn);
				final IntegrationKey key = keyService.getKey(this.userId, KeyType.RSA);
				params = this.getOAuthParams(key.getKeyPair().getPrivate());
			}
			else {
				params = this.getBasicAuthParams();
			}
			final HttpRequestFactory reqFactory = new ApacheHttpTransport(this.httpClient).createRequestFactory(params);
			req = reqFactory.buildGetRequest(new GenericUrl(
					new URI(this.uri + "/rest/api/2/" + (StringUtils.isEmpty(metaData) ? "search?jql=" + URLEncoder.encode(query, "UTF-8") : metaData)).normalize()));
		}
		catch (final KeyManagementException | NoSuchAlgorithmException | KeyStoreException
					 | CertificateException | InvalidKeySpecException | IOException | Base64DecodingException | SQLException | URISyntaxException e) {
			LOG.warn("JIRA failed to build request, Error: ", e);
			return null;
		}
		try {
			return req.execute().parseAsString();
		}
		catch (final IllegalStateException | SSLHandshakeException error) {
			LOG.info("JIRA request failed: ", error);
			return null;
		}
		catch (final Exception e) {
			LOG.warn("JIRA request could not be executed: ", e);
			return null;
		}
	}

	/**
	 * Post an object to JIRA.
	 *
	 * @param conn Database connection
	 * @param json Object to post.
	 * @param type Which metadata to use ex. issue, issueLink etc.
	 * @return JSONObject The posted object.
	 */
	private JSONObject postAction(final Connection conn, final JSONObject json, final String type) throws UnsupportedEncodingException {
		HttpRequest req = null;
		try {
			if (this.httpClient == null) {
				this.httpClient = SslUtils.getHttpClient(new UserServiceImpl(conn), Configuration.getConfigService(), this.userId, this.feature);
			}
			HttpRequestInitializer params = null;
			if (this.isOAuth()) {
				final IntegrationKeyService keyService = ServiceProvider.getIntegrationKeyService(conn);
				final IntegrationKey key = keyService.getKey(this.userId, KeyType.RSA);
				params = this.getOAuthParams(key.getKeyPair().getPrivate());
			}
			else {
				params = this.getBasicAuthParams();
			}
			final HttpRequestFactory reqFactory = new ApacheHttpTransport(this.httpClient).createRequestFactory(params);
			req = reqFactory.buildPostRequest(new GenericUrl(new URI(this.uri + "/rest/api/2/" + type + "/").normalize()), null);
		}
		catch (final KeyManagementException | NoSuchAlgorithmException | KeyStoreException
					 | CertificateException | InvalidKeySpecException | IOException | Base64DecodingException | SQLException | URISyntaxException e) {
			LOG.warn("JIRA failed to build request, Error: ", e);
			return null;
		}
		req.setContent(new ByteArrayContent("application/json", json.toString().getBytes("utf-8")));
		String jsonString = null;
		try {
			jsonString = req.execute().parseAsString();
			if (!StringUtils.isEmpty(jsonString)) {
				return new JSONObject(jsonString);
			}
			else {
				return new JSONObject();
			}
		}
		catch (final JSONException | HttpResponseException e) {
			LOG.info("JIRA request could not be executed. Response: " + jsonString);
			return null;
		}
		catch (final Exception e) {
			LOG.warn("JIRA request could not be executed. Response: " + jsonString + " Error: ", e);
			return null;
		}
	}

	/**
	 * Queues a JiraTicket for creation in JIRA; the queue is handled from UpdateJiraTask.
	 *
	 * @param conn A database connection.
	 * @param ticket The JiraTicket to create in JIRA.
	 */
	public void queueTicket(final Connection conn, final JiraTicket ticket) throws SQLException {
		boolean queued = false;
		final List<JiraTicket> tickets =
				JiraTicket.fetchObjects(JiraTicket.class, conn, Access.ADMIN, "xuserxid = ? AND name = ? AND xid != ?", ticket.getUserId(), ticket.getName(), ticket.getId());
		final FindingImpl finding = FindingImpl.getById(FindingImpl.class, conn, Access.ADMIN, ticket.getFindingId());
		if (tickets.size() > 0) {
			for (final JiraTicket oldTicket : tickets) {
				final FindingImpl oldFinding = FindingImpl.getById(FindingImpl.class, conn, Access.ADMIN, oldTicket.getFindingId());
				if (oldFinding != null && finding.getTarget().equals(oldFinding.getTarget())) {
					oldTicket.setCreateTicket(true);
					oldTicket.save(conn);
					queued = true;
					break;
				}
			}
		}
		if (!queued) {
			ticket.setCreateTicket(true);
			ticket.save(conn);
			queued = true;
		}
		else {
			JiraTicket.removeObject(JiraTicket.class, conn, ticket.getId());
		}
		conn.commit();
	}

	/**
	 * Creates tickets in Jira. This method can take some time so it is recommended to use queueTicket to have it managed by the queue instead.
	 *
	 * @param conn A database connection.
	 * @param ticket The ticket that the issue will be based upon.
	 * @return True if the ticket was created or recreated, else false.
	 **/
	public boolean createTicket(final Connection conn, final JiraTicket ticket) throws IOException {
		String issueLink = "";
		JSONObject parent = null;
		try {
			// Check if an old issue exists.
			if (!StringUtils.isEmpty(ticket.getJiraKey())) {
				try {
					final JSONObject subTask =
							new JSONObject(getAction(conn, JIRAVariables.Key.toString() + "=" + ticket.getJiraKey(), "")).optJSONArray(JIRAVariables.Issues.toString())
									.optJSONObject(0);
					if (!subTask.optString(JIRAVariables.Key.toString()).isEmpty() && !subTask.optJSONObject(JIRAVariables.Fields.toString())
							.optJSONObject(JIRAVariables.Status.toString())
							.optString(JIRAVariables.Name.toString())
							.equals(this.finishedStatus)) {
						LOG.info("Jira sub-task was found and will not be recreated.");
						return false;
					}
					else {
						LOG.info("Jira sub-task was not found or was closed and will be created.");
						issueLink = ticket.getJiraKey();
					}
				}
				catch (final RuntimeException e) {
					LOG.info("Jira sub-task was not found and will be created.");
				}
			}

			// Parent Issue
			boolean create = true;
			JiraIssue jiraIssue = JiraIssue.get(JiraIssue.class, conn, DbHelper.getSelect(JiraIssue.class, Access.ADMIN, "xuserxid=? AND ipaddress=?"), ticket.getUserId(),
					ticket.getIpAddress());
			try {
				parent = new JSONObject(getAction(conn, JIRAVariables.Key.toString() + "=" + jiraIssue.getKey(), "")).optJSONArray(JIRAVariables.Issues.toString())
						.optJSONObject(0);
				final String status =
						parent.optJSONObject(JIRAVariables.Fields.toString()).optJSONObject(JIRAVariables.Status.toString()).optString(JIRAVariables.Name.toString());
				if (!status.equals(finishedStatus)) {
					create = false;
					LOG.info("Jira issue was found and will not be recreated.");
				}
			}
			catch (final RuntimeException e) {
				LOG.info("Jira issue was not found or was closed and will be created.");
			}

			if (create) {
				try {
					// Create the issue
					final JSONObject fields = new JSONObject();
					// Add Project
					final JSONObject project = new JSONObject();
					project.put(JIRAVariables.Key.toString(), this.projectKey);
					fields.put(JIRAVariables.Project.toString(), project);
					// Add Summary
					fields.put(JIRAVariables.Summary.toString(), ticket.getIpAddress());
					// Add Description
					fields.put(JIRAVariables.Description.toString(),
							"Main issue for vulnerabilities on ipaddress: " + ticket.getIpAddress() + ". For more information log in to " + (Configuration.isHiabEnabled()
									? "HIAB"
									: "OUTSCAN") + " and see the report.");
					// Add Issue Type
					final JSONObject type = new JSONObject();
					type.put(JIRAVariables.Name.toString(), this.issueType);
					fields.put(JIRAVariables.IssueType.toString(), type);

					final JSONObject task = new JSONObject();
					task.put(JIRAVariables.Fields.toString(), fields);

					parent = postAction(conn, task, "issue");
					if (parent == null) {
						return false;
					}
					if (parent.has(JIRAVariables.Errors.toString()) || parent.has(JIRAVariables.ErrorMessages.toString())) {
						LOG.info("Error in createTicket (create issue): " + parent.toString());
					}

					if (jiraIssue == null) {
						jiraIssue = new JiraIssue();
					}
					jiraIssue.setIpAddress(ticket.getIpAddress());
					jiraIssue.setKey(parent.optString(JIRAVariables.Key.toString()));
					jiraIssue.setUserId(ticket.getUserId());
					jiraIssue.save(conn);
					conn.commit();
				}
				catch (final RuntimeException | SQLException ex) {
					LOG.error("Error creating JIRA ticket: " + ticket.getId(), ex);
					return false;
				}
			}

			// Vulnerability issue

			final FindingImpl finding = FindingImpl.getById(FindingImpl.class, conn, ticket.getFindingId());
			final String description = prepareDescription(conn, ticket, finding);
			final String priority = findPriority(conn, finding);

			// Create the issue
			final JSONObject fields = new JSONObject();
			// Add Project
			final JSONObject project = new JSONObject();
			project.put(JIRAVariables.Key.toString(), this.projectKey);
			fields.put(JIRAVariables.Project.toString(), project);
			// Add Summary
			fields.put(JIRAVariables.Summary.toString(), ticket.getName());
			// Add Description
			fields.put(JIRAVariables.Description.toString(), escapeEmoticons(description));
			// Add Issue Type
			final JSONObject type = new JSONObject();
			type.put(JIRAVariables.Name.toString(), "Sub-task");
			fields.put(JIRAVariables.IssueType.toString(), type);
			// Add Priority
			if (!StringUtils.isEmpty(priority)) {
				final JSONObject prio = new JSONObject();
				prio.put(JIRAVariables.Id.toString(), priority);
				fields.put(JIRAVariables.Priority.toString(), prio);
			}
			// Add Parent
			final JSONObject parentKey = new JSONObject();
			parentKey.put(JIRAVariables.Key.toString(), parent.optString(JIRAVariables.Key.toString()));
			fields.put(JIRAVariables.Parent.toString(), parentKey);

			final JSONObject task = new JSONObject();
			task.put(JIRAVariables.Fields.toString(), fields);

			final JSONObject subTask = postAction(conn, task, "issue");
			if (subTask == null) {
				return false;
			}
			if (subTask.has(JIRAVariables.Errors.toString()) || subTask.has(JIRAVariables.ErrorMessages.toString())) {
				LOG.info("Error in createTicket (create sub-task): " + subTask.toString());
			}

			// Save JIRA key to ticket.
			ticket.setJiraKey(subTask.optString(JIRAVariables.Key.toString()));
			ticket.setCreateTicket(false);
			ticket.save(conn);
			conn.commit();

			// Link issues
			if (!issueLink.isEmpty() && this.linkOldIssues) {
				final JSONObject link = new JSONObject();
				final JSONObject linkType = new JSONObject();
				linkType.put(JIRAVariables.Name.toString(), "Relates");
				link.put(JIRAVariables.Type.toString(), linkType);
				final JSONObject inwardIssue = new JSONObject();
				inwardIssue.put(JIRAVariables.Key.toString(), ticket.getJiraKey());
				link.put(JIRAVariables.InwardIssue.toString(), inwardIssue);
				final JSONObject outwardIssue = new JSONObject();
				outwardIssue.put(JIRAVariables.Key.toString(), issueLink);
				link.put(JIRAVariables.OutwardIssue.toString(), outwardIssue);
				final JSONObject linkResponse = postAction(conn, link, "issueLink");
				if (linkResponse != null && (linkResponse.has(JIRAVariables.Errors.toString()) || linkResponse.has(JIRAVariables.ErrorMessages.toString()))) {
					LOG.info("Error in createTicket (link issue): " + subTask.toString());
				}
			}
		}
		catch (final RuntimeException | SQLException e) {
			LOG.error("Error creating JIRA ticket: " + ticket.getId(), e);
			return false;
		}
		return true;
	}

	/**
	 * Prepares jira ticket description.
	 *
	 * @param conn A database connection.
	 * @param ticket The ticket that the issue will be based upon.
	 * @param finding The object that contains finding information.
	 * @return description string.
	 */
	public String prepareDescription(final Connection conn, final JiraTicket ticket, final FindingImpl finding) throws SQLException {
		final StringBuilder description = new StringBuilder();
		description.append("Ip Address: " + ticket.getIpAddress() + "\n\n");
		if (finding != null) {
			if (finding.getHostname() != null) {
				description.append("Hostname: " + finding.getHostname() + "\n\n");
			}
			BaseLoggedOnUser assignee = null;
			if (ticket.getSubUserId() > 0) {
				assignee = LoggedOnSubUser.getById(LoggedOnSubUser.class, conn, Access.ADMIN, ticket.getSubUserId());
			}
			else {
				assignee = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, ticket.getUserId());
			}
			final String language = assignee.getLanguage();
			final long parentId = assignee.getSalesOrganizationId();
			final String vulnInfo = new WorkflowBusiness().getVulInfo(conn, finding, language, parentId, false);
			description.append(vulnInfo.substring(0, vulnInfo.length() - 2) + (StringUtils.isEmpty(finding.getCve()) ? "" : "CVE: " + finding.getCve()) + "\n\n");
		}
		description.append("For more information, log in to " + (Configuration.isHiabEnabled() ? "HIAB" + (Hiab.getInstance().getIpAddress() != null ? " (IP address: "
				+ Hiab.getInstance().getIpAddress()
				+ ")" : "") : "OUTSCAN")
				+ " and see the report.");
		return description.toString();
	}

	/**
	 * @param conn A database connection.
	 * @param finding The object that contains finding information.
	 * @return priority
	 */
	private String findPriority(final Connection conn, final FindingImpl finding) throws UnsupportedEncodingException {
		final JSONArray priorities = new JSONArray(getAction(conn, "", "priority"));
		String priority = "";
		for (int i = 0; i < priorities.length(); i++) {
			if ((finding.getRiskLevel() == 4 && priorities.getJSONObject(i).optString(JIRAVariables.Name.toString()).equals("High"))
					|| ((finding.getRiskLevel() == 0 || finding.getRiskLevel() == 1) && priorities.getJSONObject(i).optString(JIRAVariables.Name.toString()).equals("Low"))
					|| ((finding.getRiskLevel() == 2 || finding.getRiskLevel() == 3) && priorities.getJSONObject(i)
					.optString(JIRAVariables.Name.toString())
					.equals("Medium"))) {
				priority = priorities.getJSONObject(i).optString(JIRAVariables.Id.toString());
			}
		}
		return priority;
	}

	/**
	 * Escapes the emoticons in JIRA based on documentation in https://jira.atlassian.com/secure/WikiRendererHelpAction.jspa?section=all.
	 *
	 * @param input The String to change.
	 * @return input with \ in front of all emoticons to escape them.
	 */
	private String escapeEmoticons(final String input) {
		String result = input;
		final String[] emoticons = {
				":)",
				":(",
				":P",
				":D",
				";)",
				"(y)",
				"(n)",
				"(i)",
				"(/)",
				"(x)",
				"(!)",
				"(+)",
				"(-)",
				"(?)",
				"(on)",
				"(off)",
				"(*)",
				"(*r)",
				"(*g)",
				"(*b)",
				"(*y)",
				"(flag)",
				"(flagoff)"
		};

		for (final String emoticon : emoticons) {
			result = result.replace(emoticon, "\\" + emoticon);
		}

		return result;
	}

	// We need these because we need to set the method as post in the token, and that is protected
	private static class JiraOAuthGetTemporaryToken extends OAuthGetTemporaryToken {
		public JiraOAuthGetTemporaryToken(final String authorizationServerUrl) {
			super(authorizationServerUrl);
			this.usePost = true;
		}
	}

	private static class JiraOAuthGetAccessToken extends OAuthGetAccessToken {
		public JiraOAuthGetAccessToken(final String authorizationServerUrl) {
			super(authorizationServerUrl);
			this.usePost = true;
		}
	}

	/**
	 * @param httpClient The http client to use.
	 * @param privateKey The private key to use.
	 * @param baseURL The base url to the jira instance.
	 * @param consumerKey The OAuth consumer key configured on the jira for authentication.
	 * @return A temporary token from the jira.
	 */
	private static String getTemporaryOauthToken(final HttpClient httpClient, final PrivateKey privateKey, final String baseURL, final String consumerKey)
			throws InvalidKeySpecException, NoSuchAlgorithmException, Base64DecodingException, IOException {
		// get a temporary token from the server
		final OAuthGetTemporaryToken tempToken = new JiraOAuthGetTemporaryToken(baseURL + JIRA_OAUTH_REQUEST_TOKEN);
		tempToken.consumerKey = consumerKey;
		final OAuthRsaSigner signer = new OAuthRsaSigner();
		signer.privateKey = privateKey;
		tempToken.signer = signer;
		if (httpClient != null) {
			tempToken.transport = new ApacheHttpTransport(httpClient);
		}
		else {
			tempToken.transport = new ApacheHttpTransport();
		}
		final OAuthCredentialsResponse response = tempToken.execute();
		return response.token;
	}

	/**
	 * @param httpClient The http client to use.
	 * @param privateKey The private key to use.
	 * @param baseURL The base url to the jira instance.
	 * @param consumerKey The OAuth consumer key configured on the jira for authentication.
	 * @return A authentication URL to redirect the customer to for authenticating the jira.
	 */
	public static String getTemporaryOauthTokenURL(final HttpClient httpClient, final PrivateKey privateKey, final String baseURL, final String consumerKey)
			throws InvalidKeySpecException, NoSuchAlgorithmException, Base64DecodingException, IOException {
		// build and return the authorization url
		final String token = getTemporaryOauthToken(httpClient, privateKey, baseURL, consumerKey);
		final OAuthAuthorizeTemporaryTokenUrl authorizationURL = new OAuthAuthorizeTemporaryTokenUrl(baseURL + JIRA_OAUTH_AUTHORIZE);
		authorizationURL.temporaryToken = token;
		return authorizationURL.toString();
	}

	/**
	 * @param httpClient The http client to use.
	 * @param privateKey The private key to use.
	 * @param baseURL The base url to the jira instance.
	 * @param consumerKey The OAuth consumer key configured on the jira for authentication.
	 * @param token The token that the user has authenticated.
	 * @param secret The OAuth secret from when the customer authenticated the jira.
	 * @return A final token for the jira.
	 */
	private static OAuthGetAccessToken createToken(final HttpClient httpClient, final PrivateKey privateKey, final String baseURL, final String consumerKey,
												   final String token, final String secret)
			throws InvalidKeySpecException, NoSuchAlgorithmException, Base64DecodingException, IOException {
		final JiraOAuthGetAccessToken accessToken = new JiraOAuthGetAccessToken(baseURL + JIRA_OAUTH_ACCESS_TOKEN);
		accessToken.consumerKey = consumerKey;
		final OAuthRsaSigner signer = new OAuthRsaSigner();
		signer.privateKey = privateKey;
		accessToken.signer = signer;
		if (httpClient != null) {
			accessToken.transport = new ApacheHttpTransport(httpClient);
		}
		else {
			accessToken.transport = new ApacheHttpTransport();
		}
		accessToken.verifier = secret;
		accessToken.temporaryToken = token;
		return accessToken;
	}

	/**
	 * get the final token that can later be used for requests.
	 *
	 * @param httpClient The http client to use.
	 * @param privateKey The private key to use.
	 * @param baseURL The base url to the jira instance.
	 * @param consumerKey The OAuth consumer key configured on the jira for authentication.
	 * @param token The token that the user has authenticated.
	 * @return A final token for the jira.
	 */
	public static String getFinalOauthToken(final HttpClient httpClient, final PrivateKey privateKey, final String baseURL, final String consumerKey, final String token)
			throws InvalidKeySpecException, NoSuchAlgorithmException, Base64DecodingException, IOException {
		final OAuthGetAccessToken accessToken = createToken(httpClient, privateKey, baseURL, consumerKey, token, "secret"); // jira dont care about secret
		final OAuthCredentialsResponse response = accessToken.execute();
		return response.token;
	}

	private boolean isOAuth() {
		return this.authenticationType == JiraServer.AUTHENTICATION_OAUTH;
	}

	/**
	 * @param privateKey The private key to use.
	 * @return A Authenticated http request initializer for the jira instance.
	 */
	private HttpRequestInitializer getOAuthParams(final PrivateKey privateKey)
			throws InvalidKeySpecException, NoSuchAlgorithmException, Base64DecodingException, IOException {
		final OAuthGetAccessToken oauthAccessToken = createToken(null, privateKey, this.uri, this.oauthConsumerKey, this.oauthToken, "secret"); // jira dont care about secret
		return oauthAccessToken.createParameters();
	}

	private HttpRequestInitializer getBasicAuthParams() {
		return new BasicAuthentication(this.username, TwoWayEncoding.decode(this.password, Configuration.getConfigService()));
	}

}
