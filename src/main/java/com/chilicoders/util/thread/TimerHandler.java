package com.chilicoders.util.thread;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.chilicoders.bl.certificate.CertificateRenewalTask;
import com.chilicoders.boris.FetchPrefsetTask;
import com.chilicoders.boris.UpdateCRLList;
import com.chilicoders.boris.hiab.BackupTask;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.hiab.update.UpdateTask;
import com.chilicoders.integrations.appcheck.AppcheckPollingTask;
import com.chilicoders.migration.MigrationTask;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.AssetNotRecentlySeenEventsTask;
import com.chilicoders.util.CalculateTargetTrendCache;
import com.chilicoders.util.ComplianceRiskExceptionExpirationEventsTask;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DailyLoggingTask;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.DeprecatedDataUpdaterTask;
import com.chilicoders.util.FetchExploitsTask;
import com.chilicoders.util.FetchRulesTask;
import com.chilicoders.util.FindingAcceptedExpirationEventsTask;
import com.chilicoders.util.FindingExpiredAcceptanceEventsTask;
import com.chilicoders.util.HiabEventTask;
import com.chilicoders.util.LoadCompliancePoliciesTask;
import com.chilicoders.util.NotificationRetentionHandlingTask;
import com.chilicoders.util.NotifyUpdatedRulesTask;
import com.chilicoders.util.PciDssQaTask;
import com.chilicoders.util.RemovedAcceptedRisksTask;
import com.chilicoders.util.ScheduleScheduledEventsTask;
import com.chilicoders.util.SecureCodeWarriorTask;
import com.chilicoders.util.StatsTask;
import com.chilicoders.util.SyncAgentSchedulesTask;
import com.chilicoders.util.SyncAgentVersionTask;
import com.chilicoders.util.UpdateDynamicGroupsTask;
import com.chilicoders.util.UpdateIdpTask;
import com.chilicoders.util.UpdateJiraTask;
import com.chilicoders.util.UpdatePendingSalesTask;
import com.chilicoders.util.UpdateServiceNowTask;
import com.chilicoders.util.UpdateTimeZoneTask;
import com.chilicoders.util.UpdateTranslationsTask;
import com.chilicoders.util.UpdateVulnDbTask;

/**
 * Deals with scheduling and executing delayed tasks or tasks that should run at fixed intervals.
 */
public class TimerHandler {
	/**
	 * The different tasks that are available, used for scheduling and cancelling.
	 */
	public enum Task {
		BackupTask {
			Runnable createTimerTask() {
				return new BackupTask();
			}
		},
		UpdateTask {
			Runnable createTimerTask() {
				return new UpdateTask();
			}
		},
		FetchPrefsetTask {
			Runnable createTimerTask() {
				return new FetchPrefsetTask();
			}
		},
		/**
		 * Updates statistics for accepted risks when they expire.
		 */
		RemoveAcceptedRisk {
			Runnable createTimerTask() {
				return new RemovedAcceptedRisksTask();
			}
		},
		/**
		 * Send logging for accepted risks that are about to expire.
		 */
		DailyLogging {
			Runnable createTimerTask() {
				return new DailyLoggingTask();
			}
		},
		/**
		 * Used to update dynamic groups.
		 */
		UpdateDynamicGroups {
			Runnable createTimerTask() {
				return new UpdateDynamicGroupsTask();
			}
		},
		/**
		 * Used to update pending sales.
		 */
		UpdatePendingSales {
			Runnable createTimerTask() {
				return new UpdatePendingSalesTask();
			}
		},
		FetchExploits {
			Runnable createTimerTask() {
				return new FetchExploitsTask();
			}
		},
		UpdateJira {
			Runnable createTimerTask() {
				return new UpdateJiraTask();
			}
		},
		UpdateServiceNow {
			Runnable createTimerTask() {
				return new UpdateServiceNowTask();
			}
		},
		UpdateIdp {
			Runnable createTimerTask() {
				return new UpdateIdpTask();
			}
		},
		UpdateVulnDb {
			Runnable createTimerTask() {
				return new UpdateVulnDbTask();
			}
		},
		NotifyUpdatedRules {
			Runnable createTimerTask() {
				return new NotifyUpdatedRulesTask();
			}
		},
		UpdateTranslations {
			Runnable createTimerTask() {
				return new UpdateTranslationsTask();
			}
		},
		SyncAgentVersion {
			Runnable createTimerTask() {
				return new SyncAgentVersionTask(ServiceProvider.getAgentsService());
			}
		},
		LoadCompliancePolicies {
			Runnable createTimerTask() {
				return new LoadCompliancePoliciesTask();
			}
		},
		PciDssQa {
			Runnable createTimerTask() {
				return new PciDssQaTask();
			}
		},
		PagerDutyTask {
			Runnable createTimerTask() {
				return new PagerDutyTask();
			}
		},
		UpdateCRLListTask {
			Runnable createTimerTask() {
				return new UpdateCRLList();
			}
		},
		FetchRules {
			Runnable createTimerTask() {
				return new FetchRulesTask();
			}
		},
		DeleteDownloadCacheFiles {
			Runnable createTimerTask() {
				return new DeleteDownloadCacheFilesTask();
			}
		},
		TargetVulnerabilityTrendCalculator {
			Runnable createTimerTask() {
				return new CalculateTargetTrendCache();
			}
		},
		DeprecatedDataUpdaterTask {
			Runnable createTimerTask() {
				return new DeprecatedDataUpdaterTask();
			}
		},
		DbCheckTask {
			Runnable createTimerTask() {
				return new DbCheckTask();
			}
		},
		CertificateRenewalTask {
			Runnable createTimerTask() {
				return new CertificateRenewalTask();
			}
		},
		StatsTask {
			Runnable createTimerTask() {
				return new StatsTask();
			}
		},
		SyncAgentSchedulesTask {
			Runnable createTimerTask() {
				return new SyncAgentSchedulesTask();
			}
		},
		SecureCodeWarriorTask {
			Runnable createTimerTask() {
				return new SecureCodeWarriorTask();
			}
		},
		UpdateTimeZoneTask {
			Runnable createTimerTask() {
				return new UpdateTimeZoneTask();
			}
		},
		ScheduleScheduledEventsTask {
			Runnable createTimerTask() {
				return new ScheduleScheduledEventsTask();
			}
		},
		AssetNotRecentlySeenEventsTask {
			Runnable createTimerTask() {
				return new AssetNotRecentlySeenEventsTask();
			}
		},
		HiabEventTask {
			Runnable createTimerTask() {
				return new HiabEventTask();
			}
		},
		FindingAcceptedExpirationEventsTask {
			Runnable createTimerTask() {
				return new FindingAcceptedExpirationEventsTask();
			}
		},
		NotificationRetentionHandlingTask {
			Runnable createTimerTask() {
				return new NotificationRetentionHandlingTask();
			}
		},
		AppCheckPollingTask {
			Runnable createTimerTask() {
				return new AppcheckPollingTask();
			}
		},
		MigrationTask {
			Runnable createTimerTask() {
				return new MigrationTask();
			}
		},
		ComplianceRiskExceptionExpirationEventsTask {
			Runnable createTimerTask() {
				return new ComplianceRiskExceptionExpirationEventsTask();
			}
		},
		FindingExpiredAcceptanceEventsTask {
			@Override
			Runnable createTimerTask() {
				return new FindingExpiredAcceptanceEventsTask();
			}
		};

		/**
		 * Creates a timertask for the task.
		 *
		 * @return A timer task.
		 */
		abstract Runnable createTimerTask();
	}

	/**
	 * Our timer that is responsible for actually starting the tasks.
	 */
	private ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(10);

	/**
	 * A map that contains all the scheduled tasks.
	 */
	private Map<Task, ScheduledFuture<?>> tasks = new HashMap<>();

	/**
	 * <code>true</code> if the timerhandler has been destroyed, can not be used afterwards.
	 */
	private boolean destroyed = false;

	/**
	 * The one and only instance.
	 */
	private static TimerHandler instance = new TimerHandler();

	/**
	 * Private constructor.
	 */
	private TimerHandler() {
	}

	/**
	 * Returns the timerhandler instance.
	 *
	 * @return The one and only instance.
	 */
	private static TimerHandler getInstance() {
		return instance;
	}

	/**
	 * Schedules a task at a certain time, time is specified as yyyy-MM-dd HH:mm. If the date is not valid the task will be canceled. If the task type is already scheduled it will be cancelled.
	 *
	 * @param task The type of task to schedule.
	 * @param time The time to schedule the task, if <code>null</code> the task will be canceled.
	 */
	public static void schedule(final Task task, final String time) {
		final Date date = DateUtils.parseTimeDate(time);
		if (date != null) {
			TimerHandler.schedule(task, date);
		}
		else {
			cancel(task);
		}
	}

	/**
	 * Schedules a task at a certain time. If the task type is already scheduled it will be canceled.
	 *
	 * @param task The type of the task to schedule.
	 * @param time The time to schedule the task.
	 */
	public static synchronized void schedule(final Task task, final Date time) {
		if (!Configuration.getProperty(ConfigurationBooleanKey.test_mode) && !getInstance().destroyed) {
			cancel(task);
			final ScheduledFuture<?> scheduled = instance.executor.schedule(task.createTimerTask(), time.getTime() - new Date().getTime(), TimeUnit.MILLISECONDS);
			instance.tasks.put(task, scheduled);
		}
	}

	/**
	 * Schedule a task to be executed at a fixed rate. If the task type is already scheduled it will be canceled.
	 *
	 * @param task The type of task to be scheduled.
	 * @param delaySeconds The delay between execution in seconds, will also be the initial delay before the first execution.
	 */
	public static synchronized void scheduleAtFixedRate(final Task task, final int delaySeconds) {
		if (!getInstance().destroyed) {
			cancel(task);
			final ScheduledFuture<?> scheduled = instance.executor.scheduleAtFixedRate(task.createTimerTask(), delaySeconds, delaySeconds, TimeUnit.SECONDS);
			instance.tasks.put(task, scheduled);
		}
	}

	/**
	 * Cancels a schedule task.
	 *
	 * @param task The type of the task to cancel.
	 */
	private static synchronized void cancel(final Task task) {
		final ScheduledFuture<?> oldTask = getInstance().tasks.remove(task);
		if (oldTask != null) {
			oldTask.cancel(false);
		}
	}

	/**
	 * Destroys the timerhandler, after this has been called no tasks can be scheduled.
	 */
	public static synchronized void destroy() {
		instance.executor.shutdownNow();
		instance.tasks.clear();
		instance.destroyed = true;
	}
}
