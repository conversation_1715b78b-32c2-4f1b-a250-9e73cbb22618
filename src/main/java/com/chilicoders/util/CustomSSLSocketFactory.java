package com.chilicoders.util;

import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@SuppressFBWarnings(value = "SING_SINGLETON_HAS_NONPRIVATE_CONSTRUCTOR")
public class CustomSSLSocketFactory extends SSLSocketFactory {

	private static final Logger LOG = LogManager.getLogger(CustomSSLSocketFactory.class);
	private static volatile CustomSSLSocketFactory instance = null;
	private SSLSocketFactory sf = null;

	public CustomSSLSocketFactory() {
		init();
	}

	/**
	 * Setup SSLSocketFactory.
	 */
	private void init() {
		try {
			final SSLContext ctx = SSLContext.getInstance("SSL");
			ctx.init(null, SslUtils.getTrustAllTrustManager(), null);
			sf = ctx.getSocketFactory();
		}
		catch (final Exception e) {
			LOG.error("Couldn't initialize ssl socket factory: ", e);
		}
	}

	/**
	 * Get default SSLSocketFactory.
	 *
	 * @return SSLSocketFactory obejct
	 */
	public static synchronized SSLSocketFactory getDefault() {
		if (instance == null) {
			instance = new CustomSSLSocketFactory();
		}
		return instance;
	}

	@Override
	public String[] getDefaultCipherSuites() {
		return sf.getDefaultCipherSuites();
	}

	@Override
	public String[] getSupportedCipherSuites() {
		return sf.getSupportedCipherSuites();
	}

	@Override
	public Socket createSocket(final Socket socket, final String host, final int port, final boolean autoClose) throws IOException {
		return sf.createSocket(socket, host, port, autoClose);
	}

	@Override
	public Socket createSocket(final String host, final int port) throws IOException, UnknownHostException {
		return sf.createSocket(host, port);
	}

	@Override
	public Socket createSocket(final InetAddress host, final int port) throws IOException {
		return sf.createSocket(host, port);
	}

	@Override
	public Socket createSocket(final String host, final int port, final InetAddress localHost, final int localPort) throws IOException, UnknownHostException {
		return sf.createSocket(host, port, localHost, localPort);
	}

	@Override
	public Socket createSocket(final InetAddress address, final int port, final InetAddress localAddress, final int localPort) throws IOException {
		return sf.createSocket(address, port, localAddress, localPort);
	}
}
