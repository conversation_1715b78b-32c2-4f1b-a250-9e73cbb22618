package com.chilicoders.util;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;

import org.apache.http.HttpStatus;
import org.apache.logging.log4j.Level;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.util.log4j.Log4jUtils;
import com.chilicoders.util.thread.TimerHandler;

public class NetworkConnectivityTest {
	/**
	 * Connects to Outscan via https and tries a KEEPALIVE request. Any errors are returned on System.out.
	 * If exit code is zero the request was succesfully completed.
	 *
	 * @param args None used
	 */
	public static void main(final String[] args) {
		final PrintStream out = System.out;
		final String logFile = "/tmp/connectivity.log";

		try (final PrintStream stream = new PrintStream(new FileOutputStream(logFile, true), false, UTF_8.name())) {
			System.setErr(stream);
			System.setOut(stream);
			System.out.println(System.lineSeparator() + "init NetworkConnectivityTest");

			Log4jUtils.configureFileAppender(Log4jUtils.Parameters.builder().filePath(logFile).level(Level.DEBUG).rootLevel(Level.DEBUG).build());

			Configuration.initializeConfigurationClass("/etc", "server.properties");

			XMLAPI.initializeSslContexts();

			final HashMap<String, String> params = new HashMap<>();
			params.put("ACTION", "KEEPALIVE");

			final HttpsThread thread = new HttpsThread(Configuration.getProperty(ConfigurationKey.outscan_url), params, true, null);
			thread.start();
			thread.getResponse(null);
			HttpsThread.destroyTimer();

			System.setOut(out);
			if (thread.hasErrorOccured()) {
				System.out.println(thread.getErrorMessage());
				System.exit(1);
			}
			else if (thread.getStatusCode() > HttpStatus.SC_OK) {
				System.out.println(thread.getStatusCode());
				System.exit(1);
			}
		}
		catch (final RuntimeException | FileNotFoundException | UnsupportedEncodingException e) {
			e.printStackTrace();

			System.setOut(out);
			System.out.println("Could not initiate connection");
			System.exit(1);
		}

		TimerHandler.destroy();
	}
}
