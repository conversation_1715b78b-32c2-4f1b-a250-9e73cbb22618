package com.chilicoders.util;

import static com.chilicoders.db.Access.ADMIN;
import static com.chilicoders.service.ServiceProvider.getConfigService;
import static com.chilicoders.service.ServiceProvider.getDataStoreService;
import static com.chilicoders.service.ServiceProvider.getEventApiV2;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TimerTask;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.api.EventApiInterfaceV2;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.api.DataStoreEntry.DataStoreEntryKeys;
import com.chilicoders.core.configuration.api.DataStoreService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.rest.events.FindingEvent;
import com.chilicoders.model.FindingStatus;
import com.chilicoders.rest.models.Finding;
import com.chilicoders.util.thread.TimerHandler;

/**
 * Look for all findings whose acceptance periods have recently expired.
 * Not to be confused with {@link FindingAcceptedExpirationEventsTask} which looks for findings
 * whose acceptance periods are about to expire.
 */
public class FindingExpiredAcceptanceEventsTask extends TimerTask {

	private static final Logger LOG = LogManager.getLogger(FindingExpiredAcceptanceEventsTask.class);

	private static final long BATCH_SIZE = 1000;

	@Override
	public void run() {
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			handleFindingExpiredAcceptanceEvents(conn);
		}
		catch (final SQLException e) {
			LOG.error("Error running " + getClass().getName(), e);
		}
		scheduleTask(true);
	}

	/**
	 * Schedules this task.
	 *
	 * @param reschedule True if this task has already been executed.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (reschedule) {
			final Duration interval = Duration.parse(getConfigService().getProperty(ConfigKeys.ConfigurationKey.eventtask_expired_finding_acceptance_interval));
			TimerHandler.schedule(TimerHandler.Task.FindingExpiredAcceptanceEventsTask, Date.from(Instant.now().plus(interval)));
		}
		else {
			TimerHandler.schedule(TimerHandler.Task.FindingExpiredAcceptanceEventsTask, new Date());
		}
	}

	/**
	 * Create and send events for recently expired acceptance findings
	 *
	 * @param conn The DB connection
	 * @return The list of {@link Trigger#FINDING_STATUS_TRANSITIONED} type events sent
	 */
	public List<Event> handleFindingExpiredAcceptanceEvents(final Connection conn) throws SQLException {
		LOG.info("Send finding expired acceptance events");
		final DataStoreService dataStoreService = getDataStoreService(conn);
		final DataStoreEntry lastCheck = dataStoreService.getEntry(DataStoreEntryKeys.LAST_EXPIRED_FINDING_ACCEPTANCE_CHECK.name());
		final Instant now = Instant.now();
		final Instant fromDate = lastCheck != null
				? Instant.parse(lastCheck.getValue()) : now.minus(Duration.parse(getConfigService().getProperty(ConfigKeys.ConfigurationKey.eventtask_expired_finding_acceptance_interval)));

		final Set<Integer> configuredCustomerIds = getCustomerIdsWithExistingEvents(conn);

		List<Finding> findings = getAcceptanceRecentlyExpiredFindings(conn, fromDate, now, 0);
		if (findings.isEmpty()) {
			return new ArrayList<>();
		}
		final EventApiInterfaceV2 eventApiV2 = getEventApiV2(conn);
		final List<Event> events = new ArrayList<>();
		while (!findings.isEmpty()) {
			for (final Finding finding : findings) {
				finding.save(conn, ADMIN, finding.transitionStatus(FindingStatus.PRESENT, null, null, null).toArray(new String[0]));
				final FindingEvent findingEvent = new FindingEvent(finding.getId(), finding.getCustomerId(), null, null, null, null, null, finding.getAssetId());
				events.add(findingEvent.modifiedByTransitioning());
				final Event event = findingEvent.statusTransitioned(FindingStatus.ACCEPTED, FindingStatus.PRESENT);
				if (!configuredCustomerIds.contains(finding.getCustomerId())) {
					event.setHandleMode(Event.HandleMode.WATCHER_ONLY);
				}
				events.add(event);
			}
			conn.commit();
			if (findings.size() == BATCH_SIZE) {
				findings = getAcceptanceRecentlyExpiredFindings(conn, fromDate, now, 0);
			}
			else {
				findings = Collections.emptyList();
			}
		}
		dataStoreService.setEntry(DataStoreEntryKeys.LAST_EXPIRED_FINDING_ACCEPTANCE_CHECK.name(), now.toString());
		conn.commit();

		for (final Event event : events) {
			eventApiV2.handleEvent(event, null);
		}

		return events;
	}

	/**
	 * Get all customer IDs with configured event subscriptions of type {@link Trigger#FINDING_STATUS_TRANSITIONED}
	 * where a transition from ACCEPTED -> PRESENT is included
	 *
	 * @param conn The DB connection
	 * @return Set of customer IDs
	 */
	public Set<Integer> getCustomerIdsWithExistingEvents(final Connection conn) throws SQLException {
		final String sql = "SELECT ARRAY_AGG(customerid) FROM eventsubscriptions WHERE deleted IS NULL AND trigger = ?::eventtrigger"
				+ " AND (settings ->> 'from' IS NULL OR (settings ->> 'from') = ?) AND (settings ->> 'to' IS NULL OR (settings ->> 'to') = ?)";

		final Integer[] customerIds = DbObject.getIntegerArray(conn, sql, Trigger.FINDING_STATUS_TRANSITIONED.name(), FindingStatus.ACCEPTED.name(), FindingStatus.PRESENT.name());
		return Arrays.stream(customerIds).collect(Collectors.toSet());
	}

	/**
	 * Get expired findings
	 *
	 * @param conn The DB connection
	 * @param from the from timestamp
	 * @param to the to timestamp
	 * @param offset the offset
	 * @return List of findings
	 */
	private List<Finding> getAcceptanceRecentlyExpiredFindings(final Connection conn, final Instant from, final Instant to, final long offset) throws SQLException {
		final String where = "deleted IS NULL AND status = ?::findingstatus AND accepteduntil >= ? AND accepteduntil <= ?";
		final List<Finding> findings =
				DbObject.fetchObjects(Finding.class, conn, -1, offset, BATCH_SIZE, "id", DbHelper.getSelect(Finding.class, Access.ADMIN, where), FindingStatus.PRESENT.name(), from, to);
		return findings;
	}
}
