package com.chilicoders.util;

import java.nio.charset.Charset;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.crypto.BufferedBlockCipher;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.BlowfishEngine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;

/**
 * Class to encode/decode twoway, used for storing stuff in database that should not be in clear text.
 */
public class TwoWayEncoding {
	/**
	 * Logger instance.
	 */
	private static final Logger LOG = LogManager.getLogger(TwoWayEncoding.class);

	private static final Charset CHARSET = Charset.forName("UTF-8");

	private TwoWayEncoding() {
	}

	/**
	 * Creates a new Cipher object.
	 *
	 * @param mode <code>true</code> for encryption, <code>false</code> for decryption.
	 * @param configService Configuration service.
	 * @return New Cipher object.
	 */
	private static BufferedBlockCipher getCipher(final boolean mode, final ConfigurationService configService) {
		final byte[] key = configService.getProperty(ConfigurationKey.twowayencryption_password).getBytes(CHARSET);

		final CBCBlockCipher cipher = new CBCBlockCipher(new BlowfishEngine());
		final PaddedBufferedBlockCipher paddedCipher = new PaddedBufferedBlockCipher(cipher, new PKCS7Padding());
		paddedCipher.init(mode, new KeyParameter(key));

		return paddedCipher;
	}

	/**
	 * Encodes a piece of string, first Blowfish and then base 64 on top of that.
	 *
	 * @param data Data to encode.
	 * @param configService Configuration service.
	 * @return Encoded data.
	 */
	public static String encode(final String data, final ConfigurationService configService) {
		if (data == null) {
			return null;
		}
		try {
			final BufferedBlockCipher cipher = getCipher(true, configService);
			final byte[] in = data.getBytes(Charset.forName("UTF-8"));
			final byte[] out = new byte[cipher.getOutputSize(in.length)];
			final int len = cipher.processBytes(in, 0, in.length, out, 0);
			cipher.doFinal(out, len);
			return new String(Base64.encodeBase64(out), Charset.forName("UTF-8"));
		}
		catch (final Exception e) {
			LOG.error("Error encoding", e);
		}
		return null;
	}

	/**
	 * Decodes a previously encoded string.
	 *
	 * @param data Data to decode.
	 * @param configService Configuration service.
	 * @return Decoded data.
	 */
	public static String decode(final String data, final ConfigurationService configService) {
		if (data == null) {
			return null;
		}
		try {
			final BufferedBlockCipher cipher = getCipher(false, configService);
			final byte[] in = Base64.decodeBase64(data.getBytes(Charset.forName("UTF-8")));
			final byte[] out = new byte[cipher.getOutputSize(in.length)];
			int len = cipher.processBytes(in, 0, in.length, out, 0);
			len += cipher.doFinal(out, len);
			return new String(out, 0, len, CHARSET);
		}
		catch (final Exception e) {
			LOG.error("Error decoding", e);
		}
		return null;
	}

	/**
	 * Decodes a previously encoded string. If the string does not decode properly, the input is returned. This can be used when it is not known if the value is encoded or not.
	 *
	 * @param data Data to decode.
	 * @param configService Configuration service.
	 * @return Decoded data.
	 */
	public static String decodeIfPossible(final String data, final ConfigurationService configService) {
		if (data == null) {
			return null;
		}
		try {
			final BufferedBlockCipher cipher = getCipher(false, configService);
			final byte[] in = Base64.decodeBase64(data.getBytes(CHARSET));
			final byte[] out = new byte[cipher.getOutputSize(in.length)];
			int len = cipher.processBytes(in, 0, in.length, out, 0);
			len += cipher.doFinal(out, len);
			return new String(out, 0, len, CHARSET);
		}
		catch (final RuntimeException | InvalidCipherTextException e) {
			return data;
		}
	}
}
