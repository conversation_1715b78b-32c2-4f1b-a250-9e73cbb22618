package com.chilicoders.util;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;

import javax.xml.bind.JAXBException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.api.EventApiInterfaceV2;
import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.event.model.Event;
import com.chilicoders.event.model.Trigger;
import com.chilicoders.event.rest.events.AssetEvent;
import com.chilicoders.rest.models.EventSubscription;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.thread.TimerHandler;

public class AssetNotRecentlySeenEventsTask extends TimerTask {
	/**
	 * Our logger instance.
	 */
	private static final Logger LOG = LogManager.getLogger(AssetNotRecentlySeenEventsTask.class);

	/**
	 * @see java.util.TimerTask#run()
	 */
	@Override
	public void run() {
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			LOG.info("Send asset not recently seen events");
			handleAssetNotRecentlySeenEvents(conn);
		}
		catch (final SQLException e) {
			LOG.error("Error running " + getClass().getName(), e);
		}
		scheduleTask(true);
	}

	/**
	 * Schedules this task.
	 *
	 * @param reschedule <code>true</code> if this task has just been executed, <code>false</code> otherwise.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (!reschedule) {
			TimerHandler.schedule(TimerHandler.Task.AssetNotRecentlySeenEventsTask, new Date());
		}
		else {
			final Calendar cal = Calendar.getInstance();
			final Duration interval = Duration.parse(ServiceProvider.getConfigService().getProperty(ConfigurationKey.eventtask_asset_not_recently_seen_interval));
			cal.add(Calendar.MINUTE, (int) interval.toMinutes());
			TimerHandler.schedule(TimerHandler.Task.AssetNotRecentlySeenEventsTask, cal.getTime());
		}
	}

	/**
	 * Get asset events for testing purposes.
	 *
	 * @param conn Connection to DB.
	 * @return event list.
	 */
	public List<Event> getAssetNotRecentlySeenEvents(final Connection conn) throws SQLException {
		if (!ServiceProvider.getConfigService().getProperty(ConfigurationBooleanKey.test_mode)) {
			return null;
		}
		return handleAssetNotRecentlySeenEvents(conn);
	}

	/**
	 * Handle asset not recently seen events.
	 *
	 * @param conn Connection to DB.
	 * @return Event list.
	 */
	private List<Event> handleAssetNotRecentlySeenEvents(final Connection conn) throws SQLException {
		final List<EventSubscription> subscriptions = getAssetNotRecentlySeenEventSubscriptions(conn);
		final Instant now = Instant.now();
		final List<Event> eventList = new ArrayList<>();
		final List<Integer> subscriptionIds = new ArrayList<>();

		for (final EventSubscription subscription : subscriptions) {
			try {
				final Integer timeSince = subscription.getSettings() != null ? subscription.getSettings().getTimeSince() : null;
				if (timeSince == null) {
					LOG.info("Event subscription with trigger set to ASSET_NOT_RECENTLY_SEEN has not time since settings defined. Subscription id: " + subscription.getId() + ".");
					continue;
				}
				final Integer[] assetNotRecentlySeenIds = getAssetNotRecentlySeenIds(conn, subscription.getCustomerId(),
						subscription.getLastTriggered() != null ? subscription.getLastTriggered().minus(timeSince, ChronoUnit.DAYS) : now.minus(365, ChronoUnit.DAYS),
						now.minus(timeSince, ChronoUnit.DAYS));
				for (final Integer assetId : assetNotRecentlySeenIds) {
					eventList.add(new AssetEvent(assetId, subscription.getCustomerId(), null, subscription.getId(), null, null, null).notRecentlySeen(timeSince));
				}
				if (assetNotRecentlySeenIds.length > 0) {
					subscriptionIds.add(subscription.getId());
				}
			}
			catch (final JAXBException | SQLException e) {
				LOG.error("Exception occured when getting event subscription.", e);
			}
		}

		if (!subscriptionIds.isEmpty()) {
			final List<Object> params = new ArrayList<>();
			params.add(now);
			params.add(subscriptionIds.toArray(new Integer[0]));
			DbObject.executeUpdate(conn, "UPDATE eventsubscriptions SET lasttriggered = ? WHERE id = ANY(?)", params);
			conn.commit();
			final EventApiInterfaceV2 eventApi = ServiceProvider.getEventApiV2(conn);
			for (final Event event : eventList) {
				eventApi.handleEvent(event, null);
			}
		}

		return eventList;
	}

	/**
	 * Get event subscriptions with trigger set to ASSET_NOT_RECENTLY_SEEN.
	 * @param conn Connection to the DB.
	 * @return event subscription list.
	 */
	private List<EventSubscription> getAssetNotRecentlySeenEventSubscriptions(final Connection conn) throws SQLException {
		final String where = "deleted IS NULL AND trigger = ?";
		final List<Object> params = new ArrayList<>();
		params.add(Trigger.ASSET_NOT_RECENTLY_SEEN);

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		return EventSubscription.getObjects(EventSubscription.class, conn, null, config, null, null, null, null, null, false, true,
				where, params).getRight();
	}

	/**
	 * Returns id list of assets not recently seen.
	 * @param conn Connection to DB.
	 * @param customerId Customer id.
	 * @param fromDate Date from of last seen time frame.
	 * @param toDate Date to of last seen time frame.
	 * @return asset id list.
	 */
	private Integer[] getAssetNotRecentlySeenIds(final Connection conn, final Integer customerId, final Instant fromDate, final Instant toDate) throws SQLException {
		final List<Object> params = new ArrayList<>();
		params.add(customerId);
		params.add(fromDate);
		params.add(toDate);
		return DbObject.getIntegerArray(conn, "SELECT ARRAY_AGG(a.id) FROM (SELECT id, (SELECT MAX(lastseen) FROM assetidentifiers WHERE id = ANY(assetidentifierids)) AS lastseen FROM assetsview v WHERE customerid = ? AND deleted IS NULL) a WHERE "
				+ "a.lastseen > ? AND a.lastseen <= ? ", params);
	}
}
