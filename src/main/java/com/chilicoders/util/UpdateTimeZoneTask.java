package com.chilicoders.util;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.DataBusiness;
import com.chilicoders.util.thread.TimerHandler;

public class UpdateTimeZoneTask extends TimerTask {
	private static final Logger LOG = LogManager.getLogger(UpdateTimeZoneTask.class);

	@Override
	public void run() {
		try {
			DataBusiness.reloadTimeZones();
		}
		catch (final SQLException e) {
			LOG.error("Error updating timezones");
		}
		scheduleTask();
	}

	/**
	 * Schedule task every hour.
	 */
	public static void scheduleTask() {
		final Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MINUTE, 60);
		TimerHandler.schedule(TimerHandler.Task.UpdateTimeZoneTask, cal.getTime());
	}
}
