package com.chilicoders.util;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;

import org.apache.http.client.ClientProtocolException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.ServiceNowBusiness;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.ServiceNowServer;
import com.chilicoders.db.objects.SnTicket;
import com.chilicoders.servicenow.ServiceNow;
import com.chilicoders.util.thread.TimerHandler;

public class UpdateServiceNowTask extends TimerTask {
	private static final Logger LOG = LogManager.getLogger(UpdateServiceNowTask.class);

	/**
	 * Loops all tickets and creates the ticket marked with createTicket.
	 *
	 * @param conn Database connection
	 */
	private static void updateServiceNow(final Connection conn) throws SQLException, ClientProtocolException, IOException {
		final ServiceNowBusiness snb = new ServiceNowBusiness();
		final List<ServiceNowServer> sns = ServiceNowServer.fetchObjects(ServiceNowServer.class, conn, Access.ADMIN, "xid>=? AND processing=?", 0, false);
		for (final ServiceNowServer sn : sns) {
			if (sn.isSnEnabled()) {
				final List<SnTicket> tickets = SnTicket.fetchObjects(SnTicket.class, conn, Access.ADMIN, "xuserxid = ? AND createticket = ?", sn.getId(), true);
				if (tickets.size() > 0) {
					ServiceNow.destroy(sn.getId());
					if (tickets.size() < 300 || Configuration.isHiabEnabled()) {
						LOG.info("Creating ServiceNow tickets for user " + sn.getId());
						for (final SnTicket ticket : tickets) {
							if (!snb.createServiceNowTicket(conn, ticket, sn.isSolutionAsProblem())) {
								ticket.setCreateTicket(false);
								ticket.save(conn);
							}
						}
					}
					else if (DbObject.executeCountQuery(conn, "SELECT COUNT(*) FROM servicenowservers WHERE processing=?", true) < Configuration.getProperty(
							ConfigurationIntKey.max_sn_threads, 3)) {
						sn.setProcessing(true);
						sn.save(conn);
						conn.commit();
						LOG.info("Creating ServiceNow tickets by thread for user " + sn.getId());
						new Thread() {
							public void run() {
								Connection conn = null;
								try {
									conn = DbAccess.getInstance().getConnection();
									for (final SnTicket ticket : tickets) {
										if (!snb.createServiceNowTicket(conn, ticket, sn.isSolutionAsProblem())) {
											ticket.setCreateTicket(false);
											ticket.save(conn);
										}
									}
								}
								catch (final Exception e) {
									LOG.error("Error creating ServiceNow tickets", e);
								}
								finally {
									sn.setProcessing(false);
									if (conn != null) {
										try {
											sn.save(conn);
											conn.commit();
										}
										catch (final SQLException e) {
											LOG.error(e);
										}
									}
									DbAccess.getInstance().freeConnection(conn);
								}
							}
						}.start();
					}
					else {
						LOG.info("Service Now tickets for user "
								+ sn.getId()
								+ " were delayed. No available threads. Max nr of threads for Service Now ticket creation: "
								+ Configuration.getProperty(ConfigurationIntKey.max_sn_threads, 3));
					}
				}
			}
		}
	}

	@Override
	public void run() {
		LOG.info("Create new ServiceNow tickets");
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			updateServiceNow(conn);
			conn.commit();
		}
		catch (final Exception e) {
			LOG.error("Error creating ServiceNow tickets", e);
		}
		scheduleTask(true);
	}

	/**
	 * Schedules this task.
	 *
	 * @param reschedule <code>true</code> if this task has just been executed, <code>false</code> otherwise.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (reschedule) {
			final Calendar cal = Calendar.getInstance();
			cal.add(Calendar.MINUTE, 10);
			TimerHandler.schedule(TimerHandler.Task.UpdateServiceNow, cal.getTime());
		}
		else {
			TimerHandler.schedule(TimerHandler.Task.UpdateServiceNow, new Date());
		}
	}
}
