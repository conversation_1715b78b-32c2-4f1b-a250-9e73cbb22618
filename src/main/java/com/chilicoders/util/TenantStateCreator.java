package com.chilicoders.util;

import java.net.URI;
import java.sql.Connection;
import java.util.List;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.agents.api.AgentsService;
import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.storage.api.StorageService;
import com.chilicoders.core.storage.api.model.Domain;
import com.chilicoders.core.storage.api.model.TenantResourceIdentifier;
import com.chilicoders.db.Access;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.log4j.Log4jUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteBucketRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadBucketRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.NoSuchBucketException;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.S3Object;

public class TenantStateCreator {
	protected static final Logger LOG = LogManager.getLogger(TenantStateCreator.class);

	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final ConfigurationService configService;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final StorageService storageService;
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private final AgentsService agentsService;
	private S3Client s3;

	/**
	 * Constructor for creating a {@link TenantStateCreator}.
	 *
	 * @param configService Configuration service for reading API endpoints and what-not
	 * @param storageService Storage service for interfacing with the object storage
	 * @param agentsService Agents service for interfacing with the agents API
	 */
	public TenantStateCreator(final ConfigurationService configService, final StorageService storageService, final AgentsService agentsService) {
		this.configService = configService;
		this.storageService = storageService;
		this.agentsService = agentsService;
	}

	/**
	 * Attempts to create tenancy state for the tenant.
	 * Will quit at first failure. Not transactional, in other words if the first state creation succeeds but the second doesn't it will quit at that point but the first state will not be attempted to be rolled back.
	 *
	 * @param tenantUuid UUID of the tenant
	 * @return <code>true</code> if all states were created successfully, <code>false</code> otherwise.
	 */
	public boolean createState(final String tenantUuid) {
		try {
			createTenantBucket(TenantResourceIdentifier.builder().tenantUuid(tenantUuid).build());
		}
		catch (final Exception ex) {
			LOG.error("Failed creating bucket for customer " + tenantUuid, ex);
			return false;
		}

		try {
			createAgentTenant(tenantUuid);
		}
		catch (final Exception ex) {
			LOG.error("Failed creating agent tenant for customer " + tenantUuid, ex);
			return false;
		}

		return true;
	}

	/**
	 * Get a configured AmazonS3 instance.
	 * Will cache and return the same object for future calls.
	 *
	 * @return the AmazonS3 instance.
	 */
	private S3Client getS3() {
		if (this.s3 == null) {
			if (StringUtils.isEmpty(configService.getProperty(ConfigurationKey.s3_endpoint))) {
				throw new RuntimeException("Object storage endpoint not configured");
			}
			this.s3 = S3Client.builder().credentialsProvider((StaticCredentialsProvider.create(
							AwsBasicCredentials.create(
									configService.getProperty(ConfigurationKey.s3_access_key),
									configService.getProperty(ConfigurationKey.s3_secret_key)))))
					.endpointOverride(URI.create(configService.getProperty(ConfigurationKey.s3_endpoint)))
					.region(Region.of(configService.getProperty(ConfigurationKey.s3_region)))
					.forcePathStyle(true)
					.build();
		}
		return this.s3;
	}

	/**
	 * Attempts to migrate old buckets into the tenant bucket.
	 * Will continue with next bucket after failure. Not transactional, in other words if a migration fails there will not be any attempt to roll back.
	 *
	 * @param customer the customer to migrate.
	 * @return the number of objects that were migrated successfully.
	 */
	public BucketMigrationResult migrateBuckets(final Customer customer) {
		final String uuidBucket = customer.getUuid();
		final String opiBucket = "opi-" + customer.getUserId();
		int migratedObjects = 0;
		int failedObjects = 0;
		final S3Client s3 = getS3();

		try {
			getS3().headBucket(HeadBucketRequest.builder().bucket(opiBucket).build());
			final BucketMigrationResult result = migrateObjectsWithPrefix(s3, opiBucket, "reporting/",
					TenantResourceIdentifier.builder().tenantUuid(customer.getUuid()).domain(Domain.REPORTING).build());
			if (result.getFailedObjects() == 0) {
				s3.deleteBucket(DeleteBucketRequest.builder().bucket(opiBucket).build());
			}
			failedObjects += result.getFailedObjects();
			migratedObjects += result.getMigratedObjects();
		}
		catch (final NoSuchBucketException ex) {
			LOG.error("Failed migrating opi bucket " + opiBucket, ex);
		}
		catch (final S3Exception ex) {
			LOG.error("Error: " + ex.awsErrorDetails().errorMessage(), ex);
		}

		try {
			getS3().headBucket(HeadBucketRequest.builder().bucket(uuidBucket).build());
			final BucketMigrationResult result = migrateObjectsWithPrefix(s3, uuidBucket, "reporting/",
					TenantResourceIdentifier.builder().tenantUuid(customer.getUuid()).domain(Domain.REPORTING).build());
			if (result.getFailedObjects() == 0) {
				s3.deleteBucket(DeleteBucketRequest.builder().bucket(uuidBucket).build());
			}
			failedObjects += result.getFailedObjects();
			migratedObjects += result.getMigratedObjects();
		}
		catch (final NoSuchBucketException ex) {
			LOG.error("Failed migrating uuid bucket " + uuidBucket, ex);
		}
		catch (final S3Exception ex) {
			LOG.error("Error: " + ex.awsErrorDetails().errorMessage(), ex);
		}

		return new BucketMigrationResult(migratedObjects, failedObjects);
	}

	/**
	 * Moves all objects in the sourceBucket with the sourcePrefix to the destination resource.
	 *
	 * @param s3 The configured s3 instance to use.
	 * @param sourceBucket the bucket to migrate from.
	 * @param sourcePrefix the prefix to migrate from.
	 * @param resource the resource (bucket and domain) to migrate to.
	 * @return The number of objects that were successfully migrated.
	 */
	private static BucketMigrationResult migrateObjectsWithPrefix(final S3Client s3, final String sourceBucket, final String sourcePrefix,
																  final TenantResourceIdentifier resource) {
		int migratedObjects = 0;
		int failedObjects = 0;
		ListObjectsV2Request listReq = ListObjectsV2Request.builder().bucket(sourceBucket).prefix(sourcePrefix).build();

		ListObjectsV2Response objects;
		do { // iterates over chunked lists
			objects = s3.listObjectsV2(listReq);
			for (final S3Object sourceObject : objects.contents()) {
				final String destinationKey = sourceObject.key().replaceFirst(sourcePrefix, resource.getDomain().getPath());
				try {
					s3.copyObject(CopyObjectRequest.builder()
							.sourceBucket(sourceBucket).sourceKey(sourceObject.key())
							.destinationBucket(resource.getBucket()).destinationKey(destinationKey)
							.build());

					s3.deleteObject(DeleteObjectRequest.builder().bucket(sourceBucket).key(sourceObject.key()).build());

					migratedObjects++;
				}
				catch (final Exception ex) {
					failedObjects++;
					LOG.warn("Failed migrating object " + sourceBucket + "/" + sourceObject.key(), ex);
				}
			}
			listReq = ListObjectsV2Request.builder().continuationToken(objects.nextContinuationToken()).build();
		}
		while (objects.isTruncated());
		return new BucketMigrationResult(migratedObjects, failedObjects);
	}

	@Getter
	@AllArgsConstructor
	private static class BucketMigrationResult {
		private final int migratedObjects;
		private final int failedObjects;
	}

	/**
	 * Creates a bucket for the tenant in the object storage.
	 *
	 * @param resourceId {@link TenantResourceIdentifier} identifying the bucket to create
	 */
	private void createTenantBucket(final TenantResourceIdentifier resourceId) {
		if (StringUtils.isEmpty(configService.getProperty(ConfigurationKey.s3_endpoint))) {
			throw new RuntimeException("Object storage endpoint not configured");
		}

		storageService.createBucket(resourceId);
	}

	/**
	 * Calls the agent API to create a tenant.
	 *
	 * @param tenantUuid UUID of the tenant
	 */
	private void createAgentTenant(final String tenantUuid) {
		if (StringUtils.isEmpty(configService.getProperty(ConfigurationKey.agent_api_url))) {
			throw new RuntimeException("Agent API endpoint not configured");
		}

		if (!agentsService.createTenant(tenantUuid)) {
			throw new RuntimeException("agentsService.createTenant(\"" + tenantUuid + "\") returned false");
		}
	}

	/**
	 * Main method that iterates over non-deleted customers and attempts to create tenancy state for them.
	 *
	 * @param args Command-line arguments
	 */
	public static void main(final String[] args) throws Exception {
		try {
			Log4jUtils.configureConsoleAppender(Log4jUtils.Parameters.builder().level(Level.DEBUG).build());

			Configuration.initializeConfigurationClass();
			XMLAPI.initializeSslContexts();

			final TenantStateCreator stateCreator =
					new TenantStateCreator(ServiceProvider.getConfigService(), ServiceProvider.getStorageService(), ServiceProvider.getAgentsService());

			final List<Customer> customers;
			try (final Connection databaseConnection = DbAccess.getInstance().getConnection()) {
				customers = Customer.fetchObjects(Customer.class, databaseConnection, Access.ADMIN, "deleted IS NULL");
			}

			for (final Customer customer : customers) {
				LOG.info("Creating tenant state for customer " + customer.getUuid());

				if (stateCreator.createState(customer.getUuid())) {
					LOG.info("Successfully created tenant state for customer " + customer.getUuid());
				}
				else {
					LOG.error("Failed to create tenant state for customer " + customer.getUuid());
				}

				LOG.info("Migrating buckets for customer " + customer.getUuid());
				final BucketMigrationResult result = stateCreator.migrateBuckets(customer);
				LOG.info("Migrated "
						+ result.getMigratedObjects()
						+ " objects and failed to migrate "
						+ result.getFailedObjects()
						+ " objects for customer "
						+ customer.getUuid());
			}
		}
		catch (final RuntimeException ex) {
			LOG.error("Something went wrong trying to create tenant state", ex);
			System.exit(1);
		}
		System.exit(0);
	}
}
