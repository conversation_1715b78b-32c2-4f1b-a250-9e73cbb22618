package com.chilicoders.util;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;

import com.chilicoders.app.ScanApp;
import com.chilicoders.bl.UserBusiness;
import com.chilicoders.util.log4j.Log4jUtils;
import com.chilicoders.util.thread.TimerHandler;

public class Forgotten {
	/**
	 * Used to send out a forgotten password email.
	 *
	 * @param arg Email to send to.
	 */
	public static void main(final String[] arg) {
		final PrintStream out = System.out;
		final String logFile = "/tmp/forgotten.log";

		try (final PrintStream stream = new PrintStream(new FileOutputStream(logFile, true), false, UTF_8.name())) {
			System.setErr(stream);
			System.setOut(stream);
			System.out.println(System.lineSeparator() + "init Password Forgotten");

			Log4jUtils.configureFileAppender(Log4jUtils.Parameters.builder().filePath(logFile).build());

			Configuration.initializeConfigurationClass();
			ScanApp.getInstance(false);

			final UserBusiness ub = new UserBusiness();
			final String newPwd = ub.fixHiabPwd(arg.length > 0 ? arg[0] : null, arg.length > 1 ? arg[1] : null);

			System.setOut(out);
			if (newPwd != null) {
				System.out.println("Your username and password recovery URL is: " + newPwd);
			}
		}
		catch (final RuntimeException | FileNotFoundException | UnsupportedEncodingException e) {
			e.printStackTrace();

			System.setOut(out);
			System.out.println("Error occured while resetting password");
			System.exit(1);
		}
		TimerHandler.destroy();
	}
}
