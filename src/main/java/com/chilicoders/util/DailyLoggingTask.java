package com.chilicoders.util;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.bl.LoggingBusiness;
import com.chilicoders.bl.ScheduleObjectBusiness;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.db.Access;
import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.FindingImpl;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.db.objects.Logging;
import com.chilicoders.db.objects.ScheduleObject;
import com.chilicoders.db.objects.Target;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.model.EventType;
import com.chilicoders.model.Template;
import com.chilicoders.model.events.properties.EventAcceptanceExpiredProperties;
import com.chilicoders.model.events.properties.EventScanScheduledProperties;
import com.chilicoders.model.events.properties.EventScanScheduledProperties.ScheduleInfo;
import com.chilicoders.model.events.properties.EventScanScheduledProperties.TargetInfo;
import com.chilicoders.util.thread.TimerHandler;

/**
 * Sends Loggings for different events every day.
 */
public class DailyLoggingTask extends TimerTask {
	/**
	 * Our logger instance.
	 */
	private static final Logger LOG = LogManager.getLogger(DailyLoggingTask.class);

	/**
	 * @see java.util.TimerTask#run()
	 */
	@Override
	public void run() {
		try (final Connection conn = DbAccess.getInstance().getConnection()) {
			LOG.info("Sending loggings");

			sendAcceptanceTimeOutLogging(conn);
			sendTargetScheduledLogging(conn);
			sendScanScheduledLogging(conn);
		}
		catch (final Exception e) {
			LOG.error("Error: ", e);
		}
		scheduleTask(true);
	}

	/**
	 * Schedules this task.
	 *
	 * @param reschedule <code>true</code> if this task has just been executed, <code>false</code> otherwise.
	 */
	public static void scheduleTask(final boolean reschedule) {
		if (!reschedule) {
			TimerHandler.schedule(TimerHandler.Task.DailyLogging, new Date());
		}
		else {
			final Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, 1);
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 10);
			cal.set(Calendar.SECOND, 0);

			TimerHandler.schedule(TimerHandler.Task.DailyLogging, cal.getTime());
		}
	}

	/**
	 * Sends information to user about accepted risks that are about to expire.
	 *
	 * @param conn A database connection.
	 */
	private void sendAcceptanceTimeOutLogging(final Connection conn) {
		LOG.info("Sending logging for accepted risks that are about to expire");
		try {
			final String where =
					"((reportxid = ANY(SELECT lastreportid FROM tuserdatas u LEFT JOIN targetscandata ts ON u.xid = ts.targetid WHERE pci=0 AND xuserxid=r.xuserxid AND xid = r.xipxid AND r.xtemplate != ?)) OR "
							+ "(reportxid = ANY(SELECT unnest(lastreportids) FROM schedules WHERE template=-10 AND xuserxid=r.xuserxid AND xid = r.xsoxid AND r.xtemplate = ?))) AND r.acceptedlength > 0 AND r.acceptedlength < 30000 "
							+ "AND r.acceptexpires >= NOW()::DATE + INTERVAL '1 DAY' AND r.acceptexpires < NOW()::DATE + INTERVAL '2 DAY' ORDER BY r.xuserxid";
			final List<FindingImpl> findings = FindingImpl.fetchObjects(FindingImpl.class, conn, Access.ADMIN, where, Template.Was.getId(), Template.Was.getId());

			final Map<Long, List<EventAcceptanceExpiredProperties.Finding>> userFindings = new HashMap<>();
			final HashMap<Long, String> map = new HashMap<>();

			for (final FindingImpl finding : findings) {
				if (finding.getTemplateId() != Template.Swat.getId()) {
					final EventAcceptanceExpiredProperties.Finding info = EventAcceptanceExpiredProperties.Finding.builder()
							.targetId(finding.getTargetId())
							.ruleName(finding.getName())
							.ruleId(finding.getVulnId())
							.port(finding.getPort())
							.protocol(finding.getProtocol())
							.acceptedBy(finding.getAcceptBy())
							.acceptDate(finding.getAcceptDate())
							.acceptedLength(finding.getAcceptedLength())
							.acceptExpires(finding.getAcceptExpires())
							.build();

					if (StringUtils.isEmpty(map.get(finding.getTargetId()))) {
						String host = finding.getVirtualHost();
						if (finding.getTemplateId() != Template.Was.getId()) {
							final List<Target> targets =
									Target.fetchObjects(Target.class, conn, Access.createAccess(AccessType.ADMIN, "target"), "xuserxid = ? AND xid = ?", finding.getUserId(),
											finding.getTargetId());
							if (targets.size() > 0) {
								host = targets.get(0).getTarget();
							}
						}
						map.put(finding.getTargetId(), host);
					}

					final List<EventAcceptanceExpiredProperties.Finding> findingList =
							userFindings.containsKey(finding.getUserId()) ? userFindings.get(finding.getUserId()) : new ArrayList<>();
					findingList.add(info);
					userFindings.put(finding.getUserId(), findingList);
				}
			}

			final LoggingBusiness lb = new LoggingBusiness();
			for (final Entry<Long, List<EventAcceptanceExpiredProperties.Finding>> entry : userFindings.entrySet()) {
				final EventAcceptanceExpiredProperties properties = new EventAcceptanceExpiredProperties(entry.getValue());
				lb.sendLoggingList(entry.getKey(), O24Event.RiskAcceptanceExpiring, properties, EventType.Normal);
			}
		}
		catch (final RuntimeException | SQLException e) {
			LOG.error("Error: ", e);
		}
	}

	/**
	 * Sends events about targets that are scheduled for scanning.
	 *
	 * @param conn A database connection.
	 */
	private void sendTargetScheduledLogging(final Connection conn) {
		try {
			LOG.info("Sending logging for target scheduled");

			final Map<String, List<TargetInfo>> userTargets = new HashMap<>();

			final List<Logging> loggings = Logging.fetchObjects(Logging.class, conn, Access.ADMIN, "xrefid =? AND bactive = 1 ORDER BY xuserxid, daysinadvance",
					O24Event.TargetScanScheduled.getId());
			for (final Logging logging : loggings) {
				if (logging.getDaysInAdvance() > -1) {
					final String key = logging.getUserId() + "_" + logging.getDaysInAdvance();
					if (userTargets.get(key) == null) {
						final List<TargetInfo> targetInfo = new ArrayList<>();

						final String tableOverride = "vuserdata u LEFT JOIN (SELECT MIN(nextscandate) AS nextscandate, s.userid AS nextscandateuser, unnest(("
								+ "(SELECT array_agg(xid) FROM tuserdatas u WHERE u.xuserxid=s.userid AND xs.itype=2 AND xs.xgroupxid != -1 AND xid IN (SELECT xipxid FROM xlinkgeneric WHERE xid =ANY (paths))) ||"
								+ "(SELECT array_agg(xid) FROM tuserdatas u WHERE u.xuserxid=s.userid AND xs.itype=2 AND xs.xgroupxid = -1) ||"
								+ "(SELECT array_agg(xid) FROM tuserdatas u WHERE u.xuserxid=s.userid AND xs.itype=1 AND CASE WHEN xs.scannerid != -1 THEN xs.scannerid=u.scannerid ELSE true END AND ((u.ipaddress >= xs.ipaddress AND "
								+ "u.ipaddress <= xs.endipaddress) OR (u.ipaddress IS NULL AND u.hostnameid=xs.hostnameid)))"
								+ ")) AS nextscandatexipxid FROM schedules s LEFT JOIN xlinkscheduleobject xs ON s.id=xs.xid WHERE template NOT IN (-10, -11) AND nextscandate IS NOT NULL AND (finalscandate IS NULL OR finalscandate>nextscandate) AND NOT deleted "
								+ "GROUP BY nextscandatexipxid, userid) AS n ON n.nextscandateuser = u.xuserxid AND n.nextscandatexipxid = u.xid";
						final String select =
								DbHelper.getObjectSelect(Target.class, null, tableOverride, Access.createAccess(AccessType.ADMIN, "target,nextscandate"), null, null, null,
										"nextscandate >= NOW()::DATE + (? || 'days')::INTERVAL AND nextscandate < NOW()::DATE + (? || 'days')::INTERVAL AND xuserxid =?");

						final List<Target> targets =
								Target.fetchObjects(Target.class, conn, select, logging.getDaysInAdvance(), logging.getDaysInAdvance() + 1, logging.getUserId());
						for (final Target target : targets) {
							final TargetInfo info = TargetInfo.builder().targetId(target.getId()).host(target.getTarget()).startTime(target.getNextScanDate()).build();
							targetInfo.add(info);
						}
						if (!targetInfo.isEmpty()) {
							userTargets.put(key, targetInfo);
						}
					}

					if (userTargets.get(key) != null) {
						final EventScanScheduledProperties properties = EventScanScheduledProperties.builder().days(logging.getDaysInAdvance())
								.loggingId(logging.getId())
								.target(true)
								.targets(userTargets.get(key))
								.build();
						new LoggingBusiness().sendLoggingList(logging.getUserId(), O24Event.TargetScanScheduled, properties, EventType.Normal);
					}
				}
			}
		}
		catch (final RuntimeException | SQLException e) {
			LOG.error("Error: ", e);
		}
	}

	/**
	 * Sends events for scan schedules that are about to start.
	 *
	 * @param conn A database connection.
	 */
	private void sendScanScheduledLogging(final Connection conn) {
		try {
			LOG.info("Sending logging for scan schedules that are about to start");

			final List<Logging> loggings =
					Logging.fetchObjects(Logging.class, conn, Access.ADMIN, "xrefid =? AND bactive = 1 ORDER BY xuserxid", (Object) O24Event.ScanScheduleScheduled.getId());
			for (final Logging logging : loggings) {
				final List<ScheduleInfo> scheduleInfo = new ArrayList<>();
				if (logging.getDaysInAdvance() > -1) {
					final BaseLoggedOnUser user;
					if (logging.getSubUserId() > 0) {
						user = LoggedOnSubUser.getById(LoggedOnSubUser.class, conn, Access.ADMIN, logging.getSubUserId());
					}
					else {
						user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, logging.getUserId());
					}

					final StringBuilder where = new StringBuilder();
					final List<Object> params = new ArrayList<>();
					StringUtils.concatenateFilters(where,
							"nextscandate >= NOW()::DATE + (? || 'days')::INTERVAL AND nextscandate < NOW()::DATE + (? || 'days')::INTERVAL AND userid =? AND NOT deleted AND template NOT IN (-10,-11)");
					params.add(logging.getDaysInAdvance());
					params.add(logging.getDaysInAdvance() + 1);
					params.add(logging.getUserId());
					if (logging.getSubUserId() > 0) {
						if (logging.isMyScans()) {
							StringUtils.concatenateFilters(where, "subuserid =?");
							params.add(logging.getSubUserId());
						}
						else if (!logging.isMyScans() && (!user.hasAllTargetsAccess() || !user.hasAllScannerAccess() || !user.allowScheduleManagement(true))) {
							StringUtils.concatenateFilters(where, "subuserid IN (SELECT xid FROM tsubusers WHERE xpathup ~ (',' || ? || ','))");
							params.add(logging.getSubUserId());
						}
					}
					else if (logging.isMyScans()) {
						StringUtils.concatenateFilters(where, "subuserid IS NULL OR subuserid = -1");
					}

					final List<ScheduleObject> schedules = ScheduleObject.fetchObjects(ScheduleObject.class, conn, Access.ADMIN, where.toString(), params);
					for (final ScheduleObject schedule : schedules) {
						final List<Target> targetlist = new ArrayList<>();

						final String blacklist = IpUtils.removeTargetListComments(schedule.getIgnoreTargetlist());
						if (!StringUtils.isEmpty(schedule.getGroupList())) {
							ScheduleObjectBusiness.collectScanTargetsFromGroups(conn, user, schedule, false, targetlist, null, blacklist, false);
						}
						if (!StringUtils.isEmpty(schedule.getTargetList())) {
							ScheduleObjectBusiness.collectScanTargets(conn, user, schedule, false, targetlist, null, blacklist, false);
						}
						if (schedule.isPci()) {
							ScheduleObjectBusiness.consolidateScans(conn, targetlist);
						}

						final ScheduleInfo info = ScheduleInfo.builder().targetIds(Target.getTargetIdString(targetlist))
								.scheduleName(schedule.getName())
								.startTime(schedule.getNextScanDate())
								.scheduleId(schedule.getId()).build();
						scheduleInfo.add(info);
					}

					if (!scheduleInfo.isEmpty()) {
						final EventScanScheduledProperties properties = EventScanScheduledProperties.builder().days(logging.getDaysInAdvance())
								.subuserId(logging.getSubUserId())
								.loggingId(logging.getId())
								.schedules(scheduleInfo)
								.build();
						new LoggingBusiness().sendLoggingList(logging.getUserId(), O24Event.ScanScheduleScheduled, properties, EventType.Normal);
					}
				}
			}
		}
		catch (final RuntimeException | SQLException e) {
			LOG.error("Error: ", e);
		}
	}
}
