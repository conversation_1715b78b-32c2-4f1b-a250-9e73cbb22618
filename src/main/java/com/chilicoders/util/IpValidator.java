package com.chilicoders.util;

import java.util.regex.Pattern;

import com.googlecode.ipv6.IPv6Address;

public class IpValidator {

	private static final Pattern pipv4 =
			Pattern.compile("^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])$");

	/**
	 * Checks if an address is a valid IPv6 address.
	 *
	 * @param address Address
	 * @return <code>true</code> if valid, <code>false</code> otherwise.
	 */
	public static boolean isValidIPv6(final String address) {
		try {
			return address != null && IPv6Address.fromString(address) != null;
		}
		catch (final IllegalArgumentException e) {
			return false;
		}
	}

	/**
	 * Checks if an address is a valid IPv4 address.
	 *
	 * @param address Address
	 * @return <code>true</code> if valid, <code>false</code> otherwise.
	 */
	public static boolean isValidIPv4(final String address) {
		return address != null && pipv4.matcher(address).matches();
	}

	/**
	 * Checks if an address is a valid IP address.
	 *
	 * @param address Address
	 * @return <code>true</code> if valid, <code>false</code> otherwise.
	 */
	public static boolean isValidIP(final String address) {
		return address != null && (isValidIPv4(address) || isValidIPv6(address));
	}
}
