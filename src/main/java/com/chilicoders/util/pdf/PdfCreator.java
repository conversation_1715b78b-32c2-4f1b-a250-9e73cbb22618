package com.chilicoders.util.pdf;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.io.IOUtils;

import com.google.common.collect.ImmutableList;
import com.lowagie.text.Anchor;
import com.lowagie.text.BadElementException;
import com.lowagie.text.ChapterAutoNumber;
import com.lowagie.text.Chunk;
import com.lowagie.text.DocListener;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.ElementTags;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.Section;
import com.lowagie.text.html.HtmlTags;
import com.lowagie.text.html.simpleparser.HTMLWorker;
import com.lowagie.text.html.simpleparser.StyleSheet;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.FontSelector;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPageEvent;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfTemplate;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.draw.DottedLineSeparator;
import com.lowagie.text.pdf.draw.DrawInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

public class PdfCreator {

	public static final int FONT_BOLD = Font.BOLD;
	public static final int FONT_NORMAL = Font.NORMAL;
	public static final int FONT_UNDERLINE = Font.UNDERLINE;
	public static final int FONT_ITALIC = Font.ITALIC;

	public static final int RUN_DIRECTION_RTL = PdfWriter.RUN_DIRECTION_RTL;
	public static final int RUN_DIRECTION_LTR = PdfWriter.RUN_DIRECTION_LTR;
	public static final int RUN_DIRECTION_NO_BIDI = PdfWriter.RUN_DIRECTION_NO_BIDI;

	public static final int ALIGN_CENTER = Element.ALIGN_CENTER;
	public static final int ALIGN_LEFT = Element.ALIGN_LEFT;
	public static final int ALIGN_RIGHT = Element.ALIGN_RIGHT;
	public static final int ALIGN_BOTTOM = Element.ALIGN_BOTTOM;
	public static final int ALIGN_TOP = Element.ALIGN_TOP;
	public static final int ALIGN_MIDDLE = Element.ALIGN_MIDDLE;

	private static final int POSITION_BOTTOM_LEFT = 1;
	private static final int POSITION_TOP_RIGHT = 2;

	public static final int STANDARD_ENCRYPTION_128 = PdfWriter.STANDARD_ENCRYPTION_128;

	private final HashMap<String, FontSelector> fonts = new HashMap<>();

	@Getter
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private Document document;

	@Getter
	@SuppressFBWarnings("EI_EXPOSE_REP")
	private PdfWriter writer;

	/**
	 * Set document destination.
	 *
	 * @param paragraph Reference paragraph object.
	 * @param destination Destination key
	 */
	public static void setDestination(final PdfParagraph paragraph, final String destination) {
		if (paragraph != null && !paragraph.getChunks().isEmpty()) {
			((Chunk) paragraph.getChunks().get(paragraph.getChunks().size() - 1)).setLocalDestination(destination);
		}
	}

	/**
	 * Set document goto.
	 *
	 * @param paragraph Reference paragraph object.
	 * @param key Goto key
	 */
	public static void setGoto(final PdfParagraph paragraph, final String key) {
		if (paragraph != null) {
			for (final Element element : paragraph.getChunks()) {
				((Chunk) element).setLocalGoto(key);
			}
		}
	}

	/**
	 * Set background color of phrase object.
	 *
	 * @param phrase The phrase obejct
	 * @param color Background color
	 * @param left Extra left space
	 * @param bottom Extra bottom space
	 * @param right Extra right space
	 * @param top Extra top space
	 */
	public static void setBackground(final PdfPhrase phrase, final Color color, final float left, final float bottom, final float right, final float top) {
		if (phrase != null) {
			for (final Element element : phrase.getChunks()) {
				((Chunk) element).setBackground(color, left, bottom, right, top);
			}
		}
	}

	/**
	 * Create new chapter.
	 *
	 * @param title Chapter title
	 * @param bookmark Chapther bookmark
	 * @return The created chapter
	 */
	public static PdfChapter newChapter(final PdfParagraph title, final String bookmark) {
		final PdfChapter chapter = new PdfChapter("");
		chapter.setNumberDepth(0);
		if (title != null) {
			chapter.setTitle(title);
		}
		if (bookmark != null) {
			chapter.setBookmarkTitle(bookmark);
		}
		return chapter;
	}

	/**
	 * Set document into.
	 *
	 * @param title Document title
	 * @param subject Document subject
	 * @param author Document author
	 * @param creator Document creator
	 */
	public void setDocumentInfo(final String title, final String subject, final String author, final String creator) {
		this.document.addTitle(title);
		this.document.addSubject(subject);
		this.document.addAuthor(author);
		this.document.addCreator(creator);
	}

	public void open() {
		this.document.open();
	}

	public void close() {
		this.document.close();
	}

	public void closeWriter() {
		this.writer.close();
	}

	public void newPage() {
		this.document.newPage();
	}

	public void add(final PdfSection section) throws PdfException {
		add(section.getSection());
	}

	public void add(final PdfImage image) throws PdfException {
		add(image.getImage());
	}

	/**
	 * Add element to pdf.
	 *
	 * @param element A element to add
	 */
	public void add(final Element element) throws PdfException {
		try {
			this.document.add(element);
		}
		catch (final DocumentException e) {
			throw new PdfException(e.getMessage());
		}
	}

	/**
	 * Setup pdf document.
	 *
	 * @param marginTop Top margin
	 * @param marginBottom Bottom margin
	 * @param out The result stream
	 */
	public void init(final float marginTop, final float marginBottom, final OutputStream out) throws PdfException {
		try {
			this.document = new Document(PageSize.A4, 0, 0, marginTop, marginBottom);
			this.writer = PdfWriter.getInstance(this.document, out);
		}
		catch (final DocumentException e) {
			throw new PdfException(e.getMessage());
		}
	}

	/**
	 * Setup pdf document.
	 *
	 * @param out The result stream
	 */
	public void init(final ByteArrayOutputStream out) throws PdfException {
		try {
			this.document = new Document();
			this.writer = PdfWriter.getInstance(this.document, out);
		}
		catch (final DocumentException e) {
			throw new PdfException(e.getMessage());
		}
	}

	/**
	 * Set pdf encryption.
	 *
	 * @param userPassword Document password for user
	 * @param ownerPassword Document password for owner
	 * @param encryption Document encryptions
	 */
	public void setEncryption(final String userPassword, final String ownerPassword, final int encryption) throws PdfException {
		try {
			this.writer.setEncryption((userPassword != null ? userPassword.getBytes(UTF_8) : null), (ownerPassword != null ? ownerPassword.getBytes(UTF_8) : null),
					PdfWriter.ALLOW_PRINTING | PdfWriter.ALLOW_SCREENREADERS | PdfWriter.ALLOW_COPY | PdfWriter.ALLOW_FILL_IN, encryption);
		}
		catch (final DocumentException e) {
			throw new PdfException(e.getMessage());
		}
	}

	public void setRunDirection(final int rd) {
		this.writer.setRunDirection(rd);
	}

	public FontSelector getFont(final String key) {
		return this.fonts.get(key);
	}

	public static Font getFont(final String fontName, final float fontSize, final Color fontColor) {
		return FontFactory.getFont(fontName, fontSize, fontColor);
	}

	public static Font getFont(final String fontName, final float fontSize, final int fontStyle, final Color fontColor) {
		return FontFactory.getFont(fontName, fontSize, fontStyle, fontColor);
	}

	public static Font getFont(final String fontName, final float fontSize) {
		return FontFactory.getFont(fontName, fontSize);
	}

	/**
	 * Create new font.
	 *
	 * @param key Font key
	 * @param family1 Font family
	 * @param encoding1 Font encoding
	 * @param family2 Font family if first not exists
	 * @param size Font size
	 * @param color Font color
	 * @param style Font style
	 */
	public void addFont(final String key, final String family1, final String encoding1, final String family2, final float size, final Color color, final int style)
			throws IOException, PdfException {
		try {
			final FontSelector selector = new FontSelector();

			Font font = FontFactory.getFont(family2);
			font.setFamily(family2);
			font.setSize(size);
			font.setColor(color);
			font.setStyle(style);
			selector.addFont(font);

			font = new Font(family1 == null ? null : BaseFont.createFont(family1, encoding1, BaseFont.NOT_EMBEDDED));
			font.setSize(size);
			font.setColor(color);
			font.setStyle(style);
			selector.addFont(font);

			this.fonts.put(key, selector);
		}
		catch (final DocumentException e) {
			throw new PdfException(e.getMessage());
		}
	}

	public static void registerFont(final String path) {
		FontFactory.register(path);
		FontFactory.defaultEncoding = BaseFont.IDENTITY_H;
	}

	public static void registerFont(final String path, final String alias) {
		FontFactory.register(path, alias);
		FontFactory.defaultEncoding = BaseFont.IDENTITY_H;
	}

	public static boolean hasFont(final String alias) {
		return FontFactory.contains(alias);
	}

	/**
	 * Loads an image from file or classpath.
	 *
	 * @param path Path to load from, can start with classpath: to indicate load from classpath.
	 * @return PDFImage
	 */
	public static PdfImage getImage(final String path) throws IOException {
		final Image image;
		if (path.startsWith("classpath")) {
			final byte[] data = IOUtils.toByteArray(ClassLoader.getSystemClassLoader().getResourceAsStream(path.replaceAll("classpath:?/?", "")));
			image = Image.getInstance(data);
		}
		else {
			image = Image.getInstance(path);
		}
		return image == null ? null : new PdfImage(image);
	}

	/**
	 * Create image from byte data.
	 *
	 * @param data Image data
	 * @param fit True to fit image in cell
	 * @return The created image.
	 */
	public static PdfImage getImage(final byte[] data, final boolean fit) {
		try {
			if (data == null) {
				return null;
			}
			return new PdfImage(Image.getInstance(data), fit);
		}
		catch (final Exception e) {
			return null;
		}
	}

	public static PdfImage getImage(final byte[] data) {
		return getImage(data, false);
	}

	/**
	 * Get image instance.
	 *
	 * @param path Path to image
	 * @return Image
	 */
	public static PdfImage getImageInstance(final String path) throws PdfException, IOException {
		try {
			final Image image;
			if (path.startsWith("classpath")) {
				final byte[] data = IOUtils.toByteArray(ClassLoader.getSystemClassLoader().getResourceAsStream(path.substring(10)));
				image = Image.getInstance(data);
			}
			else {
				image = Image.getInstance(path);
			}
			return image == null ? null : new PdfImage(image);
		}
		catch (final BadElementException e) {
			throw new PdfException(e.getMessage());
		}
	}

	/**
	 * Create new line image.
	 *
	 * @param color Image color
	 * @return The created image
	 */
	public PdfImage getLineImage(final Color color) throws PdfException {
		final PdfTemplate template = this.writer.getDirectContent().createTemplate(5, 5);
		template.setColorFill(color);
		template.rectangle(0, 0, 5f, 5f);
		template.fill();

		try {
			final Image image = Image.getInstance(template);
			image.scaleAbsolute(8, 8);
			return new PdfImage(image);
		}
		catch (final BadElementException e) {
			throw new PdfException(e.getMessage());
		}
	}

	public void setPageEvent(final PdfPageEvent event) {
		this.writer.setPageEvent(event);
	}

	/**
	 * Merge a pdf into document.
	 *
	 * @param pdf Path to pdf to merge
	 */
	public void mergePdf(final String pdf) throws IOException {
		final PdfReader reader = new PdfReader(pdf);
		final PdfContentByte cb = this.writer.getDirectContent();

		int currentPage = 0;
		while (currentPage < reader.getNumberOfPages()) {
			if (currentPage != 0) {
				this.document.newPage();
			}
			currentPage++;

			final PdfImportedPage page = this.writer.getImportedPage(reader, currentPage);
			cb.addTemplate(page, 0, 0);
		}
	}

	/**
	 * Add cells to table.
	 *
	 * @param table The table
	 * @param objects List of object to add
	 * @param paddingtop Top padding
	 * @param paddingbottom Bottom padding
	 * @param font Text font
	 */
	public void addTableCells(final PdfTable table, final Object[] objects, final int paddingtop, final int paddingbottom, final String font) {
		for (final Object object : objects) {
			if (object instanceof PdfCell) {
				final PdfCell cell = (PdfCell) object;
				table.addCell(cell);
			}
			else {
				PdfCell cell = getCell();
				if (object == null) {
					cell.setPhrase(getFont(font != null ? font : "text").process(""));
				}
				else if (object instanceof PdfTable) {
					cell.addElement((PdfTable) object);
				}
				else if (object instanceof Phrase) {
					cell.setPhrase((Phrase) object);
				}
				else if (object instanceof Paragraph) {
					cell.addElement((Paragraph) object);
				}
				else if (object instanceof PdfImage) {
					cell = getCell((PdfImage) object);
				}
				else if (object instanceof String) {
					cell.setPhrase(getFont(font != null ? font : "text").process((String) object));
				}
				else if (object instanceof ArrayList) {
					final PdfTable listtable = getTable(null, 1, RUN_DIRECTION_LTR);
					@SuppressWarnings("unchecked")
					final ArrayList<PdfTable> tables = (ArrayList<PdfTable>) object;
					for (final PdfTable pdfTable : tables) {
						final PdfCell listcell = getCell();
						setCellPadding(listcell, 0, 0, 0, 0);
						listcell.addElement(pdfTable);
						listtable.addCell(listcell);
						listtable.addRow();
					}
					cell.addElement(listtable);
				}
				else {
					cell.setPhrase(getFont(font != null ? font : "text").process("" + object));
				}
				table.addCell(cell);
			}
		}
		setCellPadding(table, paddingtop, paddingbottom, -1, -1);
		table.addRow();
	}

	/**
	 * Create new table.
	 *
	 * @param widths Column widths
	 * @param columns Number of columns
	 * @param dir Run direction for text
	 * @return The created table.
	 */
	public static PdfTable getTable(final float[] widths, final int columns, final int dir) {
		final PdfTable table;
		if (widths != null) {
			table = new PdfTable(widths);
			table.setTotalWidth(100f);
		}
		else {
			table = new PdfTable(columns);
		}

		table.setWidthPercentage(100);
		table.setRunDirection(dir);

		if (dir == RUN_DIRECTION_RTL) {
			try {
				final float[] absoluteWidths = table.getAbsoluteWidths();
				final float[] newWidths = new float[absoluteWidths.length];
				for (int i = 0; i < absoluteWidths.length; i++) {
					final int index = absoluteWidths.length - 1 - i;
					if (absoluteWidths[i] == 0) {
						newWidths[index] = 100.0f / absoluteWidths.length;
					}
					else {
						newWidths[index] = absoluteWidths[i];
					}
				}
				table.setWidths(newWidths);
			}
			catch (final Exception e) {
				return table;
			}
		}

		return table;
	}

	public static PdfCell getCell() {
		return getCell(-1, -1, -1, -1, 0, -1, null);
	}

	public static PdfCell getCell(final PdfImage image) {
		return getCell(-1, -1, -1, -1, 0, -1, image);
	}

	/**
	 * Create new cell.
	 *
	 * @param text Cell text
	 * @param font Cell font
	 * @param alignment Cell alignment
	 * @return The created cell
	 */
	public static PdfCell getCell(final String text, final FontSelector font, final int alignment) {
		final Paragraph p = new Paragraph(font.process(text));
		if (alignment != -1) {
			p.setAlignment(ALIGN_CENTER);
		}
		final PdfCell c = getCell();
		c.addElement(p);
		return c;
	}

	public static PdfCell getCell(final String text, final FontSelector font) {
		return getCell(text, font, -1);
	}

	/**
	 * Create new cell.
	 *
	 * @param horizontalAlignment Horizontal alignment
	 * @param verticalAlignment Vertical alignment
	 * @param colSpan Colspan
	 * @param rowSpan Rowspan
	 * @param border Cell border
	 * @param borderWidth Border width
	 * @param image Cell image content
	 * @return The created cell
	 */
	private static PdfCell getCell(final int horizontalAlignment, final int verticalAlignment, final int colSpan, final int rowSpan,
								   final int border, final float borderWidth, final PdfImage image) {
		PdfCell cell = null;
		if (image != null) {
			cell = new PdfCell(image.getImage(), image.isFit());
		}
		else {
			cell = new PdfCell();
		}

		if (horizontalAlignment != -1) {
			cell.setHorizontalAlignment(horizontalAlignment);
		}

		if (verticalAlignment != -1) {
			cell.setVerticalAlignment(verticalAlignment);
		}

		if (colSpan != -1) {
			cell.setColspan(colSpan);
		}

		if (rowSpan != -1) {
			cell.setRowspan(rowSpan);
		}

		cell.setNoWrap(false);

		if (borderWidth != -1) {
			cell.setBorderWidth(borderWidth);
		}

		cell.setBorder(border);

		setCellPadding(cell, 2, 2, 2, 2);

		return cell;
	}

	/**
	 * Set cell padding.
	 *
	 * @param cell The cell
	 * @param top Top padding
	 * @param bottom Bottom padding
	 * @param left Left padding
	 * @param right Right padding
	 */
	public static void setCellPadding(final PdfPCell cell, final float top, final float bottom, final float left, final float right) {
		if (left != -1) {
			cell.setPaddingLeft(left);
		}
		if (right != -1) {
			cell.setPaddingRight(right);
		}
		if (top != -1) {
			cell.setPaddingTop(top);
		}
		if (bottom != -1) {
			cell.setPaddingBottom(bottom);
		}
	}

	/**
	 * Set padding on table cells.
	 *
	 * @param table The table
	 * @param top Top padding
	 * @param bottom Bottom padding
	 * @param left Left padding
	 * @param right Right padding
	 */
	public static void setCellPadding(final PdfTable table, final float top, final float bottom, final float left, final float right) {
		for (final Iterator<PdfPCell> i = table.getCells().iterator(); i.hasNext(); ) {
			final PdfPCell cell = i.next();
			setCellPadding(cell, top, bottom, left, right);
		}
	}

	public static class PdfHeaderFooter extends PdfPageEventHelper {
		private boolean header = false;
		private Paragraph text;
		@SuppressFBWarnings("EI_EXPOSE_REP2")
		@Setter
		private PdfImage image;
		@SuppressFBWarnings("EI_EXPOSE_REP2")
		@Setter
		private PdfImage bg;
		@Setter
		private int bgSize = 250;
		@Setter
		private int bgPadding = 0;
		@Setter
		private int bgPosition = POSITION_BOTTOM_LEFT;
		private FontSelector font;
		private int firstPage = 1;
		private int rdir = PdfWriter.RUN_DIRECTION_LTR;
		@Setter
		private Color color;
		@Setter
		private boolean skipFirstPage;

		/**
		 * Create new header/footer.
		 *
		 * @param header If this is a header
		 * @param text Header/footer text
		 * @param font Text font
		 * @param firstPage Page number for first page
		 */
		@SuppressFBWarnings("EI_EXPOSE_REP2")
		public PdfHeaderFooter(final boolean header, final Paragraph text, final FontSelector font, final int firstPage) {
			this.header = header;
			this.text = text;
			this.font = font;
			this.image = null;
			this.firstPage = firstPage;
			this.bg = null;
			this.color = null;
			this.skipFirstPage = false;
		}

		@Override
		public void onOpenDocument(final PdfWriter writer, final Document document) {
		}

		@Override
		public void onChapter(final PdfWriter writer, final Document document, final float paragraphPosition, final Paragraph title) {
		}

		@Override
		public void onStartPage(final PdfWriter writer, final Document document) {
		}

		@Override
		public void onEndPage(final PdfWriter writer, final Document document) {
			try {
				if (this.color != null && writer.getPageNumber() == this.firstPage) {
					final PdfContentByte canvas = writer.getDirectContentUnder();
					final Rectangle rect = document.getPageSize();
					canvas.setColorFill(this.color);
					canvas.rectangle(rect.getLeft(), rect.getBottom(), rect.getWidth(), rect.getHeight());
					canvas.fill();
				}

				if (this.bg != null && writer.getPageNumber() == this.firstPage) {
					this.bg.getImage().scaleAbsolute(this.bgSize, this.bgSize);

					final PdfTable table = getTable(null, 1, this.rdir);
					table.setLockedWidth(true);
					table.setTotalWidth(this.bgSize);

					final PdfPCell cell = getCell();
					cell.addElement(this.bg.getImage());
					table.addCell(cell);
					table.addRow();

					if (this.bgPosition == POSITION_BOTTOM_LEFT) {
						table.writeSelectedRows(0, -1, document.left(), this.bgSize, writer.getDirectContentUnder());
					}
					else if (this.bgPosition == POSITION_TOP_RIGHT) {
						table.writeSelectedRows(0, -1, document.right() - this.bgSize - this.bgPadding, document.getPageSize().getHeight() - this.bgPadding,
								writer.getDirectContentUnder());
					}
				}

				if (this.skipFirstPage && writer.getPageNumber() == this.firstPage) {
					return;
				}

				if (this.header) {
					final PdfTable table = getTable(null, 1, this.rdir);
					table.setTotalWidth(document.right() - document.left());
					table.setLockedWidth(true);

					if (!this.text.isEmpty()) {
						final PdfPCell cell = getCell();
						cell.setLeading(1f, 1f);
						cell.setHorizontalAlignment(ALIGN_CENTER);
						cell.setPhrase(this.text);
						table.addCell(cell);
						table.addRow();
					}

					if (table.countRows() > 0) {
						table.writeSelectedRows(0, -1, document.left(), document.top() + document.topMargin() - 10, writer.getDirectContent());
					}
				}
				else {
					final PdfTable table = getTable(null, 1, this.rdir);
					table.setTotalWidth(document.right() - document.left());
					table.setLockedWidth(true);

					PdfPCell cell = getCell();
					setCellPadding(cell, 0, 0, 0, 0);
					cell.setFixedHeight(6);
					if (this.image != null && (writer.getPageNumber() != this.firstPage || this.bg == null)) {
						this.image.getImage().scaleAbsolute(document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin(), 5);
						cell.addElement(this.image.getImage());
					}
					table.addCell(cell);
					table.addRow();

					final Phrase phrase = new Phrase();
					phrase.add(this.text);

					final Paragraph paragraph = new Paragraph();
					paragraph.add(phrase);
					paragraph.add(this.font.process(Integer.toString(writer.getPageNumber())));

					cell = getCell();
					cell.setLeading(1.1f, 1.1f);
					cell.setHorizontalAlignment(ALIGN_CENTER);
					cell.setPhrase(paragraph);
					table.addCell(cell);
					table.addRow();

					table.writeSelectedRows(0, -1, document.left(), table.calculateHeights(true) + 15, writer.getDirectContent());
				}
			}
			catch (final RuntimeException e) {
				return;
			}
		}
	}

	public static class PdfSimpleTable extends PdfPTable {
		public PdfSimpleTable(final int columns) {
			super(columns);
		}
	}

	public static class PdfTable extends PdfPTable {
		private ArrayList<PdfPCell> cells = new ArrayList<>();
		private float indentationLeft = 0;
		private float indentationRight = 0;
		private int rows = 0;

		public PdfTable(final float[] relativeWidths) {
			super(relativeWidths);
		}

		public PdfTable(final int numColumns) {
			super(numColumns);
		}

		@Override
		public void addCell(final PdfPCell cell) {
			this.cells.add(cell);
		}

		public List<PdfPCell> getCells() {
			return this.cells == null ? null : ImmutableList.copyOf(this.cells);
		}

		public void setIndentationLeft(final float left) {
			this.indentationLeft = left;
		}

		public void setIndentationRight(final float right) {
			this.indentationRight = right;
		}

		public void addRow() {
			addRow(true);
		}

		/**
		 * Add cells to row.
		 *
		 * @param setIndentation Set indentation on cells
		 */
		public void addRow(final boolean setIndentation) {
			for (int i = 0; i < this.cells.size(); i++) {
				final PdfPCell cell = this.cells.get(i);
				if (setIndentation) {
					if (i == 0 && this.indentationLeft != -1) {
						cell.setPaddingLeft(this.indentationLeft);
					}
					if (i == (this.cells.size() - 1) && this.indentationRight != -1) {
						cell.setPaddingRight(this.indentationRight);
					}
				}
				super.addCell(cell);
			}
			this.rows++;
			this.cells.clear();
		}

		public int countRows() {
			return this.rows;
		}

		public void setRowCount(final int count) {
			this.rows = count;
		}
	}

	public static class PdfImage {
		private Image image = null;
		private boolean fit = false;

		@SuppressFBWarnings("EI_EXPOSE_REP2")
		public PdfImage(final Image image) {
			this.image = image;
		}

		@SuppressFBWarnings("EI_EXPOSE_REP2")
		public PdfImage(final Image image, final boolean fit) {
			this.image = image;
			this.fit = fit;
		}

		@SuppressFBWarnings("EI_EXPOSE_REP")
		public Image getImage() {
			return image;
		}

		public boolean isFit() {
			return fit;
		}

		public void setAlignment(final int alignment) {
			getImage().setAlignment(alignment);
		}

		public float getWidth() {
			return getImage().getWidth();
		}

		public float getHeight() {
			return getImage().getHeight();
		}

		public void scaleAbsolute(final float width, final float height) {
			getImage().scaleAbsolute(width, height);
		}

		public void scaleToFit(final float width, final float height) {
			getImage().scaleToFit(width, height);
		}

		public void setIndentationLeft(final float indentation) {
			getImage().setIndentationLeft(indentation);
		}

		public void setIndentationRight(final float indentation) {
			getImage().setIndentationRight(indentation);
		}

		public void setSpacingBefore(final float space) {
			getImage().setSpacingBefore(space);
		}
	}

	public static class PdfParagraph extends Paragraph {
		private static final long serialVersionUID = -330545747336164914L;

		public PdfParagraph() {
			super();
		}

		public PdfParagraph(final String text) {
			super(text);
		}

		public PdfParagraph(final Phrase phrase) {
			super(phrase);
		}

		public PdfParagraph(final String text, final Font font) {
			super(text, font);
		}

		public PdfParagraph(final Chunk chunk) {
			super(chunk);
		}

		@Override
		public boolean add(final Element element) {
			if (element instanceof Paragraph) {
				final Phrase phrase = new Phrase();
				phrase.add(element);
				return super.add(phrase);
			}
			return super.add(element);
		}
	}

	public static class PdfPhrase extends Phrase {
		private static final long serialVersionUID = 9092422416424530884L;

		public PdfPhrase(final Phrase phrase) {
			super(phrase);
		}

		public PdfPhrase(final String string, final Font font) {
			super(string, font);
		}

		public PdfPhrase(final Chunk chunk) {
			super(chunk);
		}

		public PdfPhrase() {
			super();
		}
	}

	public static class PdfCell extends PdfPCell {
		public PdfCell() {
			super();
		}

		public PdfCell(final Image image, final boolean fit) {
			super(image, fit);
		}

		public PdfCell(final Phrase phrase) {
			super(phrase);
		}

		public void addElement(final PdfImage image) {
			addElement(image.getImage());
		}

		public void addElement(final Object obj) {
			addElement((Element) obj);
		}
	}

	public static class PdfChapter extends ChapterAutoNumber {
		private static final long serialVersionUID = 8998686949268835258L;

		public PdfChapter(final String title) {
			super(title);
		}

		public void add(final PdfImage image) {
			add(image.getImage());
		}

		/**
		 * Create new section.
		 *
		 * @param title Section title
		 * @param bookmark Bookmark title
		 * @return The created section
		 */
		public PdfSection addSection(final PdfParagraph title, final String bookmark) {
			final Section section = addSection("");
			section.setNumberDepth(0);
			if (title != null) {
				section.setTitle(title);
			}
			if (bookmark != null) {
				section.setBookmarkTitle(bookmark);
			}
			return new PdfSection(section);
		}

		/**
		 * Add elements to section.
		 *
		 * @param elements List of elements
		 */
		public void addAll(final ArrayList<Object> elements) {
			for (final Object o : elements) {
				if (o instanceof PdfImage) {
					add(((PdfImage) o).getImage());
				}
				else {
					add((Element) o);
				}
			}
		}
	}

	public static class PdfAnchor extends Anchor {
		private static final long serialVersionUID = 1550385973477239347L;

		public PdfAnchor(final Phrase phrase) {
			super(phrase);
		}
	}

	public static class PdfChunk extends Chunk {
		public PdfChunk(final DrawInterface separator) {
			super(separator);
		}

		public PdfChunk(final String text) {
			super(text);
		}

		public PdfChunk(final String text, final Font font) {
			super(text, font);
		}

		public PdfChunk(final Image image, final float offsetX, final float offsetY, final boolean changeLeading) {
			super(image, offsetX, offsetY, changeLeading);
		}
	}

	public static class PdfDottedLineSeparator extends DottedLineSeparator {
	}

	public static class PdfSection {
		private Section section = null;

		@SuppressFBWarnings("EI_EXPOSE_REP2")
		public PdfSection(final Section section) {
			this.section = section;
		}

		@SuppressFBWarnings("EI_EXPOSE_REP")
		public Section getSection() {
			return this.section;
		}

		public void add(final Element element) {
			getSection().add(element);
		}

		public void add(final PdfImage image) {
			getSection().add(image.getImage());
		}

		public void newPage() {
			getSection().newPage();
		}
	}

	public static class PdfException extends Exception {
		private static final long serialVersionUID = 1943692112952147620L;

		public PdfException(final String message) {
			super(message);
		}

		public PdfException(final Exception exception) {
			super(exception);
		}
	}

	public static class PdfHTMLWorker extends HTMLWorker {
		private List<Element> elements = null;

		public PdfHTMLWorker() {
			super(null);
		}

		public PdfHTMLWorker(final DocListener document) {
			super(document);
		}

		public void parse(final String text, final PdfStyleSheet style) throws IOException {
			this.elements = PdfHTMLWorker.parseToList(new StringReader(text), style);
		}

		public List<Element> getElements() {
			return this.elements == null ? null : ImmutableList.copyOf(this.elements);
		}
	}

	public static class PdfHtmlTags extends HtmlTags {
	}

	public static class PdfElementTags extends ElementTags {
	}

	public static final class PdfReader extends com.lowagie.text.pdf.PdfReader {
		public PdfReader(final String filename) throws IOException {
			super(filename);
		}

		public PdfReader(final byte[] bytes) throws IOException {
			super(bytes);
		}
	}

	public static class PdfStyleSheet extends StyleSheet {
		public PdfStyleSheet() {
			super();
		}
	}

}
