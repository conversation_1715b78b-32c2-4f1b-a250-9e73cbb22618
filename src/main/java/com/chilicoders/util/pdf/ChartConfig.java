package com.chilicoders.util.pdf;

import java.awt.Color;
import java.awt.Font;
import java.awt.image.BufferedImage;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class ChartConfig {
	private BufferedImage background = null;
	private final Color[] colors;
	private final int width;
	private final int height;
	private String title = null;
	private int explodeSection = -1;
	private String label;
	private boolean gridLines = false;
	private boolean setColors = false;
	private boolean axisLineVisible = false;
	private boolean tickMarksVisible = false;
	private String axisXLabel = null;
	private String axisYLabel = null;
	private int tickUnit = -1;
	private boolean drawShapes = false;
	private Color color = null;
	private float seriesStroke = 3.0f;
	private Font font = new Font("Arial", Font.PLAIN, 20);
	private Font labelFont = new Font("Arial", Font.PLAIN, 20);
	private boolean legendVisible = false;

	/**
	 * Create new ChartConfig.
	 *
	 * @param colors List of colors for chart.
	 * @param width Chart width
	 * @param height Chart height
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public ChartConfig(final Color[] colors, final int width, final int height) {
		this.colors = colors;
		this.width = width;
		this.height = height;
	}

	@SuppressFBWarnings("EI_EXPOSE_REP")
	public BufferedImage getBackground() {
		return background;
	}

	@SuppressFBWarnings("EI_EXPOSE_REP")
	public Color[] getColors() {
		return colors;
	}

	public int getWidth() {
		return width;
	}

	public int getHeight() {
		return height;
	}

	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public void setBackground(final BufferedImage background) {
		this.background = background;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(final String title) {
		this.title = title;
	}

	public int getExplodeSection() {
		return explodeSection;
	}

	public void setExplodeSection(final int explodeSection) {
		this.explodeSection = explodeSection;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(final String label) {
		this.label = label;
	}

	public boolean isGridLines() {
		return gridLines;
	}

	public void setGridLines(final boolean gridLines) {
		this.gridLines = gridLines;
	}

	public boolean isSetColors() {
		return setColors;
	}

	public void setSetColors(final boolean setColors) {
		this.setColors = setColors;
	}

	public int getTickUnit() {
		return tickUnit;
	}

	public void setTickUnit(final int tickUnit) {
		this.tickUnit = tickUnit;
	}

	public boolean isDrawShapes() {
		return drawShapes;
	}

	public void setDrawShapes(final boolean drawShapes) {
		this.drawShapes = drawShapes;
	}

	public Color getColor() {
		return color;
	}

	public void setColor(final Color color) {
		this.color = color;
	}

	public boolean isAxisLineVisible() {
		return axisLineVisible;
	}

	public void setAxisLineVisible(final boolean axisLineVisible) {
		this.axisLineVisible = axisLineVisible;
	}

	public boolean isTickMarksVisible() {
		return tickMarksVisible;
	}

	public void setTickMarksVisible(final boolean tickMarksVisible) {
		this.tickMarksVisible = tickMarksVisible;
	}

	public String getAxisXLabel() {
		return axisXLabel;
	}

	public void setAxisXLabel(final String axisXLabel) {
		this.axisXLabel = axisXLabel;
	}

	public String getAxisYLabel() {
		return axisYLabel;
	}

	public void setAxisYLabel(final String axisYLabel) {
		this.axisYLabel = axisYLabel;
	}

	public float getSeriesStroke() {
		return seriesStroke;
	}

	public void setSeriesStroke(final float seriesStroke) {
		this.seriesStroke = seriesStroke;
	}

	public Font getFont() {
		return font;
	}

	public void setFont(final Font font) {
		this.font = font;
	}

	public Font getLabelFont() {
		return labelFont;
	}

	public void setLabelFont(final Font labelFont) {
		this.labelFont = labelFont;
	}

	public boolean isLegendVisible() {
		return legendVisible;
	}

	public void setLegendVisible(final boolean legendVisible) {
		this.legendVisible = legendVisible;
	}
}
