package com.chilicoders.util.translation;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Reads a javascript file and calls callbacks when a string to translate has been found.
 */
public final class JavascriptReader {
	/**
	 * A pattern to find the start of a javascript class.
	 */
	private static final Pattern extendPattern = Pattern.compile("\\s*(.*)\\s*=\\s*Ext.extend.*");
	/**
	 * Pattern to find the start of a javscript class.
	 */
	private static final Pattern extendPattern2 = Pattern.compile("\\s*Ext.extend\\s*\\(\\s*([^,]*)\\s*.*");
	/**
	 * Pattern to find the override of a class.
	 */
	private static final Pattern override = Pattern.compile("\\s*Ext.override\\s*\\(\\s*([^,]*)\\s*.*");
	private static final Pattern definePattern = Pattern.compile("\\s*Ext.define\\s*\\(\\s*'([^']*)'.*");
	/**
	 * Pattern to find CommonString usage.
	 */
	private static final Pattern csUsage = Pattern.compile("CS\\.([\\w]*)");

	/**
	 * The javascript file reader.
	 */
	private final BufferedReader reader;

	/**
	 * The callback listener.
	 */
	@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
	private JavaScriptReaderListener listener;

	/**
	 * A cached value, will call the notifier with this, but we must first know if this is the last string in the class or not.
	 */
	private String keyCached = null;

	/**
	 * Cached value, goes together with the cached key.
	 */
	private String valueCached = null;

	/**
	 * Creates a new reader.
	 *
	 * @param reader Reader to read javascript file to parse
	 * @param listener The callback listener.
	 */
	public JavascriptReader(final InputStreamReader reader, final JavaScriptReaderListener listener) {
		this.reader = new BufferedReader(reader);
		this.listener = listener;
	}

	/**
	 * The class we are currently parsing.
	 */
	private String currentClass = null;

	/**
	 * The last class we notified the listener with.
	 */
	private String latestNotifiedClass = null;

	/**
	 * The caller defined object to be passed to the listener.
	 */
	private Object obj;

	/**
	 * Cleans the class name.
	 *
	 * @param group An uncleaned class name.
	 * @return Cleaned class name.
	 */
	private String cleanClassString(final String group) {
		if (group.contains(";")) {
			return group.substring(group.indexOf(";") + 1).trim();
		}
		return group.trim();
	}

	/**
	 * Starts parsing the javascript file.
	 *
	 * @param obj An object that will be passed to the listener.
	 * @param classDescription A hashmap that will be filled with class description strings, className -&gt; description.
	 * @throws Exception On errors in the listener.
	 */
	public void parse(final Object obj, final HashMap<String, String> classDescription) throws Exception {
		this.obj = obj;
		String line = reader.readLine();
		while (line != null) {
			if ("CommonStrings".equals(currentClass)) {
				if (line.contains(":")) {
					final String key = line.substring(0, line.indexOf(":")).trim();
					String value = line.substring(line.indexOf(":") + 1).trim();
					if (value.endsWith(",")) {
						value = value.substring(0, value.length() - 1);
					}
					if (keyCached != null) {
						notifyString(false);
					}
					keyCached = key;
					valueCached = value;
				}
			}
			else if (line.contains("//$")) {
				classDescription.put(currentClass, line.substring(line.indexOf("//$") + 3).trim());
			}
			else if (line.contains("CS.")) {
				final Matcher matcher = csUsage.matcher(line);
				while (matcher.find()) {
					listener.commonStringUsage(matcher.group(1), currentClass, obj);
				}
			}
			Matcher matcher = extendPattern.matcher(line);
			if (matcher.matches()) {
				if (keyCached != null) {
					notifyString(true);
				}
				currentClass = cleanClassString(matcher.group(1));
			}
			matcher = extendPattern2.matcher(line);
			if (matcher.matches()) {
				if (keyCached != null) {
					notifyString(true);
				}
				currentClass = cleanClassString(matcher.group(1));
			}
			matcher = override.matcher(line);
			if (matcher.matches()) {
				if (keyCached != null) {
					notifyString(true);
				}
				currentClass = cleanClassString(matcher.group(1));
			}
			matcher = definePattern.matcher(line);
			if (matcher.matches()) {
				if (keyCached != null) {
					notifyString(true);
				}
				currentClass = cleanClassString(matcher.group(1));
			}
			line = reader.readLine();
		}
		if (keyCached != null) {
			notifyString(true);
		}
		if (latestNotifiedClass != null) {
			listener.changeClass(latestNotifiedClass, null, false, obj);
		}
	}

	/**
	 * Calls the listener about a string.
	 *
	 * @param last <code>true</code> if this is the last string to translate in a class.
	 */
	private void notifyString(final boolean last) throws Exception {
		if (!currentClass.equals(latestNotifiedClass)) {
			listener.changeClass(latestNotifiedClass, currentClass, latestNotifiedClass == null, obj);
			latestNotifiedClass = currentClass;
		}
		String value = valueCached.trim();
		value = value.substring(1, value.length() - 1);
		listener.addString(keyCached.trim(), value, currentClass, last, obj);
		keyCached = null;
	}
}
