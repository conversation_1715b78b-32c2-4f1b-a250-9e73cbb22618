package com.chilicoders.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.core.configuration.api.ConfigurationService;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Executes an external application and gets the result from it.
 */
public class Executor extends Thread {
	/**
	 * The log file.
	 */
	private static final Logger LOG = LogManager.getLogger(Executor.class);

	private static long HUNDRED_MS = 100;

	private static long MAX_EXECUTETIME = 240;

	/**
	 * The application to be executed followed by all parameters.
	 */
	private final String[] applicationAndParameters;
	private final String[] envVariables;

	/**
	 * Is this thread allowed to run?.
	 */
	private volatile boolean done = false;

	private final StringBuilder str = new StringBuilder();
	private final StringBuilder error = new StringBuilder();

	private final InputStream stdin;
	private final OutputStream stdout;

	private final String redirectOutput;

	private int exitValue = 0;

	private Process proc = null;

	private final ConfigurationService configurationService;

	/**
	 * Timeout to use for ten minutes.
	 */
	public static final int TEN_MINUTE_TIMEOUT = 10 * 60;

	/**
	 * Constructor.
	 *
	 * @param applicationAndParameters Application and parameters as an array.
	 * @param redirectOutput File to write output to.
	 * @param environmentVariable Environment variable.
	 * @param configurationService Configuration service.
	 */
	public Executor(final String[] applicationAndParameters, final String redirectOutput, final String environmentVariable, final ConfigurationService configurationService) {
		this(applicationAndParameters, redirectOutput, environmentVariable, null, null, configurationService);
	}

	/**
	 * Constructor.
	 *
	 * @param applicationAndParameters Application and parameters as an array.
	 * @param configurationService Configuration service.
	 */
	public Executor(final String[] applicationAndParameters, final ConfigurationService configurationService) {
		this(applicationAndParameters, null, null, null, null, configurationService);
	}

	/**
	 * Constructor.
	 *
	 * @param applicationAndParameters Application and parameters as an array.
	 * @param stdin Data to send to stdin.
	 * @param configurationService Configuration service.
	 */
	public Executor(final String[] applicationAndParameters, final InputStream stdin, final ConfigurationService configurationService) {
		this(applicationAndParameters, null, null, stdin, null, configurationService);
	}

	/**
	 * Constructor.
	 *
	 * @param applicationAndParameters Application and parameters as an array.
	 * @param redirectOutput File to write output to.
	 * @param configurationService Configuration service.
	 */
	public Executor(final String[] applicationAndParameters, final String redirectOutput, final ConfigurationService configurationService) {
		this(applicationAndParameters, redirectOutput, null, null, null, configurationService);
	}

	/**
	 * Constructor.
	 *
	 * @param applicationAndParameters Application and parameters as an array.
	 * @param redirectOutput File to write output to.
	 * @param environmentVariable Environment variable.
	 * @param stdin Data to send to stdin.
	 * @param stdout Stream to put stdout in.
	 * @param configurationService Configuration service.
	 */
	@SuppressFBWarnings("EI_EXPOSE_REP2")
	public Executor(final String[] applicationAndParameters, final String redirectOutput, final String environmentVariable, final InputStream stdin,
					final OutputStream stdout, final ConfigurationService configurationService) {
		super("Exec [ " + applicationAndParameters[0] + " ] ");
		this.applicationAndParameters = applicationAndParameters;
		this.redirectOutput = redirectOutput;
		this.envVariables = environmentVariable != null ? new String[] {environmentVariable} : null;
		this.stdin = stdin;
		this.stdout = stdout;
		this.configurationService = configurationService;
	}

	/**
	 * Used to execute the application.
	 */
	public void execute() {
		if (applicationAndParameters != null) {
			LOG.info("Executing: " + StringUtils.concatListValues(Arrays.asList(applicationAndParameters), " "));
		}
		if (configurationService.isKubernetesEnabled()) {
			return;
		}
		try {
			if (this.applicationAndParameters != null) {
				final ProcessBuilder pb = new ProcessBuilder(this.applicationAndParameters);

				if (redirectOutput != null) {
					pb.redirectOutput(new File(redirectOutput));
				}
				if (this.envVariables != null) {
					final Map<String, String> env = pb.environment();
					for (final String e : envVariables) {
						env.put(e.substring(0, e.indexOf("=")), e.substring(e.indexOf("=") + 1));
					}
				}
				proc = pb.start();

				final StreamGobbler errorGobbler = new StreamGobbler(proc.getErrorStream(), this.error);
				final StreamGobbler outputGobbler = new StreamGobbler(proc.getInputStream(), this.str);
				final Thread outputCopyThread = new Thread() {
					public void run() {
						try {
							IOUtils.copy(proc.getInputStream(), stdout);
						}
						catch (final IOException ex) {
							LOG.error(ex);
						}
					}
				};

				errorGobbler.start();
				(this.stdout != null ? outputCopyThread : outputGobbler).start();

				if (this.stdin != null) {
					IOUtils.copy(this.stdin, proc.getOutputStream());
					proc.getOutputStream().flush();
					proc.getOutputStream().close();
				}

				while (!errorGobbler.isStarted() && !outputGobbler.isStarted()) {
					java.lang.Thread.sleep(100);
				}

				this.exitValue = proc.waitFor();

				errorGobbler.stopThread();
				outputGobbler.stopThread();

				outputGobbler.join();
				errorGobbler.join();
				outputCopyThread.join();

				proc.getOutputStream().close();
				proc.destroy();
			}
		}
		catch (final RuntimeException | IOException | InterruptedException e) {
			LOG.error("Error : ", e);
			this.exitValue = -1;
		}
		synchronized (this) {
			this.done = true;
			notifyAll();
		}
	}

	/**
	 * Executes a bash script and returns the result.
	 *
	 * @param applicationAndParameters Application to execute and parameters as an array.
	 * @param redirectOutput File to write output to.
	 * @param environmentVariable Environment variable.
	 * @param configurationService Configuration service.
	 * @return Status and output from executed application.
	 */
	public static Pair<Integer, String> execute(final String[] applicationAndParameters, final String redirectOutput, final String environmentVariable,
												final ConfigurationService configurationService) {
		if (configurationService.isKubernetesEnabled()) {
			return Pair.of(-1, null);
		}
		final Executor exec = new Executor(applicationAndParameters, redirectOutput, environmentVariable, configurationService);
		exec.start();
		try {
			final long start = System.currentTimeMillis();
			while ((((System.currentTimeMillis() - start) / 1000) < 60L * MAX_EXECUTETIME) && !exec.done()) {
				synchronized (exec) {
					try {
						exec.wait(HUNDRED_MS);
					}
					catch (final InterruptedException ignored) {
						continue;
					}
				}
			}
			if (!exec.done()) {
				LOG.info("Shutting down application : " + StringUtils.join(applicationAndParameters, " "));
				exec.shutdown(false);
			}
			if (exec.getExitValue() < 2) {
				LOG.info("Exit code : " + exec.getExitValue());
				return Pair.of(exec.getExitValue(), exec.getOutput());
			}
			return Pair.of(exec.getExitValue(), exec.getError());
		}
		catch (final Exception e) {
			LOG.error("execute failed", e);
		}

		return Pair.of(-1, null);
	}

	/**
	 * Executes a bash script and returns the result.
	 *
	 * @param configurationService Configuration service
	 * @param applicationAndParameters Application to execute and parameters as an array.
	 * @return Status and output from executed application.
	 */
	public static Pair<Integer, String> execute(final ConfigurationService configurationService, final String... applicationAndParameters) {
		return execute(applicationAndParameters, null, null, configurationService);
	}

	/**
	 * Used to fetch the exit value of the process.
	 *
	 * @return Exit value
	 */
	public int getExitValue() {
		return this.exitValue;
	}

	/**
	 * Used to get the output of the application.
	 *
	 * @return Output from the executed application
	 */
	public String getOutput() {
		return this.str.toString();
	}

	public String getError() {
		return this.error.toString();
	}

	/**
	 * Used to see if we are done.
	 *
	 * @return true if done, otherwise false
	 */
	public boolean done() {
		return this.done;
	}

	/**
	 * Run method.
	 *
	 * @param wait If <code>true</code> waits for the process to die.
	 */
	public void shutdown(final boolean wait) {
		if (proc != null) {
			proc.destroy();
			if (wait) {
				synchronized (this) {
					while (!done) {
						try {
							wait();
						}
						catch (final InterruptedException e) {
							continue;
						}
					}
				}
			}
		}
	}

	@Override
	public void run() {
		execute();
	}

	/**
	 * Waits for the execute to finish.
	 *
	 * @param timeout Timeout in seconds.
	 * @return <code>true</code> upon success, <code>false</code> if timeout occured.
	 */
	public synchronized boolean waitForCompletion(final int timeout) {
		final long start = System.currentTimeMillis();
		while (!done && System.currentTimeMillis() - (timeout * 1000L) < start) {
			try {
				wait((timeout * 1000L) - (System.currentTimeMillis() - start));
			}
			catch (final InterruptedException e) {
				continue;
			}
		}

		if (!done) {
			shutdown(false);
			return false;
		}
		return true;
	}
}
