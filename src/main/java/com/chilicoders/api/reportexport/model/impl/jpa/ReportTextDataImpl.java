package com.chilicoders.api.reportexport.model.impl.jpa;

import java.io.Serializable;

import javax.persistence.Column;

import com.chilicoders.api.reportexport.model.ReportTextInterface;

import lombok.Getter;

@Getter
public class ReportTextDataImpl implements ReportTextInterface, Serializable {

	private static final long serialVersionUID = 6895494783334736664L;

	@Column(name = "xid")
	private long id;

	@Column
	private int reportLevel = -1;

	@Column(name = "ilocation")
	private int location;

	@Column(name = "vcheadline")
	private String headline;

	@Column(name = "txtext")
	private String text;

	public void setId(final Number id) {
		this.id = id.longValue();
	}
}
