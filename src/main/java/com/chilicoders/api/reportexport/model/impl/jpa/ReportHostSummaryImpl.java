package com.chilicoders.api.reportexport.model.impl.jpa;

import java.io.Serializable;

import javax.persistence.Column;

import com.chilicoders.api.reportexport.model.ReportHostSummaryInterface;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReportHostSummaryImpl implements ReportHostSummaryInterface, Serializable {

	private static final long serialVersionUID = 2476497123441896888L;

	@Column(name = "xid")
	private long id;

	@Column(name = "vctarget")
	private String target;

	@Column
	private String hostname;

	@Column
	private String netbios;

	@Column
	private long scannerId;

}