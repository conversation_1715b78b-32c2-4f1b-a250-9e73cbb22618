package com.chilicoders.api.reportexport.model.impl;

import java.util.ArrayList;
import java.util.List;

import com.chilicoders.core.reporting.api.model.WasFinding;
import com.chilicoders.api.reportexport.model.WasUrlFinding;
import com.google.common.collect.ImmutableList;

public class WasUrlFindingImpl implements WasUrlFinding {
	private final String host;

	private final long reportId;

	private final long vulnerabilityId;

	private final List<WasFinding> findings = new ArrayList<>();

	/**
	 * Create new WasUrlFindingImpl.
	 *
	 * @param host Host
	 * @param reportId Report id
	 * @param vulnerabilityId Vulnerability id
	 * @param finding Finding
	 */
	public WasUrlFindingImpl(final String host, final long reportId, final long vulnerabilityId, final WasFinding finding) {
		this.host = host;
		this.reportId = reportId;
		this.vulnerabilityId = vulnerabilityId;
		findings.add(finding);
	}

	@Override
	public String getHost() {
		return host;
	}

	@Override
	public long getReportId() {
		return reportId;
	}

	@Override
	public long getVulnerabilityId() {
		return vulnerabilityId;
	}

	@Override
	public List<WasFinding> getFindings() {
		return ImmutableList.copyOf(findings);
	}

	@Override
	public void addFinding(final WasFinding finding) {
		findings.add(finding);
	}
}
