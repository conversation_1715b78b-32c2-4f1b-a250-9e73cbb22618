package com.chilicoders.api.reportexport.model.impl;

import java.util.HashSet;
import java.util.Set;

import com.chilicoders.api.reportexport.model.FindingCountDataInterface;
import com.chilicoders.api.reportexport.model.FindingCountInfoInterface;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;

@Getter
@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
public class FindingCountInfo implements FindingCountInfoInterface {

	private String key;
	private long ports;
	private long cvssSum;
	private long lowrisk;
	private long mediumrisk;
	private long highrisk;
	private long lowaccepted;
	private long mediumaccepted;
	private long highaccepted;
	private long lowFp;
	private long mediumFp;
	private long highFp;
	private long information;
	private long newFindings;
	private long unchanged;
	private boolean notReachable;
	private Set<String> owasp = new HashSet<>();
	private Set<String> informationalOwasp = new HashSet<>();

	private long risks = 0;
	private long[] findings = new long[] {0, 0, 0, 0, 0};
	private long[] acceptedFindings = new long[] {0, 0, 0, 0, 0};
	private long[] fpFindings = new long[] {0, 0, 0, 0, 0};
	private long[] delta = new long[] {0, 0, 0, 0, 0, 0};
	private int[] ageFindings = new int[5];
	private int[] remediation = new int[5];
	private Set<String> targetList = new HashSet<>();
	private Set<String> reports = new HashSet<>();
	private long count = 0;

	public FindingCountInfo() {
	}

	/**
	 * Creates an instance based in the db data.
	 *
	 * @param findingCountData the data from the db
	 */
	public FindingCountInfo(final FindingCountDataInterface findingCountData) {
		this.key = findingCountData.getKey();
		this.ports = findingCountData.getPorts();
		this.cvssSum = findingCountData.getCvssSum();
		this.lowrisk = findingCountData.getLowRisk();
		this.mediumrisk = findingCountData.getMediumRisk();
		this.highrisk = findingCountData.getHighRisk();
		this.lowaccepted = findingCountData.getLowAccepted();
		this.mediumaccepted = findingCountData.getMediumAccepted();
		this.highaccepted = findingCountData.getHighAccepted();
		this.lowFp = findingCountData.getLowFp();
		this.mediumFp = findingCountData.getMediumFp();
		this.highFp = findingCountData.getHighFp();
		this.information = findingCountData.getInformation();
		this.newFindings = findingCountData.getNewFindings();
		this.unchanged = findingCountData.getUnchanged();
		this.notReachable = findingCountData.isNotReachable();
		this.owasp = findingCountData.getOwasp();
		this.informationalOwasp = findingCountData.getInformationalOwasp();
		computeValues();
	}

	/**
	 * Computes the necessary values.
	 */
	private void computeValues() {
		risks = lowrisk + mediumrisk + highrisk;
		acceptedFindings = new long[] {0, lowaccepted, mediumaccepted, 0, highaccepted};
		fpFindings = new long[] {0, lowFp, mediumFp, 0, highFp};
		findings = new long[] {information, lowrisk, mediumrisk, 0, highrisk};
		delta = new long[] {newFindings, 0, unchanged, 0, 0, 0};
	}

	@Override
	public void setCount(final long count) {
		this.count = count;
	}

	@Override
	public void setPorts(final long ports) {
		this.ports = ports;
	}

	@Override
	public void setRisks(final long risks) {
		this.risks = risks;
	}

	@Override
	public void setCvssSum(final long cvssSum) {
		this.cvssSum = cvssSum;
	}
}
