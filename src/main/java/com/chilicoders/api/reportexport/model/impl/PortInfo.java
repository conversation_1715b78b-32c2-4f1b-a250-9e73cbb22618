package com.chilicoders.api.reportexport.model.impl;

import java.util.HashMap;
import java.util.Map;

import com.chilicoders.api.reportexport.model.PortInfoDataInterface;
import com.chilicoders.api.reportexport.model.PortInfoInterface;
import com.chilicoders.api.reportexport.model.ReportHostInterface;
import com.chilicoders.api.reportexport.model.impl.jpa.ReportHostImpl;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PortInfo implements PortInfoInterface {

	private String service = "";
	private long count = -1;
	private int port = -1;
	private String protocol = "";
	private String deltaStatus = "";
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private ReportHostInterface host = new ReportHostImpl();
	@SuppressFBWarnings(value = "EI_EXPOSE_REP", justification = "Intentionally exposing internal representation")
	private Map<String, String> data = new HashMap<>();

	public PortInfo() {
	}

	public PortInfo(final PortInfoDataInterface portData) {
		this.service = portData.getService();
		this.count = portData.getCount();
	}
}
