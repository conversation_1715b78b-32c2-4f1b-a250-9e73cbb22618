package com.chilicoders.api;

import java.util.Properties;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.core.configuration.api.ConfigurationService;
import com.chilicoders.core.configuration.common.ConfigKeys;
import com.chilicoders.util.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

public class KafkaMessageSender {
	private static final Logger LOG = LogManager.getLogger(KafkaMessageSender.class);

	private KafkaProducer<String, String> producer;
	private final ObjectMapper objectMapper = new ObjectMapper();
	private final String topic;

	/**
	 * Creates a kafka message sender.
	 *
	 * @param configService Configuration service.
	 * @param topic Topic to send to.
	 * @param maxMessageSize Maximum message size.
	 */
	public KafkaMessageSender(final ConfigurationService configService, final String topic, final Integer maxMessageSize) {
		LOG.debug(StringUtils.isEmpty(topic) ? "Message sender not created due to undefined topic" : "Message sender created with topic set to " + topic);
		this.topic = topic;
		if (!StringUtils.isEmpty(this.topic)) {
			createProducer(configService, maxMessageSize);
			objectMapper.registerModule(new JavaTimeModule());
		}
	}

	/**
	 * Create a kafka producer.
	 *
	 * @param configService Configuration service.
	 * @param maxMessageSize Maximum message size.
	 */
	public void createProducer(final ConfigurationService configService, final Integer maxMessageSize) {
		if (this.producer != null) {
			this.producer.close();
		}
		final Properties props = new Properties();
		if (!configService.getProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap).contains("localhost") &&
				!configService.getProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap).contains("127.0.0.1") &&
				!configService.getProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap).contains("host.docker.internal") &&
				!configService.getProperty(ConfigKeys.ConfigurationBooleanKey.kafka_disable_authentication)) {
			props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
			props.put(SaslConfigs.SASL_MECHANISM, "AWS_MSK_IAM");
			props.put(SaslConfigs.SASL_JAAS_CONFIG, "software.amazon.msk.auth.iam.IAMLoginModule required awsDebugCreds=true;");
			props.put(SaslConfigs.SASL_CLIENT_CALLBACK_HANDLER_CLASS, "software.amazon.msk.auth.iam.IAMClientCallbackHandler");
		}
		props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, configService.getProperty(ConfigKeys.ConfigurationKey.kafka_bootstrap));
		if (maxMessageSize != null) {
			props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10485880);
		}
		this.producer = new KafkaProducer<>(props, new StringSerializer(), new StringSerializer());
	}

	/**
	 * Send an object to the topic.
	 *
	 * @param object Object to be sent.
	 */
	public void send(final Object object) {
		if (StringUtils.isEmpty(this.topic)) {
			LOG.debug("The topic is not defined so the event cannot be published.");
			return;
		}
		LOG.debug("The topic is set to : " + this.topic);
		try {
			producer.send(new ProducerRecord<>(topic, objectMapper.writeValueAsString(object)), (metadata, exception) -> {
				if (exception != null) {
					LOG.debug("Error sending message to event service: " + exception.getMessage(), exception);
				}
				else {
					LOG.debug("Message delivered to topic: {}, partition: {}, offset: {}", metadata.topic(), metadata.partition(), metadata.offset());
				}
			});
		}
		catch (final JsonProcessingException e) {
			LOG.error("Error sending message to event service: " + e.getMessage(), e);
		}
	}
}
