package com.chilicoders.cache;

/**
 * Exception class to be used for db access issues, non-related to SQL.
 */
public class DbAccessException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	private final Reason reason;

	public DbAccessException(final Reason reason) {
		super(reason.getMessage());
		this.reason = reason;
	}

	public Reason getReason() {
		return reason;
	}

	/**
	 * Enum containing the possible reasons for throwing a DbAccessException.
	 */
	public enum Reason {
		NO_CONNECTION_AVAILABLE("No database connection is available.");

		private final String message;

		private Reason(final String message) {
			this.message = message;
		}

		public String getMessage() {
			return message;
		}
	}
}
