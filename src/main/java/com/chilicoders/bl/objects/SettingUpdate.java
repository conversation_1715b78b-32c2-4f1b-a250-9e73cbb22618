package com.chilicoders.bl.objects;

import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;

public class SettingUpdate {
	private final String uiName;
	@SuppressWarnings("rawtypes")
	private final Enum settingName;
	private final int defaultInt;
	private final String defaultString;
	private final boolean defaultBoolean;
	private final String auditString;

	/**
	 * Create new SettingUpdate object.
	 *
	 * @param ui Field name of ui
	 * @param setting Settings key
	 * @param defaultString Default value
	 * @param auditString Text string for audit
	 */
	public SettingUpdate(final String ui, final ConfigurationKey setting, final String defaultString, final String auditString) {
		this.uiName = ui;
		this.settingName = setting;
		this.defaultString = defaultString;
		this.defaultInt = -1;
		this.defaultBoolean = false;
		this.auditString = auditString;
	}

	/**
	 * Create new SettingUpdate object.
	 *
	 * @param ui Field name of ui
	 * @param setting Settings key
	 * @param defaultValue Default value
	 * @param auditString Text string for audit
	 */
	public SettingUpdate(final String ui, final ConfigurationIntKey setting, final int defaultValue, final String auditString) {
		this.uiName = ui;
		this.settingName = setting;
		this.defaultInt = defaultValue;
		this.defaultString = "";
		this.defaultBoolean = false;
		this.auditString = auditString;
	}

	/**
	 * Create new SettingUpdate object.
	 *
	 * @param ui Field name of ui
	 * @param setting Settings key
	 * @param defaultValue Default value
	 * @param auditString Text string for audit
	 */
	public SettingUpdate(final String ui, final ConfigurationBooleanKey setting, final boolean defaultValue, final String auditString) {
		this.uiName = ui;
		this.settingName = setting;
		this.defaultBoolean = defaultValue;
		this.defaultInt = -1;
		this.defaultString = "";
		this.auditString = auditString;
	}

	public String getUiName() {
		return uiName;
	}

	@SuppressWarnings("rawtypes")
	public Enum getSettingName() {
		return settingName;
	}

	public int getDefaultInt() {
		return defaultInt;
	}

	public String getDefaultString() {
		return defaultString;
	}

	public boolean getDefaultBoolean() {
		return defaultBoolean;
	}

	public String getAuditString() {
		return auditString;
	}
}
