package com.chilicoders.bl.objects;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.app.ScanApp;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.events.model.EventType;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.Audit;
import com.chilicoders.db.objects.Splunk;
import com.chilicoders.util.Configuration;

/**
 * Handles the post processing of target removal.
 * - Write audit logs
 * - Remove xlinkscheduleobjects
 * - Update schedule object count
 * The thread will use advisory locks to make sure that only one instance will run at any given moment.
 */
public class TargetRemoveThread extends Thread {
	/**
	 * Lock id for psql.
	 */
	private static final int ADVISORY_LOCK_ID = 1000;

	private static final Logger LOG = LogManager.getLogger(TargetRemoveThread.class);

	/**
	 * @see java.lang.Thread#run()
	 */
	@Override
	public void run() {
		Connection conn = null;
		final DbAccess dba = DbAccess.getInstance();
		try {
			conn = dba.getConnection();
			DbObject.execute(conn, "SELECT pg_advisory_lock(?)", ADVISORY_LOCK_ID);
			DbObject.execute(conn, "CREATE TEMPORARY TABLE deletexids ON COMMIT DROP AS SELECT xid FROM deletetargets WHERE deleted IS NULL");
			// Delete xlinkscheduleobjects
			DbObject.executeUpdate(conn,
					"DELETE FROM xlinkscheduleobject xlink WHERE COALESCE(endipaddress = ipaddress, true) AND itype = 1 AND ((SELECT userid FROM schedules s WHERE s.id = xlink.xid), ipaddress, hostnameid) "
							+ "IN (SELECT xuserxid, ipaddress, hostnameid FROM deletetargets WHERE xid IN (SELECT xid FROM deletexids))");

			final String sql =
					"INSERT INTO taudits (xid, xxid, xuserxid, xsubuserxid, xvcapp, imode, deletename, txcustom, xtime, xconsultancyxid, pci) SELECT nextval('taudits_seq'), xid, xuserxid, subuserxid, 'tUserdataS', 2, "
							+ "(CASE WHEN ipaddress IS NULL THEN CASE WHEN aws_instance_id IS NULL THEN CASE WHEN agentid IS NULL THEN CASE WHEN snsysid IS NULL THEN hostname ELSE snname END ELSE agentid END ELSE aws_instance_id END ELSE HOST(ipaddress) END), "
							+ "deletenote, current_timestamp, consultancyxid, pci FROM deletetargets WHERE xid IN (SELECT xid FROM deletexids) RETURNING xid, xuserxid";
			final List<Audit> audits = Audit.fetchObjects(Audit.class, conn, sql);
			final HashMap<Long, StringBuilder> auditIds = new HashMap<>();
			for (final Audit audit : audits) {
				if (auditIds.containsKey(audit.getUserId())) {
					final StringBuilder sb = auditIds.get(audit.getUserId());
					sb.append("," + audit.getId());
				}
				else {
					auditIds.put(audit.getUserId(), new StringBuilder("" + audit.getId()));
				}
			}

			final Iterator<Entry<Long, StringBuilder>> it = auditIds.entrySet().iterator();
			while (it.hasNext()) {
				final Entry<Long, StringBuilder> entry = it.next();
				final long userId = entry.getKey();
				if (Configuration.getProperty(ConfigurationBooleanKey.hiab_syslog_audit)) {
					ScanApp.getInstance().sendAuditLog(conn, userId, -1, entry.getValue().toString(), EventType.SYSLOG);
				}
				final Splunk splunk = Splunk.getById(Splunk.class, conn, Access.ADMIN, userId);
				if (splunk != null && splunk.isSplunkEnabled() && splunk.isSplunkAudit()) {
					ScanApp.getInstance().sendAuditLog(conn, userId, -1, entry.getValue().toString(), EventType.SPLUNK);
				}
			}

			DbObject.executeUpdate(conn, "UPDATE deletetargets SET deleted = now() WHERE xid IN (SELECT xid FROM deletexids)");

			conn.commit();

			DbObject.execute(conn, "SELECT pg_advisory_unlock_all()");
		}
		catch (final SQLException e) {
			LOG.error("Error running targetremovethread", e);
			try {
				if (conn != null) {
					conn.rollback();
					DbObject.execute(conn, "SELECT pg_advisory_unlock_all()");
				}
			}
			catch (final SQLException e1) {
				LOG.error("Error releasing lock.", e1);
			}
		}
		finally {
			dba.freeConnection(conn);
		}
	}
}
