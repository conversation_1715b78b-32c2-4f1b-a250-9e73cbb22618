package com.chilicoders.bl;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import com.chilicoders.app.WebRequest;
import com.chilicoders.bl.BusinessObject.DbObjectSendConfiguration.AfterDataLoadCallback;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.reporting.api.model.ReportTemplateScanType;
import com.chilicoders.core.user.api.Permission;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.ReportTemplate;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.util.HttpRequestWrapper;
import com.chilicoders.util.ParamValidator;
import com.chilicoders.util.ParamValidator.Method;
import com.chilicoders.util.StringUtils;

/**
 * This class is responsible for dealing with report templates.
 */
public class ReportTemplateBusiness extends BusinessObject {

	/**
	 * Rowset tag for XML.
	 */
	private static final String ROWSET_TAG = "REPORTTEMPLATES";
	/**
	 * Row tag for XML.
	 */
	private static final String ROW_TAG = "TEMPLATE";

	private static ParamValidator[] validators = {
			ParamValidator.digitOnly("XID", -1, -1, Method.Delete),
			ParamValidator.digitOnly("SCANTYPE"),
			ParamValidator.printable("NAME", 64),
			ParamValidator.booleanValue("ISPUBLIC"),
			ParamValidator.booleanValue("ISDEFAULT"),
			ParamValidator.printable("SERVERFILTER"),
			ParamValidator.printable("STATE"),
			ParamValidator.printable("TARGETS"),
			ParamValidator.booleanValue("PUBLIC"),
			ParamValidator.digitOnly("SCANLOGXID"),
			ParamValidator.digitOnly("SCHEDULEXID"),
			ParamValidator.printable("COMMENT"),
			ParamValidator.booleanValue("DEFAULT"),
			ParamValidator.digitList("TARGETGROUPS"),
			ParamValidator.digitOnly("POLICY")
	};

	public ReportTemplateBusiness() {
		super();
	}

	public ReportTemplateBusiness(final UserDetails user) {
		super(user);
	}

	/**
	 * Check access for user.
	 *
	 * @param request The HTTP servlet request
	 * @param update true if update actions, otherwise false
	 * @return true if the user has access, otherwise false
	 */
	private boolean checkAllowedAccess(final HttpRequestWrapper request, final boolean update) {
		final int type = StringUtils.getIntValue(request.getParameter("SCANTYPE"), -1);
		if ((type < 0 || type > 4) ||
				(type == 0 && !allow(Permission.REPORTING, !update)) ||
				(type == 1 && !allow(Permission.PCI_REPORTING, !update)) ||
				(type == 2 && !allow(Permission.WAS_REPORTING, !update)) ||
				(type == 3 && !allow(Permission.COMPLIANCE, !update)) ||
				(type == 4 && !allow(Permission.SWAT, !update))) {
			return false;
		}
		return true;
	}

	/**
	 * Adds a where clause that enforces the subuser rights for report templates.
	 *
	 * @param filter The buffer to add it to.
	 * @param arrParams Parameters to add to the query.
	 */
	private void addSubuserClause(final StringBuilder filter, final List<Object> arrParams) {
		if (isSubUser()) {
			StringUtils.concatenateFilters(filter, "(xsubuserxid=? OR ispublic=1)");
			arrParams.add(getSubUserId());
		}
		else {
			StringUtils.concatenateFilters(filter, "(xsubuserxid IS NULL OR ispublic=1)");
		}
	}

	/**
	 * List report templates.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "REPORTTEMPLATEDATA")
	private void sendReportTemplates(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, validators);

		if (!checkAllowedAccess(request, false)) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
			return;
		}

		final StringBuilder filter = new StringBuilder();
		final List<Object> params = new ArrayList<>();
		final long id = request.getLongParameter("XID", -1);
		if (id > 0) {
			filter.append("xid=?");
			params.add(id);
		}
		else {
			filter.append("scantype=?");
			params.add(request.getLongParameter("SCANTYPE", -1));
		}
		addSubuserClause(filter, params);

		final long defaultId = getNewLoggedOnUser().getDefaultReportTemplate(ReportTemplateScanType.getInstance(request.getIntParameter("SCANTYPE", -1)));
		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.ADMIN);
		config.setAfterLoadCallback(new AfterDataLoadCallback() {
			@Override
			public void dataLoaded(final List<? extends XmlAble> objects) {
				for (final XmlAble el : objects) {
					if (((ReportTemplate) el).getId() == defaultId) {
						((ReportTemplate) el).setDefault();
					}
				}
			}
		});
		sendInformation(ReportTemplate.class, conn, request, response, ROWSET_TAG, ROW_TAG, config, null, null, filter.toString(), params);
	}

	/**
	 * Fetches a report template object based on xid.
	 *
	 * @param conn Database connection
	 * @param xid The xid of the object to fetch.
	 * @param userId The user xid that owns the schedule.
	 * @return A report template.
	 */
	public ReportTemplate getReportTemplate(final Connection conn, final long xid, final long userId) throws SQLException {
		final StringBuilder filter = new StringBuilder("xid=? AND xuserxid=?");
		final List<Object> params = new ArrayList<>();
		params.add(xid);
		params.add(userId);

		return ReportTemplate.get(ReportTemplate.class, conn, DbHelper.getSelect(ReportTemplate.class, Access.ADMIN, filter.toString()), params);
	}

	/**
	 * Update report template.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "UPDATEREPORTTEMPLATEDATA")
	private void updateReportTemplate(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, validators);

		if (!checkAllowedAccess(request, true)) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
			return;
		}

		final Set<String> missingColumns = new HashSet<>();
		final ReportTemplate template = ReportTemplate.fromRequest(ReportTemplate.class, request, missingColumns);
		template.setUserId(getMainUserId());
		missingColumns.remove("userid");
		if (template.getId() == -1) {
			template.setSubUserId(isSubUser() ? getSubUserId() : null);
			missingColumns.remove("subuserid");
		}

		final ReportTemplateScanType scanType = template.getScanType();
		if (scanType == ReportTemplateScanType.Compliance && request.getParameter("POLICY") != null) {
			template.setCompliancePolicy(request.getLongParameter("POLICY", -1));
			missingColumns.remove("compliancepolicy");
		}

		final long id = template.save(conn, Access.ADMIN, null, missingColumns.toArray(new String[0]));
		if (template.isDefault() || id == getNewLoggedOnUser().getDefaultReportTemplate(scanType)) {
			final String sql = "UPDATE " + (isSubUser() ? "tsubusers" : "tusers") + " SET " + scanType.getDefaultReportTemplate() + " = ? WHERE xid = ?";
			DbObject.executeUpdate(conn, sql, template.isDefault() ? id : null, isSubUser() ? getSubUserId() : getMainUserId());
			getNewLoggedOnUser().setDefaultReportTemplate(scanType, template.isDefault() ? id : 0);
		}

		audit(conn, id, AppName.REPORTTEMPLATE, template.wasUpdate() ? AuditMode.UPDATE : AuditMode.ADD, null, null, scanType == ReportTemplateScanType.PCI);

		conn.commit();
		sendSuccessMessage(request, response, id);
	}

	/**
	 * Remove report template.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "REMOVEREPORTTEMPLATEDATA")
	private void removeReportTemplate(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, Method.Delete, validators);

		if (!checkAllowedAccess(request, true)) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
			return;
		}

		final long id = request.getLongParameter("XID", -1);
		final ReportTemplate template = ReportTemplate.getById(ReportTemplate.class, conn, id);
		final StringBuilder sql = new StringBuilder("DELETE FROM treporttemplates WHERE xuserxid=? AND xid=?");
		final List<Object> params = new ArrayList<>();
		params.add(getMainUserId());
		params.add(id);
		addSubuserClause(sql, params);

		final long rowCount = DbObject.executeUpdate(conn, sql.toString(), params);
		if (rowCount > 0) {
			audit(conn, id, AppName.REPORTTEMPLATE, AuditMode.DELETE, template.getName(), null, template.getScanType() == ReportTemplateScanType.PCI);
		}
		conn.commit();
		sendSuccessMessage(request, response);
	}
}
