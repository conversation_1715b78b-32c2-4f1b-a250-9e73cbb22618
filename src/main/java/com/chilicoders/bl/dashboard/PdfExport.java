package com.chilicoders.bl.dashboard;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

import javax.imageio.ImageIO;

import org.apache.catalina.connector.ClientAbortException;

import com.chilicoders.app.XMLAPI;
import com.chilicoders.bl.DashboardBusiness;
import com.chilicoders.bl.DashboardBusiness.AcceptedFindings;
import com.chilicoders.bl.DashboardBusiness.CertificateInfo;
import com.chilicoders.bl.DashboardBusiness.Countable;
import com.chilicoders.bl.DashboardBusiness.DashTrend;
import com.chilicoders.bl.DashboardBusiness.DashboardModule;
import com.chilicoders.bl.DashboardBusiness.ExploitableTargets;
import com.chilicoders.bl.DashboardBusiness.RemediationCounts;
import com.chilicoders.bl.DashboardBusiness.RiskSummaryCounts;
import com.chilicoders.bl.DashboardBusiness.TopAgingFindings;
import com.chilicoders.bl.DashboardBusiness.TopApplicationCounts;
import com.chilicoders.bl.DashboardBusiness.TopPortCounts;
import com.chilicoders.bl.DashboardBusiness.TopTargetCounts;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationIntKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.db.objects.FindingImpl;
import com.chilicoders.db.objects.TopSolution;
import com.chilicoders.db.objects.TopVulnerableFinding;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.ZipUtils;
import com.chilicoders.util.ZipUtils.ZipFile;
import com.chilicoders.util.pdf.ChartConfig;
import com.chilicoders.util.pdf.ChartFactory;
import com.chilicoders.util.pdf.PdfCreator;
import com.chilicoders.util.pdf.PdfCreator.PdfCell;
import com.chilicoders.util.pdf.PdfCreator.PdfChapter;
import com.chilicoders.util.pdf.PdfCreator.PdfChunk;
import com.chilicoders.util.pdf.PdfCreator.PdfDottedLineSeparator;
import com.chilicoders.util.pdf.PdfCreator.PdfException;
import com.chilicoders.util.pdf.PdfCreator.PdfHeaderFooter;
import com.chilicoders.util.pdf.PdfCreator.PdfImage;
import com.chilicoders.util.pdf.PdfCreator.PdfParagraph;
import com.chilicoders.util.pdf.PdfCreator.PdfSection;
import com.chilicoders.util.pdf.PdfCreator.PdfTable;

public class PdfExport extends BaseExport {

	private final PdfCreator pdf = new PdfCreator();

	/**
	 * The pdf chapter object.
	 */
	private PdfChapter chapter;

	/**
	 * Password to use for PDF.
	 */
	private final String password;

	private final ByteArrayOutputStream out = new ByteArrayOutputStream();

	private Color[] colorScale;

	/**
	 * Creates a new pdf export.
	 *
	 * @param password Password to use for PDF.
	 */
	public PdfExport(final String password) {
		this.password = password;
	}

	@Override
	protected void addFirstPage() {
		try {
			chapter.setTitle(new PdfParagraph(getString("_DASHBOARD"), null));
			chapter.add(new PdfParagraph(""));

			// Logo
			final PdfImage image;
			final byte[] logo = getLogo();
			if (logo != null) {
				image = PdfCreator.getImage(logo);
			}
			else {
				image = PdfCreator.getImageInstance(
						"file:" + Configuration.getProperty(ConfigurationKey.report_pdflogo, getWhiteLabelUser(), Configuration.getProperty(ConfigurationKey.report_pdflogo)));
			}

			final int height =
					Configuration.getProperty(ConfigurationIntKey.pdf_logo_height, getWhiteLabelUser(), Configuration.getProperty(ConfigurationIntKey.pdf_logo_height));
			if (image != null) {
				image.scaleToFit(
						Configuration.getProperty(ConfigurationIntKey.pdf_logo_width, getWhiteLabelUser(), Configuration.getProperty(ConfigurationIntKey.pdf_logo_width)),
						height);
			}

			PdfTable table = PdfCreator.getTable(null, 1, getRunDirection());

			PdfCell cell = PdfCreator.getCell();
			cell.setFixedHeight(50);
			table.addCell(cell);
			table.addRow();

			cell = PdfCreator.getCell(image);
			PdfCreator.setCellPadding(cell, 0, 0, 0, 0);
			cell.setFixedHeight(height + 5);
			cell.setHorizontalAlignment(PdfCreator.ALIGN_CENTER);
			cell.setVerticalAlignment(PdfCreator.ALIGN_BOTTOM);
			table.addCell(cell);
			table.addRow();

			chapter.add(table);

			// Title
			PdfParagraph paragraph = new PdfParagraph(pdf.getFont("title_cover").process(getString("_DASHBOARD")));
			paragraph.setAlignment(PdfCreator.ALIGN_CENTER);
			paragraph.setSpacingBefore(15);
			chapter.add(paragraph);

			final String gmtoffset = DateUtils.getTimezone(this.user.getGmtOffset());
			paragraph = new PdfParagraph(pdf.getFont("text")
					.process(DateUtils.formatDateTimezone(new Date(),
							DateUtils.getDateFormat(this.user.getDateFormat()) + " " + DateUtils.getTimeFormat(this.user.getTimeFormat()), gmtoffset)));
			paragraph.setSpacingBefore(5);
			paragraph.setSpacingAfter(5);
			paragraph.setAlignment(PdfCreator.ALIGN_CENTER);
			chapter.add(paragraph);

			// Sections

			table = PdfCreator.getTable(null, 1, getRunDirection());
			table.setSpacingBefore(20);
			table.setIndentationLeft(75);
			table.setIndentationRight(75);

			final List<DashboardModule> modules = getModules();

			for (final DashboardModule module : modules) {
				paragraph = new PdfParagraph();
				paragraph.add(pdf.getFont("toc").process(getModuleName(module.name, module.group)));
				PdfCreator.setGoto(paragraph, module.getKey());
				paragraph.add(new PdfChunk(new PdfDottedLineSeparator()));

				cell = PdfCreator.getCell();
				cell.setPhrase(paragraph);
				table.addCell(cell);
				table.addRow();
			}

			chapter.add(table);

			chapter.newPage();
		}
		catch (final RuntimeException | SQLException | IOException | PdfException e) {
			LOG.error("Error adding first page", e);
		}
	}

	@Override
	public void export() {
		try {
			final List<DashboardModule> modules = getModules();

			for (final DashboardModule module : modules) {
				final String name = module.name;
				final String title = getModuleName(name, module.group);

				final PdfSection section = chapter.addSection(null, title);

				final PdfParagraph paragraph = new PdfParagraph(pdf.getFont("title").process(title));
				PdfCreator.setDestination(paragraph, module.getKey());

				final float[] widths = new float[] {375, 200};

				final PdfTable table = PdfCreator.getTable(widths, -1, getRunDirection());
				table.setKeepTogether(true);
				table.setSpacingAfter(20);
				table.setIndentationLeft(15);
				table.setIndentationRight(15);

				final PdfCell cell = PdfCreator.getCell();
				cell.setColspan(2);
				cell.setPaddingBottom(10);
				cell.setPhrase(paragraph);
				table.addCell(cell);
				table.addRow();

				if ("dashboard_topports".equals(name)) {
					addTopPorts(module, table);
				}
				else if ("dashboard_toptargets".equals(name)) {
					addTopTargets(module, table);
				}
				else if ("dashboard_topapplications".equals(name)) {
					addTopApplications(module, table);
				}
				else if ("dashboard_topsolutions".equals(name)) {
					addTopSolutions(module, table);
				}
				else if ("dashboard_risksummary".equals(name)) {
					addRiskSummary(module, table);
				}
				else if ("dashboard_latestfindings".equals(name)) {
					addLatestFindings(module, table);
				}
				else if ("dashboard_exploitable".equals(name)) {
					addExploitable(module, table);
				}
				else if ("dashboard_certificates".equals(name)) {
					addCertificates(module, table);
				}
				else if ("dashboard_topagingfindings".equals(name)) {
					addTopAgingFindings(module, table);
				}
				else if ("dashboard_topvulnerablefindings".equals(name)) {
					addTopVulnerableFindings(module, table);
				}
				else if ("dashboard_acceptedfindings".equals(name)) {
					addAcceptedFindings(module, table);
				}
				else if ("dashboard_trend".equals(name)) {
					addTrend(module, table);
				}
				else {
					LOG.info("Module not found: " + name);
				}

				section.add(table);
			}
		}
		catch (final RuntimeException | SQLException | PdfException | IOException e) {
			LOG.error("Error exporting dashboard", e);
		}
	}

	/**
	 * Add top solutions.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTopSolutions(final DashboardModule module, final PdfTable mainTable) throws SQLException, PdfException {
		final PdfTable table = PdfCreator.getTable(new float[] {15, 300, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_PRODUCT")));

		pdf.addTableCells(table, new Object[] {cell, new PdfParagraph(pdf.getFont("header").process(getString("_COUNT")))}, -1, 5, null);

		final List<TopSolution> doc = getTopSolutions(getConnection(), module.count, module.group, module.low, module.medium, module.high);

		final Color[] colors = getColors(module.count);

		for (int i = 0; i < doc.size(); i++) {
			final PdfImage image = pdf.getLineImage(colors[i]);
			image.setSpacingBefore(3);
			addTableCells(table, new Object[] {image, StringUtils.setEmpty(doc.get(i).getProduct(), ""), doc.get(i).count});
		}

		addDataToTable(mainTable, table, doc, doc.size(), module.count);
	}

	/**
	 * Add top vulnerable findings.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTopVulnerableFindings(final DashboardModule module, final PdfTable mainTable) throws SQLException, PdfException {
		final DashboardBusiness db = new DashboardBusiness(this.user);
		final List<TopVulnerableFinding> findings = db.getTopVulnerableFindings(getConnection(), module.group, module.low, module.medium, module.high, module.count, -1);

		final PdfTable table = PdfCreator.getTable(new float[] {15, 160, 100, 100}, -1, getRunDirection());
		table.setTotalWidth(600);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_FINDING")));

		pdf.addTableCells(table,
				new Object[] {cell, new PdfParagraph(pdf.getFont("header").process(getString("_CVE"))), new PdfParagraph(pdf.getFont("header").process(getString("_RISK")))},
				-1, 5, null);

		final Color[] colors = getColors(Math.min(findings.size(), module.count));

		for (int i = 0; i < findings.size(); i++) {
			final FindingImpl finding = findings.get(i);
			final PdfImage image = pdf.getLineImage(colors[i]);
			image.setSpacingBefore(3);
			addTableCells(table,
					new Object[] {image, StringUtils.setEmpty(finding.getName(), ""), StringUtils.setEmpty(finding.getCve(), ""), getRiskLevel(finding.getRiskLevel())});
		}

		final PdfCell c = PdfCreator.getCell();
		c.setColspan(2);
		c.addElement(table);
		mainTable.addCell(c);
		mainTable.addRow();
	}

	/**
	 * Add top aging findings.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTopAgingFindings(final DashboardModule module, final PdfTable mainTable) throws SQLException, PdfException {
		final DashboardBusiness db = new DashboardBusiness(this.user);
		final List<TopAgingFindings> findings = db.getTopAgingFindings(getConnection(), module.group, module.low, module.medium, module.high, module.count, -1);

		final PdfTable table = PdfCreator.getTable(new float[] {15, 120, 120, 60, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_FINDING")));

		pdf.addTableCells(table, new Object[] {
				cell, new PdfParagraph(pdf.getFont("header").process(getString("_CVE"))), new PdfParagraph(pdf.getFont("header").process(getString("_COUNT"))),
				new PdfParagraph(pdf.getFont("header").process(getString("_TARGETS")))
		}, -1, 5, null);

		final Color[] colors = getColors(Math.min(findings.size(), module.count));

		for (int i = 0; i < findings.size(); i++) {
			final TopAgingFindings finding = findings.get(i);
			final PdfImage image = pdf.getLineImage(colors[i]);
			image.setSpacingBefore(3);
			addTableCells(table,
					new Object[] {image, StringUtils.setEmpty(finding.vcname, ""), StringUtils.setEmpty(finding.cve, ""), "" + finding.count, "" + finding.targets});
		}

		addDataToTable(mainTable, table, findings, findings.size(), module.count);
	}

	/**
	 * Add top ports.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTopPorts(final DashboardModule module, final PdfTable mainTable) throws PdfException {
		final PdfTable table = PdfCreator.getTable(new float[] {15, 300, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_PORT")));

		pdf.addTableCells(table, new Object[] {cell, new PdfParagraph(pdf.getFont("header").process(getString("_COUNT")))}, -1, 5, null);

		final List<TopPortCounts> doc = getTopPorts(module.count, module.group, module.low, module.medium, module.high);

		final Color[] colors = getColors(module.count);

		int count = 0;
		for (final TopPortCounts r : doc) {
			final PdfImage image = pdf.getLineImage(colors[count]);
			image.setSpacingBefore(3);
			addTableCells(table, new Object[] {image, r.port, r.count});
			count++;
		}

		addDataToTable(mainTable, table, doc, count, module.count);
	}

	private void addDataToTable(final PdfTable mainTable, final PdfTable table, final List<? extends Countable> doc, final int count, final int limit) {
		addDataToTable(mainTable, table, doc, count, limit, null, null);
	}

	/**
	 * Add data to table.
	 *
	 * @param mainTable Main table.
	 * @param table Table to add to.
	 * @param doc DOcument for generating pie chart.
	 * @param count If zero text, otherwise a graph.
	 * @param limit Limit of number of entries for pie chart.
	 * @param image UIMage to add instead of pie chart.
	 * @param colors Colors to use in pie chart.
	 */
	private void addDataToTable(final PdfTable mainTable, final PdfTable table, final List<? extends Countable> doc, final int count, final int limit, final PdfImage image,
								final Color[] colors) {
		if (count == 0) {
			final PdfCell cell = PdfCreator.getCell();
			cell.setColspan(3);
			cell.setPaddingTop(20);
			cell.setPhrase(pdf.getFont("text").process(getString("_NONE")));

			table.addCell(cell);
			table.addRow();
			mainTable.addCell(table);
			mainTable.addRow();
		}
		else {
			final PdfCell cell = PdfCreator.getCell();
			cell.addElement(table);
			mainTable.addCell(cell);
			mainTable.addCell(PdfCreator.getCell(image != null ? image : generatePieChart(doc, limit, colors)));
			mainTable.addRow();
		}
	}

	/**
	 * Add top targets to the dashboard export.
	 *
	 * @param module The current module setup
	 * @param mainTable Pdf table.
	 */
	private void addTopTargets(final DashboardModule module, final PdfTable mainTable) throws PdfException {

		final PdfTable table = PdfCreator.getTable(new float[] {15, 300, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_NAME")));

		pdf.addTableCells(table, new Object[] {cell, new PdfParagraph(pdf.getFont("header").process(getString("_COUNT")))}, -1, 5, null);

		final List<TopTargetCounts> doc = getTopTargets(module.count, module.group, module.low, module.medium, module.high);

		final Color[] colors = getColors(module.count);

		int count = 0;
		for (final TopTargetCounts r : doc) {
			final PdfImage image = pdf.getLineImage(colors[count]);
			image.setSpacingBefore(3);
			addTableCells(table, new Object[] {image, r.name, r.count});
			count++;
		}

		addDataToTable(mainTable, table, doc, count, module.count);
	}

	/**
	 * Add top applications.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTopApplications(final DashboardModule module, final PdfTable mainTable) throws PdfException {

		final PdfTable table = PdfCreator.getTable(new float[] {15, 300, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		// Titles
		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_NAME")));

		pdf.addTableCells(table, new Object[] {cell, new PdfParagraph(pdf.getFont("header").process(getString("_COUNT")))}, -1, 5, null);

		final List<TopApplicationCounts> doc = getTopApplications(module.count, module.group, module.low, module.medium, module.high);

		final Color[] colors = getColors(module.count);

		int count = 0;
		for (final TopApplicationCounts r : doc) {
			final PdfImage image = pdf.getLineImage(colors[count]);
			image.setSpacingBefore(3);
			addTableCells(table, new Object[] {image, r.product, r.count});
			count++;
		}

		addDataToTable(mainTable, table, doc, count, module.count);
	}

	/**
	 * Add certificate information.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addCertificates(final DashboardModule module, final PdfTable mainTable) throws SQLException {
		final CertificateInfo info = getCertificateInfo(module.group).get(0);
		final PdfTable infoTable = new PdfTable(new float[] {0.8f, 0.2f});
		infoTable.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);
		infoTable.addCell(PdfCreator.getCell(getString("_EXPIRED_CERTIFICATES"), pdf.getFont("text")));
		infoTable.addCell(PdfCreator.getCell("" + info.expired, pdf.getFont("text")));
		infoTable.addRow();
		infoTable.addCell(PdfCreator.getCell(getString("_EXPIRES_SOON"), pdf.getFont("text")));
		infoTable.addCell(PdfCreator.getCell("" + info.nearlyExpired, pdf.getFont("text")));
		infoTable.addRow();
		infoTable.addCell(PdfCreator.getCell(getString("_UNTRUSTED_ISSUER"), pdf.getFont("text")));
		infoTable.addCell(PdfCreator.getCell("" + info.invalid, pdf.getFont("text")));
		infoTable.addRow();
		infoTable.addCell(PdfCreator.getCell(getString("_WEAK_CIPHERS"), pdf.getFont("text")));
		infoTable.addCell(PdfCreator.getCell("" + info.weak, pdf.getFont("text")));
		infoTable.addRow();
		infoTable.addCell(PdfCreator.getCell(getString("_TOTAL"), pdf.getFont("text")));
		infoTable.addCell(PdfCreator.getCell("" + info.total, pdf.getFont("text")));
		infoTable.addRow();

		final PdfCell c = PdfCreator.getCell();
		c.addElement(infoTable);
		final Color[] colors = new Color[] {new Color(Integer.parseInt("ff0000", 16)), new Color(Integer.parseInt("bababa", 16))};
		final PdfTable table = new PdfTable(3);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);
		table.addCell(PdfCreator.getCell());

		table.addCell(PdfCreator.getCell(getString("_EXPIRED_CERTIFICATES"), pdf.getFont("text"), PdfCreator.ALIGN_CENTER));
		table.addCell(PdfCreator.getCell(getString("_EXPIRES_SOON"), pdf.getFont("text"), PdfCreator.ALIGN_CENTER));
		table.addRow();
		table.addCell(c);
		table.addCell(PdfCreator.getCell(
				PdfCreator.getImage(ChartFactory.createPieChart(null, new long[] {info.expired, (info.total - info.expired)}, new ChartConfig(colors, 500, 500)), true)));
		table.addCell(PdfCreator.getCell(
				PdfCreator.getImage(ChartFactory.createPieChart(null, new long[] {info.nearlyExpired, (info.total - info.nearlyExpired)}, new ChartConfig(colors, 500, 500)),
						true)));
		table.addRow();
		table.addCell(PdfCreator.getCell());
		table.addCell(PdfCreator.getCell(getString("_UNTRUSTED_ISSUER"), pdf.getFont("text"), PdfCreator.ALIGN_CENTER));
		table.addCell(PdfCreator.getCell(getString("_WEAK_CIPHERS"), pdf.getFont("text"), PdfCreator.ALIGN_CENTER));
		table.addRow();
		table.addCell(PdfCreator.getCell());
		table.addCell(PdfCreator.getCell(
				PdfCreator.getImage(ChartFactory.createPieChart(null, new long[] {info.invalid, (info.total - info.invalid)}, new ChartConfig(colors, 500, 500)), true)));
		table.addCell(PdfCreator.getCell(
				PdfCreator.getImage(ChartFactory.createPieChart(null, new long[] {info.weak, (info.total - info.weak)}, new ChartConfig(colors, 500, 500)), true)));
		table.addRow();

		final PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.addElement(table);
		mainTable.addCell(cell);
		mainTable.addRow();
	}

	/**
	 * Add trend.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addTrend(final DashboardModule module, final PdfTable mainTable) throws SQLException {

		final ArrayList<Color> colors = new ArrayList<>();
		if (module.high) {
			colors.add(new Color(Integer.parseInt("ff0000", 16)));
		}
		if (module.medium) {
			colors.add(new Color(Integer.parseInt("ec9513", 16)));
		}
		if (module.low) {
			colors.add(new Color(Integer.parseInt("306cce", 16)));
		}

		final TreeMap<String, Number[]> tvalues = new TreeMap<>();
		Date trendDate = null;
		final List<DashTrend> tcounts = getVulnerabilityTrend(module.group);
		for (final DashTrend count : tcounts) {
			final ArrayList<Integer> values = new ArrayList<>();
			if (module.high) {
				values.add((int) count.high);
			}
			if (module.medium) {
				values.add((int) count.medium);
			}
			if (module.low) {
				values.add((int) count.low);
			}

			tvalues.put(DateUtils.formatDate(count.date), values.toArray(new Integer[values.size()]));
			if (trendDate == null || count.date.after(trendDate)) {
				trendDate = count.date;
			}
		}
		if (trendDate != null) {
			final Calendar cal = Calendar.getInstance();
			cal.setTime(trendDate);
			cal.add(Calendar.DATE, 1);
			while (cal.getTime().before(new Date())) {
				tvalues.put(DateUtils.formatDate(cal.getTime()), new Integer[] {null, null, null});
				cal.add(Calendar.DATE, 1);
			}
		}

		ChartConfig config = new ChartConfig(colors.toArray(new Color[colors.size()]), 1170, 300);
		config.setTickUnit(14);
		final byte[] trend = ChartFactory.createTimeSeriesChart(new String[colors.size()], tvalues, "DD", "yyyy-MM-dd", config);
		PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_VULNERABILITIES")));
		mainTable.addCell(cell);

		cell = PdfCreator.getCell(PdfCreator.getImage(trend, true));
		cell.setColspan(2);
		cell.setPaddingTop(10);
		mainTable.addCell(cell);
		mainTable.addRow();

		final TreeMap<String, Number[]> rvalues = new TreeMap<>();
		Date reportDate = null;
		final List<RemediationCounts> rcounts = getRemediationStats(module.group, module.low, module.medium, module.high);
		for (final RemediationCounts count : rcounts) {
			final ArrayList<Integer> valuesForDate = new ArrayList<>();
			if (module.high) {
				valuesForDate.add((int) count.daysHigh);
			}
			if (module.medium) {
				valuesForDate.add((int) count.daysMedium);
			}
			if (module.low) {
				valuesForDate.add((int) count.daysLow);
			}
			rvalues.put(DateUtils.formatDate(count.day), valuesForDate.toArray(new Integer[valuesForDate.size()]));
			if (reportDate == null || count.day.after(reportDate)) {
				reportDate = count.day;
			}
		}

		if (reportDate != null) {
			final Calendar cal = Calendar.getInstance();
			cal.setTime(reportDate);
			cal.add(Calendar.DATE, 1);
			while (cal.getTime().before(new Date())) {
				rvalues.put(DateUtils.formatDate(cal.getTime()), new Integer[] {null, null, null});
				cal.add(Calendar.DATE, 1);
			}
		}

		config = new ChartConfig(colors.toArray(new Color[colors.size()]), 1170, 300);
		config.setTickUnit(14);
		final byte[] remediation = ChartFactory.createTimeSeriesChart(new String[colors.size()], rvalues, "DD", "yyyy-MM-dd", config);
		cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPaddingTop(20);
		cell.setPhrase(pdf.getFont("header").process(getString("_REMEDIATION")));
		mainTable.addCell(cell);

		cell = PdfCreator.getCell(PdfCreator.getImage(remediation, true));
		cell.setColspan(2);
		cell.setPaddingTop(10);
		cell.setPaddingBottom(20);
		mainTable.addCell(cell);
		mainTable.addRow();
	}

	/**
	 * Add accepted findings.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addAcceptedFindings(final DashboardModule module, final PdfTable mainTable) throws SQLException {
		final List<AcceptedFindings> findings = getAcceptedFindings(module.group, module.low, module.medium, module.high);
		final PdfTable table = PdfCreator.getTable(new float[] {300, 60}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		final Color[] colors = new Color[] {new Color(Integer.parseInt("ff0000", 16)), new Color(Integer.parseInt("bababa", 16))};

		long accepted = 0;
		long total = 0;
		for (final AcceptedFindings ac : findings) {
			if (ac.accepted) {
				accepted = ac.count;
			}
			else {
				total = ac.count;
			}
		}
		addTableCells(table, new Object[] {getString("_ACCEPTED"), accepted});
		addTableCells(table, new Object[] {getString("_TOTAL"), total});

		if (accepted == 0 && findings.size() == 1) {
			final AcceptedFindings acceptedFindings = new AcceptedFindings();
			acceptedFindings.accepted = true;
			acceptedFindings.count = 0;
			findings.add(0, acceptedFindings);
		}

		addDataToTable(mainTable, table, findings, 1, module.count, null, colors);
	}

	/**
	 * Add exploitable findings.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addExploitable(final DashboardModule module, final PdfTable mainTable) throws SQLException {
		final List<ExploitableTargets> findings = getExploitableTargets(module.group, module.low, module.medium, module.high);

		final PdfTable table = PdfCreator.getTable(new float[] {160, 100}, -1, getRunDirection());
		table.setTotalWidth(375);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		addTableCells(table, new Object[] {findings.get(0).name, findings.get(0).count});
		addTableCells(table, new Object[] {findings.get(1).name, findings.get(1).count});

		addDataToTable(mainTable, table, findings, findings.size(), 2);
	}

	/**
	 * Add latest findings.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addLatestFindings(final DashboardModule module, final PdfTable mainTable) throws SQLException, PdfException {
		final List<FindingImpl> findings = getLatestFindings(module.count, module.group, module.low, module.medium, module.high);

		final PdfTable table = PdfCreator.getTable(new float[] {15, 220, 120, 100, 60}, -1, getRunDirection());
		table.setTotalWidth(515);
		table.setLockedWidth(true);
		table.setHorizontalAlignment(PdfCreator.ALIGN_LEFT);

		PdfCell cell = PdfCreator.getCell();
		cell.setColspan(2);
		cell.setPhrase(pdf.getFont("header").process(getString("_FINDING")));

		pdf.addTableCells(table, new Object[] {
				cell, new PdfParagraph(pdf.getFont("header").process(getString("_TARGET"))), new PdfParagraph(pdf.getFont("header").process(getString("_CVE"))),
				new PdfParagraph(pdf.getFont("header").process(getString("_RISK")))
		}, -1, 5, null);

		final Color[] colors = getColors(Math.min(findings.size(), module.count));
		for (int i = 0; i < findings.size(); i++) {
			final FindingImpl r = findings.get(i);
			final PdfImage image = pdf.getLineImage(colors[i]);
			image.setSpacingBefore(3);
			addTableCells(table, new Object[] {
					image,
					StringUtils.setEmpty(r.getName(), ""),
					StringUtils.setEmpty(r.getTarget(), ""),
					StringUtils.setEmpty(r.getCve(), ""),
					getRiskLevel(r.getRiskLevel())
			});
		}

		if (findings.isEmpty()) {
			cell = PdfCreator.getCell();
			cell.setColspan(3);
			cell.setPaddingTop(20);
			cell.setPhrase(pdf.getFont("text").process(getString("_NONE")));

			table.addCell(cell);
			table.addRow();
		}

		final PdfCell c = PdfCreator.getCell();
		c.setColspan(2);
		c.addElement(table);
		mainTable.addCell(c);
		mainTable.addRow();
	}

	/**
	 * Add risk summary.
	 *
	 * @param module Module to add.
	 * @param mainTable Pdf table.
	 */
	private void addRiskSummary(final DashboardModule module, final PdfTable mainTable) throws IOException {
		final List<RiskSummaryCounts> doc = getRiskCounts(module.group);

		if (!doc.isEmpty()) {
			final RiskSummaryCounts r = doc.get(0);
			double high = 0;
			double medium = 0;
			double low = 0;
			final long total = r.total;
			if (total != 0) {
				high = r.high / (double) total * 100;
				medium = r.medium / (double) total * 100;
				low = r.low / (double) total * 100;
			}

			final PdfTable table = PdfCreator.getTable(new float[] {20, 355}, -1, getRunDirection());
			table.setTotalWidth(375);
			table.setLockedWidth(true);
			table.setHorizontalAlignment(getRunDirection() == PdfCreator.RUN_DIRECTION_LTR ? PdfCreator.ALIGN_LEFT : PdfCreator.ALIGN_RIGHT);

			addRiskSummaryLabel(table, r.hightrend, Math.round(high), getString("_HIGH"), !module.high);
			addRiskSummaryLabel(table, r.mediumtrend, Math.round(medium), getString("_MEDIUM"), !module.medium);
			addRiskSummaryLabel(table, r.lowtrend, Math.round(low), getString("_LOW"), !module.low);

			final Color color_high = new Color(Integer.parseInt("ff0000", 16));
			final Color color_medium = new Color(Integer.parseInt("ec9513", 16));
			final Color color_low = new Color(Integer.parseInt("306cce", 16));
			final Color color_info = new Color(Integer.parseInt("bababa", 16));

			final BufferedImage bg = ImageIO.read(new File(getPath("/META-INF/images/", "chart-bg-h.png")));

			final int rows = (module.high ? 1 : 0) + (module.medium ? 1 : 0) + (module.low ? 1 : 0);
			final long[][] values = new long[rows][rows + 1];
			final Color[] colors = new Color[rows + 1];
			colors[0] = color_info;

			int index = 0;
			if (module.high) {
				values[index][0] = 100 - (int) Math.round(high);
				values[index][index + 1] = (int) Math.round(high);
				colors[index + 1] = color_high;
				index++;
			}

			if (module.medium) {
				values[index][0] = 100 - (int) Math.round(medium);
				values[index][index + 1] = (int) Math.round(medium);
				colors[index + 1] = color_medium;
				index++;
			}

			if (module.low) {
				values[index][0] = 100 - (int) Math.round(low);
				values[index][index + 1] = (int) Math.round(low);
				colors[index + 1] = color_low;
				index++;
			}

			final ChartConfig config = new ChartConfig(colors, 400, 300);
			config.setBackground(bg);
			addDataToTable(mainTable, table, null, 1, 0, PdfCreator.getImage(ChartFactory.createStackedBarChart(null, values, config), true), null);
		}
	}

	/**
	 * Add risk summary label.
	 *
	 * @param table Pdf table.
	 * @param trend Change in trend.
	 * @param value Percentage of exposed targets.
	 * @param type Type of risk (_LOW/_MEDIUM/_HIGH)
	 * @param hidden If true, nothing is added.
	 */
	private void addRiskSummaryLabel(final PdfTable table, final long trend, final long value, final String type, final boolean hidden) throws IOException {
		if (hidden) {
			return;
		}

		PdfCell cell = PdfCreator.getCell();
		if (trend > 0) {
			final PdfImage image = PdfCreator.getImage(getPath("/META-INF/images/", "trend_up.png"));
			if (image != null) {
				image.scaleAbsolute(12, 16);
				cell = PdfCreator.getCell(image);
			}
		}
		else if (trend < 0) {
			final PdfImage image = PdfCreator.getImage(getPath("/META-INF/images/", "trend_down.png"));
			if (image != null) {
				image.scaleAbsolute(12, 16);
				cell = PdfCreator.getCell(image);
			}
		}
		cell.setFixedHeight(16);
		table.addCell(cell);

		cell = PdfCreator.getCell();
		cell.setVerticalAlignment(PdfCreator.ALIGN_MIDDLE);
		final PdfParagraph paragraph = new PdfParagraph();
		paragraph.add(pdf.getFont("header").process("" + value + "%"));
		paragraph.add(pdf.getFont("text").process(" " + getString("_OF_YOUR_TARGETS_HAVE_A") + " "));
		paragraph.add(pdf.getFont("header").process(type + " " + getString("_RISK")));
		cell.setPhrase(paragraph);
		table.addCell(cell);

		table.addRow();
	}

	/**
	 * Adds the footer for the document.
	 *
	 * @param text Footer text
	 */
	private void addFooter(final String text) throws IOException {
		final PdfParagraph paragraph = new PdfParagraph(pdf.getFont("footer").process(text));
		final PdfImage image = PdfCreator.getImage(getPath("/META-INF/images/", "section-footer.png"));
		final PdfHeaderFooter footer = new PdfHeaderFooter(false, paragraph, this.pdf.getFont("footer"), 1);
		footer.setImage(image);

		final String cover;
		if (Configuration.isWhitelabel(getWhiteLabelUser())) {
			cover = Configuration.getProperty(ConfigurationKey.report_pdfcover, getWhiteLabelUser());
		}
		else {
			cover = Configuration.getProperty(ConfigurationKey.report_pdfcover);
		}
		if (!StringUtils.isEmpty(cover)) {
			footer.setBg(PdfCreator.getImage(cover));
		}
		footer.setBgPosition(Configuration.getProperty(ConfigurationIntKey.report_pdfcover_position, getWhiteLabelUser(),
				Configuration.getProperty(ConfigurationIntKey.report_pdfcover_position)));
		footer.setBgSize(
				Configuration.getProperty(ConfigurationIntKey.report_pdfcover_size, getWhiteLabelUser(), Configuration.getProperty(ConfigurationIntKey.report_pdfcover_size)));
		footer.setBgPadding(Configuration.getProperty(ConfigurationIntKey.report_pdfcover_padding, getWhiteLabelUser(),
				Configuration.getProperty(ConfigurationIntKey.report_pdfcover_padding)));

		this.pdf.setPageEvent(footer);
	}

	/**
	 * Get path to file.
	 *
	 * @param path File path.
	 * @param file File name.
	 * @return Full file path.
	 */
	private String getPath(final String path, final String file) {
		String basePath = Configuration.getProperty(ConfigurationKey.report_staticpath, XMLAPI.getPrefix());
		if (basePath.startsWith("classpath")) {
			return basePath + file;
		}
		if (!new File(basePath + path + file).exists()) {
			basePath = XMLAPI.getPrefix();
		}
		return basePath + path + file;
	}

	/**
	 * Creates all fonts used in this document.
	 */
	private void createFonts() throws IOException, PdfException {
		String font = "ru".equals(user.getLanguage()) ? "AGHelvetica" : "Vaud";
		final String baseFont = "jp".equals(user.getLanguage()) ? "KozMinPro-Regular" : null;
		final String baseEncoding = "jp".equals(user.getLanguage()) ? "UniJIS-UCS2-H" : null;

		final String customFont = Configuration.getProperty(ConfigurationKey.report_pdffont, getWhiteLabelUser());
		if (!StringUtils.isEmpty(customFont)) {
			font = "font_" + getWhiteLabelUser();
			if (!PdfCreator.hasFont(font)) {
				PdfCreator.registerFont(customFont, font);
			}
		}

		pdf.addFont("title_cover", baseFont, baseEncoding, font, 24, new Color(37, 54, 70), PdfCreator.FONT_BOLD);

		pdf.addFont("title", baseFont, baseEncoding, font, 12, new Color(0, 81, 114), PdfCreator.FONT_BOLD);

		pdf.addFont("header", baseFont, baseEncoding, font, 10, new Color(0, 0, 0), PdfCreator.FONT_BOLD);
		pdf.addFont("header_red", baseFont, baseEncoding, font, 10, new Color(255, 44, 44), PdfCreator.FONT_BOLD);
		pdf.addFont("header_green", baseFont, baseEncoding, font, 10, new Color(0, 128, 0), PdfCreator.FONT_BOLD);

		pdf.addFont("footer", baseFont, baseEncoding, font, 8, new Color(107, 107, 107), PdfCreator.FONT_NORMAL);
		pdf.addFont("toc", baseFont, baseEncoding, font, 10, new Color(0, 0, 0), PdfCreator.FONT_NORMAL);

		pdf.addFont("text", baseFont, baseEncoding, font, 10, new Color(0, 0, 0), PdfCreator.FONT_NORMAL);
		pdf.addFont("text_bold", baseFont, baseEncoding, font, 10, new Color(0, 0, 0), PdfCreator.FONT_BOLD);
		pdf.addFont("text_red", baseFont, baseEncoding, font, 10, new Color(255, 44, 44), PdfCreator.FONT_NORMAL);
		pdf.addFont("text_green", baseFont, baseEncoding, font, 10, new Color(0, 128, 0), PdfCreator.FONT_NORMAL);
	}

	@Override
	protected void init() {
		try {
			createFonts();

			final String company = Configuration.isHiabEnabled()
					? Configuration.getProperty(ConfigurationKey.company)
					: Configuration.getProperty(ConfigurationKey.company, getWhiteLabelUser());

			if (headerInfo != null) {
				headerInfo.setContentType("application/pdf");
				headerInfo.setContentDisposition("attachment; filename=\"dashboard.pdf\"");
			}

			pdf.init(50, 50, out);

			final String userPassword = StringUtils.setEmpty(password, this.user.getReportPassword());
			final String ownerPassword = StringUtils.setEmpty(Configuration.getProperty(ConfigurationKey.report_ownerpassword), userPassword);
			if (!StringUtils.isEmpty(ownerPassword)) {
				if (shouldZip()) {
					pdf.setEncryption(null, ownerPassword, PdfCreator.STANDARD_ENCRYPTION_128);
				}
				else {
					pdf.setEncryption(userPassword, ownerPassword, PdfCreator.STANDARD_ENCRYPTION_128);
				}
			}

			final String gmtoffset = DateUtils.getTimezone(this.user.getGmtOffset());

			addFooter(getString("_DASHBOARD") + ", " + DateUtils.formatDateTimezone(new Date(), DateUtils.getDateFormat(this.user.getDateFormat()), gmtoffset) +
					" © Copyright " + Calendar.getInstance().get(Calendar.YEAR) + " " + company + "      Page ");

			pdf.open();
			pdf.setDocumentInfo(getString("_DASHBOARD"), "", company, company);

			chapter = PdfCreator.newChapter(null, null);
		}
		catch (final Exception e) {
			LOG.error("Init failed:", e);
		}
	}

	@Override
	protected void finish() {
		try {
			pdf.add(chapter);

			pdf.close();

			if (shouldZip()) {
				final String userpassword = !StringUtils.isEmpty(password) ? password : this.user.getReportPassword();
				try {
					getZipFiles().add(new ZipFile("dashboard.pdf", out.toByteArray(), null));
					ZipUtils.zipAndEncryptContent(result, getZipFiles().toArray(new ZipFile[0]), userpassword);
				}
				catch (final Exception e) {
					LOG.error("Compressing of dashboard.pdf failed: ", e);
				}
			}
			else {
				result.write(out.toByteArray());
			}

			result.flush();
			result.close();

			pdf.closeWriter();
		}
		catch (final ClientAbortException e) {
			LOG.info("User aborted download");
		}
		catch (final Exception e) {
			LOG.error("Finish failed:", e);
		}
	}

	/**
	 * Lazy loading color scale when needed.
	 *
	 * @return An array of {@link Color}s
	 */
	private Color[] getColorScale() {
		if (this.colorScale == null) {
			this.colorScale = new Color[] {
					new Color(37, 55, 70),
					new Color(84, 146, 128),
					new Color(111, 165, 210),
					new Color(51, 13, 45),
					new Color(83, 60, 60),
					new Color(210, 204, 126),
					new Color(50, 70, 41),
					new Color(115, 99, 71),
					new Color(125, 88, 82)
			};
		}

		return this.colorScale;
	}

	/**
	 * Get a list of colors for graphs.
	 *
	 * @param count Number of colors to get.
	 * @return List of colors.
	 */
	private Color[] getColors(final int count) {
		final List<Color> colors = new ArrayList<>();

		final Color[] colorScale = getColorScale();
		int scaleIndex = 0;

		for (int i = 0; i < count; i++) {
			colors.add(colorScale[scaleIndex]);

			if (scaleIndex >= colorScale.length - 1) {
				scaleIndex = 0;
			}
			else {
				scaleIndex++;
			}
		}

		return colors.toArray(new Color[colors.size()]);
	}

	/**
	 * Generate a pie chart.
	 *
	 * @param counts Values for chart.
	 * @param maxRows Max number of rows for chart.
	 * @param colors Colors to use.
	 * @return Pdf image.
	 */
	private PdfImage generatePieChart(final List<? extends Countable> counts, final int maxRows, final Color[] colors) {
		final int rows = counts.size();
		if (rows != 0) {
			final long[] values = new long[Math.min(rows, maxRows)];
			int index = 0;

			for (final Countable r : counts) {
				values[index] = (int) r.getCount();
				index++;
				if (index == maxRows) {
					break;
				}
			}

			final byte[] bytes = ChartFactory.createPieChart(null, values, new ChartConfig(colors != null ? colors : getColors(Math.min(rows, maxRows)), 500, 500));
			return bytes == null ? null : PdfCreator.getImage(bytes, true);
		}
		return null;
	}

	private int getRunDirection() {
		return PdfCreator.RUN_DIRECTION_LTR;
	}

	private void addTableCells(final PdfTable table, final Object[] objects) {
		pdf.addTableCells(table, objects, -1, -1, null);
	}
}
