package com.chilicoders.bl;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.chilicoders.app.WebRequest;
import com.chilicoders.bl.filters.FilteringDataFactory;
import com.chilicoders.bl.filters.FilteringUtils;
import com.chilicoders.bl.filters.TargetGroupCustomFilter;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.common.sql.SearchQuerySqlFragment;
import com.chilicoders.core.rendering.api.Renderer;
import com.chilicoders.core.rendering.api.RenderingService;
import com.chilicoders.core.user.api.AttributeType;
import com.chilicoders.core.user.api.Permission;
import com.chilicoders.core.user.api.UserDetails;
import com.chilicoders.db.Access;
import com.chilicoders.db.Access.AccessType;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.Attribute;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.ScannerImpl;
import com.chilicoders.db.objects.Target;
import com.chilicoders.db.objects.TargetGroup;
import com.chilicoders.db.query.NativeSqlFragment;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.filters.CustomFilter;
import com.chilicoders.filters.FieldDefinition;
import com.chilicoders.filters.FieldType;
import com.chilicoders.filters.FilterField;
import com.chilicoders.filters.FilterName;
import com.chilicoders.filters.FilteringData;
import com.chilicoders.filters.FilteringSqlUtils;
import com.chilicoders.filters.ListDataType;
import com.chilicoders.filters.report.ReportingCustomFilter;
import com.chilicoders.filters.target.TargetCustomFilter;
import com.chilicoders.model.BusinessCriticality;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.rest.exceptions.BaseRestException;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.HttpRequestWrapper;
import com.chilicoders.util.ParamValidator;
import com.chilicoders.util.ParamValidator.DynamicValidatorLocation;
import com.chilicoders.util.ParamValidator.Method;
import com.chilicoders.util.StringUtils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class TargetGroupBusiness extends BusinessObject {
	private static final String ROWSET_TAG = "GROUPLIST";
	private static final String ROW_TAG = "GROUP";

	private static final ParamValidator[] listValidators = new ParamValidator[] {
			ParamValidator.digitList("XID"),
			ParamValidator.digitOnly("XIPARENTID"),
			ParamValidator.digitOnly("ROOT"),
			ParamValidator.booleanValue("INCLUDEALLGROUP"),
			ParamValidator.booleanValue("INCLUDEUNGROUP"),
			ParamValidator.booleanValue("PCI"),
			ParamValidator.digitOnly("PARENTUSER"),
			ParamValidator.digitOnly("TARGET"),
			ParamValidator.digitList("GROUPXID"),
			ParamValidator.printable("DELETENOTE"),
			ParamValidator.booleanValue("RULEBASED"),
			ParamValidator.booleanValue("REPORTFILTER"),
			ParamValidator.printable("NAME", 250),
			ParamValidator.printable("TARGETS"),
			ParamValidator.printable("DESCRIPTION"),
			ParamValidator.booleanValue("INCLUDERULE"),
			ParamValidator.digitList("REMOVETARGETLIST"),
			ParamValidator.digitList("GROUPS"),
			ParamValidator.printable("DATAINDEXES"),
			ParamValidator.printable("COLUMNS"),
			ParamValidator.ascii("FORMAT", 10, null, null),
			ParamValidator.booleanValue("MOVE"),
			ParamValidator.digitList("ADDTARGETLIST")
	};

	private static final Map<String, FieldDefinition> FILTER_FIELDS;

	static {
		final Map<String, FieldDefinition> filters = new HashMap<>();
		filters.put("XID", new FieldDefinition(FieldType.NUMERIC, ListDataType.NUMERIC));
		filters.put("NAME", new FieldDefinition(FieldType.STRING, ListDataType.STRING, "", "", "", "", 255));
		FILTER_FIELDS = Collections.unmodifiableMap(filters);
	}

	private static final Logger LOG = LogManager.getLogger(TargetGroupBusiness.class);

	public TargetGroupBusiness() {
		super();
	}

	protected TargetGroupBusiness(final UserDetails user) {
		super(user);
	}

	/**
	 * Adds the header to the ip report.
	 *
	 * @param builder The resulting output.
	 * @param columns The columns to add to the report.
	 */
	private void addHeader(final StringBuilder builder, final String[] columns) {
		builder.append("<table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr>");
		for (final String column : columns) {
			builder.append("<td valign=\"middle\"><div class=\"header\" style=\"font-size:8px;\">").append(StringEscapeUtils.escapeHtml4(column)).append("</div></td>");
		}
		builder.append("</tr>");
	}

	/**
	 * Exports an html report with all targetgroups and targets starting from the id of the parameter ROOT.
	 *
	 * @param conn Database connection
	 * @param request HTTP servlet request.
	 * @param response HTTP Servlet response.
	 */
	@WebRequest(action = "IPREPORT")
	private void ipReport(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException, IOException, IllegalAccessException {
		request.checkInput(conn, listValidators);

		final boolean isRoot = "-1".equals(request.getParameter("ROOT"));
		final boolean isUngrouped = "-2".equals(request.getParameter("ROOT"));
		final String format = StringUtils.setEmpty(request.getParameter("FORMAT"), "html").toLowerCase();
		final String root = request.getParameter("ROOT");
		final String filter = (isRoot ? null : "xid IN (SELECT p.xid FROM xgenericpaths p WHERE p.parentxid = " + root + ")");

		final StringBuilder sql = new StringBuilder();
		final List<Object> params = new ArrayList<>();
		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.MAINUSER);
		addTargetGroupListFilter(conn, config, sql, params, getMainUserId(), -1, false, filter, -1, isRoot, isRoot || isUngrouped);
		final List<TargetGroup> groups = fetchInformation(TargetGroup.class, conn, config, sql.toString(), "sortorder, name ASC", params);

		sql.delete(0, sql.length());
		params.clear();
		addTargetGroupListFilter(conn, config, sql, params, getMainUserId(), -1, false, null, -1, true, false);
		final List<TargetGroup> allGroups = fetchInformation(TargetGroup.class, conn, config, sql.toString(), null, params);
		final List<Attribute> attributes = UserBusiness.getAttributes(conn, getMainUserId());

		final String targetSort = dba.getSorting(conn, request.getGroupSortParams(), getMainUserId(), TargetBusiness.getFilters(conn, getMainUserId()).keySet());
		final String[] columns = StringUtils.trimChar(request.getParameter("COLUMNS"), true, ',').split(",");
		final String[] dataIndex = StringUtils.trimChar(request.getParameter("DATAINDEXES"), true, ',').split(",");
		final StringBuilder builder = new StringBuilder();

		TargetGroup rootGroup = null;
		for (final TargetGroup group : groups) {
			if (group.getId() == StringUtils.getLongValue(root)) {
				rootGroup = group;
				break;
			}
		}

		if (format.equals("csv")) {
			builder.append("\"Target Group\"");
			for (final String column : columns) {
				builder.append(",\"").append(column).append("\"");
			}
			builder.append("\n");

			for (final TargetGroup group : groups) {
				final List<Target> targets = getTargets(conn, new Long[] {group.getId()}, getMainUserId(), targetSort);
				for (final Target target : targets) {
					builder.append("\"").append(group.getName()).append("\"");
					for (final String index : dataIndex) {
						builder.append(",\"").append(formatValue(index.toUpperCase(), target, attributes, false).replace("\"", "\"\"")).append("\"");
					}
					builder.append("\n");
				}
			}

			final String title = (rootGroup != null ? rootGroup.getName() : root) + "_" + DateUtils.getCurrentTimeDate(getNewLoggedOnUser().getGmtOffset()).replace(" ", "_");
			response.setHeader("Content-Type", "text/csv;charset=UTF-8");
			response.setHeader("Content-Disposition", "attachment; filename=\"" + title + ".csv\"");
			response.getWriter().print(builder);
		}
		else {
			for (final TargetGroup group : groups) {
				final String title = getTargetgroupTitle(group, allGroups);
				builder.append("<b>").append(StringEscapeUtils.escapeHtml4(StringUtils.setEmpty(title, ""))).append("</b><br/>");
				addHeader(builder, columns);
				final List<Target> targets = getTargets(conn, new Long[] {group.getId()}, getMainUserId(), targetSort);
				for (final Target target : targets) {
					builder.append("<tr>");
					for (final String index : dataIndex) {
						builder.append("<td class=\"values\">");
						final String value = formatValue(index.toUpperCase(), target, attributes, true);
						if (value != null) {
							builder.append(StringEscapeUtils.escapeHtml4(value));
						}
						builder.append("</td>");
					}
					builder.append("</tr>");
				}
				builder.append("</table>");
				builder.append("<hr noshade=\"noshade\" width=\"100%\" align=\"left\"/>");
			}

			response.setContentType(MediaType.TEXT_HTML);
			response.getWriter().printf(MessageBusiness.getMessageString("ipreport", getUserLanguage(), "TEMPLATE"), DateUtils.getCurrentDate(), "", builder);
		}
	}

	/**
	 * Get target group title.
	 *
	 * @param currentGroup Current group
	 * @param allGroups List of target groups
	 * @return Target group names
	 */
	private String getTargetgroupTitle(final TargetGroup currentGroup, final List<TargetGroup> allGroups) {
		long parent = currentGroup.getParentId();
		String title = currentGroup.getName();
		while (parent != -1 && currentGroup.getId() != -1) {
			TargetGroup parentGroup = null;
			for (final TargetGroup group : allGroups) {
				if (group.getId() == parent) {
					parentGroup = group;
					break;
				}
			}
			if (parentGroup != null) {
				title = parentGroup.getName() + " -> " + title;
				parent = parentGroup.getParentId();
			}
			else {
				parent = -1;
			}
		}
		return title;
	}

	/**
	 * Formats the target data according in a html export of the target group.
	 *
	 * @param dataIndex The dataindex to format.
	 * @param target The target object
	 * @param attributes User attributes
	 * @param html true to use html, otherwise false
	 * @return A string that represents the value.
	 */
	private String formatValue(final String dataIndex, final Target target, final List<Attribute> attributes, final boolean html) throws IllegalAccessException {
		final Object objValue = DbHelper.getObjectInformation(Target.class).getField(dataIndex, true).get(target);
		Renderer renderer;
		final RenderingService renderingService = ServiceProvider.getRenderingService(null);
		String value = (objValue == null ? null : objValue + "");

		switch (dataIndex) {
			case "LATESTSCANDATE":
			case "LASTDISCOVERYDATE":
			case "LATESTSUCCESSFULSCANDATE":
				renderer = renderingService.getDateRenderer(getNewLoggedOnUser(), "_NEVER");
				break;
			case "LATESTSCANSTATUS":
				renderer = renderingService.getScanStatusRenderer();
				break;
			case "NEXTSCANDATE":
				renderer = renderingService.getDateRenderer(getNewLoggedOnUser(), "_NOT_SCHEDULED");
				break;
			case "PLATFORM":
				renderer = renderingService.getPlatformRenderer();
				break;
			case "CVSS_SR_AVAIL":
			case "CVSS_SR_INTEG":
			case "CVSS_SR_CONF":
			case "CVSS_CDP":
			case "CVSS_TD":
				renderer = renderingService.getLowMediumHighRenderer();
				break;
			case "TEMPLATEOVERRIDE":
				renderer = renderingService.getOverrideScanPolicyRenderer();
				break;
			case "USESLICENSE":
			case "SCANUPDATEAVAILIBLE":
				renderer = renderingService.getBooleanRenderer();
				break;
			case "AUTHENTICATIONTYPE":
				renderer = renderingService.getTargetAuthenticationTypeRenderer();
				break;
			case "AUTHENTICATIONRESULT":
				renderer = renderingService.getTargetAuthenticationResultRenderer();
				break;
			default:
				renderer = renderingService.getValueRenderer();
		}

		if (dataIndex.matches("CUSTOM\\d")) {
			final Matcher m = Pattern.compile("CUSTOM(\\d)").matcher(dataIndex);
			if (m.matches()) {
				final Attribute attribute = Attribute.getAttribute(attributes, StringUtils.getLongValue(m.group(1)));
				if (attribute != null && attribute.getType() == AttributeType.Checkbox) {
					renderer = renderingService.getBooleanRenderer();
				}
				else if (attribute != null && attribute.getType() == AttributeType.Date) {
					renderer = renderingService.getDateRenderer(getNewLoggedOnUser(), "");
				}
			}
		}

		value = StringUtils.setEmpty(renderer.renderData(value, null, null, html), "");

		if (value != null && value.startsWith("_")) {
			final String newData = MessageBusiness.getMessageString(value, getUserLanguage());
			if (newData != null) {
				return newData;
			}
		}
		return value;
	}

	/**
	 * Gets a set of all targets that are in a group and a certain set of target ids. Any of the two parameters can be excluded in which case the
	 * group or target id restriction is no longer valid.
	 *
	 * @param conn Database connection
	 * @param groupXid The group id to look in.
	 * @param targetXid The target ids to fetch.
	 * @param target A target defined by ipaddress, hostname or netios. If set both groupXid and targetXid will be ignored. Will only return a single target xid, if multiple possible targets are found none will be returned.
	 * @return A set with all the targets received.
	 */
	protected Set<Long> getTargets(final Connection conn, final Long[] groupXid, final String targetXid, final String target) {
		final StringBuilder sql = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		try {
			if (!StringUtils.isEmpty(target)) {
				final String[] targetScannerParts = target.split("<");
				if (targetScannerParts.length == 0 || targetScannerParts.length > 2) {
					return new HashSet<>();
				}

				StringUtils.concatenateFilters(sql, "xuserxid=? AND (HOST(ipaddress) = ? OR hostname = ? OR netbios =?) AND pci=0");
				params.add(getMainUserId());
				params.add(targetScannerParts[0]);
				params.add(targetScannerParts[0]);
				params.add(targetScannerParts[0]);

				if (!hasAllTargetsAccess()) {
					StringUtils.concatenateFilters(sql, "xid IN (SELECT xipxid FROM vsubhost WHERE xsubxid = ?)");
					params.add(getSubUserId());
				}
				if (targetScannerParts.length > 1) {
					StringUtils.concatenateFilters(sql, "scannerid IN (SELECT xid FROM tscanners WHERE name=?)");
					params.add(targetScannerParts[1]);
				}

				final Set<Long> result = DbObject.fetchSet(conn, "SELECT xid FROM tuserdatas WHERE " + sql, params);
				if (result.size() <= 1) {
					return result;
				}
				return new HashSet<>();
			}
			else {
				StringUtils.concatenateFilters(sql, "xuserxid=? AND pci=0");
				params.add(getMainUserId());
				if (groupXid != null && groupXid.length > 0) {
					ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), groupXid, sql, params, null);
				}
				if (!StringUtils.isEmpty(targetXid)) {
					StringUtils.concatenateFilters(sql, "xid =ANY (?)");
					params.add(DbHelper.createIdArray(targetXid));
				}
				if (!hasAllTargetsAccess()) {
					StringUtils.concatenateFilters(sql, "xid IN (SELECT xipxid FROM vsubhost WHERE xsubxid = ?)");
					params.add(getSubUserId());
				}

				return DbObject.fetchSet(conn, "SELECT xid FROM tuserdatas WHERE " + sql, params);
			}
		}
		catch (final SQLException e) {
			LOG.error("Error getting targets", e);
			return new HashSet<>();
		}
	}

	/**
	 * Fetches the targets belonging to a specific target group.
	 *
	 * @param conn Database connection
	 * @param targetGroupIds The target group ids.
	 * @param userId The user id.
	 * @param sortOrder The sort order requested.
	 * @return A <code>XMLDoc</code> with all the targets.
	 * @throws SQLException On database access errors.
	 */
	private List<Target> getTargets(final Connection conn, final Long[] targetGroupIds, final long userId, final String sortOrder) throws SQLException {
		final TargetBusiness tb = new TargetBusiness(getNewLoggedOnUser());

		final NativeSqlFragment targetFilter = new NativeSqlFragment("xuserxid = ? AND pci = 0");
		targetFilter.addParam(userId);

		if (!hasAllTargetsAccess()) {
			targetFilter.appendFilter("xid IN (SELECT xipxid FROM vsubhost WHERE xsubxid = ?)");
			targetFilter.addParam(getSubUserId());
		}

		targetFilter.appendFilter(tb.createGroupFilter(ServiceProvider.getUserService(conn), targetGroupIds, userId, null, null, false));

		if (StringUtils.isEmpty(sortOrder)) {
			targetFilter.addSql(" ORDER BY " + sortOrder);
		}

		return Target.fetchObjects(Target.class, conn, DbHelper.getSelect(Target.class, Access.createAccess(AccessType.MAINUSER, "list"), targetFilter.getSQL()),
				targetFilter.getParameters());
	}

	/**
	 * Lists scanners for targets in target groups.
	 *
	 * @param conn Database connection
	 * @param request HTTP request
	 * @param response HTTP response
	 */
	@WebRequest(action = "TARGETGROUP_SCANNERS", permissions = {Permission.TARGET_MANAGEMENT})
	private void sendGroupScanners(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		request.checkInput(conn, Method.List, ParamValidator.digitList("GROUPIDS", Method.List));

		if (isSubUser() && !isSuperSubUser() && StringUtils.isEmpty(getNewLoggedOnUser().getScannerList())) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
		}

		final NativeSqlFragment targetFilter = new NativeSqlFragment();
		targetFilter.appendFilter("xuserxid = ?");
		targetFilter.addParam(getMainUserId());

		final TargetBusiness tb = new TargetBusiness(getNewLoggedOnUser());
		final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
		final String filter = dba.getFiltering(filteringData, TargetBusiness.getFilters(conn, getMainUserId()), getNewLoggedOnUser(), tb);
		if (!StringUtils.isEmpty(filter)) {
			targetFilter.appendFilter("xid IN (SELECT xid FROM vuserdata WHERE xuserxid = ? AND " + filter + ")");
			targetFilter.addParam(getMainUserId());
		}

		final Long[] targetGroupIds = request.getLongArrayParameter("GROUPIDS");
		targetFilter.appendFilter(tb.createGroupFilter(ServiceProvider.getUserService(conn), targetGroupIds, getMainUserId(), getSubUserId(), null, false));
		targetFilter.appendFilter("pci = 0 GROUP BY scannerid, scannername");

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.createAccess(AccessType.ADMIN, "groupscanners"));
		config.setIgnoreUserIdField(true);
		config.setIgnoreIdField(true);
		sendInformation(Target.class, conn, request, response, config, "count DESC", null, targetFilter.getSQL(), targetFilter.getParameters());
	}

	/**
	 * Sets the scannerid for all the targets in the given group. The group is fetched from post parameter XID and the scannerid is the post parameter SCANNERID.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request.
	 * @param response The HTTP servlet response.
	 */
	@SuppressFBWarnings(value = "SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING")
	@WebRequest(action = "SCANNERTARGETGROUP", permissions = {Permission.TARGET_MANAGEMENT})
	private void setScanners(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		request.checkInput(conn, Method.Update, ParamValidator.digitOnly("XID", Method.Update), ParamValidator.digitOnly("SCANNERID", Method.Update));
		validateMigration(conn);

		if (isSubUser() && !isSuperSubUser() && StringUtils.isEmpty(getNewLoggedOnUser().getScannerList())) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
		}

		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		final long scannerId = request.getLongParameter("SCANNERID", ScannerBusiness.LOCAL_SCANNER);
		final Long groupId = request.getLongParameter("XID", -1);

		// not allowed to change targets to AWS scanner
		final ScannerImpl scanner = ScannerImpl.getById(ScannerImpl.class, conn, Access.ADMIN, scannerId);
		if (scanner != null && scanner.isAwsScanner()) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied, "_NOT_ALLOWED_CHANGE_TO_AWS_SCANNER");
			return;
		}

		if (!ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), new Long[] {groupId}, where, params, null)) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGET_GROUP");
			return;
		}

		final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getMainUserId());
		final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
		final String filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), new TargetBusiness(getNewLoggedOnUser()));
		if (!StringUtils.isEmpty(filter)) {
			StringUtils.concatenateFilters(where, "xid IN (SELECT xid FROM vuserdata WHERE xuserxid = ? AND " + filter + ")");
			params.add(getMainUserId());
		}

		if (!hasAllScannerAccess()) {
			StringUtils.concatenateFilters(where,
					"scannerid IN (SELECT xid FROM tscanners WHERE (xid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?)) OR (groupxid IN (SELECT scannerid FROM xlinksubscanners WHERE xsubxid = ?)))");
			params.add(getSubUserId());
			params.add(getSubUserId());
		}

		if (Configuration.isHiabEnabled() && scannerId == ScannerBusiness.EXTERNAL_SCANNER_ID) {
			StringUtils.concatenateFilters(where, "(ipaddress IS NOT NULL OR (ipaddress IS NULL AND netbios IS NULL))");
		}

		if (Configuration.isHiabEnabled() && scannerId != ScannerBusiness.EXTERNAL_SCANNER_ID) {
			StringUtils.concatenateFilters(where, "aws_instance_id IS NULL");
		}

		if (Configuration.isHiabEnabled()) {
			final boolean validatePrivateIps =
					((!Configuration.isHiabEnabled() && !Configuration.isOosEnabled()) || scannerId == ScannerBusiness.EXTERNAL_SCANNER_ID) && !Configuration.isDevMode();
			StringUtils.concatenateFilters(where, "validip(ipaddress, " + (validatePrivateIps ? "true" : "false") + ")");
		}

		final String sql = "SELECT COALESCE(HOST(ipaddress), hostname, netbios) AS ipaddress FROM tuserdatas u "
				+ "WHERE EXISTS(SELECT xid FROM tuserdatas u2 WHERE u2.xuserxid=u.xuserxid AND COALESCE(host(u2.ipaddress),'') = COALESCE(host(u.ipaddress),'') "
				+ "AND (u.ipaddress IS NOT NULL OR (COALESCE(u2.netbios,'') = COALESCE(u.netbios,'') AND COALESCE(u2.hostname,'') = COALESCE(u.hostname,''))) AND u2.xid != u.xid AND u2.scannerid=?) AND scannerid != ? AND ";
		final Set<String> existingTargets = DbObject.fetchSet(conn, sql + where + " LIMIT 50", scannerId, scannerId, params);
		if (!existingTargets.isEmpty()) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_TARGET_WITH_SCANNER_EXISTS", StringUtils.join(existingTargets, ", "));
			return;
		}

		DbObject.executeUpdate(conn, "UPDATE tuserdatas SET scannerid = ? WHERE " + where, scannerId, params);

		audit(conn, groupId, AppName.TARGETGROUP, AuditMode.UPDATE, null, "_CHANGED_GROUP_ASSET_LABELING");

		conn.commit();

		sendSuccessMessage(request, response);
	}

	/**
	 * Removes all the targets in a group that the user has access to. The group xid is fetched from the post parameter XID.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request.
	 * @param response The HTTP servlet response.
	 */
	@WebRequest(action = "DELETETARGETSINGROUP", permissions = {Permission.TARGET_MANAGEMENT}, limitUpdate = true)
	private void removeTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		if (!allow(Permission.TARGET_MANAGEMENT, false)) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied);
			return;
		}

		request.checkInput(conn, listValidators);
		validateMigration(conn);

		final TargetBusiness tb = new TargetBusiness(getNewLoggedOnUser());

		final Long[] groupXid = request.getLongArrayParameter("XID");
		if (!ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), groupXid, new StringBuilder(), new ArrayList<>(), null)) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGET_GROUP");
			return;
		}

		final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getMainUserId());
		final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
		final String filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), tb);

		try {
			tb.removeRecords(conn, null, request.getLongArrayParameter("XID"), filter, request.getParameter("DELETENOTE"));
		}
		catch (final BaseRestException ex) {
			sendErrorMessage(request, response, ex.getErrorCode(), ex.getMessage());
			return;
		}

		conn.commit();
		TargetBusiness.startPostRemove();
		sendSuccessMessage(request, response);
	}

	/**
	 * Changes the asset labels on all the targets in a group.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request.
	 * @param response The HTTP servlet response.
	 */
	@WebRequest(action = "ASSETLABELTARGETGROUP", permissions = {Permission.TARGET_MANAGEMENT}, limitUpdate = true)
	private void labelTargetGroups(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		setAssetLabels(conn, request, response, true);
	}

	@WebRequest(action = "LABELTARGETS", permissions = {Permission.TARGET_MANAGEMENT})
	private void labelTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		setAssetLabels(conn, request, response, false);
	}

	/**
	 * Set label on targets in groups.
	 *
	 * @param conn Database connection
	 * @param request HTTP request
	 * @param response HTTP response
	 * @param labelGroups true if request ASSETLABELTARGETGROUP, otherwise false
	 */
	@SuppressFBWarnings(value = "SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING")
	private void setAssetLabels(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response, final boolean labelGroups)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, TargetBusiness.updateMissingDynamicsValidators);
		request.checkInput(conn, Method.Update, ParamValidator.getDynamicValidators(ServiceProvider.getUserService(conn), getMainUserId(), DynamicValidatorLocation.Target));
		validateMigration(conn);

		final String[] parameters = new String[] {
				"EXPOSED", "BUSINESSCRITICALITY", "CVSS_SR_AVAIL", "CVSS_SR_INTEG", "CVSS_SR_CONF", "CVSS_CDP", "CVSS_TD",
				"CUSTOM0", "CUSTOM1", "CUSTOM2", "CUSTOM3", "CUSTOM4", "CUSTOM5", "CUSTOM6", "CUSTOM7", "CUSTOM8", "CUSTOM9"
		};

		final StringBuilder set = new StringBuilder();
		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		for (final String param : parameters) {
			if (StringUtils.getBooleanValue(request.getParameter("PRE_" + param))) {
				if (set.length() > 0) {
					set.append(",");
				}
				set.append(param + " = ?");
				switch (param) {
					case "BUSINESSCRITICALITY":
						params.add(BusinessCriticality.valueOf(request.getParameter(param)));
						break;
					case "EXPOSED":
						params.add(request.getBooleanParameter(param));
						break;
					default:
						params.add(request.getParameter(param));
				}
			}
		}

		if (set.length() == 0) {
			sendSuccessMessage(request, response);
			return;
		}

		final Long[] groupIds = request.getLongArrayParameter("XID");

		if (labelGroups) {
			if (!ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), groupIds, where, params, null)) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGET_GROUP");
				return;
			}
			final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getMainUserId());
			final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
			final String filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), new TargetBusiness(getNewLoggedOnUser()));
			if (!StringUtils.isEmpty(filter)) {
				StringUtils.concatenateFilters(where, "xid IN (SELECT xid FROM vuserdata WHERE xuserxid = ? AND " + filter + ")");
				params.add(getMainUserId());
			}
		}
		else {
			if (groupIds == null || groupIds.length == 0) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGETS_SELECTED");
				return;
			}
			StringUtils.concatenateFilters(where, "xid =ANY (?)");
			params.add(groupIds);
		}

		DbObject.executeUpdate(conn, "UPDATE tuserdatas SET " + set + " WHERE " + where, params);

		if (labelGroups) {
			for (final long groupId : groupIds) {
				audit(conn, groupId, AppName.TARGETGROUP, AuditMode.UPDATE, null, "_CHANGED_GROUP_ASSET_LABELING");
			}
		}
		else {
			auditMultiple(conn, getNewLoggedOnUser(), org.apache.commons.lang3.StringUtils.join(groupIds, ","), AppName.TARGET.getDbName(), AuditMode.UPDATE,
					"_CHANGED_ASSET_LABELING", false, false);
		}

		conn.commit();

		sendSuccessMessage(request, response);
	}

	@WebRequest(action = "POLICIESONTARGETS", permissions = {Permission.TARGET_MANAGEMENT})
	private void policiesOnTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitList("XID"), ParamValidator.digitList("COMPLIANCESENABLED"));
		setPolicies(conn, request, response, false);
	}

	@WebRequest(action = "POLICIESONTARGETGROUP", permissions = {Permission.TARGET_MANAGEMENT})
	private void setPoliciesOnTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitList("XID"), ParamValidator.digitList("COMPLIANCESENABLED"));
		setPolicies(conn, request, response, true);
	}

	@WebRequest(action = "SERVICENOWONTARGETS", permissions = {Permission.TARGET_MANAGEMENT})
	private void serviceNowOnTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitList("XID"));
		updateServiceNow(conn, request, response, false);
	}

	@WebRequest(action = "SERVICENOWONTARGETGROUP", permissions = {Permission.TARGET_MANAGEMENT})
	private void setServiceNowOnTargets(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitList("XID"));
		updateServiceNow(conn, request, response, true);
	}

	/**
	 * Sets the policies to send notifications on.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 * @param group True if the targets are one group.
	 */
	private void setPolicies(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response, final boolean group)
			throws ParamValidationException, SQLException {
		validateMigration(conn);

		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		params.add(request.getParameter("COMPLIANCESENABLED"));

		final Long[] groupIds = request.getLongArrayParameter("XID");

		if (group) {
			if (!ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), groupIds, where, params, null)) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGET_GROUP");
				return;
			}
			final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getMainUserId());
			final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
			final String filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), new TargetBusiness(getNewLoggedOnUser()));
			if (!StringUtils.isEmpty(filter)) {
				StringUtils.concatenateFilters(where, "xid IN (SELECT xid FROM vuserdata WHERE xuserxid = ? AND " + filter + ")");
				params.add(getMainUserId());
			}
		}
		else {
			if (groupIds == null || groupIds.length == 0) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGETS_SELECTED");
				return;
			}
			StringUtils.concatenateFilters(where, "xid =ANY (?)");
			params.add(groupIds);
		}

		DbObject.executeUpdate(conn, "UPDATE tuserdatas SET compliancesenabled = ? WHERE " + where, params);

		if (group) {
			for (final long groupId : groupIds) {
				audit(conn, groupId, AppName.TARGETGROUP, AuditMode.UPDATE, null, "_CHANGED_GROUP_POLICIES");
			}
		}
		else {
			auditMultiple(conn, getNewLoggedOnUser(), StringUtils.join(groupIds, ","), AppName.TARGET.getDbName(), AuditMode.UPDATE, "_CHANGED_POLICIES", false, false);
		}

		conn.commit();

		sendSuccessMessage(request, response);
	}

	/**
	 * Updates ServiceNow targets.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 * @param group True if the targets are one group.
	 */
	private void updateServiceNow(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response, final boolean group)
			throws SQLException, ParamValidationException {
		validateMigration(conn);

		final StringBuilder where = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		where.append("snsysid != '' AND snsysid IS NOT NULL");

		final Long[] ids = request.getLongArrayParameter("XID");

		if (group) {
			if (!ServiceProvider.getTargetService(conn).getGroupConstraint(getNewLoggedOnUser(), ids, where, params, null)) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGET_GROUP");
				return;
			}
			final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getMainUserId());
			final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
			final String filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), new TargetBusiness(getNewLoggedOnUser()));
			if (!StringUtils.isEmpty(filter)) {
				StringUtils.concatenateFilters(where, "xid IN (SELECT xid FROM vuserdata WHERE xuserxid = ? AND " + filter + ")");
				params.add(getMainUserId());
			}
		}
		else {
			if (ids == null || ids.length == 0) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_TARGETS_SELECTED");
				return;
			}
			StringUtils.concatenateFilters(where, "xid = ANY(?)");
			params.add(ids);
		}

		final List<Target> targets =
				Target.fetchObjects(Target.class, conn, DbHelper.getSelect(Target.class, Access.createAccess(AccessType.ADMIN, "target"), where.toString()), params);
		if (targets.isEmpty()) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_SN_ASSETS_SELECTED");
			return;
		}

		if (!ServiceProvider.getScanSchedulingService(conn).updateSnAssets(targets, false)) {
			sendErrorMessage(request, response, null, "_SN_SOME_ASSET_NOT_UPDATED");
			return;
		}

		conn.commit();

		sendSuccessMessage(request, response);
	}

	/**
	 * Send target groups.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "TARGETGROUPDATA", permissionsOr = true, permissions = {
			Permission.USER_MANAGEMENT, Permission.COMPLIANCE, Permission.SCHEDULE_MANAGEMENT, Permission.WAS_GENERAL, Permission.REPORTING,
			Permission.TARGET_MANAGEMENT, Permission.PCI_ANY, Permission.DASHBOARD
	})
	private void sendTargetGroups(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws ParamValidationException, SQLException {
		request.checkInput(conn, listValidators);

		final long xid = request.getLongParameter("XID", -1);
		final long parentId = request.getLongParameter("XIPARENTID", -1);
		long parentUserId = request.getLongParameter("PARENTUSER", -1);
		final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
		String filter = dba.getFiltering(filteringData, FILTER_FIELDS, null, this);
		final boolean includeAll = request.getBooleanParameter("INCLUDEALLGROUP", true) && parentId == -1 && xid == -1;
		final boolean includeUngrouped = request.getBooleanParameter("INCLUDEUNGROUP", false) && xid == -1;
		final boolean pci = request.getBooleanParameter("PCI", false) || isUserPciOnly();
		final long targetId = request.getLongParameter("TARGET", -1);

		if (parentUserId > 0) {
			final LoggedOnSubUser subuser = LoggedOnSubUser.getById(LoggedOnSubUser.class, conn, Access.ADMIN, parentUserId);
			if (!subuser.hasAllTargetsAccess()) {
				filter = StringUtils.concatenateFilters(filter, "xid IN (SELECT xid FROM vsubgroup WHERE xsubxid = " + subuser.getSubUserId() + ")");
				parentUserId = subuser.getSubUserId();
			}
		}

		if (xid > 0) {
			filter = StringUtils.concatenateFilters(filter, "xid = " + xid);
		}

		if (parentId > 0) {
			filter = StringUtils.concatenateFilters(filter, "xiparentid = " + parentId);
		}

		final StringBuilder sql = new StringBuilder();
		final List<Object> params = new ArrayList<>();

		final DbObjectSendConfiguration config = new DbObjectSendConfiguration(Access.MAINUSER);
		config.setForceCount(true);
		config.setOverrideLimit(-1);

		addTargetGroupListFilter(conn, config, sql, params, getMainUserId(), parentUserId, pci, filter, targetId, includeAll, includeUngrouped);

		String orderby = dba.getSorting(conn, request.getGroupSortParams(), getMainUserId(), FILTER_FIELDS.keySet());
		orderby = StringUtils.concatenateFilters("sortorder", orderby, ",");

		sendInformation(TargetGroup.class, conn, request, response, ROWSET_TAG, ROW_TAG, config, orderby, null, sql.toString(), params);
	}

	@Override
	public CustomFilter getCustomFilter() {
		return new TargetGroupCustomFilter();
	}

	/**
	 * Add filters for target groups.
	 *
	 * @param conn Database connection
	 * @param config Database send configuration
	 * @param filter Buffer where filter will be added
	 * @param params List where parameters will be added
	 * @param userId User id
	 * @param parentUserId Parent user id
	 * @param pci true if pci, otherwise false
	 * @param filters Target group filters
	 * @param targetId Target id
	 * @param includeAll true to include group "All targets", otherwise false
	 * @param includeUngrouped true to include group "Ungrouped", otherwise false
	 */
	private void addTargetGroupListFilter(final Connection conn, final DbObjectSendConfiguration config, final StringBuilder filter, final List<Object> params,
										  final long userId, final long parentUserId, final boolean pci,
										  final String filters, final long targetId, final boolean includeAll, final boolean includeUngrouped) throws SQLException {
		final List<Object> tableParams = new ArrayList<>();

		StringUtils.concatenateFilters(filter, "xuserxid = ? AND pci = ?");
		params.add(userId);
		params.add(pci ? 1 : 0);

		if (!hasAllTargetsAccess()) {
			StringUtils.concatenateFilters(filter, "xid IN (SELECT xid FROM vsubgroup WHERE xsubxid = ?)");
			params.add(getSubUserId());
		}

		if (targetId > 0) {
			StringUtils.concatenateFilters(filter,
					"EXISTS(SELECT xid FROM xlinkgeneric lg WHERE xid=g.xid AND xipxid=? AND xid IN (SELECT xp.xid FROM xgenericpaths xp WHERE parentxid=lg.xid))");
			params.add(targetId);
		}

		if (!StringUtils.isEmpty(filters)) {
			StringUtils.concatenateFilters(filter, filters);
		}

		String sql = "";
		final LoggedOnSubUser subuser = parentUserId != -1 ? LoggedOnSubUser.getById(LoggedOnSubUser.class, conn, Access.ADMIN, parentUserId) : null;
		if (includeAll) {
			boolean notAllTargets = !hasAllTargetsAccess();
			if (parentUserId != -1) {
				notAllTargets = !subuser.hasAllTargetsAccess();
			}
			sql += "SELECT -1 AS xid, 1 AS sortorder, ? AS xuserxid, -3 AS xiparentid, ? AS name, (SELECT COUNT(DISTINCT xid) FROM tuserdatas WHERE xuserxid = ? AND pci = ?";
			tableParams.add(userId);
			tableParams.add(MessageBusiness.getMessageString("_ALLTARGETS", getUserLanguage(), "MESSAGES"));
			tableParams.add(userId);
			tableParams.add(pci ? 1 : 0);
			if (notAllTargets) {
				sql += " AND xid IN (SELECT xipxid FROM vsubhost WHERE xsubxid=?)";
				tableParams.add(parentUserId != -1 ? parentUserId : getSubUserId());
			}
			sql += ") AS icount, false AS rulebased, false AS reportbased, '' AS description, '' AS rule UNION ALL ";
		}
		if (includeUngrouped) {
			final boolean notAllTargets = !hasAllTargetsAccess();
			sql +=
					"SELECT -2 AS xid, 2 AS sortorder, ? AS xuserxid, -1 AS xiparentid, ? AS name, (SELECT COUNT(DISTINCT xid) FROM tuserdatas u LEFT JOIN targetscandata ts ON u.xid = ts.targetid WHERE xuserxid = ? AND pci = ?";
			tableParams.add(userId);
			tableParams.add(MessageBusiness.getMessageString("_UNGROUPED", getUserLanguage(), "MESSAGES"));
			tableParams.add(userId);
			tableParams.add(pci ? 1 : 0);
			if (notAllTargets) {
				sql += " AND xid IN (SELECT xipxid FROM vsubhost WHERE xsubxid=?)";
				tableParams.add(getSubUserId());
			}
			sql += " AND ts.ungrouped) AS icount, false AS rulebased, false AS reportbased, '' AS description, '' AS rule UNION ALL ";
		}

		String parentIdSql = "xiparentid";
		if (parentUserId != -1 || !hasAllTargetsAccess()) {
			if (subuser == null || !subuser.hasAllTargetsAccess()) {
				parentIdSql = "COALESCE((SELECT xid FROM vsubgroup sg WHERE xsubxid=? AND sg.xid=xiparentid), -1) AS xiparentid";
				tableParams.add(parentUserId != -1 ? parentUserId : getSubUserId());
			}
		}

		sql += "SELECT xid, 3 AS sortorder, xuserxid, " + parentIdSql + ", name, icount, rulebased, reportbased, description, rule";

		config.setSelectOverride(sql);
		config.setTableParams(tableParams);
	}

	/**
	 * Update target group.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "UPDATETARGETGROUPDATA", permissions = {Permission.TARGET_MANAGEMENT}, readOnly = false)
	private void updateTargetGroups(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, listValidators);

		if (!hasAllTargetsAccess() && StringUtils.getBooleanValue(request.getParameter("RULEBASED"), false)) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied, "_NOT_ALLOWED_ADD_GROUP");
			return;
		}

		if (StringUtils.getIntValue(request.getParameter("XID"), -1) < -1 || StringUtils.getIntValue(request.getParameter("XIPARENTID"), -1) < -1) {
			sendErrorMessage(request, response, ErrorCode.AccessDenied, "_NOT_ALLOWED_CHANGE_SYSTEMGROUPS");
			return;
		}

		final String addTargets = request.getParameter("ADDTARGETLIST");
		final String removeTargets = request.getParameter("REMOVETARGETLIST");
		final boolean reportFilter = request.getBooleanParameter("REPORTFILTER", false);
		final boolean ruleBased = request.getBooleanParameter("RULEBASED", false);
		final boolean pci = request.getBooleanParameter("PCI", false);

		if (!pci) {
			validateMigration(conn);
		}

		if (!StringUtils.isEmpty(addTargets) || !StringUtils.isEmpty(removeTargets)) {
			String resultMessage = null;
			final StringTokenizer st = new StringTokenizer(StringUtils.setEmpty(request.getParameter("XID"), ""), ",");
			while (st.hasMoreTokens()) {
				final long id = StringUtils.getLongValue(st.nextToken());
				final boolean ruleBasedGroup = ruleBased || DbObject.executeCountQuery(conn, "SELECT COUNT(xid) FROM xlinkrules WHERE xid=?", id) > 0;
				if (ruleBasedGroup) {
					sendErrorMessage(request, response, ErrorCode.InputValidationFailed);
					return;
				}

				String filter = null;
				if (("-1".equals(removeTargets) || "-1".equals(addTargets))) {
					final TargetBusiness tb = new TargetBusiness(getNewLoggedOnUser());
					final Map<String, FieldDefinition> filters = TargetBusiness.getFilters(conn, getNewLoggedOnUser().getMainUserId());
					final FilteringData filteringData = FilteringDataFactory.getFilteringData(conn, request);
					filter = dba.getFiltering(filteringData, filters, getNewLoggedOnUser(), tb);
				}
				final Pair<Boolean, String> result =
						ServiceProvider.getTargetService(conn)
								.changeTargetGroupsForTargets(getNewLoggedOnUser(), addTargets, removeTargets, false, pci, request.getBooleanParameter("MOVE", false), id,
										request.getLongArrayParameter("GROUPXID"), filter);
				if (!result.getLeft()) {
					sendErrorMessage(request, response, ErrorCode.InputValidationFailed, result.getRight());
					return;
				}
				if (!StringUtils.isEmpty(result.getRight())) {
					resultMessage = result.getRight();
				}
			}

			conn.commit();

			sendSuccessMessage(request, response, null, null, resultMessage);
			return;
		}

		final HashSet<String> missingColumns = new HashSet<>();

		TargetGroup group = TargetGroup.fromRequest(TargetGroup.class, request, missingColumns);

		missingColumns.remove("id");
		group.setUserId(getMainUserId());
		missingColumns.remove("userid");
		group.setUpdated(new Date());
		missingColumns.remove("updated");
		group.setUpdatorId(isSubUser() ? -getSubUserId() : getMainUserId());
		missingColumns.remove("updatorid");

		if (isUserPciOnly() || group.isPci()) {
			group.setPci(true);
			missingColumns.remove("pci");
		}
		else {
			missingColumns.add("pci");
		}

		if (group.isPci()) {
			group.setParentId(-1);
			missingColumns.remove("parentid");
		}
		else if (group.getId() != -1 && group.getParentId() > 0) {
			if (DbObject.executeCountQuery(conn, "SELECT COUNT(parentxid) FROM xgenericpaths WHERE xid=? AND parentxid=?", group.getParentId(), group.getId()) > 0) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_CHILD_OF_SELF");
				return;
			}
		}

		TargetGroup child = null;
		if (group.getId() > 0) {
			child = TargetGroup.getById(TargetGroup.class, conn, group.getId());
		}
		final TargetGroup parent = TargetGroup.getById(TargetGroup.class, conn, group.getParentId());
		if (parent != null) {
			if (parent.isReportBased() && ((child != null && !child.isReportBased()) || (child == null && !reportFilter))) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_DYNAMIC_GROUPS_CHILDREN_ONLY");
				return;
			}
			if (!parent.isReportBased() && parent.isRuleBased() && ((child != null && !child.isRuleBased()) || (child == null && !ruleBased))) {
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_DYNAMIC_GROUPS_CHILDREN_ONLY");
				return;
			}
		}

		if (reportFilter && group.getId() == -1) {
			final String groups = StringUtils.setEmpty(StringUtils.trimChar(request.getParameter("GROUPS"), true, ','), "");
			if (groups.split(",").length == 1) {
				group.setParentId(StringUtils.getLongValue(groups, -1));
				missingColumns.remove("parentid");
			}
		}

		if (missingColumns.contains("parentid") && group.getId() <= 0) {
			group.setParentId(-1);
			missingColumns.remove("parentid");
		}

		if (reportFilter) {
			group.setReportBased(true);
			missingColumns.remove("reportbased");
		}

		final long id = group.save(conn, Access.ADMIN, null, missingColumns.toArray(new String[0]));

		if (!hasAllTargetsAccess()) {
			if (DbObject.executeCountQuery(conn, "SELECT xiparentid FROM tgenericgroups WHERE xid=? AND xiparentid IN (SELECT xid FROM vsubgroup WHERE xsubxid=?)", id,
					getSubUserId()) <= 0) {
				sendErrorMessage(request, response, ErrorCode.AccessDenied);
				return;
			}
		}

		if (ruleBased) {
			try {
				if (insertDynamicGroupRules(conn, request, id) == 0 && reportFilter) {
					sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_NO_VALID_REPORT_FILTERS");
					return;
				}
			}
			catch (final IllegalArgumentException e) {
				LOG.info("Error creating dynamicgroup", e);
				sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_TODAY_FILTER_NOT_ALLOWED");
				return;
			}
			DbObject.execute(conn, "SELECT xlinkCreateDynamicGroup(?, ?)", getMainUserId(), id);
		}

		final JSONObject json = new JSONObject();
		json.put("XID", id);

		if (!group.wasUpdate()) {
			group = TargetGroup.getById(TargetGroup.class, conn, Access.ADMIN, id);
			if (group != null) {
				json.put("XIPARENTID", group.getParentId());
				json.put("NAME", group.getName());
				json.put("RULEBASED", group.isRuleBased());
				json.put("REPORTBASED", group.isReportBased());
				json.put("ICOUNT", group.getCount());
			}

			audit(conn, id, AppName.TARGETGROUP, AuditMode.ADD, null, null, group != null ? group.isPci() : false);
		}
		else {
			audit(conn, id, AppName.TARGETGROUP, AuditMode.UPDATE, null, null, group.isPci());
		}

		conn.commit();

		sendSuccessMessage(request, null, json.toString(), response);
	}

	/**
	 * Determines if a field is ok to use as a reportbased dynamic group filter.
	 *
	 * @param field The field name.
	 * @return <code>true</code> if ok to use, <code>false</code> otherwise.
	 */
	private boolean isAllowedReportFilter(final String field) {
		final String[] disallowed = new String[] {"DREPORTDATE", "DATE", "DREPORTENDDATE"};
		for (final String check : disallowed) {
			if (check.equals(field)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * Creates the rules for a dynamic group based on the http request filters.
	 *
	 * @param conn A database connection.
	 * @param request The HTTP servlet request.
	 * @param xid The id of the group.
	 * @return The count of rules inserted.
	 * @throws SQLException On database errors.
	 */
	protected int insertDynamicGroupRules(final Connection conn, final HttpRequestWrapper request, final long xid) throws SQLException, ParamValidationException {
		int count = 0;
		final boolean reportBased = request.getBooleanParameter("REPORTFILTER", false);
		final List<FilterField> filterFields = FilteringUtils.extractFilterData(request, conn, FilterName.FILTER);
		if (filterFields == null || filterFields.size() == 0) {
			return 0;
		}

		try (final PreparedStatement ps = conn.prepareStatement("INSERT INTO xlinkrules (xid, field, value, operator, definedquery, dynamic) VALUES (?, ?, ?, ?, ?, ?)")) {
			ps.setLong(1, xid);

			DbObject.executeUpdate(conn, "DELETE FROM xlinkrules WHERE xid = ?", xid);

			final Map<String, FieldDefinition> filterDefinitions =
					reportBased ? ReportingBusiness.getFilters(conn, getMainUserId(), request) : TargetBusiness.getFilters(conn, getMainUserId());
			for (final FilterField filterField : filterFields) {
				String field = filterField.getFieldName();
				if (!reportBased || isAllowedReportFilter(field)) {
					final FieldDefinition definition = filterDefinitions.get(field.toUpperCase());

					final String orgField = field;
					final FieldType type = filterField.getFieldType();
					final String value = filterField.getValue();
					final String operator = filterField.getComparison();

					ps.setString(2, field);
					ps.setBoolean(6, false);

					if (definition != null && !StringUtils.isEmpty(definition.getFieldMapping(null)) && !((type == FieldType.NUMERIC || type == FieldType.BOOLEAN)
							&& field.matches("CUSTOM."))) {
						field = definition.getFieldMapping(null);
					}
					int maxlen = -1;
					if (definition != null && definition.getMaxLength() != null) {
						maxlen = definition.getMaxLength();
					}

					if (!reportBased) {
						ps.setString(2, field);
					}

					if (type == FieldType.STRING || type == FieldType.DATE || type == FieldType.NUMERIC || type == FieldType.BOOLEAN) {
						ps.setString(3, value);
						ps.setString(4, getInternalComparisonOperator(operator != null ? operator.replace("range-", "") : null, type));
						if (reportBased) {
							final StringBuilder query = new StringBuilder();
							if (field.equals("QUERY")) {
								final NativeSqlFragment searchQuerySql = new SearchQuerySqlFragment(getMainUserId(), filterField, true);
								try (final PreparedStatement st = conn.prepareStatement(searchQuerySql.getSQL())) {
									DbObject.addParameter(conn, st, 1, searchQuerySql.getParameters());
									query.append(st.toString());
								}
							}
							else if (type == FieldType.STRING) {
								FilteringSqlUtils.addStringFilter(query, field, maxlen, type, value, operator, new ReportingCustomFilter(), getNewLoggedOnUser());
								if (query.length() == 0) {
									throw new ParamValidationException("Cannot use filter for dynamic rules", null, null);
								}
							}
							else if (type == FieldType.DATE) {
								if (operator.matches("range-.*")) { // when days filter within date filter
									final String dateField = field.replaceAll(orgField, "(abs(date_part('days', (now() - " + orgField + "::date))))");
									FilteringSqlUtils.addNumericFilter(query, dateField, maxlen, type, value, operator.replace("range-", ""));
									ps.setBoolean(6, true);
								}
								else {
									query.append(field).append("::date").append(getInternalComparisonOperator(operator, type)).append("'").append(value).append("'::date");
								}
							}
							else if (type == FieldType.NUMERIC && field.matches("CUSTOM.") && !StringUtils.isEmpty(definition.getFieldMapping(null))) {
								FilteringSqlUtils.addNumericFilter(query, definition.getFieldMapping(null), maxlen, type, value, operator);
							}
							else if (type == FieldType.BOOLEAN && field.matches("CUSTOM.") && !StringUtils.isEmpty(definition.getFieldMapping(null))) {
								query.append(definition.getFieldMapping(null)).append("=").append(StringUtils.getBooleanIntValue(value, 0));
							}
							else if (type == FieldType.BOOLEAN) {
								query.append(field);
								query.append("=");
								query.append(definition.useBooleans() ? value : StringUtils.getBooleanIntValue(value, 0));
							}
							else {
								FilteringSqlUtils.addNumericFilter(query, field, maxlen, type, value, operator);
							}
							ps.setString(5, query.toString());
						}
						else if (type == FieldType.STRING) {
							final StringBuilder filter = new StringBuilder();
							FilteringSqlUtils.addStringFilter(filter, field, maxlen, type, value, operator, new TargetCustomFilter(), getNewLoggedOnUser());
							ps.setString(5, "(" + filter + ")");
						}
						else if (type == FieldType.DATE && (operator.equalsIgnoreCase("null") || operator.matches("range-.*"))) {
							if (operator.matches("range-.*")) { // when days filter within date filter
								final StringBuilder query = new StringBuilder();
								final String dateField = field.replaceAll(orgField, "(abs(date_part('days', (now() - " + orgField + "::date))))");
								FilteringSqlUtils.addNumericFilter(query, dateField, maxlen, type, value, operator.replace("range-", ""));
								ps.setString(5, query.toString());
								ps.setBoolean(6, true);
							}
							else {
								ps.setString(5, field + " is " + getInternalComparisonOperator(operator, type));
							}
						}
						else if (type == FieldType.DATE) {
							ps.setString(2, orgField);
							ps.setString(5, field + "::date " + getInternalComparisonOperator(operator, type) + "'" + value + "'::date");
							if ("NEXTSCANDATE".equals(field)) {
								ps.setBoolean(6, true);
							}
						}
						else if (type == FieldType.NUMERIC && field.matches("CUSTOM.") && !StringUtils.isEmpty(definition.getFieldMapping(null))) {
							ps.setString(5, definition.getFieldMapping(null) + getInternalComparisonOperator(operator, type) + value);
						}
						else if (type == FieldType.BOOLEAN && field.matches("CUSTOM.") && !StringUtils.isEmpty(definition.getFieldMapping(null))) {
							ps.setString(5, definition.getFieldMapping(null) + "=" + StringUtils.getBooleanIntValue(value, 0));
						}
						else if (type == FieldType.BOOLEAN) {
							ps.setString(5, field + "=" + (definition.useBooleans() ? value : StringUtils.getBooleanIntValue(value, 0)));
						}
						else {
							ps.setNull(5, Types.VARCHAR);
						}
						ps.execute();
						count++;
					}
					else if (type == FieldType.LIST) {
						ps.setString(3, value.replaceAll(",", "|"));
						ps.setString(4, "~");
						final StringBuilder query = new StringBuilder();
						if (definition.getListDataType() == ListDataType.STRING) {
							query.append("LOWER(").append(field).append(") IN (LOWER('").append(value.replaceAll(",", "'), LOWER('")).append("'))");
						}
						else {
							query.append(field).append(" IN ('").append(value.replaceAll(",", "','")).append("')");
						}
						ps.setString(5, query.toString());
						ps.execute();
						count++;
					}
					else {
						LOG.warn("Unknow filter type, ignoring");
					}
				}
			}
		}

		return count;
	}

	/**
	 * Returns the sql comparison operater for a filter.
	 *
	 * @param operator The filter operator
	 * @param type The type of data
	 * @return lt -&gt; &lt; gt -&gt; &gt; eq -&gt; (= || ~), null -&gt; ~
	 * @throws IllegalArgumentException On any unknown input.
	 */
	private String getInternalComparisonOperator(final String operator, final FieldType type) {
		if (operator == null || "null".equalsIgnoreCase(operator)) {
			return type == FieldType.BOOLEAN ? "==" : (type == FieldType.DATE ? operator : "~");
		}
		if ("lt".equals(operator)) {
			return "<";
		}
		if ("gt".equals(operator)) {
			return ">";
		}
		if ("eq".equals(operator)) {
			return "=";
		}
		if ("ne".equals(operator)) {
			return "!=";
		}
		if ("none".equals(operator)) {
			return "!~~*";
		}
		if ("all".equals(operator)) {
			return "~~*";
		}
		if ("any".equals(operator)) {
			return "~~*?";
		}
		throw new IllegalArgumentException("Invalid comparison operator: " + operator);
	}

	/**
	 * Remove target group.
	 *
	 * @param conn Database connection
	 * @param request The HTTP servlet request
	 * @param response The HTTP servlet response
	 */
	@WebRequest(action = "REMOVETARGETGROUPDATA", permissions = {Permission.TARGET_MANAGEMENT}, readOnly = false)
	private void removeTargetGroups(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, listValidators);
		request.checkInput(conn, Method.Delete, ParamValidator.digitList("XID", Method.Delete));
		validateMigration(conn);

		long count = 0;
		final Long[] ids = DbHelper.createIdArray(request.getParameter("XID"));
		for (final Long id : ids) {
			if (ServiceProvider.getTargetService(conn).removeTargetGroup(getNewLoggedOnUser(), id, request.getParameter("DELETENOTE"))) {
				count++;
			}
		}

		if (count > 0) {
			conn.commit();
			TargetBusiness.startPostRemove();
		}

		sendSuccessMessage(request, response);
	}
}
