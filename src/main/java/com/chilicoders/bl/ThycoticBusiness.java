package com.chilicoders.bl;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.chilicoders.app.WebRequest;
import com.chilicoders.authentication.thycotic.model.ThycoticAuthentication;
import com.chilicoders.authentication.thycotic.model.ThycoticServerConfiguration;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.authentication.thycotic.api.ThycoticConnectionStatus;
import com.chilicoders.core.authentication.thycotic.api.ThycoticService;
import com.chilicoders.core.authentication.thycotic.api.ThycoticValidator;
import com.chilicoders.core.authentication.thycotic.model.api.ThycoticAuthenticationInterface;
import com.chilicoders.core.authentication.thycotic.model.api.ThycoticServerConfigurationInterface;
import com.chilicoders.core.configuration.common.Constants;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.Features;
import com.chilicoders.db.objects.XmlAble;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.HttpRequestWrapper;
import com.chilicoders.util.ParamValidator;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TwoWayEncoding;
import com.chilicoders.util.xml.XMLDoc;

public final class ThycoticBusiness extends BusinessObject {

	/**
	 * Constructor.
	 */
	public ThycoticBusiness() {
		super();
	}

	public ThycoticBusiness(final BaseLoggedOnUser user) {
		super(user);
	}

	public ThycoticBusiness(final HttpSession session) throws SQLException {
		super(session);
	}

	/**
	 * Update thycotic server configuration
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "UPDATETHYCOTIC")
	private void updateThycoticServerConfigs(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.xml("THYCOTICCONFIG"), ParamValidator.booleanValue("THYCOTICENABLED"));

		final boolean enabled = request.getBooleanParameter("THYCOTICENABLED", false);
		final String configsXml = request.getParameter("THYCOTICCONFIG");

		if (enabled && StringUtils.isEmpty(configsXml)) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_THYCOTIC_CONFIGURATION_REQUIRED");
			return;
		}

		final Features features = Features.getById(Features.class, conn, Access.ADMIN, getMainUserId());
		if (features != null && enabled != features.isThycoticEnabled()) {
			audit(conn, getMainUserId(), AppName.USER, AuditMode.UPDATE, null, "Thycotic: " + (enabled ? "enabled" : "disabled"));
			features.setThycoticEnabled(enabled);
			features.save(conn);
			conn.commit();
			getNewLoggedOnUser().setThycoticEnabled(enabled);
		}

		if (!StringUtils.isEmpty(configsXml)) {
			final ThycoticService thycoticService = ServiceProvider.getThycoticService(conn);
			final Map<Long, ThycoticServerConfigurationInterface> previousConfigsMap = new HashMap<>();
			final List<? extends ThycoticServerConfigurationInterface> previousConfigs = thycoticService.getServerConfigurations(getMainUserId());
			for (final ThycoticServerConfigurationInterface config : previousConfigs) {
				previousConfigsMap.put(config.getId(), config);
			}

			final List<ThycoticServerConfiguration> configs = new ArrayList<>();
			final XMLDoc doc = new XMLDoc(configsXml, "THYCOTICCONFIGS", "THYCOTICCONFIG");
			String id = doc.getFirstValueOf("THYCOTICID");
			while (id != null) {
				final ThycoticServerConfiguration config = new ThycoticServerConfiguration();
				final Long configId = doc.getLongValueOf("THYCOTICID", -1);
				config.setId(configId);
				config.setUserId(getMainUserId());
				config.setName(doc.getValueOf("NAME"));
				config.setServerURL(doc.getValueOf("SERVERURL"));
				config.setUsername(doc.getValueOf("USERNAME"));
				config.setOrganization(doc.getValueOf("ORGANIZATION"));
				config.setDomain(doc.getValueOf("DOMAIN"));
				config.setTenant(doc.getValueOf("TENANT"));
				config.setPassword(getEncodedPassword(doc.getValueOf("PASSWORD"), previousConfigsMap.get(configId)));
				config.setIgnoreCerts(doc.getBooleanValueOf("IGNORECERTS", false));
				final String validationError = ThycoticValidator.validateServerConfiguration(config);
				if (!StringUtils.isEmpty(validationError)) {
					sendErrorMessage(request, response, ErrorCode.InputValidationFailed, validationError);
					return;
				}
				configs.add(config);
				id = doc.getNextValueOf("THYCOTICID");
			}
			thycoticService.saveServerConfigurations(getMainUserId(), configs);
		}

		sendSuccessMessage(request, response);
	}

	/**
	 * Retrieve Thycotic server configurations.
	 *
	 * @param conn Database connection.
	 * @param request HTTP servlet request.
	 * @param response HTTP servlet response.
	 */
	@WebRequest(action = "THYCOTICSERVERCONFIG")
	private void sendThycoticServerConfigs(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.noParametersRequired();

		final ThycoticService thycoticService = ServiceProvider.getThycoticService(conn);
		final List<? extends ThycoticServerConfigurationInterface> configs = thycoticService.getServerConfigurations(getMainUserId());
		for (final ThycoticServerConfigurationInterface config : configs) {
			config.setPassword(Constants.PASSWORD_MASK);
		}
		final List<ThycoticServerConfiguration> result = configs.stream().map(item -> (ThycoticServerConfiguration) item).collect(Collectors.toList());

		XmlAble.sendInformation(result, configs.size(), request, response, getNewLoggedOnUser(), Access.ADMIN);
	}

	/**
	 * Test Thycotic server configurations.
	 *
	 * @param conn Database connection.
	 * @param request HTTP servlet request.
	 * @param response HTTP servlet response.
	 */
	@WebRequest(action = "TESTTHYCOTICSERVER")
	private void testThycoticServerConfig(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitOnly("THYCOTICID"),
				ParamValidator.printable("NAME"),
				ParamValidator.printable("SERVERURL"),
				ParamValidator.printable("USERNAME"),
				ParamValidator.printable("PASSWORD"),
				ParamValidator.printable("ORGANIZATION"),
				ParamValidator.printable("DOMAIN"),
				ParamValidator.printable("TENANT"),
				ParamValidator.booleanValue("IGNORECERTS"));

		final ThycoticService thycoticService = ServiceProvider.getThycoticService(conn);

		final Long id = request.getLongParameter("THYCOTICID", -1);
		final ThycoticServerConfigurationInterface previous = id >= 0 ? thycoticService.getServerConfiguration(getMainUserId(), id) : null;

		final ThycoticServerConfiguration serverConfig = ThycoticServerConfiguration.builder()
				.userId(getMainUserId())
				.name(request.getParameter("NAME"))
				.serverURL(request.getParameter("SERVERURL"))
				.username(request.getParameter("USERNAME"))
				.password(getEncodedPassword(request.getParameter("PASSWORD"), previous))
				.organization(request.getParameter("ORGANIZATION"))
				.domain(request.getParameter("DOMAIN"))
				.tenant(request.getParameter("TENANT"))
				.ignoreCerts(request.getBooleanParameter("IGNORECERTS", false))
				.build();
		final String validationError = ThycoticValidator.validateServerConfiguration(serverConfig);
		if (!StringUtils.isEmpty(validationError)) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, validationError);
			return;
		}

		final ThycoticConnectionStatus status = thycoticService.testServerConnection(serverConfig, ServiceProvider.getConfigService());
		if (status != ThycoticConnectionStatus.OK) {
			sendErrorMessage(request, response, ErrorCode.ConnectionFailed, "_THYCOTIC_CONNECT_FAILURE");
			return;
		}

		sendSuccessMessage(request, response);
	}

	/**
	 * Gets the encoded password based on the value received from the request and the previous saved value.
	 *
	 * @param passedPassword the password passed in the request
	 * @param previous the saved configuration
	 * @return the encoded password
	 */
	private String getEncodedPassword(final String passedPassword, final ThycoticServerConfigurationInterface previous) {
		String password = null;
		if (Constants.PASSWORD_MASK.equals(passedPassword) && previous != null) {
			password = previous.getPassword();
		}
		else if (!StringUtils.isEmpty(passedPassword)) {
			password = TwoWayEncoding.encode(passedPassword, ServiceProvider.getConfigService());
		}
		return password;
	}

	/**
	 * Get Thycotic authentication details.
	 *
	 * @param conn Database connection.
	 * @param request HTTP servlet request.
	 * @param response HTTP servlet response.
	 */
	@WebRequest(action = "THYCOTICAUTH")
	private void sendThycoticConfiguration(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SQLException {
		request.checkInput(conn, ParamValidator.digitOnly("SCANPOLICYID"),
				ParamValidator.booleanValue("SSH"),
				ParamValidator.booleanValue("SMB"));

		final ThycoticService thycoticService = ServiceProvider.getThycoticService(conn);
		final boolean isSsh = request.getBooleanParameter("SSH");
		final boolean isSmb = request.getBooleanParameter("SMB");
		final long scanPolicyId = request.getLongParameter("SCANPOLICYID", -1);

		ThycoticAuthenticationInterface auth = null;
		if (isSsh) {
			auth = thycoticService.getSshAuthenticationForScanPolicy(getMainUserId(), scanPolicyId);
		}
		else if (isSmb) {
			auth = thycoticService.getSmbAuthenticationForScanPolicy(getMainUserId(), scanPolicyId);
		}
		if (auth == null) {
			auth = new ThycoticAuthentication();
		}

		XmlAble.sendInformation((XmlAble) auth, "THYCOTICAUTHS", "THYCOTICAUTH", request, response, getNewLoggedOnUser(),
				hasRoles(Role.Administrator, Role.Support) ? Access.ADMIN : Access.MAINUSER, null, null);
	}
}
