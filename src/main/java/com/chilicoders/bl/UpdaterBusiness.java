package com.chilicoders.bl;

import static com.chilicoders.cache.ConnectionCache.PROPERTY_PREFIX;
import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateException;
import java.security.spec.InvalidKeySpecException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPException;
import org.bouncycastle.operator.OperatorCreationException;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationVersion;
import org.flywaydb.core.api.configuration.FluentConfiguration;

import com.chilicoders.app.Hiab;
import com.chilicoders.app.WebRequest;
import com.chilicoders.app.XMLAPI;
import com.chilicoders.bl.objects.LoginAttempt;
import com.chilicoders.cache.DbAccess;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationBooleanKey;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.libellum.ServiceIdentity;
import com.chilicoders.core.libellum.ServiceIdentity.Service;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.DbObject;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.HiabStat;
import com.chilicoders.db.objects.LoggedOnSubUser;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.hiab.update.UpdateKeyInformation;
import com.chilicoders.hiab.update.UpdateUtil;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.model.Event.O24Event;
import com.chilicoders.model.EventType;
import com.chilicoders.model.events.properties.EventLoginProperties;
import com.chilicoders.model.events.properties.EventProperties;
import com.chilicoders.rest.models.Appliance;
import com.chilicoders.rest.models.CertificateCreationRequest;
import com.chilicoders.util.CertificateUtils;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.Executor;
import com.chilicoders.util.HttpRequestWrapper;
import com.chilicoders.util.HttpsThread;
import com.chilicoders.util.LibellumUtils;
import com.chilicoders.util.MarshallingUtils;
import com.chilicoders.util.ParamValidator;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TwoWayEncoding;
import com.chilicoders.util.log4j.Log4jUtils;
import com.chilicoders.util.thread.TimerHandler;
import com.splunk.HttpService;
import com.splunk.SSLSecurityProtocol;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class UpdaterBusiness extends BusinessObject {
	private static final Map<String, Integer> productTypes = new HashMap<>();

	private static final Logger LOG = LogManager.getLogger(UpdaterBusiness.class);

	// Keep a list of when an enrollment is attempted so that we can introduce a timeout when the user cannot log in
	private static final Map<String, LoginAttempt> enrollAttempts = new HashMap<>();

	static {
		productTypes.put("outscan", 1);
		productTypes.put("hiab", 2);
		productTypes.put("pen", 3);
		productTypes.put("oos", 4);
		productTypes.put("pci", 5);
		productTypes.put("dev", 6);
	}

	/**
	 * Updates relevant {@link HiabStat} with information in the request
	 *
	 * @param conn A database connection.
	 * @param identityId The subject-identifier/UUID of the HIAB that is being verified and updated
	 * @param hiabStat {@link HiabStat} object of the HIAB needs to be verified and updated
	 * @param request Http request.
	 * @param response Http response.
	 * @return {@code false} if validation fails
	 */
	private boolean updateHiabStat(final Connection conn, final String identityId, final HiabStat hiabStat, final HttpRequestWrapper request,
								   final HttpServletResponse response) throws SQLException {
		if (hiabStat == null) {
			logUpdateError(conn, request, response, "Identifier not found", null, -1, identityId, ErrorCode.InputValidationFailed);
			return false;
		}

		if (hiabStat.isRevoked()) {
			logUpdateError(conn, request, response, "Hiab has been revoked", null, hiabStat.getUserId(), identityId, ErrorCode.ServerNotRegistered);
			return false;
		}

		if (!StringUtils.compareIgnoreCase(request.getParameter("MAC"), hiabStat.getMac())) {
			logUpdateError(conn, request, response, "Incorrect MAC address", request.getParameter("MAC"), hiabStat.getUserId(), identityId, ErrorCode.InputValidationFailed);
			return false;
		}

		final StringBuilder query = new StringBuilder("UPDATE thiabstats SET os='CentOS'");
		final List<Object> parameters = new ArrayList<>();

		if (!StringUtils.isEmpty(request.getParameter("LASTUPDATE"))) {
			query.append(", lastupdate=?::TIMESTAMP");
			parameters.add(request.getParameter("LASTUPDATE"));

			final Date lastUpdate = DateUtils.parseTimeDate(request.getParameter("LASTUPDATE"));
			if (lastUpdate != null && !UpdateUtil.isUpdateAllowed(lastUpdate.toInstant())) {
				DbObject.executeUpdate(conn, "UPDATE thiabstats SET revoked = true, uuid = 'DELETED_' || uuid WHERE id = ?", hiabStat.getId());

				final String errorMessage = MessageBusiness.getMessageString("_REVOKED_TOO_OLD_APPLIANCE", (String) request.getSession().getAttribute("LANGUAGE"), "MESSAGES");
				audit(conn, hiabStat.getUserId(), AppName.HIAB, AuditMode.UPDATE, null, errorMessage, hiabStat.getUserId(), true, false, null, null, false);
				logUpdateError(conn, request, response, errorMessage, null, hiabStat.getUserId(), identityId, ErrorCode.ServerNotRegistered);

				return false;
			}
		}

		if (!StringUtils.isEmpty(request.getParameter("PUBLICSBC"))) {
			query.append(", publicsbc=?");
			parameters.add(request.getParameter("PUBLICSBC"));
		}

		if (!StringUtils.isEmpty(request.getParameter("INSTALLEDPACKAGES"))) {
			query.append(", installedpackages=?");
			parameters.add(request.getParameter("INSTALLEDPACKAGES"));
		}

		if (!StringUtils.isEmpty(request.getParameter("VERSION"))) {
			query.append(", version=?");
			parameters.add(request.getParameter("VERSION"));
		}

		query.append(" WHERE id = ?");
		parameters.add(hiabStat.getId());
		DbObject.executeUpdate(conn, query.toString(), parameters);

		return true;
	}

	/**
	 * Log enroll error.
	 *
	 * @param conn Database connection.
	 * @param request Http request.
	 * @param response Http response.
	 * @param message Message to log.
	 * @param userId User id.
	 */
	private void logEnrollError(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response, final String message, final long userId)
			throws SQLException {
		DbObject.execute(conn, "INSERT INTO userver_enroll_error(tstamp, emsg, assignee) VALUES (NOW(), ?, ?)", message, userId);
		conn.commit();
		sendErrorMessage(request, response, ErrorCode.InternalServerError, message);
	}

	/**
	 * Log update error.
	 *
	 * @param conn Database connection.
	 * @param request Http request.
	 * @param response Http response.
	 * @param message Message to log.
	 * @param additionData Additional information to log.
	 * @param userId User id.
	 * @param subjectKeyId Subject key id from hiab.
	 * @param errorCode Errorcode to use.
	 */
	private void logUpdateError(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response, final String message, final String additionData,
								final long userId, final String subjectKeyId,
								final ErrorCode errorCode) throws SQLException {
		DbObject.execute(conn, "INSERT INTO updatelog(message, additionaldata, xuserxid, subjectkeyid) values(?, ?, ?, ?)", message, additionData, userId, subjectKeyId);
		conn.commit();
		sendErrorMessage(request, response, errorCode, message);
	}

	/**
	 * Fetch information from outscan to hiab.
	 *
	 * @param conn Database connection.
	 * @param request Http request.
	 * @param response Http response.
	 */
	@WebRequest(action = "OUTSCAN_UPDATEHIAB", requireLogin = false, ignoreCsrf = true, outscanOnly = true,
			allowedServices = {Service.HIAB_SCHEDULER, Service.HIAB_SCANNER}, enabledOnKubernetes = false)
	private void updateHiab(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws SQLException, IOException, ParamValidationException {
		request.checkInput(conn,
				ParamValidator.ascii("SUBJECTKEYID"),
				ParamValidator.ascii("SCANNERKEYID"),
				ParamValidator.ascii("MAC", 100, ":", null),
				ParamValidator.date("LASTUPDATE"),
				ParamValidator.date("RULEDATE"),
				ParamValidator.date("EXPLOITDATE"),
				ParamValidator.date("PATCHSUPERSEDENCEDATE"),
				ParamValidator.date("PRODUCTINFORMATIONDATE"),
				ParamValidator.date("BLUELIVDATE"),
				ParamValidator.digitOnly("SCANSUSED"),
				ParamValidator.digitOnly("SCANSLEFT"),
				ParamValidator.digitOnly("WEBAPPSUSED"),
				ParamValidator.ascii("VERSION"),
				ParamValidator.ascii("LASTPATCH"),
				ParamValidator.digitOnly("HOSTSUSED"),
				ParamValidator.digitOnly("WEBAPPSCANSLEFT"),
				ParamValidator.digitOnly("WEBAPPSCANSUSED"),
				ParamValidator.printable("PUBLICSBC"),
				ParamValidator.ascii("WHITELABELMD5"),
				ParamValidator.booleanValue("SCANNERUPDATE"),
				ParamValidator.digitOnly("RULECOUNT"),
				ParamValidator.printable("HMACKEY"),
				ParamValidator.json("INSTALLEDPACKAGES"),
				ParamValidator.printable("SCANNERVERSION"),
				ParamValidator.printable("UIVERSION"),
				ParamValidator.date("RULESVERSION")
		);

		final String identityId = request.getParameter("SUBJECTKEYID");
		if (StringUtils.isEmpty(identityId)) {
			logUpdateError(conn, request, response, "Identifier is missing", null, -1, null, ErrorCode.InputValidationFailed);
			return;
		}

		final HiabStat hiabStat = HiabStat.getByKey(conn, identityId);
		if (hiabStat == null) {
			logUpdateError(conn, request, response, "Identifier not found", null, -1, identityId, ErrorCode.InputValidationFailed);
			return;
		}

		final LoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, hiabStat.getUserId());
		if (user == null || !user.isActive()) {
			logUpdateError(conn, request, response, "Account has been closed", null, hiabStat.getUserId(), identityId, ErrorCode.ServerNotRegistered);
			return;
		}

		if (request.getBooleanParameter("SCANNERUPDATE", false)) {
			final String scannerKeyId = request.getParameter("SCANNERKEYID");
			if (StringUtils.isEmpty(scannerKeyId)) {
				logUpdateError(conn, request, response, "Scheduler is trying to update a scanner hiabstat but identifier is missing", null, -1, null,
						ErrorCode.InputValidationFailed);
				return;
			}

			final HiabStat scannerHiabStat = HiabStat.getByKey(conn, scannerKeyId);
			if (!updateHiabStat(conn, scannerKeyId, scannerHiabStat, request, response)) {
				return;
			}

			DbObject.execute(conn, "INSERT INTO updatelog(message, additionaldata, xuserxid, subjectkeyid) values(?, ?, ?, ?)", "Done", "Scanner update", hiabStat.getUserId(),
					identityId);
			conn.commit();

			sendSuccessMessage(request, response);
			return;
		}

		// request sent by same appliance
		if (!updateHiabStat(conn, identityId, hiabStat, request, response)) {
			return;
		}

		final Appliance appliance = Appliance.getByField(Appliance.class, conn, Access.ADMIN, "hiabId", hiabStat.getId());
		if (appliance != null) {
			if (!StringUtils.isEmpty(request.getParameter("LASTUPDATE"))) {
				final Date lastUpdate = DateUtils.parseTimeDate(request.getParameter("LASTUPDATE"));
				if (lastUpdate != null) {
					appliance.setUpdatedOnline(lastUpdate.toInstant());
				}
			}
			appliance.setVersions(request.getParameter("VERSION"), request.getParameter("UIVERSION"), request.getParameter("SCANNERVERSION"),
					request.getParameter("RULESVERSION"));
			appliance.save(conn);
		}

		final FluentConfiguration config = Flyway.configure();
		config.dataSource(Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.jdbc_url, PROPERTY_PREFIX),
				Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.db_username, PROPERTY_PREFIX),
				Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.db_password, PROPERTY_PREFIX));
		config.table("schema_version");
		final String locations = "filesystem://" + XMLAPI.getPrefix() + "META-INF/migrations";
		config.locations(locations, "filesystem://" + XMLAPI.getPrefix() + "META-INF/repeatablemigrations", "classpath:com/chilicoders/db/migrations");
		config.placeholders(Collections.singletonMap("postgresql_array_agg_type", conn.getMetaData().getDatabaseMajorVersion() >= 14 ? "anycompatiblearray" : "anyarray"));
		final Flyway flyway = config.load();

		final MigrationVersion localPatch = flyway.info().current().getVersion();
		final String requestPatch = request.isParameterEmpty("LASTPATCH") ? null : request.getParameter("LASTPATCH");
		final long userId = hiabStat.getUserId();
		if (localPatch.isNewerThan(requestPatch)) {
			logUpdateError(conn, request, response, "System needs to be updated before retrieving new information",
					"Last patch: " + requestPatch + " differs from: " + localPatch, userId, identityId, ErrorCode.InputValidationFailed);
			return;
		}

		final long hostsUsed = request.getLongParameter("HOSTSUSED", 0);
		final long scansUsed = request.getLongParameter("SCANSUSED", 0);
		final long scansLeft = request.getLongParameter("SCANSLEFT", 0);
		final long webAppsUsed = request.getLongParameter("WEBAPPSUSED", 0);
		final long webAppScansLeft = request.getLongParameter("WEBAPPSCANSLEFT", 0);
		final long webAppScansUsed = request.getLongParameter("WEBAPPSCANSUSED", 0);
		final String mac = request.getParameter("MAC");

		DbObject.execute(conn, "SELECT db_server_set_scaninfo(?, ?, ?, ?, ?, ?, ?, ?, ?)", userId, identityId.toUpperCase(), hostsUsed, scansUsed, scansLeft, webAppsUsed,
				webAppScansLeft, webAppScansUsed, mac);
		conn.commit();

		final File licenseAndTermsFile = File.createTempFile("license-and-terms", ".sql");
		final String updateString = UpdateUtil.createDatabaseCommands(conn, identityId, user, userId, licenseAndTermsFile, null, true);

		final List<String> params = new ArrayList<>();
		params.add(Configuration.getProperty(ConfigurationKey.outscan_exportrules, XMLAPI.getPrefix() + "META-INF/scripts/exportrules.sh"));
		params.addAll(Arrays.asList("-l", DbAccess.getDatabaseUrlFromJdbcUrl(Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.jdbc_url, PROPERTY_PREFIX))));
		params.addAll(Arrays.asList("-f", licenseAndTermsFile.getAbsolutePath())); // extra sql file

		String ruleDate = request.getParameter("RULEDATE");
		if (ruleDate != null) {
			final Long ownRuleCount =
					DbObject.getLong(conn, "SELECT COUNT(*) FROM trules WHERE NOT isscript AND updated <= ?::timestamp AND runforadmins IS DISTINCT FROM TRUE", ruleDate);
			if (ownRuleCount != null && ownRuleCount > request.getLongParameter("RULECOUNT", ownRuleCount)) {
				ruleDate = "1970-01-01";
			}
		}

		params.addAll(Arrays.asList("-r", ruleDate));
		params.addAll(Arrays.asList("-m", "0")); // migrations
		params.addAll(Arrays.asList("-b", StringUtils.setEmpty(request.getParameter("BLUELIVDATE"), "1970-01-01")));
		params.addAll(Arrays.asList("-e", StringUtils.setEmpty(request.getParameter("EXPLOITDATE"), "1970-01-01")));
		params.addAll(Arrays.asList("-p", StringUtils.setEmpty(request.getParameter("PRODUCTINFORMATIONDATE"), "1970-01-01")));
		params.addAll(Arrays.asList("-t", StringUtils.setEmpty(request.getParameter("PATCHSUPERSEDENCEDATE"), "1970-01-01")));
		params.addAll(Arrays.asList("-s", hiabStat.isScheduler() ? "scheduler" : "scanner")); // extracting for

		final File whiteLabelFile = getWhiteLabelFile(conn, userId);
		if (whiteLabelFile != null && whiteLabelFile.exists()) {
			final String localMd5 = DigestUtils.md5Hex(new FileInputStream(whiteLabelFile));
			final String remoteMd5 = request.getParameter("WHITELABELMD5");
			LOG.info("Md5 local:remote " + localMd5 + ":" + remoteMd5);
			if (!StringUtils.compare(localMd5, remoteMd5)) {
				response.setHeader("WHITELABELUPDATE", "true");
				try (final FileInputStream fis = new FileInputStream(whiteLabelFile)) {
					IOUtils.copy(fis, response.getOutputStream());
				}
				DbObject.execute(conn, "INSERT INTO updatelog(message, additionaldata, xuserxid, subjectkeyid) values(?, ?, ?, ?)", "Sending new whitelabel update package",
						updateString, userId, identityId);
				conn.commit();
				return;
			}
		}

		final Executor ex =
				new Executor(params.toArray(new String[0]), null, "PGPASSWORD=" + Configuration.getPropertyWithOptionalPrefix(ConfigurationKey.db_password, PROPERTY_PREFIX), Configuration.getConfigService());
		ex.start();
		ex.waitForCompletion(5 * 60);

		FileUtils.deleteQuietly(licenseAndTermsFile);

		final String filename = ex.getOutput();
		if (StringUtils.isEmpty(filename)) {
			logUpdateError(conn, request, response, "No file reported when generating SQL", null, userId, identityId, ErrorCode.InternalServerError);
			return;
		}
		final File f = new File(filename.trim());
		if (!f.exists()) {
			logUpdateError(conn, request, response, "Could not create update SQL file", ex.getError(), userId, identityId, ErrorCode.InternalServerError);
			return;
		}
		try (final FileInputStream inputStream = new FileInputStream(f)) {
			IOUtils.copy(inputStream, response.getOutputStream());
		}
		FileUtils.deleteQuietly(f);
		DbObject.execute(conn, "INSERT INTO updatelog(message, additionaldata, xuserxid, subjectkeyid) values(?, ?, ?, ?)", "Done", updateString, userId, identityId);
		conn.commit();
	}

	/**
	 * Enroll HIAB via outscan.
	 *
	 * @param conn Database connection.
	 * @param request Http request.
	 * @param response Http response.
	 */
	@SuppressFBWarnings(value = "SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING")
	@WebRequest(action = "OUTSCAN_ENROLLHIAB", requireLogin = false, requireCert = false, enabledOnKubernetes = false)
	private void enrollHiab(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws SQLException, IOException, ParamValidationException, JAXBException {
		request.checkInput(conn,
				ParamValidator.json("CERTIFICATEREQUESTS"),
				ParamValidator.json("ENROLLMENTKEY"),
				ParamValidator.printable("HIABIMAGEVERSION")
		);

		if (request.isParameterEmpty("ENROLLMENTKEY") ||
				(!Configuration.getProperty(ConfigurationBooleanKey.ignore_hiab_image_version) && (request.isParameterEmpty("HIABIMAGEVERSION")
						|| !HiabToolsBusiness.validateHiabImageVersion(request.getParameter("HIABIMAGEVERSION"))))) {
			logEnrollError(conn, request, response, MessageBusiness.getMessageString("_GET_LATEST_IMAGE", (String) request.getSession().getAttribute("LANGUAGE"), "MESSAGES"),
					-1);
			return;
		}

		final UpdateKeyInformation keyInfo = MarshallingUtils.unmarshal(UpdateKeyInformation.class, request.getParameter("ENROLLMENTKEY"));
		final List<CertificateCreationRequest> certificateRequests =
				MarshallingUtils.unmarshalList(CertificateCreationRequest.class, request.getParameter("CERTIFICATEREQUESTS"));

		if (certificateRequests.size() != 2 && StringUtils.isEmpty(certificateRequests.get(0).getCertificateSigningRequest()) && StringUtils.isEmpty(
				certificateRequests.get(1).getCertificateSigningRequest())) {
			logEnrollError(conn, request, response,
					MessageBusiness.getMessageString("_ENROLLMENT_PACKAGE_FAILED", (String) request.getSession().getAttribute("LANGUAGE"), "MESSAGES")
							+ ", Certificate signing request is missing", -1);
			return;
		}

		if (keyInfo.getUsername() == null) {
			logEnrollError(conn, request, response, "Username missing", -1);
			return;
		}

		LoginAttempt loginAttempt = enrollAttempts.get(keyInfo.getUsername());
		if (loginAttempt == null) {
			loginAttempt = new LoginAttempt(keyInfo.getUsername());
		}
		if (!loginAttempt.checkAttempt()) {
			logEnrollError(conn, request, response, "Too many attempts, wait " + LoginAttempt.DELAY_SECONDS + " between each attempt", -1);
			return;
		}
		loginAttempt.updateLastAttempt();
		enrollAttempts.put(keyInfo.getUsername(), loginAttempt);

		BaseLoggedOnUser user = LoggedOnUser.get(LoggedOnUser.class, conn, DbHelper.getSelect(LoggedOnUser.class, Access.ADMIN, "vcusername=UPPER(?)"), keyInfo.getUsername());
		if (user == null) {
			user = LoggedOnSubUser.get(LoggedOnSubUser.class, conn,
					DbHelper.getSelect(LoggedOnSubUser.class, Access.ADMIN, "vcusername=UPPER(?) AND bactive = 1 AND (hiabenroll OR superuser=1)"), keyInfo.getUsername());
		}
		if (user == null) {
			logEnrollError(conn, request, response, "Incorrect credentials", -1);
			return;
		}

		final long userId = user.getMainUserId();

		if (!user.isActive() || !user.isParentActive()) {
			logEnrollError(conn, request, response, "Account closed", userId);
			return;
		}

		if (StringUtils.isEmpty(keyInfo.getMac())) {
			logEnrollError(conn, request, response, "No MAC address", userId);
			return;
		}

		if (!UserBusiness.checkPasswd(TwoWayEncoding.decodeIfPossible(keyInfo.getPassword(), Configuration.getConfigService()), user.getPasswordHash(), false, 0)) {
			sendLoginEvent(conn, user, false, request.getRemoteAddr());
			logEnrollError(conn, request, response, "Incorrect credentials", userId);
			return;
		}

		sendLoginEvent(conn, user, true, request.getRemoteAddr());
		if (StringUtils.isEmpty(keyInfo.getProduct())) {
			logEnrollError(conn, request, response, "Product is missing", userId);
			return;
		}
		keyInfo.setProduct(StringUtils.trimChar(keyInfo.getProduct(), true, ' '));
		final boolean penTest = "pen".equals(keyInfo.getProduct());
		if (penTest && !keyInfo.isVirtual()) {
			logEnrollError(conn, request, response, "PEN HIABs must be virtual", userId);
			return;
		}

		final long ip = DbObject.executeCountQuery(conn, "SELECT " + (penTest ? "maxpentesthiabs" : "xhiabip") + " FROM tusers WHERE xid=? AND bhiablicense=1", userId);
		final long externalIp =
				DbObject.executeCountQuery(conn, "SELECT " + (penTest ? "maxpentesthiabs" : "xhiabexternalip") + " FROM tusers WHERE xid=? AND bhiablicense=1", userId);
		final long webApps =
				DbObject.executeCountQuery(conn, "SELECT " + (penTest ? "maxpentesthiabs" : "hiabmaxwebapps") + " FROM tusers WHERE xid=? AND bhiablicense=1", userId);
		final long appsecScaleExternal =
				DbObject.executeCountQuery(conn, "SELECT " + (penTest ? "maxpentesthiabs" : "hiabexternalwebapps") + " FROM tusers WHERE xid=? AND bhiablicense=1", userId);
		if (ip == 0 && externalIp == 0 && webApps == 0 && appsecScaleExternal == 0) {
			logEnrollError(conn, request, response, "No HIAB license", userId);
			return;
		}

		if (keyInfo.isVirtual()) {
			final long maxVirtuals = penTest ? user.getMaxPenTestHiabs() : user.getMaxVirtualHiabs();
			final long existingVirtuals =
					DbObject.executeCountQuery(conn, "SELECT COUNT(*) FROM thiabstats WHERE xuserxid=? AND NOT revoked AND virtual = 1 AND product=?", userId,
							penTest ? "pen" : "hiab");
			if (existingVirtuals >= maxVirtuals) {
				logEnrollError(conn, request, response, "Too many virtual HIABs", userId);
				return;
			}
		}

		final boolean isScheduler = penTest
				|| DbObject.executeCountQuery(conn, "SELECT COUNT(*) FROM thiabstats WHERE xuserxid=? AND scheduler=1 AND revoked IS DISTINCT FROM true AND product='hiab'",
				userId) == 0;
		final Service service = isScheduler ? Service.HIAB_SCHEDULER : Service.HIAB_SCANNER;
		final CertificateCreationRequest certificateRequest = certificateRequests.stream().filter(crtRequest -> crtRequest.getService() == service).findFirst().get();

		final long serial = DbObject.executeCountQuery(conn, "SELECT nextval('userver_certificate_serial_seq')");
		final String certificate = LibellumUtils.getCertificate(certificateRequest.getCertificateSigningRequest(), user.getCustomerUuid());
		final ServiceIdentity serviceIdentity = CertificateUtils.getIdentityExtension(CertificateUtils.getCertificateFromString(certificate));
		final HiabStat hiabStat = new HiabStat(userId, keyInfo.getProduct(), keyInfo.getCpuInfo(), keyInfo.isVirtual(), serial, serviceIdentity.getUuid(), keyInfo.getMac());
		hiabStat.setOs("CentOS");
		hiabStat.setScheduler(isScheduler);
		final long hiabStatId = hiabStat.save(conn);

		final Appliance appliance = new Appliance();
		appliance.setCustomerId(user.getCustomerId());
		appliance.setCreatedById(Appliance.SYSTEM_USER);
		appliance.setHiabId((int) hiabStatId);
		appliance.setPrimary(isScheduler);
		appliance.setVirtual(keyInfo.isVirtual());
		appliance.setMac(keyInfo.getMac());
		appliance.setUuid(serviceIdentity.getUuid());
		appliance.save(conn);

		final String installedPackages = request.getParameter("INSTALLEDPACKAGES");
		if (!StringUtils.isEmpty(installedPackages)) {
			DbObject.execute(conn, "UPDATE thiabstats SET installedpackages = ? WHERE id = ?", installedPackages, hiabStatId);
		}

		conn.commit();
		response.setContentType("application/zip");
		try (final ServletOutputStream outputStream = response.getOutputStream()) {
			try (final ZipOutputStream zip = new ZipOutputStream(outputStream)) {
				zip.putNextEntry(new ZipEntry("crt"));
				zip.write(certificate.getBytes(UTF_8));
				zip.closeEntry();
				zip.putNextEntry(new ZipEntry("sql"));
				final String string =
						(String) DbObject.getObject(conn, "SELECT db_server_get_userobject(?, ?);", userId, getProductTypes().get(keyInfo.getProduct().toLowerCase()));
				zip.write(string.getBytes(UTF_8));
				zip.write(("DELETE FROM hiabinfo; INSERT INTO hiabinfo VALUES(" + (hiabStat.isScheduler() ? "1" : "0") + ");").getBytes(UTF_8));
				if (user.isSubUser()) {
					final String createUser =
							"INSERT INTO tsubusers(xid, vcusername, vcfirstname, vclastname, vccountry, vcemail, xiparentid, superuser, vcpassword, xisubparentid) VALUES (NEXTVAL('tsubusers_seq'), '"
									+
									StringUtils.escapeSQL(user.getUsername())
									+ "', '"
									+ StringUtils.escapeSQL(user.getFirstName())
									+ "','"
									+ StringUtils.escapeSQL(user.getLastName())
									+ "','"
									+ user.getCountryCode()
									+ "','"
									+
									StringUtils.escapeSQL(user.getEmail())
									+ "',"
									+ UserBusiness.HIAB_USER_ID
									+ ", 1, '"
									+ user.getPasswordHash()
									+ "', -1);";
					zip.write(createUser.getBytes(UTF_8));
				}
				zip.closeEntry();
				final File whiteLabelFile = getWhiteLabelFile(conn, user.getMainUserId());
				if (whiteLabelFile != null && whiteLabelFile.exists()) {
					zip.putNextEntry(new ZipEntry("whitelabel.rpm"));
					IOUtils.copy(new FileInputStream(whiteLabelFile), zip);
					zip.closeEntry();
				}

				final Path tmpFolder = Files.createTempDirectory(null);
				Executor.execute(Configuration.getConfigService(), "yumdownloader", "--exclude", "hiab-custom-default",
						"--setopt=reposdir=" + Configuration.getProperty(ConfigurationKey.hiab_repo_dir_path_on_outscan),
						"--destdir=" + tmpFolder.toString(), "hiab-custom", "hiab-keys");

				try (final DirectoryStream<Path> directoryStream = Files.newDirectoryStream(tmpFolder, "*.rpm")) {
					for (final Path path : directoryStream) {
						zip.putNextEntry(new ZipEntry(path.getFileName().toString()));
						FileUtils.copyFile(path.toFile(), zip);
						zip.closeEntry();
					}
				}
				FileUtils.deleteDirectory(tmpFolder.toFile());
			}
		}
	}

	/**
	 * Send a login event.
	 *
	 * @param conn Database connection.
	 * @param user Logged on user.
	 * @param loggedOn True if logged on, false on failure.
	 * @param remote Remote address.
	 */
	private void sendLoginEvent(final Connection conn, final BaseLoggedOnUser user, final boolean loggedOn, final String remote) {
		final EventProperties properties = EventLoginProperties.builder().isSuccessful(loggedOn)
				.statusText(loggedOn ? "_SUCCESS_LOGIN" : "_FAILED_LOGIN")
				.userName(user.getUsername())
				.origin(remote)
				.time(new Date())
				.subuserId(user.getSubUserId()).build();
		final LoggingBusiness lb = new LoggingBusiness(getNewLoggedOnUser());
		lb.sendLogging(conn, user.getMainUserId(), properties, O24Event.UserLogin, -1, EventType.Unspecified);
	}

	/**
	 * Get path to whitelabel file.
	 *
	 * @param conn Database connection
	 * @param userId User id
	 * @return Path to whitelabel file
	 */
	@SuppressFBWarnings(value = "DMI_HARDCODED_ABSOLUTE_FILENAME", justification = "Intentionally using hardcoded absolute file name")
	protected static File getWhiteLabelFile(final Connection conn, final long userId) throws SQLException {
		final Long salesOrgId = DbObject.getLong(conn, "SELECT xiparentid FROM tusers WHERE xid=?", userId);
		final File folder = new File("/var/cache/opi/whitelabel/");
		if (folder.exists()) {
			final File[] files = folder.listFiles();
			if (files != null) {
				for (final File file : files) {
					if (file.getName().startsWith("whitelabel-hiab-" + salesOrgId + "-") && file.getName().endsWith("rpm")) {
						return file;
					}
				}
			}
		}
		return null;
	}

	/**
	 * Main method.
	 *
	 * @param args Arguments
	 */
	public static void main(final String[] args) throws SQLException, NoSuchAlgorithmException, OperatorCreationException, NoSuchProviderException,
			IOException, javax.security.cert.CertificateException, PGPException, InvalidKeySpecException, CertificateException {

		if (args.length != 2 && args.length != 3) {
			System.out.println("Usage: <username> <password> [url]");
			return;
		}
		try {
			Log4jUtils.configureFileAppender(Log4jUtils.Parameters.builder().configureRoot(false).filePath("/tmp/update.log").build());

			Configuration.initializeConfigurationClass();
			XMLAPI.initializeSslContexts();
			HttpService.setSslSecurityProtocol(SSLSecurityProtocol.TLSv1_2);
			java.security.Security.addProvider(new BouncyCastleProvider());
			Hiab.getInstance().getNetworkInformation();
			final String enrollmentUrl = args.length == 3
					? (args[2].startsWith("https://") || args[2].startsWith("http://") ? args[2] : "https://" + args[2] + "/opi/XMLAPI")
					: Configuration.getProperty(ConfigurationKey.outscan_url);
			HiabToolsBusiness.call64bitEnroll(args[0], args[1], null, null, enrollmentUrl, false, null);
			HttpsThread.destroyTimer();
			if (XMLAPI.isEnrollFailed()) {
				System.out.println(XMLAPI.getEnrollErrorMessage());
				System.exit(-1);
			}
		}
		catch (final Exception e) {
			System.out.println(e.getMessage());
		}
		TimerHandler.destroy();
	}

	protected static Map<String, Integer> getProductTypes() {
		return productTypes;
	}

	public static void batcher() {
		enrollAttempts.clear();
	}
}
