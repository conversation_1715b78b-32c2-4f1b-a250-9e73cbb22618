package com.chilicoders.bl;

import java.sql.Connection;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.chilicoders.api.EventApiInterface;
import com.chilicoders.model.Event;
import com.chilicoders.model.events.properties.EventFindingProperties;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

@SuppressFBWarnings(value = "EI_EXPOSE_REP2", justification = "Intentionally incorporating reference to mutable object")
public class LoggingBusinessEventApi implements EventApiInterface {
	private final LoggingBusiness loggingBusiness;
	private final Connection conn;

	private static final Logger LOG = LogManager.getLogger(LoggingBusinessEventApi.class);

	public LoggingBusinessEventApi(final LoggingBusiness loggingBusiness, final Connection conn) {
		this.loggingBusiness = loggingBusiness;
		this.conn = conn;
	}

	@Override
	public boolean handleEvent(final Event event) {
		try {
			if (event.getFindings() != null) {
				loggingBusiness.sendLoggingFindings(conn, event.getUserId(), event.getFindings(), event.getTargetId(), event.getEventType(),
						(EventFindingProperties) event.getProperties(), event.getSubuserId());
			}
			else if (event.getLists() != null) {
				loggingBusiness.sendLoggingList(conn, event.getUserId(), event.getSubuserId(), event.getEvent(), event.getProperties(), event.getEventType());
			}
			else if (event.getTargetIds() != null) {
				event.getTargetIds().forEach(id ->
						loggingBusiness.sendLogging(conn, event.getUserId(), event.getProperties(), event.getEvent(), id, event.getEventType())
				);
			}
			else {
				loggingBusiness.sendLogging(conn, event.getUserId(), event.getProperties(), event.getEvent(), -1, event.getEventType());
			}
		}
		catch (final Exception e) {
			LOG.error("Error sending event", e);
		}
		return true;
	}

}
