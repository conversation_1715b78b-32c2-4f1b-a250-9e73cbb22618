package com.chilicoders.bl;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.StringWriter;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateFactory;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x509.AlgorithmIdentifier;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.X509v3CertificateBuilder;
import org.bouncycastle.cert.jcajce.JcaX509ExtensionUtils;
import org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import org.bouncycastle.crypto.util.PrivateKeyFactory;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.operator.DefaultDigestAlgorithmIdentifierFinder;
import org.bouncycastle.operator.DefaultSignatureAlgorithmIdentifierFinder;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.operator.bc.BcRSAContentSignerBuilder;
import org.bouncycastle.pkcs.PKCS10CertificationRequest;
import org.bouncycastle.pkcs.PKCS10CertificationRequestBuilder;
import org.joda.time.DateTime;
import org.json.JSONException;
import org.json.JSONObject;
import org.opensaml.common.SAMLVersion;
import org.opensaml.common.xml.SAMLConstants;
import org.opensaml.saml2.core.Assertion;
import org.opensaml.saml2.core.AttributeStatement;
import org.opensaml.saml2.core.AuthnContextClassRef;
import org.opensaml.saml2.core.AuthnContextComparisonTypeEnumeration;
import org.opensaml.saml2.core.AuthnRequest;
import org.opensaml.saml2.core.EncryptedAssertion;
import org.opensaml.saml2.core.Issuer;
import org.opensaml.saml2.core.NameIDPolicy;
import org.opensaml.saml2.core.RequestedAuthnContext;
import org.opensaml.saml2.core.Response;
import org.opensaml.saml2.core.impl.AuthnContextClassRefBuilder;
import org.opensaml.saml2.core.impl.AuthnRequestBuilder;
import org.opensaml.saml2.core.impl.IssuerBuilder;
import org.opensaml.saml2.core.impl.NameIDPolicyBuilder;
import org.opensaml.saml2.core.impl.RequestedAuthnContextBuilder;
import org.opensaml.saml2.encryption.Decrypter;
import org.opensaml.saml2.metadata.AssertionConsumerService;
import org.opensaml.saml2.metadata.EntitiesDescriptor;
import org.opensaml.saml2.metadata.EntityDescriptor;
import org.opensaml.saml2.metadata.IDPSSODescriptor;
import org.opensaml.saml2.metadata.KeyDescriptor;
import org.opensaml.saml2.metadata.NameIDFormat;
import org.opensaml.saml2.metadata.SPSSODescriptor;
import org.opensaml.saml2.metadata.SingleSignOnService;
import org.opensaml.saml2.metadata.provider.DOMMetadataProvider;
import org.opensaml.saml2.metadata.provider.MetadataProviderException;
import org.opensaml.security.MetadataCredentialResolver;
import org.opensaml.security.MetadataCriteria;
import org.opensaml.xml.encryption.DecryptionException;
import org.opensaml.xml.encryption.InlineEncryptedKeyResolver;
import org.opensaml.xml.io.Marshaller;
import org.opensaml.xml.io.MarshallingException;
import org.opensaml.xml.io.Unmarshaller;
import org.opensaml.xml.io.UnmarshallingException;
import org.opensaml.xml.security.CriteriaSet;
import org.opensaml.xml.security.SecurityException;
import org.opensaml.xml.security.SecurityHelper;
import org.opensaml.xml.security.credential.BasicCredential;
import org.opensaml.xml.security.credential.UsageType;
import org.opensaml.xml.security.criteria.EntityIDCriteria;
import org.opensaml.xml.security.criteria.UsageCriteria;
import org.opensaml.xml.security.keyinfo.KeyInfoHelper;
import org.opensaml.xml.security.keyinfo.StaticKeyInfoCredentialResolver;
import org.opensaml.xml.security.x509.BasicX509Credential;
import org.opensaml.xml.signature.KeyInfo;
import org.opensaml.xml.signature.Signature;
import org.opensaml.xml.signature.SignatureConstants;
import org.opensaml.xml.signature.SignatureException;
import org.opensaml.xml.signature.Signer;
import org.opensaml.xml.signature.impl.ExplicitKeySignatureTrustEngine;
import org.opensaml.xml.util.Base64;
import org.opensaml.xml.util.XMLHelper;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.SAXException;

import com.chilicoders.app.MultipartHttpServletRequest;
import com.chilicoders.app.RequestFile;
import com.chilicoders.app.WebRequest;
import com.chilicoders.app.XMLAPI;
import com.chilicoders.bl.objects.SamlException;
import com.chilicoders.core.auditing.api.model.AppName;
import com.chilicoders.core.auditing.api.model.AuditMode;
import com.chilicoders.core.configuration.api.DataStoreEntry;
import com.chilicoders.core.configuration.api.DataStoreEntry.DataStoreEntryKeys;
import com.chilicoders.core.configuration.api.DataStoreService;
import com.chilicoders.core.configuration.common.ConfigKeys.ConfigurationKey;
import com.chilicoders.core.user.api.Role;
import com.chilicoders.db.Access;
import com.chilicoders.db.DbHelper;
import com.chilicoders.db.objects.BaseLoggedOnUser;
import com.chilicoders.db.objects.Features;
import com.chilicoders.db.objects.Idp;
import com.chilicoders.db.objects.Idp.CryptoHashAlgorithm;
import com.chilicoders.db.objects.LoggedOnUser;
import com.chilicoders.db.objects.Login;
import com.chilicoders.db.objects.PasswordPolicy;
import com.chilicoders.db.objects.SamlToken;
import com.chilicoders.db.objects.SubUser;
import com.chilicoders.db.objects.User;
import com.chilicoders.exception.ParamValidationException;
import com.chilicoders.model.ErrorCode;
import com.chilicoders.rest.models.Customer;
import com.chilicoders.service.ServiceProvider;
import com.chilicoders.util.Configuration;
import com.chilicoders.util.DateUtils;
import com.chilicoders.util.HttpRequestWrapper;
import com.chilicoders.util.IpUtils;
import com.chilicoders.util.ParamValidator;
import com.chilicoders.util.RuntimeEnvironment;
import com.chilicoders.util.SslUtils;
import com.chilicoders.util.StringUtils;
import com.chilicoders.util.TwoWayEncoding;
import com.chilicoders.util.xml.XMLDoc;
import com.chilicoders.util.xml.XMLObject;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class SamlBusiness extends BusinessObject {
	private static final Logger LOG = LogManager.getLogger(SamlBusiness.class);
	private static final String ROWSET_TAG = "SSOLIST";
	private static final String ROW_TAG = "SSO";
	public static final String IDPCOOKIE_ID = "JDNEHSSGD";
	private static final ParamValidator[] ssoEnableValidators = {
			ParamValidator.booleanValue("SSOENABLED"),
			ParamValidator.listOfValues("SIGNATUREHASHALGORITHM", new String[] {"SHA_1", "SHA_256"}),
			ParamValidator.printable("USERIDFIELD"),
			ParamValidator.booleanValue("BINDTOPORTAL")
	};
	private static final SecureRandom SECURE_RANDOM = new SecureRandom();

	public SamlBusiness() {
		super();
	}

	/**
	 * Sends the sso settings.
	 *
	 * @param conn A database connection.
	 * @param request The HTTP servlet request.
	 * @param response The HTTP servlet response.
	 */
	@WebRequest(action = "SSODATA", roles = {Role.MainUser, Role.SuperUser})
	private void sendSsoData(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws SQLException {
		request.noParametersRequired();

		final ArrayList<Idp> list = new ArrayList<>();

		final Idp idp = Idp.getById(Idp.class, conn, Access.ADMIN, getMainUserId());
		if (idp != null) {
			list.add(idp);
		}

		Idp.sendInformation(list, list.size(), ROWSET_TAG, ROW_TAG, request, response, getNewLoggedOnUser(), Access.ADMIN, null, null);
	}

	/**
	 * Checks if user should automatically log in through saml.
	 *
	 * @param conn A database connection.
	 * @param request The HTTP servlet request.
	 * @param response The HTTP servlet response.
	 */
	@SuppressFBWarnings(value = "DCN_NULLPOINTER_EXCEPTION", justification = "Intentionally catching NPE")
	@WebRequest(action = "AUTOMATICSAML", requireLogin = false, ignoreCsrf = true)
	private void automaticSaml(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws JSONException, NumberFormatException, SQLException {
		request.noParametersRequired();

		boolean exists = false;
		final JSONObject json = new JSONObject();

		final Cookie[] cookies = request.getCookies();
		if (cookies != null) {
			for (final Cookie cookie : cookies) {
				try {
					if (IDPCOOKIE_ID.equals(cookie.getName())) {
						final Idp idp = Idp.getById(Idp.class, conn, Long.parseLong(TwoWayEncoding.decode(cookie.getValue(), Configuration.getConfigService())));
						if (idp != null && idp.isSsoEnabled()) {
							exists = true;
							LOG.debug("Automatically redirect to saml login.");
						}
					}
				}
				catch (final NullPointerException e) {
					LOG.error("Error handling cookies in AUTOMATICSAML. Name: " + cookie.getName() + " value: " + cookie.getValue(), e);
					sendErrorMessage(request, response, ErrorCode.AutomaticLoginFailed);
					return;
				}
			}
		}

		String message = "sso";
		if (!exists) {
			message = "normal";
		}
		else {
			final List<SamlToken> samlRequestTokens = SamlToken.fetchObjects(SamlToken.class, conn, Access.ADMIN, "ipaddress=?", request.getRemoteAddr());
			for (final SamlToken token : samlRequestTokens) {
				if (token != null && token.getExpire().after(new Date())) {
					message = "normal";
					break;
				}
			}
		}
		json.put("login", message);

		sendSuccessMessage(request, null, json.toString(), response);
	}

	/**
	 * Function to send a login saml request.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "SAMLREQUEST", requireLogin = false, ignoreCsrf = true)
	private void samlRequest(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, NumberFormatException, SQLException, SecurityException, SamlException {
		request.checkInput(conn, ParamValidator.alphanumeric("VCUSERNAME", -1, (Configuration.isHiabEnabled() ? "@._- " : "@._-")));

		final String userName = request.getParameter("VCUSERNAME");
		Idp idp = null;
		final Cookie[] cookies = request.getCookies();
		if (userName != null) {
			final SubUser subUser = SubUser.getByField(SubUser.class, conn, Access.ADMIN, "vcusername", userName.toUpperCase());
			final User user = User.getByField(User.class, conn, Access.ADMIN, "vcusername", userName.toUpperCase());
			final Long id;
			if (user != null) {
				id = user.getId();
			}
			else if (subUser != null) {
				id = subUser.getParentId();
			}
			else {
				LOG.info("Saml login failed because username could not be found.");
				sendErrorMessage(request, response, ErrorCode.Unsupported);
				return;
			}
			idp = Idp.getById(Idp.class, conn, Access.ADMIN, id);
		}
		else if (cookies != null) {
			for (final Cookie cookie : cookies) {
				if (IDPCOOKIE_ID.equals(cookie.getName())) {
					idp = Idp.getById(Idp.class, conn, Long.parseLong(TwoWayEncoding.decode(cookie.getValue(), Configuration.getConfigService())));
				}
			}
		}
		if (idp == null) {
			LOG.info("Saml login failed because idp could not be found.");
			sendErrorMessage(request, response, ErrorCode.Unsupported);
			return;
		}
		else if (!idp.isSsoEnabled()) {
			LOG.info("Saml login failed because idp was not enabled for the user " + userName);
			sendErrorMessage(request, response, ErrorCode.Unsupported);
			return;
		}

		try {
			if (!checkIdpMetadata(idp.getMetadata().getBytes(UTF_8), false, new DateTime(idp.getCacheDate()))) {
				if (idp.getNewMetadata() != null && !idp.getNewMetadata().isEmpty()) {
					idp.setMetadata(idp.getNewMetadata());
					idp.setNewMetadata("");
					idp.save(conn);
					conn.commit();
					final HashMap<String, String> customtags = new HashMap<>();
					customtags.put("PRODUCT", Configuration.isHiabEnabled() ? "HIAB" : "OUTSCAN");
					customtags.put("METADATA", idp.getMetadata());
					final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
					UserBusiness.sendEmailtoUser(conn, user.getEmail(), (Configuration.isHiabEnabled()
									? Configuration.getProperty(ConfigurationKey.mail_fromaddresshiab)
									: Configuration.getProperty(ConfigurationKey.mail_fromaddress)),
							user, "idpold", customtags, user.getEmailEncryptionKey(), -1);
					LOG.debug("Idp metadata for mainuser " + idp.getId() + " was updated.");
				}
				else {
					LOG.info("Saml login failed because idp metadata was old.");
					sendErrorMessage(request, response, ErrorCode.Unsupported);
					return;
				}
			}

			// Load (and if needed, regenerate) private key and certificate.
			// On regen, an IdP might auto-renew SP metadata or simply reject.
			PrivateKey privKey = loadPrivateKey(conn);
			X509Certificate certificate = loadX509Certificate(conn);

			if (privKey == null || !checkCertificateValidity(certificate)) {
				try {
					generateSSOPrivateKeyAndCertificate(conn, null, null);
					privKey = loadPrivateKey(conn);
					certificate = loadX509Certificate(conn);

					if (privKey == null || certificate == null) {
						throw new SamlException("Could not obtain SAML key/certificate");
					}
				}
				catch (final SamlException e) {
					LOG.error("Saml login attempt failed", e);
					sendErrorMessage(request, response, ErrorCode.InternalServerError);
					return;
				}
			}

			EntityDescriptor spEntity = null;
			try {
				final Customer customer = Customer.getByField(Customer.class, conn, Access.ADMIN, "userId", idp.getId());
				final String spMetadata = buildSPMetadata(certificate, request, idp.isHasUUIDField() ? customer.getUuid() : null);
				if (spMetadata.isEmpty()) {
					LOG.error("Saml login failed because sp metadata could not be built.");
					sendErrorMessage(request, response, ErrorCode.Unsupported);
					return;
				}
				spEntity = (EntityDescriptor) buildMetadataProvider(new ByteArrayInputStream(spMetadata.getBytes(UTF_8))).getMetadata();
			}
			catch (final SamlException e) {
				LOG.error("Saml login failed because sp metadata could not be built.");
				sendErrorMessage(request, response, ErrorCode.Unsupported);
				return;
			}

			SingleSignOnService endpoint = null;
			final List<EntityDescriptor> entityList = getEntityList(idp.getMetadata().getBytes(UTF_8));
			if (entityList != null) {
				for (final EntityDescriptor entity : entityList) {
					final IDPSSODescriptor ssoDescriptor = entity.getIDPSSODescriptor(SAMLConstants.SAML20P_NS);
					if (ssoDescriptor != null) {
						for (final SingleSignOnService ssoService : ssoDescriptor.getSingleSignOnServices()) {
							if (ssoService.getBinding().equals(SAMLConstants.SAML2_REDIRECT_BINDING_URI)) {
								endpoint = ssoService;
							}
						}
					}
				}
			}

			// Signature
			final Signature signature =
					(Signature) org.opensaml.xml.Configuration.getBuilderFactory().getBuilder(Signature.DEFAULT_ELEMENT_NAME).buildObject(Signature.DEFAULT_ELEMENT_NAME);
			final BasicX509Credential credential = new BasicX509Credential();
			credential.setPrivateKey(privKey);
			credential.setEntityCertificate(certificate);
			signature.setSigningCredential(credential);
			final CryptoHashAlgorithm sha = idp.getSignatureHashAlgorithm();
			switch (sha) {
				case SHA_1:
					signature.setSignatureAlgorithm(SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA1);
					break;
				case SHA_256:
				default:
					signature.setSignatureAlgorithm(SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA256);
			}
			signature.setCanonicalizationAlgorithm(SignatureConstants.ALGO_ID_C14N_EXCL_OMIT_COMMENTS);

			// Extend signature object with KeyInfo
			SecurityHelper.prepareSignatureParams(signature, credential, null, null);

			// Issuer object
			final Issuer issuer = new IssuerBuilder().buildObject("urn:oasis:names:tc:SAML:2.0:assertion", "Issuer", "samlp");
			issuer.setValue(spEntity.getEntityID());

			// NameIDPolicy
			final NameIDPolicy nameIdPolicy = new NameIDPolicyBuilder().buildObject();
			nameIdPolicy.setFormat("urn:oasis:names:tc:SAML:2.0:nameid-format:transient");
			nameIdPolicy.setSPNameQualifier(spEntity.getEntityID());
			nameIdPolicy.setAllowCreate(Boolean.TRUE);

			// AuthnContextClass
			final AuthnContextClassRef authnContextClassRef =
					new AuthnContextClassRefBuilder().buildObject("urn:oasis:names:tc:SAML:2.0:assertion", "AuthnContextClassRef", "saml");
			// Fix issue AADSTS75011
			authnContextClassRef.setAuthnContextClassRef("urn:oasis:names:tc:SAML:2.0:ac:classes:unspecified");

			// AuthnContex
			final RequestedAuthnContext requestedAuthnContext = new RequestedAuthnContextBuilder().buildObject();
			requestedAuthnContext.setComparison(AuthnContextComparisonTypeEnumeration.EXACT);
			requestedAuthnContext.getAuthnContextClassRefs().add(authnContextClassRef);

			// Creation of AuthRequestObject
			final AuthnRequest authRequest = new AuthnRequestBuilder().buildObject("urn:oasis:names:tc:SAML:2.0:protocol", "AuthnRequest", "samlp");
			authRequest.setIssueInstant(new DateTime());
			authRequest.setProtocolBinding("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST");
			authRequest.setAssertionConsumerServiceURL(spEntity.getSPSSODescriptor(SAMLConstants.SAML20P_NS).getAssertionConsumerServices().get(0).getLocation());
			authRequest.setIssuer(issuer);
			authRequest.setNameIDPolicy(nameIdPolicy);
			authRequest.setRequestedAuthnContext(requestedAuthnContext);
			authRequest.setID("Outpost24" + UUID.randomUUID().toString());
			authRequest.setVersion(SAMLVersion.VERSION_20);
			authRequest.setSignature(signature);
			authRequest.setDestination(endpoint.getLocation());

			// Sign request, deflate, encode
			final Marshaller marshaller = org.opensaml.Configuration.getMarshallerFactory().getMarshaller(authRequest);
			final Element authDOM = marshaller.marshall(authRequest);
			Signer.signObject(signature);
			final StringWriter requestWriter = new StringWriter();
			XMLHelper.writeNode(authDOM, requestWriter);
			final String requestXML = requestWriter.toString();
			String encodedRequestMessage = Base64.encodeBytes(requestXML.getBytes(UTF_8), Base64.DONT_BREAK_LINES);
			encodedRequestMessage = URLEncoder.encode(encodedRequestMessage, "UTF-8").trim();

			// Create SamlToken in database
			final SamlToken samlToken = new SamlToken();
			samlToken.setId(authRequest.getID());
			samlToken.setExpire(DateUtils.getDateTimeOffset(null, 5));
			samlToken.setIdpId(idp.getId());
			samlToken.setIpAddress(request.getRemoteAddr());
			samlToken.saveObjectId(conn, Access.ADMIN, null, null);
			conn.commit();

			// Redirect the user to the idp
			LOG.debug("Redirecting to idp: " + endpoint.getLocation());
			sendSuccessMessage(request, endpoint.getLocation() + "?SAMLRequest=" + encodedRequestMessage, null, response);
		}
		catch (final RuntimeException | IOException | SignatureException | MarshallingException | MetadataProviderException e) {
			throw new SamlException("Could not build saml request.", e);
		}
	}

	/**
	 * Function to enable SSO.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "ENABLESINGLESIGNON", roles = {Role.MainUser, Role.SuperUser})
	private void enableSSO(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SamlException, SQLException, JSONException {
		request.checkInput(conn, ssoEnableValidators);

		final PasswordPolicy passwordPolicy = getPasswordPolicy(conn, getMainUserId());
		if (!request.getBooleanParameter("SSOENABLED", false) && passwordPolicy.isSsoOnly()) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_SSO_ONLY_ENABLED_CANT_DISABLE_IDP");
			return;
		}

		if (enable(conn, request, response)) {
			final JSONObject json = new JSONObject();
			json.put("idpUpdate", "true");
			if (request.getBooleanParameter("SSOENABLED", false)) {
				sendSuccessMessage(request, null, json.toString(), response);
			}
			else {
				sendSuccessMessage(request, response);
			}
		}
		else {
			LOG.info("Unable to enable sso.");
		}
	}

	/**
	 * Function to enable SSO.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 * @return boolean False if error message was sent.
	 */
	private boolean enable(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, SamlException, SQLException {
		request.checkInput(conn, ssoEnableValidators);

		try {
			final Idp idp = Idp.getById(Idp.class, conn, getMainUserId());

			final boolean enableSSO = request.getBooleanParameter("SSOENABLED", false);
			final String sha = request.getParameter("SIGNATUREHASHALGORITHM");

			if (idp == null && !enableSSO) {
				return true;
			}

			if (enableSSO) {
				if (idp == null) {
					sendErrorMessage(request, response, ErrorCode.Unsupported);
					final String audit = "Single Sign On: disabled";
					audit(conn, getMainUserId(), AppName.HIAB, AuditMode.UPDATE, null, audit, getMainUserId(), true, false, null, null, false);
					conn.commit();
					return false;
				}
				if (!checkIdpMetadata(idp.getMetadata().getBytes(UTF_8), true, new DateTime(idp.getCacheDate()))) {
					if (idp.getNewMetadata() != null && !idp.getNewMetadata().isEmpty()) {
						idp.setMetadata(idp.getNewMetadata());
						idp.setNewMetadata("");
						final HashMap<String, String> customtags = new HashMap<>();
						customtags.put("PRODUCT", Configuration.isHiabEnabled() ? "HIAB" : "OUTSCAN");
						customtags.put("METADATA", idp.getMetadata());
						final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
						UserBusiness.sendEmailtoUser(conn, user.getEmail(), (Configuration.isHiabEnabled()
										? Configuration.getProperty(ConfigurationKey.mail_fromaddresshiab)
										: Configuration.getProperty(ConfigurationKey.mail_fromaddress)),
								user, "idpold", customtags, user.getEmailEncryptionKey(), -1);
					}
					else {
						LOG.warn("Unable to enable saml login due to old metadata.");
						sendErrorMessage(request, response, ErrorCode.Unsupported);
						idp.setSsoEnabled(false);
						idp.save(conn);
						conn.commit();
						return false;
					}
				}
				idp.setSignatureHashAlgorithm(sha);

				final X509Certificate certificate = loadX509Certificate(conn);
				if (!checkCertificateValidity(certificate)) {
					try {
						generateSSOPrivateKeyAndCertificate(conn, null, null);
					}
					catch (final SamlException e) {
						LOG.error("Could not regenerate SAML key/certificate", e);
						idp.setSsoEnabled(false);
						idp.save(conn);
						conn.commit();
						sendErrorMessage(request, response, ErrorCode.InternalServerError);
						return false;
					}
				}
			}

			String audit = "";
			if (idp.isSsoEnabled() != enableSSO) {
				audit = "Single Sign On: " + (enableSSO ? "enabled" : "disabled");
			}
			idp.setSsoEnabled(enableSSO);

			final Features features = Features.getById(Features.class, conn, Access.ADMIN, getMainUserId());
			features.setSsoEnabled(enableSSO);
			features.save(conn);
			idp.setUserIdField(StringUtils.setEmpty(request.getParameter("USERIDFIELD"), "uid"));
			idp.setBindToPortal(request.getBooleanParameter("BINDTOPORTAL", false));

			idp.save(conn);
			if (!audit.isEmpty()) {
				audit(conn, getMainUserId(), AppName.HIAB, AuditMode.UPDATE, null, audit, getMainUserId(), true, false, null, null, false);
			}
		}
		catch (final RuntimeException | SQLException | IOException e) {
			throw new SamlException("Could not enable/disable single sign on.", e);
		}
		conn.commit();

		return true;
	}

	/**
	 * Deletes all information about an idp for a specific user.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "DELETEIDP", roles = {Role.MainUser, Role.SuperUser})
	private void deleteIDP(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws SQLException {
		request.noParametersRequired();

		final PasswordPolicy passwordPolicy = getPasswordPolicy(conn, getMainUserId());
		if (passwordPolicy.isSsoOnly()) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_SSO_ONLY_ENABLED_CANT_DISABLE_IDP");
			return;
		}

		Idp.removeObject(Idp.class, conn, getMainUserId());
		final Features features = Features.getById(Features.class, conn, Access.ADMIN, getMainUserId());
		features.setSsoEnabled(false);
		features.save(conn);
		conn.commit();
		sendSuccessMessage(request, response);
	}

	/**
	 * Function to upload metadata for idp.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "UPLOADIDPMETADATA", roles = {Role.MainUser, Role.SuperUser})
	private void uploadIDPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, IOException, SQLException {
		request.checkInput(conn, ParamValidator.booleanValue("SSOENABLED"));

		final PasswordPolicy passwordPolicy = getPasswordPolicy(conn, getMainUserId());
		if (!request.getBooleanParameter("SSOENABLED", false) && passwordPolicy.isSsoOnly()) {
			sendErrorMessage(request, response, ErrorCode.InputValidationFailed, "_SSO_ONLY_ENABLED_CANT_DISABLE_IDP");
			return;
		}

		if (saveIDPMetadata(conn, request, response)) {
			sendSuccessMessage(request, response);
		}
	}

	/**
	 * Function to upload metadata for idp.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 * @return boolean False if error message was sent.
	 */
	private boolean saveIDPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws ParamValidationException, IOException, SQLException {
		request.checkInput(conn, ParamValidator.printable("IDPMETADATAURL"), ParamValidator.booleanValue("SSOENABLED"), ParamValidator.file("IDPMETADATAFILE"),
				ParamValidator.listOfValues("SIGNATUREHASHALGORITHM", new String[] {"SHA_1", "SHA_256"}),
				ParamValidator.printable("USERIDFIELD"), ParamValidator.booleanValue("BINDTOPORTAL"));

		final RequestFile data = request instanceof MultipartHttpServletRequest ? request.getFile("IDPMETADATAFILE") : null;
		final String url = request.getParameter("IDPMETADATAURL");
		final String sha = request.getParameter("SIGNATUREHASHALGORITHM");

		final Idp idp = Idp.fromRequest(Idp.class, request, null);
		idp.setId(getMainUserId());
		idp.setUserId(getMainUserId());
		idp.setSsoEnabled(false);
		idp.setSignatureHashAlgorithm(sha);
		idp.setCacheDate(new Date());
		if (data != null && data.getSize() > 0) {
			idp.setMetadata(new String(data.getData(), UTF_8));
		}
		else if (url != null && !url.isEmpty()) {
			String metadata = "";
			try {
				metadata = readURL(conn, url);
			}
			catch (final SamlException e) {
				LOG.info("Unable to save idp metadata. " + e.getMessage());
				sendErrorMessage(request, response, ErrorCode.Unsupported);
				return false;
			}
			if (metadata.isEmpty()) {
				LOG.info("Unable to save idp metadata. Empty metadata.");
				sendErrorMessage(request, response, ErrorCode.Unsupported);
				return false;
			}
			idp.setMetadata(metadata);
		}
		else {
			LOG.info("Unable to save idp metadata. No metadata or url.");
			sendErrorMessage(request, response, ErrorCode.Unsupported);
			return false;
		}

		try {
			if (!checkIdpMetadata(idp.getMetadata().getBytes(UTF_8), true, new DateTime(idp.getCacheDate()))) {
				final List<EntityDescriptor> entityList = getEntityList(idp.getMetadata().getBytes(UTF_8));
				if (entityList != null && !StringUtils.isEmpty(url)) {
					for (final EntityDescriptor entity : entityList) {
						try {
							final String newMetadata = readURL(conn, entity.getEntityID());
							if (!newMetadata.isEmpty() && checkIdpMetadata(newMetadata.getBytes(UTF_8), true, new DateTime(idp.getCacheDate()))) {
								idp.setMetadata(newMetadata);
								break;
							}
							LOG.info("Unable to save idp metadata. Metadata check failed.");
							sendErrorMessage(request, response, ErrorCode.Unsupported);
							return false;
						}
						catch (final Exception e) {
							LOG.info("Unable to save idp metadata. " + e.getMessage());
							sendErrorMessage(request, response, ErrorCode.Unsupported);
							return false;
						}
					}
				}
				else {
					sendErrorMessage(request, response, ErrorCode.Unsupported);
					return false;
				}
			}
		}
		catch (final Exception e) {
			LOG.info("Unable to save idp metadata. " + e.getMessage());
			sendErrorMessage(request, response, ErrorCode.Unsupported);
			return false;
		}
		idp.save(conn);

		final String audit = "New metadata for an identity provider was added.";
		audit(conn, getMainUserId(), AppName.HIAB, AuditMode.UPDATE, null, audit, getMainUserId(), true, false, null, null, false);
		conn.commit();

		return true;
	}

	/**
	 * Function to send a file with sp metadata.
	 *
	 * @param conn Database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "DOWNLOADSPMETADATA", roles = {Role.MainUser, Role.SuperUser})
	private void downloadSPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws IOException, SamlException, SQLException {
		request.noParametersRequired();

		X509Certificate certificate = loadX509Certificate(conn);
		if (!checkCertificateValidity(certificate)) {
			generateSSOPrivateKeyAndCertificate(conn, null, null);
			certificate = loadX509Certificate(conn);

			if (certificate == null) {
				throw new SamlException("Could not regenerate SAML key/certificate");
			}
		}

		final Idp idp = Idp.getById(Idp.class, conn, Access.ADMIN, getMainUserId());
		final Customer customer = Customer.getByField(Customer.class, conn, Access.ADMIN, "userId", getMainUserId());
		final String metadataXML = buildSPMetadata(certificate, request, customer.getUuid());
		if (StringUtils.isEmpty(metadataXML)) {
			throw new SamlException("Could not build service provider metadata");
		}

		// Upload
		final byte[] bytes = metadataXML.getBytes(UTF_8);
		final OutputStream stream = response.getOutputStream();
		response.setHeader("Content-Disposition", "attachment; filename=\"" + (Configuration.isHiabEnabled() ? "HIAB" : "OUTSCAN") + "Metadata\"");
		try (final InputStream input = new ByteArrayInputStream(bytes)) {
			IOUtils.copy(input, stream);
		}
		if (idp != null && !idp.isHasUUIDField()) {
			idp.setHasUUIDField(true);
			idp.save(conn);
			conn.commit();
		}
	}

	/**
	 * Function to show sp metadata.
	 *
	 * @param conn Database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "SHOWSPMETADATA", requireLogin = false, ignoreCsrf = true)
	private void showSPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws IOException, SamlException, ParamValidationException {
		request.checkInput(conn, ParamValidator.printable("UUID"));

		X509Certificate certificate = loadX509Certificate(conn);
		if (!checkCertificateValidity(certificate)) {
			generateSSOPrivateKeyAndCertificate(conn, null, null);
			certificate = loadX509Certificate(conn);

			if (certificate == null) {
				throw new SamlException("Could not regenerate SAML key/certificate");
			}
		}

		final String metadataXML;
		try {
			metadataXML = buildSPMetadata(certificate, request, request.getParameter("UUID"));
			if (StringUtils.isEmpty(metadataXML)) {
				throw new SamlException("Could not build service provider metadata");
			}
		}
		catch (final RuntimeException e) {
			throw new SamlException("Could not show service provider metadata", e);
		}

		response.getWriter().print(metadataXML);
	}

	/**
	 * Function to check current sso keys for validity.
	 *
	 * @param conn Database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "VALIDATESPMETADATA", roles = {Role.MainUser, Role.SuperUser})
	private void validateSPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws JSONException {
		request.noParametersRequired();

		final JSONObject json = new JSONObject();
		final X509Certificate certificate = loadX509Certificate(conn);

		json.put("update", !checkCertificateValidity(certificate));
		sendSuccessMessage(request, null, json.toString(), response);
	}

	/**
	 * Function to show identity provider metadata.
	 *
	 * @param conn Database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "SHOWIDPMETADATA", roles = {Role.MainUser, Role.SuperUser})
	private void showIDPMetadata(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws SQLException, IOException {
		request.noParametersRequired();

		final Idp idp = Idp.getById(Idp.class, conn, Access.ADMIN, getMainUserId());
		if (idp == null) {
			sendErrorMessage(request, response, ErrorCode.Unsupported, "_IDP_METADATA_NOT_AVAILABLE");
			return;
		}

		response.getWriter().print(idp.getMetadata());
	}

	/**
	 * Function to receive saml response and send it forward.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "SAMLRESPONSE", requireLogin = false, ignoreCsrf = true)
	private void samlResponse(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws IOException, SQLException, ParamValidationException {
		request.checkInput(conn, ParamValidator.printable("SAMLResponse"), ParamValidator.printable("UUID"));
		final String customerUuid = request.getParameter("UUID");
		final Customer customer = customerUuid != null ? Customer.getByField(Customer.class, conn, Access.ADMIN, "uuid", customerUuid) : null;
		final Idp idp = customer != null ? Idp.getById(Idp.class, conn, Access.ADMIN, customer.getUserId()) : null;
		final boolean isK8sEnabled = RuntimeEnvironment.isKubernetesEnabled();

		if (request.getParameter("SAMLResponse") == null) {
			sendErrorMessage(request, response, ErrorCode.FieldsMissing);
			return;
		}

		final SamlToken samlToken = new SamlToken();
		samlToken.setId(UserBusiness.cryptPassword(false, "" + System.currentTimeMillis()));
		samlToken.setSamlResponse(request.getParameter("SAMLResponse"));
		samlToken.setExpire(DateUtils.getDateTimeOffset(null, 5));
		samlToken.setIdpId(idp != null ? idp.getId() : null);
		samlToken.setIpAddress(request.getRemoteAddr());
		samlToken.saveObjectId(conn, Access.ADMIN, null, null);
		conn.commit();

		// Redirect with encrypt id to SamlToken
		final String redirectString =
				request.getRequestURL().toString().replaceAll("http:", "https:").replaceAll(isK8sEnabled ? "/XMLAPI" : "/opi/XMLAPI", "") + "?SAMLTOKEN=" + TwoWayEncoding.encode(samlToken.getId(),
						Configuration.getConfigService())
						+ (idp != null && idp.isBindToPortal() ? "&PORTAL=true" : "");
		LOG.info("SAMLRESPONSE - redirect url: " + redirectString);
		response.sendRedirect(redirectString);
	}

	/**
	 * Helper method for ensuring that the given certificate is within its validity period.
	 *
	 * @param certificate X.509 certificate.
	 * @return boolean True if valid, otherwise false.
	 */
	private boolean checkCertificateValidity(final X509Certificate certificate) {
		if (certificate == null) {
			return false;
		}

		try {
			certificate.checkValidity();
		}
		catch (final CertificateExpiredException | CertificateNotYetValidException e) {
			return false;
		}

		return true;
	}

	/**
	 * Constructs Service Provider metadata XML.
	 *
	 * @param certificate The Service Provider X.509 certificate.
	 * @param request The http servlet request that the request originates from.
	 * @param customerUuid the uuid of customer that uses the idP configuration
	 * @return String with valid xml-encoded service provider metadata
	 */
	private String buildSPMetadata(final X509Certificate certificate, final HttpRequestWrapper request, final String customerUuid) throws SamlException {
		final String result;
		final Document document;
		final boolean isK8sEnabled = RuntimeEnvironment.isKubernetesEnabled();
		try {
			// Build later used objects
			final EntityDescriptor spEntityDescriptor = (EntityDescriptor) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(EntityDescriptor.DEFAULT_ELEMENT_NAME).buildObject(EntityDescriptor.DEFAULT_ELEMENT_NAME);
			final SPSSODescriptor spSSODescriptor = (SPSSODescriptor) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(SPSSODescriptor.DEFAULT_ELEMENT_NAME).buildObject(SPSSODescriptor.DEFAULT_ELEMENT_NAME);
			final NameIDFormat nameIDFormat = (NameIDFormat) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(NameIDFormat.DEFAULT_ELEMENT_NAME).buildObject(NameIDFormat.DEFAULT_ELEMENT_NAME);
			final AssertionConsumerService assertionConsumerService = (AssertionConsumerService) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(AssertionConsumerService.DEFAULT_ELEMENT_NAME).buildObject(AssertionConsumerService.DEFAULT_ELEMENT_NAME);
			final KeyDescriptor encKeyDescriptor = (KeyDescriptor) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(KeyDescriptor.DEFAULT_ELEMENT_NAME).buildObject(KeyDescriptor.DEFAULT_ELEMENT_NAME);
			final KeyDescriptor signKeyDescriptor = (KeyDescriptor) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(KeyDescriptor.DEFAULT_ELEMENT_NAME).buildObject(KeyDescriptor.DEFAULT_ELEMENT_NAME);
			final KeyInfo encKeyInfo = (KeyInfo) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(KeyInfo.DEFAULT_ELEMENT_NAME).buildObject(KeyInfo.DEFAULT_ELEMENT_NAME);
			final KeyInfo signKeyInfo = (KeyInfo) org.opensaml.xml.Configuration.getBuilderFactory()
					.getBuilder(KeyInfo.DEFAULT_ELEMENT_NAME).buildObject(KeyInfo.DEFAULT_ELEMENT_NAME);

			// Descriptor
			spSSODescriptor.setWantAssertionsSigned(true);
			spSSODescriptor.setAuthnRequestsSigned(true);
			nameIDFormat.setFormat("urn:oasis:names:tc:SAML:2.0:nameid-format:transient");
			spSSODescriptor.getNameIDFormats().add(nameIDFormat);
			assertionConsumerService.setIndex(0);
			assertionConsumerService.setBinding(SAMLConstants.SAML2_POST_BINDING_URI);
			assertionConsumerService.setLocation(
					request.getRequestURL().toString().replaceAll("http:", "https:").replaceAll(isK8sEnabled ? "/XMLAPI" : "/opi/XMLAPI", "/opi/XMLAPI") + "?ACTION=SAMLRESPONSE" + (customerUuid != null ? "&UUID=" + customerUuid : ""));
			spSSODescriptor.getAssertionConsumerServices().add(assertionConsumerService);
			spSSODescriptor.addSupportedProtocol(SAMLConstants.SAML20P_NS);

			// Keys and certificates
			KeyInfoHelper.addCertificate(encKeyInfo, certificate);
			KeyInfoHelper.addCertificate(signKeyInfo, certificate);
			encKeyDescriptor.setUse(UsageType.ENCRYPTION);
			encKeyDescriptor.setKeyInfo(encKeyInfo);
			spSSODescriptor.getKeyDescriptors().add(encKeyDescriptor);
			signKeyDescriptor.setUse(UsageType.SIGNING);
			signKeyDescriptor.setKeyInfo(signKeyInfo);
			spSSODescriptor.getKeyDescriptors().add(signKeyDescriptor);

			// Entity
			spEntityDescriptor.setEntityID(
					request.getRequestURL().toString().replaceAll("http:", "https:").replaceAll(isK8sEnabled ? "/XMLAPI" : "/opi/XMLAPI", "/opi/XMLAPI") + "?ACTION=SHOWSPMETADATA" + (customerUuid != null ? "&UUID=" + customerUuid : ""));
			spEntityDescriptor.getRoleDescriptors().add(spSSODescriptor);
			spEntityDescriptor.setValidUntil(new DateTime(certificate.getNotAfter()));

			// Marshall, sign and convert to a XML string
			document = XMLObject.createNewDocumentBuilder(false).newDocument();
			org.opensaml.xml.Configuration.getMarshallerFactory().getMarshaller(spEntityDescriptor).marshall(spEntityDescriptor, document);
			try (final StringWriter stringWriter = new StringWriter()) {
				TransformerFactory.newInstance().newTransformer().transform(new DOMSource(document), new StreamResult(stringWriter));
				result = stringWriter.toString();
			}
		}
		catch (final RuntimeException | CertificateEncodingException | IOException | TransformerException | TransformerFactoryConfigurationError | MarshallingException e) {
			throw new SamlException("Could not build service provider metadata", e);
		}
		return result;
	}

	/**
	 * Function to sign a X509Certificate.
	 *
	 * @param inputCSR PKCS10CertificationRequest
	 * @param serial Random positive long.
	 * @param publicKey A public key that matches inputCSR.
	 * @param fromDate The first valid Date.
	 * @param toDate The last valid date.
	 * @return signed X509Certificate.
	 */
	private static X509Certificate signCertificate(final PKCS10CertificationRequest inputCSR, final long serial, final PublicKey publicKey, final Date fromDate,
												   final Date toDate) throws SamlException {
		final AlgorithmIdentifier sigAlgId = new DefaultSignatureAlgorithmIdentifierFinder().find("SHA256withRSA");
		final AlgorithmIdentifier digAlgId = new DefaultDigestAlgorithmIdentifierFinder().find(sigAlgId);

		final String keyFileName = new StringBuilder().append(XMLAPI.getConfpath()).append("rootCA.key").toString();
		try (final PEMParser pemParser = new PEMParser(new InputStreamReader(Files.newInputStream(Paths.get(keyFileName)), UTF_8))) {
			final Object pem = pemParser.readObject();

			final SubjectPublicKeyInfo keyInfo = SubjectPublicKeyInfo.getInstance(publicKey.getEncoded());
			final AsymmetricKeyParameter akparam = PrivateKeyFactory.createKey(((PEMKeyPair) pem).getPrivateKeyInfo());

			Date from = fromDate;
			Date to = toDate;
			if (fromDate == null) {
				from = new Date(System.currentTimeMillis());
			}
			if (toDate == null) {
				final Calendar cal = Calendar.getInstance();
				cal.add(Calendar.MONTH, 6);
				to = cal.getTime();
			}

			final X509v3CertificateBuilder myCertificateGenerator =
					new X509v3CertificateBuilder(new X500Name("C=SE, L=Karlskrona, O=Outpost24, CN=outpost24.com"), BigInteger.valueOf(serial), from, to,
							inputCSR.getSubject(), keyInfo);
			myCertificateGenerator.addExtension(Extension.subjectKeyIdentifier, false, new JcaX509ExtensionUtils().createSubjectKeyIdentifier(publicKey));

			final ContentSigner sigGen = new BcRSAContentSignerBuilder(sigAlgId, digAlgId).build(akparam);

			final X509CertificateHolder holder = myCertificateGenerator.build(sigGen);
			final org.bouncycastle.asn1.x509.Certificate eeX509CertificateStructure = holder.toASN1Structure();

			final CertificateFactory cf = CertificateFactory.getInstance("X.509");

			try (final InputStream is1 = new ByteArrayInputStream(eeX509CertificateStructure.getEncoded())) {
				return (X509Certificate) cf.generateCertificate(is1);
			}
		}
		catch (final RuntimeException | CertificateException | IOException | OperatorCreationException | NoSuchAlgorithmException e) {
			throw new SamlException("Could not sign certificate for saml use.", e);
		}
	}

	/**
	 * Generates new Service Provider private key and certificate.
	 * These records are committed to the data store service.
	 *
	 * @param dbConnection Database connection.
	 * @param fromDate The first valid date for the certificate. Will be today if null.
	 * @param toDate The last valid date for the certificate. Will be in 6 months if null.
	 */
	public static void generateSSOPrivateKeyAndCertificate(final Connection dbConnection, final Date fromDate, final Date toDate) throws SamlException {
		LOG.info("Attempting to generate new Service Provider key and certificate");
		try {
			final DataStoreService dsService = ServiceProvider.getDataStoreService(dbConnection);

			final KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
			keyGen.initialize(Configuration.isDevMode() ? 1024 : 4096, SECURE_RANDOM);
			final KeyPair keyPair = keyGen.generateKeyPair();

			final StringBuilder sb = new StringBuilder();
			StringUtils.addXid(sb, "CN=" + (Configuration.isHiabEnabled() ? "HIAB" : "OUTSCAN"));
			StringUtils.addXid(sb, "O=OUTPOST24");
			final X500Name name = new X500Name(sb.toString());

			final AlgorithmIdentifier sigAlgId = new DefaultSignatureAlgorithmIdentifierFinder().find("SHA256withRSA");
			final AlgorithmIdentifier digAlgId = new DefaultDigestAlgorithmIdentifierFinder().find(sigAlgId);
			final AsymmetricKeyParameter keyParam = PrivateKeyFactory.createKey(keyPair.getPrivate().getEncoded());
			final ContentSigner signer = new BcRSAContentSignerBuilder(sigAlgId, digAlgId).build(keyParam);

			final PKCS10CertificationRequestBuilder requestBuilder =
					new PKCS10CertificationRequestBuilder(name, SubjectPublicKeyInfo.getInstance(ASN1Sequence.getInstance(keyPair.getPublic().getEncoded())));
			final PKCS10CertificationRequest req = requestBuilder.build(signer);
			final X509Certificate certificate = signCertificate(req, SECURE_RANDOM.nextInt(Integer.SIZE - 1), keyPair.getPublic(), fromDate, toDate);

			final String strCert = Base64.encodeBytes(certificate.getEncoded(), Base64.DONT_BREAK_LINES);
			final String privKey = Base64.encodeBytes(keyPair.getPrivate().getEncoded(), Base64.DONT_BREAK_LINES);
			final String strKey = TwoWayEncoding.encode(privKey, Configuration.getConfigService());

			// Commit generated records to data store.
			dsService.setEntry(DataStoreEntryKeys.SSO_PRIVATE_KEY.name(), strKey);
			dsService.setEntry(DataStoreEntryKeys.SSO_CERTIFICATE.name(), strCert);
			dbConnection.commit();
		}
		catch (final RuntimeException | IOException | CertificateEncodingException | NoSuchAlgorithmException | OperatorCreationException | SQLException e) {
			throw new SamlException("Could not generate new keys for saml use.", e);
		}
	}

	/**
	 * Retrieves the private key from the datastore.
	 *
	 * @param dbConnection Database connection.
	 * @return PrivateKey. Return null if it does not exist.
	 */
	private static PrivateKey loadPrivateKey(final Connection dbConnection) {
		try {
			final DataStoreService dsService = ServiceProvider.getDataStoreService(dbConnection);
			final DataStoreEntry dsPrivateKey = dsService.getEntry(DataStoreEntryKeys.SSO_PRIVATE_KEY.name());

			if (dsPrivateKey == null) {
				LOG.warn("No Service Provider private key in data store");
				return null;
			}

			final byte[] privateKey = Base64.decode(TwoWayEncoding.decode(dsPrivateKey.getValue(), Configuration.getConfigService()));
			if (privateKey == null) {
				LOG.error("Could not decode Service Provider private key");
				return null;
			}

			return KeyFactory
					.getInstance("RSA")
					.generatePrivate(new PKCS8EncodedKeySpec(privateKey));
		}
		catch (final SQLException | InvalidKeySpecException | NoSuchAlgorithmException e) {
			LOG.error("Could not load Service Provider private key", e);
			return null;
		}
	}

	/**
	 * Retrieves the X.509 certificate from the datastore.
	 *
	 * @param dbConnection Database connection.
	 * @return X509Certificate. Return null if it does not exist.
	 */
	private static X509Certificate loadX509Certificate(final Connection dbConnection) {
		try {
			final DataStoreService dsService = ServiceProvider.getDataStoreService(dbConnection);
			final DataStoreEntry dsCertificate = dsService.getEntry(DataStoreEntryKeys.SSO_CERTIFICATE.name());

			if (dsCertificate == null) {
				LOG.warn("No Service Provider certificate in data store");
				return null;
			}

			return (X509Certificate) CertificateFactory
					.getInstance("X.509")
					.generateCertificate(new ByteArrayInputStream(Base64.decode(dsCertificate.getValue())));
		}
		catch (final SQLException | CertificateException e) {
			LOG.warn("Could not load Service Provider certificate", e);
			return null;
		}
	}

	/**
	 * Function to build a DOMMetadataProvider for OpenSaml use.
	 *
	 * @param data InputStream with the metadata.
	 * @return DOMMetadataProvider
	 */
	private DOMMetadataProvider buildMetadataProvider(final InputStream data) throws SamlException {
		final DOMMetadataProvider mdProvider;
		try {
			final DocumentBuilder docBuilder = XMLObject.createNewDocumentBuilder(false, true);
			mdProvider = new DOMMetadataProvider(docBuilder.parse(data).getDocumentElement());
			mdProvider.initialize();
		}
		catch (final RuntimeException | MetadataProviderException | SAXException | IOException e) {
			throw new SamlException("Could not build MetadataProvider. " + e.getMessage(), e);
		}
		return mdProvider;
	}

	/**
	 * Reads an URL and converts it to a String.
	 *
	 * @param conn A database connection.
	 * @param url The URL to read.
	 * @return String with the content from the URL.
	 */
	private String readURL(final Connection conn, final String url) throws SamlException {
		String metadataXML = "";
		try {
			// TODO we need to check the purpose of the following check.
			// If purpose is to validate the given url then we may use IpUtils.isValidUrl but it allows only http & https, not sure if we can ever get other protocols here
			// If purpose is to check the host, it different story then
			// Not sure why we are only checking on Outscan
			if (!Configuration.isHiabEnabled() && !(IpUtils.isValid(new URL(url).getHost()) || IpUtils.isHostname(conn, new URL(url).getHost()))) {
				return "";
			}
			final URL url2 = new URL(url);
			final URLConnection urlConnection;
			if ("https".equals(url2.getProtocol())) {
				urlConnection = SslUtils.getTrustAllHttpsConnection(url2);
			}
			else {
				urlConnection = url2.openConnection();
			}
			urlConnection.setRequestProperty("User-Agent", Configuration.isHiabEnabled() ? "HIAB" : "OUTSCAN");
			final InputStream is = urlConnection.getInputStream();
			metadataXML = IOUtils.toString(is, UTF_8);
		}
		catch (final RuntimeException | IOException | SQLException e) {
			throw new SamlException("Could not read URL.", e);
		}
		return metadataXML;
	}

	/**
	 * Compares idp metadata to our requirements.
	 *
	 * @param metadata byte array with idp metadata to check.
	 * @param twoWeeks True if there should be a 2 weeks margin until valid date. Should be set for updates like enable.
	 * @param cacheDate the date when the metadata was cached. This is what the cacheDuration will be compared to and won't be used if there is no cacheDuration.
	 * @return boolean True if a match, false if not a match.
	 */
	private boolean checkIdpMetadata(final byte[] metadata, final boolean twoWeeks, final DateTime cacheDate) throws SamlException {
		try {
			final DOMMetadataProvider mdProvider = buildMetadataProvider(new ByteArrayInputStream(metadata));
			SingleSignOnService endpoint = null;
			boolean validDate = false;

			if (mdProvider.getMetadata() instanceof EntitiesDescriptor) {
				validDate = true;
				final EntitiesDescriptor entities = (EntitiesDescriptor) mdProvider.getMetadata();
				final DateTime valid = entities.getValidUntil();
				final Long cacheDuration = entities.getCacheDuration();
				if ((valid == null || (twoWeeks && !valid.minusDays(14).isAfterNow()) || !valid.isAfterNow()) &&
						(cacheDuration == null || (twoWeeks && !cacheDate.plusMillis(cacheDuration.intValue()).minusDays(14).isAfterNow()) || !cacheDate.plusMillis(
								cacheDuration.intValue()).isAfterNow())) {
					validDate = false;
				}
			}
			final List<EntityDescriptor> entityList = getEntityList(metadata);
			if (entityList == null) {
				return false;
			}
			for (final EntityDescriptor entity : entityList) {
				final DateTime valid = entity.getValidUntil();
				final Long cacheDuration = entity.getCacheDuration();
				if (!validDate && ((valid == null || (twoWeeks && !valid.minusDays(14).isAfterNow()) || !valid.isAfterNow()) &&
						(cacheDuration == null || (twoWeeks && !cacheDate.plusMillis(cacheDuration.intValue()).minusDays(14).isAfterNow()) || !cacheDate.plusMillis(
								cacheDuration.intValue()).isAfterNow()))) {
					return false;
				}
				final IDPSSODescriptor ssoDescriptor = entity.getIDPSSODescriptor(SAMLConstants.SAML20P_NS);
				if (ssoDescriptor != null) {
					for (final SingleSignOnService ssoService : ssoDescriptor.getSingleSignOnServices()) {
						if (ssoService.getBinding().equals(SAMLConstants.SAML2_REDIRECT_BINDING_URI)) {
							endpoint = ssoService;
						}
					}
				}
			}
			if (endpoint == null || endpoint.getLocation() == null || endpoint.getLocation().equals("null")) {
				return false;
			}
		}
		catch (final RuntimeException | MetadataProviderException e) {
			throw new SamlException("Could not complete idp metadata check. Metadata: " + new String(metadata, UTF_8), e);
		}
		return true;
	}

	/**
	 * Extracts a list of EntityDescriptors from metadata. If metadata contains EntitiesDescriptor this might be many, if only EntityDescriptor the List will contain only one object.
	 *
	 * @param metadata Byte array with metadata.
	 * @return List EntityDescriptor All EntityDescriptors from the metadata.
	 */
	private List<EntityDescriptor> getEntityList(final byte[] metadata) {
		List<EntityDescriptor> entityList = null;
		try {
			final DOMMetadataProvider mdProvider = buildMetadataProvider(new ByteArrayInputStream(metadata));
			if (mdProvider.getMetadata() instanceof EntitiesDescriptor) {
				final EntitiesDescriptor entities = (EntitiesDescriptor) mdProvider.getMetadata();
				if (entities != null) {
					entityList = entities.getEntityDescriptors();
				}
			}
			else if (mdProvider.getMetadata() instanceof EntityDescriptor) {
				entityList = new ArrayList<>();
				entityList.add((EntityDescriptor) mdProvider.getMetadata());
			}
		}
		catch (final RuntimeException | MetadataProviderException | SamlException e) {
			return null;
		}
		return entityList;
	}

	/**
	 * Function to prepare a Login object for sso login.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 * @return Login with username, but nothing else. Returns null if failed.
	 */
	public Login logon(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response) throws SQLException {
		final Login login = new Login();
		try {
			// Get the SamlToken with the request
			final SamlToken samlResponse = SamlToken.get(SamlToken.class, conn, DbHelper.getSelect(SamlToken.class, Access.ADMIN, "xid=?"),
					TwoWayEncoding.decode(request.getParameter("SAMLTOKEN"), Configuration.getConfigService()));
			if (samlResponse == null) {
				LOG.info("The recieved SAML token was not found in DB.");
				sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_SAML_TOKEN_NOT_FOUND");
				return null;
			}
			SamlToken.executeUpdate(conn, "DELETE FROM tsamltokens WHERE xid=?", samlResponse.getId());
			conn.commit();

			final DocumentBuilder docBuilder = XMLObject.createNewDocumentBuilder(false);
			final ByteArrayInputStream is = new ByteArrayInputStream(Base64.decode(samlResponse.getSamlResponse()));
			final Document document = docBuilder.parse(is);
			final Element element = document.getDocumentElement();
			final Unmarshaller unmarshaller = org.opensaml.Configuration.getUnmarshallerFactory().getUnmarshaller(element);
			final Response SAMLResponse = (Response) unmarshaller.unmarshall(element);
			final SamlToken samlRequestToken =
					SamlToken.get(SamlToken.class, conn, DbHelper.getSelect(SamlToken.class, Access.ADMIN, "xid=?"), SAMLResponse.getInResponseTo());
			final boolean idPInitiated = SAMLResponse.getInResponseTo() == null;

			if (samlRequestToken != null) {
				SamlToken.executeUpdate(conn, "DELETE FROM tsamltokens WHERE xid=?", samlRequestToken.getId());
				conn.commit();
			}
			if (!idPInitiated && (samlRequestToken == null || !samlRequestToken.getExpire().after(new Date()) || !samlResponse.getExpire().after(new Date()))) {
				LOG.info("Matching SAML request was not found. This can be the result of a slow login or an unknown SAML response.");
				sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_NO_MATCHING_SAML_TOKEN_FOUND");
				return null;
			}

			// Without a username we can't log in.
			if (SAMLResponse.getAssertions().isEmpty() && SAMLResponse.getEncryptedAssertions().isEmpty()) {
				LOG.debug("SAML Response did not include enough information for a login.");
				sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_NO_ASSERTION_FOUND");
				return null;
			}

			// Get Assertions, unencrypt the encrypted ones.
			List<Assertion> assertions = new ArrayList<>();
			if (!SAMLResponse.getAssertions().isEmpty()) {
				assertions = SAMLResponse.getAssertions();
			}
			if (!SAMLResponse.getEncryptedAssertions().isEmpty()) {
				// Get our private key and create credentials
				final BasicCredential decryptionCredential = new BasicCredential();
				final PrivateKey privKey = loadPrivateKey(conn);
				if (privKey == null) {
					LOG.error("Saml login failed due to key problems.");
					sendErrorMessage(request, response, ErrorCode.InternalServerError);
				}
				decryptionCredential.setPrivateKey(privKey);

				// Create a decrypter.
				final Decrypter decrypter = new Decrypter(null, new StaticKeyInfoCredentialResolver(decryptionCredential), new InlineEncryptedKeyResolver());
				decrypter.setRootInNewDocument(true);

				// Decrypt
				for (final EncryptedAssertion encAssertion : SAMLResponse.getEncryptedAssertions()) {
					assertions.add(decrypter.decrypt(encAssertion));
				}
			}

			// Load the idp's metadata
			final Idp idp = Idp.getById(Idp.class, conn, idPInitiated ? samlResponse.getIdpId() : samlRequestToken.getIdpId());
			final DOMMetadataProvider mdProvider = buildMetadataProvider(new ByteArrayInputStream(idp.getMetadata().getBytes(UTF_8)));
			final ExplicitKeySignatureTrustEngine explicitTrustEngine = new ExplicitKeySignatureTrustEngine(new MetadataCredentialResolver(mdProvider),
					org.opensaml.Configuration.getGlobalSecurityConfiguration().getDefaultKeyInfoCredentialResolver());
			final CriteriaSet criteriaSet = new CriteriaSet();
			criteriaSet.add(new EntityIDCriteria(SAMLResponse.getIssuer().getValue()));
			criteriaSet.add(new MetadataCriteria(IDPSSODescriptor.DEFAULT_ELEMENT_NAME, SAMLConstants.SAML20P_NS));
			criteriaSet.add(new UsageCriteria(UsageType.SIGNING));

			// Validate each assertion and extract the username
			for (final Assertion assertion : assertions) {
				if (assertion.getSignature() == null) {
					LOG.debug("SAML Response did not include enough information to establish trust.");
					LOG.info("SAMLResponse: " + new XMLDoc(document, "", "").toString());
					sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_ASSERTIONS_SHALL_BE_SIGNED");
					return null;
				}
				try {
					if (explicitTrustEngine.validate(assertion.getSignature(), criteriaSet)) {
						final List<AttributeStatement> attributeStatements = assertion.getAttributeStatements();
						if (attributeStatements.size() > 0) {
							final List<org.opensaml.saml2.core.Attribute> attributes = attributeStatements.get(0).getAttributes();
							for (final org.opensaml.saml2.core.Attribute attrb : attributes) {
								final String friendlyName = attrb.getFriendlyName();
								final String name = attrb.getName();
								if (name.equals(idp.getUserIdField())) {
									final List<org.opensaml.xml.XMLObject> obj = attrb.getAttributeValues();
									login.setUsername("uid".equals(name) ? obj.get(0).getDOM().getTextContent().split("@")[0] : obj.get(0).getDOM().getTextContent());
									break;
								}
								if (friendlyName != null) {
									if (friendlyName.equals(idp.getUserIdField())) {
										final List<org.opensaml.xml.XMLObject> obj = attrb.getAttributeValues();
										login.setUsername(obj.get(0).getDOM().getTextContent());
										break;
									}
								}
							}
						}
					}
					else {
						LOG.info("SAML Response was not trusted.");
						LOG.info("SAMLResponse: " + new XMLDoc(document, "", "").toString());
						sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_FAILED_ASSERTION_SIGNATURE_CHECK");
						return null;
					}
				}
				catch (final SecurityException e) {
					LOG.error("SAML Response was not trusted.", e);
					LOG.info("SAMLResponse: " + new XMLDoc(document, "", "").toString());
					sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_SECURITY_ISSUE");
					return null;
				}

				if (!StringUtils.isEmpty(login.getUsername())) {
					break;
				}
			}
			if (login.getUsername() == null || (SubUser.executeCountQuery(conn, "SELECT COUNT(*) FROM tsubusers WHERE vcusername=? AND xiparentid=?",
					login.getUsername().toUpperCase(), idp.getId())
					+ User.executeCountQuery(conn, "SELECT COUNT(*) FROM tusers WHERE vcusername=? AND xid=?", login.getUsername().toUpperCase(), idp.getId()) == 0)) {
				LOG.info("SAML Response was not trusted due to lack of matching user: " + login.getUsername());
				LOG.info("SAMLResponse: " + new XMLDoc(document, "", "").toString());
				sendErrorMessage(request, response, ErrorCode.IncorrectLogin, "_NO_USER_FOUND");
				return null;
			}
		}
		catch (final RuntimeException | SamlException | DecryptionException | UnmarshallingException | SAXException | IOException e) {
			LOG.error("Login request failed", e);
			sendErrorMessage(request, response, ErrorCode.IncorrectLogin);
			return null;
		}
		return login;
	}

	/**
	 * Function to automatically update idp metadata.
	 *
	 * @param conn A database connection.
	 * @param request The http servlet request that the request originates from.
	 * @param response The http servlet response that the request originates from.
	 */
	@WebRequest(action = "AUTOUPDATEIDP", roles = {Role.MainUser, Role.SuperUser})
	private void autoUpdateIdp(final Connection conn, final HttpRequestWrapper request, final HttpServletResponse response)
			throws SQLException, SamlException, MetadataProviderException, IOException {
		updateIdp(conn, Idp.getById(Idp.class, conn, getMainUserId()));
		sendSuccessMessage(request, response);
	}

	/**
	 * Checks date on metadata for idp and updates old metadata.
	 * If 14 days left, an URL update will be attempted and a mail will be sent to MainUser/SuperUser with either the metadata we will be updating to in 14 days or a reminder to upload new metadata.
	 * If 1 days left idp will be updated, if possible.
	 *
	 * @param conn A database connection.
	 * @param idp Idp to update.
	 */
	public void updateIdp(final Connection conn, final Idp idp) throws SamlException, SQLException, MetadataProviderException, IOException {
		boolean update = false;
		boolean store = false;
		final boolean alreadyStored = (idp.getNewMetadata() != null && !idp.getNewMetadata().isEmpty());
		String url = "";
		final DateTime cacheDate = new DateTime(idp.getCacheDate());

		final DOMMetadataProvider mdProvider = buildMetadataProvider(new ByteArrayInputStream(idp.getMetadata().getBytes(UTF_8)));
		if (mdProvider.getMetadata() instanceof EntitiesDescriptor) {
			final EntitiesDescriptor entities = (EntitiesDescriptor) mdProvider.getMetadata();
			final DateTime valid = entities.getValidUntil();
			final Long cacheDuration = entities.getCacheDuration();
			if ((valid != null && !valid.isAfterNow()) || (cacheDuration != null && !cacheDate.plusMillis(cacheDuration.intValue()).isAfterNow())) {
				update = true;
			}
			else if (!alreadyStored && ((valid != null && !valid.minusDays(14).isAfterNow()) || (cacheDuration != null && !cacheDate.plusMillis(cacheDuration.intValue())
					.minusDays(14)
					.isAfterNow()))) {
				store = true;
			}
		}

		if (!update) {
			final List<EntityDescriptor> entityList = getEntityList(idp.getMetadata().getBytes(UTF_8));
			for (final EntityDescriptor entity : entityList) {
				final IDPSSODescriptor ssoDescriptor = entity.getIDPSSODescriptor(SAMLConstants.SAML20P_NS);
				if (ssoDescriptor != null) {
					for (final SingleSignOnService ssoService : ssoDescriptor.getSingleSignOnServices()) {
						if (ssoService.getBinding().equals(SAMLConstants.SAML2_REDIRECT_BINDING_URI)) {
							if (!store) {
								final DateTime valid = entity.getValidUntil();
								final Long cacheDuration = entity.getCacheDuration();
								if ((valid != null && !valid.isAfterNow()) || (cacheDuration != null && !cacheDate.plusMillis(cacheDuration.intValue()).isAfterNow())) {
									update = true;
								}
								else if (!alreadyStored && ((valid != null && !valid.minusDays(14).isAfterNow()) || (cacheDuration != null && !cacheDate.plusMillis(
										cacheDuration.intValue()).minusDays(14).isAfterNow()))) {
									store = true;
								}
							}
							url = entity.getEntityID().isEmpty() ? "" : entity.getEntityID();
						}
					}
				}
			}
		}

		final String fromMailAddress = Configuration.isHiabEnabled()
				? Configuration.getProperty(ConfigurationKey.mail_fromaddresshiab)
				: Configuration.getProperty(ConfigurationKey.mail_fromaddress);
		if (store) {
			try {
				final String metadata = readURL(conn, url);
				if (checkIdpMetadata(metadata.getBytes(UTF_8), true, new DateTime(idp.getCacheDate()))) {
					idp.setNewMetadata(metadata);
					idp.save(conn);
					final HashMap<String, String> customtags = new HashMap<>();
					customtags.put("METADATA", idp.getNewMetadata());
					final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
					UserBusiness.sendEmailtoUser(conn, user.getEmail(), fromMailAddress, user, "idpurlupdatesuccess", customtags, user.getEmailEncryptionKey(), -1);
				}
				else {
					idp.setNewMetadata("temp");
					idp.save(conn);
					final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
					UserBusiness.sendEmailtoUser(conn, user.getEmail(), fromMailAddress, user, "idpurlupdatefailed", null, user.getEmailEncryptionKey(), -1);
				}
			}
			catch (final SamlException e) {
				idp.setNewMetadata("temp");
				idp.save(conn);
				final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
				UserBusiness.sendEmailtoUser(conn, user.getEmail(), fromMailAddress, user, "idpurlupdatefailed", null, user.getEmailEncryptionKey(), -1);
			}
		}
		else if (update) {
			if (idp.getNewMetadata() != null && !idp.getNewMetadata().equals("temp")) {
				idp.setMetadata(idp.getNewMetadata());
				idp.setNewMetadata("");
				idp.setCacheDate(new Date());
				idp.save(conn);
				final HashMap<String, String> customtags = new HashMap<>();
				customtags.put("METADATA", idp.getMetadata());
				final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
				UserBusiness.sendEmailtoUser(conn, user.getEmail(), fromMailAddress, user, "idpupdate", customtags, user.getEmailEncryptionKey(), -1);
			}
			else {
				idp.setSsoEnabled(false);
				idp.setNewMetadata("");
				idp.save(conn);
				final BaseLoggedOnUser user = LoggedOnUser.getById(LoggedOnUser.class, conn, Access.ADMIN, idp.getId());
				UserBusiness.sendEmailtoUser(conn, user.getEmail(), fromMailAddress, user, "idpold", null, user.getEmailEncryptionKey(), -1);
			}
		}

		conn.commit();
	}
}
