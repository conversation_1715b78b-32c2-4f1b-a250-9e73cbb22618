DO $$
	BEGIN
		CREATE OR REPLACE FUNCTION addColumn(_table REGCLASS, _column TEXT, _type TEXT) RETURNS bool AS
		$FUNC$
		BEGIN
			IF EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = _table AND attname = LOWER(_column) AND NOT attisdropped) THEN
				RETURN FALSE;
			ELSE
				RAISE WARNING 'Creating % on %', _column, _table;
				EXECUTE format('ALTER TABLE %s ADD COLUMN %s %s', _table, _column, _type);
				RETURN TRUE;
			END IF;
		END
		$FUNC$	LANGUAGE plpgsql;

		PERFORM addColumn('trules', 'cvssV3Score', 'DECIMAL(10, 1)');
		PERFORM addColumn('trules', 'cvssV3Vector', 'VARCHAR(100)');
		PERFORM addColumn('tproductinformation', 'updatedby', 'BIGINT');
		PERFORM addColumn('tproductinformation', 'updated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('tpatchsupersedence', 'updatedby', 'BIGINT');
		PERFORM addColumn('tpatchsupersedence', 'updated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('tpatchsupersedence', 'deleted', 'BOOLEAN DEFAULT FALSE');

		CREATE SEQUENCE IF NOT EXISTS texploits_seq
			START WITH 1000
			INCREMENT BY 1
			NO MAXVALUE
			NO MINVALUE
			CACHE 1;

		PERFORM addColumn('texploits', 'approved', 'boolean');
		PERFORM addColumn('texploits', 'id', 'bigint NOT NULL DEFAULT nextval(''texploits_seq''::regclass)');
		IF EXISTS(SELECT column_default FROM information_schema.columns col WHERE col.column_default IS NOT NULL AND table_name='texploits' and column_name='id') THEN
			ALTER TABLE texploits ALTER COLUMN id DROP DEFAULT;
			ALTER TABLE texploits ADD CONSTRAINT texploits_pkey PRIMARY KEY (id);
		END IF;

		PERFORM addColumn('trules', 'readyforreview', 'BOOLEAN');
		PERFORM addColumn('trulesdef', 'hidereport', 'BOOLEAN');
		IF (SELECT addColumn('trules', 'specialNotes', 'specialNoteType[]')) THEN
			UPDATE trules SET specialNotes = array_append(ARRAY[]::specialNoteType[], specialnote) WHERE specialnote IS NOT NULL AND specialNote != 'NONE';
		END IF;
		PERFORM addColumn('trules', 'cwe', 'INTEGER');
		PERFORM addColumn('trules', 'operation', 'SMALLINT');
		PERFORM addColumn('tproductinformation', 'signaturekeys', 'TEXT[]');
		PERFORM addColumn('trules', 'cyrating', 'DECIMAL(10, 2) NOT NULL DEFAULT 1.0');
		PERFORM addColumn('trules', 'exploitprobability', 'DECIMAL(10, 2) NOT NULL DEFAULT 0');
		PERFORM addColumn('trules', 'previouscyrating', 'DECIMAL(10, 2) NOT NULL DEFAULT 1.0');
		PERFORM addColumn('trules', 'previousexploitprobability', 'DECIMAL(10, 2) NOT NULL DEFAULT 0');
		PERFORM addColumn('trules', 'cyr3conupdated', 'TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()');
		PERFORM addColumn('classifications', 'securecodewarrior', 'JSONB');
		IF EXISTS(SELECT column_name FROM information_schema.columns WHERE table_name='texploits' AND column_name='identifier' AND data_type='character varying') THEN
			ALTER TABLE texploits ALTER COLUMN identifier TYPE text;
		END IF;
		PERFORM addColumn('cyr3con', 'lastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'cyratinglastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('truleshistory', 'cyratinglastseen', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'productid', 'INTEGER');
		PERFORM addColumn('trules', 'bluelivupdated', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'bluelivscore', 'FLOAT');
		PERFORM addColumn('trules', 'bluelivdelta', 'FLOAT');
		PERFORM addColumn('trules', 'bluelivlastthreatactivity', 'TIMESTAMP WITHOUT TIME ZONE');
		PERFORM addColumn('trules', 'bluelivmentions', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'bluelivthreatactors', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'bluelivexploits', 'INTEGER DEFAULT 0 NOT NULL');
		PERFORM addColumn('trules', 'farsight', 'JSONB');
		PERFORM addColumn('truleshistory', 'farsight', 'JSONB');
		UPDATE trules SET productid = (SELECT xid FROM tproductinformation WHERE product = solutionproduct) WHERE productid IS NULL;

		DROP FUNCTION addColumn(REGCLASS, TEXT, TEXT);
	END;
$$;
