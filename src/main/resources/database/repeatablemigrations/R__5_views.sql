SET standard_conforming_strings = off;
SET check_function_bodies = false;
SET client_min_messages = warning;
SET escape_string_warning = off;
SET default_tablespace = '';

----
--- Name: vsubroles; Type: VIEW; Schema: public; Owner: -
---

DROP VIEW IF EXISTS vsubroles CASCADE;
CREATE OR REPLACE VIEW vsubroles AS
		SELECT x.xsubxid,
		MAX(g.boemail) AS boemail, MAX(g.bosettings) AS bosettings, MAX(g.boreports) AS boreports,
		MAX(g.boschedules) AS boschedules, MAX(g.bsubadmin) AS bsubadmin, MAX(g.boadmingroups) AS boadmingroups,
		MAX(g.bhadmin) AS bhadmin, MAX(g.bosms) AS bosms, MAX(g.bodisable) AS bodisable,
		MAX(g.bovultext) AS bovultext, MAX(g.bodeletereport) AS bodeletereport, MAX(g.badminusergroup) AS badminusergroup,
		MAX(g.bacceptrisk) AS bacceptrisk, MAX(g.pciscoping) AS pciscoping, MAX(g.pcischeduling) AS pcischeduling,
		MAX(g.pcireporting) AS pcireporting, MAX(g.pcidisputing) AS pcidisputing, MAX(g.webappadmin) AS webappadmin,
		MAX(g.webappreporting) AS webappreporting, MAX(g.webappdeletereport) AS webappdeletereport, MAX(g.managedservices) AS managedservices,
		MAX(g.managedservicescomment) AS managedservicescomment, MAX(g.verifyscan) AS verifyscan, MAX(g.dashboard) AS dashboard,
		(SELECT CASE WHEN (SELECT forcegroupscheduling FROM tusers u WHERE u.xid = (SELECT xiparentid FROM tsubusers su WHERE su.xid=x.xsubxid)) = 1 THEN 1 ELSE MAX(g.forcegroupscheduling) END) AS forcegroupscheduling,
		MAX(g.stopscan) AS stopscan, MAX(compliance_enabled::int) AS compliance_enabled, MAX(g.markcomplianceexceptions) AS markcomplianceexceptions,
		MAX(g.answercompliancequestions) AS answercompliancequestions, MAX(g.approvecompliancequestions) AS approvecompliancequestions, MAX(g.editcompliancepolicies) AS editcompliancepolicies,
		MAX(web::int) AS web, MAX(g.swatcomment) AS swatcomment, MAX(g.swatverification) AS swatverification, MAX(g.swatdiscussion) AS swatdiscussion, MAX(g.swatrisks) AS swatrisks,
		MAX(g.bticketmanagement) AS bticketmanagement, MAX(g.grantalltickets::int) AS grantalltickets, MAX(readlicense::int) AS readlicense, MAX(read_auditlogs::int) AS read_auditlogs,
		MAX(g.rulemanagement::int) AS rulemanagement, MAX(g.ruleadmin::int) AS ruleadmin, MAX(g.autorules::int) AS autorules, MAX(g.editrules::int) AS editrules,
		MAX(g.wasx::int) AS wasx, MAX(g.submitscoping::int) AS submitscoping
		FROM tusergroups g, xlinksubusergroups x
		WHERE (g.xid = x.xgroupxid)
		GROUP BY x.xsubxid;

DROP VIEW IF EXISTS vsubroles_readonly CASCADE;
CREATE OR REPLACE VIEW vsubroles_readonly AS
		SELECT x.xsubxid as xsubxid_readonly,
		0::smallint AS boemail_readonly, MAX(g.bosettings) AS bosettings_readonly, MAX(g.boreports) AS boreports_readonly,
		MAX(g.boschedules) AS boschedules_readonly, MAX(g.bsubadmin) AS bsubadmin_readonly, MAX(g.boadmingroups) AS boadmingroups_readonly,
		MAX(g.bhadmin) AS bhadmin_readonly, 0::smallint AS bosms_readonly, MAX(g.bodisable) AS bodisable_readonly,
		MAX(g.bovultext) AS bovultext_readonly, MAX(g.bodeletereport) AS bodeletereport_readonly, MAX(g.badminusergroup) AS badminusergroup_readonly,
		MAX(g.bacceptrisk) AS bacceptrisk_readonly, MAX(g.pciscoping) AS pciscoping_readonly, MAX(g.pcischeduling) AS pcischeduling_readonly,
		MAX(g.pcireporting) AS pcireporting_readonly, MAX(g.pcidisputing) AS pcidisputing_readonly, MAX(g.webappadmin) AS webappadmin_readonly,
		MAX(g.webappreporting) AS webappreporting_readonly, MAX(g.webappdeletereport) AS webappdeletereport_readonly, MAX(g.managedservices) AS managedservices_readonly,
		MAX(g.managedservicescomment) AS managedservicescomment_readonly, MAX(g.verifyscan) AS verifyscan_readonly, 0::smallint AS dashboard_readonly,
		0::smallint AS forcegroupscheduling_readonly,
		MAX(g.stopscan) AS stopscan_readonly, MAX(compliance_enabled::int) AS compliance_enabled_readonly, MAX(g.markcomplianceexceptions) AS markcomplianceexceptions_readonly,
		MAX(g.answercompliancequestions) AS answercompliancequestions_readonly, MAX(g.approvecompliancequestions) AS approvecompliancequestions_readonly, MAX(g.editcompliancepolicies) AS editcompliancepolicies_readonly,
		MAX(web::int) AS web_readonly, MAX(g.swatcomment) AS swatcomment_readonly, MAX(g.swatverification) AS swatverification_readonly, MAX(g.swatdiscussion) AS swatdiscussion_readonly, MAX(g.swatrisks) AS swatrisks_readonly,
		MAX(g.bticketmanagement) AS bticketmanagement_readonly, MAX(g.grantalltickets::int) AS grantalltickets_readonly, MAX(readlicense::int) AS readlicense_readonly, MAX(read_auditlogs::int) AS read_auditlogs_readonly,
		MAX(g.rulemanagement::int) AS rulemanagement_readonly, MAX(g.ruleadmin::int) AS ruleadmin_readonly, MAX(g.autorules::int) AS autorules_readonly, MAX(g.editrules::int) AS editrules_readonly,
		MAX(g.wasx::int) AS wasx_readonly, MAX(g.submitscoping::int) AS submitscoping_readonly
		FROM tusergroups g, xlinksubusergroups x
		WHERE (g.xid = x.xgroupxid) AND g.readonly
		GROUP BY x.xsubxid;

DROP VIEW IF EXISTS vsubrolesldap CASCADE;
CREATE OR REPLACE VIEW vsubrolesldap AS
		SELECT su.xid as xsubxid,
		coalesce(max(g.boemail), 0) AS boemail, coalesce(max(g.bosettings), 0) AS bosettings, coalesce(max(g.boreports), 0) AS boreports,
		coalesce(max(g.boschedules), 0) AS boschedules, coalesce(max(g.bsubadmin), 0) AS bsubadmin, coalesce(max(g.boadmingroups), 0) AS boadmingroups,
		coalesce(max(g.bhadmin), 0) AS bhadmin, coalesce(max(g.bosms), 0) AS bosms, coalesce(max(g.bodisable), 0) AS bodisable,
		coalesce(max(g.bovultext), 0) AS bovultext, coalesce(max(g.bodeletereport), 0) AS bodeletereport, coalesce(max(g.badminusergroup), 0) AS badminusergroup,
		coalesce(max(g.bacceptrisk), 0) AS bacceptrisk, coalesce(max(g.pciscoping), 0) AS pciscoping, coalesce(max(g.pcischeduling), 0) AS pcischeduling,
		coalesce(max(g.pcireporting), 0) AS pcireporting, coalesce(max(g.pcidisputing), 0) AS pcidisputing, coalesce(max(g.webappadmin), 0) AS webappadmin,
		coalesce(max(g.webappreporting), 0) AS webappreporting, coalesce(max(g.webappdeletereport), 0) AS webappdeletereport, coalesce(max(g.managedservices), 0) AS managedservices,
		coalesce(max(g.managedservicescomment), 0) AS managedservicescomment, coalesce(max(g.verifyscan), 0) AS verifyscan, coalesce(max(g.dashboard), 0) AS dashboard,
		(SELECT CASE WHEN (SELECT forcegroupscheduling FROM tusers u WHERE u.xid = g.xuserxid) = 1 THEN 1 ELSE max(g.forcegroupscheduling) END) AS forcegroupscheduling,
		coalesce(max(g.stopscan), 0) AS stopscan, coalesce(max(g.compliance_enabled::int), 0) as compliance_enabled, coalesce(max(g.markcomplianceexceptions), 0) AS markcomplianceexceptions,
		coalesce(max(g.answercompliancequestions), 0) AS answercompliancequestions, coalesce(max(g.approvecompliancequestions), 0) AS approvecompliancequestions, coalesce(max(g.editcompliancepolicies), 0) AS editcompliancepolicies,
		coalesce(max(g.bticketmanagement), 0) AS bticketmanagement, coalesce(max(g.grantalltickets::int), 0) AS grantalltickets, coalesce(max(g.readlicense::int), 0) AS readlicense, coalesce(max(g.read_auditlogs::int), 0) AS read_auditlogs
		FROM tsubusers su
	LEFT JOIN tusergroups g ON (g.xuserxid = su.xiparentid AND string_to_array(regexp_replace(lower(trim(g.ldapgroup)), E',\\s*', ',', 'g'), ',') && string_to_array(regexp_replace(lower(trim(su.ldapgroups)), E',\\s*', ',', 'g'), ','))
	GROUP BY su.xid, g.xuserxid;

DROP VIEW IF EXISTS vsubrolesldap_readonly CASCADE;
CREATE OR REPLACE VIEW vsubrolesldap_readonly AS
		SELECT su.xid as xsubxid_readonly,
		0::smallint as boemail_readonly, coalesce(max(g.bosettings), 0) AS bosettings_readonly, coalesce(max(g.boreports), 0) AS boreports_readonly,
		coalesce(max(g.boschedules), 0) AS boschedules_readonly, coalesce(max(g.bsubadmin), 0) AS bsubadmin_readonly, coalesce(max(g.boadmingroups), 0) AS boadmingroups_readonly,
		coalesce(max(g.bhadmin), 0) AS bhadmin_readonly, 0::smallint AS bosms_readonly, coalesce(max(g.bodisable), 0) AS bodisable_readonly,
		coalesce(max(g.bovultext), 0) AS bovultext_readonly, coalesce(max(g.bodeletereport), 0) AS bodeletereport_readonly, coalesce(max(g.badminusergroup), 0) AS badminusergroup_readonly,
		coalesce(max(g.bacceptrisk), 0) AS bacceptrisk_readonly, coalesce(max(g.pciscoping), 0) AS pciscoping_readonly, coalesce(max(g.pcischeduling), 0) AS pcischeduling_readonly,
		coalesce(max(g.pcireporting), 0) AS pcireporting_readonly, coalesce(max(g.pcidisputing), 0) AS pcidisputing_readonly, coalesce(max(g.webappadmin), 0) AS webappadmin_readonly,
		coalesce(max(g.webappreporting), 0) AS webappreporting_readonly, coalesce(max(g.webappdeletereport), 0) AS webappdeletereport_readonly, coalesce(max(g.managedservices), 0) AS managedservices_readonly,
		coalesce(max(g.managedservicescomment), 0) AS managedservicescomment_readonly, coalesce(max(g.verifyscan), 0) AS verifyscan_readonly, 0::smallint AS dashboard_readonly,
		0::smallint AS forcegroupscheduling_readonly,
		coalesce(max(g.stopscan), 0) AS stopscan_readonly, coalesce(max(g.compliance_enabled::int), 0) AS compliance_enabled_readonly, coalesce(max(g.markcomplianceexceptions), 0) AS markcomplianceexceptions_readonly,
		coalesce(max(g.answercompliancequestions), 0) AS answercompliancequestions_readonly, coalesce(max(g.approvecompliancequestions), 0) AS approvecompliancequestions_readonly, coalesce(max(g.editcompliancepolicies), 0) AS editcompliancepolicies_readonly,
		coalesce(max(g.bticketmanagement), 0) AS bticketmanagement_readonly, coalesce(max(g.grantalltickets::int), 0) AS grantalltickets_readonly, coalesce(max(g.readlicense::int), 0) AS readlicense_readonly, coalesce(max(g.read_auditlogs::int), 0) AS read_auditlogs_readonly
		FROM tsubusers su
	LEFT JOIN tusergroups g ON (g.xuserxid = su.xiparentid AND g.readonly AND string_to_array(regexp_replace(lower(trim(g.ldapgroup)), E',\\s*', ',', 'g'), ',') && string_to_array(regexp_replace(lower(trim(su.ldapgroups)), E',\\s*', ',', 'g'), ','))
	GROUP BY su.xid, g.xuserxid;

--
-- Name: vuser; Type: VIEW; Schema: public; Owner: -
--
DROP VIEW IF EXISTS vuser CASCADE;
CREATE OR REPLACE VIEW vuser
AS
SELECT u.xid AS xid, vccompany, vcfirstname, vclastname, vcfullname, vcemail, bactive, vcusername, u.xupdator, vcaddress, vcaddress2,
vcpostal, vccity, vcphoneday, vcphonefax, vcphonemobile, vcvatnumber, vcorgnumber, mssp, passwordchanged, scannertimeout, emailencryptionkey, sshpublickey, dlastlogon, lastlogonversion, sslclientcafile,
dcreated, dupdated, ilogon, xiparentid, xiorg, breseller, vcpassword, ifailedlogon, lowrisklevel, mediumrisklevel, highrisklevel,
bhiablicense,boutscanlicense, booslicense, btaskalert, customcompanyname, customreportheader, customreportfooter, pciname, pciemail,
vctype, xvcip, vcacctid, demail, dstart, pcicertificate, reportcompany, language, iemailtype, xiaorg, bulimit,
activateadmin, showguide, startdayofweek, forcegroupscheduling, concurrentscans, bsecuritytrial, isecurityleft, ipciscansleft, iexternalscansleft, bremovereport,
gmtoffset, showpciinfo, dateformat, timeformat, translation, bpcitrial, sessiontimeout, audittargetmanagement, auditschedulemanagement, auditscanpolicymanagement,
auditriskacceptance, auditchangerisklevel, COALESCE(pp.csrfvalidation, 1) AS csrfvalidation, u.webappscansleft, webapptrial, externalwebappscansleft, salesperson,
NOW()::date - passwordchanged::date AS passwordage, vccountry, c.vcname AS country, vcstate, s.vcname AS state, removehiabscans, removehiabwebappscans,
'0'::smallint AS bdeleted, current_timestamp AS ddeleted, '0'::bigint AS xdeletor, xcreator, xroles, xosip, xosscan, xpciip, xpciscan, xhiabip, xhiabexternalip,
xhiabschedule, xhiabscheduleadd, xhiabclosed, xoosip, xoosschedule, xoosscheduleadd, xoosclosed, maxwebapps, webappscans, hiabwebappscansadd, hiabexternalwebapps,
hiabmaxwebapps, hiabwebappscans, maxvirtual, maxpentesthiabs, downloadvirtual, buildProductString(U.*) AS product,
COALESCE(h.iips, 0)::bigint AS shiabip, COALESCE(h.itest, 0)::bigint AS shiabtest, h.dupdate::timestamp AS shiabupdate, COALESCE(u.shiabs, 0) AS shiabs, wasmaximumlinks,
(SELECT COUNT(*) FROM tuserdatas tu LEFT JOIN targetscandata ts ON tu.xid=ts.targetid WHERE tu.xuserxid=u.xid AND pci=0 AND scannerid != 2 AND useslicense = 1) AS hiabtargetcount,
(SELECT COUNT(*) FROM tuserdatas tu LEFT JOIN targetscandata ts ON tu.xid=ts.targetid WHERE tu.xuserxid=u.xid AND pci=0 AND scannerid = 0 AND useslicense = 1) AS outscanTargetCount,
(SELECT COUNT(*) FROM tuserdatas tu LEFT JOIN targetscandata ts ON tu.xid=ts.targetid WHERE tu.xuserxid=u.xid AND pci=0 AND scannerid > 999 AND useslicense = 1) AS internaltargetcount,
(SELECT COUNT(*) FROM tuserdatas tu LEFT JOIN targetscandata ts ON tu.xid=ts.targetid WHERE tu.xuserxid=u.xid AND pci=1 AND useslicense = 1) AS pcitargetcount,
(SELECT COUNT(*) FROM tuserdatas tu LEFT JOIN targetscandata ts ON tu.xid=ts.targetid WHERE tu.xuserxid=u.xid AND pci=0 AND scannerid=2 AND useslicense = 1) AS externaltargetcount,
(SELECT SUM(webappcount) FROM schedules WHERE userid=u.xid AND scannerid != 2 AND NOT deleted AND template NOT IN (-10, -11))::bigint AS webappcount,
(SELECT count(distinct(lower(substring(url from '^https?://([^/]*?)(:[0-9]*)?/') || coalesce(substring(url from '^https?://.*?:([0-9]+)/')::integer, CASE WHEN url ILIKE 'https://%' THEN 443 ELSE 80 END)))) FROM (SELECT jsonb_array_elements_text(configuration::jsonb->'seeds') AS url
FROM scanconfigurations sc WHERE COALESCE(scannerid, 0) != 2 AND template='SCALE' AND sc.customerid=customers.id AND deleted IS NULL AND
EXISTS(SELECT ended FROM scanlogs l WHERE l.scanconfigurationid = sc.id AND l.ended IS NOT NULL AND l.deleted IS NULL AND (status = 'FINISHED' OR status = 'ISSUES'))) AS a) AS wasxappcount,
(SELECT count(distinct(lower(substring(url from '^https?://([^/]*?)(:[0-9]*)?/') || coalesce(substring(url from '^https?://.*?:([0-9]+)/')::integer, CASE WHEN url ILIKE 'https://%' THEN 443 ELSE 80 END)))) FROM (SELECT jsonb_array_elements_text(configuration::jsonb->'seeds') AS url
FROM scanconfigurations sc WHERE COALESCE(scannerid, 0) = 2 AND template='SCALE' AND sc.customerid=customers.id AND deleted IS NULL AND
EXISTS(SELECT ended FROM scanlogs l WHERE l.scanconfigurationid = sc.id AND l.ended IS NOT NULL AND l.deleted IS NULL AND (status = 'FINISHED' OR status = 'ISSUES'))) AS a)::bigint AS externalwebappcount,
latestscandate, discoverylimit, services, defaultreporttemplate, defaultreporttemplatepci, defaultreporttemplatewas, defaultreporttemplatecompliance, defaultreporttemplateswat, reportpassword,
managedserviceslimited, automaticgmt, nextservicereminder, servicereminderinterval, (SELECT signature FROM temailsignatures WHERE xuserxid = U.xid) AS signature,
u.vccurrency, u.scanpolicyownership, COALESCE(riskage, 90) AS riskage, u.portasvulnerability, credentialid, u.lastconsumedotptimeindex, u.twofactorauthentication,
COALESCE(acceptedlength, 30) AS acceptedlength, COALESCE(accepttargets, 0) AS accepttargets, acceptcomment, compliance, allowtargetmultiplegroups, pentestcompliance,
payperuse, web, web AS swat, agentsenabled, defaultagentcallhomefrequency, remotesupport, awsenabled, thycoticenabled, defaultscanner, defaulttemplateid, xsalesorganizations,
(SELECT STRING_AGG(vccompany, ',' ORDER BY vccompany) FROM tusers WHERE xid =ANY ((regexp_split_to_array(u.xsalesorganizations, ','))::bigint[])) AS allowedsalesorganizations,
u.disableunsafechecks, u.hiabs, u.hiabp, defaultmaxscantime, u.compliancepolicyownership, u.custominfotxt, u.vcemail AS pciemailaddress, pp.loginiprestriction,
COALESCE(pp.maximumage, 0) AS maximumage, COALESCE(pp.twofactorauthentication, 0) AS policytwofactorauthentication, COALESCE(pp.requireChange, 0) AS requirepasswordchange,
targetinactivediscoveries, groupsinscheduling, outscaninternalips, allowcomplianceonlyscanning, u.globalignoretargetlist, u.useglobalignoretargetlistbydefault,
dismisscompliancewarning, u.expiredate, u.jiraenabled, u.ssoenabled, u.snenabled, calculatenextscandate, outscanwasxapps, hiabappsecscaleapps, cookiesaccepted, snapshotsubscriptions, snapshotsubscriptionsremaining, swatsubscriptions, swatsubscriptionsremaining, portalpreferences,
customers.id AS customerid, customers.uuid::TEXT AS customeruuid, customers.assetmigration,
(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value))::jsonb FROM tags t WHERE t.deleted IS NULL AND t.customerid = customers.id AND t.id IN (SELECT tagid FROM tag_user WHERE userid = u.xid)) AS tags,
farsightproducts, customers.features, u.timezone, u.hasanalytics, u.salesaccountid, u.rbacsetupdate, u.uuid::TEXT, u.customersuccessmanageremail, u.accountmanageremail, u.pgppublickey,
(cm.customerid IS NOT NULL AND cm.failed IS DISTINCT FROM true) AS migration
FROM tusers u
LEFT JOIN tcountryl c ON c.xid = u.vccountry
LEFT JOIN tstatel s ON s.xid = u.vcstate
LEFT JOIN thiabstats h ON h.xuserxid = u.xid AND h.scheduler = 1 AND h.itype = '0' AND h.product != 'pen' AND NOT h.revoked
LEFT JOIN tpasswordpolicy pp ON pp.xuserxid = u.xid
LEFT JOIN customers ON customers.userid = u.xid
LEFT JOIN customer_migration cm ON cm.customerid = customers.id
LEFT JOIN tusersettings us ON us.xuserxid = u.xid;

--
-- Name: vsubuser; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vsubuser CASCADE;
CREATE OR REPLACE VIEW vsubuser AS
	SELECT u.xid AS xid, u.xiparentid, u.vcfirstname, u.vclastname, u.vcfullname, u.vcemail, u.bactive, u.language, u.xupdator, u.dupdated, u.passwordchanged,
	u.vcusername, u.dlastlogon, u.lastlogonversion, u.ilogon, xisubparentid, u.ifailedlogon, '1'::SMALLINT AS bsubuser, u.showpciinfo, u.iemailtype, u.dcreated, u.subusergroupid, u.emailencryptionkey,
	u.vcpassword, u.xvcip, u.demail, u.vcphonemobile, u.boallhosts, u.allscanners, u.showguide, u.startdayofweek, u.systemnotifications, m.bremovereport,
	u.vcstate, s.vcname as state, u.vccountry, c.vcname AS country, xpathup, xpathdown, u.gmtoffset, u.dateformat, u.timeformat, superuser, m.translation, u.authenticationmethod, u.ticketparent, now()::DATE - u.passwordchanged::DATE AS passwordage,
	u.custom0, u.custom1, u.custom2, u.custom3, u.custom4, u.custom5, u.custom6, u.custom7, u.custom8, u.custom9, u.grantedweb, u.allweb, u.allswat, u.allawsarn,
	(CASE WHEN u.xisubparentid = -1 THEN 'Top Level' ELSE (SELECT vcfullname FROM tsubusers WHERE xid = u.xisubparentid) END) AS parent, m.custominfotxt,
	m.vccompany, m.customcompanyname, m.customreportheader, m.customreportfooter, m.isecurityleft, m.ipciscansleft, m.sessiontimeout, m.iexternalscansleft, m.discoverylimit,
	audittargetmanagement, auditschedulemanagement, auditscanpolicymanagement, auditriskacceptance, auditchangerisklevel, m.webappscansleft, m.webapptrial,
	m.externalwebappscansleft, m.wasmaximumlinks,
	(SELECT string_agg(x.xgroupxid||',') FROM xlinksubusergroups x, tusergroups g WHERE x.xsubxid = u.xid AND g.xuserxid = u.xiparentid AND x.xgroupxid = g.xid) AS usergrouplist,
	(SELECT string_agg(x.xgroupxid||',') FROM xlinksubgroups x, tgenericgroups g WHERE x.xsubxid = u.xid AND g.xuserxid = u.xiparentid AND x.xgroupxid = g.xid) AS grouplist,
	(SELECT string_agg(g.name||',') FROM xlinksubgroups x, tgenericgroups g WHERE x.xsubxid = u.xid AND g.xuserxid = u.xiparentid AND x.xgroupxid = g.xid) AS targetgroups,
	(SELECT string_agg(x.scannerid||',') FROM xlinksubscanners x WHERE x.xsubxid = u.xid) AS scannerlist,
	(SELECT string_agg(x.swatid||',') FROM xlinksubswat x WHERE x.xsubxid = u.xid) AS swatlist,
	(SELECT string_agg(x.awsarnid||',') FROM xlinksubawsarn x WHERE x.xsubxid = u.xid) AS awsarnlist,
	(SELECT string_agg(w.name||E'\n') FROM xlinksubswat x, swatschedules w WHERE w.id = x.swatid AND x.xsubxid = u.xid AND NOT w.deleted) AS swatapplications,
	(SELECT string_agg(x.vctarget||E'\n') FROM xlinksubhosts x WHERE x.xsubxid = u.xid) AS targetlist,
	v.*,
	vro.*,
	(SELECT signature FROM temailsignatures WHERE xuserxid = m.xid) AS signature, m.bsecuritytrial, m.bpcitrial, m.salesperson, m.btaskalert, m.maxvirtual, m.xcreator, m.pentestcompliance, m.hiabs, m.hiabp, buildProductString(M.*) AS product, m.targetinactivediscoveries, m.groupsinscheduling,
	m.xosip, m.xosscan, m.xpciip, m.xpciscan, m.xhiabip, m.xhiabexternalip, m.xhiabschedule, m.xhiabscheduleadd, m.xhiabclosed, m.xiparentid AS salesorganizationxid, m.allowtargetmultiplegroups, m.concurrentscans, m.defaulttemplateid, m.defaultmaxscantime, m.managedserviceslimited, m.awsenabled, m.thycoticenabled, m.forcegroupscheduling AS forcetargetgroups,
	m.xoosip, m.xoosschedule, m.xoosscheduleadd, m.xoosclosed, m.maxwebapps, m.webappscans, m.hiabwebappscans, m.hiabmaxwebapps, m.hiabwebappscansadd, m.hiabexternalwebapps, u.defaultreporttemplate, u.defaultreporttemplatepci, u.defaultreporttemplatewas, u.defaultreporttemplatecompliance, u.defaultreporttemplateswat, u.reportpassword, u.automaticgmt, u.changepasswordonlogon,
	(CASE WHEN u.superuser = 0 THEN array_to_string(array(SELECT g.vcname FROM tusergroups g, xlinksubusergroups x WHERE g.xid = x.xgroupxid AND x.xsubxid = u.xid), ', ') END) AS usergroupnames, COALESCE(m.riskage, 90) AS riskage, m.portasvulnerability, u.credentialid, u.lastconsumedotptimeindex, u.twofactorauthentication, m.xroles,
	COALESCE(us.acceptedlength, 30) AS acceptedlength, COALESCE(us.accepttargets, 0) AS accepttargets, us.acceptcomment, m.compliance, m.web AS swat, m.agentsenabled, m.defaultagentcallhomefrequency, FALSE AS remotesupport, m.services, m.maxpentesthiabs, m.defaultscanner, u.hiabenroll, m.boutscanlicense, m.reportcompany, m.pcicertificate, m.compliancepolicyownership, m.disableunsafechecks, m.scanpolicyownership, m.vcemail AS pciemailaddress,
	pp.loginiprestriction, COALESCE(pp.csrfvalidation, 1) AS csrfvalidation, COALESCE(pp.maximumage, 0) AS maximumage, COALESCE(pp.twofactorauthentication, 0) AS policytwofactorauthentication, COALESCE(pp.ssoonly, 0) AS ssoonly, COALESCE(pp.requirechange, 0) AS requirepasswordchange, m.outscaninternalips, m.allowcomplianceonlyscanning, u.globalignoretargetlist, u.useglobalignoretargetlistbydefault, u.dismisscompliancewarning, m.expiredate,
	m.jiraenabled, m.ssoenabled, m.snenabled, u.calculatenextscandate, m.outscanwasxapps, m.hiabappsecscaleapps, m.sslclientcafile, u.cookiesaccepted, m.snapshotsubscriptions, m.snapshotsubscriptionsremaining, m.swatsubscriptions, m.swatsubscriptionsremaining, u.portalpreferences, u.userroleids, u.resourcegroupids,
	customers.id AS customerid, customers.uuid::TEXT AS customeruuid, customers.assetmigration,
	m.bactive AS parentactive, (SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value))::jsonb FROM tags t WHERE t.deleted IS NULL AND t.customerid = customers.id AND t.id IN (SELECT tagid FROM tag_user WHERE userid = -u.xid)) AS tags,
	m.farsightproducts, customers.features, m.xsalesorganizations, u.timezone, m.hasanalytics, m.rbacsetupdate, m.uuid::TEXT, u.allowedcustomertagids,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value))::jsonb FROM tags t WHERE t.deleted IS NULL AND t.customerid = customerid AND t.id = ANY(u.allowedcustomertagids)) AS allowedcustomertags,
	m.customersuccessmanageremail, m.accountmanageremail, u.pgppublickey, (cm.customerid IS NOT NULL AND cm.failed IS DISTINCT FROM true) AS migration
	FROM tsubusers u
	LEFT JOIN tcountryl c ON c.xid = u.vccountry
	LEFT JOIN tstatel s ON s.xid = u.vcstate
	LEFT JOIN vsubroles v ON v.xsubxid = u.xid
	LEFT JOIN vsubroles_readonly vro ON vro.xsubxid_readonly = u.xid
	LEFT JOIN tusers m ON m.xid = u.xiparentid
	LEFT JOIN tusersettings us ON us.xuserxid = u.xiparentid
	LEFT JOIN customers ON customers.userid = u.xiparentid
	LEFT JOIN customer_migration cm ON cm.customerid = customers.id
	LEFT JOIN tpasswordpolicy pp ON pp.xuserxid = u.xiparentid;

--
-- Name: vaorganization; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vaorganization CASCADE;
CREATE OR REPLACE view vaorganization
AS
SELECT o.xid, o.xiparentid, o.xuserxid, o.vcaddress, o.vcaddress2, o.vcpostcode, o.vccity,
(CASE WHEN o.xuserxid > 0 THEN u.vccompany ELSE o.vcname END) AS vcname,
(CASE WHEN o.xuserxid > 0 THEN u.vcfirstname ELSE o.vcfirstname END) AS vcfirstname,
(CASE WHEN o.xuserxid > 0 THEN u.vclastname ELSE o.vclastname END) AS vclastname,
(CASE WHEN o.xuserxid > 0 THEN u.vcemail ELSE o.vcemail END) AS vcemail,
(CASE WHEN o.xuserxid > 0 THEN u.vcphonemobile ELSE o.vcmobile END) AS vcmobile,
(CASE WHEN o.xuserxid > 0 THEN (u.vcfirstname || ' ' || u.vclastname) ELSE (o.vcfirstname || ' ' || o.vclastname) END) AS vcfullname,
(CASE WHEN o.xuserxid > 0 THEN u.vccountry ELSE o.vccountry END) AS vccountry,
(CASE WHEN o.xuserxid > 0 THEN u.vcstate ELSE o.vcstate END) AS vcstate,
(CASE WHEN o.xuserxid > 0 THEN c.vcname ELSE (SELECT vcname FROM tcountryl WHERE xid = o.vccountry) END) AS country,
(CASE WHEN o.xuserxid > 0 THEN s.vcname ELSE (SELECT vcname FROM tstatel WHERE xid = o.vcstate) END) AS state,
o.vcphone, o.vcfax, o.vcreg, o.vcvat, o.dcreated, o.dupdated, o.mssp, u.bactive,
u.vcusername, u.dlastlogon, u.ilogon, u.ifailedlogon, u.bhiablicense, u.boutscanlicense, u.booslicense, u.dstart,
u.bsecuritytrial, u.bpcitrial, u.isecurityleft, u.ipciscansleft, u.iexternalscansleft, (CASE WHEN u.xid IS NULL THEN 0 ELSE 1 END) AS bdeleted, u.outscaninternalips,
u.xosip, u.xosscan, u.xpciip, u.xpciscan, u.xhiabip, u.xhiabschedule, u.xhiabscheduleadd, u.xhiabclosed, u.xhiabexternalip, u.maxwebapps, u.webappscans, u.webappscansleft,
u.webapptrial, u.hiabexternalwebapps, u.hiabmaxwebapps, u.externalwebappscansleft, u.hiabwebappscans, u.hiabwebappscansadd, u.xoosip, u.xoosschedule, u.xoosscheduleadd,
u.outscanwasxapps, u.hiabappsecscaleapps, u.snapshotsubscriptions, u.snapshotsubscriptionsremaining, u.swatsubscriptions, u.swatsubscriptionsremaining,
u.xoosclosed, COALESCE(h.iips, 0) AS shiabip, COALESCE(h.itest, 0) AS shiabtest, h.dupdate::timestamp AS shiabupdate, u.shiabs, COALESCE(u.services, 0) AS services,
u.latestscandate, u.maxvirtual, COALESCE(u.web, false) AS swat, u.maxpentesthiabs AS maxpentesthiabs, COALESCE(u.hiabs, false) AS hiabs, COALESCE(u.hiabp, false) AS hiabp,
u.downloadvirtual AS downloadvirtual, u.activateadmin AS activateadmin, (CASE WHEN u.xid IS NOT NULL THEN buildProductString(u.*) ELSE '' END) AS product,
(SELECT MAX(dlastlogon) FROM tsubusers WHERE xiparentid = o.xuserxid) AS lastsubuserlogon,
(SELECT vcfullname FROM tusers WHERE xid = o.xcreator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * o.xcreator) AS creator,
(SELECT vcfullname FROM tusers WHERE xid = o.xupdator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * o.xupdator) AS updator, COALESCE(u.compliance, false) AS compliance,
(SELECT vccompany FROM tusers WHERE xid = o.xiparentid) AS salesorganization, u.nextservicereminder, u.servicereminderinterval,
(SELECT id FROM customers WHERE userid = o.xuserxid LIMIT 1) AS customerid,
u.farsightproducts, COALESCE(u.agentsenabled, false) AS agentsenabled, u.hasanalytics, o.salesaccountid
FROM taorganizations o
LEFT JOIN tusers u ON u.xid = o.xuserxid
LEFT JOIN tcountryl c ON c.xid = u.vccountry
LEFT JOIN tstatel s ON s.xid = u.vcstate
LEFT JOIN thiabstats h ON h.xuserxid = u.xid AND h.scheduler = 1 AND h.itype = '0' AND h.product != 'pen' AND NOT h.revoked;

--
-- Name: vasale; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vasale CASCADE;
CREATE OR REPLACE VIEW vasale
AS
	SELECT s.xid, s.xiparentid, s.xiaorg, o.xuserxid, o.vccity, s.iprice, s.vccurrency,
	(CASE WHEN o.xuserxid > 0 THEN u.vccompany ELSE o.vcname END) AS vcname,
	(CASE WHEN o.xuserxid > 0 THEN (u.vcfirstname || ' ' || u.vclastname) ELSE (o.vcfirstname || ' ' || o.vclastname) END) AS vcfullname,
	(CASE WHEN o.xuserxid > 0 THEN c.vcname ELSE (SELECT vcname FROM tcountryl WHERE xid = o.vccountry) END) AS country,
	s.bapproved, s.bcancel, s.vctype, s.brenew, s.vcorderid, s.dstart, s.itotalprice, s.ipartnerfee, s.txcomment, s.dcreated, s.dupdated, s.ilicensemodel,
	s.ivsms, s.ivemail, s.ivfax, s.ivsupport, s.iip, s.iscan, s.iappliance, s.iminipcappliance, s.dscandate, s.vctrialip, s.ivirtualappliance, s.enduserprice, s.calculatedprice, s.calculatedprice2,
	s.xreseller, s.vcsalesorder, s.vclicenseagreement, s.xcreator, s.xupdator, s.applianceaddress, s.expired,
	s.salestype, s.servicetype, s.stype, s.ssize, s.sfrequency, s.sproduct, s.pending, s.renewal, s.termination, s.farsightproduct,
	(SELECT vccompany FROM tusers WHERE xid = s.xreseller) AS reseller, u.latestscandate,
	(SELECT vcfullname FROM tusers WHERE xid = s.xcreator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = (-1 * s.xcreator)) AS creator,
	(SELECT vccompany FROM tusers where xid = s.xiparentid) AS salesorganization, (
		CASE
			WHEN s.salestype = 1 THEN CASE
				WHEN s.servicetype = 20 THEN (CASE WHEN s.stype = 1 THEN 'Plan only' WHEN s.stype = 2 THEN 'Plan and Implementation' END || ', ' || (CASE WHEN s.sproduct = 'OUTSCAN' THEN 'Outscan' WHEN s.sproduct = 'HIAB' THEN 'HIAB' END))
				WHEN s.servicetype = 21 THEN ((CASE WHEN s.stype IS NULL THEN '' WHEN s.stype = 9 THEN 'Basic, ' WHEN s.stype = 10 THEN 'Advanced, ' WHEN s.stype = 11 THEN 'Custom, ' WHEN s.stype = 12 THEN 'PCI 11.2, ' END) || (CASE WHEN s.sfrequency = 1 THEN 'Quarterly' WHEN s.sfrequency = 2 THEN 'Monthly' WHEN s.sfrequency = 3 THEN 'Bimonthly' WHEN s.sfrequency = 4 THEN 'Weekly' END) || ', ' || (CASE WHEN s.sproduct = 'OUTSCAN' THEN 'Outscan' WHEN s.sproduct = 'HIAB' THEN 'HIAB' END))
				WHEN s.servicetype = 22 THEN (CASE WHEN s.stype	= 3 THEN 'External' WHEN s.stype = 4 THEN 'Internal' WHEN s.stype = 5 THEN 'Web Application' END)
				WHEN s.servicetype = 23 THEN (CASE WHEN s.ssize = 1 THEN 'Small' WHEN s.ssize = 2 THEN 'Medium' WHEN s.ssize = 3 THEN 'Large' WHEN s.ssize = 4 THEN 'X-Large' END || ', ' || (CASE WHEN s.sproduct = 'OUTSCAN' THEN 'Outscan' WHEN s.sproduct = 'WAS' THEN 'Outscan WAS' WHEN s.sproduct = 'HIAB' THEN 'HIAB' WHEN s.sproduct = 'HWAS' THEN 'HIAB WAS' END))
				WHEN s.servicetype = 24 THEN (CASE WHEN s.stype	= 6 THEN 'Awareness' WHEN s.stype = 7 THEN 'Certification' WHEN s.stype = 8 THEN 'Customized' END)
			END ELSE CASE
				WHEN s.vctype = 'OS' THEN CASE WHEN ilicensemodel = '6' OR ilicensemodel = '7' OR ilicensemodel = '8' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' ELSE '' END || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP'
				WHEN s.vctype = 'OSI' THEN 'Unlimited Scans, ' || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP'
				WHEN s.vctype = 'HIAB' OR s.vctype='HIABE' THEN CASE WHEN ilicensemodel = '6' OR ilicensemodel = '7' OR ilicensemodel = '8' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' ELSE '' END || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP, ' || COALESCE(iappliance, 0)::varchar || ' Server, ' || COALESCE(ivirtualappliance, 0)::varchar || ' Virtual, ' || COALESCE(iminipcappliance, 0)::varchar || ' Mini PC'
				WHEN s.vctype = 'VIRUS' THEN ivemail::varchar || ' Email, ' || ivsms::varchar || ' Sms, ' || ivfax::varchar || ' Fax, ' || ivsupport || ' Support'
				WHEN s.vctype = 'OOS' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP, ' || iappliance::varchar || ' Server'
				WHEN s.vctype = 'PCI' THEN CASE WHEN ilicensemodel = '6' OR ilicensemodel = '7' OR ilicensemodel = '8' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' ELSE '' END || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP '
				WHEN s.vctype = 'OSWAS' THEN CASE WHEN ilicensemodel = '6' OR ilicensemodel = '7' OR ilicensemodel = '8' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' ELSE ''END || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' Webapps'
				WHEN s.vctype = 'HWAS' THEN CASE WHEN ilicensemodel = '6' OR ilicensemodel = '7' OR ilicensemodel = '8' THEN CASE WHEN iscan = -1 THEN 'Unlimited' ELSE iscan::varchar END || ' Scans, ' ELSE '' END || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' Webapps, ' || COALESCE(iappliance, 0)::varchar || ' Server, ' || COALESCE(ivirtualappliance, 0)::varchar || ' Virtual, ' || COALESCE(iminipcappliance, 0)::varchar || ' Mini PC'
				WHEN s.vctype = 'COMPL' THEN 'Compliance'
				WHEN s.vctype = 'WEB' THEN 'SWAT'
				WHEN s.vctype = 'HIABS' OR s.vctype = 'HIABP' THEN COALESCE(ivirtualappliance, 0)::varchar || ' Virtual'
				WHEN s.vctype = 'WASX' THEN 'Unlimited Scans, ' || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP'
				WHEN s.vctype = 'HASS' OR s.vctype='HWASE' THEN 'Unlimited Scans, ' || CASE WHEN iip = -1 THEN 'Unlimited' ELSE iip::varchar END || ' IP'
				WHEN s.vctype = 'CYR3' THEN 'Farsight ' || s.farsightproduct
				WHEN s.vctype = 'ANALYTICS' THEN 'Analytics'
			END
		END
	) AS productdesc,
	CASE WHEN s.salestype = 1 THEN s.poc ELSE CASE WHEN ilicensemodel = 6 THEN 1 ELSE 0 END END AS poc, COALESCE(u.bactive, 0) AS bactive,
	s.notifiedsubscriptionexpiration, s.approveddate, s.sekprice, s.upsaleof, CASE WHEN s.upsaleof > 0 THEN 1 ELSE 0 END AS upsale,
	s.enddate, s.sfopportunityid, s.sfopportunitylineitemid, s.sfsubscriptionid,
	s.bapproved = 1 AND NOT EXISTS (SELECT xid FROM tasales s2 WHERE s.vctype = s2.vctype AND s.salestype = s2.salestype AND s.xiaorg = s2.xiaorg AND s2.bapproved = 1 AND s2.bcancel = 0 AND s2.xid > s.xid) AS lastapprovedsale, complianceenabled
FROM tasales s, taorganizations o
LEFT JOIN tusers u ON o.xuserxid = u.xid
LEFT JOIN tcountryl c ON c.xid = u.vccountry
WHERE s.xiaorg = o.xid;

--
-- Name: vaudits; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vaudits CASCADE;
CREATE OR REPLACE VIEW vaudits
AS
SELECT t.xid, t.xxid, t.xuserxid, t.xsubuserxid, t.xvcapp, t.imode, t.xtime, t.txcustom, t.xconsultancyxid, t.pci,
COALESCE(
	CASE
		WHEN t.xvcapp = 'tReport_DisputeS' THEN (SELECT name FROM treport_disputes WHERE xid=xxid LIMIT 1)
	WHEN t.xvcapp = 'tUserdataS' THEN (SELECT CASE WHEN ipaddress IS NULL THEN CASE WHEN aws_instance_id IS NULL THEN CASE WHEN agentid IS NULL THEN CASE WHEN snsysid IS NULL THEN hostname ELSE (SELECT snname FROM snassets a WHERE a.snsysid=u.snsysid AND a.xuserxid=u.xuserxid LIMIT 1) END ELSE agentid END ELSE aws_instance_id END ELSE HOST(ipaddress) END FROM tuserdatas u WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tReportTextS' THEN (SELECT vcheadline FROM treporttexts WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tGenericGroupS' THEN (SELECT name FROM tgenericgroups WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tSavedscanprefS' THEN (SELECT name FROM tsavedscanprefs WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tSubUserS' THEN (SELECT vcfullname FROM tsubusers where xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tUserS' THEN (SELECT vcfullname FROM tusers where xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tReportS' OR t.xvcapp = 'tSwat' THEN (SELECT vctarget FROM treportentrys WHERE xid = (SELECT fk_treportentrys_xid FROM treport_vulns where xid=t.xxid) LIMIT 1)
		WHEN t.xvcapp = 'tUserGroupS' THEN (SELECT vcname FROM tusergroups WHERE xid = t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tVultextS' THEN (SELECT name FROM trules WHERE ruleid = t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tOutscanFileS' THEN (SELECT comment FROM toutscanfiles WHERE xid = t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tWorkflowS' THEN (SELECT 'Taskid: ' || taskid || ' (' || name || ')' FROM tworkflows WHERE xid = t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tScannerS' THEN (SELECT name FROM tscanners WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tAgentS' THEN CASE WHEN t.xxid < 0 THEN (SELECT vcname FROM taorganizations WHERE xid = (t.xxid * -1) LIMIT 1) ELSE (SELECT vcname FROM taorganizations WHERE xid = (SELECT xiaorg from tasales where xid = t.xxid) LIMIT 1) END
		WHEN t.xvcapp = 'tReportTemplateObjectS' THEN (SELECT name FROM treporttemplates WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tScheduleObjectS' THEN (SELECT name FROM schedules WHERE id=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tWasScheduleObjectS' THEN (SELECT name FROM schedules WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tReportScheduleObjectS' THEN (SELECT name FROM treportschedules WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tCompliance' THEN (SELECT name FROM tcompliancepolicies WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tLoggingS' THEN (SELECT eventname FROM tloggings WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tAppAccess' THEN (SELECT name FROM tappaccess WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'WebSchedule' THEN (SELECT name FROM swatschedules WHERE id=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tReport_ManagedS' THEN (SELECT title FROM tmanagedreports WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tManaged_Report_GroupS' THEN (SELECT name FROM tmanagedreportgroups WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'tSubUserGroupS' THEN (SELECT name FROM tsubusergroups WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'WebTicket' THEN deleteName
		WHEN t.xvcapp = 'WasX' THEN (SELECT name FROM schedules WHERE xid=t.xxid LIMIT 1)
		WHEN t.xvcapp = 'Credentials' THEN (SELECT name FROM credentialclasses WHERE id = (SELECT classid FROM credentials WHERE id = t.xxid LIMIT 1))
	END,
	(SELECT deleteName::VARCHAR FROM taudits del WHERE t.xxid != 1 AND t.xvcapp != 'tReportS' AND del.xid >= t.xid AND del.imode=2 AND del.xvcapp=t.xvcapp AND del.xxid=t.xxid LIMIT 1) ) AS name,
CASE WHEN t.xconsultancyxid > 0 then (SELECT vcfullname from tusers where xid = t.xconsultancyxid LIMIT 1) ELSE null END AS VCCONSULTANCY, addedTargets, removedTargets, discoveryupdate, t.comment, t.vcfirstname, t.vclastname, t.deletename, t.serviceid
FROM taudits t;

--
-- Name: vgenericgroup; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vgenericgroup CASCADE;
CREATE OR REPLACE view vgenericgroup
AS
SELECT XID, XIPARENTID, XUSERXID, NAME, ICOUNT, RISKCOUNT, PCI, DESCRIPTION, XUPDATOR, DUPDATED,
EXISTS (SELECT L.XID FROM XLINKRULES L WHERE L.XID = G.XID) AS RULEBASED, REPORTBASED,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = G.XCREATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * G.XCREATOR
) AS CREATOR,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = G.XUPDATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * G.XUPDATOR
) AS UPDATOR,
(SELECT string_agg(X.field || E'\n' || X.operator || E'\n' || X.value || E'\n') FROM XLINKRULES X WHERE X.xid = G.xid) AS RULE
FROM TGENERICGROUPS G;

--
-- Name: voutscanfile; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS voutscanfile CASCADE;
CREATE OR REPLACE view voutscanfile
AS
SELECT XID, XUSERXID, NAME, SIZE, CONTENT, COMMENT, HIDDEN, XSUBUSERXID, PRIVATE, XCREATOR, XUPDATOR,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = R.XCREATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * R.XCREATOR
) AS CREATOR,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = R.XUPDATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * R.XUPDATOR
) AS UPDATOR, MODE
FROM TOUTSCANFILES R;


--
-- Name: vqueue; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vqueue CASCADE;
CREATE OR REPLACE view vqueue AS
		SELECT q.xid, q.xuserxid, CASE WHEN (q.xthreadid IS NULL) THEN 'None'::character varying ELSE q.xthreadid END AS xthreadid, q.itype, q.binvalid, q.dcreated AS dqueue, q.dlastcheck, q.iretries, q.vctype, length(q.cdata) AS isize, q.cdata, CASE WHEN (u.vcfullname IS NULL) THEN 'System'::character varying ELSE u.vcfullname END AS "owner", u.vccompany AS COMPANY, q.dcreated, q.subuserid, COALESCE(su.vcfullname, '') AS subusername, parentid, event FROM tqueues q LEFT JOIN tusers u ON u.xid = q.xuserxid LEFT JOIN tsubusers su ON su.xid=q.subuserid;


--
-- Name: vqueuelog; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vqueuelog CASCADE;
CREATE OR REPLACE view vqueuelog AS
		SELECT q.xid, q.xuserxid, CASE WHEN (q.xthreadid IS NULL) THEN 'None'::character varying ELSE q.xthreadid END AS xthreadid, q.itype, q.binvalid, q.dsent, q.dsent AS dqueue, q.dlastcheck, q.iretries, q.vctype, length(q.cdata) AS isize, q.cdata, CASE WHEN (u.vcfullname IS NULL) THEN 'System'::character varying ELSE u.vcfullname END AS "owner", q.status, u.vccompany AS COMPANY, q.dcreated, q.subuserid, COALESCE(su.vcfullname, '') AS subusername, parentid, event, trackingid FROM tqueuelogs q LEFT JOIN tusers u ON u.xid = q.xuserxid LEFT JOIN tsubusers su ON su.xid=q.subuserid;


--
-- Name: vreportentry; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vreportentry CASCADE;
CREATE OR REPLACE view vreportentry
AS
SELECT R.XID, R.XUSERXID, R.XSOXID, R.XTEMPLATE, R.VCTARGET, R.IID,
R.BSUPPORT, R.SUPPORTEXPIRATION, R.BCONSULT, R.DREPORTDATE, R.DREPORTENDDATE, R.BPCI, R.XSCANLOGXID, R.XSCANJOBXID,
(
	SELECT NAME FROM TSAVEDSCANPREFS WHERE XID = R.XTEMPLATE
) AS TEMPLATE,
(
	SELECT NAME FROM schedules WHERE ID = R.XSOXID
) AS SCHEDULEJOB,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = R.XCREATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * R.XCREATOR
) AS CREATOR,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = R.XUPDATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * R.XUPDATOR
) AS UPDATOR,
R.high_count AS HIGH, R.medium_count AS MEDIUM, R.low_count AS LOW, R.BPCICOMPLIANT, R.CVSS_SCORE, R.SCANNERID, R.XIPXID,
COALESCE( ( SELECT NAME FROM TSCANNERS WHERE XID = R.SCANNERID AND XUSERXID = R.XUSERXID ), 'Local' ) AS SCANNERNAME, L.COMPLIANCESCAN
FROM TREPORTENTRYS R
LEFT JOIN tscanlogs L ON R.xscanlogxid = L.xid;


--
-- Name: vvultext; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vvultext CASCADE;
CREATE OR REPLACE VIEW vvultext
AS
SELECT r.ruleid AS xid, 0::BIGINT AS xuserxid, (CASE WHEN pi.name IS NOT NULL AND pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END)::VARCHAR(256) AS vcname,
family::TEXT AS vcfam, (CASE WHEN r.informational OR was_informational THEN 0 WHEN cvssscore >= 7 THEN 4 WHEN cvssscore >= 4 THEN 2 ELSE 1 END) AS irisk,
CASE WHEN r.cve IS NULL OR r.cve = '' THEN 'No CVE' ELSE r.cve END AS vccve, CASE WHEN r.osvdb IS NULL OR r.osvdb = '' THEN 'No OSVDB' ELSE r.osvdb END AS osvdb,
CASE WHEN bugtraq IS NULL OR bugtraq = '' THEN 'No bugtraq' ELSE bugtraq END AS vcbug, description AS cdesc, pi.producturl,
CASE WHEN solutiontype=6 THEN COALESCE(pi.updatesolutiontext, solution) WHEN solutiontype=7 THEN 'Apply the latest patches for ' || pi.name ELSE solution END AS csol,
''::VARCHAR(40) AS icrc, (cvssscore * 10)::BIGINT AS icvss, null::SMALLINT AS ipcicvss, cvssvector::VARCHAR(64) AS vccvssvector, r.nvdcvssvector, r.created AS dcreated, r.updated AS dupdated,
deleted AS external, r.created AS scriptcreated, cvssscore cvss_score, solutiontype, pi.name::VARCHAR(255) AS solutionproduct, cvssv3score, cvssv3vector,
(CASE WHEN cvssv3score IS NULL THEN NULL WHEN cvssv3score >= 9.0 THEN 'CRITICAL' WHEN cvssv3score >= 7.0 THEN 'HIGH' WHEN cvssv3score >= 4.0 THEN 'MEDIUM' WHEN cvssv3score >= 0.1 THEN 'LOW' ELSE 'RECOMMENDATION' END)::severity AS cvssv3severity,
(CASE WHEN solutionType = 6 THEN COALESCE(pi.updateSolutionTitle, SOLUTIONTITLE)
WHEN solutiontype=7 THEN 'Apply the latest patches for ' || pi.name ELSE solutiontitle END)::VARCHAR(255) AS solutiontitle, was_informational, was_falsepos,
COALESCE(hasexploits, false) AS hasexploits, specialnotes, pcifail, r.cyrating, r.previouscyrating, r.cyrating-r.previouscyrating AS cyratingdelta, (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE AS cyratingupdated,
(r.farsight->'risk'->>'score')::NUMERIC AS exploitprobability, (r.farsight->'risk'->>'score')::NUMERIC - (r.farsight->'risk'->>'delta')::NUMERIC AS previousexploitprobability, (r.farsight->'risk'->>'delta')::NUMERIC AS exploitprobabilitydelta, r.cwe AS cweid,
(r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen, r.farsight
FROM trules r
LEFT JOIN tproductinformation pi ON pi.xid = r.productid;

--
-- Name: vreportfinding; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vreportfinding CASCADE;
CREATE OR REPLACE VIEW vreportfinding
AS
SELECT re.xid AS reportxid, re.xuserxid, re.xsoxid, re.xtemplate, re.vctarget, re.iid, re.bpci, COALESCE(re.platform, 'NOTDETECTED') AS platform, urisscanned,
re.bsupport, re.supportexpiration, re.bconsult, re.dreportdate AS date, re.dreportenddate AS enddate, COALESCE(rv.dadded, re.dreportdate) AS findingdate, re.xscanlogxid, re.xscanjobxid,
re.benabled, re.xprevxid, re.scannerid, re.xipxid, re.targettype, re.ipaddress, re.reachable, re.authenticationresult,
re.inetaddress, rv.vcscvssvector AS cvssvector, COALESCE(rv.cvssv3vector, r.cvssv3vector) AS cvssv3vector,
CASE
	WHEN rv.itype IN (3) AND rv.iscvss >= 0 THEN 'Vulnerability'
	WHEN rv.itype = 2 AND rv.irisk > 0 THEN 'Vulnerability'
	WHEN rv.itype = 1 THEN 'Port'
	ELSE 'Information'
END AS type,
rv.iport, rv.iprotocol, rv.xid, r.family AS vcfamily, (CAST(COALESCE(CASE WHEN rv.bpcifailed=1 THEN r.cvssscore * 10 WHEN rv.iscvss > 0 THEN rv.iscvss ELSE 0 END, 0) AS FLOAT) / 10)::DECIMAL(10, 1) AS cvssscore, COALESCE(rv.cvssv3score, r.cvssv3score) AS cvssv3score,
(CASE WHEN COALESCE(rv.cvssv3score, r.cvssv3score) IS NULL THEN '' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) = 0.0 THEN 'None' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) < 4.0 THEN 'Low' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) < 7.0 THEN 'Medium' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) < 9.0 THEN 'High' ELSE 'Critical' END) AS cvssv3severity,
CAST(COALESCE(rv.ipcicvss, 0) AS FLOAT) / 10 AS pcicvssscore, (CASE WHEN rv.bpcifailed=1 THEN r.cvssscore * 10 ELSE rv.iscvss END)::SMALLINT AS iscvss, rv.vcscvssvector, rv.dlastseen, rv.dfirstseen, rv.acceptdate, acceptexpires, rv.acceptedlength, rv.acceptcomment, COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) AS accepted,
CASE WHEN COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) THEN CASE WHEN acceptedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -acceptedby) WHEN acceptedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = acceptedby) END ELSE '' END AS acceptedby,
rv.bnew, (CASE WHEN rv.bfalsepos > 0 THEN 1 ELSE 0 END) AS bfalsepos, rv.bfalsepos AS bfalseposvalue, rv.disputestatus, rv.disputedate, rv.vcvulnid, rv.bpcifailed, rv.ipcicvss, rv.irisk, rv.originalrisklevel, rv.itype, rv.vcvhost, rv.servicename, rv.potentialfalse, rv.falsepositivecomment, rv.clarificationrequestcomment, rv.fixed, pi.name AS product, rv.disputecomment, rv.cdata, COALESCE(r.was_falsepos, FALSE) AS was_falsepos,
(CASE WHEN rv.vcvulnid = '101010' THEN 'Port scanner' WHEN customname IS NOT NULL THEN customname ELSE CASE WHEN pi.name IS NOT NULL AND pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END END)::VARCHAR AS vcname,
CASE WHEN rv.itype IN (2,3) AND rv.irisk > 0 THEN COALESCE(rv.customvulnerabilitytype, r.vulnerabilitytype, 'Unknown') ELSE 'Information'::vulnerabilitytype END AS vulnerabilitytype, r.cve AS vccve, r.bugtraq AS vcbug, r.osvdb, COALESCE(rv.customdescription, r.description) AS cdesc,
CASE WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=6 THEN COALESCE(rv.customsolution, pi.updatesolutiontext, r.solution) WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=7 THEN 'Apply the latest patches for ' || pi.name ELSE COALESCE(rv.customsolution, r.solution) END AS csol,
COALESCE(rv.customsolutiontype, r.solutiontype) AS solutiontype, pi.name::VARCHAR(255) AS solutionproduct, (CASE WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=6 THEN COALESCE(rv.customsolutiontitle, pi.updatesolutiontitle) WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=7 THEN 'Apply latest patches for ' || pi.name ELSE COALESCE(rv.customsolutiontitle, r.solutiontitle) END)::VARCHAR(255) AS solutiontitle,
r.created AS scriptcreated,CASE WHEN verifyDate IS NOT NULL THEN 1 ELSE 0 END AS verified, COALESCE(stillpresent, -1) AS stillpresent, verifydate, verifydata,
(SELECT nextscandate FROM tverifyscans vs WHERE vs.treport_vulns_xid = rv.xid AND vs.xuserxid = re.xuserxid) AS nextverifyscan,
u.hostname, u.netbios, u.virtualhosts, u.aws_instance_id, u.awsarn, u.agentid, CASE WHEN u.xid IS NULL THEN true ELSE false END AS targetremoved, COALESCE(u.businesscriticality, 'MEDIUM') AS businesscriticality, COALESCE(u.businesscriticality, 'MEDIUM') AS assetbusinesscriticality,
CASE WHEN re.bpci = 1 AND rv.vcvulnid = '101010' AND rv.specialnote THEN 1 WHEN rv.specialnote THEN 1 WHEN r.ruleid IS NULL OR r.specialnotes IS NULL THEN 0 ELSE 1 END AS specialnote, disputeaccepted,
CASE WHEN re.bpci = 1 AND rv.disputeaccepted = 0 AND rv.itype IN (2,3) AND (rv.bpcifailed = 1 OR rv.IPCICVSS >= 40) THEN 0 ELSE 1 END AS pcicompliance,
u.custom0 AS targetcustom0, u.custom1 AS targetcustom1, u.custom2 AS targetcustom2, u.custom3 AS targetcustom3, u.custom4 AS targetcustom4, u.custom5 AS targetcustom5, u.custom6 AS targetcustom6, u.custom7 AS targetcustom7, u.custom8 AS targetcustom8, u.custom9 AS targetcustom9,
CASE WHEN rv.dadded IS NOT NULL THEN 1 ELSE 0 END AS isadded, NULL AS assignee, NULL::BIGINT AS taskid,
case WHEN falsepositivecomment IS NOT NULL THEN 1 ELSE 0 END AS hasfpcomment, rv.falsepositiveby AS falseposby,
CASE WHEN falsepositiveby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -falsepositiveby) WHEN falsepositiveby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = falsepositiveby) END AS falsepositiveby,
CASE WHEN clarificationrequestby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -clarificationrequestby) WHEN clarificationrequestby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = clarificationrequestby) END AS clarificationrequestbyname,
CASE WHEN reviewedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -reviewedby) WHEN reviewedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = reviewedby) END AS reviewedbyname,
CASE WHEN addedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -addedby) WHEN addedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = addedby) END AS addedbyname,
CASE WHEN confirmedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -confirmedby) WHEN confirmedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = confirmedby) END AS confirmedbyname,
CASE WHEN fixedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -fixedby) WHEN fixedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = fixedby) END AS fixedbyname,
date_part('day', (now() - dfirstseen)) AS age, COALESCE(r.hasexploits, false) AS hasexploits, r.created AS checkcreated, addedby, reviewedby, confirmedby, fixedby,
re.high_count, re.medium_count, re.low_count, re.fixedhighvulns, re.fixedmediumvulns, re.fixedlowvulns, re.daysfixedhighvulns, re.daysfixedmediumvulns, re.daysfixedlowvulns, re.daysnonfixedhighvulns, re.daysnonfixedmediumvulns, re.daysnonfixedlowvulns,
r.cvssscore AS nvdcvssscore, r.overridecomment, r.cvssvector AS nvdcvssvector, pi.producturl,
c.cwe AS cweid, (CASE WHEN c.cwe IS NOT NULL THEN 'CWE-' || c.cwe ELSE NULL END) AS cwe,
(c.classifications->'sans25')::TEXT AS sanstop25,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2004')) ELSE NULL END) AS owasp2004,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2007')) ELSE NULL END) AS owasp2007,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2010')) ELSE NULL END) AS owasp2010,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2013')) ELSE NULL END) AS owasp2013,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2017')) ELSE NULL END) AS owasp2017,
(CASE WHEN xtemplate = -2 THEN prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2021')) ELSE NULL END) AS owasp2021,
c.classifications,
rv.recreationflow, rv.explanation, COALESCE(rv.swatstatus, 0) AS swatstatus, rv.olddisputeacceptedid, rv.externalticket, ARRAY['NETSEC']::source[] AS source,
rv.custom0, rv.custom1, rv.custom2, rv.custom3, rv.custom4, rv.custom5, rv.custom6, rv.custom7, rv.custom8, rv.custom9, rv.acceptedby AS accepteduserxid, r.solutionproduct AS ruleproduct, rv.previouslydetected, COALESCE(rv.wasfinding, false) AS wasfinding, CASE WHEN xtemplate = -2 THEN (SELECT COUNT(id) FROM treportfiles rf WHERE rf.findingid = rv.xid) ELSE 0 END AS attachments, patchinformation, COALESCE(exposed, FALSE) AS exposed,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN cyrating ELSE NULL END) AS cyrating,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN cyrating-previouscyrating ELSE NULL END) AS cyratingdelta,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE ELSE NULL END) AS cyratingupdated, (r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen,
(CASE WHEN COALESCE(rv.cvssv3score, r.cvssv3score) IS NULL THEN NULL WHEN COALESCE(rv.cvssv3score, r.cvssv3score) >= 9.0 THEN 'CRITICAL' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) >= 7.0 THEN 'HIGH' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) >= 4.0 THEN 'MEDIUM' WHEN COALESCE(rv.cvssv3score, r.cvssv3score) >= 0.1 THEN 'LOW' ELSE 'RECOMMENDATION' END)::severity AS newcvssv3severity,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN (r.farsight->'risk'->>'score')::NUMERIC ELSE NULL END) AS exploitprobability,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN (r.farsight->'risk'->>'delta')::NUMERIC ELSE NULL END) AS exploitprobabilitydelta,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN bluelivmentions ELSE NULL END) AS bluelivmentions,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN bluelivthreatactors ELSE NULL END) AS bluelivthreatactors,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN bluelivexploits ELSE NULL END) AS bluelivexploits,
(CASE WHEN (rv.itype = 3 AND rv.iscvss >= 0) OR (rv.itype = 2 AND rv.irisk > 0) THEN r.farsight ELSE NULL END) AS farsight
FROM treportentrys re
LEFT JOIN treport_vulns rv ON rv.fk_treportentrys_xid = re.xid
LEFT JOIN tuserdatas u ON u.xid = re.xipxid AND u.xuserxid = re.xuserxid
LEFT JOIN trules r ON r.ruleid = rv.vcvulnid
LEFT JOIN tproductinformation pi ON pi.xid = r.productid
LEFT JOIN classifications c ON c.cwe = (CASE WHEN xtemplate = -2 THEN COALESCE(rv.customCwe, r.cwe) ELSE NULL END);

DROP VIEW IF EXISTS vwebfinding CASCADE;
CREATE OR REPLACE VIEW vwebfinding AS
SELECT rv.xid, re.xid AS reportxid, re.xtemplate, re.xuserxid, rv.vcvulnid, re.xsoxid,
(CASE WHEN xtemplate = -2 THEN (SELECT name FROM swatschedules WHERE id = re.xsoxid) WHEN xtemplate = -11 THEN (SELECT name FROM schedules WHERE id = re.xsoxid) ELSE '' END) AS schedulename,
(CASE WHEN xtemplate = -2 THEN (SELECT parent FROM swatschedules WHERE id = re.xsoxid) ELSE 0 END) AS swatappid, (CASE WHEN xtemplate = -2 THEN (SELECT name FROM swatschedules WHERE id = (SELECT parent FROM swatschedules WHERE id = re.xsoxid)) ELSE '' END) AS swatappname,
COALESCE(rv.dadded, re.dreportdate) AS findingdate, r.created AS scriptcreated, rv.dlastseen, rv.dfirstseen, rv.bnew, rv.previouslydetected,
(CASE WHEN (rv.vcscvssvector = '') IS NOT FALSE THEN NULL ELSE (CAST(COALESCE(CASE WHEN rv.iscvss > 0 THEN rv.iscvss ELSE 0 END, 0) AS FLOAT) / 10)::DECIMAL(10, 1) END) AS cvssscore,
(CASE WHEN COALESCE(rv.cvssv3vector, NULLIF(r.cvssv3vector, '')) IS NULL THEN NULL ELSE COALESCE(rv.cvssv3score, r.cvssv3score) END) AS cvssv3score,
(CASE WHEN (r.cvssvector = '') IS NOT FALSE THEN NULL ELSE CAST(r.cvssscore AS NUMERIC) END) AS nvdcvssscore,
NULLIF(rv.vcscvssvector, '') AS cvssvector,
COALESCE(rv.cvssv3vector, NULLIF(r.cvssv3vector, '')) AS cvssv3vector,
NULLIF(r.cvssvector, '') AS nvdcvssvector,
COALESCE(rv.customcve, r.cve) AS cve, COALESCE(rv.custombugtraq, r.bugtraq) AS bugtraq, r.osvdb,
(CASE WHEN c.cwe IS NOT NULL THEN 'CWE-' || c.cwe ELSE NULL END) AS cwe,
(c.classifications->'sans25')::TEXT AS sanstop25,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2004')) AS owasp2004,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2007')) AS owasp2007,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2010')) AS owasp2010,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2013')) AS owasp2013,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2017')) AS owasp2017,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2021')) AS owasp2021,
COALESCE(r.hasexploits, false) AS hasexploits, rv.recreationflow, rv.explanation, rv.irisk, rv.originalrisklevel::bigint,
COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) AS accepted, rv.acceptedlength, rv.acceptedby,
CASE WHEN COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) THEN rv.acceptdate ELSE NULL END AS acceptdate,
CASE WHEN COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) THEN rv.acceptexpires ELSE NULL END AS acceptexpires,
CASE WHEN COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) THEN rv.acceptcomment ELSE NULL END AS acceptcomment,
CASE WHEN COALESCE(rv.acceptedlength > 0 AND rv.acceptexpires > NOW(), false) THEN (CASE WHEN rv.acceptedby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -rv.acceptedby) WHEN rv.acceptedby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = rv.acceptedby) END) ELSE NULL END AS acceptedbyname,
CASE WHEN rv.bfalsepos > 0 THEN TRUE ELSE FALSE END AS falsepositive,
CASE WHEN rv.bfalsepos > 0 THEN (CASE WHEN rv.bfalsepos = 2 THEN TRUE ELSE FALSE END) ELSE NULL END AS persistentfalsepositive,
CASE WHEN rv.bfalsepos > 0 THEN rv.falsepositivecomment ELSE NULL END AS falsepositivecomment,
CASE WHEN rv.bfalsepos > 0 THEN rv.disputedate ELSE NULL END AS disputedate,
CASE WHEN rv.bfalsepos > 0 THEN (CASE WHEN rv.falsepositiveby < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -rv.falsepositiveby) WHEN rv.falsepositiveby > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = rv.falsepositiveby) END) ELSE NULL END AS falsepositivebyname,
rv.falsepositiveby, rv.disputestatus, rv.clarificationrequestcomment, rv.disputecomment, COALESCE(r.was_falsepos, FALSE) AS was_falsepos,
(CASE WHEN rv.vcvulnid = '101010' THEN 'Port scanner' WHEN customname IS NOT NULL THEN customname ELSE CASE WHEN pi.name IS NOT NULL AND pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END END)::VARCHAR AS vcname,
CASE
	WHEN rv.itype IN (3) AND rv.iscvss >= 0 THEN 'Vulnerability'
	WHEN rv.itype = 2 AND rv.irisk > 0 THEN 'Vulnerability'
	WHEN rv.itype = 1 THEN 'Port'
	ELSE 'Information'
END AS typestring,
rv.itype, (CASE WHEN xtemplate = -2 THEN NULL ELSE rv.vcvhost END) AS vcvhost, rv.iport, rv.iprotocol, rv.servicename, pi.name AS productname, pi.producturl, COALESCE(rv.customdescription, r.description) AS description, rv.cdata,
CASE WHEN rv.itype IN (2,3) AND rv.irisk > 0 THEN COALESCE(rv.customvulnerabilitytype, r.vulnerabilitytype, 'Unknown') ELSE 'Information'::vulnerabilitytype END AS vulnerabilitytype,
COALESCE(rv.customsolutiontype, r.solutiontype) AS solutiontype,
(CASE WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=6 THEN COALESCE(rv.customsolutiontitle, pi.updatesolutiontitle) WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=7 THEN 'Apply latest patches for ' || pi.name ELSE COALESCE(rv.customsolutiontitle, r.solutiontitle) END)::VARCHAR(255) AS solutiontitle,
CASE WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=6 THEN COALESCE(rv.customsolution, pi.updatesolutiontext, r.solution) WHEN COALESCE(rv.customsolutiontype, r.solutiontype)=7 THEN 'Apply the latest patches for ' || pi.name ELSE COALESCE(rv.customsolution, r.solution) END AS solution,
COALESCE(rv.wasfinding, false) AS wasfinding, (CASE WHEN rv.fixed = 1 THEN true ELSE false END) AS fixed
FROM treportentrys re
LEFT JOIN treport_vulns rv ON rv.fk_treportentrys_xid = re.xid
LEFT JOIN trules r ON r.ruleid = rv.vcvulnid
LEFT JOIN tproductinformation pi ON pi.xid = r.productid
LEFT JOIN classifications c ON c.cwe = COALESCE(rv.customCwe, r.cwe)
WHERE xtemplate IN (-11, -10, -2);

DROP VIEW IF EXISTS vwasreportfinding CASCADE;
CREATE OR REPLACE VIEW vwasreportfinding
AS
SELECT W.TREPORTENTRY, R.REPORTXID, R.XUSERXID, R.XSOXID, R.XTEMPLATE, R.VCTARGET, R.IID, R.BPCI, R.PLATFORM, R.URISSCANNED, R.BSUPPORT, R.SUPPORTEXPIRATION, R.BCONSULT, R.DATE, R.ENDDATE, R.FINDINGDATE, R.XSCANLOGXID, R.XSCANJOBXID, R.BENABLED, R.XPREVXID, R.SCANNERID, R.XIPXID, R.TARGETTYPE, R.IPADDRESS, R.CVSSVECTOR,
R.TYPE, R.IPORT, R.IPROTOCOL, R.XID, R.VCFAMILY, R.CVSSSCORE, R.PCICVSSSCORE, R.ISCVSS, R.VCSCVSSVECTOR, R.DLASTSEEN, R.DFIRSTSEEN, R.BNEW, R.BFALSEPOS, R.DISPUTESTATUS, R.DISPUTEDATE, R.VCVULNID, R.BPCIFAILED, R.IPCICVSS, R.IRISK, R.ORIGINALRISKLEVEL, R.ITYPE, R.VCVHOST,
R.SERVICENAME, R.POTENTIALFALSE, R.FALSEPOSITIVECOMMENT, R.FIXED, R.PRODUCT, R.DISPUTECOMMENT, R.VCNAME, R.VCCVE, R.VCBUG, R.CDESC, R.CSOL, R.SOLUTIONTYPE, R.SOLUTIONPRODUCT, R.SOLUTIONTITLE, R.SCRIPTCREATED,
R.VERIFIED, R.STILLPRESENT, R.VERIFYDATE, R.VERIFYDATA, R.NEXTVERIFYSCAN, R.HOSTNAME, R.NETBIOS, R.VIRTUALHOSTS, R.DISPUTEACCEPTED, R.PCICOMPLIANCE, R.TARGETCUSTOM0, R.TARGETCUSTOM1, R.TARGETCUSTOM2, R.TARGETCUSTOM3, R.TARGETCUSTOM4, R.TARGETCUSTOM5, R.TARGETCUSTOM6, R.TARGETCUSTOM7, R.TARGETCUSTOM8, R.TARGETCUSTOM9, R.ISADDED, R.HASFPCOMMENT, R.FALSEPOSITIVEBY, R.AGE, R.HASEXPLOITS, W.HASCONTENT = 1 AS HASDETAILS, WAS_FALSEPOS,
W.XID AS FINDINGXID, W.PATH, W.ACCEPTDATE, W.ACCEPTEXPIRES, W.ACCEPTEDLENGTH, W.ACCEPTCOMMENT, COALESCE(W.ACCEPTEDLENGTH > 0 AND W.ACCEPTEXPIRES > NOW(), false) AS ACCEPTED, CASE WHEN W.ACCEPTEDBY < 0 THEN (SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -W.ACCEPTEDBY) WHEN W.ACCEPTEDBY > 0 THEN (SELECT VCFULLNAME FROM TUSERS WHERE XID = W.ACCEPTEDBY) END AS ACCEPTEDBY, NULL AS RECREATIONFLOW, NULL AS EXPLANATION
FROM TWASFINDINGS W
LEFT JOIN VREPORTFINDING R ON W.TREPORTENTRY = R.REPORTXID AND W.VCVULNID = R.VCVULNID
WHERE W.FINDINGXID IS NULL
UNION ALL
SELECT U.TREPORTENTRY, R.REPORTXID, R.XUSERXID, R.XSOXID, R.XTEMPLATE, R.VCTARGET, R.IID, R.BPCI, R.PLATFORM, R.URISSCANNED, R.BSUPPORT, R.SUPPORTEXPIRATION, R.BCONSULT, R.DATE, R.ENDDATE, R.FINDINGDATE, R.XSCANLOGXID, R.XSCANJOBXID, R.BENABLED, R.XPREVXID, R.SCANNERID, R.XIPXID, R.TARGETTYPE, R.IPADDRESS, R.CVSSVECTOR,
R.TYPE, R.IPORT, R.IPROTOCOL, R.XID, R.VCFAMILY, R.CVSSSCORE, R.PCICVSSSCORE, R.ISCVSS, R.VCSCVSSVECTOR, R.DLASTSEEN, R.DFIRSTSEEN, R.BNEW, R.BFALSEPOS, R.DISPUTESTATUS, R.DISPUTEDATE, R.VCVULNID, R.BPCIFAILED, R.IPCICVSS, R.IRISK, R.ORIGINALRISKLEVEL, R.ITYPE, R.VCVHOST,
R.SERVICENAME, R.POTENTIALFALSE, R.FALSEPOSITIVECOMMENT, R.FIXED, R.PRODUCT, R.DISPUTECOMMENT, R.VCNAME, R.VCCVE, R.VCBUG, R.CDESC, R.CSOL, R.SOLUTIONTYPE, R.SOLUTIONPRODUCT, R.SOLUTIONTITLE, R.SCRIPTCREATED,
R.VERIFIED, R.STILLPRESENT, R.VERIFYDATE, R.VERIFYDATA, R.NEXTVERIFYSCAN, R.HOSTNAME, R.NETBIOS, R.VIRTUALHOSTS, R.DISPUTEACCEPTED, R.PCICOMPLIANCE, R.TARGETCUSTOM0, R.TARGETCUSTOM1, R.TARGETCUSTOM2, R.TARGETCUSTOM3, R.TARGETCUSTOM4, R.TARGETCUSTOM5, R.TARGETCUSTOM6, R.TARGETCUSTOM7, R.TARGETCUSTOM8, R.TARGETCUSTOM9, R.ISADDED, R.HASFPCOMMENT, R.FALSEPOSITIVEBY, R.AGE, R.HASEXPLOITS, false AS HASDETAILS, WAS_FALSEPOS,
-U.XID AS FINDINGXID, U.PATH, R.ACCEPTDATE, R.ACCEPTEXPIRES, R.ACCEPTEDLENGTH, R.ACCEPTCOMMENT, R.ACCEPTED, R.ACCEPTEDBY, NULL AS RECREATIONFLOW, NULL AS EXPLANATION
FROM TWASURLS U
LEFT JOIN VREPORTFINDING R ON U.TREPORTENTRY = R.REPORTXID AND R.VCVULNID = 295843
WHERE U.FINDINGXID IS NULL;

DROP VIEW IF EXISTS vreporthistory CASCADE;
CREATE OR REPLACE VIEW vreporthistory
AS
SELECT h.id, h.dcreated, h.xcreator, h.findingid, h.vcvulnid, h.iport, h.vcscvssvector AS cvssvector, h.iscvss, h.irisk, h.dfirstseen, h.dlastseen, h.cdata, h.recreationflow, h.explanation, h.externalticket,
(CASE WHEN h.xcreator < 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = -h.xcreator) WHEN h.xcreator > 0 THEN (SELECT vcfullname FROM tusers WHERE xid = h.xcreator) END) AS creator,
(CASE WHEN h.vcvulnid = '101010' then 'Port scanner' WHEN h.customname IS NOT NULL THEN h.customname ELSE CASE WHEN pi.name IS NOT NULL AND pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END END)::VARCHAR AS vcname,
(CASE WHEN h.irisk > 0 THEN COALESCE(h.customvulnerabilitytype, r.vulnerabilitytype, 'Unknown') ELSE 'Information'::vulnerabilitytype END) AS vulnerabilitytype,
COALESCE(h.customcve, r.cve) AS vccve, COALESCE(h.custombugtraq, r.bugtraq) AS vcbug, COALESCE(h.customdescription, r.description) AS cdesc,
CASE WHEN COALESCE(h.customsolutiontype, r.solutiontype)=6 THEN COALESCE(h.customsolution, pi.updatesolutiontext, r.solution) WHEN COALESCE(h.customsolutiontype, r.solutiontype)=7 THEN 'Apply the latest patches for ' || pi.name ELSE COALESCE(h.customsolution, r.solution) END AS csol,
COALESCE(h.customsolutiontype, r.solutiontype) AS solutiontype, (CASE WHEN COALESCE(h.customsolutiontype, r.solutiontype)=6 THEN COALESCE(h.customsolutiontitle, pi.updatesolutiontitle) WHEN COALESCE(h.customsolutiontype, r.solutiontype)=7 THEN 'Apply latest patches for ' || pi.name ELSE COALESCE(h.customsolutiontitle, r.solutiontitle) END)::VARCHAR(255) AS solutiontitle,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2004')) AS owasp2004,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2007')) AS owasp2007,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2010')) AS owasp2010,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2013')) AS owasp2013,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2017')) AS owasp2017,
prefixedOwaspList(jsonb_array_castint(c.classifications->'owasp2021')) AS owasp2021,
h.cvssv3vector, h.cvssv3score
FROM treporthistory h
LEFT JOIN trules r ON r.ruleid = h.vcvulnid
LEFT JOIN tproductinformation pi ON pi.xid = r.productid
LEFT JOIN classifications c ON c.cwe = COALESCE(h.customCwe, r.cwe);

--
-- Name: vworkflow; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vworkflow CASCADE;
CREATE OR REPLACE view vworkflow
AS
SELECT w.xid, w.xuserxid, xsubuserxid, w.xcreator, w.xupdator, w.type, duedate, priority, w.status, name, taskid, w.iport, w.iprotocol, w.vcvulnid, w.xipxid,
(SELECT vcfullname FROM tusers WHERE xid = w.xuserxid AND w.xsubuserxid IS NULL UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = w.xsubuserxid) AS assignee, u.hostname,
COALESCE(re.vctarget, w.ipaddress) AS ipaddress, re.targettype, CASE WHEN scanjob IS NULL THEN re.dreportdate ELSE (SELECT dscanstartdate FROM tscanlogs sl WHERE sl.xid=scanjob) END AS dreportdate,
CASE WHEN w.type = 1 THEN w.vcvulnid ELSE -1 END AS scriptid, v.xid AS reportxid, re.xsoxid AS scheduleobjectxid,
CASE WHEN re.xtemplate = 10 THEN (SELECT name FROM schedules WHERE id = re.xsoxid) ELSE '' END AS scheduleobjectname,
CASE WHEN re.xtemplate = 10 THEN 1 WHEN re.xtemplate = -10 THEN 2 WHEN re.xtemplate = -2 THEN 3 ELSE 0 END AS ticketorigin,
CASE WHEN w.type = 1 THEN (CASE WHEN w.vcvulnid=101010 THEN 'Port scanner' ELSE (SELECT name FROM trules WHERE ruleid = w.vcvulnid) END) ELSE '' END AS scriptname,
w.iport AS port, w.iprotocol AS protocol, v.vcvhost AS virtualhost, COALESCE(scanjob, re.xscanjobxid) AS scanjob, isescalated, findingid,
CASE WHEN verifydate IS NOT NULL THEN 1 ELSE 0 END AS verified, COALESCE(stillpresent, -1) AS stillpresent, CASE WHEN verifydate IS NOT NULL THEN stillpresent ELSE -1 END AS verifiedstatus, w.includevulinfo
FROM tworkflows w
LEFT JOIN treport_vulns v ON v.xid = (CASE WHEN COALESCE(w.findingid, 0) > 0 THEN w.findingid ELSE (SELECT MAX(rv2.xid) FROM treport_vulns rv2 LEFT JOIN treportentrys re2 ON rv2.fk_treportentrys_xid = re2.xid WHERE re2.xuserxid = w.xuserxid AND rv2.xipxid = w.xipxid AND rv2.iport=w.iport AND rv2.iprotocol=w.iprotocol AND rv2.vcvulnid=w.vcvulnid) END)
LEFT JOIN tuserdatas u ON u.xid = w.xipxid
LEFT JOIN treportentrys re ON v.fk_treportentrys_xid = re.xid;

--
-- Name: vjiratickets; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vjiratickets CASCADE;
CREATE OR REPLACE view vjiratickets
AS
SELECT w.xid, w.xuserxid, xsubuserxid, w.xcreator, w.xupdator, w.type, duedate, priority, w.status, name, taskid, w.iport, w.iprotocol, w.vcvulnid, w.xipxid, w.jirakey, w.createticket,
(SELECT vcfullname FROM tusers WHERE xid = w.xuserxid AND w.xsubuserxid IS NULL UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = w.xsubuserxid) AS assignee, u.hostname,
COALESCE(re.vctarget, w.ipaddress) AS ipaddress, re.targettype, CASE WHEN scanjob IS NULL THEN re.dreportdate ELSE (SELECT dscanstartdate FROM tscanlogs sl WHERE sl.xid=scanjob) END AS dreportdate,
CASE WHEN w.type = 1 THEN w.vcvulnid ELSE -1 END AS scriptid, v.xid AS reportxid, re.xsoxid AS scheduleobjectxid,
CASE WHEN re.xtemplate = 10 THEN (SELECT name FROM schedules WHERE id = re.xsoxid) ELSE '' END AS scheduleobjectname,
CASE WHEN re.xtemplate = 10 THEN 1 WHEN re.xtemplate = -10 THEN 2 WHEN re.xtemplate = -2 THEN 3 ELSE 0 END AS ticketorigin,
CASE WHEN w.type = 1 THEN (CASE WHEN w.vcvulnid=101010 THEN 'Port scanner' ELSE (SELECT name FROM trules WHERE ruleid = w.vcvulnid) END) ELSE '' END AS scriptname,
w.iport AS port, w.iprotocol AS protocol, v.vcvhost AS virtualhost, COALESCE(scanjob, re.xscanjobxid) AS scanjob, isescalated, findingid,
CASE WHEN verifydate IS NOT NULL THEN 1 ELSE 0 END AS verified, COALESCE(stillpresent, -1) AS stillpresent, CASE WHEN verifydate IS NOT NULL THEN stillpresent ELSE -1 END AS verifiedstatus, w.includevulinfo
FROM jiratickets w
LEFT JOIN treport_vulns v ON v.xid = (CASE WHEN COALESCE(w.findingid, 0) > 0 THEN w.findingid ELSE (SELECT MAX(rv2.xid) FROM treport_vulns rv2 LEFT JOIN treportentrys re2 ON rv2.fk_treportentrys_xid = re2.xid WHERE re2.xuserxid = w.xuserxid AND rv2.xipxid = w.xipxid AND rv2.iport=w.iport AND rv2.iprotocol=w.iprotocol AND rv2.vcvulnid=w.vcvulnid) END)
LEFT JOIN tuserdatas u ON u.xid = w.xipxid
LEFT JOIN treportentrys re ON v.fk_treportentrys_xid = re.xid;

--
-- Name: vreportmodification; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vreportmodification CASCADE;
CREATE OR REPLACE view vreportmodification
AS
SELECT RE.XID AS REPORTXID, RE.XUSERXID, RE.XSOXID, RE.XTEMPLATE, RE.VCTARGET, RE.IID,
RE.BSUPPORT, RE.BCONSULT, RE.DREPORTDATE, RE.DREPORTENDDATE AS ENDDATE, RE.XSCANLOGXID, RE.XSCANJOBXID,
RE.SCANNERID, RE.XIPXID, RE.TARGETTYPE,
COALESCE( ( SELECT NAME FROM TSCANNERS WHERE XID = RE.SCANNERID AND XUSERXID = RE.XUSERXID ), 'Local' ) AS SCANNERNAME,
(
				SELECT NAME FROM TSAVEDSCANPREFS WHERE XID = RE.XTEMPLATE
) AS TEMPLATE,
(
				SELECT NAME FROM schedules WHERE ID = RE.XSOXID
) AS SCHEDULEJOB,
RS.XID, RS.VCTYPE, RS.CDATA AS MODIFICATIONS
FROM TREPORTENTRYS RE
LEFT JOIN TREPORT_SETUPS RS ON RS.FK_TREPORTENTRYS_XID = RE.XID;


--
-- Name: vreporttext; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vreporttext CASCADE;
CREATE OR REPLACE VIEW vreporttext AS
		SELECT s.xid, s.xuserxid, s.xsubuserxid, s.itype, s.ilocation, s.isort, s.vcheadline, s.txtext, s.reportlevel, s.xcreator, s.xupdator,
		(SELECT tusers.vcfullname FROM tusers WHERE (tusers.xid = s.xcreator) UNION ALL SELECT tsubusers.vcfullname FROM tsubusers WHERE (tsubusers.xid = (-1 * s.xcreator))) AS creator,
		(SELECT tusers.vcfullname FROM tusers WHERE (tusers.xid = s.xupdator) UNION ALL SELECT tsubusers.vcfullname FROM tsubusers WHERE (tsubusers.xid = (-1 * s.xupdator))) AS updator,
		reporttemplate, (SELECT name FROM treporttemplates WHERE xid = s.reporttemplate) AS reporttemplatename, ispublic
		FROM treporttexts s;


--
-- Name: vsavedscanpref; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vsavedscanpref CASCADE;
CREATE OR REPLACE view vsavedscanpref
AS
SELECT XID, XUSERXID, NAME, DESCRIPTION, GLOBAL, CPLUGINPROP, TARGETOVERRIDE, TARGETOVERRIDEEDIT, DELETED, XSUBUSERXID, AUTHENTICATIONTYPESSH, AUTHENTICATIONTYPESMB,
( select string_agg( x.vcid||',')from xlinksavedscanpref x where x.xid = s.xid and x.itype = '2' ) AS DISABLEDFAMILYLIST,
( case when (select count(xid) from xlinksavedscanpref x where x.xid = s.xid and x.itype='4') > 0 then
	(select string_agg(distinct vcfam, ',') from vvultext t where xid IN ((select vcid::bigint from xlinksavedscanpref x where x.xid=s.xid and x.itype='4'))) else '' end) AS PARTIALLYDISABLEDFAMILYLIST,
( select string_agg( x.vcid||',')from xlinksavedscanpref x where x.xid = s.xid and x.itype = '3' ) AS ENABLEDFAMILYLIST,
( select string_agg( x.vcid||',')from xlinksavedscanpref x where x.xid = s.xid and x.itype = '4' ) AS DISABLEDSCRIPTLIST,
( select string_agg( x.vcid||',')from xlinksavedscanpref x where x.xid = s.xid and x.itype = '5' ) AS ENABLEDSCRIPTLIST,
( case when (select count(xid) from xlinksavedscanpref x where x.xid=s.xid and x.itype='4') > 0 then
	(select string_agg( t.xid::text, ',') from vvultext t where vcfam =any ((select array_agg(distinct vcfam) from vvultext where xid IN ((select vcid::bigint from xlinksavedscanpref x where x.xid=s.xid and x.itype='4')))::varchar[]) and xid not in (select x.vcid::bigint from xlinksavedscanpref x where x.xid = s.xid and x.itype = '4' )) else '' end) AS UIHINTENABLEDSCRIPTLIST,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = S.XCREATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * S.XCREATOR
) AS CREATOR,
(
	SELECT VCFULLNAME FROM TUSERS WHERE XID = S.XUPDATOR
	UNION ALL SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = -1 * S.XUPDATOR
) AS UPDATOR, XUPDATOR,
( CASE WHEN S.XSUBUSERXID IS NOT NULL THEN (SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = S.XSUBUSERXID) ELSE (SELECT VCFULLNAME FROM TUSERS WHERE XID = S.XUSERXID) END) AS OWNER
FROM TSAVEDSCANPREFS S;


--
-- Name: vscanlog; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vscanlog CASCADE;
CREATE OR REPLACE VIEW vscanlog AS
	SELECT u.xid, u.xuserxid, u.scannerid, u.vchost, u.dscanstartdate, u.dscanenddate, u.itype,
	u.xtemplate, u.xsoxid, u.iid, u.bdeleted, u.xscanjobxid, u.xipxid, u.reason, u.confirmed, u.fromhiab, u.xschedulexid,
	greatest(date_trunc('minute', u.dscanenddate - u.dscanstartdate), '0'::interval) AS scantime, u.targetgroupxid, u.targetgroupname,
	CASE WHEN u.scannerid = 0 THEN 'Local' ELSE COALESCE(s.name, 'Local') END AS scannername,
	CASE WHEN (u.itype <= 10 OR u.itype > 15) AND u.fromhiab != 1 THEN (
		SELECT schedules.name FROM schedules WHERE (schedules.id = u.xsoxid AND schedules.userid = u.xuserxid)
	) ELSE NULL END AS schedulejob,
	CASE WHEN u.itype > 10 AND u.itype <= 15 AND u.xsoxid > 0 AND u.fromhiab != 1 THEN (SELECT schedules.name FROM schedules WHERE schedules.id = u.xsoxid AND schedules.userid=u.xuserxid) ELSE null END AS discoverytemplate,
	CASE WHEN u.discovery AND itype IN (19,20) THEN 'Discovery' WHEN u.itype <= 10 OR u.itype > 16 THEN (SELECT tsavedscanprefs.name FROM tsavedscanprefs WHERE (tsavedscanprefs.xid = u.xtemplate)) ELSE 'Discovery' END AS "template",
	(SELECT tuserdatas.hostname FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS targetname,
	(SELECT tuserdatas.netbios FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS netbios,
	(SELECT tuserdatas.aws_instance_id FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS aws_instance_id,
	(SELECT tuserdatas.agentid FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS agentid,
	COALESCE((SELECT targetscandata.platform FROM targetscandata WHERE targetscandata.targetid = u.xipxid), 'NOTDETECTED') AS platform,
	CASE WHEN u.itype = 20 AND u.xtemplate != -10 THEN NOT EXISTS(SELECT xid FROM tscanlogs WHERE xuserxid = u.xuserxid AND xsoxid = u.xsoxid AND xtemplate != -10 AND itype = 20 AND bdeleted = 0 AND xid > u.xid) ELSE false END AS last,
	CASE WHEN scanlessreportxid > 0 THEN true ELSE false END AS scanless,
	CASE WHEN u.itype = 20 AND u.xtemplate != -10 AND u.xtemplate != 10 AND DSCANENDDATE > (NOW() - '35 days'::INTERVAL) THEN COALESCE((SELECT xid FROM tscanlogs a WHERE a.xscanjobxid = u.xid AND a.scanschema IS NOT NULL AND a.xuserxid=u.xuserxid LIMIT 1) > 0, false) ELSE false END AS canupdate,
	COALESCE(u.latestscanupdate, u.dscanstartdate) AS latestscanupdate,
	CASE WHEN (u.xtemplate = 10) THEN 1::smallint ELSE 0::smallint END AS pci,
	CASE WHEN (u.itype <= 10 OR u.itype IN (50, 51, 52, 53)) THEN u.vchost WHEN (u.itype > 9 AND fromhiab != 1) THEN '' END AS target,
	CASE WHEN u.xtemplate = -10 THEN EXISTS(SELECT scanlogxid FROM twasstatistics WHERE scanlogxid = u.xid AND xuserxid = u.xuserxid) ELSE false END AS haswasstats,
	CASE
		WHEN u.xtemplate != 10 THEN 0
		WHEN u.itype < 2 THEN COALESCE((SELECT bpcicompliant from treportentrys where xscanlogxid = u.xid ), 0)
		WHEN u.itype = 20 THEN COALESCE((CASE WHEN EXISTS(SELECT bpcicompliant FROM treportentrys re WHERE re.xscanjobxid = u.xid AND bpcicompliant = 0) OR EXISTS(SELECT itype FROM tscanlogs ts WHERE ts.xscanjobxid=u.xid AND ts.xuserxid=u.xuserxid AND itype>1 AND itype < 8) THEN 0 ELSE 1 END), 0)
		ELSE 0
	END AS compliant,
	submitted, scanschema, u.xsubuserxid, compliancescan, compliancepolicies, blueprint, u.scanlessreportxid, u.vcgname, discovery, latestruledate, dupdated, dcreated, vccountry
	FROM tscanlogs u
	LEFT JOIN tscanners s ON s.xid = u.scannerid AND s.xuserxid = u.xuserxid;


--
-- Name: vscanlogstat; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vscanlogstat CASCADE;
CREATE OR REPLACE VIEW vscanlogstat AS
		SELECT u.xid, u.xuserxid, u.scannerid, u.vchost, u.dscanstartdate, u.dscanenddate, u.itype, u.xtemplate, u.xsoxid, u.iid,
	u.bdeleted, u.xscanjobxid, u.xipxid, e.high_count AS high, e.medium_count AS medium, e.low_count AS low,
	p.high_count AS prevhigh, p.medium_count AS prevmedium, p.low_count AS prevlow, p.xid as lastreport,
	COALESCE( ( SELECT NAME FROM TSCANNERS WHERE XID = U.SCANNERID AND XUSERXID = U.XUSERXID ), 'Local' ) AS SCANNERNAME,
	(SELECT schedules.name FROM schedules WHERE (schedules.id = u.xsoxid)) AS schedulejob,
	(SELECT tsavedscanprefs.name FROM tsavedscanprefs WHERE (tsavedscanprefs.xid = u.xtemplate)) AS "template",
	(SELECT tuserdatas.hostname FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS targetname,
	(SELECT tuserdatas.ipaddress FROM tuserdatas WHERE tuserdatas.xid = u.xipxid AND tuserdatas.xuserxid = u.xuserxid) AS ipaddress,
	(SELECT string_agg((((g.name)::text || ','::text))::character varying) AS string_agg FROM tgenericgroups g, xlinkgeneric x
	WHERE (((g.xid = x.xid) AND (g.xuserxid = u.xuserxid)) AND (x.xipxid = u.xipxid))) AS grouplist,
	CASE WHEN (u.itype < 8) THEN u.vchost WHEN (u.itype > 9) THEN
	(SELECT schedules.name FROM schedules WHERE (schedules.id = u.xsoxid)) ELSE '' END AS target, u.xsubuserxid
	FROM ((tscanlogs u LEFT JOIN treportentrys e ON ((e.xscanlogxid = u.xid)))
	LEFT JOIN treportentrys p ON ((p.xid = e.xprevxid)));


--
-- Name: vscanstatus; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vscanstatus CASCADE;
CREATE OR REPLACE VIEW vscanstatus AS
		SELECT s.xid, s.xuserxid, s.xsubuserxid, s.xsoxid, s.vcservice, s.vcpercent, s.ipercentv, s.ithreadid,
		s.vcstatus, s.vcstate, s.bpause, s.vcjobname, s.vcgname, s.iattackerid, s.vctarget, s.icount,
		s.iverify, s.dscanstart, s.dscanstarted, s.dscanend, s.xtemplate, s.xscanjobxid, s.txsettings, s.xipxid,
		s.scannerid, s.bstop, s.bsync, s.reason, s.probeid, s.resuming, s.scanwindows, s.scanwindowdelay, s.scansent, s.targettype, s.isstopped, s.ispaused, s.hostname, s.lookup, s.scanlessreportxid,s.scanschema,
		(CASE WHEN REMOTEXID > 0 THEN 'Distributed: ' ELSE '' END || (CASE WHEN S.SCANNERID > 0 THEN (SELECT NAME FROM TSCANNERS WHERE XID = S.SCANNERID AND XUSERXID = S.XUSERXID) ELSE 'Local' END)) AS SCANNERNAME,
		s.remotexid, s.txreport, s.dcreated, s.dupdated, s.wakeonlan, s.wakeonlandelay, s.wakeonlansent,
		CASE WHEN s.xtemplate = 0::bigint THEN 'Custom'::character varying WHEN s.vcservice='P' OR s.vcservice='A' OR s.vcservice='G' THEN 'Discovery' WHEN templatename IS NOT NULL THEN templatename ELSE (SELECT name FROM tsavedscanprefs WHERE xid = s.xtemplate) END AS "template",
		u.vccompany AS company, includeAdminRules, activatecompliance, dscanended, openports, scanresult, ipaddress, reportattempted, priority, pauseduration, workflowid,
		CASE WHEN iverify > 0 THEN (SELECT port FROM tverifys WHERE tscanstatuss_xid = s.xid) ELSE 0 END AS port, scanmode, s.argoworkflowname, s.issues, s.networklookupdata
		FROM tscanstatuss s
		LEFT JOIN tusers u ON u.xid = s.xuserxid;


DROP VIEW IF EXISTS schedulesview CASCADE;
CREATE OR REPLACE VIEW schedulesview
AS
SELECT s.id, s.userid, subuserid, s.name, s.deleted, nextscandate, maxscantimeminutes, (maxscantimeminutes / 60) AS maxscantime, scanwindows, scanwindowdelay, scannerid,
latestscandate, COALESCE(latestscanstatus, -1) AS latestscanstatus, urilist, uriblacklist, uriwhitelist, maximumlinks, requestdelay, transfertimeout, eventtimeout,
useragent, xss, contentanalysis, sqlinjection, timesqlinjection, webappcount,
remotefileinclude, localfileinclude, codeinjection, commandinjection, formatstring, crlfinjection, unvalidatedredirect,
enableajax, discoverymode, dayweekmonth, finalscandate, frequency, scheme, hostname, port,
COALESCE((SELECT name FROM tscanners WHERE xid = s.scannerid AND xuserxid = s.userid), 'Local') AS scannername,
createdby, updatedby, type, ignoretcprst, reportbpffilter, useglobalignoretargetlist, hosts, infrastructurescan,
(SELECT COALESCE((SELECT vcfullname FROM tsubusers WHERE xid = s.subuserid), (SELECT vcfullname FROM tusers WHERE xid = s.userid))) AS owner, template = 10 AS iswas,
wascertificate, certificatepassword, comment, GREATEST(DATE_TRUNC('minute', totalscanduration / totalscans), '0'::interval) AS averagescanduration,
GREATEST(DATE_TRUNC('minute', latestscanduration), '0'::interval) AS latestscanduration, s.gmtoffset, s.scanjobcount, s.awsarn, s.lastreportids,
COALESCE(s.lastreportids[1], 0) AS lastreportid, fromldap, addtogroupxid, wakeonlandelay, ignoretargetlist, cvss_sr_conf, cvss_sr_avail, cvss_sr_integ, cvss_cdp, cvss_td,
schedulegroupid, compliancesenabled, emptytargetgroup, priority, ldapsearch, servicenow, template, ports, concurrentscans,
custom0, custom1, custom2, custom3, custom4, custom5, custom6, custom7, custom8, custom9, addnotexistingonly, scanmode, activatecompliance, scancount, basedn, netbioslookup,
scanless, targetcustom0, targetcustom1, targetcustom2, targetcustom3, targetcustom4, targetcustom5, targetcustom6, targetcustom7, targetcustom8, targetcustom9,
dnslookup, disableprotocol, amazondiscovery, agentdiscovery, agentscanfrequency, agentscheduleid, synctoagentapi, agentcustomattributeflags, updateagentattributes, updatetargetattributes, syncingtoagentapi, nrofhighvulns, nrofmediumvulns, nroflowvulns, settings,
(SELECT COALESCE((SELECT globalignoretargetlist FROM tsubusers WHERE xid = s.subuserid), (SELECT globalignoretargetlist FROM tusers WHERE xid = s.userid))) AS globalignoretargetlist,
CASE WHEN s.scanmode = 0 THEN 'Discovery' ELSE (SELECT name FROM tsavedscanprefs WHERE xid = s.template) END AS templatename,
(SELECT string_agg(x.xgroupxid::text||',') FROM xlinkscheduleobject x WHERE x.itype = 2 AND x.xid = s.id) AS grouplist,
(SELECT array_to_string(array_agg((CASE WHEN x.scannerid != -1 THEN x.vctarget || '<' || COALESCE((SELECT name FROM tscanners WHERE xid = x.scannerid), 'Local') ELSE x.vctarget END) || (CASE WHEN x.comment != '' THEN ' '|| x.comment ELSE '' END)), E'\n') FROM xlinkscheduleobject x WHERE x.itype = 1 AND x.xid = s.id) AS targetlist,
slsnotified, COALESCE(tz.name, 'UTC') AS timezone, ((nextscandate AT TIME ZONE invert_gmt_timezone(COALESCE(tz.name, 'UTC'))) AT TIME ZONE 'UTC') AS nextscandateutc
FROM schedules s LEFT JOIN pg_timezone_names tz on tz.name = s.timezone;

DROP VIEW IF EXISTS vwasdiscoveryresult CASCADE;
CREATE OR REPLACE view vwasdiscoveryresult
AS
SELECT R.XID, R.XSOXID, R.URI, R.OUTOFSCOPE, R.STATUS, R.WAS, R.VHOSTXID, R.XSCANJOBXID, R.SOURCEURI, R.CRAWLEDFAILED, R.FUZZED, R.XUSERXID, H.HOSTNAME AS VHOST, (R.VHOSTXID || '_' || R.OUTOFSCOPE) AS HOSTXID, R.XIPXID
FROM TWASDISCOVERYRESULTS R
INNER JOIN HOSTIDS H ON H.ID = R.VHOSTXID;


--
-- Name: vscheduleobjectgroup; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vscheduleobjectgroup CASCADE;
CREATE OR REPLACE view vscheduleobjectgroup AS
select g.*, xpathup, xpathdown from tscheduleobjectgroups g
left join (with recursive tree as
	(SELECT xid, array[xid]::bigint[] as xpathup, xuserxid from tscheduleobjectgroups where xiparentid = -1
	UNION ALL
	SELECT sg.xid,(t.xpathup || sg.xid), t.xuserxid FROM tree t, tscheduleobjectgroups sg WHERE sg.xiparentid = t.xid)
	SELECT *, (array(select xid from tree where t.xid = ANY(xpathup))) as xpathdown from tree t) path
on g.xid = path.xid and path.xuserxid = g.xuserxid;


--
-- Name: vsubgroup; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vsubgroup CASCADE;
CREATE OR REPLACE view vsubgroup
AS
SELECT DISTINCT U.XID AS XSUBXID, G.XID, G.NAME AS VCNAME
FROM TGENERICGROUPS G, XLINKSUBGROUPS S, TSUBUSERS U, XGENERICPATHS P WHERE
P.PARENTXID = S.XGROUPXID AND P.XID = G.XID AND
S.XSUBXID = U.XID AND G.XUSERXID = U.XIPARENTID;


--
-- Name: vsubhost; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vsubhost CASCADE;
CREATE OR REPLACE view vsubhost
AS
SELECT U.XID AS XSUBXID, D.XID AS XIPXID, D.HOSTNAMEID, D.IPADDRESS, D.SCANNERID AS VCHOST FROM TUSERDATAS D, TSUBUSERS U, XLINKSUBHOSTS X WHERE
X.XSUBXID = U.XID AND ((D.IPADDRESS >= X.IPADDRESS AND D.IPADDRESS <= X.ENDIPADDRESS) OR X.HOSTNAMEID = D.HOSTNAMEID) AND D.XUSERXID = U.XIPARENTID
UNION ALL
SELECT u.xid AS xsubxid, d.xid as XIPXID, d.hostnameid, d.ipaddress AS vchost, d.scannerid
FROM tuserdatas d, tsubusers u, xlinkgeneric x, tgenericgroups g, xlinksubgroups s
WHERE d.xid = x.xipxid and x.xid = g.xid AND g.xuserxid=u.xiparentid AND g.xid =ANY ((select ARRAY_AGG(xid) from xgenericpaths p where p.parentxid = s.xgroupxid)::BIGINT[])
AND s.xsubxid = u.xid AND d.xuserxid = u.xiparentid group by u.xid, d.xid, d.hostnameid, d.ipaddress, d.scannerid;

--
-- Name: vuserdata; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vuserdata CASCADE;
CREATE OR REPLACE VIEW vuserdata
AS
SELECT u.xid, u.xuserxid, u.scannerid, u.ipaddress, u.hostname, u.lastknowninfo, compliancesenabled, aws_instance_id, agentid, agentversion, agentlastsynchronized, agentretired, agentcallhomefrequency, virtualhosts, u.hostnameid, macaddress, cvss_sr_avail, cvss_sr_integ, cvss_sr_conf, cvss_cdp, cvss_td, sync, netbios, COALESCE(platform, 'NOTDETECTED') AS platform,
u.custom0, u.custom1, u.custom2, u.custom3, u.custom4, u.custom5, u.custom6, u.custom7, u.custom8, u.custom9, u.lookupipaddress, u.awsarn, u.macsource,
pci, confirmed, hiddenurls, lastreportid, sp.xid AS templateoverride, lastdiscoverydate, latestsuccessfulscandate,
u.ipaddress::TEXT || COALESCE(u.hostname, '') AS iphost, COALESCE(sc.name, 'Undefined') AS scannername,
latestscandate, COALESCE(latestscanstatus, -1) AS latestscanstatus,
(SELECT snassettag FROM snassets WHERE snsysid=u.snsysid AND xuserxid=u.xuserxid) AS snassettag,
(SELECT snname FROM snassets WHERE snsysid=u.snsysid AND xuserxid=u.xuserxid) AS snname,
(SELECT sntable FROM snassets WHERE snsysid=u.snsysid AND xuserxid=u.xuserxid) AS sntable,
(SELECT snsysid FROM snassets WHERE snsysid=u.snsysid AND xuserxid=u.xuserxid) AS snsysid,
(SELECT vcfullname FROM tusers WHERE xid = u.xcreator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * u.xcreator) AS creator,
(SELECT vcfullname FROM tusers WHERE xid = u.xupdator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * u.xupdator) AS updator, u.xupdator,
CASE WHEN u.pci=1 THEN COALESCE((SELECT bpcicompliant FROM treportentrys WHERE xscanlogxid=(SELECT xid FROM tscanlogs WHERE xipxid=u.xid ORDER BY xid DESC LIMIT 1)), 0) ELSE 0 END AS pcicompliance, COALESCE(highrisk, 0) AS high_count, COALESCE(mediumrisk, 0) AS medium_count, COALESCE(lowrisk, 0) AS low_count,
COALESCE(authenticationtype, 0)::SMALLINT AS authenticationtype, sshusername, sshpassword, sshpublickey, sshprivatekey, sshprivatekeypassword, sshsubstituteuser, sshsudocommand, smbusername, smbpassword, smbdomain, smbntlmv1, registrydump, COALESCE(au.enableremoteregistry, false) AS enableremoteregistry, authenticationresult, useslicense,
EXISTS (SELECT xid FROM tscanlogs WHERE u.xuserxid = xuserxid AND itype = 0 AND xtemplate != -10 AND xtemplate != 10 AND xipxid = u.xid AND scanschema IS NOT NULL AND bdeleted = 0) AS scanupdateavailible,
urlblacklist, requestbodyblacklist, inactivediscoveries, ungrouped,
cyberarkname, cyberarkoverridesafe, cyberarkoverridefolder, vsphereusername, vspherepassword, ignorecerts, credentialproviderid, thycoticsecretname, thycoticpath, businesscriticality, exposed, u.dcreated AS created, u.dcreated AS firstseen, ts.latestsuccessfulscandate AS lastseen, ARRAY['NETSEC']::source[] AS source, tags
FROM tuserdatas u
LEFT JOIN tauthentications au ON au.xipxid=u.xid AND u.authenticationtype = au.type
LEFT JOIN tscanners sc ON sc.xid = u.scannerid AND (sc.xuserxid = u.xuserxid OR sc.global)
LEFT JOIN tsavedscanprefs sp ON sp.targetoverride = u.xid AND sp.xid > 0 AND sp.xuserxid = u.xuserxid
LEFT JOIN targetscandata ts ON ts.targetid = u.xid;

DROP VIEW IF EXISTS vpciuserdata CASCADE;
CREATE OR REPLACE view vpciuserdata
AS
	SELECT *, FALSE AS outOfScope, 0 as xgroupxid, null as reason FROM vuserdata WHERE pci = 1
UNION ALL
	SELECT xid, xuserxid, 0, ipaddress, hostname, null, null, null, null, null, null, null, null, null, hostid(hostname), macaddress, 'ND', 'ND', 'ND', 'ND', 'ND', 0, null, 'NOTDETECTED',
		null, null, null, null, null, null, null, null, null, null, null, null, 0,
		1, 0, null, null, null, null, null, ipaddress::text || coalesce(hostname, ''),
		'Undefined',
		null, null, null, null, null, null,
		'Unknown', 'Unknown', -1,
		0, 0, 0, 0,
		0, null, null, null, null, null, null, null, null, null, null, 0, null, false, 0, 0, FALSE,
		null, null, 0, FALSE,
		null, null, null, null, null, FALSE, null, null, null, 'MEDIUM', false, null, null, null, null, null,
		TRUE, xgroupxid, reason FROM tdeletedtargets WHERE pci = 1;

--
-- Name: vuserdatasmall; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vuserdatasmall CASCADE;
CREATE OR REPLACE view vuserdatasmall
AS
SELECT U.XID, U.XUSERXID, U.SCANNERID, U.IPADDRESS, U.HOSTNAME, AWS_INSTANCE_ID, AWSARN, agentid, VIRTUALHOSTS, U.HOSTNAMEID, MACADDRESS, CVSS_SR_AVAIL, CVSS_SR_INTEG, CVSS_SR_CONF, CVSS_CDP, CVSS_TD,
U.IPADDRESS::text || COALESCE(U.HOSTNAME, '') AS IPHOST, PCI, U.CONFIRMED, SYNC, NETBIOS, COALESCE(ts.PLATFORM, 'NOTDETECTED') AS PLATFORM, COALESCE(SC.NAME , 'Local' ) AS SCANNERNAME,
LATESTSCANDATE, LATESTSCANSTATUS, FALSE AS SCANLESSPOSSIBLE, COALESCE(HIGHRISK, 0) AS HIGH_COUNT, COALESCE(MEDIUMRISK, 0) AS MEDIUM_COUNT, COALESCE(LOWRISK, 0) AS LOW_COUNT,
COALESCE((SELECT REACHABLE FROM TREPORTENTRYS WHERE XID = ts.LASTREPORTID), CASE WHEN LATESTSCANSTATUS IN (1,2,3,4,7) THEN 0 ELSE 1 END) AS REACHABLE, ts.UNGROUPED
FROM TUSERDATAS U
LEFT JOIN targetscandata ts ON u.xid = ts.targetid
LEFT JOIN TSCANNERS SC ON SC.XID = U.SCANNERID AND SC.XUSERXID = U.XUSERXID;

--
-- Name: vusergroup; Type: VIEW; Schema: public; Owner: -
--

DROP VIEW IF EXISTS vusergroup CASCADE;
CREATE OR REPLACE VIEW vusergroup AS
		SELECT u.xid, u.xuserxid, u.vcname, u.bosettings, u.boreports, u.boschedules, u.bsubadmin, u.boemail, u.boadmingroups, u.bhadmin, u.bosms, u.bodisable, u.bovultext, u.bodeletereport, u.badminusergroup,
		u.bacceptrisk, u.pcireporting, u.pciscoping, u.pcischeduling, u.pcidisputing, u.webappadmin, u.webappreporting, u.webappdeletereport, u.forcegroupscheduling, u.managedservices, u.managedservicescomment, u.verifyscan, u.dashboard, u.stopscan,
		xcreator, (SELECT tusers.vcfullname FROM tusers WHERE (tusers.xid = u.xcreator) UNION ALL SELECT tsubusers.vcfullname FROM tsubusers WHERE (tsubusers.xid = (-1 * u.xcreator))) AS creator,
		xupdator, (SELECT tusers.vcfullname FROM tusers WHERE (tusers.xid = u.xupdator) UNION ALL SELECT tsubusers.vcfullname FROM tsubusers WHERE (tsubusers.xid = (-1 * u.xupdator))) AS updator,
		u.ldapgroup, u.compliance_enabled, u.markcomplianceexceptions, u.answercompliancequestions, u.approvecompliancequestions, u.editcompliancepolicies, u.bticketmanagement, u.grantalltickets, u.readonly, u.web, u.read_auditlogs, u.readlicense,
		u.swatcomment, u.swatverification, u.swatdiscussion, u.swatrisks, u.wasx, u.rulemanagement, u.ruleadmin, u.autorules, u.editrules, u.submitscoping
		FROM tusergroups u;


--
-- Name: vloggings; Type: VIEW; Schema: public; Owner: -
--
DROP VIEW IF EXISTS vloggings CASCADE;
CREATE OR REPLACE VIEW vloggings
AS
SELECT s.xid, s.xuserxid, s.xsubuserxid, s.itype, s.xrefid, s.ipriority, s.recipient, s.assignee, s.ticketpriority, s.ticketsummary, duedatenumber, duedatetype, uisource,
s.myscans, s.newfindings, s.scanformat, s.encryptionkey, s.attachreport, s.reporttype, s.scantype, s.eventname, s.syslogfields, s.dblogfields, s.attributefields, s.targetinformation, s.customsubject, s.customtext,
(SELECT string_agg(x.targets||E'\n') FROM xlinkloggings x WHERE x.xid = s.xid) AS targetlist,
(SELECT string_agg(x.groupxid||',') FROM xlinklogginggroups x WHERE x.xid = s.xid) AS targetgrouplist,
(SELECT string_agg(coalesce(u.vcfullname, s.vcfullname) || ',') from unnest(s.assignee) a LEFT JOIN tusers u ON u.xid = a LEFT JOIN tsubusers s ON s.xid = -a) AS xassignee,
(SELECT string_agg(x.swatid||',') FROM xlinkloggingswat x WHERE x.xid = s.xid) AS swatlist, reporttemplate,
reportpassword, onlyonpreviouslyactive, s.comments, s.emailencryptionkey, s.bactive, s.customconfig, s.daysinadvance, s.scriptid, s.splunkformat
FROM tloggings s;


--
-- vreporthost
--
DROP VIEW IF EXISTS vreporthost CASCADE;
CREATE OR REPLACE view vreporthost
as
SELECT XIPXID as xid, XUSERXID, VCTARGET AS IPADDRESS, HOSTNAMEID, 0::bigint AS LATESTSCANSTATUS, XIPXID, ''::varchar(1) AS SCANNERNAME, CASE WHEN XTEMPLATE=10 THEN 1 ELSE 0 END AS PCI, XTEMPLATE
FROM TREPORTENTRYS R
GROUP BY XUSERXID, VCTARGET, HOSTNAMEID, XIPXID, XTEMPLATE;

--
-- vscanner
--
DROP VIEW IF EXISTS vscanner CASCADE;
CREATE OR REPLACE view vscanner
AS
SELECT S.XID, S.XUSERXID, S.NAME, S.IPADDRESS, S.KEY, S.MODE, S.CREATED, S.APPROVED, S.LASTCONNECTION, S.INACTIVE, S.MESSAGE, S.ISOUTPOST, S.ISAWSSCANNER, S.SCANNINGDISABLED,
S.POLLING, S.USEPROXY, S.LASTNOTIFICATION, S.LASTUPDATE, S.VERSION, S.PERFORMUPDATE, S.ISGROUP, CASE WHEN S.ISGROUP = 1 THEN S.XID ELSE G.XID END AS GROUPXID,
CASE WHEN S.ISGROUP = 1 THEN S.NAME ELSE G.NAME END AS GROUPNAME, S.DELETED, S.HWADDR, S.SERVERSTATUS, S.UIVERSION, S.SCANNERVERSION, S.XUPDATOR, S.GLOBAL, S.HMACKEY, S.APPSECSCALESCANNER, S.RULESVERSION,
S.CERTIFICATEREQUEST, S.CERTIFICATE, S.SLSSCANSCHEDULED, S.REVOKED, S.UUID
FROM TSCANNERS S
LEFT JOIN TSCANNERS G ON (S.GROUPXID = G.XID AND S.XUSERXID = G.XUSERXID);

DROP VIEW IF EXISTS vtranslation CASCADE;
CREATE OR REPLACE VIEW vtranslation AS
	SELECT a.key, a.value, COALESCE(lang.language, a.language) AS language, b.value AS original, b.locationHint, a.type,
	CASE WHEN lang IS NULL OR lang.language = 'en' THEN a.valid ELSE 0 END AS valid,
	CASE WHEN lang IS NULL OR lang.language = 'en' THEN a.ignore ELSE 0 END AS ignore
	FROM ttranslations a
	LEFT JOIN ttranslationlanguages lang
	ON (a.language = 'en' AND (a.type != 4 OR (a.type = 4 AND (lang.language = 'jp' OR lang.language = 'en'))) AND (a.translations IS NULL OR lang.language <> ALL (a.translations)))
	LEFT JOIN ttranslations b ON a.key = b.key AND b.language = 'en';

DROP VIEW IF EXISTS vreporttemplate CASCADE;
CREATE OR REPLACE view vreporttemplate
AS
SELECT S.XID, S.XUSERXID, XSUBUSERXID, NAME, ISPUBLIC, STATE, SERVERFILTER, TARGETS, TARGETGROUPS, SCHEDULEXID, COMPLIANCEPOLICY,
(SELECT COALESCE((SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = S.XSUBUSERXID), (SELECT VCFULLNAME FROM TUSERS WHERE XID = S.XUSERXID))) AS OWNER,
(SELECT name FROM schedules WHERE id=schedulexid) AS SCHEDULENAME, SCANTYPE, COMMENT
FROM TREPORTTEMPLATES S;

DROP VIEW IF EXISTS vgridview CASCADE;
CREATE OR REPLACE VIEW vgridview
AS
SELECT S.XID, S.XUSERXID, XSUBUSERXID, NAME, STATEID, STATE,
(SELECT COALESCE((SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = S.XSUBUSERXID), (SELECT VCFULLNAME FROM TUSERS WHERE XID = S.XUSERXID))) AS OWNER
FROM tgridviews S;

DROP VIEW IF EXISTS vreportschedule CASCADE;
CREATE OR REPLACE VIEW vreportschedule
AS
SELECT s.xid, s.xuserxid, s.xsubuserxid, s.name, s.frequency, s.lastdate, s.latestdate, s.dayweekmonth, s.reporttype, s.period, s.length, s.format, s.recipient, s.recipientemail, s.encryptionkey, s.reporttemplate, s.targets, s.targetgroups,
s.managedreporttitle, s.managedreportgroup, s.managedreporttoken,
s.scantype, s.recipienttype, s.reportpassword, s.targetgroupdepth, s.includehostinfo, s.reportLevel, s.sendemptyreports, s.recipientdir, s.xsoxid,
(CASE WHEN (su.xid IS NULL OR su.bactive = 1) AND u.bactive = 1 THEN s.nextdate ELSE null END) AS nextdate, s.comment, s.zip,
(CASE WHEN su.xid IS NOT NULL THEN su.vcfullname ELSE u.vcfullname END) AS owner, s.customsubject, s.customtext, s.includepolicy
FROM treportschedules s
LEFT JOIN tusers u ON u.xid = s.xuserxid
LEFT JOIN tsubusers su ON su.xid = s.xsubuserxid;

DROP VIEW IF EXISTS vmanagedservicespermissions CASCADE;
CREATE OR REPLACE VIEW vmanagedservicespermissions
AS
SELECT a.xid AS serviceuser, b.xid AS userxid
FROM tusers a
LEFT JOIN tusers b ON (a.xroles LIKE '%admin%' OR a.xroles LIKE '%support%' OR b.xiparentid = a.xiparentid OR b.xiparentid::text =ANY(string_to_array(a.xsalesorganizations, ',')))
WHERE a.xroles LIKE '%services%' OR a.xroles LIKE '%admin%' OR a.xroles LIKE '%support%'
UNION
SELECT serviceuser, xuserxid AS userxid
FROM tmanagedservicesgrants;

DROP VIEW IF EXISTS vvultextscomment CASCADE;
CREATE OR REPLACE VIEW vvultextscomment
AS
SELECT V.XID, V.XUSERXID, V.XSUBUSERXID, V.COMMENT, V.SCRIPTID, V.PUBLIC, V.DCREATED, V.DAPPROVED, V.FINDINGID,
CASE WHEN V.XSUBUSERXID > 0 THEN (SELECT VCFULLNAME FROM TSUBUSERS WHERE XID = V.XSUBUSERXID) ELSE (SELECT VCFULLNAME FROM TUSERS WHERE XID = V.XUSERXID) END AS NAME,
CASE WHEN V.DAPPROVED IS NOT NULL THEN 1 ELSE 0 END AS APPROVED, T.VCNAME AS SCRIPTNAME, V.PARENTCOMMENT, V.READBY
FROM TVULTEXTSCOMMENTS V
LEFT JOIN VVULTEXT T ON V.SCRIPTID = T.XID;

DROP VIEW IF EXISTS vrules CASCADE;
CREATE VIEW vrules AS
	SELECT r.allproductsrequired, r.informational, r.ruleid, r.name, r.pcifail, r.potentialfalsepositive, r.requiredproduct, r.cve, r.bugtraq, r.osvdb, r.family, r.protocol, r.booleanmethod, r.description, r.solutiontype, r.vulnerabilitytype,
	r.solution, r.solutiontitle, r.solutionproduct, r.cvssvector, r.cvssv3vector, r.gatheredinformationtext, r.comment, r.created, r.updated, r.reviewed, r.reviewer, r.creator,
	r.updatedby, r.deleted, r.runforadmins, r.cvssscore, r.cvssv3score, r.risklevel, r.hasexploits, r.specialnotes, r.isscript, r.was_informational, r.was_falsepos, r.reviewneeded, COALESCE(r.autorules, 0) AS autorules, r.released,
	CASE WHEN r.updatedby = -1 THEN 'Autorules' WHEN r.updatedby < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-r.updatedby))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = r.updatedby)) END AS updatedbyname,
	CASE WHEN r.creator = -1 THEN 'Autorules' WHEN r.creator < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-r.creator))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = r.creator)) END AS creatorname,
	CASE WHEN r.reviewer = -1 THEN 'Autorules' WHEN r.reviewer < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-r.reviewer))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = r.reviewer)) END AS reviewername,
	nvdcvssvector, nvdcvssscore, overridecomment, patchreleased, pi.name as productname, COALESCE(readyforreview, false) AS readyforreview, cwe, COALESCE(r.operation, 0) AS operation,
	CASE WHEN pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END AS normalizedName, cyrating, (r.farsight->'risk'->>'score')::NUMERIC AS exploitprobability, previouscyrating,
	(r.farsight->'risk'->>'score')::NUMERIC - (r.farsight->'risk'->>'delta')::NUMERIC AS previousexploitprobability, (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE AS cyr3conupdated, (r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen
	FROM trules r
		LEFT JOIN tproductinformation pi ON pi.xid = r.productid;

DROP VIEW IF EXISTS vruleshistory CASCADE;
CREATE OR REPLACE VIEW vruleshistory AS
	SELECT r.id,r.ruleid,r.historydate,r.name,r.pcifail,r.potentialfalsepositive,r.requiredproduct,r.cve,r.bugtraq,r.service,r.family,r.protocol,r.booleanmethod,r.description,r.solutiontype,r.solution,r.solutionproduct,
	r.solutiontitle,r.cvssvector,r.gatheredinformationtext,r.comment,r.created,r.updated,r.reviewed,r.reviewer,r.creator,r.updatedby,r.deleted,r.runforadmins,r.cvssscore,r.risklevel,r.specialnote,r.product,r.isscript,
	r.was_informational,r.was_falsepos,r.informational,r.vulnerabilitytype,r.nvdcvssvector,r.overridecomment,r.nvdcvssscore,r.patchreleased,r.reviewneeded,r.cvssv3score,r.cvssv3vector,r.autorules,r.specialnotes,
	NULL::TIMESTAMP WITHOUT TIME ZONE AS released, false AS readyforreview, cwe, operation,
	CASE WHEN r.updatedby = -1 THEN 'Autorules' WHEN r.updatedby < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-r.updatedby) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=r.updatedby) END AS updatedbyname,
	CASE WHEN r.creator = -1 THEN 'Autorules' WHEN r.creator < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-r.creator) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=r.creator) END AS creatorname,
	CASE WHEN r.reviewer = -1 THEN 'Autorules' WHEN r.reviewer < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-r.reviewer) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=r.reviewer) END AS reviewername,
	pi.name AS productname, CASE WHEN pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END AS normalizedname, cyrating, (r.farsight->'risk'->>'score')::NUMERIC AS exploitprobability, previouscyrating,
	(r.farsight->'risk'->>'score')::NUMERIC - (r.farsight->'risk'->>'delta')::NUMERIC AS previousexploitprobability, (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE AS cyr3conupdated, (r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen
	FROM truleshistory r
	LEFT JOIN tproductinformation pi ON r.solutionproduct = pi.product;

DROP VIEW IF EXISTS vproductinformation CASCADE;
CREATE OR REPLACE VIEW vproductinformation AS
	SELECT xid, product, name, latestservicepacks, solutionversions, versionendoflife, branches, parser, parseroptions, partorder, parserdelimiters, backported, productchain, signaturekeys,
	updatesolutiontitle, updatesolutiontext, patchesimpliedbyversion, producturl, productendoflife, normalizerulename, nvdtags, blacklistedcve, comment, compliance, updated, updatedby,
	CASE WHEN p.updatedby = -1 THEN 'Autorules' WHEN p.updatedby < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-updatedby))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = p.updatedby)) END AS updatedbyname
	FROM tproductinformation p;

DROP VIEW IF EXISTS vproductinformationhistory CASCADE;
CREATE OR REPLACE VIEW vproductinformationhistory AS
	SELECT p.historyxid, p.historydate, p.xid, p.product, p.name, p.productendoflife, p.latestservicepacks, p.solutionversions, p.versionendoflife, p.branches, p.parser, p.parseroptions, p.partorder, p.parserdelimiters, p.updatesolutiontitle, p.updatesolutiontext, p.patchesimpliedbyversion, p.producturl, p.normalizerulename, p.nvdtags, p.blacklistedcve, p.comment, p.compliance, p.backported, p.productchain, p.signaturekeys, p.updatedby, p.updated,
	CASE WHEN p.updatedby = -1 THEN 'Autorules' WHEN p.updatedby < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-p.updatedby) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=p.updatedby) END AS updatedbyname
	FROM tproductinformationhistory p;

DROP VIEW IF EXISTS vcve CASCADE;
CREATE OR REPLACE VIEW vcve AS
	SELECT xid, name, title, description, vector, vector3, refs, reviewneeded, products, status, created, updated, updatedby,
	CASE WHEN c.updatedby = -1 THEN 'Autorules' WHEN c.updatedby < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-c.updatedby))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = c.updatedby)) END AS updatedbyname
	FROM tcve c;

DROP VIEW IF EXISTS vcvehistory CASCADE;
CREATE OR REPLACE VIEW vcvehistory AS
	SELECT historyxid, historydate, xid, name, title, description, vector, vector3, refs, reviewneeded, products, status, created, updated, updatedby,
	CASE WHEN c.updatedby = -1 THEN 'Autorules' WHEN c.updatedby < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-c.updatedby))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = c.updatedby)) END AS updatedbyname
	FROM tcvehistory c;

DROP VIEW IF EXISTS vpatchsupersedence CASCADE;
CREATE OR REPLACE VIEW vpatchsupersedence AS
	SELECT xid, product, patch, supersededby, dateadded, bulletinid, updated, updatedby, deleted,
	CASE WHEN p.updatedby = -1 THEN 'Autorules' WHEN p.updatedby < 0 THEN (SELECT u.vcfullname FROM tsubusers u WHERE (u.xid = (-updatedby))) ELSE (SELECT u.vcfullname FROM tusers u WHERE (u.xid = p.updatedby)) END AS updatedbyname
	FROM tpatchsupersedence p;

DROP VIEW IF EXISTS vpatchsupersedencetree CASCADE;
CREATE VIEW vpatchsupersedencetree AS
	SELECT DISTINCT ON (patch, supersededby) xid, product, patch, supersededby, dateadded, bulletinid FROM tpatchsupersedence;

DROP VIEW IF EXISTS vappaccess CASCADE;
CREATE OR REPLACE VIEW vappaccess
AS
	SELECT xid, xuserxid, name, usage, ipaddress, ipaddressrestriction, tokenkey, active, xsubuserxid, expires,
	(SELECT vcfullname FROM tusers WHERE xid = a.xcreator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * a.xcreator) AS creator,
	(SELECT vcfullname FROM tusers WHERE xid = a.xupdator UNION ALL SELECT vcfullname FROM tsubusers WHERE xid = -1 * a.xupdator) AS updator,
	(SELECT COALESCE((SELECT vcfullname FROM tsubusers WHERE xid = a.xsubuserxid), (SELECT vcfullname FROM tusers WHERE xid = a.xuserxid))) AS accessright
	FROM tappaccess a;

DROP VIEW IF EXISTS vcompliancepolicies;
CREATE OR REPLACE VIEW vcompliancepolicies
AS
	SELECT p.xid, p.name, p.description, p.xuserxid, p.global, p.deleted, p.parent, p.isgroup, p.xsubuserxid, p.private, p.version, targetgroups,
	EXISTS(SELECT xid FROM tcompliancerequirements r WHERE r.policyid = p.xid AND r.requirementtype = 1 AND NOT r.disabled) AS questions
	FROM tcompliancepolicies p;

DROP VIEW IF EXISTS vcompliancerequirements;
CREATE OR REPLACE VIEW vcompliancerequirements
AS
	SELECT r.xid, r.policyid, r.parent, r.description, r.orderid, r.windowsversion, r.report, r.disabled, r.inherit, r.fields, r.level, r.requirementtype, r.uploadpolicy, r.identifier, r.precondition,
	CASE WHEN r.requirementtype = 0 THEN r.settings ELSE null END AS settings,
	CASE WHEN r.requirementtype = 0 THEN r.solution ELSE null END AS solution,
	CASE WHEN r.requirementtype = 1 THEN r.settings ELSE null END AS answers,
	CASE WHEN r.requirementtype = 1 THEN r.solution ELSE null END AS question,
	p.name AS policy, CASE WHEN r.inherit > 0 THEN (SELECT name FROM tcompliancepolicies WHERE xid = r.inherit) ELSE r.name END AS name
	FROM tcompliancerequirements r
	LEFT JOIN tcompliancepolicies p ON p.xid = r.policyid;

DROP VIEW IF EXISTS vcompliancefindings;
CREATE OR REPLACE VIEW vcompliancefindings
AS
	SELECT f.xid, target, f.xipxid, re.name, information, reportid, (compliant OR COALESCE(NOW() < acceptuntil, false)) AS compliant, f.vcvulnid, potentialfalse, COALESCE(re.level, 0) AS level, f.facts,
	COALESCE((NOW() < acceptuntil), false) AS accepted, a.comment AS acceptcomment, CASE WHEN acceptforever THEN '9999-01-01'::timestamp ELSE acceptuntil END AS acceptuntil, acceptforever, a.date AS acceptdate,
	COALESCE(fp.xipxid > 0, false) AS falsepositive, fp.comment AS falsepositivecomment, fp.date AS falsepositivedate,
	hostname, netbios, COALESCE(platform, 'NOTDETECTED') AS platform, (SELECT name FROM tscanners s WHERE s.xid=f.scannerid) AS scannername, scannerid, re.description, re.solution, re.fields, re.xid AS requirementid
	FROM tcompliancefindings f
	LEFT JOIN tcomplianceaccepted a ON f.xipxid = a.xipxid AND f.vcvulnid = a.vcvulnid
	LEFT JOIN tcompliancefalsepositive fp ON f.xipxid = fp.xipxid AND f.vcvulnid = fp.vcvulnid
	LEFT JOIN tcompliancerequirements re ON f.vcvulnid = re.xid;

DROP VIEW IF EXISTS vcompliancequestions;
CREATE OR REPLACE VIEW vcompliancequestions
AS
	SELECT q.xid, q.xuserxid, q.policyid, q.identifier, q.answer, q.answerdate, q.approved, q.approvedate, (q.answerdate IS NOT NULL) AS answered, (q.answerdate IS NOT NULL AND q.approved = 1) AS compliant,
	re.name, re.description, re.solution AS question, re.uploadpolicy, re.settings AS answers, re.xid AS requirementid, q.requirementpolicy,
	q.answerby, CASE WHEN q.answerby < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-q.answerby) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=q.answerby) END AS answerbyname,
	q.approvedby, CASE WHEN q.approvedby < 0 THEN (SELECT vcfullname FROM tsubusers u WHERE u.xid=-q.approvedby) ELSE (SELECT vcfullname FROM tusers u WHERE u.xid=q.approvedby) END AS approvedbyname,
	(SELECT string_agg(xid::TEXT || '§' || name, ',') FROM tcompliancequestionfiles f where f.questionid = q.xid) AS files
	FROM tcompliancequestions q
	LEFT JOIN tcompliancerequirements re ON q.identifier = re.identifier AND re.policyid = q.requirementpolicy AND re.requirementtype = 1;

DROP VIEW IF EXISTS vswatschedules CASCADE;
CREATE OR REPLACE VIEW vswatschedules AS
	SELECT s.id, s.customerid, s.name, s.nextscandate, s.scanstatus, s.scanrunning, s.scanstarted, s.latestscandate, s.latestscanstatus, s.latestscantime,
	s.jobid, s.scannerid, s.deleted, s.status, s.parent, s.parent > 0 AS instance, u.vccompany AS company,
	(SELECT SUM(findingsdraft)::BIGINT FROM swatschedules WHERE customerid = s.customerid AND NOT deleted AND (parent = s.id OR id = s.id)) AS findingsdraft,
	(SELECT SUM(findingsreview)::BIGINT FROM swatschedules WHERE customerid = s.customerid AND NOT deleted AND (parent = s.id OR id = s.id)) AS findingsreview,
	(SELECT SUM(findingsqa)::BIGINT FROM swatschedules WHERE customerid = s.customerid AND NOT deleted AND (parent = s.id OR id = s.id)) AS findingsqa,
	(SELECT SUM(findingsready)::BIGINT FROM swatschedules WHERE customerid = s.customerid AND NOT deleted AND (parent = s.id OR id = s.id)) AS findingsready,
	(SELECT content FROM summaries es WHERE es.swatscheduleid = s.id AND es.deleted IS NULL AND es.status = 'PUBLISHED'::summarystatus ORDER BY es.published DESC LIMIT 1) AS executivesummary,
	0 AS high, 0 AS medium, 0 AS low, NULL::TEXT AS owasp
	FROM swatschedules s
	LEFT JOIN tusers u ON s.customerid = u.xid;

DROP VIEW IF EXISTS vreportlist CASCADE;
CREATE OR REPLACE VIEW vreportlist
AS
	SELECT re.xid, re.xuserxid, re.xtemplate, re.vctarget, re.bpci,
	COALESCE(re.platform, 'NOTDETECTED'::character varying) AS platform,
	re.dreportdate, re.dreportdate AS date, re.xscanlogxid, re.xscanjobxid, re.benabled, re.scannerid, re.xipxid, re.ipaddress, re.reachable,
	inetaddress,
	u.hostname, u.netbios, u.virtualhosts, u.aws_instance_id, u.agentid, ts.latestscanstatus,
	re.high_count, re.medium_count, re.low_count, re.xsoxid
	FROM treportentrys re
	LEFT JOIN tuserdatas u ON u.xid = re.xipxid AND u.xuserxid = re.xuserxid
	LEFT JOIN targetscandata ts ON u.xid = ts.targetid;

DROP VIEW IF EXISTS subscriptionauditsview CASCADE;
CREATE OR REPLACE VIEW subscriptionauditsview AS
	SELECT s.id, s.action, s.time, s.ip, s.userid, s.subuserid, s.comment, s.customerid,
	CASE WHEN s.subuserid > 0 THEN (SELECT vcfullname FROM tsubusers WHERE xid = s.subuserid) ELSE (SELECT vcfullname FROM tusers WHERE xid = s.userid) END AS username,
	CASE WHEN s.customerid > 0 THEN (SELECT vccompany FROM tusers WHERE xid = s.customerid) ELSE NULL END AS customername
	FROM subscriptionaudits s;

DROP VIEW IF EXISTS vultextattributesview CASCADE;
CREATE OR REPLACE VIEW vultextattributesview AS
	SELECT custom0 AS findingcustom0, custom1 AS findingcustom1, custom2 AS findingcustom2, custom3 AS findingcustom3, custom4 AS findingcustom4, custom5 AS findingcustom5, custom6 AS findingcustom6, custom7 AS findingcustom7, custom8 AS findingcustom8, custom9 AS findingcustom9,
		xuserxid AS attribute_user, vcvulnid AS attribute_vcvulnid FROM tvultextattributes;

DROP VIEW IF EXISTS assetidentifiersview CASCADE;
CREATE OR REPLACE VIEW assetidentifiersview AS
	SELECT ai.id, ai.name, ai.presentablename, ai.type, ai.firstseen, ai.lastseen, ai.firstscanid, ai.lastscanid, ai.source, ai.scannerid, COALESCE(s.name, 'Local') AS scannername, COALESCE(ai.customownership, ai.ownership) AS ownership, ai.customownership, ai.platform, ai.properties,
	ai.customerid, ai.created, ai.updated, ai.createdbyid, ai.createdby, COALESCE(ai.maskedcreatedby, ai.createdby) AS maskedcreatedby, ai.updatedbyid, ai.updatedby, COALESCE(ai.maskedupdatedby, ai.updatedby) AS maskedupdatedby, ai.deleted, ai.migration,
	ARRAY(SELECT tmp.id FROM (SELECT DISTINCT ai2.id, l.lastseen FROM assetidentifiers AS ai2, assetidentifier_assetidentifier AS l WHERE ai2.deleted IS NULL AND ((ai.id = l.assetidentifierid1 AND ai2.id = l.assetidentifierid2) OR (ai.id = l.assetidentifierid2 AND ai2.id = l.assetidentifierid1))) AS tmp ORDER BY tmp.lastseen, tmp.id) AS links,
	(SELECT array_agg(id) FROM assets WHERE deleted IS NULL AND id IN (SELECT assetid FROM asset_assetidentifier WHERE assetidentifierid = ai.id ORDER BY assetid)) AS assetids,
	(SELECT array_agg(name ORDER BY name) FROM assets WHERE deleted IS NULL AND id IN (SELECT assetid FROM asset_assetidentifier WHERE assetidentifierid = ai.id)) AS assetnames,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSETGROUP' AS origin WHERE EXISTS(SELECT a.id FROM assets a JOIN asset_assetidentifier aai ON a.id = aai.assetid JOIN assetgroup_asset aga ON a.id = aga.assetid WHERE a.deleted IS NULL AND aai.assetidentifierid = ai.id)
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSET' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ai.customerid AND tg.assetgroupid = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY((SELECT array_agg(assetgroupid) FROM assetgroup_asset aa
				WHERE aa.assetid = ANY((SELECT array_agg(id) FROM assets WHERE deleted IS NULL AND id = ANY((SELECT array_agg(assetid) FROM asset_assetidentifier WHERE assetidentifierid = ai.id)::INTEGER[]))::INTEGER[]))::INTEGER[]))
		UNION SELECT id, key, value, true AS inherited, 'ASSET' AS origin FROM tags t JOIN tag_asset ta ON t.id = ta.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ai.customerid AND ta.assetid = ANY((SELECT array_agg(id) FROM assets WHERE deleted IS NULL AND id = ANY((SELECT array_agg(assetid) FROM asset_assetidentifier WHERE assetidentifierid = ai.id)::INTEGER[]))::INTEGER[])
		UNION SELECT id, key, value, false AS inherited, 'ASSETIDENTIFIER' AS origin FROM tags t JOIN tag_assetidentifier ti ON t.id = ti.tagid WHERE t.deleted IS NULL AND t.customerid = ai.customerid AND ti.assetidentifierid = ai.id) t) AS tags,
	(SELECT array_agg(ac.id) FROM accounts ac, assetidentifier_account l WHERE l.assetidentifierid = ai.id AND l.accountid = ac.id AND ac.deleted IS NULL) AS accountids
	FROM assetidentifiers ai
	LEFT JOIN tscanners s ON s.xid = ai.scannerid;

DROP VIEW IF EXISTS scanconfigurationsview CASCADE;
CREATE OR REPLACE VIEW scanconfigurationsview AS
	SELECT s.id, s.name, s.template, s.configuration, s.groupid, s.enabled, s.scannerid, s.properties, s.workflowid,
	COALESCE(ts.name, 'Local') || COALESCE(NULLIF(' - ' || ts.hwaddr, ' - '), '') AS scannername,
	ARRAY(SELECT g.id FROM genericschedules g, scanconfiguration_genericschedule l WHERE l.scanconfigurationid = s.id AND l.genericscheduleid = g.id AND g.deleted IS NULL) AS scheduleids,
	ARRAY(SELECT a.id FROM assetidentifiers a, scanconfiguration_assetidentifier l WHERE l.scanconfigurationid = s.id AND l.assetidentifierid = a.id AND a.deleted IS NULL) AS assetidentifierids,
	ARRAY(SELECT a.id FROM assets a, asset_scanconfiguration l WHERE l.scanconfigurationid = s.id AND l.assetid = a.id AND a.deleted IS NULL) AS assetids,
	(SELECT MIN(g.nextoccurrence) FROM genericschedules g, scanconfiguration_genericschedule l WHERE l.scanconfigurationid = s.id AND l.genericscheduleid = g.id AND g.deleted IS NULL
	AND (g.remainingoccurrences IS NULL OR g.remainingoccurrences > 0) AND (until IS NULL OR until > now())) AS nextoccurrence,
	(SELECT l.ended FROM scanlogs l WHERE l.scanconfigurationid = s.id AND l.ended IS NOT NULL AND l.deleted IS NULL ORDER BY l.id DESC LIMIT 1) AS lastscan,
	(SELECT l.ended FROM scanlogs l WHERE l.scanconfigurationid = s.id AND l.ended IS NOT NULL AND l.deleted IS NULL AND (status = 'FINISHED' OR status = 'ISSUES') ORDER BY l.id DESC LIMIT 1) AS lastsuccessfulscan,
	s.customerid, s.created, s.updated, s.createdbyid, s.createdby, s.updatedbyid, s.updatedby, s.deleted,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM (
		SELECT 0 AS id, NULL AS key, NULL AS value, false AS inherited, 'SCANCONFIGURATION' AS origin
		UNION SELECT id, key, value, false AS inherited, 'SCANCONFIGURATION' AS origin
		FROM tags t JOIN tag_scanconfiguration ts ON t.id = ts.tagid WHERE t.deleted IS NULL AND t.customerid = s.customerid AND ts.scanconfigurationid = s.id
		UNION SELECT id, key, value, true AS inherited, 'SCANCONFIGURATION' AS origin
		FROM tags t JOIN tag_workflow tw ON t.id = tw.tagid WHERE t.deleted IS NULL AND t.customerid = s.customerid AND tw.workflowid = s.workflowid
	) t) AS tags
	FROM scanconfigurations s
	LEFT JOIN tscanners ts ON ts.xid = s.scannerid AND ts.deleted = 0;

DROP VIEW IF EXISTS customersview CASCADE;
CREATE OR REPLACE VIEW customersview AS
	SELECT c.id, c.name, c.created, c.updated, c.createdbyid, c.createdby, c.updatedbyid, c.updatedby, c.deleted, c.userid, c.features, c.backupencryptionkey, c.assetmigration,
	(SELECT vcname FROM tcountryl WHERE xid = c.country) AS country,
	(SELECT vcname FROM tstatel WHERE lower(xid) = c.state) AS state,
	COALESCE(u.bactive = 1, false) AS active, c.uuid::TEXT AS awsexternalid, c.uuid::TEXT,
	(SELECT vccompany FROM tusers WHERE xid = (SELECT xiparentid FROM taorganizations WHERE xid = c.organizationid)) AS organization,
	(SELECT uuid::TEXT FROM tusers WHERE xid = (SELECT xiparentid FROM taorganizations WHERE xid = c.organizationid)) AS organizationuuid,
	u.farsightproducts, COALESCE(u.web, false) AS hasswat, u.salesaccountid,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, false AS inherited, 'CUSTOMER' AS origin
		UNION SELECT id, key, value, false AS inherited, 'CUSTOMER' AS origin FROM tags t JOIN tag_customer tc ON t.id = tc.tagid WHERE t.deleted IS NULL AND tc.customerid = c.id) t) AS tags,
	u.customersuccessmanageremail, u.accountmanageremail,
	(CASE WHEN cm.customerid IS NULL THEN NULL ELSE jsonb_strip_nulls(json_build_object('queued', cm.queued, 'started', cm.started, 'ended', cm.ended, 'failed', cm.failed,
		'targets', cm.targets, 'migratedTargets', cm.migratedtargets, 'targetGroups', cm.targetgroups, 'migratedTargetGroups', cm.migratedtargetgroups, 'reports', cm.reports, 'migratedReports', cm.migratedReports, 'managedReports', cm.managedreports, 'migratedManagedReports', cm.migratedmanagedreports)::JSONB) END) AS migration,
	(CASE WHEN cm.customerid IS NULL THEN NULL WHEN cm.failed THEN 'FAILED' WHEN cm.ended IS NOT NULL THEN 'FINISHED' ELSE 'RUNNING' END) AS migrationstatus
	FROM customers c
	LEFT JOIN tusers u ON u.xid = c.userid
	LEFT JOIN customer_migration cm ON cm.customerid = c.id;

DROP VIEW IF EXISTS accountsview CASCADE;
CREATE OR REPLACE VIEW accountsview AS
	SELECT a.id, a.name, a.type, a.url, a.role, a.integrationid,
	a.customerid, a.created, a.updated, a.createdbyid, a.createdby, a.updatedbyid, a.updatedby, a.deleted,
	(SELECT array_agg(ai.id) FROM assetidentifiers ai, assetidentifier_account l WHERE l.accountid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL) AS assetidentifierids,
	(SELECT jsonb_build_object('id', 0, 'origin', 'ACCOUNT') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'ACCOUNT')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = a.customerid AND t.id IN (SELECT tagid FROM tag_account WHERE accountid = a.id)) AS tags,
	(SELECT array_agg(sp.id) FROM scanpolicies sp, scanpolicy_account l WHERE l.accountid = a.id AND l.scanpolicyid = sp.id AND sp.deleted IS NULL) AS scanpolicyids,
	(SELECT array_agg(id) FROM scanconfigurations sc WHERE sc.customerid = a.customerid AND sc.template = ANY(ARRAY['CLOUD_DISCOVERY','CLOUDSEC','DOCKER_DISCOVERY']::scantemplate[]) AND sc.deleted IS NULL AND (sc.configuration->>'accountId')::INTEGER = a.id) AS scanconfigurationids
	FROM accounts a;

DROP VIEW IF EXISTS credentialsview CASCADE;
CREATE OR REPLACE VIEW credentialsview AS
	SELECT cr.id, cr.accountid, cr.classid, cr.value,
	cr.customerid, cr.created, cr.updated, cr.createdbyid, cr.createdby, cr.updatedbyid, cr.updatedby, cr.deleted,
	cl.name AS classname, cl.hidden AS classhidden, a.integrationid,
	(SELECT jsonb_build_object('id', 0, 'inherited', true, 'origin', 'ACCOUNT') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', true, 'origin', 'ACCOUNT')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = cr.customerid AND t.id IN (SELECT tagid FROM tag_account WHERE accountid = cr.accountid)) AS tags
	FROM credentials cr
	LEFT JOIN credentialclasses cl ON cr.classid = cl.id
	LEFT JOIN accounts a ON cr.accountid = a.id;

DROP VIEW IF EXISTS genericschedulesview CASCADE;
CREATE OR REPLACE VIEW genericschedulesview AS
	SELECT id, name, enabled,
	startsfrom, frequency, interval, weekdays, days, nthdays, weeks, months,
	until, remainingoccurrences,
	nextoccurrence, finaloccurrence, timezone, blockedtimeslots, scanwindowduration,
	ARRAY(SELECT s.id FROM scanconfigurations AS s, scanconfiguration_genericschedule AS l WHERE l.genericscheduleid = g.id AND l.scanconfigurationid = s.id AND s.deleted IS NULL) AS scanconfigurationids,
	ARRAY(SELECT w.id FROM workflows AS w, workflow_genericschedule AS l WHERE l.genericscheduleid = g.id AND l.workflowid = w.id AND w.deleted IS NULL) AS workflowids,
	ARRAY(SELECT s.id FROM scheduledreports AS s, scheduledreport_genericschedule AS l WHERE l.genericscheduleid = g.id AND l.scheduledreportid = s.id AND s.deleted IS NULL) AS scheduledreportids,
	customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted
	FROM genericschedules g;

DROP VIEW IF EXISTS matchesview CASCADE;
CREATE OR REPLACE VIEW matchesview AS
	SELECT id, assetid, type, firstseen, lastseen, firstscanid, lastscanid, source, subscriptiontype, match, migration,
	(CASE WHEN type = 'COMPLIANCE' THEN (SELECT array_agg(id) FROM compliancefindings WHERE deleted IS NULL AND matchid = m.id)
	ELSE (SELECT array_agg(id) FROM findings WHERE deleted IS NULL AND id IN (SELECT findingid FROM match_finding WHERE matchid = m.id)) END) AS findingids,
	(SELECT array_agg(id) FROM information WHERE deleted IS NULL AND id IN (SELECT informationid FROM match_information WHERE matchid = m.id)) AS informationids,
	customerid, created, updated, createdbyid, createdby, COALESCE(maskedcreatedby, createdby) AS maskedcreatedby, updatedbyid, updatedby, COALESCE(maskedupdatedby, updatedby) AS maskedupdatedby, deleted,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSETGROUP' AS origin WHERE EXISTS(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = m.assetid)
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSET' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = m.customerid AND tg.assetgroupid = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = m.assetid))
		UNION SELECT id, key, value, true AS inherited, 'ASSET' AS origin FROM tags t JOIN tag_asset ta ON t.id = ta.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = m.customerid AND ta.assetid = m.assetid) t) AS tags
	FROM matches m;

DROP VIEW IF EXISTS commentsview CASCADE;
CREATE OR REPLACE VIEW commentsview AS
	SELECT c.id, c.parentid, c.entityid, c.customerid, c.entitytype,
		c.created, c.createdby, COALESCE(c.maskedcreatedby, c.createdby) AS maskedcreatedby, c.createdbyid, c.updated, c.updatedby, COALESCE(c.maskedupdatedby, c.updatedby) AS maskedupdatedby, c.updatedbyid, c.deleted,
		c.createdbyid IS NOT DISTINCT FROM 100 AS authorisstaff, -- BaseDbObject.APPSEC_USER_ID
		CASE WHEN c.deleted IS NOT NULL
			THEN ''
			ELSE c.comment
		END AS comment,
		CASE WHEN c.parentid IS NOT NULL
			THEN p.supportstatus
			ELSE c.supportstatus
		END AS supportstatus,
		CASE WHEN c.parentid IS NULL AND c.supportstatus = 'WAITING' AND c.deleted IS NULL
			THEN COALESCE ((SELECT created FROM comments WHERE parentid = c.id AND deleted IS NULL AND createdbyid IS DISTINCT FROM 100 AND created > (SELECT MAX(created) FROM comments WHERE parentid = c.id AND createdbyid IS NOT DISTINCT FROM 100) ORDER BY created ASC LIMIT 1), c.created)
			ELSE NULL
		END AS pendingsince -- Auxiliary field for commentpendingsince field in findingsview and assetgroupsview.
	FROM comments c LEFT JOIN comments p ON p.id = c.parentid;

DROP VIEW IF EXISTS findingsview CASCADE;
CREATE OR REPLACE VIEW findingsview AS
	SELECT f.id, f.checkid, r.created AS checkcreated, f.findingtemplateid, f.migration,
	f.cvssv2score, f.cvssv2basescore, f.cvssv2temporalscore, f.cvssv2environmentalscore, COALESCE(f.customcvssv2vector, f.cvssv2vector) AS cvssv2vector, f.cvssv2vector AS originalcvssv2vector,
	COALESCE(f.customcvssv2severity, f.cvssv2severity) AS cvssv2severity, f.customcvssv2severity, f.cvssv2severity AS originalcvssv2severity,
	f.cvssv3score, f.cvssv3basescore, f.cvssv3temporalscore, f.cvssv3environmentalscore, COALESCE(f.customcvssv3vector, f.cvssv3vector) AS cvssv3vector, f.cvssv3vector AS originalcvssv3vector,
	COALESCE(f.customcvssv3severity, f.cvssv3severity) AS cvssv3severity, f.customcvssv3severity, f.cvssv3severity AS originalcvssv3severity,
	COALESCE(f.cvssv3score,f.cvssv2score) AS cvssscore, COALESCE(f.customcvssv3severity, f.cvssv3severity, f.customcvssv2severity, f.cvssv2severity) AS cvssseverity,
	falsepositive, falsepositivecomment,
	f.accepted, f.acceptedcomment, f.accepteduntil, (CASE WHEN f.status = 'PRESENT' AND f.accepted IS NOT NULL AND f.accepteduntil > NOW() THEN true ELSE false END) AS isaccepted,
	f.fixed, COALESCE(f.commentscount, 0) AS commentscount, f.commentpendingsince,
	f.customname, f.renderedname, f.customdescription, f.rendereddescription, f.customcve, f.customcwe, f.renderedcwe, f.custombugtraq, f.customsolution, f.renderedsolution, f.customcvssv2vector, f.customcvssv3vector,
	f.customerid, cu.name AS customername,
	f.created, f.updated, f.createdbyid, f.createdby, COALESCE(f.maskedcreatedby, f.createdby) AS maskedcreatedby, f.updatedbyid, f.updatedby, COALESCE(f.maskedupdatedby, f.updatedby) AS maskedupdatedby, f.deleted, f.metadata,
	f.assetid, asset.name AS assetname, asset.enabled AS enabled, f.solutionpatches, f.sentto,
	(CASE WHEN f.findingtemplateid IS NOT NULL THEN COALESCE(f.customname, f.renderedname) ELSE (CASE WHEN f.customname IS NOT NULL THEN f.customname ELSE CASE WHEN p.name IS NOT NULL AND p.normalizerulename THEN p.name || (CASE WHEN position('/' IN p.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END END) END)::TEXT AS name,
	COALESCE(f.customdescription, f.rendereddescription, r.description) AS description,
	COALESCE(f.customcve, NULLIF(r.cve, '')) AS cve,
	COALESCE(f.custombugtraq, NULLIF(r.bugtraq, '')) AS bugtraq, p.producturl, p.name AS softwarecomponent,
	COALESCE(r.hasexploits, false) AS exploitavailable,
	am.matchids, am.ports, am.source,
	(CASE WHEN NOT asset.enabled THEN ARRAY[]::subscriptiontype[]
		ELSE
		(SELECT ARRAY_AGG(type) FROM
			(SELECT UNNEST(activesubscriptiontypes) AS type FROM assets a WHERE a.id = f.assetid
			UNION
			SELECT UNNEST(activesubscriptiontypes) AS type FROM assetgroups ag WHERE deleted IS NULL AND ag.id = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = f.assetid))) s)
		END) AS activesubscriptiontypes,
	(SELECT ARRAY_AGG(id) FROM assetgroups ag WHERE ag.deleted IS NULL AND ag.id = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = f.assetid))) AS assetgroupids,
	date_part('day', (now() - f.created))::INTEGER AS age,
	LEAST(am.firstseen, f.created) AS firstseen,
	am.lastseen, am.firstscanid, am.lastscanid,
	(SELECT array_agg(id) FROM attachments WHERE deleted IS NULL AND findingid = f.id) AS attachmentids,
	(CASE WHEN f.status IS NULL THEN 'PRESENT' WHEN f.status = 'PRESENT' AND f.accepted IS NOT NULL AND f.accepteduntil > NOW() THEN 'ACCEPTED' ELSE f.status END)::findingstatus AS status,
	f.reviewed, f.reviewedby, f.maskedreviewedby, f.reviewedbyid, f.qualityassured, f.qualityassuredby, f.maskedqualityassuredby, f.qualityassuredbyid, f.verified, f.verifiedby, f.maskedverifiedby, f.verifiedbyid, f.published, f.publishedby, f.maskedpublishedby, f.publishedbyid, f.rejected, f.rejectedby, f.rejectedbyid,
	COALESCE(f.customrecreation, f.renderedrecreation) AS recreation, f.customrecreation, f.renderedrecreation, f.alternativerecreation,
	COALESCE(f.customimpact, f.renderedimpact) AS impact, f.customimpact, f.renderedimpact, f.potential,
	COALESCE(f.customowaspmobile2024, f.renderedowaspmobile2024) AS owaspmobile2024, customowaspmobile2024, renderedowaspmobile2024,
	COALESCE(f.customowaspapi2023, f.renderedowaspapi2023) AS owaspapi2023, customowaspapi2023, renderedowaspapi2023,
	(CASE WHEN c.cwe IS NOT NULL THEN 'CWE-' || c.cwe ELSE null END) AS cwe, c.securecodewarrior, c.classifications,
	(c.classifications->>'sans25')::int AS sans25,
	NULLIF(jsonb_array_castint(c.classifications->'capec'), '{}') AS capec,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2004'), '{}') AS owasp2004,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2007'), '{}') AS owasp2007,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2010'), '{}') AS owasp2010,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2013'), '{}') AS owasp2013,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2017'), '{}') AS owasp2017,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2021'), '{}') AS owasp2021,
	r.cyrating, r.cyrating-r.previouscyrating AS cyratingdelta, (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE AS cyratingupdated, (r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen,
	(r.farsight->'risk'->>'score')::NUMERIC AS exploitprobability, (r.farsight->'risk'->>'delta')::NUMERIC AS exploitprobabilitydelta, r.farsight,
	at.tags,
	p.name AS solutionproduct,
	(CASE WHEN r.solutiontype = 0 THEN 'NOT_CLASSIFIED' WHEN r.solutiontype = 1 THEN 'UNKNOWN' WHEN r.solutiontype = 2 THEN 'RECONFIGURE' WHEN r.solutiontype = 3 THEN 'WORKAROUND' WHEN r.solutiontype = 4 THEN 'SOLUTION_IN_PROGRESS'
	WHEN r.solutiontype = 5 THEN 'CONTACT_VENDOR' WHEN r.solutiontype = 6 THEN 'UPDATE' WHEN r.solutiontype = 7 THEN 'PATCH' WHEN r.solutiontype = 8 THEN 'NOT_ACKNOWLEDGED'
	WHEN r.solutiontype = 9 THEN 'NO_SOLUTION' WHEN r.solutiontype = 10 THEN 'CONFIGURE_ACCOUNT' WHEN r.solutiontype = 11 THEN 'DISABLE' WHEN r.solutiontype = 12 THEN 'FILTER'
	WHEN r.solutiontype = 13 THEN 'MALWARE' ELSE NULL END)::solutiontype AS solutiontype,
	(CASE WHEN f.findingtemplateid IS NOT NULL THEN COALESCE(f.customsolution, f.renderedsolution) ELSE (CASE WHEN r.solutiontype = 6 THEN COALESCE(f.customsolution, p.updatesolutiontext, r.solution) WHEN r.solutiontype = 7 THEN 'Apply the latest patches for ' || p.name ELSE COALESCE(f.customsolution, r.solution) END) END) AS solution,
	(CASE WHEN r.solutiontype = 6 THEN COALESCE(p.updatesolutiontitle, r.solutiontitle) WHEN r.solutiontype = 7 THEN 'Apply latest patches for ' || p.name ELSE r.solutiontitle END) AS solutiontitle,
	uuid_in(md5(COALESCE(r.solutiontype, 0) || COALESCE(p.name, '') ||
	COALESCE((CASE WHEN r.solutiontype = 6 THEN COALESCE(f.customsolution, p.updatesolutiontext, r.solution) WHEN r.solutiontype = 7 THEN 'Apply the latest patches for ' || p.name ELSE COALESCE(f.customsolution, r.solution) END), '') ||
	COALESCE((CASE WHEN r.solutiontype = 6 THEN COALESCE(p.updatesolutiontitle, r.solutiontitle) WHEN r.solutiontype = 7 THEN 'Apply latest patches for ' || p.name ELSE r.solutiontitle END), ''))::CSTRING)::TEXT AS solutionuuid,
	CASE WHEN am.isappsec THEN true ELSE COALESCE(am.lastscanid = asset.lastscanid OR EXISTS (SELECT 1 FROM scanlogs sl1 WHERE sl.invocationid IS NOT NULL AND sl1.invocationid = sl.invocationid AND sl1.customerid = sl.customerid AND sl1.id = asset.lastscanid), false) END AS seenlastscan,
	sl.scanconfigurationid AS lastscanconfigurationid,
	false AS watching
	FROM findings f
	LEFT JOIN trules r ON r.ruleid = f.checkid
	LEFT JOIN tproductinformation p ON p.xid = r.productid
	LEFT JOIN classifications c ON COALESCE(f.customcwe, f.renderedcwe, r.cwe) = c.cwe
	LEFT JOIN assets asset ON asset.id = f.assetid
	LEFT JOIN customers cu ON cu.id = f.customerid
	LEFT JOIN aggregatedmatches am ON am.findingid = f.id
	LEFT JOIN aggregatedfindingtags at ON at.findingid = f.id
	LEFT JOIN scanlogs sl ON sl.id = am.lastscanid;

DROP VIEW IF EXISTS compliancefindingsview CASCADE;
CREATE OR REPLACE VIEW compliancefindingsview AS
	SELECT f.id, f.customerid, f.created, f.updated, f.createdbyid, f.createdby, f.updatedbyid, f.updatedby, f.deleted,
	f.falsepositive, f.falsepositivecomment, f.falsepositiveby,
	f.exception, f.exceptioncomment, f.exceptionuntil, f.exceptionby, (CASE WHEN f.exception IS NOT NULL AND (f.exceptionuntil IS NULL OR f.exceptionuntil > NOW()) THEN true ELSE false END) AS isexception,
	f.firstfailed, f.lastfailed, f.assetid, asset.name AS assetname,
	r.id AS requirementid, r.audit, r.description, r.rationale, r.scored, r.namelong, r.nameshort, r.solution, r.impacts, r.notes, r.references, r.controls, r.category, r.requirementid AS requirement,
	r.policyid, p.name AS policyname,
	m.id AS matchid, m.source AS source, COALESCE((m.match->>'humanCheck')::boolean, false) AS humancheck, COALESCE((m.match->>'compliant')::boolean, false) AS iscompliant,
	date_part('day', (now() - f.created))::INTEGER AS age, m.firstseen, m.lastseen, m.firstscanid, m.lastscanid,
	(CASE WHEN (m.match->>'humanCheck')::boolean THEN (CASE WHEN (f.exception IS NOT NULL AND (f.exceptionuntil IS NULL OR f.exceptionuntil > NOW())) THEN 'COMPLIANT_WITH_EXCEPTIONS' ELSE NULL END)
	WHEN (m.match->>'compliant')::boolean THEN 'COMPLIANT' WHEN (f.exception IS NOT NULL AND (f.exceptionuntil IS NULL OR f.exceptionuntil > NOW())) THEN 'COMPLIANT_WITH_EXCEPTIONS' ELSE 'NOT_COMPLIANT' END) AS compliant,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSETGROUP' AS origin WHERE EXISTS(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = f.assetid)
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSET' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = f.customerid AND tg.assetgroupid = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = f.assetid))
		UNION SELECT id, key, value, true AS inherited, 'ASSET' AS origin FROM tags t JOIN tag_asset ta ON t.id = ta.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = f.customerid AND ta.assetid = f.assetid
		UNION SELECT id, key, value, false AS inherited, 'COMPLIANCEFINDING' AS origin FROM tags t JOIN tag_compliancefinding tf ON t.id = tf.tagid WHERE t.deleted IS NULL AND t.customerid = f.customerid AND tf.compliancefindingid = f.id) t) AS tags
	FROM compliancefindings f
	LEFT JOIN compliancerequirements r ON r.id = f.requirementid
	LEFT JOIN compliancepolicies p ON p.id = r.policyid
	LEFT JOIN assets asset ON asset.id = f.assetid
	LEFT JOIN matches m ON m.id = f.matchid;

DROP VIEW IF EXISTS scanconfigurationgroupsview CASCADE;
CREATE OR REPLACE VIEW scanconfigurationgroupsview AS
	SELECT scg.id, scg.name, scg.parentid, scg.oldid, scg.customerid, scg.created, scg.updated, scg.createdby, scg.updatedby, scg.createdbyid, scg.updatedbyid, scg.deleted,
	(SELECT COUNT(*) FROM scanconfigurations WHERE groupid = scg.id AND deleted IS NULL) AS count, pathup, pathdown
	FROM scanconfigurationgroups scg
		LEFT JOIN (
		WITH recursive tree AS (
			SELECT r.id, array[r.id]::INTEGER[] AS pathup, r.customerid FROM scanconfigurationgroups r WHERE (r.parentid IS NULL OR r.parentid = 0)
			UNION ALL
			SELECT c.id, (t.pathup || c.id), t.customerid FROM tree t, scanconfigurationgroups c WHERE c.parentid = t.id
		)
		SELECT *, ARRAY(SELECT id FROM tree WHERE t.id = ANY(pathup)) AS pathdown FROM tree t
	) path ON scg.id = path.id AND path.customerid = scg.customerid;;

DROP VIEW IF EXISTS scanlogsview CASCADE;
CREATE OR REPLACE VIEW scanlogsview AS
	SELECT sl.id, sl.jobid, sl.status, sl.statusdetails, sl.template, sl.started, sl.ended, sl.schema, sl.attacker, sl.scannerid, sl.scanconfigurationid, sl.expectedstart, sl.expectedend, sl.authentication,
	sl.latestruledate, sl.scanless, sl.parentid, sl.scheduleid, sl.workflowid, sl.invocationid, sl.targets, sl.virtualhosts, sc.name AS scanconfigurationname, w.name AS workflowname,
	(CASE WHEN (sl.status = 'FINISHED' OR sl.status = 'ISSUES') AND sl.template = 'SCALE' AND sl.schema IS NOT NULL AND (now() - '30 days'::INTERVAL) < sl.ended AND (((SELECT COUNT(*) FROM hiabinfo) = 0 AND sl.scannerid > 999) OR ((SELECT COUNT(*) FROM hiabinfo) > 0 AND sl.scannerid != 2)) THEN true ELSE false END) AS blueprintavailable,
	COALESCE(ts.name, 'Local') || COALESCE(NULLIF(' - ' || ts.hwaddr, ' - '), '') AS scannername,
	sl.customerid, sl.created, sl.updated, sl.createdby, sl.updatedby, sl.createdbyid, sl.updatedbyid, sl.deleted,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'SCANCONFIGURATION' AS origin
		UNION SELECT id, key, value, true AS inherited, 'SCANCONFIGURATION' AS origin FROM tags t JOIN tag_scanconfiguration ts ON t.id = ts.tagid WHERE t.deleted IS NULL AND t.customerid = sl.customerid AND ts.scanconfigurationid = sl.scanconfigurationid
		UNION SELECT id, key, value, true AS inherited, 'SCANCONFIGURATION' AS origin FROM tags t JOIN tag_workflow tw ON t.id = tw.tagid WHERE t.deleted IS NULL AND t.customerid = sl.customerid AND tw.workflowid = sl.workflowid
	) t) AS tags,
	ai.id AS assetidentifierid, ai.name AS assetidentifiername, ai.type AS assetidentifiertype, a.id AS assetid, a.name AS assetname
	FROM scanlogs sl
	LEFT JOIN scanconfigurations sc ON sl.scanconfigurationid = sc.id
	LEFT JOIN workflows w ON sl.workflowid = w.id
	LEFT JOIN assetidentifiers ai ON sl.assetidentifierid = ai.id
	LEFT JOIN asset_scanlog asl ON sl.id = asl.scanlogid
	LEFT JOIN assets a ON COALESCE(asl.assetid, sl.assetid) = a.id
	LEFT JOIN tscanners ts ON sl.scannerid = ts.xid AND ts.deleted = 0;

DROP VIEW IF EXISTS termsview CASCADE;
CREATE OR REPLACE VIEW termsview AS
	SELECT t.id, t.mainusermustaccept, t.allusersmustaccept, t.termstemplateid, tt.name AS termstemplatename, tt.content AS termstemplatecontent, c.name AS customername,
	t.customerid, t.created, t.updated, t.createdbyid, t.createdby, t.updatedbyid, t.updatedby, t.deleted
	FROM terms t
	LEFT JOIN termstemplates tt ON tt.id = t.termstemplateid
	LEFT JOIN customers c ON c.id = t.customerid;

DROP VIEW IF EXISTS consumptionstatsview CASCADE;
CREATE OR REPLACE VIEW consumptionstatsview AS
	SELECT s.id, s.customerid, s.created, s.updated, s.createdbyid, s.createdby, s.updatedbyid, s.updatedby, s.deleted, s.periodstart, s.periodend, s.applianceperiodend,
	c.name AS customername, (SELECT vccompany FROM tusers WHERE xid = (SELECT xiparentid FROM taorganizations WHERE xid = c.organizationid)) AS organization,
	(SELECT salesaccountid FROM tusers WHERE xid = c.userid) AS salesaccountid,
	s.netsecassets, s.netsecscans, s.netsecinternalassets, s.netsecinternalscans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.netsecapplianceassets END) AS netsecapplianceassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.netsecappliancescans END) AS netsecappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.netsecapplianceexternalassets END) AS netsecapplianceexternalassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.netsecapplianceexternalscans END) AS netsecapplianceexternalscans,
	s.complianceassets, s.compliancescans, s.complianceinternalassets, s.complianceinternalscans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.complianceapplianceassets END) AS complianceapplianceassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.complianceappliancescans END) AS complianceappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.complianceapplianceexternalassets END) AS complianceapplianceexternalassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.complianceapplianceexternalscans END) AS complianceapplianceexternalscans,
	s.pciassets, s.pciscans, s.cloudsecassets, s.cloudsecscans, s.scaleassets, s.scalescans, s.scaleinternalassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.scaleapplianceassets END) AS scaleapplianceassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.scaleappliancescans END) AS scaleappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.scaleapplianceexternalassets END) AS scaleapplianceexternalassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.scaleapplianceexternalscans END) AS scaleapplianceexternalscans,
	s.networkdiscoveryscans, s.clouddiscoveryscans, s.networkassets, s.networkscans, s.dockerimagediscoveryscans, s.dockerimageassets, s.dockerimagescans, s.outscannxassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.networkdiscoveryappliancescans END) AS networkdiscoveryappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.clouddiscoveryappliancescans END) AS clouddiscoveryappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.networkapplianceassets END) AS networkapplianceassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.networkappliancescans END) AS networkappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.dockerimagediscoveryappliancescans END) AS dockerimagediscoveryappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.dockerimageapplianceassets END) AS dockerimageapplianceassets,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.dockerimageappliancescans END) AS dockerimageappliancescans,
	(CASE WHEN s.applianceperiodend IS NULL THEN NULL ELSE s.outscannxapplianceassets END) AS outscannxapplianceassets
	FROM consumptionstats s
	LEFT JOIN customers c ON c.id = s.customerid;

DROP VIEW IF EXISTS usagestatsview CASCADE;
CREATE OR REPLACE VIEW usagestatsview AS
	SELECT s.id, s.customerid, s.created, s.updated, s.createdbyid, s.createdby, s.updatedbyid, s.updatedby, s.deleted, s.statsupdated, s.appliancestatsupdated,
	c.name AS customername, (SELECT vccompany FROM tusers WHERE xid = (SELECT xiparentid FROM taorganizations WHERE xid = c.organizationid)) AS organization,
	(SELECT salesaccountid FROM tusers WHERE xid = c.userid) AS salesaccountid,
	s.scaleconfigurations, s.scoutconfigurations, s.cloudsecconfigurations,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancescaleconfigurations END) AS appliancescaleconfigurations,
	s.users, s.lastlogin,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.applianceusers END) AS applianceusers,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancelastlogin END) AS appliancelastlogin,
	s.findingscritical, s.findingshigh, s.findingsmedium, s.findingslow, s.findingsrecommendations,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancefindingscritical END) AS appliancefindingscritical,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancefindingshigh END) AS appliancefindingshigh,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancefindingsmedium END) AS appliancefindingsmedium,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancefindingslow END) AS appliancefindingslow,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancefindingsrecommendations END) AS appliancefindingsrecommendations,
	s.scans, s.lastscan, (CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancescans END) AS appliancescans,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.appliancelastscan END) AS appliancelastscan,
	s.accountsaws, s.accountsgcp, s.accountsazure, s.accountsvsphere, s.accountsbasic, s.accountsweb,
	s.applianceaccountsaws, s.applianceaccountsgcp, s.applianceaccountsazure, s.applianceaccountsvsphere, s.applianceaccountsbasic, s.applianceaccountsweb,
	s.cyr3conassets, s.cyr3conapplianceassets, s.agents, s.agentsscanned, s.agentsoutdated,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.applianceagents END) AS applianceagents,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.applianceagentsscanned END) AS applianceagentsscanned,
	(CASE WHEN s.appliancestatsupdated IS NULL THEN NULL ELSE s.applianceagentsoutdated END) AS applianceagentsoutdated
	FROM usagestats s
	LEFT JOIN customers c ON c.id = s.customerid;

DROP VIEW IF EXISTS tagsview CASCADE;
CREATE OR REPLACE VIEW tagsview AS
	SELECT t.id, t.key, t.value, false AS inherited, NULL AS count,
	t.customerid, t.created, t.updated, t.createdby, t.updatedby, t.createdbyid, t.updatedbyid, t.deleted, t.migration
	FROM tags t;

DROP VIEW IF EXISTS tagstatisticsview CASCADE;
CREATE OR REPLACE VIEW tagstatisticsview AS
	SELECT t.id, t.key, t.value,
	t.customerid, t.created, t.updated, t.createdby, t.updatedby, t.createdbyid, t.updatedbyid, t.deleted, t.migration,
	(SELECT json_build_object('accounts', accounts, 'assetGroups', assetgroups, 'assets', assets, 'assetIdentifiers', assetidentifiers, 'eventSubscriptions', eventsubscriptions, 'managedReports', managedreports,
	'scheduledReports', scheduledreports, 'complianceFindings', compliancefindings, 'findings', findings, 'information', information, 'scanConfigurations', scanconfigurations, 'workflows', workflows,
	'dashboards', dashboards, 'integrations', integrations, 'scanPolicies', scanpolicies, 'viewTemplates', viewtemplates, 'users', users,
	'scanConfigurationSettings', scanconfigurationsettings, 'scheduledReportSettings', scheduledreportsettings, 'resourceGroupSettings', resourcegroupsettings)::jsonb FROM (SELECT
	(SELECT COUNT(id) AS accounts FROM accounts WHERE id IN (SELECT accountid FROM tag_account WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS assetgroups FROM assetgroups WHERE id IN
		((SELECT assetgroupid FROM tag_assetgroup WHERE tagid = t.id) UNION (SELECT assetgroupid FROM assetgroup_includetag WHERE tagid = t.id) UNION (SELECT assetgroupid FROM assetgroup_excludetag WHERE tagid = t.id))
		AND deleted IS NULL AND migration IS NULL),
	(SELECT COUNT(id) AS assets FROM assets WHERE id IN (SELECT assetid FROM tag_asset WHERE tagid = t.id) AND deleted IS NULL AND migration IS NULL),
	(SELECT COUNT(id) AS assetidentifiers FROM assetidentifiers WHERE id IN (SELECT assetidentifierid FROM tag_assetidentifier WHERE tagid = t.id) AND deleted IS NULL AND migration IS NULL),
	(SELECT count(id) AS eventsubscriptions FROM eventsubscriptions WHERE id IN (SELECT eventsubscriptionid FROM tag_eventsubscription WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS managedreports FROM managedreports WHERE id IN (SELECT managedreportid FROM tag_managedreport WHERE tagid = t.id) AND deleted IS NULL AND migration IS NULL),
	(SELECT COUNT(id) AS scheduledreports FROM scheduledreports WHERE id IN (SELECT scheduledreportid FROM tag_scheduledreport WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS compliancefindings FROM compliancefindings WHERE id IN (SELECT compliancefindingid FROM tag_compliancefinding WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS findings FROM findings WHERE id IN (SELECT findingid FROM tag_finding WHERE tagid = t.id) AND deleted IS NULL AND migration IS NULL),
	(SELECT COUNT(id) AS information FROM information WHERE id IN (SELECT informationid FROM tag_information WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS scanconfigurations FROM scanconfigurations WHERE id IN (SELECT scanconfigurationid FROM tag_scanconfiguration WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS workflows FROM workflows WHERE id IN (SELECT workflowid FROM tag_workflow WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS dashboards FROM dashboards WHERE id IN (SELECT dashboardid FROM tag_dashboard WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS integrations FROM integrations WHERE id IN (SELECT integrationid FROM tag_integration WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS scanpolicies FROM scanpolicies WHERE id IN (SELECT scanpolicyid FROM tag_scanpolicy WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(id) AS viewtemplates FROM viewtemplates WHERE id IN (SELECT viewtemplateid FROM tag_viewtemplate WHERE tagid = t.id) AND deleted IS NULL),
	(SELECT COUNT(userid) AS users FROM tag_user WHERE tagid = t.id),
	(SELECT COUNT(id) AS scanconfigurationsettings FROM scanconfigurations sc WHERE sc.customerid = t.customerid AND deleted IS NULL AND (
		(template = ANY(ARRAY['CLOUD_DISCOVERY','NETWORK_DISCOVERY']::scantemplate[]) AND ((configuration->>'addTagIds')::jsonb @> t.id::text::jsonb OR (configuration->>'replaceTagIds')::jsonb @> t.id::text::jsonb)) OR
		(template = ANY(ARRAY['NETWORK_SCAN', 'AGENT_SCAN']::scantemplate[]) AND (configuration->>'assetTagIds')::jsonb @> t.id::text::jsonb)
	)),
	(SELECT COUNT(id) AS scheduledreportsettings FROM scheduledreports sr WHERE sr.customerid = t.customerid AND deleted IS NULL AND (scope->>'tagIds')::jsonb @> t.id::text::jsonb),
	(SELECT COUNT(id) AS resourcegroupsettings FROM (SELECT id, jsonb_array_elements(resources)->>'tagIds' AS tagids FROM resourcegroups rg WHERE rg.customerid = t.customerid AND deleted IS NULL) x WHERE tagids::jsonb @> t.id::text::jsonb)
	) s) AS statistics
	FROM tags t;

DROP VIEW IF EXISTS appliancesview CASCADE;
CREATE OR REPLACE VIEW appliancesview AS
	SELECT a.id, a.primary, a.virtual, a.revoked, a.mac, a.updatedonline, a.updatedoffline, a.versions, a.publicsbc, a.uuid, a.hiabid, c.name AS customername,
	a.customerid, a.created, a.updated, a.createdby, a.updatedby, a.createdbyid, a.updatedbyid, a.deleted
	FROM appliances a
	LEFT JOIN customers c ON c.id = a.customerid;

DROP VIEW IF EXISTS scheduledreportsview CASCADE;
CREATE OR REPLACE VIEW scheduledreportsview AS
	SELECT id, name, enabled, deliverymethod, scope,
	(CASE WHEN report ->> 'viewTemplateId' IS NULL THEN report ELSE report || COALESCE(jsonb_build_object('viewTemplateName', (SELECT name FROM viewtemplates WHERE id = (report ->> 'viewTemplateId')::INTEGER)), '{}'::jsonb)::jsonb END) AS report,
	lastsent, workflowid, customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted,
	ARRAY(SELECT g.id FROM genericschedules g, scheduledreport_genericschedule l WHERE l.scheduledreportid = s.id AND l.genericscheduleid = g.id AND g.deleted IS NULL) AS scheduleids,
	(SELECT MIN(g.nextoccurrence) FROM genericschedules g, scheduledreport_genericschedule l WHERE l.scheduledreportid = s.id AND l.genericscheduleid = g.id AND g.deleted IS NULL AND (g.remainingoccurrences IS NULL OR g.remainingoccurrences > 0) AND (until IS NULL OR until > now())) AS nextoccurrence,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM (
		SELECT 0 AS id, NULL AS key, NULL AS value, false AS inherited, 'SCHEDULEDREPORT' AS origin
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'SCANCONFIGURATION' AS origin WHERE s.workflowid IS NOT NULL
		UNION SELECT id, key, value, false AS inherited, 'SCHEDULEDREPORT' AS origin
		FROM tags t JOIN tag_scheduledreport ts ON t.id = ts.tagid WHERE t.deleted IS NULL AND t.customerid = s.customerid AND ts.scheduledreportid = s.id
		UNION SELECT id, key, value, true AS inherited, 'SCANCONFIGURATION' AS origin
		FROM tags t JOIN tag_workflow tw ON t.id = tw.tagid WHERE t.deleted IS NULL AND t.customerid = s.customerid AND tw.workflowid = s.workflowid
	) t) AS tags
	FROM scheduledreports s;

DROP VIEW IF EXISTS managedreportsview CASCADE;
CREATE OR REPLACE VIEW managedreportsview AS
	SELECT id, name, size, lastdownloaded, lastdownloadedby, uuid::TEXT, migration,
	customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted,
	(SELECT jsonb_build_object('id', 0, 'origin', 'MANAGEDREPORT') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'MANAGEDREPORT')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = m.customerid AND t.id IN (SELECT tagid FROM tag_managedreport WHERE managedreportid = m.id)) AS tags
	FROM managedreports m;

DROP VIEW IF EXISTS dashboardsview CASCADE;
CREATE OR REPLACE VIEW dashboardsview AS
	SELECT id, name, cards,
	customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted,
	(SELECT jsonb_build_object('id', 0, 'origin', 'DASHBOARD') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'DASHBOARD')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = d.customerid AND t.id IN (SELECT tagid FROM tag_dashboard WHERE dashboardid = d.id)) AS tags
	FROM dashboards d;

DROP VIEW IF EXISTS integrationsview CASCADE;
CREATE OR REPLACE VIEW integrationsview AS
	SELECT id, name, type, configuration,
	customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted, verified, verifiedbyid, verifiedby, verifystatus,
	(SELECT jsonb_build_object('id', 0, 'origin', 'INTEGRATION') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'INTEGRATION')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = i.customerid AND t.id IN (SELECT tagid FROM tag_integration WHERE integrationid = i.id)) AS tags
	FROM integrations i;

DROP VIEW IF EXISTS assetsview CASCADE;
CREATE OR REPLACE VIEW assetsview AS
	SELECT a.id, a.name, a.source, a.uuid, a.migration,
	a.customerid, c.name AS customername,
	a.created, a.updated, a.createdbyid, a.createdby, COALESCE(a.maskedcreatedby, a.createdby) AS maskedcreatedby, a.updatedbyid, a.updatedby, COALESCE(a.maskedupdatedby, a.updatedby) AS maskedupdatedby, a.deleted, a.firstscanid, a.lastscanid, a.cvssv2environmentalvector, a.cvssv3environmentalvector, a.enabled,
	(a.enabled AND (NOT a.source && ARRAY['OFFSEC','APPSEC']::source[] OR array_length(s.type, 1) IS NOT NULL)) AS active,
	(SELECT array_agg(ai.id) FROM assetidentifiers ai, asset_assetidentifier l WHERE l.assetid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL) AS assetidentifierids,
	(SELECT array_agg(DISTINCT ai.type) FROM assetidentifiers ai, asset_assetidentifier l WHERE l.assetid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL) AS assetidentifiertypes,
	(SELECT platform FROM assetidentifiers ai, asset_assetidentifier l WHERE l.assetid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL AND platform IS NOT NULL ORDER BY lastscanid DESC NULLS LAST LIMIT 1) AS platform,
	(SELECT properties FROM assetidentifiers ai, asset_assetidentifier l WHERE l.assetid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL AND ai.type = 'DOCKER_IMAGE' LIMIT 1) AS dockerimageproperties,
	(SELECT json_agg(json_build_object('id', g.id, 'name', g.name, 'dynamic', g.dynamic))::jsonb FROM assetgroups g WHERE g.deleted IS NULL AND g.customerid = a.customerid AND g.id = ANY(SELECT assetgroupid FROM assetgroup_asset l WHERE l.assetid = a.id)) AS assetgroups,
	(CASE WHEN a.enabled THEN s.type ELSE ARRAY[]::subscriptiontype[] END) AS activesubscriptiontypes,
	(SELECT authentication FROM scanlogs s WHERE s.id = a.lastscanid) AS authenticationlatestscan,
	(SELECT json_agg(json_build_object(
		'id', ai.id,
		'type', ai.type,
		'name', ai.name,
		'presentableName', ai.presentablename,
		'firstSeen', ai.firstseen,
		'lastSeen', ai.lastseen,
		'linkId', l.id,
		'scanSelectionType', l.scanselectiontype,
		'scannerId', ai.scannerid,
		'scannerName', COALESCE((SELECT name FROM tscanners WHERE xid = ai.scannerid), 'Local')
	))::jsonb FROM assetidentifiers ai, asset_assetidentifier l WHERE l.assetid = a.id AND l.assetidentifierid = ai.id AND ai.deleted IS NULL) AS assetidentifiers,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSETGROUP' AS origin WHERE EXISTS(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = a.id)
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, false AS inherited, 'ASSET' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = a.customerid AND tg.assetgroupid = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = a.id))
		UNION SELECT id, key, value, false AS inherited, 'ASSET' AS origin FROM tags t JOIN tag_asset ta ON t.id = ta.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = a.customerid AND ta.assetid = a.id) t) AS tags,
	(CASE WHEN 'CLOUDSEC' = ANY(a.source)
		THEN (SELECT MAX(CASE
			WHEN ((m.match->>'compliant')::boolean AND NOT (m.match->>'humanCheck')::boolean) THEN 'COMPLIANT'
			WHEN (f.exception IS NOT NULL AND (f.exceptionuntil IS NULL OR f.exceptionuntil > NOW())) THEN 'COMPLIANT_WITH_EXCEPTIONS'
			ELSE 'NOT_COMPLIANT' END)
			FROM compliancefindings f LEFT JOIN matches m ON m.id = f.matchid WHERE f.customerid = a.customerid AND f.assetid = a.id AND f.deleted IS NULL AND m.deleted IS NULL)
		END) AS compliant
	FROM assets a
	LEFT JOIN customers c ON c.id = a.customerid
	LEFT JOIN LATERAL
		(SELECT ARRAY_AGG("type") AS "type" FROM (SELECT UNNEST(activesubscriptiontypes) AS "type" FROM assets a2 WHERE a2.id = a.id
			UNION
			SELECT UNNEST(activesubscriptiontypes) AS "type" FROM assetgroups ag WHERE ag.id = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = a.id))) t
		) s ON TRUE;

DROP VIEW IF EXISTS assetgroupsview CASCADE;
CREATE OR REPLACE VIEW assetgroupsview AS
	SELECT ag.id, ag.name, ag.type, ag.parentid, ag.managed, ag.dynamic, ag.migration, ag.labels, ag.contactname, ag.contactdetails, ag.additionaldetails,
	ag.customerid, c.name AS customername, ag.created, ag.updated, ag.createdbyid, ag.createdby, COALESCE(ag.maskedcreatedby, ag.createdby) AS maskedcreatedby, ag.updatedbyid, ag.updatedby, COALESCE(ag.maskedupdatedby, ag.updatedby) AS maskedupdatedby, ag.deleted, ag.path,
	(SELECT array_agg(a.id) FROM assets a, assetgroup_asset l WHERE l.assetgroupid = ag.id AND l.assetid = a.id AND a.deleted IS NULL) AS assetids,
	(SELECT array_agg(a.id) FROM assetgroups a WHERE a.parentid = ag.id AND a.deleted IS NULL) AS assetgroupids,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, false AS inherited, 'ASSETGROUP' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ANY((array_remove(ag.path, ag.id))::INTEGER[])
		UNION SELECT id, key, value, false AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ag.id) t) AS tags,
	(CASE WHEN ag.dynamic THEN (SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited))::jsonb FROM
		(SELECT id, key, value, true AS inherited FROM tags t JOIN assetgroup_includetag tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ANY((array_remove(ag.path, ag.id))::INTEGER[])
		UNION SELECT id, key, value, false AS inherited FROM tags t JOIN assetgroup_includetag tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ag.id) t) ELSE NULL END) AS includeddynamictags,
	(CASE WHEN ag.dynamic THEN (SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited))::jsonb FROM
		(SELECT id, key, value, true AS inherited FROM tags t JOIN assetgroup_excludetag tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ANY((array_remove(ag.path, ag.id))::INTEGER[])
		UNION SELECT id, key, value, false AS inherited FROM tags t JOIN assetgroup_excludetag tg ON t.id = tg.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = ag.customerid AND tg.assetgroupid = ag.id) t) ELSE NULL END) AS excludeddynamictags,
	ss.summaryid, ss.summary, ss.summarypublishedat,
	(SELECT ARRAY_AGG(type) FROM (SELECT DISTINCT UNNEST(activesubscriptiontypes) AS type FROM assetgroups ag2 WHERE ag2.id = ANY(ag.path)) s) AS activesubscriptiontypes,
	(SELECT MIN(pendingsince) FROM commentsview WHERE entitytype = 'ASSET_GROUP' AND entityid = ag.id AND parentid IS NULL AND deleted IS NULL) AS commentpendingsince
	FROM assetgroups ag LEFT JOIN customers c ON ag.customerid = c.id
	LEFT JOIN LATERAL
		(SELECT s.id AS summaryid, s.assetgroupid, s.content AS summary, s.published AS summarypublishedat FROM summaries s INNER JOIN (SELECT id, array_position(ag.path, id) AS depth FROM assetgroups WHERE id = ANY(ag.path)) t ON s.assetgroupid = t.id
			WHERE s.deleted IS NULL AND s.status = 'PUBLISHED'::summarystatus ORDER BY (array_length(ag.path, 1) - t.depth), published DESC NULLS LAST LIMIT 1) ss ON TRUE;

DROP VIEW IF EXISTS exploitsview CASCADE;
CREATE OR REPLACE VIEW exploitsview AS
	SELECT id, name,
	(CASE WHEN type = 1 THEN 'CORE' WHEN type = 2 THEN 'IMMUNITY' WHEN type = 3 THEN 'EXPLOIT_DB' WHEN type = 4 THEN 'D_SQUARE' WHEN type = 5 THEN 'CONTAGIO' WHEN type = 6 THEN 'METASPLOIT'
	WHEN type = 7 THEN 'SAINT' WHEN type = 8 THEN 'SECURITY_FOCUS' WHEN type = 9 THEN 'SNORT' WHEN type = 10 THEN 'FARSIGHT' ELSE 'UNKNOWN' END)::exploitsource AS source,
	type, cve, created, identifier, pack, scriptid, approved
	FROM texploits;

DROP VIEW IF EXISTS eventsubscriptionsview CASCADE;
CREATE OR REPLACE VIEW eventsubscriptionsview AS
	SELECT id, name, trigger, viewtemplateid, integrationid, integrationtype, enabled, settings, contentconfiguration, lasttriggered, hidden, customerid, created, updated, createdbyid, createdby, updatedbyid, updatedby, deleted,
	(SELECT jsonb_build_object('id', 0, 'origin', 'EVENTSUBSCRIPTION') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'EVENTSUBSCRIPTION')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = e.customerid AND t.id IN (SELECT tagid FROM tag_eventsubscription WHERE eventsubscriptionid = e.id)) AS tags
	FROM eventsubscriptions e;

DROP VIEW IF EXISTS checksview CASCADE;
CREATE OR REPLACE VIEW checksview AS
	SELECT r.ruleid::INTEGER AS id, (CASE WHEN r.cve IS NULL OR r.cve = '' THEN 'No CVE' ELSE r.cve END) AS cve, (CASE WHEN bugtraq IS NULL OR bugtraq = '' THEN 'No bugtraq' ELSE bugtraq END) AS bugtraq,
	(CASE WHEN pi.name IS NOT NULL AND pi.normalizerulename THEN pi.name || (CASE WHEN position('/' IN pi.product) <> 0 THEN ': ' ELSE ' ' END) || r.name ELSE r.name END)::VARCHAR(256) AS name,
	r.cvssscore AS nvdcvssv2score, r.cvssv3score AS nvdcvssv3score, r.cvssvector::VARCHAR(64) AS nvdcvssv2vector, r.cvssv3vector AS nvdcvssv3vector,
	(CASE WHEN cvssv3score IS NULL THEN NULL WHEN cvssv3score >= 9.0 THEN 'CRITICAL' WHEN cvssv3score >= 7.0 THEN 'HIGH' WHEN cvssv3score >= 4.0 THEN 'MEDIUM' WHEN cvssv3score >= 0.1 THEN 'LOW' ELSE 'RECOMMENDATION' END)::severity AS cvssv3severity,
	COALESCE(r.hasexploits, false) AS hasexploits, r.created, r.updated, r.description, r.deleted AS external,
	r.cyrating, r.previouscyrating, r.cyrating-r.previouscyrating AS cyratingdelta, (r.farsight->>'updated')::TIMESTAMP WITHOUT TIME ZONE AS cyratingupdated,
	(r.farsight->>'lastThreatActivity')::TIMESTAMP WITHOUT TIME ZONE AS cyratinglastseen,
	(r.farsight->'risk'->>'score')::NUMERIC AS exploitprobability, (r.farsight->'risk'->>'score')::NUMERIC - (r.farsight->'risk'->>'delta')::NUMERIC AS previousexploitprobability,
	(r.farsight->'risk'->>'delta')::NUMERIC AS exploitprobabilitydelta, r.farsight, pi.name::VARCHAR(255) AS softwarecomponent,
	NULLIF(jsonb_array_castint(c.classifications->'owasp2017'), '{}') AS owasp2017, c.classifications,
	pi.name AS solutionproduct,
	(CASE WHEN r.solutiontype = 0 THEN 'NOT_CLASSIFIED' WHEN r.solutiontype = 1 THEN 'UNKNOWN' WHEN r.solutiontype = 2 THEN 'RECONFIGURE' WHEN r.solutiontype = 3 THEN 'WORKAROUND' WHEN r.solutiontype = 4 THEN 'SOLUTION_IN_PROGRESS'
	WHEN r.solutiontype = 5 THEN 'CONTACT_VENDOR' WHEN r.solutiontype = 6 THEN 'UPDATE' WHEN r.solutiontype = 7 THEN 'PATCH' WHEN r.solutiontype = 8 THEN 'NOT_ACKNOWLEDGED'
	WHEN r.solutiontype = 9 THEN 'NO_SOLUTION' WHEN r.solutiontype = 10 THEN 'CONFIGURE_ACCOUNT' WHEN r.solutiontype = 11 THEN 'DISABLE' WHEN r.solutiontype = 12 THEN 'FILTER'
	WHEN r.solutiontype = 13 THEN 'MALWARE' ELSE NULL END)::solutiontype AS solutiontype,
	(CASE WHEN r.solutiontype = 6 THEN COALESCE(pi.updatesolutiontext, r.solution) WHEN r.solutiontype = 7 THEN 'Apply the latest patches for ' || pi.name ELSE r.solution END) AS solution,
	(CASE WHEN r.solutiontype = 6 THEN COALESCE(pi.updatesolutiontitle, r.solutiontitle) WHEN r.solutiontype = 7 THEN 'Apply latest patches for ' || pi.name ELSE r.solutiontitle END) AS solutiontitle,
	uuid_in(md5(COALESCE(r.solutiontype, 0) || COALESCE(pi.name, '') ||
	COALESCE((CASE WHEN r.solutiontype = 6 THEN COALESCE(pi.updatesolutiontext, r.solution) WHEN r.solutiontype = 7 THEN 'Apply the latest patches for ' || pi.name ELSE r.solution END), '') ||
	COALESCE((CASE WHEN r.solutiontype = 6 THEN COALESCE(pi.updatesolutiontitle, r.solutiontitle) WHEN r.solutiontype = 7 THEN 'Apply latest patches for ' || pi.name ELSE r.solutiontitle END), ''))::CSTRING)::TEXT AS solutionuuid
	FROM trules r
	LEFT JOIN tproductinformation pi ON pi.xid = r.productid
	LEFT JOIN classifications c ON r.cwe = c.cwe;

DROP VIEW IF EXISTS productinformationview CASCADE;
CREATE OR REPLACE VIEW productinformationview AS
	SELECT xid::INTEGER AS id, name, product, updated,
	(CASE WHEN length(producturl) > 0 THEN producturl ELSE NULL END) AS url,
	(CASE WHEN nvdtags IS NULL OR nvdtags = '{}' THEN NULL ELSE 'cpe:/a:' || replace(nvdtags[1], '§', ':') || ':-' END) AS cpev2_2,
	(CASE WHEN nvdtags IS NULL OR nvdtags = '{}' THEN NULL ELSE 'cpe:2.3:a:' || replace(nvdtags[1], '§', ':') || ':-:*:*:*:*:*:*:*' END) AS cpev2_3,
	(CASE WHEN productendoflife IS NOT NULL THEN (SELECT json_agg(x)::JSONB FROM jsonb_build_object('date', productendoflife::TIMESTAMP WITHOUT TIME ZONE) AS x)
		WHEN versionendoflife IS NULL OR versionendoflife = '{}' THEN NULL
		ELSE (SELECT json_agg(x)::JSONB FROM (SELECT CASE WHEN NOT is_timestamp(split_part(versioneol, '§', 2)) THEN NULL
			ELSE jsonb_build_object('version', split_part(versioneol, '§', 1), 'date', split_part(versioneol, '§', 2)::TIMESTAMP WITHOUT TIME ZONE) END AS x FROM unnest(versionendoflife) AS versioneol) xx WHERE x IS NOT NULL) END
	) AS eol
	FROM tproductinformation;

DROP VIEW IF EXISTS viewtemplategroupsview CASCADE;
CREATE OR REPLACE view viewtemplategroupsview AS
	SELECT g.*, pathup, pathdown FROM viewtemplategroups g
	LEFT JOIN (
		WITH recursive tree AS (
				SELECT r.id, array[r.id]::INTEGER[] AS pathup, r.customerid FROM viewtemplategroups r WHERE (r.parentid IS NULL OR r.parentid = 0)
				UNION ALL
				SELECT c.id, (t.pathup || c.id), t.customerid FROM tree t, viewtemplategroups c WHERE c.parentid = t.id
		)
		SELECT *, ARRAY(SELECT id FROM tree WHERE t.id = ANY(pathup)) AS pathdown FROM tree t
	) path
	ON g.id = path.id AND path.customerid = g.customerid;

DROP VIEW IF EXISTS workflowsview CASCADE;
CREATE OR REPLACE VIEW workflowsview AS
	SELECT w.id, w.name, w.configurations, w.enabled, w.scannerid, w.settings,
	COALESCE(ts.name, 'Local') || COALESCE(NULLIF(' - ' || ts.hwaddr, ' - '), '') AS scannername,
	ARRAY(SELECT g.id FROM genericschedules g, workflow_genericschedule l WHERE l.workflowid = w.id AND l.genericscheduleid = g.id AND g.deleted IS NULL) AS scheduleids,
	(SELECT MIN(g.nextoccurrence) FROM genericschedules g, workflow_genericschedule l WHERE l.workflowid = w.id AND l.genericscheduleid = g.id AND g.deleted IS NULL
	AND (g.remainingoccurrences IS NULL OR g.remainingoccurrences > 0) AND (until IS NULL OR until > now())) AS nextoccurrence,
	(SELECT l.ended FROM scanlogs l WHERE l.workflowid = w.id AND template = 'WORKFLOW' AND l.ended IS NOT NULL AND l.deleted IS NULL ORDER BY id DESC LIMIT 1) AS lastscan,
	w.customerid, w.created, w.updated, w.createdbyid, w.createdby, w.updatedbyid, w.updatedby, w.deleted,
	(SELECT jsonb_build_object('id', 0, 'origin', 'SCANCONFIGURATION') || COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'SCANCONFIGURATION')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = w.customerid AND t.id IN (SELECT tagid FROM tag_workflow WHERE workflowid = w.id)) AS tags
	FROM workflows w
	LEFT JOIN tscanners ts ON ts.xid = w.scannerid AND ts.deleted = 0;

DROP VIEW IF EXISTS findingtemplatesview CASCADE;
CREATE OR REPLACE VIEW findingtemplatesview AS
	SELECT f.id, f.template, f.cvssv2score, f.cvssv3score, f.uuid, f.groupid, f.cvssv2severity, f.cvssv3severity,
	(SELECT COALESCE(json_agg(json_build_object('id', id, 'key', key, 'value', value, 'origin', 'FINDINGTEMPLATE')), '[]'::json)::jsonb FROM tags t
		WHERE t.deleted IS NULL AND t.customerid = f.customerid AND t.id IN (SELECT tagid FROM tag_findingtemplate WHERE findingtemplateid = f.id)) AS tags,
	f.customerid, f.created, f.updated, f.createdby, f.updatedby, f.createdbyid, f.updatedbyid, f.deleted
	FROM findingtemplates f;

DROP VIEW IF EXISTS findingtemplategroupsview CASCADE;
CREATE OR REPLACE VIEW findingtemplategroupsview AS
	SELECT fg.id, fg.name, fg.parentid, pathup, pathdown, fg.customerid, fg.created, fg.updated, fg.createdby, fg.updatedby, fg.createdbyid, fg.updatedbyid, fg.deleted FROM findingtemplategroups fg
	LEFT JOIN (
		WITH RECURSIVE tree AS (
			SELECT r.id, array[r.id]::INTEGER[] AS pathup, r.customerid FROM findingtemplategroups r WHERE (r.parentid IS NULL OR r.parentid = 0)
			UNION ALL
			SELECT c.id, (t.pathup || c.id), t.customerid FROM tree t, findingtemplategroups c WHERE c.parentid = t.id
		)
		SELECT *, ARRAY(SELECT id FROM tree WHERE t.id = ANY(pathup)) AS pathdown FROM tree t
	) path
	ON fg.id = path.id AND path.customerid = fg.customerid;

DROP VIEW IF EXISTS informationview CASCADE;
CREATE OR REPLACE VIEW informationview AS
	SELECT i.id, i.type, i.falsepositive, i.falsepositivecomment, COALESCE(i.commentscount, 0) AS commentscount, i.status,
	i.customerid, i.created, i.updated, i.createdbyid, i.createdby, i.updatedbyid, i.updatedby, i.deleted,
	i.assetid, a.name AS assetname,
	(SELECT ARRAY_AGG(id) FROM assetgroups ag WHERE ag.deleted IS NULL AND ag.id = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = i.assetid))) AS assetgroupids,
	date_part('day', (now() - i.created))::INTEGER AS age,
	(SELECT array_agg(id) FROM matches WHERE deleted IS NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id)) AS matchids,
	(SELECT json_agg(match->'service') FROM matches WHERE deleted IS NULL AND match IS NOT NULL AND match->'service' IS NOT NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id))::JSONB AS ports,
	(SELECT array_distinct(array_agg_flat(source)) FROM matches WHERE deleted IS NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id)) AS source,
	LEAST((SELECT firstseen FROM matches WHERE deleted IS NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id) ORDER BY firstseen ASC LIMIT 1), i.created) AS firstseen,
	(SELECT lastseen FROM matches WHERE deleted IS NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id) ORDER BY lastseen ASC LIMIT 1) AS lastseen,
	(SELECT firstscanid FROM matches WHERE deleted IS NULL AND firstscanid IS NOT NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id) ORDER BY firstscanid DESC LIMIT 1) AS firstscanid,
	(SELECT lastscanid FROM matches WHERE deleted IS NULL AND lastscanid IS NOT NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id) ORDER BY lastscanid ASC LIMIT 1) AS lastscanid,
	COALESCE((SELECT lastscanid FROM matches WHERE deleted IS NULL AND lastscanid IS NOT NULL AND id IN (SELECT matchid FROM match_information WHERE informationid = i.id) ORDER BY lastscanid ASC LIMIT 1) = a.lastscanid, false) AS seenlastscan,
	(CASE WHEN i.type = 'PRODUCT' THEN
		(SELECT json_agg(jsonb_strip_nulls(jsonb_build_object('type', type, 'name', name, 'url', url, 'cpeV2_2', cpev2_2, 'cpeV2_3', cpev2_3, 'version', version, 'eol', eol, 'product', product, 'patches', patches))) FROM (
			SELECT m.type, COALESCE(p.name, (m.match->>'name')) AS name, (m.match->>'version') AS version, (m.match->>'eol') AS eol, (m.match->>'product') AS product, (m.match->'patches') AS patches,
			(CASE WHEN length(producturl) > 0 THEN producturl ELSE NULL END) AS url,
			(CASE WHEN nvdtags IS NULL OR nvdtags = '{}' THEN NULL ELSE 'cpe:/a:' || replace(nvdtags[1], '§', ':') || ':' || (match->>'version') END) AS cpev2_2,
			(CASE WHEN nvdtags IS NULL OR nvdtags = '{}' THEN NULL ELSE 'cpe:2.3:a:' || replace(nvdtags[1], '§', ':') || ':' || (match->>'version') || ':*:*:*:*:*:*:*' END) AS cpev2_3
			FROM matches m LEFT JOIN tproductinformation p ON (m.match->>'product') = p.product
			WHERE m.deleted IS NULL AND m.match IS NOT NULL AND m.id IN (SELECT matchid FROM match_information WHERE informationid = i.id)
		) x )
	WHEN (i.type = 'PORT' OR i.type = 'SERVICE') THEN
		(SELECT json_agg(match - 'service' || jsonb_build_object('type', type)) FROM matches m
		WHERE m.deleted IS NULL AND m.match IS NOT NULL AND m.id IN (SELECT matchid FROM match_information WHERE informationid = i.id))
	ELSE NULL END)::JSONB AS matches,
	(SELECT json_agg(json_build_object('id', id, 'key', key, 'value', value, 'inherited', inherited, 'origin', origin))::jsonb FROM
		(SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSETGROUP' AS origin WHERE EXISTS(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = i.assetid)
		UNION SELECT 0 AS id, NULL AS key, NULL AS value, true AS inherited, 'ASSET' AS origin
		UNION SELECT id, key, value, true AS inherited, 'ASSETGROUP' AS origin FROM tags t JOIN tag_assetgroup tg ON t.id = tg.tagid
			WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = i.customerid AND tg.assetgroupid = ANY(SELECT UNNEST(path) FROM assetgroups WHERE id = ANY(SELECT assetgroupid FROM assetgroup_asset aa WHERE aa.assetid = i.assetid))
		UNION SELECT id, key, value, true AS inherited, 'ASSET' AS origin FROM tags t JOIN tag_asset ta ON t.id = ta.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = i.customerid AND ta.assetid = i.assetid
		UNION SELECT id, key, value, false AS inherited, 'INFORMATION' AS origin FROM tags t JOIN tag_information ti ON t.id = ti.tagid WHERE t.deleted IS NULL AND t.migration IS NULL AND t.customerid = i.customerid AND ti.informationid = i.id) t) AS tags
	FROM information i
	LEFT JOIN assets a ON a.id = i.assetid;
