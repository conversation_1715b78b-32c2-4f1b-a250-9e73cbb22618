
SET standard_conforming_strings = off;
SET check_function_bodies = false;
SET client_min_messages = warning;
SET escape_string_warning = off;
SET default_tablespace = '';


--
-- Name: g2_scaninfo; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE g2_scaninfo AS (
	max_hosts bigint,
	hosts_used bigint,
	max_scans bigint,
	scans_used bigint,
	scans_left bigint,
	webapps_used bigint,
	webapp_scans_left bigint,
	webapp_scans_used bigint
);

CREATE TYPE VulnerabilityType AS ENUM ('Unknown', 'Dos', 'CodeExecution', 'Overflow', 'MemoryCorruption', 'SqlInjection', 'XSS', 'DirectoryTraversal', 'HttpResponseSplitting', 'Bypass', 'GainInformation', 'GainPrivileges', 'CSRF', 'FileInclusion', 'Information');

CREATE TYPE EventSource AS ENUM ('EXTJS', 'PORTAL', 'UNIFIED_VIEW');

--
-- Name: seq_workflowmsgs; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE seq_workflowmsgs
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: taordersequence; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE taordersequence (
	xuserxid bigint NOT NULL,
	seq bigint
);


--
-- Name: taorganizations; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE taorganizations (
	xid bigint NOT NULL,
	xiparentid bigint NOT NULL,
	vcname character varying(100) NOT NULL,
	vcaddress character varying(100),
	vcaddress2 character varying(100),
	vcpostcode character varying(100),
	vccity character varying(100),
	vccountry character varying(2),
	vcstate character varying(2),
	vcfirstname character varying(100),
	vclastname character varying(100),
	vcemail character varying(50),
	vcphone character varying(20),
	vcfax character varying(20),
	vcmobile character varying(20),
	dcreated timestamp without time zone,
	xcreator bigint,
	dupdated timestamp without time zone,
	xupdator bigint,
	xuserxid bigint,
	vcreg character varying(20),
	vcvat character varying(20),
	mssp smallint default 0,
	salesaccountid TEXT
);


--
-- Name: taorganizations_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taorganizations_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tappaccess
--
create table tappaccess (
	xid bigint not null,
	xuserxid bigint not null,
	xsubuserxid bigint default -1 not null,
	tokenkey varchar(64) default 'NEWTOKEN' not null UNIQUE,
	usage bigint not null default 0,
	ipaddress varchar(64),
	name varchar(128) not null,
	ipaddressrestriction text,
	active int default 1 not null,
	dcreated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	dupdated timestamp without time zone,
	expires TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: tappaccess_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tappaccess_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tasales; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tasales (
	xid bigint NOT NULL,
	xiparentid bigint NOT NULL,
	xiaorg bigint NOT NULL,
	iprice integer,
	vccurrency character varying(3),
	ivsms smallint DEFAULT 0,
	ivemail integer DEFAULT 0,
	ivfax smallint DEFAULT 0,
	ivsupport smallint DEFAULT 0,
	iip bigint DEFAULT 0,
	iscan bigint DEFAULT 0,
	iappliance bigint DEFAULT 0,
	bapproved smallint DEFAULT 0,
	bcancel smallint DEFAULT 0,
	xcreator bigint,
	dcreated timestamp without time zone,
	xupdator bigint,
	dupdated timestamp without time zone,
	vctype character varying(10),
	iduration smallint,
	vcorderid character varying(15),
	dstart timestamp without time zone,
	itotalprice bigint,
	txcomment character varying(4000),
	dscandate timestamp without time zone,
	vctrialip text,
	brenew smallint DEFAULT 0,
	vcsalesorder varchar(255),
	xreseller bigint,
	ilicensemodel smallint,
	applianceaddress text,
	vclicenseagreement varchar(255),
	expired smallint default 0,
	iminipcappliance bigint not null default 0,
	ivirtualappliance bigint DEFAULT 0,
	salestype smallint default 0,
	servicetype smallint,
	stype smallint,
	ssize smallint,
	sfrequency smallint,
	sproduct character varying(10),
	pocperiod smallint default 3,
	pending smallint default 0,
	notifiedsubscriptionexpiration smallint default 0,
	enduserprice bigint,
	calculatedprice bigint,
	calculatedprice2 bigint,
	approveddate timestamp without time zone,
	sekprice bigint,
	upsaleof bigint,
	renewal smallint default 0,
	termination smallint default 0,
	complianceenabled boolean default false,
	poc smallint default 0,
	ipartnerfee bigint default 0,
	sfopportunityid text,
	sfopportunitylineitemid TEXT,
	sfsubscriptionid TEXT,
	enddate timestamp without time zone,
	farsightproduct TEXT
);


--
-- Name: tasales_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tasales_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: taudits; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE taudits (
	xid bigint NOT NULL,
	xxid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	xvcapp character varying(40) NOT NULL,
	imode smallint NOT NULL,
	xtime timestamp without time zone NOT NULL,
	txcustom text,
	xconsultancyxid bigint,
	pci smallint default 0,
	deleteName text,
	addedtargets bigint[],
	removedtargets bigint[],
	discoveryupdate smallint,
	comment text,
	vcfirstname character varying(100),
	vclastname character varying(100),
	serviceid smallint
);


--
-- Name: taudits_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taudits_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

--
-- Name: tcountryl; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tcountryl (
	xid character varying(2) NOT NULL,
	xvclanguageid character varying(10),
	vcname character varying(100),
	vciso character varying(3),
	iiso smallint,
	vctld character varying(9),
	vcregion character varying(2),
	vcmapid character varying(3),
	vcareacode character varying(3),
	vccurrency character varying(3),
	ivat smallint,
	timezone character varying(100)
);


CREATE TABLE tdeletedtargets (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xgroupxid bigint NOT NULL,
	ipaddress inet,
	hostname character varying(255),
	scannerid bigint DEFAULT 0,
	hostnameid bigint,
	confirmed smallint default 0,
	pci smallint default 0,
	xupdator bigint,
	dupdated timestamp without time zone,
	xcreator bigint,
	dcreated timestamp without time zone,
	virtualhosts character varying(2048),
	macaddress character varying(18),
	reason text
);

--
--
-- Name: tgenericgroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tgenericgroups (
	xuserxid bigint NOT NULL,
	name character varying(256) NOT NULL,
	xid bigint NOT NULL,
	xiparentid bigint NOT NULL,
	xupdator bigint,
	dupdated timestamp without time zone,
	xcreator bigint,
	dcreated timestamp without time zone,
	icount bigint,
	riskcount bigint default 0,
	pci smallint default 0,
	reportbased boolean default false,
	groupriskcount bigint default 0,
	description text,
	tags JSONB
);

--
-- Name: tgenericgroups_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tgenericgroups_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: thiabstats; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE SEQUENCE thiabstats_seq START WITH 1000;

CREATE TABLE thiabstats (
	id BIGINT UNIQUE DEFAULT nextval('thiabstats_seq'),
	xuserxid bigint NOT NULL,
	xtime timestamp without time zone,
	iips integer,
	itest integer,
	dupdate timestamp without time zone,
	itype smallint DEFAULT 0 NOT NULL,
	scheduler smallint default 1,
	scansleft bigint default 0,
	webappsused BIGINT DEFAULT 0,
	webappscansleft BIGINT DEFAULT 0,
	webappscansused BIGINT DEFAULT 0,
	virtual smallint default 0,
	hwaddr VARCHAR(32),
	cpuinfo VARCHAR(128),
	revoked BOOLEAN DEFAULT FALSE NOT NULL,
	product VARCHAR(128),
	serial BIGINT,
	version TEXT,
	lastUpdate TIMESTAMP WITHOUT TIME ZONE,
	os TEXT,
	publicsbc TEXT,
	hmackey VARCHAR(8196),
	installedpackages TEXT,
	uuid TEXT DEFAULT 'NOID' NOT NULL
);

CREATE SEQUENCE thiabstatshistory_seq START WITH 1000;

CREATE TABLE coreprefs(groupName TEXT, keyName TEXT, type TEXT, value TEXT);

--
-- Name: tlanguages; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tlanguages (
	xid character varying(10) NOT NULL,
	xtime timestamp without time zone,
	vcname character varying(100) NOT NULL,
	iactive smallint DEFAULT 0
);


--
-- Name: tloggings; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tloggings (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	itype smallint DEFAULT 3,
	xrefid bigint,
	ipriority smallint,
	assignee bigint[] DEFAULT ARRAY[]::bigint[],
	ticketpriority smallint,
	recipient text,
	xsubuserxid bigint,
	myscans smallint default 1,
	newfindings smallint default 1,
	scanformat smallint not null default 0,
	encryptionkey varchar(255),
	attachreport smallint default 0,
	reporttype smallint default 0,
	scantype smallint default 31,
	created timestamp without time zone,
	updated timestamp without time zone,
	eventName varchar(200),
	ticketsummary varchar(256),
	syslogfields bigint,
	dblogfields bigint,
	attributefields bigint,
	targetinformation smallint default 1,
	reportpassword varchar(150),
	customsubject varchar(200),
	customtext text,
	duedatenumber bigint,
	duedatetype bigint,
	onlyonpreviouslyactive boolean not null default false,
	comments text,
	emailencryptionkey varchar(255),
	customconfig BOOLEAN DEFAULT false,
	daysinadvance integer default 0,
	reporttemplate BIGINT default 0,
	scriptid text,
	bactive smallint DEFAULT 1,
	splunkformat SMALLINT DEFAULT 0,
	uisource EventSource DEFAULT 'EXTJS'
);


--
-- Name: tloggings_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tloggings_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;




--
-- Name: toldworkflows; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE toldworkflows (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	taskid bigint,
	"type" smallint,
	dcreated timestamp without time zone,
	priority smallint,
	status smallint,
	name character varying(400),
	duedate timestamp without time zone,
	dupdated timestamp without time zone,
	ipaddress character varying(255),
	ipvalue bigint,
	xupdator bigint,
	xcreator bigint,
	iport bigint,
	iprotocol bigint,
	vcvulnid bigint,
	xipxid bigint
);


--
-- Name: toutscanfiles; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE toutscanfiles (
	xid bigint NOT NULL,
	dupdated timestamp without time zone,
	xuserxid bigint NOT NULL,
	name character varying(255),
	size bigint,
	dcreated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	content text,
	"comment" character varying(255),
	hidden smallint default 0,
	xsubuserxid bigint default -1,
	private smallint default 0,
	mode SMALLINT DEFAULT 0
);


--
-- Name: toutscanfiles_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE toutscanfiles_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


CREATE TABLE tpasswordrecoverys (
	xid varchar(64) not null,
	xuserxid bigint not null,
	xsubuserxid bigint,
	expire timestamp not null,
	console boolean default false
);

ALTER TABLE ONLY tpasswordrecoverys ADD CONSTRAINT tpasswordrecoverys_pkey PRIMARY KEY (xid);

CREATE TABLE tcookietokens (
	token varchar(64) not null,
	userid bigint not null,
	subuserid bigint,
	expire timestamp not null,
	consultancyid bigint default 0
);

ALTER TABLE ONLY tcookietokens ADD CONSTRAINT tcookietokens_pkey PRIMARY KEY (token);

CREATE TABLE tvalidationtokens (
	xid varchar(64) not null,
	xuserxid bigint not null,
	xsubuserxid bigint,
	expire timestamp not null
);

CREATE TABLE tsamltokens (
	xid varchar(64) PRIMARY KEY,
	samlresponse text,
	expire timestamp not null,
	idpid bigint,
	ipaddress text
);

--
-- Name: tqueuelogs; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tqueuelogs (
	xid bigint NOT NULL,
	xuserxid bigint,
	itype smallint,
	dcreated timestamp without time zone,
	dlastcheck timestamp without time zone,
	dsent timestamp without time zone,
	iretries integer,
	cdata text,
	xthreadid character varying(5),
	binvalid smallint,
	vctype character varying(50),
	trackingid character varying(150),
	status character varying(50),
	event INTEGER,
	parentId BIGINT,
	subuserid BIGINT
);


--
-- Name: tqueues_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tqueues_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tqueues; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tqueues (
	xid bigint NOT NULL DEFAULT nextval('tqueues_seq'),
	xuserxid bigint,
	itype smallint,
	dcreated timestamp without time zone,
	dlastcheck timestamp without time zone,
	iretries integer,
	cdata text,
	binvalid smallint,
	xthreadid character varying(5),
	vctype character varying(50),
	event INTEGER,
	parentId BIGINT,
	subuserid BIGINT
);


CREATE TABLE treport_disputes (
	xid bigint NOT NULL,
	fk_treportentrys_xid bigint NOT NULL,
	fk_treport_vulns_xid bigint NOT NULL,
	name varchar(255),
	size bigint,
	content text,
	linkid bigint
);

CREATE SEQUENCE treport_disputes_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE disputeinfo (
	vcvulnid bigint,
	content text
);

--
-- Name: treport_setups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE treport_setups (
	xid bigint NOT NULL,
	fk_treportentrys_xid bigint NOT NULL,
	iorigin bigint,
	vctype character varying(32),
	cdata text,
	xrid bigint
);


--
-- Name: treport_setups_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE treport_setups_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: treport_vulns; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE treport_vulns (
	xid bigint NOT NULL,
	restid integer,
	fk_treportentrys_xid bigint NOT NULL,
	itype bigint,
	iport bigint,
	irisk bigint,
	iprotocol bigint,
	bfalsepos smallint DEFAULT 0,
	vcvulnid bigint,
	vcvhost character varying(2048),
	cdata text,
	bpcifailed smallint,
	iscvss smallint,
	vcscvssvector character varying(128),
	servicename character varying(64),
	bnew smallint DEFAULT 0,
	dfirstseen timestamp without time zone,
	dlastseen timestamp without time zone,
	ipcicvss smallint,
	potentialfalse smallint DEFAULT 0,
	falsepositivecomment text,
	acceptdate timestamp without time zone,
	acceptedlength integer DEFAULT 0,
	acceptComment text,
	acceptedBy bigint,
	xipxid bigint,
	fixed smallint default 0,
	specialnotesecure smallint not null default 0,
	specialnotedescription text,
	disputeaccepted smallint not null default 0,
	globalaccepted smallint default 0,
	disputestatus smallint default 0,
	disputedate timestamp,
	disputecomment text,
	originalrisklevel smallint default -1,
	acceptexpires TIMESTAMP WITHOUT TIME ZONE,
	dadded timestamp without time zone,
	falsepositiveby bigint,
	recreationflow text,
	explanation text,
	olddisputeacceptedid bigint,
	customName TEXT,
	customDescription TEXT,
	customCve TEXT,
	customBugtraq TEXT,
	customVulnerabilitytype VulnerabilityType,
	customCwe INTEGER,
	customSolutionType SMALLINT,
	customSolutionTitle TEXT,
	customSolution TEXT,
	custom0 VARCHAR(1024),
	custom1 VARCHAR(1024),
	custom2 VARCHAR(1024),
	custom3 VARCHAR(1024),
	custom4 VARCHAR(1024),
	custom5 VARCHAR(1024),
	custom6 VARCHAR(1024),
	custom7 VARCHAR(1024),
	custom8 VARCHAR(1024),
	custom9 VARCHAR(1024),
	reviewedBy bigint,
	addedBy bigint,
	confirmedBy bigint,
	fixedBy bigint,
	externalTicket VARCHAR(100),
	previouslyDetected BOOLEAN,
	wasFinding BOOLEAN,
	swatstatus smallint,
	patchInformation TEXT,
	clarificationrequestcomment TEXT,
	clarificationrequestby BIGINT,
	verifyData TEXT,
	verifyDate TIMESTAMP WITHOUT TIME ZONE,
	stillPresent SMALLINT,
	verifyAttacker BIGINT,
	verifyScanStatusId BIGINT,
	cvssv3score DECIMAL(10, 1),
	cvssv3vector VARCHAR(100),
	specialnote BOOLEAN
);


--
-- Name: treport_vulns_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE treport_vulns_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


CREATE TABLE tdeletedreport_vulns (
	xid bigint NOT NULL,
	fk_treportentrys_xid bigint NOT NULL,
	itype bigint,
	iport bigint,
	irisk bigint,
	iprotocol bigint,
	vcvulnid bigint,
	vcvhost character varying(2048),
	cdata text,
	dfirstseen timestamp without time zone,
	dlastseen timestamp without time zone,
	xipxid bigint,
	dadded timestamp without time zone
);

--
-- Name: treportentrys; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE treportentrys (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xschedulexid bigint,
	xsoxid bigint,
	scannerid bigint default 0,
	xtemplate bigint,
	hostnameid bigint,
	xipxid bigint not null default -1,
	itype smallint,
	iid bigint,
	benabled smallint,
	bsupport smallint,
	bconsult smallint,
	dupdated timestamp without time zone,
	dreportdate timestamp without time zone,
	dreportenddate timestamp without time zone,
	vctarget character varying(255),
	bpci smallint DEFAULT 0,
	iorigin bigint,
	dcreated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	xprevxid bigint,
	xscanlogxid bigint NOT NULL,
	xscanjobxid bigint NOT NULL,
	cvss_score bigint DEFAULT 0,
	high_count bigint DEFAULT 0,
	medium_count bigint DEFAULT 0,
	low_count bigint DEFAULT 0,
	supportexpiration date,
	bpcicompliant smallint default 1,
	targettype smallint default 0,
	platform varchar(50),
	ipaddress varchar(50),
	fixedHighvulns bigint default 0,
	fixedMediumvulns bigint default 0,
	fixedLowvulns bigint default 0,
	daysfixedHighvulns bigint default 0,
	daysfixedMediumvulns bigint default 0,
	daysfixedLowvulns bigint default 0,
	daysnonfixedHighvulns bigint default 0,
	daysnonfixedMediumvulns bigint default 0,
	daysnonfixedLowvulns bigint default 0,
	urisscanned bigint,
	reachable smallint default 1,
	authenticationResult smallint,
	highaccepted integer DEFAULT 0,
	highfalsepos integer DEFAULT 0,
	mediumaccepted integer DEFAULT 0,
	mediumfalsepos integer DEFAULT 0,
	lowaccepted integer DEFAULT 0,
	lowfalsepos integer DEFAULT 0,
	openports integer[] DEFAULT ARRAY[]::integer[],
	inetaddress INET
);


--
-- Name: treportentrys_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE treportentrys_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: treporttexts; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE treporttexts (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	itype integer DEFAULT -1,
	ilocation smallint DEFAULT 0,
	isort integer DEFAULT 1,
	vcheadline character varying(200),
	txtext text,
	xsubuserxid bigint,
	xupdator bigint,
	dupdated timestamp without time zone,
	xcreator bigint,
	dcreated timestamp without time zone,
	reporttemplate bigint DEFAULT 0,
	reportlevel smallint DEFAULT -1,
	ispublic BOOLEAN NOT NULL DEFAULT FALSE
);


--
-- Name: treporttexts_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE treporttexts_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tsavedscanprefs; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE TABLE tsavedscanprefs (
	xid bigint not null,
	xuserxid bigint not null,
	name character varying(128),
	cpluginprop text,
	global smallint default 0,
	xupdator bigint,
	dupdated timestamp without time zone,
	xcreator bigint,
	dcreated timestamp without time zone,
	targetOverride bigint,
	targetoverrideedit bigint,
	description text,
	deleted smallint DEFAULT 0,
	xsubuserxid bigint,
	authenticationtypessh SMALLINT DEFAULT 1,
	authenticationtypesmb SMALLINT DEFAULT 2
);


--
-- Name: tsavedscanprefs_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tsavedscanprefs_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tscanlogs_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tscanlogs_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tscanlogs; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tscanlogs (
	xid bigint NOT NULL DEFAULT nextval('tscanlogs_seq'),
	xuserxid bigint NOT NULL,
	itype smallint,
	dupdated timestamp without time zone,
	dscanstartdate timestamp without time zone,
	dscanenddate timestamp without time zone,
	vchost character varying(255),
	xtemplate bigint,
	scannerid bigint DEFAULT 0,
	xipxid bigint not null default -1,
	xsoxid bigint,
	iid bigint,
	bdeleted smallint DEFAULT 0,
	vccountry character varying(2) DEFAULT '--'::character varying,
	dcreated timestamp without time zone,
	xscanjobxid bigint NOT NULL,
	reason varchar(2048),
	confirmed smallint default 0,
	fromhiab smallint not null default 0,
	targetgroupxid bigint,
	targetgroupname varchar(256),
	submitted smallint default 0 NOT NULL,
	xschedulexid BIGINT,
	scanlessreportxid bigint default -1,
	latestscanupdate timestamp,
	scanschema text,
	vcgname character varying(50),
	xsubuserxid bigint,
	compliancescan boolean not null default false,
	blueprint integer default 0,
	compliancepolicies BIGINT[],
	discovery BOOLEAN DEFAULT FALSE,
	latestRuleDate TIMESTAMP
);


--
-- Name: tscanners; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

create table tscanners (
	xid bigint not null,
	xuserxid bigint not null,
	name varchar(128) not null,
	ipaddress varchar(255) not null,
	key varchar(32),
	mode smallint,
	approved smallint,
	lastconnection timestamp,
	inactive smallint,
	message text,
	xcreator bigint,
	xupdator bigint,
	created timestamp,
	updated timestamp,
	isoutpost smallint default 0,
	isawsscanner smallint default 0,
	scanningdisabled smallint default 0,
	polling smallint default 0,
	useProxy smallint default 0 not null,
	lastnotification timestamp,
	lastupdate timestamp,
	version varchar(20),
	uiversion varchar(50),
	scannerversion varchar(50),
	groupxid bigint,
	isgroup smallint default 0,
	performupdate smallint default 0,
	deleted smallint DEFAULT 0,
	serverStatus TEXT,
	HWADDR character varying(32),
	global BOOLEAN DEFAULT FALSE,
	hmackey varchar(8196),
	rulesversion TIMESTAMP WITHOUT TIME ZONE,
	appsecScaleScanner BOOLEAN DEFAULT FALSE NOT NULL,
	certificaterequest JSONB,
	certificate TEXT,
	revoked BOOLEAN DEFAULT FALSE NOT NULL,
	uuid UUID,
	slsscanscheduled BOOLEAN DEFAULT FALSE NOT NULL
);

--
-- Name: tscanners_seq; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE SEQUENCE tscanners_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tscanstatuss_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tscanstatuss_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tscanstatuss; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tscanstatuss (
	xid bigint NOT NULL DEFAULT nextval('tscanstatuss_seq'),
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	xsoxid bigint NOT NULL,
	xscanjobxid bigint NOT NULL,
	scannerid bigint DEFAULT 0 NOT NULL,
	remotexid bigint DEFAULT 0,
	vcservice character varying(10) NOT NULL,
	vcpercent character varying(10),
	ipercentv integer,
	ithreadid integer,
	vcstatus character varying(50),
	vcstate character varying(20),
	vcgname character varying(50),
	iattackerid integer,
	icount integer,
	vctarget character varying(255),
	xipxid bigint not null default -1,
	xtemplate bigint,
	iverify bigint default 0,
	txsettings text,
	txreport text,
	dscanstarted timestamp without time zone,
	dscanended timestamp without time zone,
	dscanstart timestamp without time zone,
	dscanend timestamp without time zone,
	dcreated timestamp without time zone,
	dupdated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	bpause smallint DEFAULT 0,
	bstop smallint default 0,
	bsync smallint default 0,
	vcjobname character varying(255),
	reason text,
	scanwindows INTEGER NOT NULL DEFAULT 1,
	scanwindowdelay INTEGER NOT NULL DEFAULT 1,
	probeid VARCHAR(100),
	resuming SMALLINT DEFAULT 0,
	maxscanwindow INTERVAL,
	scansent SMALLINT DEFAULT 0,
	isstopped SMALLINT DEFAULT 0 NOT NULL,
	ispaused SMALLINT DEFAULT 0 NOT NULL,
	targettype smallint default 0,
	hostname character varying(255),
	lookup smallint default 0,
	scanlessreportxid bigint default -1,
	scanschema VARCHAR(255),
	wakeonlandelay BIGINT DEFAULT 0,
	wakeonlan TEXT,
	wakeonlansent timestamp without time zone,
	templatename character varying(255),
	includeAdminRules SMALLINT DEFAULT 0,
	reportAttempted TIMESTAMP WITHOUT TIME ZONE,
	activatecompliance boolean not null default false,
	openports bigint,
	scanresult integer DEFAULT 0,
	ipaddress varchar(64),
	priority INTEGER DEFAULT 1,
	pauseduration bigint default 0,
	scanmode smallint default -1,
	workflowid BIGINT,
	argoworkflowname VARCHAR(200),
	issues TEXT,
	assetid INTEGER,
	networklookupdata TEXT
);


--
-- Name: tscheduleobjectgroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--
CREATE TABLE tscheduleobjectgroups (
	xid bigint PRIMARY KEY NOT NULL,
	xuserxid bigint NOT NULL,
	name character varying(200),
	xiparentid bigint default -1
);


--
-- Name: tscheduleobjectgroups_seq; Type: SEQUENCE; Schema: public; Owner: -
--
CREATE SEQUENCE tscheduleobjectgroups_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE SEQUENCE twasauths_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

create table twasauths (
	xid bigint not null,
	wasxid bigint not null,
	recordtype varchar(1),
	authtype varchar(20),
	uri varchar(1024),
	username varchar(64),
	password varchar(64),
	query varchar(8193),
	formtype smallint default 1,
	formfield varchar(255),
	formvalues text
);

CREATE SEQUENCE twascookies_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

create table twascookies (
	xid bigint not null,
	wasxid bigint not null,
	name varchar(200),
	domain varchar(64),
	path varchar(255),
	"value" text,
	secure smallint default 0,
	httponly smallint default 0
);

CREATE SEQUENCE twasheaders_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

create table twasheaders (
	xid bigint not null,
	wasxid bigint not null,
	name varchar(64),
	"value" text
);

CREATE SEQUENCE twasparameters_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

create table twasparameters (
	xid bigint not null,
	wasxid bigint not null,
	name varchar(255),
	id varchar(255),
	"value" text,
	form varchar(255),
	page varchar(1024),
	protectvalue smallint default 0,
	sortorder bigint default 0
);

CREATE SEQUENCE twaspaths_seq START WITH 1000;

CREATE TABLE twaspaths (
	xid bigint not null,
	xuserxid bigint not null,
	xipxid bigint not null,
	treportentry bigint not null,
	xsoxid bigint not null,
	vhost character varying(2048),
	parent bigint,
	path text,
	branch bigint[],
	nodefindingcount bigint default 0,
	branchfindingcount bigint default 0
);

CREATE SEQUENCE twasurls_seq START WITH 1000;

CREATE TABLE twasurls (
	xid bigint not null,
	xuserxid bigint not null,
	xipxid bigint not null,
	treportentry bigint not null,
	xsoxid bigint not null,
	vhost character varying(2048),
	was smallint,
	delta smallint,
	path bigint,
	url text,
	method varchar(10),
	time integer,
	post text,
	code smallint,
	fuzzed smallint default 0,
	findingxid bigint
);

CREATE SEQUENCE twasfindings_seq START WITH 1000;

CREATE TABLE twasfindings (
	xid bigint not null,
	xuserxid bigint not null,
	xipxid bigint not null,
	treportentry bigint not null,
	xsoxid bigint not null,
	vhost character varying(2048),
	path bigint,
	vcvulnid bigint,
	url text,
	method varchar(10),
	time varchar(255),
	post text,
	requestheaders text,
	responseheaders text,
	content text,
	acceptdate timestamp without time zone,
	acceptedlength integer DEFAULT 0,
	acceptComment text,
	acceptedBy bigint,
	acceptexpires TIMESTAMP WITHOUT TIME ZONE,
	data text[],
	hascontent smallint default 0,
	findingxid bigint,
	match TEXT,
	matchStart INTEGER,
	matchLength INTEGER,
	wasFindingId BIGINT
);

--
-- Name: tserverstatuss; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tserverstatuss (
	xid character varying(25) NOT NULL,
	vcservice text NOT NULL,
	isleep integer,
	iallowed integer,
	iexec integer,
	itimeout integer,
	isignoff integer,
	vcstatus character varying(50),
	vcload character varying(50),
	vcaverage character varying(16),
	vctotaltime character varying(16),
	dstart timestamp without time zone,
	dcurrent timestamp without time zone,
	dcreated timestamp without time zone,
	dupdated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	txinfo text,
	iqueue integer,
	scriptversion varchar(100),
	iexecupdates integer,
	vcaverageupdates character varying(16),
	iexecwas integer,
	iexecwasx integer,
	vcaveragewas character varying(16),
	vcaveragewasx character varying(16),
	framework text,
	LASTUPDATEDRULE character varying(100),
	paused boolean default false,
	localip TEXT,
	freememory TEXT,
	totalmemory TEXT,
	freedisk TEXT,
	totaldisk TEXT,
	wasallowed INTEGER,
	wasxallowed INTEGER,
	load TEXT,
	coreversion character varying(100),
	maintenancestart TIMESTAMP WITHOUT TIME ZONE,
	maintenanceend TIMESTAMP WITHOUT TIME ZONE,
	uuid VARCHAR(200)
);


--
-- Name: tsessionstates; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tsessionstates (
	xuserxid bigint not null,
	xsubuserxid bigint not null,
	name varchar(100),
	value text
);

--
-- Name: tstatel; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tstatel (
	xid character varying(2) NOT NULL,
	xvclanguageid character varying(10) NOT NULL,
	xtime timestamp without time zone,
	vcname character varying(100) NOT NULL,
	timezone character varying(100),
	countryxid character varying(2)
);


--
-- Name: tsubusers; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tsubusers (
	xid bigint NOT NULL,
	dcreated timestamp without time zone,
	xiparentid bigint NOT NULL,
	subusergroupid bigint default -1,
	dupdated timestamp without time zone,
	xcreator bigint,
	vcusername character varying(64) NOT NULL,
	vcpassword character varying(150) NOT NULL,
	vcfirstname character varying(100) NOT NULL,
	vclastname character varying(100) NOT NULL,
	vcemail character varying(150) NOT NULL,
	vcstate character varying(2) DEFAULT '--'::character varying,
	vccountry character varying(2),
	timezone character varying(100) NOT NULL DEFAULT 'Etc/UTC',
	demail timestamp without time zone,
	bactive smallint DEFAULT 1,
	xvcip character varying(40),
	dlastlogon timestamp without time zone,
	ilogon bigint DEFAULT 0,
	boallhosts smallint DEFAULT 0,
	xisubparentid bigint,
	ifailedlogon bigint DEFAULT 0,
	xupdator bigint,
	vcphonemobile character varying(20),
	iemailtype smallint DEFAULT 1,
	vcfullname character varying(200),
	xpathdown character varying(2048),
	xpathup character varying(2048),
	gmtoffset decimal(10, 2) DEFAULT 0,
	dateformat varchar(20) default 'Y-m-d',
	timeformat varchar(20) default 'H:i',
	showguide smallint default 1,
	startdayofweek smallint default 1,
	language varchar(10) default 'en',
	superuser smallint default 0,
	authenticationmethod smallint default 0,
	ticketParent bigint not null default 0,
	passwordchanged TIMESTAMP NOT NULL,
	showpciinfo smallint default 1,
	allscanners smallint default 1,
	custom0 varchar(1024),
	custom1 varchar(1024),
	custom2 varchar(1024),
	custom3 varchar(1024),
	custom4 varchar(1024),
	custom5 varchar(1024),
	custom6 varchar(1024),
	custom7 varchar(1024),
	custom8 varchar(1024),
	custom9 varchar(1024),
	grantedWeb TEXT,
	allWeb SMALLINT DEFAULT 1,
	defaultreporttemplate bigint,
	defaultreporttemplatepci bigint,
	defaultreporttemplatewas bigint,
	defaultreporttemplatecompliance bigint,
	defaultreporttemplateswat bigint,
	reportpassword character varying(150),
	automaticgmt smallint default 1,
	changepasswordonlogon smallint default 0,
	ldapgroups text,
	systemnotifications smallint default 0,
	credentialid character varying(50),
	lastconsumedotptimeindex integer default 0,
	twofactorauthentication smallint default 0,
	hiabenroll boolean default false,
	allswat smallint default 1,
	allawsarn smallint default 1,
	globalignoretargetlist TEXT,
	useglobalignoretargetlistbydefault BOOLEAN DEFAULT FALSE,
	dismissComplianceWarning BOOLEAN DEFAULT FALSE,
	calculateNextScanDate BOOLEAN DEFAULT TRUE,
	emailencryptionkey varchar(255),
	cookiesAccepted TIMESTAMP WITHOUT TIME ZONE,
	portalpreferences TEXT,
	userroleids INTEGER[],
	resourcegroupids INTEGER[],
	lastlogonversion VARCHAR(50),
	allowedcustomertagids INTEGER[],
	pgppublickey TEXT
);

--
-- Name: tsubusers_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tsubusers_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TYPE businesscriticality AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

--
-- Name: tuserdatas; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tuserdatas (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	ipaddress inet,
	hostname character varying(255),
	scannerid bigint DEFAULT 0,
	hostnameid bigint,
	confirmed smallint default 0,
	pci smallint default 0,
	xupdator bigint,
	dupdated timestamp without time zone,
	xcreator bigint,
	dcreated timestamp without time zone,
	virtualhosts character varying(2048),
	countrycode character varying(2) DEFAULT '--'::character varying,
	macaddress character varying(18),
	cvss_sr_avail character varying(2) DEFAULT 'ND'::character varying,
	cvss_sr_integ character varying(2) DEFAULT 'ND'::character varying,
	cvss_sr_conf character varying(2) DEFAULT 'ND'::character varying,
	cvss_cdp character varying(2) DEFAULT 'ND'::character varying,
	cvss_td character varying(2) DEFAULT 'ND'::character varying,
	custom0 varchar(1024),
	custom1 varchar(1024),
	custom2 varchar(1024),
	custom3 varchar(1024),
	custom4 varchar(1024),
	custom5 varchar(1024),
	custom6 varchar(1024),
	custom7 varchar(1024),
	custom8 varchar(1024),
	custom9 varchar(1024),
	hiddenurls text,
	sync smallint default 0,
	netbios character varying(15),
	macSource smallint default 0,
	authenticationtype SMALLINT default 0,
	batchmode boolean,
	aws_instance_id varchar(255),
	aws_last_region varchar(50),
	awsarn varchar(255),
	lookupipaddress inet,
	urlblacklist text,
	requestbodyblacklist text,
	compliancesenabled text,
	snsysid text,
	lastknowninfo text,
	agentid text,
	agentversion text,
	agentlastsynchronized timestamp without time zone,
	agentretired BOOLEAN,
	agentcallhomefrequency text,
	businesscriticality businesscriticality NOT NULL DEFAULT 'MEDIUM',
	exposed BOOLEAN NOT NULL,
	tags JSONB
);


--
-- Name: tuserdatas_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tuserdatas_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tusergroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tusergroups (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	dcreated timestamp without time zone,
	dupdated timestamp without time zone,
	xcreator bigint,
	xupdator bigint,
	vcname character varying(50) NOT NULL,
	bosettings smallint DEFAULT 0,
	boreports smallint DEFAULT 0,
	boschedules smallint DEFAULT 0,
	bsubadmin smallint DEFAULT 0,
	boemail smallint DEFAULT 1,
	boadmingroups smallint DEFAULT 0,
	bhadmin smallint DEFAULT 0,
	bosms smallint DEFAULT 0,
	bodisable smallint DEFAULT 0,
	bovultext smallint DEFAULT 0,
	bodeletereport smallint DEFAULT 0,
	badminusergroup smallint DEFAULT 0,
	bacceptrisk smallint DEFAULT 0,
	pciscoping smallint default 0,
	pcireporting smallint default 0,
	pcischeduling smallint default 0,
	pcidisputing smallint default 0,
	webappadmin smallint DEFAULT 0,
	webappreporting smallint DEFAULT 0,
	webappdeletereport smallint default 0,
	forcegroupscheduling smallint default 0,
	managedservices smallint default 0,
	managedservicescomment smallint default 0,
	verifyscan smallint default 0,
	stopscan smallint default 0,
	ldapgroup character varying(255),
	dashboard smallint default 0,
	compliance_enabled boolean not null default false,
	web boolean not null default false,
	readlicense boolean not null default false,
	bticketmanagement smallint default 0,
	grantalltickets BOOLEAN NOT NULL DEFAULT FALSE,
	readonly BOOLEAN NOT NULL DEFAULT FALSE,
	read_auditlogs BOOLEAN NOT NULL DEFAULT TRUE,
	markcomplianceexceptions smallint default 0,
	swatcomment smallint default 0,
	swatverification smallint default 0,
	swatdiscussion smallint default 0,
	swatrisks smallint default 0,
	submitscoping BOOLEAN NOT NULL DEFAULT FALSE,
	answercompliancequestions SMALLINT DEFAULT 0,
	approvecompliancequestions SMALLINT DEFAULT 0,
	editcompliancepolicies SMALLINT DEFAULT 0,
	wasx BOOLEAN DEFAULT FALSE,
	rulemanagement BOOLEAN NOT NULL DEFAULT FALSE,
	ruleadmin BOOLEAN NOT NULL DEFAULT FALSE,
	autorules BOOLEAN NOT NULL DEFAULT FALSE,
	editrules BOOLEAN NOT NULL DEFAULT FALSE
);

--
-- Name: tusergroups_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tusergroups_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

--
-- Name: tsubusergroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tsubusergroups (
	xid bigint PRIMARY KEY NOT NULL,
	xuserxid bigint NOT NULL,
	name character varying(200),
	xiparentid bigint default -1,
	icount bigint default 0
);

--
-- Name: tsubusergroups_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tsubusergroups_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

--
-- Name: tusers; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tusers (
	xid bigint NOT NULL PRIMARY KEY,
	dupdated timestamp without time zone,
	vcemail character varying(150) NOT NULL,
	vcusername character varying(64) NOT NULL,
	vcpassword character varying(150) NOT NULL,
	vcfirstname character varying(100) NOT NULL,
	vclastname character varying(100) NOT NULL,
	vcaddress character varying(150) DEFAULT ' '::character varying,
	vcaddress2 character varying(150),
	vcpostal character varying(100) DEFAULT ' '::character varying,
	vccity character varying(100) DEFAULT ' '::character varying,
	vcstate character varying(2) DEFAULT '--'::character varying,
	vccountry character varying(2) NOT NULL,
	timezone character varying(100) NOT NULL DEFAULT 'Etc/UTC',
	vcphoneday character varying(20),
	vcphonenight character varying(20),
	vcphonemobile character varying(20),
	vcphonefax character varying(20),
	vccompany character varying(150),
	vcvatnumber character varying(20),
	vcorgnumber character varying(20),
	dlastlogon timestamp without time zone,
	bactive smallint DEFAULT 1,
	vcacctid character varying(100),
	xiparentid bigint DEFAULT 0,
	dcreated timestamp without time zone,
	xupdator bigint NOT NULL,
	xcreator bigint NOT NULL,
	vctype character varying(3) DEFAULT 'COM'::character varying,
	xvcip character varying(40),
	ilogon bigint DEFAULT 0,
	demail timestamp without time zone,
	dstart timestamp without time zone,
	xiorg bigint,
	bsecuritytrial smallint DEFAULT 1,
	isecurityleft integer DEFAULT 0,
	ipciscansleft integer DEFAULT 0,
	breseller smallint DEFAULT 0,
	ifailedlogon bigint DEFAULT 0,
	bremovereport smallint DEFAULT 0,
	bhiablicense smallint DEFAULT 0,
	iemailtype smallint DEFAULT 1,
	boutscanlicense smallint DEFAULT 0,
	xiaorg bigint,
	booslicense smallint DEFAULT 0,
	bulimit smallint DEFAULT 0,
	vcfullname character varying(200),
	btaskalert smallint DEFAULT 1,
	gmtoffset decimal(10, 2) DEFAULT 0,
	dateformat varchar(20) default 'Y-m-d',
	timeformat varchar(20) default 'H:i',
	activateadmin smallint default 0,
	showguide smallint default 1,
	startdayofweek smallint default 1,
	language varchar(10) default 'en',
	pcicertificate varchar(20),
	reportcompany varchar( 100),
	customcompanyname varchar(150),
	pciname varchar(200),
	pciemail varchar(150),
	translation varchar(20),
	bpcitrial smallint DEFAULT 0,
	sessiontimeout smallint DEFAULT 60,
	iexternalscansleft integer not null default 0,
	auditTargetManagement SMALLINT DEFAULT 0 NOT NULL,
	auditScheduleManagement SMALLINT DEFAULT 0 NOT NULL,
	auditScanPolicyManagement SMALLINT DEFAULT 0 NOT NULL,
	auditRiskAcceptance SMALLINT DEFAULT 0 NOT NULL,
	auditChangeRiskLevel SMALLINT DEFAULT 0 NOT NULL,
	webappscansleft bigint default 0,
	webapptrial smallint default 0,
	externalwebappscansleft BIGINT DEFAULT 0,
	salesperson bigint DEFAULT 0,
	wasMaximumLinks BIGINT NOT NULL DEFAULT 2000,
	passwordChanged TIMESTAMP NOT NULL,
	xosip integer default 0,
	xosscan integer default 0,
	xhiabip bigint default 0,
	xhiabschedule bigint default 0,
	xhiabscheduleadd bigint default 0,
	xhiabclosed smallint default 0,
	xoosip bigint default 0,
	xoosschedule bigint default 0,
	xoosscheduleadd bigint default 0,
	xoosclosed smallint default 0,
	xpciip bigint default 0,
	xpciscan bigint default 0,
	xhiabexternalip bigint default 0,
	maxwebapps bigint default 0,
	webappscans bigint default 0,
	hiabwebappscansadd bigint default 0,
	hiabexternalwebapps BIGINT DEFAULT 0,
	HIABMAXWEBAPPS BIGINT DEFAULT 0,
	hiabwebappscans BIGINT DEFAULT 0,
	xroles character varying(1024),
	shiabs bigint default 0,
	maxVirtual integer default 0,
	downloadVirtual integer default 0,
	mssp smallint default 0,
	showpciinfo smallint default 1,
	forcegroupscheduling smallint default 1,
	CONCURRENTSCANS int default 0,
	removehiabscans smallint default 0,
	removehiabwebappscans smallint default 0,
	discoveryLimit bigint not null default 0,
	services smallint default 0,
	latestscandate timestamp without time zone,
	customreportheader varchar(255),
	customreportfooter varchar(255),
	defaultreporttemplate bigint,
	defaultreporttemplatepci bigint,
	defaultreporttemplatewas bigint,
	defaultreporttemplatecompliance bigint,
	defaultreporttemplateswat bigint,
	reportpassword character varying(150),
	managedserviceslimited smallint default 0,
	automaticgmt smallint default 1,
	servicereminderinterval integer default 0,
	nextservicereminder timestamp without time zone,
	vccurrency character varying(3),
	scannertimeout bigint default 15,
	scanpolicyownership smallint default 0,
	lowrisklevel smallint default 0 not null,
	mediumrisklevel smallint default 40 not null,
	highrisklevel smallint default 70 not null,
	riskage bigint default 90,
	credentialid character varying(50),
	lastconsumedotptimeindex integer default 0,
	twofactorauthentication smallint default 0,
	compliance boolean not null default false,
	compliancepolicyownership smallint default 1,
	allowtargetmultiplegroups SMALLINT NOT NULL DEFAULT 1,
	pentestcompliance boolean default false,
	maxPenTestHiabs INTEGER NOT NULL DEFAULT 0,
	payperuse BOOLEAN NOT NULL DEFAULT FALSE,
	web BOOLEAN NOT NULL DEFAULT FALSE,
	agentsenabled BOOLEAN NOT NULL DEFAULT FALSE,
	defaultagentcallhomefrequency text,
	remotesupport BOOLEAN NOT NULL DEFAULT FALSE,
	portasvulnerability smallint default 0,
	awsenabled boolean default false,
	thycoticenabled boolean default false,
	defaultscanner BIGINT default 0,
	DEFAULTTEMPLATEID BIGINT default 0,
	xsalesorganizations TEXT,
	disableunsafechecks smallint default 0,
	hiabs boolean DEFAULT false,
	hiabp boolean DEFAULT false,
	DEFAULTMAXSCANTIME BIGINT default 0,
	custominfotxt TEXT,
	targetinactivediscoveries bigint default 4,
	groupsinscheduling boolean default false,
	outscaninternalips BIGINT DEFAULT 0,
	jiraenabled boolean,
	ssoenabled boolean,
	snenabled boolean default false,
	allowComplianceOnlyScanning BOOLEAN DEFAULT FALSE,
	globalignoretargetlist TEXT,
	useglobalignoretargetlistbydefault BOOLEAN DEFAULT FALSE,
	dismissComplianceWarning BOOLEAN DEFAULT FALSE,
	splunkenabled boolean default false,
	expiredate timestamp without time zone,
	calculateNextScanDate BOOLEAN DEFAULT TRUE,
	outscanWasXApps BIGINT DEFAULT 0,
	emailencryptionkey varchar(255),
	sshpublickey TEXT,
	hiabAppsecScaleApps BIGINT DEFAULT 0 NOT NULL,
	sslclientcafile varchar(255),
	cookiesAccepted TIMESTAMP WITHOUT TIME ZONE,
	snapshotsubscriptions BIGINT DEFAULT 0,
	snapshotsubscriptionsremaining BIGINT DEFAULT 0,
	swatsubscriptions BIGINT DEFAULT 0,
	swatsubscriptionsremaining BIGINT DEFAULT 0,
	portalpreferences TEXT,
	inactivateddate TIMESTAMP WITHOUT TIME ZONE,
	farsightproducts TEXT[],
	lastlogonversion VARCHAR(50),
	hasanalytics BOOLEAN NOT NULL DEFAULT FALSE,
	rbacsetupdate TIMESTAMP WITHOUT TIME ZONE,
	salesaccountid TEXT,
	uuid UUID NOT NULL DEFAULT gen_random_uuid(),
	customersuccessmanageremail CHARACTER VARYING(150),
	accountmanageremail CHARACTER VARYING(150),
	pgppublickey TEXT
);


--
-- Name: tusers_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tusers_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

--
-- Name: tusersettings; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tuserSettings(
	xuserXid BIGINT,
	acceptedLength BIGINT,
	acceptTargets INTEGER,
	acceptComment TEXT
);

ALTER TABLE ONLY tusersettings ADD CONSTRAINT tusersettings_pkey PRIMARY KEY (xuserxid);

CREATE SEQUENCE tawsarns_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tawsarns (
	xid BIGINT PRIMARY KEY,
	xuserxid BIGINT,
	name TEXT,
	arn TEXT
);

--
-- Name: tvultexts; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tvultexts (
	xid bigint,
	dupdated timestamp without time zone,
	vcname character varying(256),
	vcfam character varying(50),
	cdesc text,
	csol text,
	irisk smallint,
	dcreated timestamp without time zone,
	vccve character varying(4096),
	vcbug character varying(4096),
	icrc character varying(40),
	xuserxid bigint DEFAULT -1,
	icvss bigint,
	vccvssvector character varying(64),
	xcreator bigint,
	xupdator bigint,
	ipcicvss smallint,
	external boolean default false,
	scriptcreated timestamp without time zone,
	solutionType smallint,
	solutionProduct varchar(255),
	solutionTitle varchar(255),
	was_informational boolean,
	was_falsepos boolean,
	hasexploits integer,
	packageDetection TEXT[]
);


--
-- Name: tvultexts_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tvultexts_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tvultextscomments; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tvultextscomments (
	xid bigint,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	scriptid bigint,
	"comment" text,
	public smallint default 0,
	dcreated timestamp without time zone,
	dupdated timestamp without time zone,
	dapproved timestamp without time zone,
	findingid bigint,
	parentcomment bigint,
	readBy BIGINT[]
);

CREATE SEQUENCE tvultextscomments_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tvulxrefs; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tvulxrefs (
	xid bigint NOT NULL,
	vctype character varying(32),
	vcxref character varying(512),
	iid bigint
);


--
-- Name: tvulxrefs_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tvulxrefs_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TYPE exploitsource AS ENUM ('UNKNOWN', 'CORE', 'IMMUNITY', 'EXPLOIT_DB', 'D_SQUARE', 'CONTAGIO', 'METASPLOIT', 'SAINT', 'SECURITY_FOCUS', 'SNORT', 'FARSIGHT');

CREATE SEQUENCE texploits_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE texploits (
	id bigint NOT NULL,
	type smallint DEFAULT 0,
	cve varchar(100),
	name text,
	created timestamp without time zone,
	identifier text,
	pack varchar(255),
	scriptid BIGINT,
	approved boolean
);

ALTER TABLE texploits ADD CONSTRAINT texploits_pkey PRIMARY KEY (id);

--
-- Name: tworkflowmsgs; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tworkflowmsgs (
	xid bigint DEFAULT nextval('seq_workflowmsgs'::regclass) NOT NULL,
	xuserxid bigint NOT NULL,
	taskid bigint NOT NULL,
	message character varying(2048),
	dcreated timestamp without time zone DEFAULT now(),
	xcreator bigint
);


--
-- Name: tworkflows; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tworkflows (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	taskid bigint,
	"type" smallint,
	dcreated timestamp without time zone,
	priority smallint,
	status smallint,
	name character varying(400),
	duedate timestamp without time zone,
	dupdated timestamp without time zone,
	ipaddress character varying(255),
	ipvalue bigint,
	xupdator bigint,
	xcreator bigint,
	isEscalated smallint default 0 not null,
	iport bigint,
	iprotocol bigint,
	vcvulnid bigint,
	xipxid bigint,
	scanjob bigint,
	includevulinfo boolean DEFAULT false,
	findingid bigint DEFAULT 0
);


--
-- Name: tworkflows_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE tworkflows_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: tworkflowsequence; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE tworkflowsequence (
	xid bigint NOT NULL,
	seq bigint NOT NULL
);


--
-- Name: userver_certificate_serial_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE userver_certificate_serial_seq
	START WITH 1
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: usrv_error_log_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE usrv_error_log_seq
	START WITH 1
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: userver_error_log; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE userver_error_log (
	id bigint DEFAULT nextval('usrv_error_log_seq'::regclass) NOT NULL,
	hiabstatusid bigint NOT NULL,
	emsg text NOT NULL,
	tstamp bigint NOT NULL
);

CREATE TABLE userver_enroll_error (
	TSTAMP TIMESTAMP WITHOUT TIME ZONE,
	EMSG TEXT,
	ASSIGNEE BIGINT
);


--
-- Name: userver_update_log; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE userver_update_log (
	hwaddr character varying(128) NOT NULL,
	"time" timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: xlinksubusergroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinksubusergroups (
	xsubxid bigint NOT NULL,
	xgroupxid bigint NOT NULL
);


--
-- Name: xlinkgroup; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinkgroup (
	xid bigint NOT NULL,
	xvcapp character varying(20) NOT NULL,
	xvcgroup character varying(20) NOT NULL,
	vcoption character varying(4000),
	vcname character varying(20) DEFAULT '-'::character varying NOT NULL,
	xtime timestamp without time zone
);


CREATE TABLE xlinkrules (
	xid BIGINT,
	field VARCHAR(200),
	value TEXT,
	operator VARCHAR(20),
	definedQuery TEXT,
	dynamic boolean default false
);

--
-- Name: xlinkpayloads_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE xlinkpayloads_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;


--
-- Name: xlinkuser; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinkuser (
	xid bigint NOT NULL,
	xvcapp character varying(20) NOT NULL,
	xuserid bigint NOT NULL
);


--
-- Name: xlinkgeneric; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinkgeneric (
	xid bigint NOT NULL,
	ipaddress inet,
	xipxid bigint not null DEFAULT 0,
	hostnameid bigint
);


--
-- Name: xlinkloggings; Type: TABLE; Schema: public; Owner: -; Tablespace:
--
CREATE TABLE xlinkloggings (
	xid bigint,
	hostnameid bigint,
	ipaddress inet,
	endipaddress inet,
	targets varchar(255)
);


--
-- Name: xlinklogginggroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--
CREATE TABLE xlinklogginggroups (
	xid bigint,
	groupxid bigint
);

CREATE TABLE xlinkloggingswat (
	xid bigint,
	swatid bigint
);

--
-- Name: xlinksavedscanpref; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinksavedscanpref (
	xid bigint NOT NULL,
	vcid character varying(50) NOT NULL,
	itype smallint NOT NULL
);


--
-- Name: xlinkscheduleobject; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinkscheduleobject (
	xid bigint NOT NULL,
	itype smallint NOT NULL,
	vctarget character varying(255) NOT NULL,
	hostnameid bigint,
	xgroupxid bigint,
	ipaddress inet,
	endipaddress inet,
	discoverycount bigint,
	scannerid bigint DEFAULT -1 NOT NULL,
	paths bigint[],
	comment text
);


--
-- Name: xlinksubgroups; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinksubgroups (
	xsubxid bigint NOT NULL,
	xgroupxid bigint NOT NULL
);

--
-- Name: xlinksubscanners; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinksubscanners (
	xsubxid bigint NOT NULL,
	scannerid bigint NOT NULL
);

CREATE TABLE xlinksubswat (
	xsubxid bigint NOT NULL,
	swatid bigint NOT NULL
);

CREATE TABLE xlinksubawsarn (
	xsubxid bigint NOT NULL,
	awsarnid bigint NOT NULL
);

--
-- Name: xlinksubhosts; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE xlinksubhosts (
	xsubxid bigint NOT NULL,
	vctarget character varying(255) NOT NULL,
	hostnameid bigint,
	ipaddress inet,
	endipaddress inet
);


-- Name: patches; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE patches (
	id character varying(256),
	xtime timestamp without time zone
);

ALTER TABLE patches ADD CONSTRAINT patches_pkey PRIMARY KEY (id);

create table xlinkmanagedservices (
	xsubuserxid bigint,
	reportid bigint,
	REPORTGROUPID BIGINT default 0,
	emailnotification BOOLEAN NOT NULL DEFAULT TRUE
);


--
-- Name: revision; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE revision (
	rev character varying(256)
);

ALTER TABLE revision ADD CONSTRAINT revision_pkey PRIMARY KEY (rev);

CREATE TABLE ttranslations(
	key VARCHAR(256),
	value TEXT,
	language VARCHAR(20),
	valid SMALLINT,
	ignore SMALLINT default 0 NOT NULL,
	type SMALLINT,
	UNIQUE (key, language),
	updated timestamp,
	locationHint TEXT,
	active smallint,
	translations VARCHAR[]
);

CREATE TABLE ttranslationlanguages(
	language VARCHAR(20) UNIQUE
);

CREATE TABLE ttranslationvulns(
	ruleId BIGINT default 0,
	type SMALLINT default 0,
	product VARCHAR(100),
	key VARCHAR(256),
	language VARCHAR(20),
	value TEXT
);

-- This constraint is only for Outscan, will cause errors on HIAB
--ALTER TABLE ONLY ttranslationvulns ADD CONSTRAINT ttranslationvulns_key FOREIGN KEY (key, language) REFERENCES ttranslations(key, language) ON DELETE CASCADE;

CREATE SEQUENCE hostids_seq
	START WITH 5000000000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

create table hostids (
	id bigint not null,
	hostname varchar(255) not null
);

CREATE TABLE deletetargets(
	xid BIGINT,
	xuserxid BIGINT,
	hostnameid BIGINT,
	ipaddress INET,
	hostname CHARACTER VARYING(256),
	pci SMALLINT,
	deletenote TEXT,
	subuserxid BIGINT,
	consultancyxid BIGINT,
	aws_instance_id VARCHAR(255),
	snsysid TEXT,
	snname TEXT,
	agentid TEXT,
	deleted TIMESTAMP WITHOUT TIME ZONE
);

ALTER TABLE ONLY hostids ADD CONSTRAINT hostids_pkey PRIMARY KEY (hostname);

CREATE TABLE treporttemplates (
	xid BIGINT PRIMARY KEY,
	xuserxid BIGINT NOT NULL,
	xsubuserxid BIGINT,
	name VARCHAR(100) NOT NULL,
	ispublic smallint NOT NULL DEFAULT 0,
	state TEXT,
	serverfilter TEXT,
	targets TEXT,
	targetGroups TEXT,
	schedulexid BIGINT,
	compliancepolicy bigint,
	scantype SMALLINT NOT NULL,
	comment text
);

CREATE SEQUENCE treporttemplates_seq START WITH 1000;

CREATE TABLE tgridviews (
	xid BIGINT PRIMARY KEY,
	xuserxid BIGINT NOT NULL,
	xsubuserxid BIGINT,
	name VARCHAR(100) NOT NULL,
	stateid VARCHAR(100) NOT NULL,
	state TEXT
);
ALTER TABLE tgridviews ADD CONSTRAINT gridview_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE tgridviews_seq START WITH 1000;

CREATE TABLE treportschedules (
	xid BIGINT PRIMARY KEY,
	xuserxid BIGINT NOT NULL,
	xsubuserxid BIGINT,
	name VARCHAR(100) NOT NULL,
	frequency SMALLINT NOT NULL DEFAULT 10,
	nextdate timestamp without time zone,
	latestdate timestamp without time zone,
	lastdate timestamp without time zone,
	dayweekmonth SMALLINT NOT NULL DEFAULT 0,
	reportType SMALLINT NOT NULL,
	period SMALLINT NOT NULL,
	length SMALLINT NOT NULL DEFAULT 1,
	format SMALLINT NOT NULL DEFAULT 0,
	recipient bigint[] DEFAULT ARRAY[]::bigint[],
	recipientEmail text,
	encryptionkey VARCHAR(255),
	reportTemplate BIGINT,
	targets TEXT,
	targetgroups TEXT,
	managedreportgroup BIGINT,
	managedreporttitle TEXT,
	managedreporttoken TEXT,
	scantype SMALLINT NOT NULL,
	recipienttype SMALLINT DEFAULT 0,
	reportpassword VARCHAR(150),
	targetgroupdepth SMALLINT,
	includehostinfo SMALLINT,
	reportLevel SMALLINT DEFAULT 0,
	sendemptyreports SMALLINT DEFAULT 0,
	recipientdir VARCHAR(100),
	xsoxid bigint,
	includepolicy smallint default 0,
	customsubject VARCHAR(200),
	customtext TEXT,
	comment TEXT,
	zip BOOLEAN default false
);

CREATE SEQUENCE treportschedules_seq START WITH 1000;

CREATE TABLE tdiscoveryresults (
	xsoxid bigint,
	xscanjobxid bigint,
	xuserxid bigint NOT NULL,
	host character varying(255),
	alive smallint default 0,
	added smallint default 0,
	previousadded smallint default 0,
	macaddress varchar(18),
	ipaddress varchar(255),
	hostname TEXT,
	netbios varchar(255),
	instanceid varchar(255),
	agentid text,
	trigger varchar(50),
	delta smallint default 0
);
ALTER TABLE ONLY tdiscoveryresults ADD CONSTRAINT discoveryresults_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE acceptedrisks (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	vulnid bigint,
	port bigint,
	protocol bigint,
	acceptdate timestamp without time zone,
	acceptedlength integer DEFAULT (0),
	acceptcomment text,
	acceptedby bigint,
	xipxid bigint NOT NULL DEFAULT (-1),
	groups bigint[] DEFAULT ARRAY[]::bigint[]
);

ALTER TABLE ONLY acceptedrisks ADD CONSTRAINT acceptedrisks_pkey PRIMARY KEY (xid);

CREATE SEQUENCE acceptedrisks_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tuseddownloadkeys (
	xuserxid bigint,
	time bigint
);

CREATE TABLE xgenericpaths(
	parentxid BIGINT NOT NULL,
	xid BIGINT NOT NULL
);

CREATE TABLE tworkflowsettings (xuserxid BIGINT PRIMARY KEY,
	p1days integer,
	p2days integer,
	p3days integer,
	p4days integer,
	p5days integer,
	autoclosetickets smallint default 1,
	includevulinfo boolean DEFAULT false,
	overduealert boolean default false,
	pactive smallint default 31,
	p1label character varying(50),
	p2label character varying(50),
	p3label character varying(50),
	p4label character varying(50),
	p5label character varying(50)
);

CREATE SEQUENCE tverifys_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tverifys (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	treport_vulns_xid bigint NOT NULL,
	tscanstatuss_xid bigint NOT NULL,
	xipxid bigint,
	port integer
);

CREATE SEQUENCE tverifyscans_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tverifyscans (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	pci smallint default 0,
	treport_vulns_xid bigint NOT NULL,
	nextscandate timestamp without time zone,
	verifyxid bigint NOT NULL
);

CREATE TABLE toldpasswords(
	xuserxid bigint,
	subuserxid bigint,
	password text not null,
	changed timestamp not null);

CREATE TABLE tpasswordpolicy (
	xuserxid bigint primary key,
	maximumAge smallint not null default 0,
	historyLength smallint not null default 10,
	minimumLength smallint not null default 6,
	enforceNumeric smallint not null default 0,
	enforceSpecial smallint not null default 0,
	requireChange smallint not null default 0,
	loginiprestriction text,
	csrfvalidation smallint default 1,
	ssoonly smallint default 0,
	twofactorauthentication smallint default 0
);

CREATE SEQUENCE twasdiscoveryresults_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE twasdiscoveryresults (
	xid bigint,
	xsoxid BIGINT,
	uri TEXT NOT NULL,
	outOfScope SMALLINT NOT NULL DEFAULT 0,
	status integer NOT NULL DEFAULT 0,
	was smallint,
	vhostxid bigint,
	sourceuri text,
	crawledfailed smallint,
	xuserxid bigint,
	xscanjobxid bigint,
	xipxid bigint,
	headers text,
	cookies text,
	content text,
	fuzzed smallint default 0
);

CREATE TABLE twasstatistics (
	scanlogxid bigint NOT NULL,
	xuserxid bigint,
	key character varying(50),
	value character varying(50)
);

create table tattributes (
	xid bigint primary key,
	columnid bigint,
	xuserxid bigint,
	name varchar(256),
	bactive smallint default 0,
	onuser smallint default 0,
	target smallint default 0,
	reporting smallint default 0,
	scheduling smallint default 0,
	vulnerability boolean default false,
	finding boolean default false,
	required smallint default 0,
	fieldType smallint default 0,
	acceptableValues text,
	exportreport smallint default 1
);

CREATE SEQUENCE tattributes_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

ALTER TABLE xgenericpaths ADD CONSTRAINT genericpaths_pkey primary key (parentxid, xid);

create table tmsspinfo (
	xuserxid bigint,
	day date,
	hiabscans bigint default 0 not null,
	hiabwebappscans bigint default 0 not null,
	externalscans bigint default 0 not null,
	externalwebappscans bigint default 0 not null,
	outscanscans bigint default 0 not null,
	outscanwebappscans bigint default 0 not null,
	pciscans bigint default 0 not null,
	hiabtargets bigint default 0 not null,
	hiabwebapps bigint default 0 not null,
	externaltargets bigint default 0 not null,
	externalwebapps bigint default 0 not null,
	outscantargets bigint default 0 not null,
	outscanwebapps bigint default 0 not null,
	pcitargets bigint default 0 not null,
	internalscans BIGINT DEFAULT 0 NOT NULL,
	internaltargets BIGINT DEFAULT 0 NOT NULL,
	scalescans BIGINT DEFAULT 0 NOT NULL,
	scaletargets BIGINT DEFAULT 0 NOT NULL,
	scaleinternalscans BIGINT DEFAULT 0 NOT NULL,
	scaleinternaltargets BIGINT DEFAULT 0 NOT NULL,
	compliancescans BIGINT DEFAULT 0 NOT NULL,
	hiabcompliancescans BIGINT DEFAULT 0 NOT NULL,
	hiabscalescans BIGINT DEFAULT 0 NOT NULL,
	hiabscaletargets BIGINT DEFAULT 0 NOT NULL,
	scaleexternalscans BIGINT DEFAULT 0 NOT NULL,
	scaleexternaltargets BIGINT DEFAULT 0 NOT NULL,
	uploaded smallint default 0 not null
);

CREATE TABLE tmanagedreports(
	xid bigint primary key,
	xuserxid bigint,
	name varchar(256),
	title varchar(256),
	date timestamp without time zone,
	latestdownload timestamp without time zone,
	REPORTGROUPID BIGINT default 0,
	uuid UUID
);

CREATE SEQUENCE tmanagedreports_seq
	START WITH 100
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tmanagedreportgroups (
	xid bigint PRIMARY KEY NOT NULL,
	xuserxid bigint NOT NULL,
	name character varying(200),
	xiparentid bigint default -1,
	xpathup bigint[] default ARRAY[]::bigint[],
	xpathdown bigint[] default ARRAY[]::bigint[],
	dupdated timestamp without time zone,
	dcreated timestamp without time zone
);

CREATE SEQUENCE tmanagedreportgroup_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tmanagedreportscomment(
	xid bigint primary key,
	reportxid bigint,
	author bigint,
	comment text,
	date timestamp without time zone
);

CREATE SEQUENCE tmanagedreportscomment_seq
	START WITH 100
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

ALTER TABLE ONLY tmanagedreportscomment ADD CONSTRAINT tmanagedreportscomment_xid_fkey FOREIGN KEY (reportxid) REFERENCES tmanagedreports(xid) ON DELETE CASCADE;

CREATE TABLE tmanagedservicesgrants(
	xuserxid bigint,
	serviceuser bigint
);

create table triskchanges (
	xuserxid bigint,
	xipxid bigint,
	vcvulnid bigint,
	level smallint
);

create table consultancytokens (
	token text,
	owner bigint,
	userid bigint,
	created timestamp without time zone,
	subuserid bigint
);

CREATE TABLE twaslatestdiscovery(
	xipxid bigint,
	xsoxid bigint,
	was smallint,
	xuserxid bigint,
	xscanjobxid bigint
);

ALTER TABLE tmsspinfo ADD CONSTRAINT tmsspinfo_pkey PRIMARY KEY (xuserxid, day);

create table temailsignatures(
	xuserxid bigint,
	signature text
);

create table hiabinfo(scheduler bigint, revoked BOOLEAN NOT NULL DEFAULT FALSE);

CREATE SEQUENCE treportfiles_seq START WITH 1000;

CREATE TABLE treportfiles(
	id BIGINT PRIMARY KEY,
	userId BIGINT NOT NULL,
	findingId BIGINT NOT NULL,
	type SMALLINT DEFAULT 0,
	name VARCHAR(255),
	fileName VARCHAR(255),
	content TEXT
);

CREATE TABLE treportfiletokens(
	token TEXT NOT NULL,
	userid BIGINT NOT NULL,
	subuserid BIGINT NOT NULL,
	expire TIMESTAMP NOT NULL,
	fileid BIGINT NOT NULL
);

ALTER TABLE treportfiletokens ADD CONSTRAINT treportfiletokens_fileid FOREIGN KEY (fileid) REFERENCES treportfiles(id) ON DELETE CASCADE;

CREATE SEQUENCE treporthistory_seq START WITH 1000;

CREATE TABLE treporthistory (
	id bigint PRIMARY KEY,
	dcreated timestamp without time zone,
	xcreator bigint,
	findingId bigint,
	vcvulnid bigint,
	customName TEXT,
	customDescription TEXT,
	customCve TEXT,
	customCwe INTEGER,
	customBugtraq TEXT,
	customVulnerabilitytype VulnerabilityType,
	customSolutionType SMALLINT,
	customSolutionTitle TEXT,
	customSolution TEXT,
	iport bigint,
	vcscvssvector character varying(128),
	iscvss smallint,
	irisk bigint,
	dfirstseen timestamp without time zone,
	dlastseen timestamp without time zone,
	cdata text,
	recreationflow text,
	explanation text,
	externalTicket VARCHAR(100),
	cvssv3score DECIMAL(10, 1),
	cvssv3vector VARCHAR(100)
);

CREATE TABLE tauthentications(
	xipxid BIGINT,
	type smallint default 0,
	sshusername VARCHAR(100),
	sshpassword TEXT,
	sshpublickey VARCHAR(255),
	sshprivatekey VARCHAR(255),
	sshprivatekeypassword TEXT,
	sshsubstituteuser VARCHAR(10),
	smbusername VARCHAR(100),
	smbpassword TEXT,
	smbdomain VARCHAR(100),
	smbntlmv1 SMALLINT,
	registrydump VARCHAR(255),
	result smallint,
	authenticationdate timestamp without time zone,
	resultLevel smallint default 0,
	enableRemoteRegistry boolean default false,
	sshsudocommand VARCHAR(255),
	cyberarkname TEXT,
	cyberarkoverridesafe TEXT,
	cyberarkoverridefolder TEXT,
	vsphereusername TEXT,
	vspherepassword TEXT,
	ignorecerts boolean default false,
	credentialproviderid BIGINT,
	thycoticsecretname TEXT,
	thycoticpath TEXT
);

ALTER TABLE tauthentications ADD CONSTRAINT authentication_pkey PRIMARY KEY (xipxid, type);

CREATE TABLE tauthenticationresults(xid bigint primary key, success smallint, reason text, time timestamp without time zone);

CREATE TYPE specialNoteType AS ENUM ('NONE', 'BROWSE_DIRECTORIES', 'LOAD_BALANCER', 'REMOTE_ACCESS', 'POINT_OF_SALE', 'UNKNOWN_SERVICE', 'EMBEDDED_LINKS', 'ANONYMOUS_PROTOCOLS', 'INSECURE_SERVICES', 'PAYMENT_PAGE');

CREATE SEQUENCE trules_seq START WITH 300000;
CREATE SEQUENCE trulesdef_seq START WITH 1000;

CREATE TABLE trules (
	ruleid BIGINT PRIMARY KEY,
	name TEXT NOT NULL,
	pciFail boolean DEFAULT FALSE,
	potentialFalsePositive boolean DEFAULT FALSE,
	requiredProduct TEXT[],
	cve TEXT,
	bugtraq TEXT,
	family TEXT NOT NULL,
	protocol SMALLINT,
	booleanMethod SMALLINT,
	description TEXT NOT NULL,
	solutionType SMALLINT,
	solution TEXT,
	solutionProduct TEXT,
	solutionTitle TEXT,
	cvssVector VARCHAR(50) NOT NULL,
	gatheredInformationText TEXT,
	comment TEXT,
	created TIMESTAMP WITHOUT TIME ZONE NOT NULL,
	updated TIMESTAMP WITHOUT TIME ZONE NOT NULL,
	reviewed TIMESTAMP WITHOUT TIME ZONE,
	reviewer BIGINT,
	creator BIGINT,
	updatedby BIGINT,
	deleted BOOLEAN,
	runForAdmins BOOLEAN,
	cvssScore FLOAT,
	riskLevel SMALLINT,
	product TEXT,
	hasExploits BOOLEAN DEFAULT FALSE,
	isScript BOOLEAN DEFAULT FALSE NOT NULL,
	WAS_INFORMATIONAL BOOLEAN,
	WAS_FALSEPOS BOOLEAN,
	specialNote specialNoteType NOT NULL DEFAULT 'NONE',
	specialNotes specialNoteType[],
	informational BOOLEAN DEFAULT FALSE NOT NULL,
	vulnerabilitytype VulnerabilityType NOT NULL DEFAULT 'Unknown',
	nvdcvssvector varchar(50),
	overridecomment text,
	nvdcvssscore float,
	patchreleased date,
	osvdb TEXT,
	reviewneeded BOOLEAN DEFAULT FALSE,
	allproductsrequired BOOLEAN NOT NULL DEFAULT FALSE,
	autorules SMALLINT,
	lastnotification TIMESTAMP WITHOUT TIME ZONE,
	cvssV3Score DECIMAL(10, 1),
	cvssV3Vector VARCHAR(100),
	released TIMESTAMP WITHOUT TIME ZONE,
	readyforreview BOOLEAN,
	cwe INTEGER,
	operation SMALLINT,
	cyrating DECIMAL(10, 2) NOT NULL DEFAULT 1.0,
	exploitprobability DECIMAL(10, 3) NOT NULL DEFAULT 0.026,
	previouscyrating DECIMAL(10, 2) NOT NULL DEFAULT 1.0,
	previousexploitprobability DECIMAL(10, 3) NOT NULL DEFAULT 0.026,
	cyr3conupdated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	cyratinglastseen TIMESTAMP WITHOUT TIME ZONE,
	productid INTEGER,
	bluelivupdated TIMESTAMP WITHOUT TIME ZONE,
	bluelivscore FLOAT,
	bluelivdelta FLOAT,
	bluelivlastthreatactivity TIMESTAMP WITHOUT TIME ZONE,
	bluelivmentions INTEGER DEFAULT 0 NOT NULL,
	bluelivthreatactors INTEGER DEFAULT 0 NOT NULL,
	bluelivexploits INTEGER DEFAULT 0 NOT NULL,
	bluelivtheatresofoperation TEXT[],
	farsight JSONB
);

CREATE TABLE trulesdef (
	id BIGINT PRIMARY KEY,
	ruleid BIGINT,
	conditionKey TEXT,
	conditionType TEXT,
	comparisonType SMALLINT,
	conditionValue TEXT,
	conditionValue2 TEXT,
	conditionPath TEXT,
	booleanMethod SMALLINT,
	parent BIGINT,
	report BOOLEAN,
	product TEXT,
	gatheredInformationText TEXT,
	checkPortExists TEXT,
	loopFactKey TEXT,
	loopFactType TEXT,
	informational BOOLEAN DEFAULT FALSE NOT NULL,
	evaluatesameport BOOLEAN default false,
	evaluateallchildren BOOLEAN,
	potentialFalsePositive BOOLEAN DEFAULT FALSE,
	hidereport BOOLEAN
);

CREATE TABLE truleshistory (
	id BIGINT PRIMARY KEY,
	ruleid BIGINT,
	historydate TIMESTAMP WITHOUT TIME ZONE,
	name TEXT,
	pciFail boolean,
	potentialFalsePositive boolean,
	requiredProduct TEXT[],
	cve TEXT,
	bugtraq TEXT,
	service TEXT,
	family TEXT,
	protocol SMALLINT,
	booleanMethod SMALLINT,
	description TEXT,
	solutionType SMALLINT,
	solution TEXT,
	solutionProduct TEXT,
	solutionTitle TEXT,
	cvssVector VARCHAR(50),
	gatheredInformationText TEXT,
	comment TEXT,
	created TIMESTAMP WITHOUT TIME ZONE,
	updated TIMESTAMP WITHOUT TIME ZONE,
	reviewed TIMESTAMP WITHOUT TIME ZONE,
	reviewer BIGINT,
	creator BIGINT,
	updatedby BIGINT,
	deleted BOOLEAN,
	runForAdmins BOOLEAN,
	cvssScore FLOAT,
	riskLevel SMALLINT,
	specialNote specialNoteType NOT NULL DEFAULT 'NONE',
	specialNotes specialNoteType[],
	product TEXT,
	isScript BOOLEAN DEFAULT FALSE NOT NULL,
	WAS_INFORMATIONAL BOOLEAN,
	WAS_FALSEPOS BOOLEAN,
	informational BOOLEAN DEFAULT FALSE NOT NULL,
	vulnerabilitytype VulnerabilityType NOT NULL DEFAULT 'Unknown',
	nvdcvssvector varchar(50),
	overridecomment text,
	nvdcvssscore float,
	patchreleased date,
	reviewneeded BOOLEAN DEFAULT FALSE,
	cvssV3Score DECIMAL(10, 1),
	cvssV3Vector VARCHAR(100),
	autorules SMALLINT,
	cwe INTEGER,
	operation SMALLINT,
	cyrating DECIMAL(10, 2) NOT NULL DEFAULT 1.0,
	exploitprobability DECIMAL(10, 3) NOT NULL DEFAULT 0.026,
	previouscyrating DECIMAL(10, 2) NOT NULL DEFAULT 1.0,
	previousexploitprobability DECIMAL(10, 3) NOT NULL DEFAULT 0.026,
	cyr3conupdated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	cyratinglastseen TIMESTAMP WITHOUT TIME ZONE,
	bluelivupdated TIMESTAMP WITHOUT TIME ZONE,
	bluelivscore FLOAT,
	bluelivdelta FLOAT,
	bluelivlastthreatactivity TIMESTAMP WITHOUT TIME ZONE,
	bluelivmentions INTEGER DEFAULT 0 NOT NULL,
	bluelivthreatactors INTEGER DEFAULT 0 NOT NULL,
	bluelivexploits INTEGER DEFAULT 0 NOT NULL,
	farsight JSONB
);

CREATE TABLE trulesdefhistory (
	id BIGINT PRIMARY KEY,
	rulehistoryid BIGINT,
	ruleid BIGINT,
	conditionKey TEXT,
	conditionType TEXT,
	comparisonType SMALLINT,
	conditionValue TEXT,
	conditionValue2 TEXT,
	conditionPath TEXT,
	booleanMethod SMALLINT,
	parent BIGINT,
	report BOOLEAN,
	product TEXT,
	gatheredInformationText TEXT,
	checkPortExists TEXT,
	loopFactKey TEXT,
	loopFactType TEXT,
	informational BOOLEAN DEFAULT FALSE NOT NULL,
	evaluatesameport boolean default false,
	evaluateallchildren BOOLEAN,
	potentialFalsePositive boolean DEFAULT FALSE,
	hidereport BOOLEAN,
	oldid BIGINT
);

CREATE SEQUENCE truleshistory_seq START WITH 300000;
CREATE SEQUENCE trulesdefhistory_seq START WITH 1000;

CREATE TABLE trulesissues (
	ruleid BIGINT,
	updated TIMESTAMP WITHOUT TIME ZONE,
	issue TEXT
);

CREATE TABLE trulesissueshistory (
	rulehistoryid BIGINT,
	ruleid BIGINT,
	updated TIMESTAMP WITHOUT TIME ZONE,
	issue TEXT
);

CREATE TABLE tvulxrefshistory (
	rulehistoryid BIGINT,
	vctype character varying(32),
	vcxref character varying(512),
	iid bigint
);

CREATE SEQUENCE truleslog_seq START WITH 1000;

CREATE TABLE truleslog (
	id BIGINT PRIMARY KEY,
	type SMALLINT NOT NULL,
	log TEXT,
	updated TIMESTAMP WITHOUT TIME ZONE,
	identifier TEXT,
	status SMALLINT DEFAULT 0
);

CREATE TABLE treportsolutions(userXid BIGINT,
	targetXid BIGINT,
	scannerid BIGINT,
	solutionTitle TEXT,
	solutionType SMALLINT,
	product TEXT,
	lowrisk BIGINT,
	mediumrisk BIGINT,
	highrisk BIGINT,
	translationVulnId BIGINT
);

CREATE TABLE tproductInformation(
	xid BIGINT PRIMARY KEY,
	product TEXT,
	name TEXT,
	productEndOfLife DATE,
	latestServicePacks TEXT[],
	solutionVersions TEXT[],
	versionEndOfLife TEXT[],
	branches TEXT[],
	parser TEXT,
	parserOptions INTEGER,
	partOrder SMALLINT[],
	parserDelimiters TEXT,
	updateSolutionTitle TEXT,
	updateSolutionText TEXT,
	patchesImpliedByVersion BOOLEAN NOT NULL DEFAULT FALSE,
	productUrl VARCHAR(1024),
	normalizerulename BOOLEAN DEFAULT FALSE,
	nvdTags TEXT[],
	blackListedCVE TEXT[],
	comment TEXT,
	compliance BOOLEAN DEFAULT true,
	backported TEXT[],
	productchain TEXT[],
	updated TIMESTAMP WITHOUT TIME ZONE,
	updatedby BIGINT,
	signaturekeys TEXT[]
);

CREATE SEQUENCE tproductinformation_seq START WITH 1000;

CREATE TABLE tproductinformationhistory (
	historyxid BIGINT PRIMARY KEY,
	historydate TIMESTAMP WITHOUT TIME ZONE,
	xid BIGINT,
	product TEXT,
	name TEXT,
	productEndOfLife DATE,
	latestServicePacks TEXT[],
	solutionVersions TEXT[],
	versionEndOfLife TEXT[],
	branches TEXT[],
	parser TEXT,
	parserOptions INTEGER,
	partOrder SMALLINT[],
	parserDelimiters TEXT,
	updateSolutionTitle TEXT,
	updateSolutionText TEXT,
	patchesImpliedByVersion BOOLEAN NOT NULL DEFAULT FALSE,
	productUrl VARCHAR(1024),
	normalizerulename BOOLEAN DEFAULT FALSE,
	nvdTags TEXT[],
	blackListedCVE TEXT[],
	comment TEXT,
	compliance BOOLEAN DEFAULT true,
	backported TEXT[],
	productchain TEXT[],
	updated TIMESTAMP WITHOUT TIME ZONE,
	updatedby BIGINT,
	signaturekeys TEXT[]
);

CREATE SEQUENCE tproductinformationhistory_seq START WITH 1000;

CREATE TABLE tpatchsupersedence(
	xid BIGINT PRIMARY KEY,
	product TEXT,
	patch TEXT,
	supersededby TEXT,
	dateAdded TIMESTAMP WITHOUT TIME ZONE,
	bulletinid VARCHAR(500),
	updated TIMESTAMP WITHOUT TIME ZONE,
	updatedby BIGINT,
	deleted BOOLEAN DEFAULT FALSE
);

CREATE SEQUENCE tpatchsupersedence_seq START WITH 1000;

CREATE TABLE newpatches(
	xid BIGINT PRIMARY KEY,
	patch TEXT,
	supersededby TEXT,
	bulletinid VARCHAR(500)
);

CREATE SEQUENCE newpatches_seq START WITH 1000;

CREATE TABLE tcve (
	xid BIGINT PRIMARY KEY,
	name TEXT,
	title TEXT,
	description TEXT,
	vector CHARACTER VARYING(128),
	vector3 CHARACTER VARYING(128),
	refs TEXT[],
	reviewneeded BOOLEAN DEFAULT FALSE,
	created TIMESTAMP WITHOUT TIME ZONE,
	updated TIMESTAMP WITHOUT TIME ZONE,
	updatedby BIGINT,
	products TEXT,
	status SMALLINT DEFAULT 0
);

CREATE SEQUENCE tcve_seq START WITH 1000;

CREATE TABLE tcvehistory (
	historyxid BIGINT PRIMARY KEY,
	historydate TIMESTAMP WITHOUT TIME ZONE,
	xid BIGINT,
	name TEXT,
	title TEXT,
	description TEXT,
	vector CHARACTER VARYING(128),
	vector3 CHARACTER VARYING(128),
	refs TEXT[],
	reviewneeded BOOLEAN DEFAULT FALSE,
	created TIMESTAMP WITHOUT TIME ZONE,
	updated TIMESTAMP WITHOUT TIME ZONE,
	updatedby BIGINT,
	products TEXT,
	status SMALLINT DEFAULT 0
);

CREATE SEQUENCE tcvehistory_seq START WITH 1000;

CREATE TABLE tvultextattributes (
	xid BIGINT PRIMARY KEY,
	vcvulnid BIGINT,
	xuserxid BIGINT,
	custom0 VARCHAR(1024),
	custom1 VARCHAR(1024),
	custom2 VARCHAR(1024),
	custom3 VARCHAR(1024),
	custom4 VARCHAR(1024),
	custom5 VARCHAR(1024),
	custom6 VARCHAR(1024),
	custom7 VARCHAR(1024),
	custom8 VARCHAR(1024),
	custom9 VARCHAR(1024)
);

ALTER TABLE tvultextattributes ADD CONSTRAINT tvultextattributes_vcvulnid_xuserxid_key UNIQUE (vcvulnid, xuserxid);

CREATE SEQUENCE tvultextattributes_seq START WITH 1000;

CREATE TABLE xlinkvultextfacts(
	xid BIGINT,
	key TEXT,
	type TEXT
);

CREATE SEQUENCE tcompliancepolicies_seq start with 1000;

CREATE TABLE tcompliancepolicies (
	xid bigint primary key,
	name varchar(255) NOT NULL,
	xuserxid bigint,
	global boolean not null default false,
	description text NOT NULL,
	deleted boolean not null default false,
	isgroup boolean default false,
	parent bigint default 2,
	xsubuserxid bigint default -1,
	private boolean default false,
	version varchar(100),
	targetgroups TEXT
);

ALTER TABLE tcompliancepolicies ADD CONSTRAINT unique_name UNIQUE(xuserxid, name, global, xsubuserxid, private);

CREATE SEQUENCE tcompliancescandata_seq start with 1000;

CREATE TABLE tcompliancescandata(
	xid bigint primary key,
	scanlogxid bigint,
	key text,
	type text,
	fact bytea,
	fact_type integer,
	proto text,
	port integer,
	vhost text,
	path text,
	scanjobxid bigint not null default -1,
	reportentryxid bigint not null default -1
);

CREATE SEQUENCE tcompliancefindings_seq start with 1000;

CREATE TABLE tcompliancefindings(
	xid bigint primary key,
	target text not null,
	xipxid bigint not null,
	information text,
	reportId bigint not null,
	compliant boolean,
	vcvulnid bigint not null,
	potentialfalse boolean not null default false,
	hostname text,
	netbios text,
	scannerid bigint,
	platform text,
	facts text
);

CREATE SEQUENCE tcompliancereports_seq start with 1000;

CREATE TABLE tcompliancereports(
	xid bigint primary key,
	xuserxid bigint,
	xsubuserxid bigint,
	date timestamp without time zone,
	policyid bigint,
	targetgroups bigint[],
	scanjobid bigint,
	compliant bigint,
	notcompliant bigint,
	unknowncompliance bigint
);

CREATE TABLE tcomplianceaccepted(
	vcvulnid bigint not null,
	date timestamp without time zone,
	xipxid bigint not null,
	comment text,
	xuserxid bigint not null,
	acceptforever boolean not null default false,
	acceptuntil timestamp without time zone
);

CREATE TABLE tcompliancefalsepositive(
	vcvulnid bigint not null,
	date timestamp without time zone,
	xipxid bigint not null,
	comment text,
	xuserxid bigint not null
);

CREATE SEQUENCE tcompliancerequirements_seq
	START WITH 1000
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE tcompliancerequirements (
	xid bigint NOT NULL,
	policyid bigint NOT NULL,
	parent bigint,
	name text NOT NULL,
	description text NOT NULL,
	settings text,
	orderid bigint NOT NULL,
	windowsVersion VARCHAR(100) DEFAULT 'Any',
	solution TEXT,
	report BOOLEAN DEFAULT true,
	disabled BOOLEAN,
	inherit bigint,
	fields TEXT,
	level smallint,
	requirementtype SMALLINT DEFAULT 0,
	uploadpolicy BOOLEAN DEFAULT false,
	identifier TEXT,
	precondition BOOLEAN DEFAULT false,
	oldsettings TEXT,
	oldidentifier TEXT
);

ALTER TABLE tcompliancerequirements ADD CONSTRAINT tcompliancerequirements_pkey PRIMARY KEY (xid);
ALTER TABLE tcompliancerequirements ADD CONSTRAINT tcompliancepolicy FOREIGN KEY (policyid) REFERENCES tcompliancepolicies(xid) ON DELETE CASCADE;

CREATE SEQUENCE tcompliancequestions_seq START WITH 1000;

CREATE TABLE tcompliancequestions(
	xid BIGINT PRIMARY KEY,
	xuserxid BIGINT,
	policyid BIGINT,
	requirementpolicy BIGINT,
	identifier TEXT,
	answer TEXT,
	answerdate TIMESTAMP WITHOUT TIME ZONE,
	answerby BIGINT,
	approved smallint DEFAULT -1,
	approvedate TIMESTAMP WITHOUT TIME ZONE,
	approvedby BIGINT
);

ALTER TABLE tcompliancequestions ADD CONSTRAINT tcompliancequestions_policyid FOREIGN KEY (policyid) REFERENCES tcompliancepolicies(xid) ON DELETE CASCADE;
ALTER TABLE tcompliancequestions ADD CONSTRAINT tcompliancepolicy FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE tcompliancequestionfiles_seq START WITH 1000;

CREATE TABLE tcompliancequestionfiles(
	xid BIGINT PRIMARY KEY,
	questionid BIGINT,
	name TEXT,
	data TEXT
);

ALTER TABLE tcompliancequestionfiles ADD CONSTRAINT tcompliancequestionfiles FOREIGN KEY (questionid) REFERENCES tcompliancequestions(xid) ON DELETE CASCADE;

CREATE TABLE signoffObjects (
	xid BIGINT PRIMARY KEY,
	report boolean,
	scanStart timestamp without time zone,
	scanEnd timestamp without time zone,
	scanResult integer,
	reason text,
	openPorts integer,
	scanStatus varchar(64),
	scanPercent integer,
	scanState text,
	probeId varchar(64),
	ipAddress varchar(64),
	jsonreport BOOLEAN NOT NULL DEFAULT FALSE,
	service character varying(10),
	issues TEXT
);

CREATE TABLE scanServices (
	xid BIGINT,
	vhost text,
	service varchar(64),
	port int
);

CREATE TABLE targetremediation (
	day DATE,
	xuserxid BIGINT,
	xipxid BIGINT,
	scannerid BIGINT,
	high BIGINT,
	highdays BIGINT,
	medium BIGINT,
	mediumdays BIGINT,
	low BIGINT,
	lowdays BIGINT
);

CREATE TYPE WasStatus AS ENUM ('Idle', 'Starting', 'Crawling', 'Fuzzing', 'Stopping', 'Stopped', 'Aborted', 'Paused', 'Pausing', 'Resume', 'Resuming', 'Failed', 'Finished');
CREATE TYPE WasTicketType AS ENUM ('FormFillOut', 'Discussion', 'Finding', 'Verify', 'FalsePositive', 'ScanFailure', 'AuthenticationFailure', 'NodeManagerFailure', 'UnusedFormFillOut', 'LogoutDetected', 'HostNotReachable', 'SiteChangeDetected', 'AcceptedFinding', 'UnAcceptedFinding');

CREATE SEQUENCE swatschedules_seq START WITH 1000;

CREATE TABLE swatschedules(
	id BIGINT PRIMARY KEY,
	customerid BIGINT,
	name VARCHAR(512),
	executivesummary TEXT,
	nextScanDate TIMESTAMP WITHOUT TIME ZONE,
	scanStatus WasStatus NOT NULL DEFAULT 'Idle',
	jobId BIGINT,
	scannerId BIGINT,
	scanRunning BOOLEAN NOT NULL DEFAULT FALSE,
	pauseWindowStart TIME,
	pauseWindowEnd TIME,
	latestScanDate TIMESTAMP WITHOUT TIME ZONE,
	latestScanStatus WasStatus,
	latestScanTime BIGINT default 0,
	scanStarted TIMESTAMP WITHOUT TIME ZONE,
	scanTime BIGINT default 0,
	deleted BOOLEAN NOT NULL DEFAULT FALSE,
	systemStatus TEXT,
	currentCount BIGINT[],
	latestCount BIGINT[],
	responsibleId bigint,
	responsibleName text,
	lastReviewDate TIMESTAMP WITHOUT TIME ZONE,
	nextReviewDate TIMESTAMP WITHOUT TIME ZONE,
	reviewedById bigint,
	reviewedByName text,
	findingsDraft bigint DEFAULT 0,
	findingsReview bigint DEFAULT 0,
	findingsQA bigint DEFAULT 0,
	findingsReady bigint DEFAULT 0,
	status smallint DEFAULT 0,
	parent BIGINT DEFAULT -1
);

CREATE SEQUENCE tickets_seq START WITH 1000;

CREATE TABLE tickets(
	id BIGINT PRIMARY KEY,
	customerid BIGINT,
	scheduleid BIGINT,
	created TIMESTAMP WITHOUT TIME ZONE,
	datefixed TIMESTAMP WITHOUT TIME ZONE,
	name VARCHAR(256),
	type WasTicketType,
	content TEXT,
	responseheaders TEXT,
	requestheaders TEXT,
	genericId BIGINT
);

CREATE TABLE findingtickets(
	id BIGINT PRIMARY KEY,
	requestUrl TEXT,
	requestMethod VARCHAR(100),
	requestParameters TEXT,
	responseTimes VARCHAR(100),
	auxData TEXT,
	port INTEGER,
	information TEXT,
	ruleid BIGINT,
	name TEXT,
	normalScan BOOLEAN default false,
	lastSeen TIMESTAMP WITHOUT TIME ZONE,
	filterId VARCHAR(200),
	droppedRequestsRegex TEXT,
	droppedRequestsTruncated BOOLEAN default FALSE
);

CREATE TABLE verifytickets(
	id BIGINT PRIMARY KEY,
	findingId BIGINT,
	comment TEXT,
	commentId BIGINT,
	name TEXT,
	isFalsePositive BOOLEAN
);

CREATE TABLE acceptedtickets(
	id BIGINT PRIMARY KEY,
	findingId BIGINT,
	name TEXT,
	comment TEXT,
	isRemoved BOOLEAN
);

CREATE TABLE discussiontickets(
	id BIGINT PRIMARY KEY,
	name TEXT,
	content TEXT,
	findingId BIGINT,
	commentId BIGINT
);

CREATE SEQUENCE swatlicenses_seq START WITH 1000;

CREATE TABLE swatlicenses(
	id BIGINT PRIMARY KEY,
	applicationid BIGINT,
	type INTEGER,
	startDate DATE,
	endDate DATE
);

ALTER TABLE swatschedules ADD FOREIGN KEY (customerid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tickets ADD FOREIGN KEY (scheduleid) REFERENCES swatschedules(id) ON DELETE CASCADE;
ALTER TABLE findingtickets ADD FOREIGN KEY (id) REFERENCES tickets(id) ON DELETE CASCADE;
ALTER TABLE verifytickets ADD FOREIGN KEY (id) REFERENCES tickets(id) ON DELETE CASCADE;
ALTER TABLE acceptedtickets ADD FOREIGN KEY (id) REFERENCES tickets(id) ON DELETE CASCADE;
ALTER TABLE discussiontickets ADD FOREIGN KEY (id) REFERENCES tickets(id) ON DELETE CASCADE;
ALTER TABLE swatlicenses ADD FOREIGN KEY (applicationid) REFERENCES swatschedules(id) ON DELETE CASCADE;

CREATE SEQUENCE updatelog_seq;

CREATE TABLE updatelog(
	id BIGINT DEFAULT NEXTVAL('updatelog_seq'),
	message TEXT,
	additionaldata TEXT,
	xuserxid BIGINT,
	subjectkeyid TEXT,
	at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE hiabmanagementtokens (
	id BIGINT PRIMARY KEY,
	userId BIGINT,
	name VARCHAR(200),
	ipaddress VARCHAR(200),
	port INTEGER DEFAULT 443,
	managementtoken VARCHAR(300)
);

CREATE SEQUENCE hiabmanagementtokens_seq START WITH 1000;

CREATE TABLE attackerInfo (
	id BIGINT PRIMARY KEY,
	attackerUuid VARCHAR(200),
	name VARCHAR(200),
	status smallint DEFAULT 1,
	ipaddress VARCHAR(200)
);

ALTER TABLE ONLY hiabmanagementtokens ADD CONSTRAINT hiabmanagementtokens_key FOREIGN KEY (userId) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE attackerInfo_seq START WITH 50;

CREATE SEQUENCE ttargetdatabases_seq START WITH 1000;

CREATE TABLE ttargetdatabases (
	id BIGINT PRIMARY KEY,
	target BIGINT NOT NULL,
	type SMALLINT DEFAULT 0,
	database VARCHAR(200),
	port INTEGER,
	username TEXT,
	password TEXT
);

CREATE SEQUENCE blacklistedtokens_seq START WITH 1000;

CREATE TABLE blacklistedtokens (
	id BIGINT NOT NULL PRIMARY KEY DEFAULT nextval('blacklistedtokens_seq'),
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	subuserid BIGINT DEFAULT -1,
	token TEXT NOT NULL,
	expires TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

CREATE SEQUENCE schedules_seq START WITH 1000;

CREATE TABLE schedules(id BIGINT PRIMARY KEY DEFAULT nextval('schedules_seq'),
	userId BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	subuserId BIGINT DEFAULT -1,
	name VARCHAR(250) NOT NULL,
	createdAt TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	createdBy BIGINT,
	updatedAt TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	updatedBy BIGINT,
	scannerId BIGINT NOT NULL DEFAULT 0,
	frequency SMALLINT NOT NULL DEFAULT 10,
	gmtOffset DECIMAL(10, 2),
	timezone VARCHAR(100) NOT NULL DEFAULT 'Etc/UTC',
	dayWeekMonth SMALLINT,
	nextScanDate TIMESTAMP WITHOUT TIME ZONE,
	deleted BOOLEAN DEFAULT FALSE,
	comment TEXT,
	latestScanStatus BIGINT,
	latestScanDate TIMESTAMP WITHOUT TIME ZONE,
	latestScanDuration INTERVAL,
	totalScanDuration INTERVAL,
	finalScanDate TIMESTAMP WITHOUT TIME ZONE,
	maxScanTimeMinutes INTEGER NOT NULL,
	totalScans INTEGER NOT NULL DEFAULT 0,
	lastReportIds BIGINT[] DEFAULT ARRAY[]::BIGINT[],
	template BIGINT NOT NULL,

	nrOfHighVulns BIGINT DEFAULT 0,
	nrOfMediumVulns BIGINT DEFAULT 0,
	nrOfLowVulns BIGINT DEFAULT 0,

	-- Normal & WAS
	scanwindows INTEGER NOT NULL DEFAULT 1,
	scanwindowdelay INTEGER NOT NULL DEFAULT 1,

	-- Normal
	scanjobcount bigint,
	scancount bigint DEFAULT 0,
	concurrentscans INTEGER DEFAULT 0,
	scanmode SMALLINT DEFAULT 2,
	ignoretargetlist TEXT,
	useglobalignoretargetlist SMALLINT DEFAULT 0,
	dnslookup BOOLEAN DEFAULT TRUE,
	netbioslookup BOOLEAN DEFAULT TRUE,
	cvss_sr_avail VARCHAR(2) DEFAULT 'ND',
	cvss_sr_integ VARCHAR(2) DEFAULT 'ND',
	cvss_sr_conf VARCHAR(2) DEFAULT 'ND',
	cvss_cdp VARCHAR(2) DEFAULT 'ND',
	cvss_td VARCHAR(2) DEFAULT 'ND',
	disableprotocol SMALLINT DEFAULT 0,
	addtogroupxid BIGINT,
	emptytargetgroup BOOLEAN,
	ports TEXT,
	targetcustom0 VARCHAR(1024),
	targetcustom1 VARCHAR(1024),
	targetcustom2 VARCHAR(1024),
	targetcustom3 VARCHAR(1024),
	targetcustom4 VARCHAR(1024),
	targetcustom5 VARCHAR(1024),
	targetcustom6 VARCHAR(1024),
	targetcustom7 VARCHAR(1024),
	targetcustom8 VARCHAR(1024),
	targetcustom9 VARCHAR(1024),
	scanless BOOLEAN,
	latestscanupdate timestamp,
	wakeonlandelay smallint not null default 0,
	slsnotified timestamp,
	custom0 VARCHAR(1024),
	custom1 VARCHAR(1024),
	custom2 VARCHAR(1024),
	custom3 VARCHAR(1024),
	custom4 VARCHAR(1024),
	custom5 VARCHAR(1024),
	custom6 VARCHAR(1024),
	custom7 VARCHAR(1024),
	custom8 VARCHAR(1024),
	custom9 VARCHAR(1024),
	fromldap BOOLEAN DEFAULT FALSE,
	amazondiscovery BOOLEAN DEFAULT FALSE,
	addNotExistingOnly SMALLINT DEFAULT 0,
	ignoreTcprst BOOLEAN DEFAULT FALSE,
	reportbpffilter TEXT,
	awsarn varchar(255),
	basedn varchar(256),
	activatecompliance BOOLEAN NOT NULL DEFAULT FALSE,
	ldapsearch varchar(256),
	schedulegroupid bigint default -1,
	compliancesEnabled TEXT DEFAULT '-1',
	priority SMALLINT DEFAULT 1,
	servicenow BOOLEAN,

	agentdiscovery boolean default false,
	agentscanfrequency text NOT NULL DEFAULT 'PT24H',
	agentscheduleid text,
	synctoagentapi boolean default false,
	syncingtoagentapi boolean default false,
	agentcustomattributeflags JSONB,
	updateagentattributes boolean default true,
	updatetargetattributes boolean default true,

	-- WAS
	type INTEGER,
	urilist TEXT,
	uriblacklist TEXT,
	uriwhitelist TEXT,
	maximumlinks INTEGER NOT NULL DEFAULT 2000,
	requestDelay INTEGER NOT NULL DEFAULT 0,
	transfertimeout INTEGER NOT NULL DEFAULT 20000,
	eventtimeout INTEGER NOT NULL DEFAULT 0,
	xss smallint NOT NULL default 1,
	contentanalysis smallint NOT NULL default 1,
	sqlinjection smallint NOT NULL default 1,
	timesqlinjection smallint NOT NULL default 0,
	remotefileinclude smallint NOT NULL default 1,
	localfileinclude smallint NOT NULL default 1,
	codeinjection smallint NOT NULL default 1,
	commandinjection smallint NOT NULL default 1,
	formatstring smallint NOT NULL default 1,
	crlfinjection smallint NOT NULL default 1,
	unvalidatedredirect smallint NOT NULL default 1,
	enableajax smallint NOT NULL default 1,
	discoverymode BOOLEAN DEFAULT FALSE,
	useragent VARCHAR(200),
	certificatePassword VARCHAR(100),
	wasCertificate VARCHAR(255),
	webAppCount INTEGER NOT NULL DEFAULT 0,

	-- Appsec scale
	settings TEXT,
	hosts TEXT[],
	infrastructurescan BOOLEAN DEFAULT FALSE,
	oldId BIGINT,
	scheme TEXT,
	hostname TEXT,
	port INTEGER
);

--
-- Name: taordersequence_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY taordersequence ADD CONSTRAINT taordersequence_pkey PRIMARY KEY (xuserxid);


--
-- Name: taorganizations_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY taorganizations ADD CONSTRAINT taorganizations_pkey PRIMARY KEY (xid);


ALTER TABLE ONLY tappaccess ADD CONSTRAINT tappaccess_pkey PRIMARY KEY (xid);

--
-- Name: tasales_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tasales ADD CONSTRAINT tasales_pkey PRIMARY KEY (xid);


--
-- Name: taudits_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY taudits ADD CONSTRAINT taudits_pkey PRIMARY KEY (xid, xxid, xuserxid, xvcapp);


--
-- Name: tcountryl_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tcountryl ADD CONSTRAINT tcountryl_pkey PRIMARY KEY (xid);


--
-- Name: tgenericgroups_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tgenericgroups ADD CONSTRAINT tgenericgroups_pkey PRIMARY KEY (xid);


--
-- Name: thiabstats_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY thiabstats ADD CONSTRAINT thiabstats_pkey PRIMARY KEY (xuserxid, uuid, itype);


--
-- Name: tlanguages_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tlanguages ADD CONSTRAINT tlanguages_pkey PRIMARY KEY (xid);


--
-- Name: tloggings_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tloggings ADD CONSTRAINT tloggings_pkey PRIMARY KEY (xid);


--
-- Name: toldworkflows_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY toldworkflows ADD CONSTRAINT toldworkflows_pkey PRIMARY KEY (xid);


--
-- Name: toutscanfiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY toutscanfiles ADD CONSTRAINT toutscanfiles_pkey PRIMARY KEY (xid);


--
-- Name: tqueuelogs_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tqueuelogs ADD CONSTRAINT tqueuelogs_pkey PRIMARY KEY (xid);


--
-- Name: tqueues_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tqueues ADD CONSTRAINT tqueues_pkey PRIMARY KEY (xid);


ALTER TABLE ONLY treport_disputes ADD CONSTRAINT treport_disputes_pkey PRIMARY KEY (xid);

ALTER TABLE disputeinfo ADD CONSTRAINT disputeinfo_pkey PRIMARY KEY (vcvulnid);

--
-- Name: treport_setups_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY treport_setups ADD CONSTRAINT treport_setups_pkey PRIMARY KEY (xid);


--
-- Name: treport_vulns_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY treport_vulns ADD CONSTRAINT treport_vulns_pkey PRIMARY KEY (xid);


ALTER TABLE ONLY tdeletedreport_vulns ADD CONSTRAINT tdeletedreport_vulns_pkey PRIMARY KEY (xid);

--
-- Name: treportentrys_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY treportentrys ADD CONSTRAINT treportentrys_pkey PRIMARY KEY (xid);


--
-- Name: treporttexts_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY treporttexts ADD CONSTRAINT treporttexts_pkey PRIMARY KEY (xid);


--
-- Name: tsavedscanprefs_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tsavedscanprefs ADD CONSTRAINT tsavedscanprefs_pkey PRIMARY KEY (xid);


--
-- Name: tscanlogs_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tscanlogs ADD CONSTRAINT tscanlogs_pkey PRIMARY KEY (xid);


--
-- Name: tscanners_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tscanners ADD CONSTRAINT tscanners_pkey PRIMARY KEY (xid);


--
-- Name: tscanstatuss_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tscanstatuss ADD CONSTRAINT tscanstatuss_pkey PRIMARY KEY (xid);

ALTER TABLE ONLY twasauths ADD CONSTRAINT twasauths_pkey PRIMARY KEY (xid);

ALTER TABLE ONLY twascookies ADD CONSTRAINT twascookies_pkey PRIMARY KEY (xid);

ALTER TABLE ONLY twasparameters ADD CONSTRAINT twasparameters_pkey PRIMARY KEY (xid);

--
-- Name: tserverstatuss_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tserverstatuss ADD CONSTRAINT tserverstatuss_pkey PRIMARY KEY (xid, vcservice);


--
-- Name: tsessionstates_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tsessionstates ADD CONSTRAINT tsessionstates_pkey PRIMARY KEY (xuserxid, xsubuserxid, name);


--
-- Name: tstatel_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tstatel ADD CONSTRAINT tstatel_pkey PRIMARY KEY (xid, xvclanguageid);


--
-- Name: tsubusers_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tsubusers ADD CONSTRAINT tsubusers_pkey PRIMARY KEY (xid);


--
-- Name: tuserdatas_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tuserdatas ADD CONSTRAINT tuserdatas_pkey PRIMARY KEY (xid);


--
-- Name: tusergroups_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tusergroups ADD CONSTRAINT tusergroups_pkey PRIMARY KEY (xid);


--
--
-- Name: tvultexts_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tvultexts ADD CONSTRAINT tvultexts_pkey PRIMARY KEY (xid);


--
-- Name: tvultextscomments_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tvultextscomments ADD CONSTRAINT tvultextscomments_pkey PRIMARY KEY (xid);


--
-- Name: tvulxrefs_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tvulxrefs ADD CONSTRAINT tvulxrefs_pkey PRIMARY KEY (xid);


--
-- Name: tworkflowmsgs_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tworkflowmsgs ADD CONSTRAINT tworkflowmsgs_pkey PRIMARY KEY (xid, taskid);


--
-- Name: tworkflows_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tworkflows ADD CONSTRAINT tworkflows_pkey PRIMARY KEY (xid);


--
-- Name: tworkflowsequence_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY tworkflowsequence ADD CONSTRAINT tworkflowsequence_pkey PRIMARY KEY (xid);


--
-- Name: xlinkgeneric_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinkgeneric ADD CONSTRAINT xlinkgeneric_pkey PRIMARY KEY (xid, xipxid );


--
-- Name: xlinkgroup_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinkgroup ADD CONSTRAINT xlinkgroup_pkey PRIMARY KEY (xid, xvcapp, xvcgroup, vcname);


--
-- Name: xlinksavedscanpref_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinksavedscanpref ADD CONSTRAINT xlinksavedscanpref_pkey PRIMARY KEY (xid, itype, vcid);


--
-- Name: xlinkscheduleobject_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinkscheduleobject ADD CONSTRAINT xlinkscheduleobject_pkey PRIMARY KEY (xid, vctarget, scannerid);


--
-- Name: xlinksubgroups_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinksubgroups ADD CONSTRAINT xlinksubgroups_pkey PRIMARY KEY (xsubxid, xgroupxid);


--
-- Name: xlinksubhosts_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinksubhosts ADD CONSTRAINT xlinksubhosts_pkey PRIMARY KEY (xsubxid, vctarget);


--
-- Name: xlinkuser_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--

ALTER TABLE ONLY xlinkuser ADD CONSTRAINT xlinkuser_pkey PRIMARY KEY (xid, xvcapp, xuserid);


ALTER TABLE tverifyscans ADD CONSTRAINT tverifyscans_pkey primary key (xid);

ALTER TABLE ONLY tvultextattributes ADD CONSTRAINT attributes_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY treport_disputes ADD CONSTRAINT treport_disputes_fk_treportentrys_xid_fkey FOREIGN KEY (fk_treportentrys_xid) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE ONLY treport_disputes ADD CONSTRAINT treport_disputes_fk_treport_vulns_xid_fkey FOREIGN KEY (fk_treport_vulns_xid) REFERENCES treport_vulns(xid) ON DELETE CASCADE;

--
-- Name: treport_setups_fk_treportentrys_xid_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY treport_setups ADD CONSTRAINT treport_setups_fk_treportentrys_xid_fkey FOREIGN KEY (fk_treportentrys_xid) REFERENCES treportentrys(xid) ON DELETE CASCADE;


--
-- Name: treport_vulns_fk_treportentrys_xid_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY treport_vulns ADD CONSTRAINT treport_vulns_fk_treportentrys_xid_fkey FOREIGN KEY (fk_treportentrys_xid) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE ONLY tdeletedreport_vulns ADD CONSTRAINT tdeletedreport_vulns_fk_treportentrys_xid_fkey FOREIGN KEY (fk_treportentrys_xid) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE ONLY treportentrys ADD CONSTRAINT treportentrys_user_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

--
-- Name: tworkflows_fk_treport_vulns_xid_fkey1; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY xlinkrules ADD CONSTRAINT xlinkrules_xid_fkey FOREIGN KEY(xid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;

ALTER TABLE ONLY tdeletedtargets ADD CONSTRAINT tdeletedtargets_pkey PRIMARY KEY (xid);

ALTER TABLE xlinkgeneric ADD CONSTRAINT fk_xipxid FOREIGN KEY(xipxid) REFERENCES tuserdatas(xid) ON DELETE CASCADE;

ALTER TABLE tdeletedtargets ADD CONSTRAINT fk_deletedtargets_groupxid FOREIGN KEY (xgroupxid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;

ALTER TABLE ONLY treporttemplates ADD CONSTRAINT treport_templates_user_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY treportschedules ADD CONSTRAINT treport_schedules_user_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE tuseddownloadkeys ADD CONSTRAINT tuseddownloadkeys_user_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE xgenericpaths ADD CONSTRAINT genericpaths_pkey_xid_fkey FOREIGN KEY (xid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;

ALTER TABLE ONLY twasauths ADD CONSTRAINT twasauths_xid_fkey FOREIGN KEY (wasxid) REFERENCES schedules(id) ON DELETE CASCADE;

ALTER TABLE ONLY twascookies ADD CONSTRAINT twascookies_xid_fkey FOREIGN KEY (wasxid) REFERENCES schedules(id) ON DELETE CASCADE;

ALTER TABLE ONLY twasparameters ADD CONSTRAINT twasparameters_xid_fkey FOREIGN KEY (wasxid) REFERENCES schedules(id) ON DELETE CASCADE;

ALTER TABLE tpasswordpolicy ADD CONSTRAINT tpasswordpolicy_user_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE toldpasswords ADD CONSTRAINT toldpasswords_user_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE tworkflowsettings ADD CONSTRAINT tworkflowsettings_user_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE tsavedscanprefs ADD CONSTRAINT tsavedscanprefs_target FOREIGN KEY (targetoverride) REFERENCES tuserdatas(xid) ON DELETE CASCADE;
ALTER TABLE tsavedscanprefs ADD CONSTRAINT tsavedscanprefs_target_edit FOREIGN KEY (targetoverrideedit) REFERENCES tuserdatas(xid) ON DELETE CASCADE;

ALTER TABLE tmsspinfo ADD CONSTRAINT tmsspinfo_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

alter table tattributes add constraint attribute_user foreign key (xuserxid) references tusers(xid) on delete cascade;

ALTER TABLE ONLY tmanagedreports ADD CONSTRAINT tmanagedreports_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE ONLY tmanagedservicesgrants ADD CONSTRAINT tmanagedservicesgrants_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE ONLY tmanagedservicesgrants ADD CONSTRAINT tmanagedservicesgrants_servicexid_fkey FOREIGN KEY (serviceuser) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE ONLY triskchanges ADD CONSTRAINT triskchanges_xid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY xlinkmanagedservices ADD CONSTRAINT managedservices_xsubuser_fk FOREIGN KEY(xsubuserxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY twaslatestdiscovery ADD CONSTRAINT twaslatestdiscovery_fk_xuserxid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY twasdiscoveryresults ADD CONSTRAINT twasdiscoveryresults_fk_xuserxid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY temailsignatures ADD CONSTRAINT temailsignature_fk_xuserxid_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

ALTER TABLE ONLY tverifyscans ADD CONSTRAINT tverifyscans_fk_treport_vulns_xid_fkey FOREIGN KEY (treport_vulns_xid) REFERENCES treport_vulns(xid) ON DELETE CASCADE;

ALTER TABLE tworkflows ADD UNIQUE (xipxid, vcvulnid, iport, iprotocol, findingid);

ALTER TABLE tauthentications ADD CONSTRAINT tauthentications_xipxid_fkey FOREIGN KEY(xipxid) REFERENCES tuserdatas(xid) ON DELETE CASCADE;

ALTER TABLE trulesdef ADD CONSTRAINT trulesdef_ruleid_fkey FOREIGN KEY (ruleid) REFERENCES trules(ruleid) ON DELETE CASCADE;
ALTER TABLE trulesdefhistory ADD CONSTRAINT trulesdefhistory_ruleid_fkey FOREIGN KEY (rulehistoryid) REFERENCES truleshistory(id) ON DELETE CASCADE;
ALTER TABLE trulesissues ADD CONSTRAINT trulesissues_ruleid_fkey FOREIGN KEY (ruleid) REFERENCES trules(ruleid) ON DELETE CASCADE;

CREATE UNIQUE INDEX tproductinformation_product ON tproductinformation (product);

ALTER TABLE tuserSettings ADD FOREIGN KEY (xuserXid) REFERENCES tusers(xid);

ALTER TABLE tuserdatas ADD UNIQUE(ipaddress, xuserxid, pci, scannerid);

ALTER TABLE twasurls ADD CONSTRAINT twasurls_pkey PRIMARY KEY (xid);
ALTER TABLE twasurls ADD FOREIGN KEY (treportentry) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE twaspaths ADD CONSTRAINT twaspaths_pkey PRIMARY KEY (xid);
ALTER TABLE twaspaths ADD FOREIGN KEY (treportentry) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE twasfindings ADD CONSTRAINT twasfindings_pkey PRIMARY KEY (xid);
ALTER TABLE twasfindings ADD FOREIGN KEY (treportentry) REFERENCES treportentrys(xid) ON DELETE CASCADE;

ALTER TABLE tworkflows ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tworkflows ADD UNIQUE (xuserxid, taskid);
ALTER TABLE tworkflowmsgs ADD FOREIGN KEY (xuserxid, taskid) REFERENCES tworkflows(xuserxid, taskid) ON DELETE CASCADE;
ALTER TABLE tuserdatas ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE taudits ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tworkflows ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinkgeneric ADD FOREIGN KEY (xid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;
ALTER TABLE toutscanfiles ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE treporttexts ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tscanlogs ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tscheduleobjectgroups ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinkscheduleobject ADD FOREIGN KEY (xid) REFERENCES schedules(id) ON DELETE CASCADE;
ALTER TABLE tsubusers ADD FOREIGN KEY (xiparentid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tusergroups ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tsessionstates ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinkscheduleobject ADD FOREIGN KEY (xgroupxid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubgroups ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubgroups ADD FOREIGN KEY (xgroupxid) REFERENCES tgenericgroups(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubhosts ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubusergroups ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubusergroups ADD FOREIGN KEY (xgroupxid) REFERENCES tusergroups(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubscanners ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubscanners ADD FOREIGN KEY (scannerid) REFERENCES tscanners(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubswat ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubswat ADD FOREIGN KEY (swatid) REFERENCES swatschedules(id) ON DELETE CASCADE;
ALTER TABLE xlinksubawsarn ADD FOREIGN KEY (xsubxid) REFERENCES tsubusers(xid) ON DELETE CASCADE;
ALTER TABLE xlinksubawsarn ADD FOREIGN KEY (awsarnid) REFERENCES tawsarns(xid) ON DELETE CASCADE;
ALTER TABLE tappaccess ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tloggings ADD FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
alter table tcompliancefindings add constraint tcompliancereportid foreign key(reportid) references tcompliancereports(xid) on delete cascade;
alter table tcompliancereports add constraint tcomplianceuserid foreign key(xuserxid) references tusers(xid) on delete cascade;
ALTER TABLE tcomplianceaccepted ADD CONSTRAINT tcompliancerequirements FOREIGN KEY (vcvulnid) REFERENCES tcompliancerequirements(xid) ON DELETE CASCADE;
ALTER TABLE tcompliancefalsepositive ADD CONSTRAINT tcompliancerequirements FOREIGN KEY (vcvulnid) REFERENCES tcompliancerequirements(xid) ON DELETE CASCADE;
ALTER TABLE ONLY tcompliancepolicies ADD CONSTRAINT tcompliancepolicies_user_fkey FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE tawsarns ADD CONSTRAINT tawsarns_xuserxid_fkey FOREIGN KEY(xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;
ALTER TABLE treportsolutions ADD CONSTRAINT solution_target FOREIGN KEY (targetxid) REFERENCES tuserdatas(xid) ON DELETE CASCADE;
ALTER TABLE ONLY targetremediation ADD CONSTRAINT targetremediation_target FOREIGN KEY (xipxid) REFERENCES tuserdatas(xid) ON DELETE CASCADE;
ALTER TABLE treportfiles ADD CONSTRAINT treportfiles_findingid FOREIGN KEY (findingId) REFERENCES treport_vulns(xid) ON DELETE CASCADE;
ALTER TABLE treporthistory ADD CONSTRAINT treporthistory_findingid FOREIGN KEY (findingId) REFERENCES treport_vulns(xid) ON DELETE CASCADE;
ALTER TABLE ONLY ttargetdatabases ADD CONSTRAINT ttargetdatabases_target FOREIGN KEY (target) REFERENCES tuserdatas(xid) ON DELETE CASCADE;
ALTER TABLE tcompliancescandata ADD FOREIGN KEY (scanjobxid) REFERENCES tscanlogs(xid) ON DELETE CASCADE;


-- Name: public; Type: ACL; Schema: -; Owner: -
--

alter table treport_vulns alter itype set statistics 1000;
alter table treport_vulns alter fk_treportentrys_xid set statistics 1000;
ALTER TABLE treport_vulns ALTER xipxid SET STATISTICS 1000;
ALTER TABLE treport_vulns ALTER iport SET STATISTICS 1000;
ALTER TABLE treport_vulns ALTER vcvulnid SET STATISTICS 1000;
alter table treportentrys alter xscanjobxid set statistics 1000;
ALTER TABLE ONLY tuserdatas ALTER COLUMN xuserxid SET STATISTICS 1000;
ALTER TABLE ONLY tscanlogs ALTER COLUMN xipxid SET STATISTICS 1000;
alter table tscanlogs alter xuserxid set statistics 100;
alter table ONLY treportentrys ALTER COLUMN dreportdate SET STATISTICS 500;
alter table twasdiscoveryresults alter xsoxid set statistics 500;
alter table twasdiscoveryresults alter xuserxid set statistics 500;

--
-- Name: textcat_null(text, text); Type: FUNCTION; Schema: public; Owner: -
--

create or replace function textcat_null(text, text) RETURNS text
		AS $_$
SELECT textcat(COALESCE($1, ''), COALESCE($2, ''));
$_$
		LANGUAGE sql;

--
-- Name: concat(character varying, character varying); Type: FUNCTION; Schema: public; Owner: -
--

create or replace function concat(character varying, character varying) RETURNS character varying
		AS $_$select $1 || $2;$_$
		LANGUAGE sql IMMUTABLE;

--
-- Name: string_agg(character varying); Type: AGGREGATE; Schema: public; Owner: -
--

CREATE AGGREGATE string_agg(character varying) (
	SFUNC = concat,
	STYPE = character varying,
	INITCOND = ''
);


--
-- Name: ||+; Type: OPERATOR; Schema: public; Owner: -
--

CREATE OPERATOR ||+ (
	PROCEDURE = textcat_null,
	LEFTARG = text,
	RIGHTARG = text
);

CREATE TABLE jiras (
	xid BIGINT NOT NULL PRIMARY KEY,
	uri text,
	jirauserName text,
	jirapassword text,
	projectKey text,
	issueType text,
	jiraenabled boolean,
	finishedStatus text,
	linkoldissues boolean DEFAULT false,
	processing boolean DEFAULT false,
	authenticationtype SMALLINT DEFAULT 0,
	oauthconsumerkey TEXT,
	oauthtoken TEXT
);
ALTER TABLE ONLY jiras ADD CONSTRAINT jiras_user FOREIGN KEY (xid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE jiraissues (
	xid bigint NOT NULL PRIMARY KEY,
	xuserxid bigint NOT NULL,
	ipaddress character varying(255),
	issuekey text
);
ALTER TABLE ONLY jiraissues ADD CONSTRAINT jiraissues_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE jiraissues_seq
	START WITH 1000;

CREATE TABLE jiratickets (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	taskid bigint,
	type smallint,
	dcreated timestamp without time zone,
	priority smallint,
	status smallint,
	name character varying(400),
	duedate timestamp without time zone,
	dupdated timestamp without time zone,
	ipaddress character varying(255),
	ipvalue bigint,
	xupdator bigint,
	xcreator bigint,
	isescalated smallint NOT NULL DEFAULT 0,
	iport bigint,
	iprotocol bigint,
	vcvulnid bigint,
	xipxid bigint,
	scanjob bigint,
	includevulinfo boolean DEFAULT false,
	findingid bigint DEFAULT 0,
	jirakey text,
	createticket boolean DEFAULT false,
	CONSTRAINT jiratickets_pkey PRIMARY KEY (xid),
	CONSTRAINT jiratickets_xuserxid_fkey FOREIGN KEY (xuserxid)
			REFERENCES tusers (xid) MATCH SIMPLE
			ON UPDATE NO ACTION ON DELETE CASCADE,
	CONSTRAINT jiratickets_xuserxid_fkey1 FOREIGN KEY (xuserxid)
			REFERENCES tusers (xid) MATCH SIMPLE
			ON UPDATE NO ACTION ON DELETE CASCADE,
	CONSTRAINT jiratickets_xipxid_vcvulnid_iport_iprotocol_findingid_jirakey_ke UNIQUE (xipxid, vcvulnid, iport, iprotocol, findingid, jirakey),
	CONSTRAINT jiratickets_xuserxid_taskid_key UNIQUE (xuserxid, taskid)
);

CREATE SEQUENCE jiratickets_seq
	INCREMENT 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1000
	CACHE 1;

--
-- Name: jiraticketssequence; Type: TABLE; Schema: public; Owner: -; Tablespace:
--
CREATE TABLE jiraticketssequence (
	xid bigint NOT NULL,
	seq bigint NOT NULL
);

--
-- Name: jiraticketssequence_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--
ALTER TABLE ONLY jiraticketssequence ADD CONSTRAINT jiraticketssequence_pkey PRIMARY KEY (xid);

REVOKE ALL ON SCHEMA public FROM PUBLIC;
REVOKE ALL ON SCHEMA public FROM postgres;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO PUBLIC;

CREATE TABLE splunks (
	xid bigint NOT NULL PRIMARY KEY,
	splunkhost text,
	splunkport int,
	splunkuser text,
	splunkpassword text,
	splunkindex text,
	splunkaudit boolean,
	splunkenabled boolean,
	splunkmode integer NOT NULL DEFAULT 0,
	splunktoken text
);
ALTER TABLE ONLY splunks ADD CONSTRAINT splunks_user FOREIGN KEY (xid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE cyberarks(
	id BIGINT REFERENCES tusers(xid) PRIMARY KEY,
	cyberarkHost TEXT NOT NULL,
	cyberarkPort INTEGER NOT NULL,
	cyberarkEnabled BOOLEAN NOT NULL,
	cyberarkAppId TEXT NOT NULL,
	cyberarkDefaultFolder TEXT NOT NULL,
	cyberarkDefaultSafe TEXT NOT NULL
);

CREATE TABLE snassets (
	xid BIGINT NOT NULL PRIMARY KEY,
	snsysid text,
	xuserxid bigint NOT NULL,
	sntable text,
	snassettag text,
	snname text
);
ALTER TABLE ONLY snassets ADD CONSTRAINT snassets_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE sntickets (
	xid BIGINT NOT NULL PRIMARY KEY,
	snsysid text,
	xuserxid bigint NOT NULL,
	sntable text,
	snshortdesc text,
	findingid BIGINT,
	createticket boolean
);
ALTER TABLE ONLY sntickets ADD CONSTRAINT sntickets_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE snassets_seq
	START WITH 1000;

CREATE SEQUENCE sntickets_seq
	START WITH 1000;

CREATE TABLE servicenowservers (
	xid BIGINT NOT NULL PRIMARY KEY,
	snuri text,
	snuserName text,
	snpassword text,
	snenabled boolean,
	processing boolean DEFAULT false,
	snclientid text,
	snclientsecret text,
	solutionasproblem boolean DEFAULT true,
	snappenabled boolean DEFAULT false,
	snapphost text
);
ALTER TABLE ONLY servicenowservers ADD CONSTRAINT servicenowservers_user FOREIGN KEY (xid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TYPE downloadentrytype AS ENUM ('REPORT', 'BLUEPRINT', 'INTERNAL_BLUEPRINT', 'DECRYPTED_OBJECT', 'LOGS', 'AGENT_INSTALLER', 'UPDATE_PACKAGE');

CREATE SEQUENCE downloadentries_seq;

CREATE TABLE downloadentries(
	id BIGINT DEFAULT NEXTVAL('downloadentries_seq') PRIMARY KEY,
	created TIMESTAMP WITHOUT TIME ZONE,
	userId BIGINT,
	subuserId BIGINT,
	fileName TEXT,
	presignedUrls JSONB,
	readableName TEXT,
	contentType VARCHAR(500),
	contentDisposition VARCHAR(500),
	contentTransferEncoding VARCHAR(100),
	key VARCHAR(200),
	status VARCHAR(50),
	size BIGINT,
	checksum VARCHAR(50),
	token TEXT,
	tokenexpires TIMESTAMP WITHOUT TIME ZONE,
	type downloadentrytype NOT NULL,
	scannerid BIGINT,
	unsigned boolean NOT NULL DEFAULT false,
	deleteafter boolean NOT NULL DEFAULT false
);

ALTER TABLE ONLY downloadentries ADD CONSTRAINT downloadentries_user FOREIGN KEY (userid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE wasxconfigurations_seq START WITH 1000;

CREATE TABLE wasxconfigurations (
	id BIGINT PRIMARY KEY DEFAULT nextval('wasxconfigurations_seq'),
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	name VARCHAR(250) NOT NULL,
	scannerId BIGINT NOT NULL DEFAULT 0,
	subuserId BIGINT NOT NULL DEFAULT -1,
	dcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	updated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	deleted BOOLEAN DEFAULT FALSE,
	maxscantimeMinutes INTEGER NOT NULL DEFAULT 120,
	comment TEXT,
	latestScanDuration INTERVAL,
	totalScanDuration INTERVAL,
	totalScans INT NOT NULL DEFAULT 0,
	settings TEXT NOT NULL);

CREATE SEQUENCE analytics_seq START WITH 1000;
CREATE TABLE analytics (
	id BIGINT PRIMARY KEY DEFAULT nextval('analytics_seq'),
	dcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	data TEXT NOT NULL
);


CREATE TABLE targetvulnerabilitytrend(reportDate DATE, xipxid BIGINT, high BIGINT, medium BIGINT, low BIGINT, scannerid BIGINT, reportid BIGINT);

CREATE TABLE certificates (
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	feature text,
	certificate text
);
ALTER TABLE ONLY certificates ADD CONSTRAINT certificates_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE certificates_seq;

--
-- Name: log_login; Type: TABLE; Schema: public; Owner: -; Tablespace:
--

CREATE TABLE log_login (
	xuserid bigint NOT NULL,
	xtime timestamp without time zone,
	dlastlogon timestamp without time zone,
	vcip character varying(40),
	vcuseragent character varying(100),
	vcusername character varying(64),
	vcemail character varying(200),
	xsubuserxid bigint
);
ALTER TABLE ONLY log_login ADD CONSTRAINT log_login_user FOREIGN KEY (xuserid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE thiabstatshistory (
	id BIGINT UNIQUE DEFAULT nextval('thiabstatshistory_seq'),
	hiabstatid BIGINT,
	xuserxid bigint NOT NULL,
	xtime timestamp without time zone DEFAULT NOW(),
	iips integer,
	itest integer,
	itype smallint DEFAULT 0 NOT NULL,
	scheduler smallint default 1,
	scansleft bigint default 0,
	webappsused BIGINT DEFAULT 0,
	webappscansleft BIGINT DEFAULT 0,
	webappscansused BIGINT DEFAULT 0,
	virtual smallint default 0,
	hwaddr VARCHAR(32),
	revoked BOOLEAN DEFAULT FALSE NOT NULL,
	version TEXT,
	lastUpdate TIMESTAMP WITHOUT TIME ZONE,
	os TEXT
);
ALTER TABLE ONLY thiabstatshistory ADD CONSTRAINT thiabstatshistory_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TYPE cryptohashalgorithm AS ENUM ('SHA_1', 'SHA_256');

CREATE TABLE idps (
	xid BIGINT NOT NULL PRIMARY KEY,
	metadata text,
	newmetadata text,
	ssoenabled boolean,
	hasuuidfield boolean default true,
	bindtoportal boolean default false,
	signaturehashalgorithm cryptohashalgorithm NOT NULL DEFAULT 'SHA_256',
	idpmetadataurl text,
	xuserxid bigint NOT NULL DEFAULT 0,
	cachedate timestamp without time zone DEFAULT NOW(),
	useridfield text NOT NULL DEFAULT 'uid'
);
ALTER TABLE ONLY idps ADD CONSTRAINT idps_user FOREIGN KEY (xuserxid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE TABLE specialnotes (
	id BIGINT PRIMARY KEY,
	userid BIGINT NOT NULL,
	findingid BIGINT NOT NULL,
	specialnote TEXT,
	secure BOOLEAN DEFAULT FALSE,
	description TEXT,
	scancomponent TEXT
);

CREATE SEQUENCE specialnotes_seq START WITH 1000;

ALTER TABLE specialnotes ADD CONSTRAINT specialnotes_findingid_fkey FOREIGN KEY(findingid) REFERENCES treport_vulns(xid) ON DELETE CASCADE;

CREATE TABLE subscriptionaudits (
	id BIGINT PRIMARY KEY,
	action TEXT,
	time TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
	ip TEXT,
	userid BIGINT,
	subuserid BIGINT,
	comment TEXT,
	customerid BIGINT
);

CREATE SEQUENCE subscriptionaudits_seq START WITH 1000;

--
--------------------------------------------------------------------------------------------------------------------------------------------
--

CREATE TABLE customers (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	country CHARACTER VARYING(2),
	state CHARACTER VARYING(2),
	features TEXT[],
	uuid UUID NOT NULL DEFAULT gen_random_uuid(),

	userid INTEGER,
	organizationid INTEGER,
	backupencryptionkey TEXT,
	assetmigration TIMESTAMP WITH TIME ZONE,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE scantemplate AS ENUM ('SCOUT', 'SCALE', 'SWAT', 'CLOUDSEC', 'DOCKER_SCAN', 'DOCKER_DISCOVERY', 'NETWORK_DISCOVERY', 'CLOUD_DISCOVERY', 'NETSEC', 'NETWORK_SCAN', 'AGENT_SCAN', 'WORKFLOW', 'NETWORK_LOOKUP');

CREATE TABLE scanconfigurationgroups (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	parentid INTEGER REFERENCES scanconfigurationgroups(id) ON DELETE CASCADE,
	oldid INTEGER,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE workflows (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	configurations JSONB,
	enabled BOOLEAN DEFAULT TRUE NOT NULL,
	scannerid INTEGER,
	settings JSONB,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE scanconfigurations (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	template scantemplate NOT NULL,
	configuration JSONB NOT NULL, -- this is the stuff we will ultimately send to the scanner, the format differs depending on type of scan
	groupid INTEGER REFERENCES scanconfigurationgroups(id) ON DELETE SET NULL,
	enabled BOOLEAN DEFAULT TRUE NOT NULL,
	scannerid INTEGER DEFAULT 0,
	properties JSONB,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE scanstatus AS ENUM ('QUEUED', 'PENDING', 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'REPORTING', 'FINISHED', 'ISSUES', 'FAILED');

CREATE SEQUENCE scanlogs_invocationid_seq
    START WITH 1
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

CREATE TABLE scanlogs (
	id SERIAL PRIMARY KEY,

	jobid TEXT,
	status scanstatus,
	statusdetails TEXT,
	template scantemplate,
	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE,
	started TIMESTAMP WITH TIME ZONE,
	ended TIMESTAMP WITH TIME ZONE,
	schema TEXT,
	attacker TEXT,
	scannerid INTEGER,
	assetid INTEGER,
	assetidentifierid INTEGER,
	statuschecked TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	expectedstart TIMESTAMP WITH TIME ZONE,
	expectedend TIMESTAMP WITH TIME ZONE,
	latestruledate TIMESTAMP WITH TIME ZONE,
	scanless BOOLEAN DEFAULT FALSE,
	parentid INTEGER,
	scheduleid INTEGER,
	authentication JSONB,
	invocationid INTEGER,
	targets TEXT[],
	virtualhosts TEXT[],

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE source AS ENUM ('SCOUT', 'APPSEC', 'SWAT', 'SCALE', 'SCALE_API', 'SCALE_SPA', 'CLOUDSEC', 'NETSEC', 'SNAPSHOT', 'ASSURE', 'OFFSEC');

CREATE TYPE assetidentifiertype AS ENUM ('IP', 'HOSTNAME', 'INSTANCE_ID', 'MAC', 'NETBIOS', 'AWS_INSTANCE_ID', 'AWS_REGION', 'AWS_ACCOUNT_ID',
'GCP_PROJECT_ID', 'MAZ_TENANT_ID', 'MAZ_SUBSCRIPTION', 'MAZ_RESOURCE_GROUP', 'MAZ_RESOURCE', 'DOCKER_REGISTRY', 'DOCKER_IMAGE', 'SEED_PATH', 'AGENT',
'SERIAL_MACHINE_ID', 'SERIAL_PRODUCT_ID', 'SERIAL_DISK_ID');

CREATE TABLE assetidentifiers (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	presentablename TEXT,
	type assetidentifiertype NOT NULL,
	firstseen TIMESTAMP WITH TIME ZONE NOT NULL,
	lastseen TIMESTAMP WITH TIME ZONE NOT NULL,
	source source[] NOT NULL CHECK(COALESCE(array_length(source, 1), 0) > 0),
	scannerid INTEGER REFERENCES tscanners(xid) ON DELETE CASCADE,
	firstscanid INTEGER REFERENCES scanlogs(id),
	lastscanid INTEGER REFERENCES scanlogs(id),
	ownership INTEGER CHECK(ownership >= 0 AND ownership <= 100),
	customownership INTEGER CHECK(customownership >= 0 AND customownership <= 100),
	platform TEXT,
	properties JSONB,
	migration INTEGER,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE assetidentifierlinktype AS ENUM ('RESOLVES_TO', 'LOADS_SCRIPTS_FROM', 'REFERS_IN_TLS_TO', 'DNS_CNAME', 'DNS_DNAME', 'DNS_MX', 'DNS_NS', 'DNS_PTR', 'DNS_SRV', 'DNS_A', 'DNS_AAAA', 'DNS_URI', 'CONTAINS', 'HTML_LINKS_TO', 'ARP');

CREATE TABLE assetidentifier_assetidentifier (
	id SERIAL,

	assetidentifierid1 INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	assetidentifierid2 INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,

	firstseen TIMESTAMP WITH TIME ZONE NOT NULL,
	lastseen TIMESTAMP WITH TIME ZONE NOT NULL,
	type assetidentifierlinktype NOT NULL,

	PRIMARY KEY (assetidentifierid1, assetidentifierid2, type)
);

CREATE UNIQUE INDEX ON assetidentifier_assetidentifier(greatest(assetidentifierid1, assetidentifierid2), least(assetidentifierid1, assetidentifierid2), type);

CREATE TYPE subscriptiontype AS ENUM ('NETSEC', 'CLOUDSEC', 'SNAPSHOT', 'SWAT', 'ASSURE', 'DAST_EXPERT', 'VERIFY', 'SCALE', 'DAST', 'SCOUT', 'CONSUMPTION');

CREATE TABLE assets (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	source source[] NOT NULL,
	activesubscriptiontypes subscriptiontype[],
	uuid UUID NOT NULL DEFAULT gen_random_uuid(),
	firstscanid INTEGER REFERENCES scanlogs(id),
	lastscanid INTEGER REFERENCES scanlogs(id),
	cvssv2environmentalvector TEXT,
	cvssv3environmentalvector TEXT,
	enabled BOOLEAN NOT NULL DEFAULT true,
	migration INTEGER,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE protocol AS ENUM ('TCP', 'UDP');

CREATE TABLE services (
	id SERIAL PRIMARY KEY,

	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	name TEXT NOT NULL,
	scheme TEXT,
	port INTEGER,
	virtualhost TEXT,
	firstseen TIMESTAMP WITH TIME ZONE NOT NULL,
	lastseen TIMESTAMP WITH TIME ZONE NOT NULL,
	source source[] NOT NULL CHECK(COALESCE(array_length(source, 1), 0) > 0),
	protocol protocol NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE credentialclasses (
	id INTEGER PRIMARY KEY,

	name TEXT NOT NULL,
	hidden BOOLEAN,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE integrationtype AS ENUM (
	'OBJECT_STORAGE',
	'AGENT',
	'ENCRYPTION_SERVICE',
	'WEBHOOK',
	'EMAIL',
	'LOG',
	'SYSLOG',
	'SNMP',
	'CYBERARK',
	'DELINEA',
	'NOTIFICATION'
);

CREATE TABLE integrations (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	type integrationtype NOT NULL,
	configuration JSONB NOT NULL,
	verified TIMESTAMP WITH TIME ZONE,
	verifiedby TEXT,
	verifiedbyid INTEGER,
	verifystatus BOOLEAN DEFAULT TRUE,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE accounttype AS ENUM ('SMB', 'SSH', 'BASIC', 'WEB', 'AWS', 'AZURE', 'VSPHERE', 'GCP', 'DOCKER');

CREATE TABLE accounts (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	type accounttype NOT NULL,
	url TEXT,
	role TEXT,
	integrationid INTEGER REFERENCES integrations(id) ON DELETE CASCADE,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE credentials (
	id SERIAL PRIMARY KEY,

	accountid INTEGER REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
	classid INTEGER REFERENCES credentialclasses(id) ON DELETE CASCADE NOT NULL,

	value TEXT NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE scanconfiguration_assetidentifier (
	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE NOT NULL,
	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (scanconfigurationid, assetidentifierid)
);

CREATE TYPE frequency AS ENUM ('NONE', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY', 'ONCE', 'MINUTELY', 'HOURLY');
CREATE TYPE month AS ENUM ('JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE', 'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER');
CREATE TYPE day AS ENUM ('MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY');

CREATE TABLE genericschedules (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	enabled BOOLEAN DEFAULT TRUE NOT NULL,

	startsfrom TIMESTAMP WITH TIME ZONE NOT NULL, -- from when the schedule should start running
	frequency frequency NOT NULL DEFAULT 'NONE', -- what frequency the following fields should be interpreted for
	interval INTEGER NOT NULL DEFAULT 1, -- how often to run, e.g. WEEKLY with interval 2 will run every second week
	weekdays day[], -- what day(s) of the week to run
	days INTEGER[], -- what day(s) of the month/year to run
	nthdays INTEGER[], -- run on the nth weekday of the month/year
	weeks INTEGER[], -- what week(s) of the month/year to run
	months month[], -- what month(s) of the year to run

	until TIMESTAMP WITH TIME ZONE, -- don't reschedule beyond this timestamp
	remainingoccurrences INTEGER, -- counts down every time it's run

	nextoccurrence TIMESTAMP WITH TIME ZONE, -- to quickly know when it should run next, calculated when rescheduling
	finaloccurrence TIMESTAMP WITH TIME ZONE, -- calculated final occurrence of the schedule
	timezone VARCHAR(100) NOT NULL DEFAULT 'Etc/UTC', -- timezone used for scheduling
	blockedtimeslots JSONB,
	scanwindowduration INTEGER,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE scanconfiguration_genericschedule (
	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE NOT NULL,
	genericscheduleid INTEGER REFERENCES genericschedules(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (scanconfigurationid, genericscheduleid)
);

CREATE TABLE classifications (
	id SERIAL PRIMARY KEY,
	cwe INTEGER UNIQUE,
	name TEXT,

	classifications JSONB, -- containing for example SANS25, CAPEC, and OWASP classifications; {'OWASP2004': [2, 3], 'SANS25': 5}
	securecodewarrior JSONB,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE matchtype AS ENUM ('WAS', 'GATHEREDINFORMATION', 'COMPONENT', 'COMPLIANCE', 'PAC', 'PRODUCT', 'PORT', 'SERVICE', 'CERTIFICATE');

CREATE TABLE matches (
	id SERIAL PRIMARY KEY,

	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	serviceid INTEGER REFERENCES services(id) ON DELETE CASCADE,
	type matchtype NOT NULL,
	firstseen TIMESTAMP WITH TIME ZONE NOT NULL,
	lastseen TIMESTAMP WITH TIME ZONE NOT NULL,
	firstscanid INTEGER REFERENCES scanlogs(id),
	lastscanid INTEGER REFERENCES scanlogs(id),
	source source[] NOT NULL CHECK(COALESCE(array_length(source, 1), 0) > 0),
	subscriptiontype subscriptiontype,
	match JSONB,
	migration BIGINT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE findingtemplategroups (
	id SERIAL PRIMARY KEY,
	parentid INTEGER REFERENCES findingtemplategroups(id) ON DELETE CASCADE,
	name TEXT NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE severity AS ENUM ('RECOMMENDATION', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

CREATE TABLE findingtemplates (
	id SERIAL PRIMARY KEY,
	template JSONB NOT NULL,
	uuid UUID UNIQUE,
	groupid INTEGER REFERENCES findingtemplategroups(id) ON DELETE SET NULL,
	cvssv2score DECIMAL(10, 1),
	cvssv3score DECIMAL(10, 1),
	cvssv2severity severity,
	cvssv3severity severity,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE findingstatus AS ENUM ('TO_REVIEW', 'TO_VERIFY', 'TO_QA', 'TO_PUBLISH', 'REJECTED', 'PRESENT', 'ACCEPTED', 'FALSE_POSITIVE', 'PENDING_VERIFICATION', 'FIXED', 'IRREPRODUCIBLE');

CREATE TABLE findings (
	id SERIAL PRIMARY KEY,

	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	checkid INTEGER,
	findingtemplateid INTEGER REFERENCES findingtemplates(id),
	cvssv2score DECIMAL(10, 1),
	cvssv2basescore DECIMAL(10, 1),
	cvssv2temporalscore DECIMAL(10, 1),
	cvssv2environmentalscore DECIMAL(10, 1),
	cvssv2vector TEXT,
	cvssv2severity severity,
	cvssv3score DECIMAL(10, 1),
	cvssv3basescore DECIMAL(10, 1),
	cvssv3temporalscore DECIMAL(10, 1),
	cvssv3environmentalscore DECIMAL(10, 1),
	cvssv3vector TEXT,
	cvssv3severity severity,
	falsepositive TIMESTAMP WITH TIME ZONE,
	falsepositivecomment TEXT,
	accepted TIMESTAMP WITH TIME ZONE,
	acceptedcomment TEXT,
	accepteduntil TIMESTAMP WITH TIME ZONE,
	fixed TIMESTAMP WITH TIME ZONE,
	commentpendingsince TIMESTAMP WITHOUT TIME ZONE,
	commentscount INTEGER DEFAULT 0,
	customname TEXT,
	renderedname TEXT,
	customdescription TEXT,
	rendereddescription TEXT,
	customcve TEXT,
	customcwe INTEGER,
	renderedcwe INTEGER,
	custombugtraq TEXT,
	customsolution TEXT,
	renderedsolution TEXT,
	customcvssv2vector TEXT,
	customcvssv3vector TEXT,
	customcvssv2severity severity,
	customcvssv3severity severity,
	customowaspmobile2024 INTEGER,
	customowaspapi2023 INTEGER,
	renderedowaspmobile2024 INTEGER,
	renderedowaspapi2023 INTEGER,
	status findingstatus DEFAULT 'PRESENT'::findingstatus NOT NULL,
	reviewed TIMESTAMP WITH TIME ZONE,
	reviewedby TEXT,
	reviewedbyid INTEGER,
	maskedreviewedby TEXT,
	qualityassured TIMESTAMP WITH TIME ZONE,
	qualityassuredby TEXT,
	qualityassuredbyid INTEGER,
	maskedqualityassuredby TEXT,
	verified TIMESTAMP WITH TIME ZONE,
	verifiedby TEXT,
	verifiedbyid INTEGER,
	maskedverifiedby TEXT,
	published TIMESTAMP WITH TIME ZONE,
	publishedby TEXT,
	publishedbyid INTEGER,
	maskedpublishedby TEXT,
	rejected TIMESTAMP WITH TIME ZONE,
	rejectedby TEXT,
	rejectedbyid INTEGER,
	customrecreation TEXT,
	alternativerecreation TEXT,
	renderedrecreation TEXT,
	customimpact TEXT,
	renderedimpact TEXT,
	potential BOOLEAN default FALSE,
	metadata JSONB,
	solutionpatches TEXT[],
	sentto TEXT[],
	migration BIGINT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE match_finding (
	matchid INTEGER REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
	findingid INTEGER REFERENCES findings(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (matchid, findingid)
);

CREATE TABLE audits (
	id SERIAL PRIMARY KEY,

	objectname TEXT,
	objectid INTEGER,
	action TEXT,
	query TEXT,
	parameters HSTORE,
	updatedfields HSTORE,

	customerid INTEGER,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE attachmenttype AS ENUM ('OTHER', 'IMAGE', 'TEXT', 'VIDEO', 'ARCHIVE');

CREATE TABLE attachments (
	id SERIAL PRIMARY KEY,

	findingid INTEGER REFERENCES findings(id) ON DELETE CASCADE NOT NULL,
	type attachmenttype NOT NULL,
	name TEXT,
	filename TEXT,
	content TEXT,

	customerid INTEGER,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tags (
	id SERIAL PRIMARY KEY,

	key TEXT,
	value TEXT,
	migration INTEGER,

	customerid INTEGER,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX "tags_customerid_key_value_key" ON tags(customerid, key, value) WHERE deleted IS NULL;

CREATE TABLE tag_assetidentifier (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, assetidentifierid)
);

CREATE TABLE tag_scanconfiguration (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, scanconfigurationid)
);

CREATE TABLE tag_findingtemplate (
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	findingtemplateid INTEGER REFERENCES findingtemplates(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, findingtemplateid)
);

CREATE TABLE tag_finding (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	findingid INTEGER REFERENCES findings(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, findingid)
);

CREATE TABLE appsecmigrationids (
	scheduleid INTEGER,
	assetidentifierid INTEGER,
	scanconfigurationid INTEGER,
	genericscheduleid INTEGER,
	findingsmigrated BOOLEAN default FALSE
);

CREATE TYPE entitytype AS ENUM (
	'FINDING',
	'ASSET_GROUP',
	'ASSET',
	'SCAN_CONFIGURATION',
	'SCHEDULE',
	'SCAN',
	'USER',
	'ROLE',
	'RESOURCE_GROUP',
	'WORKFLOW',
	'CHECK',
	'PRODUCT_INFORMATION',
	'INFORMATION'
);

CREATE TYPE supportstatus AS ENUM ('NOT_APPLICABLE', 'WAITING', 'DONE', 'INTERNAL');

CREATE TABLE comments (
	id SERIAL PRIMARY KEY,

	parentid INTEGER REFERENCES comments(id) ON DELETE CASCADE,
	comment TEXT,
	entitytype entitytype NOT NULL,
	entityid INTEGER NOT NULL,
	supportstatus supportstatus,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,

	CONSTRAINT valid_supportstatus CHECK ((parentid IS NULL AND supportstatus IS NOT NULL) OR (parentid IS NOT NULL AND supportstatus IS NULL))
);

CREATE TYPE scandatatype AS ENUM ('CRAWLEDURLS', 'LOG', 'ISSUES', 'PROP');

CREATE TABLE scandata (
	id SERIAL PRIMARY KEY,

	scanid INTEGER REFERENCES scanlogs(id) ON DELETE CASCADE NOT NULL,
	type scandatatype NOT NULL,
	data TEXT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE termstemplates (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	content TEXT NOT NULL,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE terms (
	id SERIAL PRIMARY KEY,

	mainusermustaccept boolean NOT NULL,
	allusersmustaccept boolean NOT NULL,
	termstemplateid INTEGER REFERENCES termstemplates(id) ON DELETE CASCADE NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE termsacceptances (
	id SERIAL PRIMARY KEY,

	termsid INTEGER REFERENCES terms(id) ON DELETE NO ACTION DEFERRABLE INITIALLY IMMEDIATE NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE consumptionstats (
	id SERIAL PRIMARY KEY,

	periodstart TIMESTAMP WITH TIME ZONE,
	periodend TIMESTAMP WITH TIME ZONE,
	applianceperiodend TIMESTAMP WITH TIME ZONE,

	netsecassets INTEGER DEFAULT 0,
	netsecscans INTEGER DEFAULT 0,
	netsecinternalassets INTEGER DEFAULT 0,
	netsecinternalscans INTEGER DEFAULT 0,
	netsecapplianceassets INTEGER DEFAULT 0,
	netsecappliancescans INTEGER DEFAULT 0,
	netsecapplianceexternalassets INTEGER DEFAULT 0,
	netsecapplianceexternalscans INTEGER DEFAULT 0,
	complianceassets INTEGER DEFAULT 0,
	compliancescans INTEGER DEFAULT 0,
	complianceinternalassets INTEGER DEFAULT 0,
	complianceinternalscans INTEGER DEFAULT 0,
	complianceapplianceassets INTEGER DEFAULT 0,
	complianceappliancescans INTEGER DEFAULT 0,
	complianceapplianceexternalassets INTEGER DEFAULT 0,
	complianceapplianceexternalscans INTEGER DEFAULT 0,
	pciassets INTEGER DEFAULT 0,
	pciscans INTEGER DEFAULT 0,
	scaleassets INTEGER DEFAULT 0,
	scalescans INTEGER DEFAULT 0,
	scaleinternalassets INTEGER DEFAULT 0,
	scaleapplianceassets INTEGER DEFAULT 0,
	scaleappliancescans INTEGER DEFAULT 0,
	scaleapplianceexternalassets INTEGER DEFAULT 0,
	scaleapplianceexternalscans INTEGER DEFAULT 0,
	cloudsecassets INTEGER DEFAULT 0,
	cloudsecscans INTEGER DEFAULT 0,
	networkdiscoveryscans INTEGER DEFAULT 0,
	networkdiscoveryappliancescans INTEGER DEFAULT 0,
	clouddiscoveryscans INTEGER DEFAULT 0,
	clouddiscoveryappliancescans INTEGER DEFAULT 0,
	networkassets INTEGER DEFAULT 0,
	networkscans INTEGER DEFAULT 0,
	networkapplianceassets INTEGER DEFAULT 0,
	networkappliancescans INTEGER DEFAULT 0,
	dockerimagediscoveryscans INTEGER DEFAULT 0,
	dockerimageassets INTEGER DEFAULT 0,
	dockerimagescans INTEGER DEFAULT 0,
	dockerimagediscoveryappliancescans INTEGER DEFAULT 0,
	dockerimageapplianceassets INTEGER DEFAULT 0,
	dockerimageappliancescans INTEGER DEFAULT 0,
	outscannxassets INTEGER DEFAULT 0,
	outscannxapplianceassets INTEGER DEFAULT 0,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE usagestats (
	id SERIAL PRIMARY KEY,

	statsupdated TIMESTAMP WITH TIME ZONE,
	appliancestatsupdated TIMESTAMP WITH TIME ZONE,

	scaleconfigurations INTEGER DEFAULT 0,
	scoutconfigurations INTEGER DEFAULT 0,
	cloudsecconfigurations INTEGER DEFAULT 0,
	appliancescaleconfigurations INTEGER DEFAULT 0,
	users INTEGER DEFAULT 0,
	lastlogin TIMESTAMP WITH TIME ZONE,
	applianceusers INTEGER DEFAULT 0,
	appliancelastlogin TIMESTAMP WITH TIME ZONE,
	findingscritical INTEGER DEFAULT 0,
	findingshigh INTEGER DEFAULT 0,
	findingsmedium INTEGER DEFAULT 0,
	findingslow INTEGER DEFAULT 0,
	findingsrecommendations INTEGER DEFAULT 0,
	appliancefindingscritical INTEGER DEFAULT 0,
	appliancefindingshigh INTEGER DEFAULT 0,
	appliancefindingsmedium INTEGER DEFAULT 0,
	appliancefindingslow INTEGER DEFAULT 0,
	appliancefindingsrecommendations INTEGER DEFAULT 0,
	scans INTEGER DEFAULT 0,
	lastscan TIMESTAMP WITH TIME ZONE,
	appliancescans INTEGER DEFAULT 0,
	appliancelastscan TIMESTAMP WITH TIME ZONE,
	accountsaws INTEGER DEFAULT 0,
	accountsgcp INTEGER DEFAULT 0,
	accountsazure INTEGER DEFAULT 0,
	accountsvsphere INTEGER DEFAULT 0,
	accountsbasic INTEGER DEFAULT 0,
	accountsweb INTEGER DEFAULT 0,
	applianceaccountsaws INTEGER DEFAULT 0,
	applianceaccountsgcp INTEGER DEFAULT 0,
	applianceaccountsazure INTEGER DEFAULT 0,
	applianceaccountsvsphere INTEGER DEFAULT 0,
	applianceaccountsbasic INTEGER DEFAULT 0,
	applianceaccountsweb INTEGER DEFAULT 0,
	cyr3conassets INTEGER DEFAULT 0,
	cyr3conapplianceassets INTEGER DEFAULT 0,
	agents INTEGER DEFAULT 0,
	agentsscanned INTEGER DEFAULT 0,
	agentsoutdated INTEGER DEFAULT 0,
	applianceagents INTEGER DEFAULT 0,
	applianceagentsscanned INTEGER DEFAULT 0,
	applianceagentsoutdated INTEGER DEFAULT 0,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE compliancepolicytype AS ENUM ('AWS', 'GCP', 'AZURE');

CREATE TABLE compliancepolicies (
	id SERIAL PRIMARY KEY,

	key TEXT,
	name TEXT,
	description TEXT,
	type compliancepolicytype NOT NULL,
	checksum bytea NOT NULL DEFAULT E'\\x00000000000000000000000000000000',

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE compliancerequirementcategory AS ENUM ('INFRASTRUCTURE', 'COMPUTE', 'NETWORK', 'STORAGE', 'DATABASE', 'BIG_DATA_AND_ANALYTICS', 'SECURITY_AND_IDENTITY', 'MONITORING_AND_LOGGING', 'NOT_DEFINED');

CREATE TABLE compliancerequirements (
	id SERIAL PRIMARY KEY,

	policyid INTEGER REFERENCES compliancepolicies(id) ON DELETE CASCADE NOT NULL,
	key TEXT,
	audit TEXT,
	description TEXT,
	rationale TEXT,
	scored BOOLEAN,
	nameLong TEXT,
	nameShort TEXT,
	requirementId TEXT,
	solution TEXT,
	impacts TEXT,
	notes TEXT,
	"references" TEXT,
	controls TEXT,
	category compliancerequirementcategory DEFAULT 'NOT_DEFINED' NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE compliancefindings (
	id SERIAL PRIMARY KEY,

	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	requirementid INTEGER REFERENCES compliancerequirements(id) ON DELETE CASCADE NOT NULL,
	matchid INTEGER REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
	falsepositive TIMESTAMP WITH TIME ZONE,
	falsepositivecomment TEXT,
	falsepositiveby TEXT,
	exception TIMESTAMP WITH TIME ZONE,
	exceptioncomment TEXT,
	exceptionuntil TIMESTAMP WITH TIME ZONE,
	exceptionby TEXT,
	firstfailed TIMESTAMP WITH TIME ZONE,
	lastfailed TIMESTAMP WITH TIME ZONE,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_compliancefinding (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	compliancefindingid INTEGER REFERENCES compliancefindings(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, compliancefindingid)
);

CREATE TABLE appliances (
	id SERIAL PRIMARY KEY,

	"primary" BOOLEAN NOT NULL,
	virtual BOOLEAN NOT NULL,
	revoked TIMESTAMP WITH TIME ZONE,
	mac TEXT,
	updatedonline TIMESTAMP WITH TIME ZONE,
	updatedoffline TIMESTAMP WITH TIME ZONE,
	versions JSONB,
	publicsbc TEXT,
	uuid TEXT DEFAULT 'NOID' NOT NULL,

	hiabid INTEGER REFERENCES thiabstats(id) ON DELETE CASCADE NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX "appliances_unique" ON appliances(customerid, uuid) WHERE deleted IS NULL;

CREATE TABLE tag_account (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	accountid INTEGER REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, accountid)
);

CREATE TABLE tag_user (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	userid INTEGER NOT NULL,
	PRIMARY KEY (tagid, userid)
);

CREATE TABLE thycotic (
	id SERIAL PRIMARY KEY,
	userid BIGINT REFERENCES tusers(xid) ON DELETE CASCADE NOT NULL,
	name TEXT NOT NULL,
	serverurl TEXT,
	username TEXT NOT NULL,
	password TEXT NOT NULL,
	organization TEXT,
	domain TEXT,
	tenant TEXT,
	ignorecerts BOOLEAN DEFAULT FALSE
);

CREATE TABLE thycoticauth (
	id SERIAL PRIMARY KEY,
	userid BIGINT REFERENCES tusers(xid) ON DELETE CASCADE NOT NULL,
	ssh BOOLEAN DEFAULT false NOT NULL,
	smb BOOLEAN DEFAULT false NOT NULL,
	scanpolicyid BIGINT REFERENCES tsavedscanprefs(xid) ON DELETE CASCADE NOT NULL,
	credentialproviderid BIGINT NOT NULL,
	path TEXT NOT NULL,
	secretname TEXT NOT NULL
);

CREATE TABLE userrolepermissions (
	id INTEGER PRIMARY KEY,

	name TEXT NOT NULL,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE userroles (
	id SERIAL PRIMARY KEY,

	name TEXT,
	system BOOLEAN,
	permissionids INTEGER[],

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE resourcegroups (
	id SERIAL PRIMARY KEY,

	name TEXT,
	system BOOLEAN,
	resources JSONB,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE scheduledreports (
	id SERIAL PRIMARY KEY,

	name TEXT,
	enabled BOOLEAN DEFAULT TRUE NOT NULL,
	deliverymethod JSONB NOT NULL,
	scope JSONB NOT NULL,
	report JSONB NOT NULL,
	lastsent TIMESTAMP WITH TIME ZONE,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_scheduledreport (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	scheduledreportid INTEGER REFERENCES scheduledreports(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, scheduledreportid)
);

CREATE TABLE scheduledreport_genericschedule (
	scheduledreportid INTEGER REFERENCES scheduledreports(id) ON DELETE CASCADE NOT NULL,
	genericscheduleid INTEGER REFERENCES genericschedules(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (scheduledreportid, genericscheduleid)
);

CREATE TABLE managedreports (
	id SERIAL PRIMARY KEY,

	name TEXT,
	size INTEGER,
	lastdownloaded TIMESTAMP WITH TIME ZONE,
	lastdownloadedby TEXT,
	uuid UUID NOT NULL DEFAULT gen_random_uuid(),
	migration INTEGER,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_managedreport (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	managedreportid INTEGER REFERENCES managedreports(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, managedreportid)
);

CREATE TABLE assetidentifier_account (
	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	accountid INTEGER REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetidentifierid, accountid)
);

CREATE TABLE tasks (
	id SERIAL PRIMARY KEY,
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	subuserid BIGINT DEFAULT -1,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE,
	tasktype smallint NOT NULL,
	status TEXT NOT NULL
);

CREATE TYPE keytype AS ENUM ('RSA');

CREATE TABLE integrationkeys (
	id SERIAL PRIMARY KEY,
	userid BIGINT REFERENCES tusers(xid) ON DELETE CASCADE NOT NULL,
	integrationid INTEGER,
	keytype keytype NOT NULL,
	privatekey TEXT NOT NULL,
	publickey TEXT NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE cyr3con (
	id SERIAL PRIMARY KEY,
	cve TEXT,
	cyrating DECIMAL(10, 2) NOT NULL DEFAULT 1.0,
	exploitprobability DECIMAL(10, 3) NOT NULL DEFAULT 0.026,
	updated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	lastseen TIMESTAMP WITHOUT TIME ZONE
);

CREATE UNIQUE INDEX cyr3con_cve_id_key ON cyr3con(cve, id);

CREATE TABLE dashboards (
	id SERIAL PRIMARY KEY,

	name TEXT,
	cards JSONB,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_dashboard (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	dashboardid INTEGER REFERENCES dashboards(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, dashboardid)
);

CREATE TABLE tag_integration (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	integrationid INTEGER REFERENCES integrations(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, integrationid)
);

CREATE TABLE datastore (id SERIAL PRIMARY KEY, key TEXT NOT NULL UNIQUE, value TEXT);

ALTER TABLE scanlogs ADD CONSTRAINT scanlogs_assetid_fkey FOREIGN KEY (assetid) REFERENCES assets(id) ON DELETE SET NULL;

CREATE TYPE scanselectiontype AS ENUM ('INCLUDED', 'VHOST_ONLY', 'EXCLUDED');

CREATE TABLE asset_assetidentifier (
	id SERIAL,

	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,

	firstseen TIMESTAMP WITH TIME ZONE NOT NULL,
	lastseen TIMESTAMP WITH TIME ZONE NOT NULL,
	scanselectiontype scanselectiontype NOT NULL DEFAULT 'INCLUDED',

	PRIMARY KEY (assetid, assetidentifierid)
);

CREATE TABLE tag_asset (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, assetid)
);

CREATE TYPE assetgrouptype AS ENUM ('WEB_APP', 'INSTANCE', 'API', 'INTERNAL', 'EXTERNAL', 'MOBILE', 'PHYSICAL', 'WIRELESS', 'PHISHING', 'RED', 'ASSUMED', 'DIGITAL', 'HARDWARE', 'PROJECT');

CREATE TABLE assetgroups (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	type assetgrouptype,
	parentid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE,
	path INTEGER[],
	managed BOOLEAN NOT NULL DEFAULT FALSE,
	dynamic BOOLEAN NOT NULL DEFAULT FALSE,
	activesubscriptiontypes subscriptiontype[],
	migration INTEGER,
	labels TEXT,
	contactname TEXT,
	contactdetails TEXT,
	additionaldetails TEXT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	maskedcreatedby TEXT,
	updatedby TEXT,
	maskedupdatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,
	CONSTRAINT assetgroups_single_type CHECK (NOT (managed AND dynamic))
);

CREATE TABLE assetgroup_asset (
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetgroupid, assetid)
);

CREATE TABLE asset_scanconfiguration (
	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE NOT NULL,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (scanconfigurationid, assetid)
);

CREATE TYPE summarystatus AS ENUM ('PENDING_REVIEW', 'PENDING_QA', 'PENDING_PUBLICATION', 'PUBLISHED', 'REJECTED');

CREATE TABLE summaries (
	id SERIAL PRIMARY KEY,

	content TEXT,
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	swatscheduleid BIGINT REFERENCES swatschedules(id) ON DELETE CASCADE,
	reviewed TIMESTAMP WITH TIME ZONE,
	reviewedby TEXT,
	reviewedbyid INTEGER,
	qualityassured TIMESTAMP WITH TIME ZONE,
	qualityassuredby TEXT,
	qualityassuredbyid INTEGER,
	published TIMESTAMP WITH TIME ZONE,
	publishedby TEXT,
	publishedbyid INTEGER,
	rejected TIMESTAMP WITH TIME ZONE,
	rejectedby TEXT,
	rejectedbyid INTEGER,
	status summarystatus DEFAULT 'PENDING_REVIEW'::summarystatus NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE asset_scanlog (
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	scanlogid INTEGER REFERENCES scanlogs(id) ON DELETE CASCADE NOT NULL,
	scandate TIMESTAMP WITH TIME ZONE,
	template scantemplate,
	scannerid INTEGER,
	scheme TEXT,
	host TEXT,
	port INTEGER,
	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetid, scanlogid, customerid)
);

CREATE TABLE uinotifications (
	id SERIAL PRIMARY KEY,

	eventid BIGINT NOT NULL,
	name TEXT NOT NULL,
	additionaldata TEXT,
	message TEXT,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	userid BIGINT REFERENCES tusers(xid) ON DELETE CASCADE NOT NULL,
	subuserid BIGINT REFERENCES tsubusers(xid) ON DELETE CASCADE,
	read BOOLEAN NOT NULL DEFAULT FALSE,
	eventname TEXT
);

CREATE TABLE appstakcategories (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX "appstakcategories_customerid_name" ON appstakcategories(customerid, name) WHERE deleted IS NULL;

CREATE TABLE appstaks (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	appstakcategoryid INTEGER REFERENCES appstakcategories(id) ON DELETE SET NULL,
	businesscriticality businesscriticality NOT NULL DEFAULT 'MEDIUM',

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE appstakruletype AS ENUM ('ASSETS', 'ASSETGROUPS');

CREATE TABLE appstakrules (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	filters JSONB,
	assetuuids UUID[],
	appstakid INTEGER REFERENCES appstaks(id) ON DELETE CASCADE NOT NULL,
	dbfilter TEXT,
	dbvalues JSONB,
	ruletype appstakruletype DEFAULT 'ASSETS'::appstakruletype NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_appstak (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	appstakid INTEGER REFERENCES appstaks(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, appstakid)
);

ALTER TABLE xlinkloggings ADD CONSTRAINT xlinkloggings_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;
ALTER TABLE xlinklogginggroups ADD CONSTRAINT xlinklogginggroups_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;
ALTER TABLE xlinkloggingswat ADD CONSTRAINT xlinkloggingswat_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;

CREATE TABLE targetscandata (
	targetid bigint,
	ungrouped boolean DEFAULT TRUE,
	lastreportid bigint,
	lastreportdate timestamp without time zone,
	hightrend smallint DEFAULT 0,
	mediumtrend smallint DEFAULT 0,
	lowtrend smallint DEFAULT 0,
	highrisk bigint DEFAULT 0,
	mediumrisk bigint DEFAULT 0,
	lowrisk bigint DEFAULT 0,
	fixedhighvulns bigint DEFAULT 0,
	fixedmediumvulns bigint DEFAULT 0,
	fixedlowvulns bigint DEFAULT 0,
	daysfixedhighvulns bigint DEFAULT 0,
	daysfixedmediumvulns bigint DEFAULT 0,
	daysfixedlowvulns bigint DEFAULT 0,
	daysnonfixedhighvulns bigint DEFAULT 0,
	daysnonfixedmediumvulns bigint DEFAULT 0,
	daysnonfixedlowvulns bigint DEFAULT 0,
	inactivediscoveries bigint,
	latestscanstatus bigint,
	latestscandate timestamp without time zone,
	latestsuccessfulscandate timestamp without time zone,
	lastdiscoverydate timestamp without time zone,
	authenticationresult smallint,
	platform character varying(50),
	useslicense smallint DEFAULT 0,
	groupschanged TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);

CREATE UNIQUE INDEX targetscandata_targetid ON targetscandata USING btree (targetid);
ALTER TABLE ONLY targetscandata
	ADD CONSTRAINT targetscandata_target FOREIGN KEY (targetid) REFERENCES tuserdatas(xid) ON DELETE CASCADE;

CREATE TYPE viewtype AS ENUM (
	'ASSET_GROUPS',
	'ASSETS',
	'SCAN_CONFIGURATIONS',
	'SCHEDULES',
	'SCANS',
	'FINDINGS',
	'REPORTS',
	'USERS',
	'ACCOUNTS',
	'AUDITS',
	'SCHEDULED_REPORTS',
	'MANAGED_REPORTS',
	'INTEGRATIONS',
	'COMPLIANCE',
	'TAGS',
	'VIEW_TEMPLATES',
	'SCAN_POLICIES',
	'FINDING_SOLUTIONS',
	'FINDING_DELTA',
	'AGENTS',
	'CONSUMPTION',
	'WORKFLOWS',
	'CHECKS',
	'PRODUCT_INFORMATION',
	'FINDING_TEMPLATES',
	'MATCHES',
	'INFORMATION_PRODUCTS',
	'INFORMATION_PORTS',
	'INFORMATION_SERVICES',
	'INFORMATION_CERTIFICATES'
);

CREATE TABLE viewtemplategroups (
	id SERIAL PRIMARY KEY,
	parentid INTEGER REFERENCES viewtemplategroups(id) ON DELETE CASCADE,

	name TEXT NOT NULL,
	type viewtype NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,

	CONSTRAINT can_not_be_own_child CHECK (parentid IS NULL OR parentid != id)
);

CREATE TABLE viewtemplates (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	type viewtype NOT NULL,
	system BOOLEAN DEFAULT FALSE,
	filters JSONB,
	sorting TEXT,
	fields TEXT,
	columnswidth JSONB,
	groupid INTEGER REFERENCES viewtemplategroups(id),

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,

	CONSTRAINT can_not_group_system_templates CHECK (groupid IS NULL OR NOT system)
);

CREATE TABLE tag_viewtemplate (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	viewtemplateid INTEGER REFERENCES viewtemplates(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, viewtemplateid)
);

CREATE TYPE eventtrigger AS ENUM (
	'SCHEDULE_CREATED',
	'SCHEDULE_DELETED',
	'SCHEDULE_MODIFIED',
	'SCHEDULE_SCHEDULED',
	'CONFIGURATION_CREATED',
	'CONFIGURATION_DELETED',
	'CONFIGURATION_MODIFIED',
	'CONFIGURATION_STARTED',
	'CONFIGURATION_DONE',
	'SCAN_STARTED',
	'SCAN_STOPPED',
	'SCAN_DONE',
	'ASSET_NOT_RECENTLY_SEEN',
	'ASSET_SEEN',
	'ASSET_CREATED',
	'ASSET_DELETED',
	'ASSET_MODIFIED',
	'FINDING_SEEN',
	'FINDING_CREATED',
	'FINDING_DELETED',
	'FINDING_MODIFIED',
	'FINDING_STATUS_TRANSITIONED',
	'FINDING_RISK_ACCEPTED_EXPIRATION',
	'WATCHED_FINDING_UPDATED',
	'USER_CREATED',
	'USER_DELETED',
	'USER_MODIFIED',
	'USER_LOGIN_ATTEMPT',
	'USER_PASSWORD_RESET',
	'ROLE_CREATED',
	'ROLE_DELETED',
	'ROLE_MODIFIED',
	'RESOURCE_GROUP_CREATED',
	'RESOURCE_GROUP_DELETED',
	'RESOURCE_GROUP_MODIFIED',
	'RELEASE_NOTES_PUBLISHED',
	'HIAB_SCANNER_MISSING',
	'HIAB_UPDATE_DONE',
	'HIAB_UPDATE_FAILED',
	'HIAB_BACKUP_DONE',
	'HIAB_BACKUP_FAILED',
	'HIAB_MAINTENANCE_PLAN_DONE',
	'HIAB_DISK_USAGE_HIGH',
	'HIAB_REBOOTED',
	'HIAB_REMOTE_SUPPORT',
	'OUTSCAN_CONSULTANCY',
	'CONSUMPTION_ABSOLUTE',
	'CONSUMPTION_RELATIVE',
	'WORKFLOW_CREATED',
	'WORKFLOW_DELETED',
	'WORKFLOW_MODIFIED',
	'WORKFLOW_STARTED',
	'WORKFLOW_DONE',
	'COMPLIANCE_CREATED',
	'COMPLIANCE_MODIFIED',
	'COMPLIANCE_REMOVED',
	'COMPLIANCE_RISK_EXCEPTION',
	'COMPLIANCE_RISK_EXCEPTION_EXPIRATION',
	'COMMENT_CREATED',
	'EXECUTIVE_SUMMARY_UPDATED',
	'ACTIVITY_FEED_UPDATED',
	'ASSET_GROUP_CREATED',
	'ASSET_GROUP_DELETED',
	'ASSET_GROUP_MODIFIED'
	'ACTIVITY_FEED_UPDATED',
	'NEWSLETTER_PUBLISHED',
	'COMMERCIAL_PUBLISHED',
	'CUSTOMER_COMMUNICATION_PUBLISHED'
);

CREATE TABLE eventsubscriptions (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	trigger eventtrigger NOT NULL,
	viewtemplateid INTEGER REFERENCES viewtemplates(id) ON DELETE CASCADE,
	integrationid INTEGER REFERENCES integrations(id) ON DELETE CASCADE,
	integrationtype integrationtype NOT NULL,
	enabled BOOLEAN DEFAULT TRUE NOT NULL,
	settings JSONB,
	contentconfiguration JSONB NOT NULL,
	lasttriggered TIMESTAMP WITH TIME ZONE, -- apply when trigger requires its own thread such as SCHEDULE_SCHEDULED
	hidden BOOLEAN DEFAULT FALSE NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_eventsubscription (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	eventsubscriptionid INTEGER REFERENCES eventsubscriptions(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, eventsubscriptionid)
);

CREATE TABLE notifications (
	id SERIAL PRIMARY KEY,

	userid BIGINT REFERENCES tusers(xid) ON DELETE CASCADE,
	subuserid BIGINT REFERENCES tsubusers(xid) ON DELETE CASCADE,
	eventsubscriptionid INTEGER REFERENCES eventsubscriptions(id) ON DELETE SET NULL,
	read TIMESTAMP WITH TIME ZONE,
	content JSONB NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER DEFAULT 0,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,
	CONSTRAINT notification_user_not_null CHECK ((userid IS NULL) <> (subuserid IS NULL))
);

CREATE TABLE watchers (
	id SERIAL PRIMARY KEY,

	userid BIGINT REFERENCES tusers (xid) ON DELETE CASCADE,
	subuserid BIGINT REFERENCES tsubusers (xid) ON DELETE CASCADE,
	entitytype entitytype NOT NULL,
	entityid INTEGER NOT NULL,

	customerid INTEGER REFERENCES customers (id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE,
	CONSTRAINT watcher_user_not_null CHECK ((userid IS NULL) <> (subuserid IS NULL)),
	UNIQUE (entityid, entitytype, userid, subuserid)
);

CREATE TABLE scanpolicies (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	settings JSONB,
	system BOOLEAN DEFAULT FALSE,

	customerid INTEGER REFERENCES customers (id) ON DELETE CASCADE,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_scanpolicy (
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	scanpolicyid INTEGER REFERENCES scanpolicies (id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, scanpolicyid)
);

CREATE TABLE scanpolicy_account (
	scanpolicyid INTEGER REFERENCES scanpolicies(id) ON DELETE CASCADE NOT NULL,
	accountid INTEGER REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
	referenceindex INTEGER,
	PRIMARY KEY (scanpolicyid, accountid)
);

CREATE TYPE solutiontype AS ENUM ('NOT_CLASSIFIED', 'UNKNOWN', 'RECONFIGURE', 'WORKAROUND', 'SOLUTION_IN_PROGRESS', 'CONTACT_VENDOR', 'UPDATE', 'PATCH', 'NOT_ACKNOWLEDGED', 'NO_SOLUTION', 'CONFIGURE_ACCOUNT', 'DISABLE', 'FILTER', 'MALWARE');

CREATE TABLE tag_assetgroup (
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, assetgroupid)
);

CREATE TABLE IF NOT EXISTS assetgroup_includetag (
	assetgroupid INTEGER REFERENCES assetgroups (id) ON DELETE CASCADE NOT NULL,
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetgroupid, tagid)
);

CREATE TABLE IF NOT EXISTS assetgroup_excludetag (
	assetgroupid INTEGER REFERENCES assetgroups (id) ON DELETE CASCADE NOT NULL,
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetgroupid, tagid)
);

CREATE TABLE integration_oauthv2state (
	state UUID,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	integrationid INTEGER REFERENCES integrations (id) ON DELETE CASCADE NOT NULL
);

CREATE TABLE appcheckscandata (
	id SERIAL PRIMARY KEY,

	scanid TEXT NOT NULL,
	runid TEXT NOT NULL,
	apikey TEXT NOT NULL,
	customerid INTEGER REFERENCES customers (id) ON DELETE CASCADE NOT NULL,
	scanresult JSONB,

	CONSTRAINT appcheckscandata_scanid_runid_customerid_key UNIQUE (scanid, runid, customerid)
);

CREATE TABLE workflow_genericschedule (
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE NOT NULL,
	genericscheduleid INTEGER REFERENCES genericschedules(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (workflowid, genericscheduleid)
);

CREATE TABLE tag_workflow (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, workflowid)
);

CREATE TABLE scanqueue (
	id SERIAL PRIMARY KEY,

	scanconfigurationid INTEGER REFERENCES scanconfigurations(id) ON DELETE CASCADE,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE,
	parentid INTEGER,
	scheduleid INTEGER,
	scandata JSONB,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL
);

CREATE TABLE scheduledreportqueue (
	id SERIAL PRIMARY KEY,

	scheduledreportid INTEGER REFERENCES scheduledreports(id) ON DELETE CASCADE,
	workflowid INTEGER REFERENCES workflows(id) ON DELETE CASCADE,
	scandata JSONB,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL
);

CREATE TABLE tag_customer (
	tagid INTEGER REFERENCES tags (id) ON DELETE CASCADE NOT NULL,
	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, customerid)
);

CREATE TABLE viewtemplate_defaults (
	viewtemplateid INTEGER REFERENCES viewtemplates(id) ON DELETE CASCADE NOT NULL,
	viewtemplatetype viewtype NOT NULL,
	userid BIGINT REFERENCES tusers (xid) ON DELETE CASCADE,
	subuserid BIGINT REFERENCES tsubusers (xid) ON DELETE CASCADE
);

CREATE TABLE emailtemplates (
	id SERIAL PRIMARY KEY,
	name TEXT NOT NULL,
	subject TEXT,
	content TEXT,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TYPE activitytype AS ENUM (
	'PENTEST_COMPLETED',
	'ONBOARDING_COMPLETED',
	'FALSE_POSITIVE_DISCARDED',
	'NEW_VULNERABILITY_CHECK',
	'EXECUTIVE_SUMMARY_UPDATED',
	'VERIFICATION_REQUEST_COMPLETED'
);

CREATE TABLE activities (
	id SERIAL PRIMARY KEY,
	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	activitytype activitytype NOT NULL,
	description TEXT,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE customer_migration (
	id SERIAL PRIMARY KEY,
	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	userid BIGINT REFERENCES tusers (xid) ON DELETE CASCADE NOT NULL,
	queued TIMESTAMP WITH TIME ZONE,
	started TIMESTAMP WITH TIME ZONE,
	ended TIMESTAMP WITH TIME ZONE,
	failed BOOLEAN,
	targets INTEGER,
	migratedtargets INTEGER,
	targetgroups INTEGER,
	migratedtargetgroups INTEGER,
	reports INTEGER,
	migratedreports INTEGER,
	managedreports INTEGER,
	migratedmanagedreports INTEGER
);

CREATE TABLE agentinstructions (
	id SERIAL PRIMARY KEY,
	module TEXT,
	checksum BIGINT,
	instructions TEXT
);

CREATE TABLE scanstatus_schema (
	scanstatusid BIGINT,
	schema TEXT,
	created TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);

CREATE TABLE aggregatedmatches (
	findingid INTEGER REFERENCES findings(id) ON DELETE CASCADE PRIMARY KEY,
	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	firstseen TIMESTAMP WITH TIME ZONE,
	lastseen TIMESTAMP WITH TIME ZONE,
	firstscanid INTEGER,
	lastscanid INTEGER,
	matchids INTEGER[],
	ports JSONB,
	source source[],
	isappsec BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TYPE announcementtype AS ENUM (
	'NEWSLETTER',
	'COMMERCIAL',
	'CUSTOMER_COMMUNICATION',
	'RELEASE_NOTE'
);

CREATE TABLE IF NOT EXISTS announcements (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	announcementtype announcementtype NOT NULL,
	url TEXT,
	subject TEXT,
	content TEXT,

	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE aggregatedfindingtags (
	findingid INTEGER PRIMARY KEY REFERENCES findings(id) ON DELETE CASCADE,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	tags JSONB NOT NULL
);

CREATE TYPE informationtype AS ENUM ('PRODUCT', 'PORT', 'SERVICE', 'CERTIFICATE');

CREATE TYPE informationstatus AS ENUM ('PRESENT', 'FALSE_POSITIVE');

CREATE TABLE information (
	id SERIAL PRIMARY KEY,

	type informationtype NOT NULL,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	falsepositive TIMESTAMP WITH TIME ZONE,
	falsepositivecomment TEXT,
	commentscount INTEGER DEFAULT 0,
	commentpendingsince TIMESTAMP WITHOUT TIME ZONE,
	status informationstatus DEFAULT 'PRESENT'::informationstatus NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE match_information (
	matchid INTEGER REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
	informationid INTEGER REFERENCES information(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (matchid, informationid)
);

CREATE TABLE tag_information (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	informationid INTEGER REFERENCES information(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, informationid)
);
