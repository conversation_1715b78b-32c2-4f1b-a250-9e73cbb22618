CREATE TABLE tasks (
	id SERIAL PRIMARY KEY,
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	subuserid BIGINT DEFAULT -1,
	created TIMES<PERSON>MP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIM<PERSON><PERSON><PERSON> WITH TIME ZONE,
	tasktype smallint NOT NULL,
	status TEXT NOT NULL
);

CREATE TYPE compliancerequirementcategory AS ENUM ('INFRASTRUCTURE', 'COMPUTE', 'NETWORK', 'STORAGE', 'DATABASE', 'BIG_DATA_AND_ANALYTICS', 'SECURITY_AND_IDENTITY', 'MONITORING_AND_LOGGING', 'NOT_DEFINED');

ALTER TABLE compliancerequirements ADD category compliancerequirementcategory DEFAULT 'NOT_DEFINED' NOT NULL;

ALTER TABLE signoffobjects ADD COLUMN jsonreport BOOLEAN NOT NULL DEFAULT false;

DROP VIEW IF EXISTS vrules CASCADE;
DROP VIEW IF EXISTS vruleshistory CASCADE;
DROP VIEW IF EXISTS vreportfinding CASCADE;
DROP VIEW IF EXISTS vvultext CASCADE;

ALTER TABLE trules ALTER cyrating TYPE DECIMAL(10, 2);
ALTER TABLE trules ALTER exploitprobability TYPE DECIMAL(10, 2);

ALTER TABLE truleshistory ALTER cyrating TYPE DECIMAL(10, 2);
ALTER TABLE truleshistory ALTER exploitprobability TYPE DECIMAL(10, 2);


