ALTER TABLE tusers ADD hasanalytics BOOLEAN NOT NULL DEFAULT FALSE;
DROP VIEW IF EXISTS vasale;
ALTER TABLE tasales ALTER COLUMN vctype TYPE character varying(10);

CREATE TYPE appstakcategory AS ENUM ('UNCATEGORIZED', 'ACCOUNTING', 'E_COMMERCE', 'ENTERPRISE', 'FINANCE');

CREATE TABLE appstaks (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	category appstakcategory,
	businesscriticality businesscriticality NOT NULL DEFAULT 'MEDIUM',

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMES<PERSON>MP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE appstakrules (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	assetfilters JSONB,
	assetuuids UUID[],
	appstakid INTEGER REFERENCES appstaks(id) ON DELETE CASCADE NOT NULL,
	filter TEXT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE tag_appstak (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	appstakid INTEGER REFERENCES appstaks(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, appstakid)
);
