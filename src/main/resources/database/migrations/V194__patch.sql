-- Compliance
UPDATE scheduledreports s
SET report = report || jsonb_build_object('viewTemplateId', v.id)
FROM viewtemplates v
WHERE report @> '{"product": "CLOUDSEC"}' AND v.name = 'Compliance (default)' AND system AND s.deleted IS NULL;

-- SWAT
UPDATE scheduledreports s
SET report = report || jsonb_build_object('viewTemplateId', v.id)
FROM viewtemplates v
WHERE report @> '{"product": "SWAT"}' AND v.name = 'SWAT (default)' AND system AND s.deleted IS NULL;

-- SCALE
UPDATE scheduledreports s
SET report = report || jsonb_build_object('viewTemplateId', v.id)
FROM viewtemplates v
WHERE report @> '{"product": "APPSEC"}' AND v.name = 'SCALE (default)' AND system AND s.deleted IS NULL;

-- tags
INSERT INTO tags (key, customerid, createdbyid)
  SELECT DISTINCT 'automigration', customerid, 0
  FROM
    resourcegroups,
    jsonb_array_elements(resources) WITH ORDINALITY arr(resource, index)
  WHERE
    resource->>'type' = 'SCHEDULEDREPORT'
    AND NOT system
    AND customerid IS NOT NULL
    AND deleted IS NULL;

-- tag_viewtemplate
INSERT INTO tag_viewtemplate(tagid, viewtemplateid)
SELECT DISTINCT t.id, v.id FROM viewtemplates v JOIN tags t ON t.key = 'automigration' AND v.system
ON CONFLICT DO NOTHING;

-- resourcegroups

-- update resourcegroups which already contain VIEWTEMPLATE
WITH viewtemplate_tags AS (
  SELECT
    id,
    tagid,
    ('{'||index-1||',tagIds}')::text[] AS path,
    (CASE WHEN resource->>'tagIds' IS NOT NULL THEN resource->>'tagIds' ELSE '[]' END) AS val
  FROM (
    SELECT
      resourcegroups.id, tags.id AS tagid, resources
    FROM
      resourcegroups LEFT JOIN tags ON resourcegroups.customerid = tags.customerid AND tags.key = 'automigration',
      jsonb_array_elements(resources) WITH ORDINALITY arr(resource, index)
    WHERE
      resource->>'type' = 'SCHEDULEDREPORT'
      AND NOT system
      AND resourcegroups.customerid IS NOT NULL
      AND resourcegroups.deleted IS NULL
      AND resources @> '[{"type": "VIEWTEMPLATE"}]'
  ) t,
  jsonb_array_elements(resources) WITH ORDINALITY arr(resource, index)
  WHERE resource->>'type' = 'VIEWTEMPLATE'
  AND resource->>'tagIds' IS NOT NULL
  AND array_to_string(array[resource->>'tagIds'], ',') NOT LIKE '%' || tagid || '%'
)
UPDATE resourcegroups
SET
  resources = jsonb_set(
      resources,
      viewtemplate_tags.path,
      viewtemplate_tags.val::jsonb || ('['||tagid||']')::jsonb
    )
FROM
  viewtemplate_tags
WHERE
  resourcegroups.id = viewtemplate_tags.id;


-- update resourcegroups which DO NOT contain VIEWTEMPLATE
WITH viewtemplate_tags AS (
  SELECT
    resourcegroups.id,
    array[tags.id] AS val
  FROM
    resourcegroups LEFT JOIN tags ON resourcegroups.customerid = tags.customerid AND tags.key = 'automigration',
    jsonb_array_elements(resources) WITH ORDINALITY arr(resource, index)
  WHERE
    resource->>'type' = 'SCHEDULEDREPORT'
    AND NOT system
    AND resourcegroups.customerid IS NOT NULL
    AND resourcegroups.deleted IS NULL
    AND tags.id IS NOT NULL
    AND NOT (resources @> '[{"type": "VIEWTEMPLATE"}]')
)
UPDATE resourcegroups
  SET resources = resources || jsonb_build_object('type', 'VIEWTEMPLATE', 'tagIds', viewtemplate_tags.val)
FROM
  viewtemplate_tags
WHERE
  resourcegroups.id = viewtemplate_tags.id;