CREATE TABLE assetgroups (
	id SERIAL PRIMARY KEY,

	name TEXT NOT NULL,
	parentid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE,
	source source[] NOT NULL CHECK(COALESCE(array_length(source, 1), 0) > 0),

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE assetgroup_asset (
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (assetgroupid, assetid)
);
