ALTER TABLE downloadentries ADD token TEXT;
ALTER TABLE downloadentries ADD tokenexpires TIMESTAMP WITHOUT TIME ZONE;

CREATE INDEX tqueuelogs_sent_asc ON tqueuelogs(dsent ASC NULLS FIRST, xid);
CREATE INDEX tqueuelogs_sent_desc ON tqueuelogs(dsent DESC NULLS LAST, xid);

UPDATE tcompliancepolicies SET name = REPLACE(name,'CIS','Hardening') WHERE global;
UPDATE tcompliancepolicies SET description = REPLACE(description,'CIS','Hardening') WHERE global;
INSERT INTO tcompliancepolicies(xid, name, isgroup, parent, global, description) VALUES (9, 'CIS', true, -1, true, '');

ALTER TABLE tpasswordrecoverys ADD console boolean default false;

UPDATE treleasenotes SET features = '<br><b>OWASP 2017 Top 10</b><br>We have added support for the 2017 OWASP top 10 in the Appsec UI. When scanning web applications, you can now see the 2017 impact of a finding.<br><br><b>Web application Authentication improvements</b><br>Added a new Form based Authentication option for web application scanning using Appsec Scale. This feature is currently in beta and available from the Beta Appsec UI.<br><br>The ability to use SSL certificates as part of the web application authentication process has also been added to the (Beta) Appsec UI.<br><br>As always we encourage our customers to try out the new appsec UI - currently marked as in Beta - to help test and review the new features.<br><br>' WHERE vversion = 'G4.1.150';

ALTER TABLE tusers ADD sslclientcafile VARCHAR(255);
