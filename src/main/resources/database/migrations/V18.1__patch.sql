CREATE TABLE assets (
	id SERIAL PRIMARY KEY,
	name TEXT NOT NULL,
	created TIMES<PERSON><PERSON> WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);


CREATE TYPE assetidentifiertype AS ENUM ('ip', 'hostname', 'instanceid', 'mac');

CREATE TABLE assetidentifiers (
	id SERIAL PRIMARY KEY,
	assetid INTEGER REFERENCES assets(id) ON DELETE CASCADE NOT NULL,
	name TEXT NOT NULL,
	type assetidentifiertype NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);


CREATE TABLE services (
	id SERIAL PRIMARY KEY,
	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	name TEXT NOT NULL,
	scheme TEXT,
	port INTEGER,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

