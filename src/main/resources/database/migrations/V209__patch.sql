CREATE TABLE appstakcategories (
       id SERIAL PRIMARY KEY,
       name TEXT NOT NULL,
       customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
       created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
       updated TIMES<PERSON>MP WITH TIME ZONE DEFAULT now() NOT NULL,
       createdby TEXT,
       updatedby TEXT,
       createdbyid INTEGER,
       updatedbyid INTEGER,
       deleted TIMESTAMP WITH TIME ZONE
 );

CREATE UNIQUE INDEX "appstakcategories_customerid_name" ON appstakcategories(customerid, name) WHERE deleted IS NULL;

ALTER TABLE appstaks ADD COLUMN appstakcategoryid INTEGER REFERENCES appstakcategories(id) ON DELETE SET NULL;
ALTER TABLE appstaks DROP COLUMN category CASCADE;
DROP TYPE appstakcategory;