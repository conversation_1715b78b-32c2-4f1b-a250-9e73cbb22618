CREATE TYPE summarystatus AS ENUM ('PENDING_REVIEW', 'PENDING_QA', 'PENDING_PUBLICATION', 'PUBLISHED');

ALTER TABLE summaries ADD COLUMN IF NOT EXISTS qualityassured TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS qualityassuredby TEXT,
    ADD COLUMN IF NOT EXISTS reviewed TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS reviewedby TEXT,
    ADD COLUMN IF NOT EXISTS published TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS publishedby TEXT,
    ADD COLUMN IF NOT EXISTS status summarystatus DEFAULT 'PENDING_REVIEW'::summarystatus NOT NULL;

