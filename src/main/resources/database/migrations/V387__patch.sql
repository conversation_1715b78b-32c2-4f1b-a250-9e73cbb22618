-- Create a new enum type for scan selection type
DROP TYPE IF EXISTS scanselectiontype;
CREATE TYPE scanselectiontype AS ENUM ('INCLUDED', 'VHOST_ONLY', 'EXCLUDED');

-- Add scanselectiontype enum field to asset_assetidentifier table with default value
ALTER TABLE asset_assetidentifier ADD COLUMN IF NOT EXISTS scanselectiontype scanselectiontype NOT NULL DEFAULT 'INCLUDED';

-- Create sequence for invocationid
CREATE SEQUENCE IF NOT EXISTS scanlogs_invocationid_seq
    START WITH 1
	INCREMENT BY 1
	NO MAXVALUE
	NO MINVALUE
	CACHE 1;

-- Add new columns to scanlogs table
ALTER TABLE IF EXISTS scanlogs
	ADD COLUMN IF NOT EXISTS invocationid INTEGER,
	ADD COLUMN IF NOT EXISTS targets TEXT[],
	ADD COLUMN IF NOT EXISTS virtualhosts TEXT[];

-- Create index on invocationid
CREATE INDEX IF NOT EXISTS scanlogs_invocationid ON scanlogs(invocationid);

-- Add settings json field to workflow table
ALTER TABLE IF EXISTS workflows ADD COLUMN IF NOT EXISTS settings JSONB;

-- Add assetid and networklookupdata fields to tscanstatuss table
ALTER TABLE IF EXISTS tscanstatuss
	ADD COLUMN IF NOT EXISTS assetid INTEGER,
	ADD COLUMN IF NOT EXISTS networklookupdata TEXT;
CREATE INDEX IF NOT EXISTS tscanstatuss_assetid ON tscanstatuss(assetid);

-- Add network lookup scan template
ALTER TYPE scantemplate ADD VALUE 'NETWORK_LOOKUP';