DROP VIEW IF EXISTS consumptionstatsview CASCADE;

ALTER TABLE consumptionstats DROP COLUMN scoutassets;
ALTER TABLE consumptionstats DROP COLUMN scoutscans;
ALTER TABLE consumptionstats DROP COLUMN datasecassets;

ALTER TABLE consumptionstats ADD networkdiscoveryscans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD networkdiscoveryappliancescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD clouddiscoveryscans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD clouddiscoveryappliancescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD networkassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD networkscans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD networkapplianceassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD networkappliancescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimagediscoveryscans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimageassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimagescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimagediscoveryappliancescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimageapplianceassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD dockerimageappliancescans INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD outscannxassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD outscannxapplianceassets INTEGER DEFAULT 0;
