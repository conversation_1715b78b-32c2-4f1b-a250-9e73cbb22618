ALTER TABLE tusergroups ADD COLUMN grantalltickets B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE;
CREATE SEQUENCE wasxschedules_seq START WITH 1000;

CREATE TABLE wasxschedules (
	id BIGINT PRIMARY KEY DEFAULT nextval('wasxschedules_seq'),
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	name VARCHAR(250) NOT NULL,
	scannerId BIGINT NOT NULL DEFAULT 0,
	subuserId BIGINT NOT NULL DEFAULT -1,
	dcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	nextscandate TIMESTAMP WITHOUT TIME ZONE,
	gmtoffset DECIMAL(10, 2),
	frequency SMALLINT NOT NULL DEFAULT 10,
	dayweekmonth SMALLINT,
	updated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	deleted BOOLEAN DEFAULT FALSE,
	lastscandate TIMESTAMP WITHOUT TIME ZONE,
	maxscantimeMinutes INTEGER NOT NULL DEFAULT 120,
	latestscanstatus BIGINT,
	latestscandate TIMESTAMP,
	comment TEXT,
	latestScanDuration INTERVAL,
	totalScanDuration INTERVAL,
	totalScans INT NOT NULL DEFAULT 0,
	settings TEXT NOT NULL);

DROP VIEW IF EXISTS VSCANSTATUS CASCADE;
ALTER TABLE tscanstatuss ALTER probeid TYPE VARCHAR(100);

CREATE TABLE jiras (
		xid BIGINT NOT NULL,
	uri text,
	jirauserName text,
	jirapassword text,
	projectKey text,
	issueType text,
	jiraenabled boolean,
	startingStatus text,
	finishedStatus text
);

CREATE TABLE jiraissues (
		xid bigint NOT NULL,
		xuserxid bigint NOT NULL,
		ipaddress character varying(255),
		issuekey text
);

CREATE SEQUENCE jiraissues_seq
	START WITH 1000;

CREATE TABLE jiratickets
(
	xid bigint NOT NULL,
	xuserxid bigint NOT NULL,
	xsubuserxid bigint,
	taskid bigint,
	type smallint,
	dcreated timestamp without time zone,
	priority smallint,
	status smallint,
	name character varying(400),
	duedate timestamp without time zone,
	dupdated timestamp without time zone,
	ipaddress character varying(255),
	ipvalue bigint,
	xupdator bigint,
	xcreator bigint,
	isescalated smallint NOT NULL DEFAULT 0,
	iport bigint,
	iprotocol bigint,
	vcvulnid bigint,
	xipxid bigint,
	scanjob bigint,
	includevulinfo boolean DEFAULT false,
	findingid bigint DEFAULT 0,
	jirakey text,
	CONSTRAINT jiratickets_pkey PRIMARY KEY (xid),
	CONSTRAINT jiratickets_xuserxid_fkey FOREIGN KEY (xuserxid)
			REFERENCES tusers (xid) MATCH SIMPLE
			ON UPDATE NO ACTION ON DELETE CASCADE,
	CONSTRAINT jiratickets_xuserxid_fkey1 FOREIGN KEY (xuserxid)
			REFERENCES tusers (xid) MATCH SIMPLE
			ON UPDATE NO ACTION ON DELETE CASCADE,
	CONSTRAINT jiratickets_xipxid_vcvulnid_iport_iprotocol_findingid_jirakey_ke UNIQUE (xipxid, vcvulnid, iport, iprotocol, findingid, jirakey),
	CONSTRAINT jiratickets_xuserxid_taskid_key UNIQUE (xuserxid, taskid)
);

CREATE SEQUENCE jiratickets_seq
	INCREMENT 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1000
	CACHE 1;

--
-- Name: jiraticketssequence; Type: TABLE; Schema: public; Owner: -; Tablespace:
--
CREATE TABLE jiraticketssequence (
		xid bigint NOT NULL,
		seq bigint NOT NULL
);

--
-- Name: jiraticketssequence_pkey; Type: CONSTRAINT; Schema: public; Owner: -; Tablespace:
--
ALTER TABLE ONLY jiraticketssequence ADD CONSTRAINT jiraticketssequence_pkey PRIMARY KEY (xid);
ALTER TABLE tusers ADD jiraenabled boolean;
ALTER TABLE tsamltokens ADD idpid bigint;
ALTER TABLE idps ADD identity text;
ALTER TABLE idps ADD xuserxid bigint NOT NULL DEFAULT 0;
UPDATE idps SET xuserxid = xid WHERE xuserxid = 0;
ALTER TABLE idps ADD ssoenabled BOOLEAN;

ALTER TABLE tusergroups ADD swatcomment smallint default 0;
UPDATE tusergroups SET swatcomment = 1 WHERE web;
ALTER TABLE tusergroups ADD swatverification smallint default 0;
UPDATE tusergroups SET swatverification = 1 WHERE web;
ALTER TABLE tusergroups ADD swatdiscussion smallint default 0;
UPDATE tusergroups SET swatdiscussion = 1 WHERE web;
ALTER TABLE tusergroups ADD swatrisks smallint default 0;
UPDATE tusergroups SET swatrisks = 1 WHERE web;

CREATE TEMPORARY TABLE tusersettingstmp AS SELECT * FROM tusersettings;
ALTER TABLE tusersettingstmp ADD xid BIGINT;
UPDATE tusersettingstmp SET xid=nextval('acceptedrisks_seq');
DELETE FROM tusersettings;
DELETE FROM tusersettingstmp WHERE xuserxid NOT IN (SELECT xid FROM tusers);
INSERT INTO tusersettings(xuserxid, acceptedlength, accepttargets, acceptcomment) SELECT xuserxid, acceptedlength, accepttargets, acceptcomment FROM tusersettingstmp WHERE xid IN (SELECT MAX(xid) FROM tusersettingstmp GROUP BY xuserxid);
DROP TABLE tusersettingstmp;

ALTER TABLE ONLY tusersettings ADD CONSTRAINT tusersettings_pkey PRIMARY KEY (xuserxid);

ALTER TABLE tusers ADD ssoenabled boolean;
ALTER TABLE idps DROP identity;
ALTER TABLE idps ADD newmetadata text;
ALTER TABLE tsamltokens ADD ipaddress text;
ALTER TABLE treport_vulns ADD patchInformation TEXT;

ALTER TABLE tpatchsupersedence ADD bulletinid VARCHAR(500);

CREATE TABLE tcve (
	xid BIGINT PRIMARY KEY,
	name TEXT,
	description TEXT,
	vector CHARACTER VARYING(128),
	refs TEXT[]
);

CREATE SEQUENCE tcve_seq START WITH 1000;

ALTER TABLE treportschedules ADD COLUMN comment TEXT;

ALTER TABLE tscheduleobjects ADD compliancesEnabled TEXT DEFAULT '-1';

ALTER TABLE wasxschedules ADD high BIGINT DEFAULT 0, ADD medium BIGINT DEFAULT 0, ADD low BIGINT DEFAULT 0;

ALTER TABLE tusers ADD allowComplianceOnlyScanning BOOLEAN DEFAULT FALSE;

ALTER TABLE tusers ADD dismissComplianceWarning BOOLEAN DEFAULT FALSE;
ALTER TABLE tsubusers ADD dismissComplianceWarning BOOLEAN DEFAULT FALSE;


ALTER TABLE tloggings ADD COLUMN reporttemplate BIGINT DEFAULT 0;

CREATE TABLE newpatches(
	xid BIGINT PRIMARY KEY,
	patch TEXT,
	supersededby TEXT,
	bulletinid VARCHAR(500)
);

CREATE SEQUENCE newpatches_seq START WITH 1000;

ALTER TABLE tloggings ADD scriptid text;
CREATE TABLE splunks (
	xid bigint NOT NULL,
	splunkhost text,
	splunkport int,
	splunkuser text,
	splunkpassword text,
	splunkindex text,
	splunkaudit boolean,
	splunkenabled boolean
);

ALTER TABLE tusers ADD splunkenabled boolean default false;
DROP FUNCTION IF EXISTS getSalesStatistics(TEXT, TEXT, TEXT, TEXT[]);
DROP FUNCTION IF EXISTS getUserStatistics(BIGINT[]);
DROP FUNCTION IF EXISTS getChurnStatistics(TEXT, TEXT, TEXT, TEXT[]);

DROP TRIGGER IF EXISTS aiur_tuserdata ON tuserdatas;
DROP TRIGGER IF EXISTS aiur_tuserdata_statement ON tuserdatas;
DROP TRIGGER IF EXISTS biur_tuserdata ON tuserdatas;
DROP TRIGGER IF EXISTS bur_tuserdata_statement ON tuserdatas;

DELETE FROM tgenericgroups g WHERE xid > 0 and xiparentid > 0 AND NOT EXISTS (SELECT xid FROM xgenericpaths x WHERE x.parentxid = g.xid);
UPDATE tuserdatas u SET ungrouped = TRUE WHERE NOT ungrouped AND NOT EXISTS(SELECT xipxid FROM xlinkgeneric WHERE xipxid=u.xid LIMIT 1);
ALTER TABLE tusers ADD expiredate timestamp without time zone;

ALTER TABLE treport_vulns ADD customDescription TEXT;
ALTER TABLE treport_vulns ADD customCve TEXT;
ALTER TABLE treport_vulns ADD customBugtraq TEXT;
ALTER TABLE treport_vulns ADD customVulnerabilitytype VulnerabilityType;
ALTER TABLE treport_vulns ADD customWasc BIGINT;
ALTER TABLE treport_vulns ADD customSolutionType SMALLINT;
ALTER TABLE treport_vulns ADD customSolutionTitle TEXT;
ALTER TABLE treport_vulns ADD customSolution TEXT;
ALTER TABLE treport_vulns ADD customOwasp2004 VARCHAR(100);
ALTER TABLE treport_vulns ADD customOwasp2007 VARCHAR(100);
ALTER TABLE treport_vulns ADD customOwasp2010 VARCHAR(100);
ALTER TABLE treport_vulns ADD customOwasp2013 VARCHAR(100);

ALTER TABLE treporthistory ADD customDescription TEXT;
ALTER TABLE treporthistory ADD customCve TEXT;
ALTER TABLE treporthistory ADD customBugtraq TEXT;
ALTER TABLE treporthistory ADD customVulnerabilitytype VulnerabilityType;
ALTER TABLE treporthistory ADD customWasc BIGINT;
ALTER TABLE treporthistory ADD customSolutionType SMALLINT;
ALTER TABLE treporthistory ADD customSolutionTitle TEXT;
ALTER TABLE treporthistory ADD customSolution TEXT;
ALTER TABLE treporthistory ADD customOwasp2004 VARCHAR(100);
ALTER TABLE treporthistory ADD customOwasp2007 VARCHAR(100);
ALTER TABLE treporthistory ADD customOwasp2010 VARCHAR(100);
ALTER TABLE treporthistory ADD customOwasp2013 VARCHAR(100);

ALTER TABLE tmsspinfo ADD pcitargets BIGINT DEFAULT 0 NOT NULL, ADD pciscans BIGINT DEFAULT 0 NOT NULL;

ALTER TABLE treport_vulns ADD lastVerifyId BIGINT;
UPDATE treport_vulns v set lastVerifyId=(SELECT MAX(xid) FROM treport_verifys vv WHERE vv.fk_treport_vulns_xid=v.xid) WHERE xid IN (SELECT fk_treport_vulns_xid FROM treport_verifys);

DO $$
	BEGIN
		BEGIN
			ALTER TABLE trules ADD cvssV3Score DECIMAL(10, 1), ADD cvssV3Vector VARCHAR(100);
		EXCEPTION
			WHEN duplicate_column THEN RAISE NOTICE 'Columns already exists in trules.';
		END;
	END;
$$;

ALTER TABLE truleshistory ADD cvssV3Score DECIMAL(10, 1);
ALTER TABLE truleshistory ADD cvssV3Vector VARCHAR(100);

DELETE FROM tmanagedreportgroups WHERE xiparentid = 0;
UPDATE tmanagedreports r SET reportgroupid = 0 WHERE reportgroupid < 0 OR NOT EXISTS (SELECT xid from tmanagedreportgroups where xid = r.reportgroupid);

