CREATE TYPE assetgrouptype AS ENUM ('WEB_APP', 'INSTANCE', 'API', 'INTERNAL', 'EXTERNAL', 'MO<PERSON><PERSON>', 'PHYSICAL', 'WIRELESS', 'PHISHING', 'RED', 'ASSUMED', 'DIGIT<PERSON>', 'HARDWA<PERSON>');

ALTER TABLE assetgroups ADD COLUMN IF NOT EXISTS "type" assetgrouptype;
ALTER TABLE assetgroups ADD COLUMN IF NOT EXISTS labels TEXT;
ALTER TABLE assetgroups ADD COLUMN IF NOT EXISTS contactname TEXT;
ALTER TABLE assetgroups ADD COLUMN IF NOT EXISTS contactdetails TEXT;

UPDATE assetgroups SET "type" = 'WEB_APP'::assetgrouptype WHERE managed AND parentid IS NULL;
UPDATE assetgroups SET "type" = 'INSTANCE'::assetgrouptype WHERE managed AND parentid IS NOT NULL;