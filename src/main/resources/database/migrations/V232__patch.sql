UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2004}', '[6]') WHERE cwe = 89;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[1]') WHERE cwe = 93;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[1]') WHERE cwe = 93;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[1]') WHERE cwe = 94;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[1]') WHERE cwe = 94;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[1]') WHERE cwe = 94;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[6]') WHERE cwe = 201;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[6]') WHERE cwe = 201;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[6]') WHERE cwe = 201;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[6]') WHERE cwe = 212;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[6]') WHERE cwe = 212;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[6]') WHERE cwe = 250;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[7]') WHERE cwe = 285;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[3]') WHERE cwe = 287;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[2]') WHERE cwe = 287;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[3]') WHERE cwe = 306;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[2]') WHERE cwe = 306;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[6]') WHERE cwe = 315;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[3]') WHERE cwe = 315;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[3]') WHERE cwe = 384;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[8]') WHERE cwe = 425;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[7]') WHERE cwe = 425;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[2]') WHERE cwe = 521;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[2]') WHERE cwe = 521;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[3]') WHERE cwe = 522;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2007}', '[10]') WHERE cwe = 601;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2010}', '[3]') WHERE cwe = 640;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[5]') WHERE cwe = 732;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[6]') WHERE cwe = 732;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[4]') WHERE cwe = 829;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[5]') WHERE cwe = 862;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2013}', '[7]') WHERE cwe = 922;
UPDATE classifications SET classifications = jsonb_set(COALESCE(classifications, '{}'::jsonb), '{owasp2017}', '[5]') WHERE cwe = 922;
