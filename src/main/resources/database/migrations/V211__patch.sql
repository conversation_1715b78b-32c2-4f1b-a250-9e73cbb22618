CREATE OR REPLACE FUNCTION modifyRules() RETURNS VOID AS $$
DECLARE
	_trigger BOOLEAN;
BEGIN
	SELECT EXISTS(SELECT tgname FROM pg_trigger WHERE tgname = 'biu_trules') INTO _trigger;
	IF _trigger THEN
		DROP TRIGGER IF EXISTS biu_trules ON trules;
	END IF;

	UPDATE trules SET farsight =
	jsonb_set(
		jsonb_set(farsight, '{risk,score}', COALESCE(ROUND((farsight->'risk'->>'score')::NUMERIC, 0)::TEXT, 'null')::JSONB),
		'{risk,delta}', COALESCE(ROUND((farsight->'risk'->>'delta')::NUMERIC, 0)::TEXT, 'null')::JSONB
	),
	bluelivscore = ROUND(bluelivscore::NUMERIC, 0),
	bluelivdelta = ROUND(bluelivdelta::NUMERIC, 0)
	WHERE farsight IS NOT NULL;

	IF _trigger THEN
		CREATE TRIGGER biu_trules
		BEFORE INSERT OR UPDATE ON trules
		FOR EACH ROW
		EXECUTE PROCEDURE trigger_rule();
	END IF;
END;
$$
	LANGUAGE plpgsql;

DO $$
	BEGIN
		PERFORM modifyRules();
	END;
$$;

DROP FUNCTION IF EXISTS modifyRules();
