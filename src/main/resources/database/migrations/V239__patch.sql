CREATE TABLE tag_assetgroup (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	assetgroupid INTEGER REFERENCES assetgroups(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, assetgroupid)
);

CREATE INDEX IF NOT EXISTS tag_assetgroup_assetgroupid ON tag_assetgroup(assetgroupid);

WITH moved AS (DELETE FROM tag_asset ta USING assetgroup_asset aga WHERE ta.assetid = aga.assetid RETURNING ta.assetid, ta.tagid, assetgroupid)
INSERT INTO tag_assetgroup SELECT tagid, assetgroupid FROM moved ON CONFLICT (tagid, assetgroupid) DO NOTHING;
