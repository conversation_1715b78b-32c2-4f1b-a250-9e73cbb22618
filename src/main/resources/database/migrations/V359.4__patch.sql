INSERT INTO scanstatus_schema (scanstatusid, schema, created)
SELECT xschedulexid, scanschema, dcreated FROM tscanlogs
WHERE dcreated > '2025-03-25' AND itype = 0 AND scanschema IS NOT NULL AND xschedulexid NOT IN (SELECT scanstatusid FROM scanstatus_schema);

INSERT INTO scanstatus_schema (scanstatusid, schema)
SELECT xid, scanschema FROM tscanstatuss
WHERE scanlessreportxid <= 0 AND scanschema IS NOT NULL AND vcservice IN ('O', 'NS') AND xid NOT IN (SELECT scanstatusid FROM scanstatus_schema);
