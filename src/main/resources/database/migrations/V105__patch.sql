CREATE TYPE EventSource AS ENUM ('EXTJS', 'PORTAL', 'UNIFIED_VIEW');
ALTER TABLE tloggings ADD COLUMN IF NOT EXISTS uisource EventSource DEFAULT 'EXTJS';

DELETE FROM xlinkloggings WHERE xid NOT IN (SELECT xid FROM tloggings);
DELETE FROM xlinklogginggroups WHERE xid NOT IN (SELECT xid FROM tloggings);
DELETE FROM xlinkloggingswat WHERE xid NOT IN (SELECT xid FROM tloggings);

ALTER TABLE xlinkloggings ADD CONSTRAINT xlinkloggings_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;
ALTER TABLE xlinklogginggroups ADD CONSTRAINT xlinklogginggroups_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;
ALTER TABLE xlinkloggingswat ADD CONSTRAINT xlinkloggingswat_event FOREIGN KEY (xid) REFERENCES tloggings(xid) ON DELETE CASCADE;

DROP FUNCTION IF EXISTS trigger_deletelogging() CASCADE;