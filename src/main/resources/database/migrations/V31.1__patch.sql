INSERT INTO credentialclasses(id, name, hidden) VALUES (22, 'Role session name', true);

CREATE TABLE compliancepolicies (
	id SERIAL PRIMARY KEY,

	key TEXT,
	name TEXT,
	description TEXT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE TABLE compliancerequirements (
	id SERIAL PRIMARY KEY,

	policyId INTEGER REFERENCES compliancepolicies(id) ON DELETE CASCADE NOT NULL,
	key TEXT,
	audit TEXT,
	description TEXT,
	level INTEGER,
	nameLong TEXT,
	nameShort TEXT,
	requirementId TEXT,
	solution TEXT,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE,
	created TIMES<PERSON><PERSON> WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

CREATE INDEX compliancerequirements_key ON compliancerequirements(key);

CREATE TABLE compliancefindings (
	id SERIAL PRIMARY KEY,

	assetidentifierid INTEGER REFERENCES assetidentifiers(id) ON DELETE CASCADE NOT NULL,
	requirementid INTEGER REFERENCES compliancerequirements(id) ON DELETE CASCADE NOT NULL,
	matchid INTEGER REFERENCES matches(id) ON DELETE CASCADE NOT NULL,

	customerid INTEGER REFERENCES customers(id) ON DELETE CASCADE NOT NULL,
	created TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	updated TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
	createdby TEXT,
	updatedby TEXT,
	createdbyid INTEGER,
	updatedbyid INTEGER,
	deleted TIMESTAMP WITH TIME ZONE
);

ALTER TABLE compliancefindings ADD falsepositive TIMESTAMP WITH TIME ZONE;
ALTER TABLE compliancefindings ADD falsepositivecomment TEXT;
ALTER TABLE compliancefindings ADD falsepositiveby TEXT;
ALTER TABLE compliancefindings ADD exception TIMESTAMP WITH TIME ZONE;
ALTER TABLE compliancefindings ADD exceptioncomment TEXT;
ALTER TABLE compliancefindings ADD exceptionuntil TIMESTAMP WITH TIME ZONE;
ALTER TABLE compliancefindings ADD exceptionby TEXT;

ALTER TABLE consumptionstats ADD cloudsecassets INTEGER DEFAULT 0;
ALTER TABLE consumptionstats ADD cloudsecscans INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD cloudsecconfigurations INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsaws INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsgcp INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsazure INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsvsphere INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsbasic INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD accountsweb INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsaws INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsgcp INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsazure INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsvsphere INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsbasic INTEGER DEFAULT 0;
ALTER TABLE usagestats ADD applianceaccountsweb INTEGER DEFAULT 0;

CREATE TABLE tag_compliancefinding (
	tagid INTEGER REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
	compliancefindingid INTEGER REFERENCES compliancefindings(id) ON DELETE CASCADE NOT NULL,
	PRIMARY KEY (tagid, compliancefindingid)
);

UPDATE scanlogs SET started = updated, ended = updated, status = 'FAILED', statusdetails = NULL WHERE status = 'STOPPING' AND created < now() - '5 days'::interval;

ALTER TABLE customers ADD awsexternalid UUID NOT NULL DEFAULT gen_random_uuid();

DELETE FROM credentials WHERE classid IN (20,22);
UPDATE credentials SET classid = 20 WHERE classid = 21;
DELETE FROM credentialclasses WHERE id IN (21,22);
UPDATE credentialclasses SET name = 'Regions', hidden = false WHERE id = 20;

UPDATE credentialclasses SET hidden = false WHERE id = 19;

DROP TRIGGER IF EXISTS update_assetidentifiers_metadata ON assetidentifiers;

ALTER TABLE assetidentifiers ALTER COLUMN ownership DROP DEFAULT;
ALTER TABLE assetidentifiers ADD customownership INTEGER CHECK(customownership >= 0 AND customownership <= 100);
UPDATE assetidentifiers SET ownership = NULL;

CREATE INDEX compliancefindings_customerid ON compliancefindings(customerid);

CREATE TYPE compliancepolicytype AS ENUM ('AWS', 'GCP');
ALTER TABLE compliancepolicies ADD type compliancepolicytype;
UPDATE compliancepolicies SET type = 'AWS';
ALTER TABLE compliancepolicies ALTER COLUMN type SET NOT NULL;

DROP VIEW IF EXISTS compliancefindingsview CASCADE;
ALTER TABLE compliancerequirements DROP COLUMN level;
ALTER TABLE compliancerequirements ADD scored BOOLEAN;

