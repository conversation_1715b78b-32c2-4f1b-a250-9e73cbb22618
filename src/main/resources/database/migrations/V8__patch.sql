ALTER TABLE tuserdatas ADD compliancesenabled text;
ALTER TABLE treportschedules ADD COLUMN zip boolean default false;

INSERT INTO tloggings (xid, xrefid, xuserxid, xsubuserxid, assignee, recipient, eventname, daysinadvance, myscans, newfindings, emailencryptionkey) SELECT nextval('tloggings_seq'), 59, xid, -1, ARRAY[xid], vcemail, 'HIAB: Disk Usage High', 90, 0, 0, 'Unencrypted' FROM tusers WHERE xid = 11 AND bactive = 1 AND vctype = 'COM';

ALTER TABLE tscanners ADD COLUMN isawsscanner smallint default 0;
ALTER TABLE tscanners ADD COLUMN scanningdisabled smallint default 0;

create index swat_test on treport_vulns(swatstatus) where coalesce(swatstatus,0)>0;

ALTER TABLE tusers ADD COLUMN snenabled boolean default false;

ALTER TABLE tuserdatas ADD COLUMN snsysid text;

CREATE TABLE sntickets (
	xid BIGINT NOT NULL,
	snsysid text,
	xuserxid bigint NOT NULL,
	sntable text,
	snshortdesc text,
	createticket boolean,
	findingid BIGINT
);

CREATE SEQUENCE sntickets_seq
	START WITH 1000;

CREATE TABLE snassets (
	xid BIGINT NOT NULL,
	snsysid text,
	xuserxid bigint NOT NULL,
	sntable text,
	snassettag text,
	snname text
);

CREATE SEQUENCE snassets_seq
	START WITH 1000;

CREATE TABLE servicenowservers (
	xid BIGINT NOT NULL,
	snuri text,
	snuserName text,
	snpassword text,
	snenabled boolean
);
CREATE SEQUENCE swatlicenses_seq START WITH 1000;

CREATE TABLE swatlicenses(
	id BIGINT PRIMARY KEY,
	applicationid BIGINT,
	type INTEGER,
	startDate DATE,
	endDate DATE
);

ALTER TABLE swatlicenses ADD FOREIGN KEY (applicationid) REFERENCES swatschedules(id) ON DELETE CASCADE;

ALTER TABLE wasxschedules ADD lastreportid BIGINT;



CREATE OR REPLACE FUNCTION modifyRules() RETURNS VOID AS $$
DECLARE
	_trigger BOOLEAN;
BEGIN
	SELECT EXISTS(SELECT tgname FROM pg_trigger WHERE tgname = 'biu_trules') INTO _trigger;
	IF _trigger THEN
		DROP TRIGGER IF EXISTS biu_trules ON trules;
	END IF;

	UPDATE trules SET deleted = false WHERE deleted IS NULL;

	IF _trigger THEN
		CREATE TRIGGER biu_trules
		BEFORE INSERT OR UPDATE ON trules
		FOR EACH ROW
		EXECUTE PROCEDURE trigger_rule();
	END IF;
END;
$$
	LANGUAGE plpgsql;

SELECT modifyRules();

DROP FUNCTION IF EXISTS modifyRules();


CREATE TABLE trulesissues (
	ruleid BIGINT,
	updated TIMESTAMP WITHOUT TIME ZONE,
	issue TEXT
);

ALTER TABLE trulesissues ADD CONSTRAINT trulesissues_ruleid_fkey FOREIGN KEY (ruleid) REFERENCES trules(ruleid) ON DELETE CASCADE;

ALTER TABLE trules ADD lastnotification TIMESTAMP WITHOUT TIME ZONE;

CREATE SEQUENCE downloadentries_seq;

CREATE TABLE downloadentries(
	id BIGINT DEFAULT NEXTVAL('downloadentries_seq'),
	created TIMESTAMP WITHOUT TIME ZONE,
	userId BIGINT,
	subuserId BIGINT,
	fileName TEXT,
	readableName TEXT,
	contentType VARCHAR(500),
	contentDisposition VARCHAR(500),
	contentTransferEncoding VARCHAR(100),
	key VARCHAR(200),
	status VARCHAR(50),
	size VARCHAR(50),
	checksum VARCHAR(50));

ALTER TABLE ONLY downloadentries ADD CONSTRAINT downloadentries_user FOREIGN KEY (userid) REFERENCES tusers(xid) ON DELETE CASCADE;

CREATE SEQUENCE thiabstatshistory_seq START WITH 1000;

CREATE TABLE thiabstatshistory (
	id BIGINT UNIQUE DEFAULT nextval('thiabstatshistory_seq'),
	hiabstatid BIGINT,
	xuserxid bigint NOT NULL,
	xtime timestamp without time zone DEFAULT NOW(),
	iips integer,
	itest integer,
	itype smallint DEFAULT 0 NOT NULL,
	scheduler smallint default 1,
	scansleft bigint default 0,
	webappsused BIGINT DEFAULT 0,
	webappscansleft BIGINT DEFAULT 0,
	webappscansused BIGINT DEFAULT 0,
	virtual smallint default 0,
	hwaddr VARCHAR(32),
	revoked BOOLEAN DEFAULT FALSE NOT NULL,
	version TEXT,
	lastUpdate TIMESTAMP WITHOUT TIME ZONE,
	os TEXT
);


ALTER TABLE consultancytokens ADD subuserid BIGINT;
 

DROP VIEW IF EXISTS vsubuser CASCADE;
ALTER TABLE tpasswordpolicy ADD ssoonly SMALLINT DEFAULT 0;

DROP VIEW IF EXISTS vuser CASCADE;
DROP VIEW IF EXISTS vsubuser CASCADE;
ALTER TABLE tusers ADD lastconsumedotptimeindex integer DEFAULT 0;
ALTER TABLE tsubusers ADD lastconsumedotptimeindex integer DEFAULT 0;

CREATE SEQUENCE wasxconfigurations_seq START WITH 1000;

CREATE TABLE wasxconfigurations (
	id BIGINT PRIMARY KEY DEFAULT nextval('wasxconfigurations_seq'),
	userid BIGINT NOT NULL REFERENCES tusers(xid) ON DELETE CASCADE,
	name VARCHAR(250) NOT NULL,
	scannerId BIGINT NOT NULL DEFAULT 0,
	subuserId BIGINT NOT NULL DEFAULT -1,
	dcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	updated TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
	deleted BOOLEAN DEFAULT FALSE,
	maxscantimeMinutes INTEGER NOT NULL DEFAULT 120,
	comment TEXT,
	latestScanDuration INTERVAL,
	totalScanDuration INTERVAL,
	totalScans INT NOT NULL DEFAULT 0,
	settings TEXT NOT NULL);


DROP VIEW IF EXISTS vscheduleobject CASCADE;
DROP VIEW IF EXISTS vuser CASCADE;
DROP VIEW IF EXISTS vsubuser CASCADE;
ALTER TABLE tscheduleobjects ADD useglobalignoretargetlist SMALLINT DEFAULT 0;
ALTER TABLE tusers ADD useglobalignoretargetlistbydefault BOOLEAN DEFAULT FALSE;
ALTER TABLE tusers ADD globalignoretargetlist TEXT;
ALTER TABLE tsubusers ADD useglobalignoretargetlistbydefault BOOLEAN DEFAULT FALSE;
ALTER TABLE tsubusers ADD globalignoretargetlist TEXT;

CREATE TABLE treportfiletokens(
	token TEXT NOT NULL,
	userid BIGINT NOT NULL,
	subuserid BIGINT NOT NULL,
	expire TIMESTAMP NOT NULL,
	fileid BIGINT NOT NULL
);

ALTER TABLE treportfiletokens ADD CONSTRAINT treportfiletokens_fileid FOREIGN KEY (fileid) REFERENCES treportfiles(id) ON DELETE CASCADE;

DROP VIEW IF EXISTS vappaccess;
ALTER TABLE tappaccess ALTER ipaddress TYPE VARCHAR(64);

