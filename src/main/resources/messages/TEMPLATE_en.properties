signature = \n\
\n\
Best regards,\n\
\n\
[:ORGANIZATION]\n\
\n\
		
hiab.signature = \n\
This notification is sent from your [:SYSTEM] Security Scanner.\n\
\n\
To access the [:SYSTEM] open https://[:BASEHREF] in a browser.\n\
		
header = \n\
Dear [:FIRSTNAME] [:LASTNAME] on behalf of [:COMPANY],\n\
		
event.info = \n\
This notification is sent due to event "[:EVENTNAME]", setup by [:EVENTCREATEDBY].\n\
   
forgotlogin.subject = [:SYSTEM]: Login Recovery
forgotlogin.body = \n\
[:SYSTEM] has given you a new login:\n\
\n\
User: [:USERNAME]\n\
Password: [:PASSWORD]\n\
\n\
Please log in at <a href="https://[:BASEHREF]">https://[:BASEHREF]</a> and change your password.\n\
		
restorepassword.subject = [:SYSTEM]: Password Recovery
restorepassword.body = \n\
[:SYSTEM] has generated a password recovery URL which is valid for 30 minutes:\n\
\n\
Please visit <a href="https://[:BASEHREF][:RESTOREURL]">https://[:BASEHREF][:RESTOREURL]</a> and change your password.\n\
		
accountnotification.subject = [:SYSTEM]: New Account
accountnotification.body = \n\
You now have a new account, use the following to log in:\n\
\n\
User: [:USERNAME]\n\
Password: [:PASSWORD]\n\
Email: [:EMAIL]\n\
\n\
[:CHANGEPASSWORD]\n\
		
accountnotification.changepassword = Please log in at <a href="https://[:BASEHREF]">https://[:BASEHREF]></a> and change your password.
accountnotification.ldap = The account uses LDAP/AD credentials for authentication.
accountnotification.assessment.subject = [:SYSTEM]: New Account
accountnotification.assessment.body = \n\
A new account have been created for your assessment at <a href="https://[:BASEHREF]">https://[:BASEHREF]></a>, use the following to log in:\n\
\n\
User: [:USERNAME]\n\
Password: [:PASSWORD]\n\
\n\
The account is prepared for the assessment, and we would urge you to not make any changes to the account or utilize the scanner\n\
before the assessment is finished and you have received your report. Using scans from the account prior to the assessment is\n\
finished will deduct from what is included in the assessment.\n\
\n\
Should you have any questions relating to your assessment, please submit a support ticket at: [:MSSERVICEDESK] and we will get back to you as soon as possible.\n\
		
accountnotification.managedservice.subject = [:SYSTEM]: New Account
accountnotification.managedservice.body = \n\
Your managed services account has been activated at https://[:BASEHREF], use the following to log in:\n\
\n\
User: [:USERNAME]\n\
Password: [:PASSWORD]\n\
\n\
You will soon be contacted by a Managed Services specialist for further information and planing of goals and methods.\n\
As soon as a report is added for download you will receive an alert by email.\n\
\n\
Should you have any questions relating to your managed services, please submit a support ticket at: [:MSSERVICEDESK] and we will get back to you as soon as possible.\n\
		
template.securitylow.subject = [:SYSTEM]: Low number of scans remaining
template.securitylow.body = \n\
Your [:SYSTEM] account is running low on [:PCI]scans. You have less than 10% of your license remaining. Please contact your account manager for more information about adding additional scans.\n\
	
template.securitylow.sales.subject = [:SYSTEM]: Low number of scans remaining
template.securitylow.sales.body = \n\
The [:SYSTEM] account for [:CUSTOMERNAME] is low on [:PCI]scans. They have less than 10% of the license remaining.\n\
	
template.newworkflowtask.subject = [:SYSTEM]: [TICKET [:TASKID]] [:STATUS] [:ALIAS]
template.newworkflowtask.body = \n\
[:SYSTEM] ticket manager:\n\
\n\
New ticket from [:CFULLNAME] ([:CEMAIL]):\n\
\n\
Ticket ID: [:TASKID]\n\
Alias: [:ALIAS]\n\
Status: [:STATUS]\n\
Priority: [:PRIORITY]\n\
Type: [:TYPE]\n\
Due date: [:DUEDATE]\n\
Assigned: [:ASSIGNEE] ([:EMAIL])\n\
\n\
Message :\n\
----------------------------------------------------------------------------\n\
[:MESSAGE]\n\
----------------------------------------------------------------------------\n\
\n\
[:VULINFO]\n\
\n\
Log into your account at https://[:BASEHREF] to view this ticket.\n\
		
template.newworkflowtask.body.html = \n\
<br>\n\
<b>[:SYSTEM] ticket manager</b>\n\
<br><br>\n\
<b>New assignment from <a href="mailto:[:CEMAIL]">[:CFULLNAME]</a>:</b>\n\
<br><br>\n\
<table border="0" cellspacing="2" cellpadding="0" summary="">\n\
<tr><td><b>Ticket ID  :</b></td><td>[:TASKID]</td></tr>\n\
<tr><td><b>Alias    :</b></td><td> [:ALIAS]</td></tr>\n\
<tr><td><b>Status   :</b></td><td> [:STATUS]</td></tr>\n\
<tr><td><b>Priority :</b></td><td> [:PRIORITY]</td></tr>\n\
<tr><td><b>Type     :</b></td><td> [:TYPE]</td></tr>\n\
<tr><td><b>Due date :</b></td><td> [:DUEDATE]</td></tr>\n\
<tr><td><b>Assigned :</b></td><td> <a href="mailto:[:AEMAIL]">[:ASSIGNEE]</a></td></tr>\n\
</table>\n\
<br>\n\
<b>Message :</b><br>\n\
<hr>\n\
[:MESSAGE]\n\
<hr>\n\
<br>\n\
<br>\n\
[:VULINFO]\n\
<br>\n\
\n\
Log into your account at <a href="/">https://[:BASEHREF]</a> to process the ticket.\n\
		
template.changeworkflowtask.subject = [:SYSTEM]: [TICKET [:TASKID]] [:STATUS] [:ALIAS]
template.changeworkflowtask.body = \n\
[:SYSTEM] ticket manager:\n\
\n\
Ticket changed by [:CFULLNAME] ([:CEMAIL]):\n\
\n\
Ticket ID: [:TASKID]\n\
Alias: [:ALIAS]\n\
Status: [:STATUS]\n\
Priority: [:PRIORITY]\n\
Type: [:TYPE]\n\
Due date: [:DUEDATE]\n\
Assigned: [:ASSIGNEE] ([:AEMAIL])\n\
\n\
Message:\n\
----------------------------------------------------------------------------\n\
[:MESSAGE]\n\
----------------------------------------------------------------------------\n\
\n\
[:VULINFO]\n\
\n\
Log into your account at https://[:BASEHREF] to process the ticket.\n\
		
template.changeworkflowtask.body.html = \n\
<br>\n\
<b>[:SYSTEM] ticket manager</b>\n\
<br><br>\n\
<b>Assignment changed by <a href="mailto:[:CEMAIL]">[:CFULLNAME]</a>:</b>\n\
<br><br>\n\
<table border="0" cellspacing="2" cellpadding="0" summary="">\n\
<tr><td><b>Ticket ID: </b></td><td> [:TASKID]</td></tr>\n\
<tr><td><b>Alias: </b></td><td> [:ALIAS]</td></tr>\n\
<tr><td><b>Status: </b></td><td> [:STATUS]</td></tr>\n\
<tr><td><b>Priority: </b></td><td> [:PRIORITY]</td></tr>\n\
<tr><td><b>Type: </b></td><td> [:TYPE]</td></tr>\n\
<tr><td><b>Due date: </b></td><td> [:DUEDATE]</td></tr>\n\
<tr><td><b>Assigned: </b></td><td> <a href="mailto:[:AEMAIL]">[:ASSIGNEE]</a></td></tr>\n\
</table>\n\
<br>\n\
<b>Message: </b><br>\n\
<hr>\n\
[:MESSAGE]\n\
<hr>\n\
<br>\n\
<br>\n\
[:VULINFO]\n\
<br>\n\
Log into your account at <a href="/">https://[:BASEHREF]</a> to process the ticket.\n\
		
template.newworkflowtask.task = \n\
Ticket ID: [:TASKID]\n\
Summary: [:SUMMARY]\n\
Alias: [:ALIAS]\n\
Status: [:STATUS]\n\
Priority: [:PRIORITY]\n\
Type: [:TYPE]\n\
Due date: [:DUEDATE]\n\
Assigned: [:ASSIGNEE] ([:EMAIL])\n\
Message:\n\
----------------------------------------------------------------------------\n\
[:MESSAGE]\n\
----------------------------------------------------------------------------\n\
\n\
[:VULINFO]\n\
\n\
		
template.newworkflowtask.task.html = \n\
<table border="0" cellspacing="2" cellpadding="0" summary="">\n\
<tr><td><b>Ticket ID: </b></td><td> [:TASKID]</td></tr>\n\
<tr><td><b>Alias: </b></td><td> [:ALIAS]</td></tr>\n\
<tr><td><b>Status: </b></td><td> [:STATUS]</td></tr>\n\
<tr><td><b>Priority: </b></td><td> [:PRIORITY]</td></tr>\n\
<tr><td><b>Type: </b></td><td> [:TYPE]</td></tr>\n\
<tr><td><b>Due date: </b></td><td> [:DUEDATE]</td></tr>\n\
<tr><td><b>Assigned: </b></td><td> <a href="mailto:[:EMAIL]">[:ASSIGNEE]</a></td></tr>\n\
</table>\n\
<b>Message: </b><br>\n\
<hr>\n\
[:MESSAGE]\n\
<hr>\n\
<br>\n\
[:VULINFO]\n\
<br>\n\
	
template.changeworkflowtask.task = \n\
Ticket ID: [:TASKID]\n\
Summary: [:SUMMARY]\n\
Alias: [:ALIAS]\n\
Status: [:STATUS]\n\
Priority: [:PRIORITY]\n\
Type: [:TYPE]\n\
Due date: [:DUEDATE]\n\
Assigned: [:ASSIGNEE] ([:AEMAIL])\n\
Message:\n\
----------------------------------------------------------------------------\n\
[:MESSAGE]\n\
----------------------------------------------------------------------------\n\
\n\
[:VULINFO]\n\
\n\
		
template.changeworkflowtask.task.html = \n\
<table border="0" cellspacing="2" cellpadding="0" summary="">\n\
<tr><td><b>Ticket ID: </b></td><td> [:TASKID]</td></tr>\n\
<tr><td><b>Alias: </b></td><td> [:ALIAS]</td></tr>\n\
<tr><td><b>Status: </b></td><td> [:STATUS]</td></tr>\n\
<tr><td><b>Priority: </b></td><td> [:PRIORITY]</td></tr>\n\
<tr><td><b>Type: </b></td><td> [:TYPE]</td></tr>\n\
<tr><td><b>Due date: </b></td><td> [:DUEDATE]</td></tr>\n\
<tr><td><b>Assigned: </b></td><td> <a href="mailto:[:AEMAIL]">[:ASSIGNEE]</a></td></tr>\n\
</table>\n\
<b>Message: </b><br>\n\
<hr>\n\
[:MESSAGE]\n\
<hr>\n\
<br>\n\
[:VULINFO]\n\
<br>\n\
	
template.workflowtasks.subject = [:SYSTEM]: Tickets 
template.workflowtasks.body = \n\
[:SYSTEM] Ticket Manager:\n\
\n\
[:TICKETCOUNT] ticket(s) from [:CFULLNAME] ([:CEMAIL]):\n\
\n\
[:TICKETS]\n\
\n\
Log into your account at https://[:BASEHREF] to process the ticket.\n\
		
template.workflowtasks.body.html = \n\
<br>\n\
<b>[:SYSTEM] Ticket Manager</b>\n\
<br><br>\n\
<b>[:TICKETCOUNT] ticket(s) from <a href="mailto:[:CEMAIL]">[:CFULLNAME]</a>:</b>\n\
<br><br>\n\
[:TICKETSHTML]\n\
<br>\n\
Log into your account at <a href="/">https://[:BASEHREF]</a> to process the ticket.\n\
		
template.reminderworkflowtask.task = \n\
Ticket ID: [:TASKID]\n\
Summary: [:SUMMARY]\n\
Alias: [:ALIAS]\n\
Status: [:STATUS]\n\
Priority: [:PRIORITY]\n\
Type: [:TYPE]\n\
Due date: [:DUEDATE]\n\
Assigned: [:ANAME] ([:AEMAIL])\n\
\n\
Message:\n\
----------------------------------------------------------------------------\n\
[:MESSAGE]\n\
----------------------------------------------------------------------------\n\
\n\
[:VULINFO]\n\
\n\
		
template.reminderworkflowtask.task.html = \n\
<table border="0" cellspacing="2" cellpadding="0" summary="">\n\
<tr><td><b>Ticket ID: </b></td><td> [:TASKID]</td></tr>\n\
<tr><td><b>Alias: </b></td><td> [:ALIAS]</td></tr>\n\
<tr><td><b>Status: </b></td><td> [:STATUS]</td></tr>\n\
<tr><td><b>Priority: </b></td><td> [:PRIORITY]</td></tr>\n\
<tr><td><b>Type: </b></td><td> [:TYPE]</td></tr>\n\
<tr><td><b>Due date: </b></td><td> [:DUEDATE]</td></tr>\n\
<tr><td><b>Assigned: </b></td><td> <a href="mailto:[:AEMAIL]">[:ASSIGNEE]</a></td></tr>\n\
</table>\n\
<b>Message: </b><br>\n\
<hr>\n\
[:MESSAGE]\n\
<hr>\n\
<br>\n\
[:VULINFO]\n\
<br>\n\
		
template.reminderworkflowtask.subject = [:SYSTEM]: Ticket reminders
template.reminderworkflowtask.body = \n\
[:SYSTEM] Ticket Manager:\n\
\n\
[:TICKETCOUNT] ticket reminder(s):\n\
\n\
[:TICKETS]\n\
\n\
Log into your account at https://[:BASEHREF] to process the tickets.\n\
		
template.reminderworkflowtask.body.html = \n\
<br>\n\
<b>[:SYSTEM] Ticket Manager</b>\n\
<br><br>\n\
<b>[:TICKETCOUNT] ticket reminder(s):\n\
<br><br>\n\
[:TICKETSHTML]\n\
<br>\n\
Log into your account at <a href="/">https://[:BASEHREF]</a> to process the ticket.\n\
		
template.overdueworkflowtask.subject = [:SYSTEM]: Ticket overdue notification
template.overdueworkflowtask.body = \n\
[:SYSTEM] Ticket Manager:\n\
\n\
Due date for following [:TICKETCOUNT] ticket(s) has been passed.\n\
\n\
[:TICKETS]\n\
\n\
Log into your account at https://[:BASEHREF] to process the tickets.\n\
		
template.overdueworkflowtask.body.html = \n\
<br>\n\
<b>[:SYSTEM] Ticket Manager</b>\n\
<br><br>\n\
<b>Due date for following [:TICKETCOUNT] ticket(s) has been passed.\n\
<br><br>\n\
[:TICKETSHTML]\n\
<br>\n\
Log into your account at <a href="/">https://[:BASEHREF]</a> to process the ticket.\n\
		
testattachment = This is a email test attachment to verify your PGP key.
testemail.text.body = \n\
This is a test email from [:SYSTEM].\n\
	
testemail.text.subject = This is a test email from [:SYSTEM].
testsms.text = This is a test sms from [:SYSTEM].
template.hiab.email.alert.update.subject = [:SYSTEM]: Update failed.
template.hiab.email.alert.update.body = \n\
Your [:SYSTEM] was unable to update. The configuration of your [:SYSTEM] only allows updates when no scans are running. The update has been attempting to run for twenty four hours but can not find a time when no scans are running.\n\
\n\
To allow your [:SYSTEM] to update, enable updating while scans are running or make a space between your scans for an update to run.\n\
		
toomanyfindings = The output from [:JOBNAME] for one or more of the targets is very large. This is an indication that the scan was blocked by an active IPS. As it is likely that the report includes a large number of false positives this report will not be automatically generated, but is available on request.\n\

noencryptionkeyfound = \n\
Your account in [:SYSTEM] has been configured to send reports via email. The report could not be sent, check your public key or make sure to include a file format in which to attach the report.\n\
\n\
Please log into your account at https://[:BASEHREF] and upload a valid key for your account under the event system.\n\

failed.generate.report = \n\
Your account in [:SYSTEM] has been configured to send reports via email. The report could not be generated.\n\

scheduling.notstarted.subject = [:SYSTEM]: [:SCANSCHEDULENAME] was not started as scheduled.
scheduling.notstarted.body = \n\
[:SYSTEM] could not start the [:SCANSCHEDULENAME] scan schedule.\n\
\n\
The following error was recorded by [:SYSTEM]:\n\
[:REASON]\n\
	
rejectdispute.subject = [:ORGANIZATION] PCI Scan for [:SCANJOB] finding [:SCRIPTID] dispute REJECTED. 
rejectdispute.body = \n\
Finding [:SCRIPTID] was found to be accurate when detected in [:SCANJOB] on [:REPORTHOST]. Please remediate this vulnerability and then run another scan.\n\
[:COMMENT]\n\
If you have any questions please contact our support team.\n\
\n\
Best Regards,\n\
\n\
[:LOGGEDONUSER]\n\
[:ORGANIZATION]\n\
		
disputeshandled.subject = [:ORGANIZATION] PCI Disputes handled
disputeshandled.body = \n\
The reported disputes has now been processed. Please log in on your account at https://[:BASEHREF] and follow up on your disputes.\n\
\n\
If you have any questions regarding the disputes, please don't hesitate to contact support.\n\
\n\
Best regards,\n\
[:ORGANIZATION] Support Team\n\

event.email.4.subject = [:SYSTEM]: High risk vulnerability
event.email.4.body = \n\
[:SCANMODE] [:REPORTHOST] [:REPORTHOSTNAME] the following high risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.2.subject = [:SYSTEM]: Medium risk vulnerability
event.email.2.body = \n\
[:SCANMODE] [:REPORTHOST] [:REPORTHOSTNAME] the following medium risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.1.subject = [:SYSTEM]: Low risk vulnerability
event.email.1.body = \n\
[:SCANMODE] [:REPORTHOST] [:REPORTHOSTNAME] the following low risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.4.notarget.subject = [:SYSTEM]: High risk vulnerability
event.email.4.notarget.body = \n\
[:SCANMODE] the following high risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.2.notarget.subject = [:SYSTEM]: Medium risk vulnerability
event.email.2.notarget.body = \n\
[:SCANMODE] the following medium risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.1.notarget.subject = [:SYSTEM]: Low risk vulnerability
event.email.1.notarget.body = \n\
[:SCANMODE] the following low risk vulnerabilities were found:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.5.subject = [:SYSTEM]: Vulnerability report available
event.email.5.body = \n\
Scan of [:REPORTHOST] is complete.\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.6.subject = [:SYSTEM]: Large report
event.email.6.body = \n\
The output from [:REPORTHOST] for one or more of the targets is very large. This could be an indication of false positives. [:REPORTINFO]\n\
		
event.6.report = This report will not be automatically generated, but is available on request.
event.6.stopped = The target has too many open ports and the scan was stopped.
event.email.7.subject = [:SYSTEM]: Scan started
event.email.7.body = \n\
Scan of [:REPORTHOST] has started.\n\

event.email.8.subject = [:SYSTEM]: Scan was not completed due to an error.
event.email.8.body = \n\
[:REPORTHOST] ran for more than the twelve hour time limit. [:SYSTEM] stops scans and discards the report when they reach the time limit without completing.\n\
		
event.email.9.subject = [:SYSTEM]: Scan Stopped
event.email.9.body = \n\
The scan of [:REPORTHOST] has been stopped and the report will be discarded.\n\
	
event.email.10.subject = [:SYSTEM]: Scan Failed
event.email.10.body = \n\
The scan of [:REPORTHOST] has failed and the report will be discarded.\n\
The following error was recorded by [:SYSTEM]:\n\
[:REASON]\n\
	
event.email.15.subject = [:SYSTEM]: Update succeeded
event.email.15.body = \n\
Update has run successfully on [:SYSTEM] with MAC address [:HWADDR].\n\
		
event.email.16.subject = [:SYSTEM]: Rebooted
event.email.16.body = \n\
[:SYSTEM] has been rebooted.\n\
		
event.email.17.subject = [:SYSTEM]: Remote Support notification
event.email.17.body = \n\
[:SYSTEM]: Remote Support has been [:BACKCHANNELSTATUS].\n\
		
event.email.18.subject = [:SYSTEM]: Backup succeeded
event.email.18.body = \n\
[:SYSTEM] backup has completed successfully.\n\
		
event.email.0.subject = [:SYSTEM]: Information found
event.email.0.body = \n\
[:SYSTEM] has found the following informations\n\
\n\
[:scriptname]\n\
\n\
 that could be used to understand vulnerabilities on [:REPORTHOST] [:REPORTHOSTNAME].\n\
		
event.email.0.notarget.subject = [:SYSTEM]: Information found
event.email.0.notarget.body = \n\
[:SYSTEM] has found the following informations\n\
\n\
[:scriptname]\n\
 		
event.email.20.subject = [:SYSTEM]: Discovery Scan Results for job: [:JOBNAME]
event.email.20.body = \n\
[:DISCOVERY_DELTA_MSG]\n\
\n\
Total number of targets that responded to our scans: [:ALIVE_LIST_SIZE]\n\
Total number of targets that did not respond to our scans: [:DEAD_LIST_SIZE]\n\
\n\
These targets were scanned:\n\
\n\
[:TESTED_LIST]\n\
\n\
The following targets responded to our scans:\n\
\n\
[:ALIVE_LIST]\n\
\n\
The following targets did not respond to our scans:\n\
\n\
[:DEAD_LIST]\n\
\n\
These targets will be added to [:SYSTEM]:\n\
\n\
[:ADDED_LIST]\n\
\n\
[:PREVIOUSLY_ADDED]\n\
\n\
[:NOT_ADDED]\n\
		
event.20.list = [:HOST] [:MAC] [:HOSTNAME] [:NETBIOS]\n\

event.20.notadded = These targets will not be added to [:SYSTEM]:\n\
\n\
[:NOT_ADDED_LIST]
event.20.previouslyadded = These targets have previously been added to [:SYSTEM]:\n\
\n\
[:PREVIOUSLY_ADDED_LIST]
event.email.20.was.subject = [:SYSTEM]: WAS Discovery Results
event.email.20.was.body = \n\
These URIs were scanned:\n\
\n\
[:URILIST]\n\
\n\
Log into your account at https://[:BASEHREF] to see the discovery results.\n\
		
event.email.21.subject = [:SYSTEM]: Discovery Alive Results for job: [:JOBNAME]
event.email.21.body = \n\
The following targets responded to our scans:\n\
\n\
[:ALIVE_LIST]\n\
\n\
These targets were scanned:\n\
\n\
[:TESTED_LIST]\n\
		
event.21.list = [:HOST] [:MAC] [:HOSTNAME] [:NETBIOS]\n\

event.email.22.subject = [:SYSTEM]: Discovery Dead Results for job: [:JOBNAME]
event.email.22.body = \n\
The following targets did not respond to our scans:\n\
\n\
[:DEAD_LIST]\n\
\n\
These targets were scanned:\n\
\n\
[:TESTED_LIST]\n\
		
event.22.list = [:HOST] [:MAC] [:HOSTNAME] [:NETBIOS]\n\

event.email.48.subject = [:SYSTEM]: Discovery Not Responding Targets (Consecutive Scans)
event.email.48.body = \n\
The following targets did not respond to previous [:INACTIVEDISCOVERIES] consecutive discovery scans:\n\
\n\
[:DEAD_LIST]\n\
		
event.48.list = [:HOST] [:MAC] [:HOSTNAME] [:NETBIOS]
event.email.23.subject = [:SYSTEM]: Discovery Added Results for job: [:JOBNAME]
event.email.23.body = \n\
These targets will be added to [:SYSTEM]:\n\
\n\
[:ADDED_LIST]\n\
\n\
[:PREVIOUSLY_ADDED]\n\
\n\
[:NOT_ADDED]\n\
\n\
These targets were scanned:\n\
\n\
[:TESTED_LIST]\n\
		
event.23.list = [:HOST] [:MAC] [:HOSTNAME] [:NETBIOS]\n\

event.23.notadded = These targets will not be added to [:SYSTEM]:\n\
\n\
[:NOT_ADDED_LIST]
event.23.previouslyadded = These targets have previously been added to [:SYSTEM]:\n\
\n\
[:PREVIOUSLY_ADDED_LIST]
event.email.24.subject = [:SYSTEM]: IP Change Notification
event.email.24.body = \n\
The following IP has been added:\n\
\n\
[:HOSTS]\n\
\n\
[:ADDNOTE]\n\
\n\
The change was made by [:LOGGEDONUSER].\n\
		
event.24.list = [:host]\n\

event.email.25.subject = [:SYSTEM]: IP Change Notification
event.email.25.body = \n\
The following IP has been removed:\n\
\n\
[:HOSTS]\n\
\n\
[:DELETENOTE]\n\
\n\
The change was made by [:LOGGEDONUSER].\n\
		
event.25.list = [:host]\n\

event.email.26.subject = Scan report from [:SYSTEM] for job: [:JOBNAME]
event.email.26.body = \n\
[:SYSTEM] has run the [:JOBNAME] scan schedule.\n\
\n\
The following hosts have been tested between [:_DATETIME_SCANSTART] and [:_DATETIME_SCANEND]:\n\
\n\
[:HOSTLIST]\n\
[:KEYMESSAGE]\n\
[:TIMEOUT]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.26.notarget.subject = Scan report from [:SYSTEM] for job: [:JOBNAME]
event.email.26.notarget.body = \n\
[:SYSTEM] has run the [:JOBNAME] scan schedule.\n\
\n\
The scan ran between [:_DATETIME_SCANSTART] and [:_DATETIME_SCANEND]:\n\
\n\
[:KEYMESSAGE]\n\
[:TIMEOUT]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.26.report =
event.26.list = [:HOST] - [:STATUS] [ Group: [:GROUP] ]\n\

event.26.list.risk = [:HOST] - [:STATUS] - High: [:HIGH] Medium: [:MEDIUM] Low: [:LOW] [ Group: [:GROUP] ]\n\

event.26.list.riskdelta = [:HOST] - [:STATUS] - Delta High: [:DELTAHIGH] Delta Medium: [:DELTAMEDIUM] Delta Low: [:DELTALOW] [ Group: [:GROUP] ]\n\

event.email.26.was.subject = Scan report from [:SYSTEM] for job: [:JOBNAME]
event.email.26.was.body = \n\
[:SYSTEM] has run the [:JOBNAME] scan schedule.\n\
\n\
The following URIs have been tested between [:_DATETIME_SCANSTART] and [:_DATETIME_SCANEND]:\n\
\n\
[:URILIST]\n\
[:KEYMESSAGE]\n\
[:TIMEOUT]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.30.subject = [:SYSTEM] User login
event.email.30.body = \n\
[:LOGINUSERNAME] [:STATUS] to [:SYSTEM].\n\
\n\
Remote host: [:ORIGIN]\n\
Time: [:_DATETIME_LOGINTIME]\n\
		
event.email.31.subject = [:SYSTEM]: Communication disturbance regarding [:SCANNERNAME]
event.email.31.body = \n\
The scheduler/scanner has detected that there seems to be some communication\n\
issues with the attached device: [:SCANNERNAME]\n\
		
event.email.32.subject = [:SYSTEM]: Automatic plan has been performed
event.email.32.body = \n\
The automatic plan defined for the [:SYSTEM] at [:BASEHREF] has been executed [:RESULT].\n\
		
event.email.33.subject = [:SYSTEM]: Update failed
event.email.33.body = \n\
Update has failed on [:SYSTEM] with MAC address [:HWADDR].\n\
[:ERROR]\n\
		
event.email.34.subject = [:SYSTEM]: Verification scan complete
event.email.34.body = \n\
The verification scan is complete for:\n\
\n\
[:VERIFYLIST]\n\
		
event.email.36.notarget.subject = [:SYSTEM]: Scan results update in [:JOBNAME]
event.email.36.notarget.body = \n\
After a scan update scan results is updated with:\n\
\n\
Scan Schedule: [:JOBNAME]\n\
\n\
[:ADDED]\n\
[:REMOVED]\n\
   		
event.email.36.subject = [:SYSTEM]: Scan results update for [:REPORTHOST] in [:JOBNAME]
event.email.36.body = \n\
After a scan update scan results is updated with:\n\
\n\
Target: [:REPORTHOST]\n\
Scan Schedule: [:JOBNAME]\n\
\n\
[:ADDED]\n\
[:REMOVED]
event.sms.36 = Scan update for [:REPORTHOST] found [:scriptname]
event.sms.36.notarget = Scan update found [:scriptname]
event.34.list = Script ID: [:SCRIPTID]\n\
Script name: [:SCRIPTNAME]\n\
Port: [:PORT]\n\
Target: [:REPORTHOST]\n\
Scan date: [:_DATETIME_REPORTDATE]\n\
Verification: [:RESULT]\n\
		
event.sms.50 = Risk ( [:scriptname] ) with public exploit found on [:REPORTHOST]
event.sms.4 = High risk ( [:scriptname] ) found on [:REPORTHOST]
event.sms.2 = Medium risk ( [:scriptname] ) found on [:REPORTHOST]
event.sms.1 = Low risk ( [:scriptname] ) found on [:REPORTHOST]
event.sms.4.notarget = High risk ( [:scriptname] ) found
event.sms.2.notarget = Medium risk ( [:scriptname] ) found
event.sms.1.notarget = Low risk ( [:scriptname] ) found
event.sms.0 = Information ( [:scriptname] )  found on [:REPORTHOST]
event.sms.0.notarget = Information ( [:scriptname] )  found
event.sms.5 = Report ready for [:REPORTHOST]
event.sms.6 = Large report for [:REPORTHOST]
event.sms.7 = Scan started on [:REPORTHOST]
event.sms.8 = Scan timed out on [:REPORTHOST]
event.sms.9 = Scan stopped on [:REPORTHOST]
event.sms.10 = Scan failed on [:REPORTHOST]
event.sms.15 = [:SYSTEM] has been updated
event.sms.16 = [:SYSTEM] has been rebooted
event.sms.18 = [:SYSTEM] backup: [:BACKUPSTATUS]
event.sms.19 = [:SYSTEM] system restarted
event.sms.20 = Discovery done: [:TESTED_LIST] - Alive: [:ALIVE_LIST_SIZE] - Dead: [:DEAD_LIST_SIZE] - Added: [:ADDED_LIST_SIZE]
event.sms.21 = Discovery done: Alive: [:ALIVE_LIST] - Tested: [:TESTED_LIST]
event.sms.22 = Discovery done: DEAD: [:DEAD_LIST] - Tested: [:TESTED_LIST]
event.sms.23 = Discovery done: ADDED: [:ADDED_LIST] - Tested: [:TESTED_LIST]
event.sms.24 = Target added: [:HOSTS]
event.sms.25 = Target removed: [:HOSTS]
event.sms.26 = Scan done: [:JOBNAME] - Completed: [:HOSTLIST_SIZE]
event.sms.30 = User logged in: [:LOGINUSERNAME] Status: [:STATUS]
event.sms.35 = [:REPORTHOST] was not reachable when scanning
event.email.59.subject = [:SYSTEM] diskspace warning
event.email.59.body = The current status of the used disk [:DISK] on [:SYSTEM] has now reached [:percent]%.\n\nPlease contact support.
template.hiab.email.oom.subject = Memory shortage on HIAB device [:HIABIDENTIFIER]
template.hiab.email.oom.body = The memory usage on the HIAB mentioned in the subject line has exceeded it's capacity.\n\nYou may have to increase the amount of memory for optimal functionality, please contact support for additional details or any needed assistance.
iplimitnotification.subject = Targets was not added to [:SYSTEM]
iplimitnotification.body = \n\
[:SYSTEM] could not add the targets found because more were found than the account allowed to be added.\n\
The targets were found while scanning using [:DISCOVERYJOB].\n\
	
scheduled.report.subject = [:SYSTEM]: Scheduled report: [:NAME]
scheduled.report.body = \n\
Reports have been generated and attached according to the schedule named [:NAME].
scheduled.report.empty.subject = [:SYSTEM]: Scheduled report: [:NAME]
scheduled.report.empty.body = \n\
No findings found for scheduled report named [:NAME].\n\
	
scheduled.report.failed.subject = [:SYSTEM]: Scheduled report: [:NAME]
scheduled.report.failed.body = \n\
The scheduled report [:NAME] failed to generate reports.\n\
	
scheduled.report.disabled.subject = [:SYSTEM]: Scheduled report
scheduled.report.disabled.body = \n\
The scheduled report [:NAME] has been disabled as the [:TXTPARAM] has been deleted.\n\
	
scheduled.report.noscanjob.subject = [:SYSTEM]: Scheduled report
scheduled.report.noscanjob.body = \n\
No compliance scanning enabled scan is found for scan schedule selected for scheduled report [:NAME].\n\
	
email.report.subject = [:SYSTEM]: [:COMPLIANCE]Report
email.report.body = \n\
Please find attached your emailed report [:NAME].\n\
	
managed.report.upload.subject = [:SYSTEM]: New managed report(s)
managed.report.upload.body = \n\
New report(s) has(ve) been added to your Managed Services section at\n\
https://[:BASEHREF]\n\
Please log in to access and download your report(s).\n\
Should you have any questions relating to the report(s) or how to access\n\
it, please submit a support ticket at: [:MSSERVICEDESK] and we will get back to you as soon as possible.\n\

managed.report.move.subject = [:SYSTEM]: New managed report(s)
managed.report.move.body = \n\
Report(s) has(ve) been moved to [:MSGROUPNAME] group in your Managed Services section at\n\
https://[:BASEHREF]\n\
Please log in to access and download your report(s).\n\
Should you have any questions relating to the report(s) or how to access\n\
it, please submit a support ticket at: [:MSSERVICEDESK] and we will get back to you as soon as possible.\n\

virtualimage.subject = HIAB Virtual Image
virtualimage.body = \n\
It is possible to install HIAB as a VMWare Virtual Image.\n\
\n\
You will find more information and the required image to download under the Virtual HIAB Appliance tab which is located at Menu | Support in the GUI.\n\
\n\
Please log in at https://[:BASEHREF].\n\
		
event.email.35.subject = [:SYSTEM]: Host not reachable
event.email.35.body = \n\
No open TCP or UDP ports were found on host: [:REPORTHOST]\n\
This could be due to the host being down or it may simply not be listening on any ports.\n\
\n\
Verify that the target host is online and that the ports set in the scan policy includes ports known to be open.\n\
	
event.email.37.subject = [:SYSTEM]: Backup failed
event.email.37.body = \n\
[:SYSTEM] backup has failed.\n\
		
event.email.38.subject = [:SYSTEM]: Release Notes: [:VERSION]
event.email.38.body = \n\
Release notes for version [:VERSION]\n\
\n\
[:ANNOUNCEMENTS]\n\
\n\
In order to disable this email notification, please log in to your account at https://[:BASEHREF] and remove the event named "New Release Notes", which is located under "Menu", "Settings", "Event Notifications".\n\
		
event.sms.39 = SLS failed to start: [:SCHEDULEJOB]
event.email.39.subject = [:SYSTEM]: SLS information outdated
event.email.39.body = \n\
The SLS scanning for schedule [:SCANJOB] failed to initialize due to outdated information. The SLS information is only retained for 30 days and are after that not considered accurate. In order to continue SLS scanning you are required to scan the device again.\n\
\n\
Please log in at https://[:BASEHREF].\n\
		
event.email.40.subject = [:SYSTEM]: Scan Schedule Started
event.email.40.body = \n\
Scan Schedule [:SCHEDULEJOB] has started.\n\
\n\
Please log in at https://[:BASEHREF].\n\
		
event.email.41.subject = [:SYSTEM]: PCI failure
event.email.41.body = \n\
While scanning [:REPORTHOST] [:REPORTHOSTNAME] the following risks were found that did not meet PCI compliance:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.		

event.email.42.subject = [:SYSTEM]: Target authentication failure
event.email.42.body = \n\
\n\
The credentials provided has failed to authenticate against the hosts during scanning:\n\
\n\
[:FAILED]\n\
\n\
Please make sure that the given credentials are correct and that the account is still active.\n\
		
event.42.list = [:TARGET][:SSH][:SMB]
while.scanning = While scanning
while.updating = During a daily update of findings on
messagetoolarge = \n\
The attached file is too large and has been removed from the message.\n\
		
event.email.43.subject = [:SYSTEM]: Ports opened for target: [:REPORTHOST]
event.email.43.body = \n\
The following ports have been opened for target: [:REPORTHOST]\n\
[:PORTLIST]\n\
		
event.email.44.subject = [:SYSTEM]: Ports closed for target: [:REPORTHOST]
event.email.44.body = \n\
The following ports have been closed for target: [:REPORTHOST]\n\
[:PORTLIST]\n\
		
event.email.45.subject = [:SYSTEM]: Risk Accepted
event.email.45.body = \n\
Risk(s) has/have been accepted by: [:ACCEPTEDBY]\n\
\n\
Acceptance Comment: [:COMMENT]\n\
Accepted Date: [:_DATETIME_ACCEPTDATE]\n\
Accepted for number of days: [:ACCEPTEDDAYS]\n\
Accepted for new targets: [:NEWTARGETS]\n\
\n\
Targets and script ids of accepted risks are given below:\n\
\n\
[:SCRIPTIDLIST]\n\
		
event.45.list = \n\
Script ID: [:SCRIPTID]\n\
Script Name: [:SCRIPTNAME]\n\
Port: [:IPORT]\n\
Protocol: [:IPROTOCOL]\n\
Targets:
event.45.list.list = \n\
	[:REPORTHOST]
event.email.46.subject = [:SYSTEM]: New Vulnerability Comment
event.email.46.body = \n\
A new vulnerability comment has been added by: [:COMMENTNAME]\n\
Script Id: [:SCRIPTID]\n\
Script Name: [:SCRIPTNAME]\n\
Port: [:IPORT]\n\
Host: [:REPORTHOST]\n\
Comment: [:COMMENT]\n\
   		
event.email.47.subject = [:SYSTEM]: Discussion Updated
event.email.47.body = \n\
	   		[:DISCUSSIONMSG]\n\
   		
event.email.47.0 = \n\
Discussion was updated with a new post from: [:COMMENTNAME]\n\
Application: [:REPORTHOST]\n\
Finding Id: [:FINDINGID]\n\
Finding Name: [:SCRIPTNAME]\n\
Post: [:COMMENT]\n\
		
event.email.47.3 = \n\
Discussion was updated with a new post.\n\
Application: [:REPORTHOST]\n\
Finding Id: [:FINDINGID]\n\
		
event.email.47.4 = \n\
Discussion was updated with a new post from: [:COMMENTNAME]\n\
Application: [:REPORTHOST]\n\
Finding Id: [:FINDINGID]\n\
Finding Name: [:SCRIPTNAME]\n\
Discussion:\n\
[:DISCUSSION]\n\
		
event.email.49.subject = [:SYSTEM]: Risk Acceptance Expiring
event.email.49.body = \n\
Acceptance for some of the accepted risk will expire shortly. Targets and script ids of those accepted risks are given below:\n\
\n\
[:SCRIPTIDLIST]\n\
		
event.49.list = \n\
Script ID: [:SCRIPTID]\n\
Script Name: [:SCRIPTNAME]\n\
Accepted by: [:ACCEPTEDBY]\n\
Accepted Date: [:_DATETIME_ACCEPTDATE]\n\
Accepted for number of days: [:ACCEPTEDDAYS]\n\
Acceptance Expires On: [:_DATETIME_ACCEPTEXPIRES]\n\
Targets:
event.49.list.list = \n\
	[:REPORTHOST]
event.email.50.subject = [:SYSTEM]: Exploitable vulnerability
event.email.50.body = \n\
[:SCANMODE] [:REPORTHOST] [:REPORTHOSTNAME] following vulnerabilities were found with public exploits:\n\
\n\
[:scriptname]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
		
event.email.51.subject = [:SYSTEM]: Target Scan Scheduled
event.email.51.body = \n\
Following targets are scheduled to be scanned after [:DAYS] days.\n\
\n\
[:TARGETIDLIST]\n\
		
event.51.list = Target: 		[:host]\n\
Next Scan Date: [:_DATETIME_starttime]\n\
		
event.email.52.subject = [:SYSTEM]: Scan Schedule Scheduled
event.email.52.body = \n\
Following scan schedules are scheduled to be run after [:DAYS] days.\n\
\n\
[:SCANSCHEDULELIST]\n\
		
event.52.list = Scan: 		[:SCHEDULEJOB]\n\
Next Scan Date: [:_DATETIME_starttime]\n\
		
event.email.53.subject = [:SYSTEM]: Risk Acceptance Expired
event.email.53.body = \n\
Acceptance for some of the accepted risk is expired recently. Targets and script ids of those risks are given below:\n\
\n\
[:SCRIPTIDLIST]\n\
		
event.53.list = \n\
Script ID: [:SCRIPTID]\n\
Script Name: [:SCRIPTNAME]\n\
Targets:
event.53.list.list = \n\
	[:REPORTHOST]
event.email.57.subject = [:SYSTEM]: Target compliant
event.email.57.body = \n\
Target [:TARGET] was compliant for the following policies:\n\
[:POLICYLIST]\n\
		
event.email.58.subject = [:SYSTEM]: Target not compliant
event.email.58.body = \n\
Target [:TARGET] was not compliant for the following policies:\n\
[:POLICYLIST]\n\
		
dbservererror.subject = [:SYSTEM]: Database error
dbservererror.body = \n\
[:PROBLEM]

compliance.report.available.subject = [:SYSTEM]: Compliance report available for policy: [:POLICY]
compliance.report.available.body = \n\
The compliance report for policy [:POLICY] is now available.\n
event.email.100.subject=[:SYSTEM]: AppStak high risks found
event.email.100.body = \n\
New high risks were found for the following AppStaks:\n\
\n\
[:appstaks]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.101.subject = [:SYSTEM]: AppStak exploitable risks found
event.email.101.body = \n\
New exploitable risks were found for the following AppStaks:\n\
\n\
[:appstaks]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.102.subject = [:SYSTEM]: Asset high risks found
event.email.102.body = \n\
New high risks were found for the following Assets:\n\
\n\
[:assets]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.103.subject = [:SYSTEM]: Asset exploitable risks found
event.email.103.body = \n\
New exploitable risks were found for the following AppStaks:\n\
\n\
[:assets]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.104.subject = [:SYSTEM]: High risks found
event.email.104.body = \n\
[:nroffindings] new high risks were found.:\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.105.subject = [:SYSTEM]: Exploitable risks found
event.email.105.body = \n\
[:nroffindings] new exploitable risks were found.\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\
	
event.email.106.subject = [:SYSTEM]: AppStaks compliant
event.email.106.body = \n\
AppStaks were found to be compliant.\n\
\n\
[:appstaks]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\

event.email.107.subject = [:SYSTEM]: AppStaks not compliant
event.email.107.body = \n\
AppStak was found to be not compliant.\n\
\n\
[:appstaks]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\

event.email.108.subject = [:SYSTEM]: Assets compliant
event.email.108.body = \n\
Assets were found to be compliant.\n\
\n\
[:assets]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\

event.email.109.subject = [:SYSTEM]: Assets not compliant
event.email.109.body = \n\
Assets were found to be not compliant.\n\
\n\
[:assets]\n\
\n\
Log into your account at https://[:BASEHREF] to see the report.\n\

pciscandone = PCI report cannot be attached as it requires acknowledgments before being sent out.
