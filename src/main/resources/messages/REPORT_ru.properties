fixed.finding = Ð£ÑÑÑÐ°Ð½ÐµÐ½Ð½Ð°Ñ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÑ
new.finding = ÐÐ¾Ð²Ð¾Ðµ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ðµ
no.longer.present = ÐÐ¾Ð»ÑÑÐµ Ð½Ðµ Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ
nortl.field.print.disputed.comment = ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð´Ð¸ÑÐ¿ÑÑÑ
nortl.field.print.falsepositive.by = ÐÐ¾Ð¶Ð½Ð¾Ðµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ðµ 
nortl.field.print.falsepositive.comment = ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ð¾ Ð»Ð¾Ð¶Ð½Ð¾Ð¼ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ð¸ 
nortl.field.print.review.comment = ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹ Ðº Ð¾Ð±Ð·Ð¾ÑÑ
nortl.report.accept.comment = ÐÑÐ¸Ð½ÑÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¹
nortl.report.assigned.to = ÐÐ°Ð·Ð½Ð°ÑÐ¸ÑÑ
nortl.report.causing.failure = ÐÑÐ¸ÑÐ¸Ð½Ð° Ð½ÐµÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ
nortl.report.cvss.score = CVSS Ð±Ð°Ð»Ð»
nortl.report.delta.details = ÐÐµÑÐ°Ð»ÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ ÐÐµÐ»ÑÑÐ°-Ð¾ÑÑÐµÑÐ°
nortl.report.exceptions.fp.compensating.controls = ÐÑÐºÐ»ÑÑÐµÐ½Ð¸Ñ, Ð»Ð¾Ð¶Ð½ÑÐµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ñ Ð¸ ÐºÐ¾Ð¼Ð¿ÐµÐ½ÑÐ¸ÑÑÑÑÐ¸Ðµ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸, Ð¾ÑÐ¼ÐµÑÐµÐ½Ð½ÑÐµ Ð²ÐµÐ½Ð´Ð¾ÑÐ¾Ð¼ ÑÐ¾ ÑÑÐ°ÑÑÑÐ¾Ð¼ ASV Ð´Ð»Ñ ÑÑÐ¾Ð¹ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸
nortl.report.field.print.advisory = Ð¡Ð¿ÑÐ°Ð²Ð¾ÑÐ½Ð¸Ðº
nortl.report.field.print.bugtraq = Bugtraq
nortl.report.field.print.comment = ÐÐ¾ÑÑÐ½ÐµÐ½Ð¸Ðµ
nortl.report.field.print.cve = CVE
nortl.report.field.print.desc = ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ
nortl.report.field.print.family = Ð¡ÐµÐ¼ÐµÐ¹ÑÑÐ²Ð¾
nortl.report.field.print.firstseen = ÐÐµÑÐ²ÑÐ¹ ÑÐ°Ð· Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
nortl.report.field.print.general = ÐÐ±ÑÐ¸Ð¹
nortl.report.field.print.history = ÐÑÑÐ¾ÑÐ¸Ñ
nortl.report.field.print.info = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
nortl.report.field.print.lastseen = ÐÐ¾ÑÐ»ÐµÐ´Ð½Ð¸Ð¹ ÑÐ°Ð· Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
nortl.report.field.print.notavailable = ÐÐµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾
nortl.report.field.print.patch = ÐÐ°ÑÑ
nortl.report.field.print.port = ÐÐ¾ÑÑ
nortl.report.field.print.product = ÐÑÐ¾Ð´ÑÐºÑ
nortl.report.field.print.risk = Ð¤Ð°ÐºÑÐ¾Ñ ÑÐ¸ÑÐºÐ°
nortl.report.field.print.sol = Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ñ
nortl.report.field.print.target = Ð¦ÐµÐ»Ñ
nortl.report.field.print.vendor = ÐÐµÐ½Ð´Ð¾Ñ
nortl.report.field.print.verify = ÐÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐ¸ÑÑ ÑÐµÐ·ÑÐ»ÑÑÐ°Ñ
nortl.report.field.print.vhost = ÐÐ¸ÑÑÑÐ°Ð»ÑÐ½ÑÐ¹ ÑÐ¾ÑÑ
nortl.report.field.print.vulnname = ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ
nortl.report.field.print.xref = Ð¡ÑÑÐ»ÐºÐ°
nortl.report.findingcomment = ÐÐ¾Ð¸ÑÐº ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ
nortl.report.findingcommentby = ÐÐ¾Ð¸ÑÐº ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ
nortl.report.findingcommentdate = ÐÐ¾Ð¸ÑÐº Ð´Ð°ÑÑ ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ñ
nortl.report.group.details = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð³ÑÑÐ¿Ð¿Ðµ
nortl.report.info.accepted = ÐÑÐ¸Ð½ÑÑÐ¾
nortl.report.info.disputed = ÐÐ¸ÑÐ¿ÑÑ Ð¾ÑÐºÑÑÑ
nortl.report.info.markedfalsepos = ÐÐ¾Ð¼ÐµÑÐµÐ½ ÐºÐ°Ðº Ð»Ð¾Ð¶Ð½Ð¾Ðµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ðµ
nortl.report.modified.cvss.score = ÐÐ·Ð¼ÐµÐ½ÐµÐ½Ð½ÑÐ¹ CVSS Ð±Ð°Ð»Ð»
nortl.report.never = ÐÐ¸ÐºÐ¾Ð³Ð´Ð°
nortl.report.nvd.cvss.score = ÐÐ°Ð»Ð» NVD CVSS
nortl.report.pci.consolidated.solution.correction.plan = ÐÐ¾Ð½ÑÐ¾Ð»Ð¸Ð´Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð¿Ð»Ð°Ð½ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¹ Ð´Ð»Ñ Ð²ÑÑÐµÑÐºÐ°Ð·Ð°Ð½Ð½Ð¾Ð³Ð¾ IP-Ð°Ð´ÑÐµÑÐ°
nortl.report.risk.accept.date = ÐÑÐ¸Ð½ÑÑÑ Ð´Ð°ÑÑ
nortl.report.risk.accepted = Ð Ð¸ÑÐºÐ° Ð¿ÑÐ¸Ð½ÑÑ
nortl.report.risk.acceptedby = ÐÑÐ¸Ð½ÑÑÐ¾
nortl.report.risk.accept.expires = ÐÑÐ¸Ð½ÑÑÐ¸Ðµ Ð¸ÑÑÐµÐºÐ°ÐµÑ
nortl.report.severity.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ Ð¾Ð¿Ð°ÑÐ½Ð¾ÑÑÐ¸
nortl.report.vulnerabilities.noted = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸ Ð¾ÑÐ¼ÐµÑÐµÐ½Ð½ÑÐµ Ð¿Ð¾ IP-Ð°Ð´ÑÐµÑÐ°Ð¼
nortl.report.vulnerabilities.noted.for = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸, Ð¾ÑÐ¼ÐµÑÐµÐ½Ð½ÑÐµ Ð´Ð»Ñ
nortl.report.vulnerability.details = ÐÐµÑÐ°Ð»Ð¸ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸
not.assigned = ÐÐµ Ð½Ð°Ð·Ð½Ð°ÑÐµÐ½
priority = ÐÑÐ¸Ð¾ÑÐ¸ÑÐµÑ
report.accepted.high = ÐÑÐ¸Ð½ÑÑÑÐµ Ð²ÑÑÐ¾ÐºÐ¸Ðµ ÑÐ¸ÑÐºÐ¸
report.accepted.low = ÐÑÐ¸Ð½ÑÑÑÐµ Ð½Ð¸Ð·ÐºÐ¸Ðµ ÑÐ¸ÑÐºÐ¸
report.accepted.medium = ÐÑÐ¸Ð½ÑÑÑÐµ ÑÑÐµÐ´Ð½Ð¸Ðµ ÑÐ¸ÑÐºÐ¸
report.accuracy = Ð¢Ð¾ÑÐ½Ð¾ÑÑÑ
report.address = Ð¡Ð»ÑÐ¶ÐµÐ±Ð½ÑÐ¹ Ð°Ð´ÑÐµÑ
report.address.city = ÐÐ¾ÑÐ¾Ð´
report.address.country = Ð¡ÑÑÐ°Ð½Ð°
report.address.state = Ð¨ÑÐ°Ñ/Ð¿ÑÐ¾Ð²Ð¸Ð½ÑÐ¸Ñ
report.address.zip = ZIP
report.aging = Ð£ÑÑÐ°ÑÐµÐ²Ð°Ð½Ð¸Ðµ Ð¿Ð°ÑÐ¾Ð»ÐµÐ¹ (> % days)
report.aging.summary = ÐÐ±Ð·Ð¾Ñ ÑÑÑÐ°ÑÐµÐ²Ð°Ð½Ð¸Ñ Ð¿Ð°ÑÐ¾Ð»ÐµÐ¹
report.all = ÐÑÐµ
report.applications = ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
report.assignee = Ð£Ð¿Ð¾Ð»Ð½Ð¾Ð¼Ð¾ÑÐµÐ½Ð½Ð¾Ðµ Ð»Ð¸ÑÐ¾
report.asv.attestation = ASV Ð°ÑÑÐµÑÑÐ°ÑÐ¸Ñ
report.asv.attestation.text = Ð­ÑÐ¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¸ Ð¾ÑÑÐµÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð»ÐµÐ½Ñ Ð¸ Ð¿ÑÐ¾Ð²Ð¾Ð´ÑÑÑÑ #COMPANY Ð² ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ° #CERTIFICATE Ð¸ Ð²Ð½ÑÑÑÐµÐ½Ð½Ð¸Ð¼Ð¸ Ð¿ÑÐ¾ÑÐµÑÑÐ°Ð¼Ð¸, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¾ÑÐ²ÐµÑÐ°ÑÑ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÑ 11.2 PCI DSS Ð¸ Ð ÑÐºÐ¾Ð²Ð¾Ð´ÑÑÐ²Ñ PCI DSS ASV Program Guide. \n\
\n\
#COMPANY ÑÐ²Ð¸Ð´ÐµÑÐµÐ»ÑÑÑÐ²ÑÐµÑ, ÑÑÐ¾ Ð¿ÑÐ¾ÑÐµÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ PCI DSS ÐºÐ¾Ð½ÑÑÐ¾Ð»Ð¸ÑÐ¾Ð²Ð°Ð»ÑÑ, Ð² ÑÐ¾Ð¼ ÑÐ¸ÑÐ»Ðµ Ð±ÑÐ» Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½ ÑÑÑÐ½Ð¾Ð¹/Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸Ð·Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹ Ð¿ÑÐ¾ÑÐµÑÑ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ ÐºÐ°ÑÐµÑÑÐ²Ð° Ñ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°Ð½Ð¸ÐµÐ¼ Ð¿ÑÐ°ÐºÑÐ¸Ðº Ð² Ð¾Ð±Ð»Ð°ÑÑÐ¸ Ð°Ð½Ð°Ð»Ð¸Ð·Ð° ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹, Ð¿ÑÐ¾Ð²ÐµÐ´ÐµÐ½ Ð°Ð½Ð°Ð»Ð¸Ð· Ð´Ð°Ð½Ð½ÑÑ Ð¾Ð± Ð°Ð½Ð¾Ð¼Ð°Ð»Ð¸ÑÑ, Ð° ÑÐ°ÐºÐ¶Ðµ ÑÐ´ÐµÐ»Ð°Ð½Ñ Ð¾Ð±Ð·Ð¾Ñ Ð¸ ÐºÐ¾ÑÑÐµÐºÑÐ¸Ð¸ 1) ÑÐ¿Ð¾ÑÐ½ÑÑ Ð¸Ð»Ð¸ Ð½ÐµÐ¿Ð¾Ð»Ð½ÑÑ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð², 2) Ð»Ð¾Ð¶Ð½ÑÑ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ð¹, 3) Ð²Ð¼ÐµÑÐ°ÑÐµÐ»ÑÑÑÐ² Ð² ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ. Ð­ÑÐ¾Ñ Ð¾ÑÑÐµÑ Ð¸ Ð»ÑÐ±ÑÐµ Ð¸ÑÐºÐ»ÑÑÐµÐ½Ð¸Ñ Ð±ÑÐ»Ð¸ ÑÐ°ÑÑÐ¼Ð¾ÑÑÐµÐ½Ñ #COMPANY.
report.asv.company = ÐÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ñ ASV
report.asv.feedback.form = Ð¤Ð¾ÑÐ¼Ð° Ð¾Ð±ÑÐ°ÑÐ½Ð¾Ð¹ ÑÐ²ÑÐ·Ð¸ ASV
report.asv.information = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ ASV (Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð¾Ð²Ð°Ð½Ð½Ð¾Ð³Ð¾ Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸ÐºÐ° ÑÑÐ»ÑÐ³ Ð² ÑÑÐµÑÐµ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ)
report.attestation = ÐÑÑÐµÑÑÐ°ÑÐ¸Ñ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.automatic.failure = Ð­ÑÐ¾ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸Ð¼ Ð½Ð°ÑÑÑÐµÐ½Ð¸ÐµÐ¼ PCI.
report.average = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹
report.average.accepted.risk = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹ Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°ÐµÐ¼ÑÐ¹ ÑÐ¸ÑÐº
report.average.age = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹ ÑÑÐ¾Ðº ÑÐ»ÑÐ¶Ð±Ñ
report.average.cvss.score = Ð¡ÑÐµÐ´Ð½ÑÑ Ð¾ÑÐµÐ½ÐºÐ° CVSS
report.average.high.risk = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¸ÑÐºÐ¾Ð² Ð²ÑÑÐ¾ÐºÐ¾Ð³Ð¾ ÑÑÐ¾Ð²Ð½Ñ
report.average.low.risk = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¸ÑÐºÐ¾Ð² Ð½Ð¸Ð·ÐºÐ¾Ð³Ð¾ ÑÑÐ¾Ð²Ð½Ñ
report.average.medium.risk = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¸ÑÐºÐ¾Ð² ÑÑÐµÐ´Ð½ÐµÐ³Ð¾ ÑÑÐ¾Ð²Ð½Ñ
report.average.other.risk = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸
report.average.ports = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾ÑÑÐ¾Ð²
report.average.risks = Ð¡ÑÐµÐ´Ð½Ð¸Ðµ ÑÐ¸ÑÐºÐ¸
report.average.risks.target = Ð¡ÑÐµÐ´Ð½Ð¸Ðµ ÑÐ¸ÑÐºÐ¸ / ÑÐµÐ»Ñ
report.category = ÐÐ°ÑÐµÐ³Ð¾ÑÐ¸Ñ
report.company = ÐÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ñ
report.compliance.failures = Ð¡Ð»ÑÑÐ°Ð¸ Ð½ÐµÑÐ¾Ð±Ð»ÑÐ´ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.compliance.no.compliance.scan.availible = ÐÐµÐ´Ð¾ÑÑÑÐ¿Ð½Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½Ð° ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð² 
report.compliance.overview = ÐÐ±Ð·Ð¾Ñ ÑÐ¾Ð±Ð»ÑÐ´ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.compliance.policy = ÐÐ¾Ð»Ð¸ÑÐ¸ÐºÐ° ÑÐ¾Ð±Ð»ÑÐ´ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.compliance.status = Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.compliance.summary = Ð¡Ð²Ð¾Ð´Ð½ÑÐ¹ Ð¾Ð±Ð·Ð¾Ñ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ñ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.compliant = Ð¡ÐÐÐ¢ÐÐÐ¢Ð¡Ð¢ÐÐ£ÐÐ¢
report.component.compliance.summary = Ð¡Ð²Ð¾Ð´Ð½ÑÐ¹ Ð¾Ð±Ð·Ð¾Ñ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½ÑÐ¾Ð² ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.contact = ÐÐ¾Ð½ÑÐ°ÐºÑ
report.contact.title = Ð¢Ð¸ÑÑÐ»
report.core.security = ÐÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾ÑÑÑ ÑÐ´ÑÐ°
report.custom = ÐÐ°ÑÑÑÐ¾Ð¸ÑÑ
report.customer.attestation = Ð¡ÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð² Ð°ÑÑÐµÑÑÐ°ÑÐ¸Ð¸ ÐºÐ»Ð¸ÐµÐ½ÑÐ¾Ð²
report.customer.attestation.text = #COMPANY Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑ Ð½Ð° Ð´Ð°ÑÑ #DATE , ÑÑÐ¾ ÑÑÐ¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¾ÑÐ²Ð°ÑÑÐ²Ð°ÐµÑ Ð²ÑÐµ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½ÑÑ*, ÐºÐ¾ÑÐ¾ÑÑÐµ Ð¾ÑÐ½Ð¾ÑÑÑÑÑ Ðº Ð¾Ð±Ð»Ð°ÑÑÐ¸ PCI DSS; Ð»ÑÐ±Ð¾Ð¹ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½Ñ Ð²Ð½Ðµ ÑÐ°Ð¼Ð¾Ðº Ð´Ð°Ð½Ð½Ð¾Ð³Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ ÐºÐ¾ÑÑÐµÐºÑÐ½Ð¾ Ð¾ÑÐ´ÐµÐ»ÐµÐ½ Ð¾Ñ ÑÑÐµÐ´Ñ Ð´Ð°Ð½Ð½ÑÑ Ð¾ Ð´ÐµÑÐ¶Ð°ÑÐµÐ»ÑÑ ÐºÐ°ÑÑ, Ð¸ Ð»ÑÐ±Ð¾Ðµ Ð´Ð¾ÐºÐ°Ð·Ð°ÑÐµÐ»ÑÑÑÐ²Ð¾ Ð¸ÑÐºÐ»ÑÑÐµÐ½Ð¸Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ, Ð¿ÑÐµÐ´ÑÑÐ°Ð²Ð»ÐµÐ½Ð½Ð¾Ðµ Ð²ÐµÐ½Ð´Ð¾ÑÑ ÑÐ¾ ÑÑÐ°ÑÑÑÐ¾Ð¼ Approved Scanning Vendor, ÑÐ²Ð»ÑÐµÑÑÑ ÑÐ¾ÑÐ½ÑÐ¼ Ð¸ Ð¿Ð¾Ð»Ð½ÑÐ¼. \n\
\n\
Ð¢Ð°ÐºÐ¶Ðµ #COMPANY Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´Ð°ÐµÑ, ÑÑÐ¾: 1) Ð¾ÑÐ²Ð°Ñ ÑÑÐ¾Ð³Ð¾ Ð²Ð½ÐµÑÐ½ÐµÐ³Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð»ÐµÐ¶Ð¸Ñ Ð² ÑÑÐµÑÐµ ÐµÑ Ð¾ÑÐ²ÐµÑÑÑÐ²ÐµÐ½Ð½Ð¾ÑÑÐ¸, 2) ÑÐµÐ·ÑÐ»ÑÑÐ°Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÑÐµÑ Ð»Ð¸ÑÑ ÑÐ¾, ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÑÑ Ð»Ð¸ Ð¾ÑÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐ¸ÑÑÐµÐ¼Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ PCI DSS Ð¿Ð¾ Ð²Ð½ÐµÑÐ½ÐµÐ¼Ñ  ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹; ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð½Ðµ Ð¾ÑÑÐ°Ð¶Ð°ÑÑ Ð¾Ð±ÑÐµÐµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÐ¾Ð±Ð»ÑÐ´ÐµÐ½Ð¸Ñ PCI DSS Ð¸Ð»Ð¸ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ Ð´ÑÑÐ³Ð¸Ð¼ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ PCI DSS.
report.customer.declared.safe = ÐÐ»Ð¸ÐµÐ½Ñ Ð¾Ð±ÑÑÐ²Ð»ÐµÐ½ Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½ÑÐ¼
report.customer.declared.safe.info = ÐÐ°ÑÐ²Ð»ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·ÑÐ¸ÐºÐ° ÑÐ¸ÑÑÐµÐ¼Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¾ ÑÐ¾Ð¼, ÑÑÐ¾ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ðµ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ðµ ÑÐµÐ°Ð»Ð¸Ð·Ð¾Ð²Ð°Ð½Ð¾ Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾ (ÑÐ¼. ÑÐ»ÐµÐ´ÑÑÑÑÑ ÐºÐ¾Ð»Ð¾Ð½ÐºÑ, ÐµÑÐ»Ð¸ Ð½Ðµ ÑÐµÐ°Ð»Ð¸Ð·Ð¾Ð²Ð°Ð½Ð¾ Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾)
report.cvss.score = CVSS ÐÐ°Ð»Ð»
report.cvss.vector = CVSS Ð²ÐµÐºÑÐ¾Ñ
report.days = ÐÐ½Ð¸
report.delta.host.overview = ÐÐµÐ»ÑÑÐ° Ð¾Ð±Ð·Ð¾Ñ ÑÐ¾ÑÑÐ¾Ð²
report.delta.summary = Ð ÐµÐ·ÑÐ¼Ðµ ÐÐµÐ»ÑÑÐ°-Ð¾ÑÑÐµÑÐ°
report.discovered = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¾
report.discovered.scanned = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¾ Ð¸ Ð¿ÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¾
report.discovery.ports = ÐÐ¾ÑÑÑ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
report.discovery.protocols = ÐÑÐ¾ÑÐ¾ÐºÐ¾Ð»Ñ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
report.discovery.results = Ð ÐµÐ·ÑÐ»ÑÑÐ°ÑÑ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
report.discovery.scope = ÐÐ±ÑÐµÐ¼ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸
report.dsquare = DSquare Security
report.due.date = Ð¡ÑÐ¾Ðº
report.email = Email
report.end = ÐÐ°Ð²ÐµÑÑÐ¸ÑÑ
report.end.datetime = ÐÐ¾Ð½ÐµÑÐ½Ð°Ñ Ð´Ð°ÑÐ°/Ð²ÑÐµÐ¼Ñ
report.event = Ð¡Ð¾Ð±ÑÑÐ¸Ðµ
report.exclude.accepted.findings = ÐÑÐºÐ»ÑÑÐ¸ÑÑ Ð¿ÑÐ¸Ð½ÑÑÑÐµ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ
report.executive.overview = ÐÑÑÐµÑ Ð´Ð»Ñ Ð¼ÐµÐ½ÐµÐ´Ð¶Ð¼ÐµÐ½ÑÐ° â ÐÐ±Ð·Ð¾Ñ Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾ÑÑÐ¸
report.exploitability = ÐÐ¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ÑÑÑ ÑÐºÑÐ¿Ð»Ð¾Ð¹ÑÐ°
report.exploitdb = ÐÐ°Ð·Ð° Ð´Ð°Ð½Ð½ÑÑ ÑÐºÑÐ¿Ð»Ð¾Ð¹ÑÐ¾Ð²
report.external = ÐÐ½ÐµÑÐ½Ð¸Ð¹
report.failed = ÐÑÐºÐ°Ð·
report.failed.filtering.active = ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ - ÑÐ¸Ð»ÑÑÑÐ°ÑÐ¸Ñ Ð°ÐºÑÐ¸Ð²Ð½Ð°
report.failed.incomplete.scans = ÐÑÐºÐ°Ð· - ÐÐµÐ·Ð°Ð²ÐµÑÑÐµÐ½Ð½ÑÐµ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ.
report.failed.missing.targets = ÐÑÐºÐ°Ð· - Ð½Ðµ Ð²ÑÐµ ÑÐµÐ»Ð¸ Ð²ÐºÐ»ÑÑÐµÐ½Ñ
report.failed.notreadysubmission = ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ - Ð½ÐµÑ Ð¾ÑÐ¼ÐµÑÐµÐ½Ð½ÑÑ Ð³Ð¾ÑÐ¾Ð²ÑÑ Ð´Ð»Ñ Ð¿ÑÐµÐ´ÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ
report.failed.specialnote = ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ - Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¿ÑÐ¾ÐºÐ¾Ð¼Ð¼ÐµÐ½ÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð¾ÑÐ¾Ð±ÑÐµ Ð¿ÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ
report.failed.timeout.targets = ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ - ÐÑÑÐµÑ Ð¸Ð¼ÐµÐµÑ ÑÐµÐ»Ð¸ Ñ ÑÐ°Ð¹Ð¼-Ð°ÑÑÐ¾Ð¼ 
report.failures = ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ 
report.false.pos = ÐÐ¾Ð¶Ð½Ð¾Ðµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ðµ 
report.false.positive = ÐÐ¾Ð¶Ð½Ð¾Ðµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ðµ
report.field.accepted.risk = ÐÑÐ¸Ð½ÑÑÑÐ¹ ÑÐ¸ÑÐº
report.field.accepted.risks = ÐÑÐ¸Ð½ÑÑÑÐµ ÑÐ¸ÑÐºÐ¸
report.field.age = ÐÐ¾Ð·ÑÐ°ÑÑ
report.field.businessunit = ÐÐ¸Ð·Ð½ÐµÑ-ÐµÐ´Ð¸Ð½Ð¸ÑÐ°
report.field.criticalityrating = Ð ÐµÐ¹ÑÐ¸Ð½Ð³ ÐºÑÐ¸ÑÐ¸ÑÐ½Ð¾ÑÑÐ¸ Ð´Ð»Ñ Ð±Ð¸Ð·Ð½ÐµÑÐ° 
report.field.date = ÐÐ°ÑÐ°
report.field.devicetype = Ð¢Ð¸Ð¿ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
report.field.filter = Ð Ð¸ÑÐº
report.field.firstreportdate = ÐÐµÑÐ²Ð°Ñ Ð´Ð°ÑÐ° Ð¾ÑÑÐµÑÐ° 
report.field.geolocation = ÐÐµÐ¾Ð³ÑÐ°ÑÐ¸ÑÐµÑÐºÐ¾Ðµ Ð¿Ð¾Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ
report.field.host = IP
report.field.lastreportdate = ÐÐ¾ÑÐ»ÐµÐ´Ð½ÑÑ Ð´Ð°ÑÐ° Ð¾ÑÑÐµÑÐ°
report.field.netbios = NetBIOS
report.field.print.accuracy = Ð¢Ð¾ÑÐ½Ð¾ÑÑÑ
report.field.print.added = ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¾
report.field.print.ajax.enabled = Javascript (AJAX) Ð²ÐºÐ»ÑÑÐµÐ½
report.field.print.bugtraq = Bugtraq
report.field.print.closed = ÐÐ°ÐºÑÑÑ\n\

report.field.print.comment = ÐÐ¾ÑÑÐ½ÐµÐ½Ð¸Ðµ
report.field.print.cookie = ÐÑÐºÐ¸-ÑÐ°Ð¹Ð»
report.field.print.custom.authentication.credential = ÐÐ°ÑÑÑÐ¾Ð¸ÑÑ Ð´Ð°Ð½Ð½ÑÐµ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¸
report.field.print.custom.authentication.procedure = ÐÑÐ¾ÑÐµÐ´ÑÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¾Ð¹ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¸
report.field.print.cve = CVE
report.field.print.default.form.authentication = Ð¤Ð¾ÑÐ¼Ð° Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¸ Ð¿Ð¾ ÑÐ¼Ð¾Ð»ÑÐ°Ð½Ð¸Ñ
report.field.print.deltatstatus = Ð¡ÑÐ°ÑÑÑ ÐÐµÐ»ÑÑÐ°-Ð¾ÑÑÐµÑÐ°
report.field.print.desc = ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ
report.field.print.discovered.at = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¾ Ð²
report.field.print.domain = ÐÐ¾Ð¼ÐµÐ½
report.field.print.external = ÐÐ½ÐµÑÐ½Ð¸Ð¹
report.field.print.family = Ð¡ÐµÐ¼ÐµÐ¹ÑÑÐ²Ð¾
report.field.print.firstseen = ÐÐµÑÐ²ÑÐ¹ ÑÐ°Ð· Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
report.field.print.form.name = ÐÐ¼Ñ ÑÐ¾ÑÐ¼Ñ
report.field.print.general = ÐÐ±ÑÐ¸Ð¹
report.field.print.history = ÐÑÑÐ¾ÑÐ¸Ñ
report.field.print.host = IP
report.field.print.hostname = ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ ÑÐ¾ÑÑÐ°
report.field.print.hosts = IP
report.field.print.http.referer = Ð¡ÑÑÐ»ÐºÐ° Ð½Ð° HTTP
report.field.print.info = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
report.field.print.ip.scope = ÐÑÐ²Ð°Ñ IP 
report.field.print.lastseen = ÐÐ¾ÑÐ»ÐµÐ´Ð½Ð¸Ð¹ ÑÐ°Ð· Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
report.field.print.link = Ð¡ÑÑÐ»ÐºÐ°
report.field.print.login.start.page = ÐÐ¾Ð¹Ð´Ð¸ÑÐµ Ð½Ð° ÑÑÐ°ÑÑÐ¾Ð²ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
report.field.print.maximum.links = ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÐµ ÑÑÑÐ»ÐºÐ¸
report.field.print.new = ÐÐ¾Ð²ÑÐ¹
report.field.print.notavailable = ÐÐµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾
report.field.print.opened = ÐÑÐºÑÑÑÑÐ¹
report.field.print.page = Ð¡ÑÑÐ°Ð½Ð¸ÑÐ°
report.field.print.parameters = ÐÐ°ÑÐ°Ð¼ÐµÑÑÑ
report.field.print.password = ÐÐ°ÑÐ¾Ð»Ñ
report.field.print.password.field.name = ÐÐ¼Ñ Ð¿Ð¾Ð»Ñ Ð¿Ð°ÑÐ¾Ð»Ñ
report.field.print.path = ÐÐ°ÑÑÑÑÑ
report.field.print.port = ÐÐ¾ÑÑ
report.field.print.product = ÐÑÐ¾Ð´ÑÐºÑ
report.field.print.query.string = Ð¡ÑÑÐ¾ÐºÐ° Ð·Ð°Ð¿ÑÐ¾ÑÐ°
report.field.print.removed = Ð£Ð´Ð°Ð»ÐµÐ½Ð¾
report.field.print.request.delay = ÐÐ°Ð´ÐµÑÐ¶ÐºÐ° Ð·Ð°Ð¿ÑÐ¾ÑÐ° (ÑÐµÐºÑÐ½Ð´)
report.field.print.risk = Ð¤Ð°ÐºÑÐ¾Ñ ÑÐ¸ÑÐºÐ°
report.field.print.scanned = ÐÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½
report.field.print.sol = Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ñ
report.field.print.static = Ð¡ÑÐ°ÑÐ¸ÑÐ½ÑÐ¹
report.field.print.target = Ð¦ÐµÐ»Ñ
report.field.print.task = ÐÐ°Ð´Ð°ÑÐ°
report.field.print.taskassigned = ÐÐ°Ð´Ð°ÑÐ° Ð¿Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð°
report.field.print.tasknew = ÐÐ¾Ð²Ð¾Ðµ Ð·Ð°Ð´Ð°Ð½Ð¸Ðµ
report.field.print.taskreopened = ÐÐ°Ð´Ð°ÑÐ° Ð·Ð°Ð½Ð¾Ð²Ð¾ Ð¾ÑÐºÑÑÑÐ°
report.field.print.taskresolved = ÐÐ°Ð´Ð°ÑÐ° ÑÐµÑÐµÐ½Ð°
report.field.print.taskverified = ÐÐ°Ð´Ð°ÑÐ° Ð¿ÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð°
report.field.print.total.ip = ÐÐ±ÑÐµÐµ ÐºÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ IP
report.field.print.transfer.timeout = ÐÑÐµÐ¼Ñ ÑÑÐ°Ð½ÑÑÐµÑÐ° Ð¸ÑÑÐµÐºÐ»Ð¾ (seconds)
report.field.print.trigger.pattern = ÐÐ±ÑÐ°Ð·ÐµÑ ÑÑÐ¸Ð³Ð³ÐµÑÐ°
report.field.print.unchanged = ÐÐµÐ¸Ð·Ð¼ÐµÐ½ÐµÐ½
report.field.print.uri = URI
report.field.print.uri.blacklist = Ð§ÐµÑÐ½ÑÐ¹ ÑÐ¿Ð¸ÑÐ¾Ðº URI
report.field.print.uri.list = Ð¡Ð¿Ð¸ÑÐ¾Ðº URI
report.field.print.uri.whitelist = ÐÐµÐ»ÑÐ¹ ÑÐ¿Ð¸ÑÐ¾Ðº URI 
report.field.print.user.agent = ÐÐ³ÐµÐ½Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
report.field.print.username = ÐÐ¾Ð³Ð¸Ð½
report.field.print.username.field.name = ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð¿Ð¾Ð»Ñ Ð¸Ð¼ÐµÐ½Ð¸ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
report.field.print.value = ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ
report.field.print.verify = ÐÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐ¸ÑÑ ÑÐµÐ·ÑÐ»ÑÑÐ°Ñ
report.field.print.vhost = ÐÐ¸ÑÑÑÐ°Ð»ÑÐ½ÑÐ¹ ÑÐ¾ÑÑ
report.field.print.vulnname = ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ
report.field.print.xref = Ð¡ÑÑÐ»ÐºÐ°
report.field.remediation.trend = Ð¢ÑÐµÐ½Ð´ ÑÑÑÑÐ°Ð½ÐµÐ½Ð¸Ñ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.field.remediation.trend.text = Ð¡ÑÐµÐ´Ð½ÐµÐµ Ð²ÑÐµÐ¼Ñ ÑÐµÑÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð±Ð»ÐµÐ¼ Ð´Ð»Ñ ÐºÐ°Ð¶Ð´Ð¾Ð³Ð¾ ÑÑÐ¾Ð²Ð½Ñ ÑÐ¸ÑÐºÐ¾Ð² Ð¼ÐµÐ¶Ð´Ñ % Ð¸ %
report.field.sensitivityrating = Ð ÐµÐ¹ÑÐ¸Ð½Ð³ ÑÑÐ²ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½ÑÑ Ð´Ð°Ð½Ð½ÑÑ 
report.field.solutions = Ð ÐµÐºÐ¾Ð¼Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¸
report.filtering = Ð¡Ð¾Ð¾Ð±ÑÐ¸ÑÐµ Ð¾ ÑÐ¸Ð»ÑÑÑÐ°ÑÐ¸Ð¸
report.filtering.active = ÐÑÑÐµÑ Ð¾ÑÑÐ°Ð¶Ð°ÐµÑ Ð°ÐºÑÐ¸Ð²Ð½ÑÑ ÑÐ¸Ð»ÑÑÑÐ°ÑÐ¸Ñ, ÑÑÐ¾ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ Ðº Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð½ÐµÐ¿Ð¾Ð»Ð½ÑÑ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ð¹.
report.filtering.active.partial.result = ÐÑÑÐµÑ Ð¾ÑÑÐ°Ð¶Ð°ÐµÑ Ð°ÐºÑÐ¸Ð²Ð½ÑÑ ÑÐ¸Ð»ÑÑÑÐ°ÑÐ¸Ñ, ÑÑÐ¾ Ð¼Ð¾Ð¶ÐµÑ Ð¿ÑÐ¸Ð²ÐµÑÑÐ¸ Ðº Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð½ÐµÐ¿Ð¾Ð»Ð½ÑÑ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ð¹.
report.finding = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ðµ
report.findings = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ
report.findings.added = ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½Ð½ÑÐµ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ
report.findings.removed = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ ÑÐ´Ð°Ð»ÐµÐ½Ñ
report.findings.sorted.by = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ Ð¾ÑÑÐ¾ÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ñ 
report.findings.unchanged = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ñ Ð½Ðµ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ñ
report.found.alive = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½ Ð² Ð°ÐºÑÐ¸Ð²Ð½Ð¾Ð¼ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸
report.generated.by = ÐÑÑÐµÑ, ÑÐ³ÐµÐ½ÐµÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¹
report.graph.acceptedoverview = ÐÐ±Ð·Ð¾Ñ Ð¿ÑÐ¸Ð½ÑÑÐ¾Ð³Ð¾ ÑÐ¸ÑÐºÐ°\n\

report.graph.hostnameoverview = ÐÐ±Ð·Ð¾Ñ Ð¸Ð¼ÐµÐ½ ÑÐ¾ÑÑÐ¾Ð² 
report.graph.riskoverview = ÐÐ±Ð·Ð¾Ñ ÑÑÐ¾Ð²Ð½Ñ ÑÐ¸ÑÐºÐ°
report.group.delta.summary = ÐÑÑÐ¿Ð¿Ð¾Ð²ÑÐµ Ð´ÐµÐ»ÑÑÐ° ÑÐ²Ð¾Ð´Ð½ÑÐµ Ð¾ÑÑÐµÑÑ
report.group.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ Ð³ÑÑÐ¿Ð¿Ñ
report.group.name = ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð³ÑÑÐ¿Ð¿Ñ
report.groups = ÐÑÑÐ¿Ð¿Ñ
report.group.summary = Ð ÐµÐ·ÑÐ¼Ðµ Ð¿Ð¾ Ð³ÑÑÐ¿Ð¿Ðµ
report.group.trend.details = ÐÐ¾Ð´ÑÐ¾Ð±Ð½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ Ð¾ ÑÐµÐ½Ð´ÐµÐ½ÑÐ¸ÑÑ Ð³ÑÑÐ¿Ð¿Ñ 
report.group.trend.summary = ÐÐ±Ð·Ð¾Ñ ÑÐµÐ½Ð´ÐµÐ½ÑÐ¸Ð¹ Ð³ÑÑÐ¿Ð¿Ñ
report.header.aging = Ð£ÑÑÐ°ÑÐµÐ²Ð°Ð½Ð¸Ðµ Ð¿Ð°ÑÐ¾Ð»ÐµÐ¹
report.header.compliant = Ð¡Ð¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ
report.header.delta = ÐÐµÐ»ÑÑÐ°
report.header.delta.discovery.text = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð°ÐºÑÐ¸Ð²Ð½ÑÑ ÑÐµÐ»ÐµÐ¹
report.header.deltaports = Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¿Ð¾ÑÑÐ¾Ð² ÐÐµÐ»ÑÑÐ°-Ð¾ÑÑÐµÑÐ°
report.header.delta.ports.text = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¾ÑÐºÑÑÑÑÑ Ð¸ Ð·Ð°ÐºÑÑÑÑÑ Ð¿Ð¾ÑÑÐ¾Ð² Ñ Ð¼Ð¾Ð¼ÐµÐ½ÑÐ° Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÐµÐ³Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.header.delta.text = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ð¹, Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð½ÑÑ Ð¸ ÑÐ´Ð°Ð»ÐµÐ½Ð½ÑÑ ÑÐ¾ Ð²ÑÐµÐ¼ÐµÐ½Ð¸ Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÐµÐ³Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.header.delta.wasdiscovery.text = Ð§Ð¸ÑÐ»Ð¾ Ð½Ð¾Ð²ÑÑ Ð°Ð´ÑÐµÑÐ¾Ð² URI Ñ Ð¼Ð¾Ð¼ÐµÐ½ÑÐ° Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÐµÐ³Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.header.header = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± Ð¾ÑÑÐµÑÐµ
report.header.notcompliant = ÐÐµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸ÑÐ¼ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.header.openports = Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¾ÑÐºÑÑÑÑÑ Ð¿Ð¾ÑÑÐ¾Ð²
report.header.open.ports = ÐÑÐºÑÑÑÑÐµ Ð¿Ð¾ÑÑÑ
report.header.remediation.age = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸ ÑÐ¾ ÑÑÐ¾ÐºÐ¾Ð¼ Ð¸ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¸Ñ Ð±Ð¾Ð»ÐµÐµ % Ð´Ð½ÐµÐ¹
report.header.risk.high = ÐÑÑÐ¾ÐºÐ¸Ð¹ ÑÐ¸ÑÐº
report.header.risk.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ ÑÐ¸ÑÐºÐ°
report.header.risk.low = ÐÐ¸Ð·ÐºÐ¸Ð¹ ÑÐ¸ÑÐº
report.header.risk.medium = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹ ÑÑÐ¾Ð²ÐµÐ½Ñ ÑÐ¸ÑÐºÐ°
report.header.risk.other = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
report.header.trend = Ð¢ÑÐµÐ½Ð´
report.header.trend.text = Ð¡ÑÐµÐ´Ð½ÐµÐµ ÑÐ¸ÑÐ»Ð¾ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ð¹ Ð´Ð»Ñ ÐºÐ°Ð¶Ð´Ð¾Ð³Ð¾ ÑÑÐ¾Ð²Ð½Ñ ÑÐ¸ÑÐºÐ¾Ð² Ð¼ÐµÐ¶Ð´Ñ % Ð¸ %
report.high = ÐÑÑÐ¾ÐºÐ¸Ð¹
report.high.risks = ÐÑÑÐ¾ÐºÐ¸Ðµ ÑÐ¸ÑÐºÐ¸
report.high.severity = ÐÑÑÐ¾ÐºÐ°Ñ ÑÑÐµÐ¿ÐµÐ½Ñ ÑÐµÑÑÐµÐ·Ð½Ð¾ÑÑÐ¸
report.host = Ð¥Ð¾ÑÑ
report.host.info = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ¾ÑÑÐµ
report.host.names = ÐÐ¼ÐµÐ½Ð° ÑÐ¾ÑÑÐ¾Ð²
report.host.not.reachable = Ð¥Ð¾ÑÑ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½
report.ignore.availability = ÐÐ³Ð½Ð¾ÑÐ¸ÑÑÐ¹ÑÐµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð¾ÑÑÑ
report.immunity = ÐÐ¼Ð¼ÑÐ½Ð¸ÑÐµÑ
report.incomplete.scan = ÐÐµÐ·Ð°Ð²ÐµÑÑÐµÐ½Ð½Ð¾Ðµ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ
report.info = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± Ð¾ÑÑÐµÑÐµ
report.info.hostrisk = Ð£ÑÐ¾Ð²ÐµÐ½Ñ ÑÐ¸ÑÐºÐ° IP
report.info.numberhostsscanned = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿ÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ IP
report.info.pcistatus = PCI ÑÑÐ°ÑÑÑ
report.ip.address = IP Ð°Ð´ÑÐµÑ
report.item.noted = ÐÑÐ¼ÐµÑÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ½ÐºÑ
report.item.noted.info = ÐÑÐ¼ÐµÑÐµÐ½Ð½ÑÐ¹ Ð¿ÑÐ½ÐºÑ (Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ðµ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ðµ ÑÐ´Ð°Ð»ÐµÐ½Ð½Ð¾Ð³Ð¾ Ð´Ð¾ÑÑÑÐ¿Ð°, Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ðµ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ðµ POS  Ð¸ Ñ.Ð´.)
report.key = ÐÐ»ÑÑ
report.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ
report.low = ÐÐ¸Ð·ÐºÐ¸Ð¹
report.low.risks = ÐÐ¸Ð·ÐºÐ¸Ðµ ÑÐ¸ÑÐºÐ¸
report.low.severity = ÐÐ¸Ð·ÐºÐ°Ñ ÑÑÐµÐ¿ÐµÐ½Ñ ÑÑÐ¶ÐµÑÑÐ¸
report.mac.address = MAC Ð°Ð´ÑÐµÑ
report.max.cvss.score = ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð¾ÑÐµÐ½ÐºÐ° CVSS
report.may.be.false = Ð­ÑÐ¾ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð»Ð¾Ð¶Ð½Ð¾Ðµ ÑÑÐ°Ð±Ð°ÑÑÐ²Ð°Ð½Ð¸Ðµ
report.medium = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹
report.medium.risks = Ð¡ÑÐµÐ´Ð½Ð¸Ðµ ÑÐ¸ÑÐºÐ¸
report.medium.severity = Ð¡ÑÐµÐ´Ð½ÑÑ ÑÑÐµÐ¿ÐµÐ½Ñ ÑÐµÑÑÐµÐ·Ð½Ð¾ÑÑÐ¸
report.metasploit = ÐÐµÑÐ°ÑÐ¿Ð»Ð¾Ð¹Ñ
report.minimum.password.aging = ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾Ðµ ÑÑÑÐ°ÑÐµÐ²Ð°Ð½Ð¸Ðµ Ð¿Ð°ÑÐ¾Ð»Ñ
report.minimum.password.history = ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð¸ÑÑÐ¾ÑÐ¸Ñ Ð¿Ð°ÑÐ¾Ð»Ñ 
report.minimum.password.length = ÐÐ¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð´Ð»Ð¸Ð½Ð° Ð¿Ð°ÑÐ¾Ð»Ñ 
report.modification.list = Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¼Ð¾Ð´Ð¸ÑÐ¸ÐºÐ°ÑÐ¸Ð¹
report.modifications.field.disabled = ÐÑÐºÐ»ÑÑÐµÐ½Ð½ÑÐµ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.modifications.field.families = ÐÑÐºÐ»ÑÑÐµÐ½Ð½ÑÐµ ÑÐµÐ¼ÐµÐ¹ÑÑÐ²Ð°
report.modifications.field.settings = ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸
report.modified.cvss.score = ÐÐ·Ð¼ÐµÐ½ÐµÐ½Ð½ÑÐ¹ CVSS Ð±Ð°Ð»Ð»
report.month = ÐÐµÑÑÑ
report.monthly = ÐÐ¶ÐµÐ¼ÐµÑÑÑÐ½Ð¾
report.months = Ð¼ÐµÑÑÑÐµÐ²
report.name = ÐÐ°Ð¸Ð¼ÐµÐ½Ð¾Ð²Ð°Ð½Ð¸Ðµ
report.nbr.components.out.of.scope = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½ÑÐ¾Ð², Ð½Ð°Ð¹Ð´ÐµÐ½Ð½ÑÑ ASV, Ð½Ð¾ Ð½Ðµ Ð¾ÑÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ Ð¸Ð·-Ð·Ð° ÑÐ¾Ð³Ð¾, ÑÑÐ¾  Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð½ÑÐµ Ð·Ð°ÐºÐ°Ð·ÑÐ¸ÐºÐ¾Ð¼ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½ÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð±ÑÐ»Ð¸ Ð²Ð½Ðµ Ð¾Ð±Ð»Ð°ÑÑÐ¸ Ð¾ÑÐ²Ð°ÑÐ°
report.nbr.components.scanned = Ð§Ð¸ÑÐ»Ð¾ ÑÐ½Ð¸ÐºÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ ÐºÐ¾Ð¼Ð¿Ð¾Ð½ÐµÐ½ÑÐ¾Ð²
report.nbr.identified.failing.vulnerabilities = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð²ÑÑÐ²Ð»ÐµÐ½Ð½ÑÑ Ð»Ð¾Ð¶Ð½ÑÑ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.nbr.targets = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐµÐ»ÐµÐ¹
report.nbr.targets.scanned = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿ÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ ÑÐµÐ»ÐµÐ¹
report.nbr.uris.found = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð½Ð°Ð¹Ð´ÐµÐ½Ð½ÑÑ URI
report.nbr.uris.scanned = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿ÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÑ  URI
report.newfixed.finding = ÐÐ¾Ð²Ð¾Ðµ/ÑÑÑÑÐ°Ð½ÐµÐ½Ð½Ð¾Ðµ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ðµ
report.no = ÐÐµÑ
report.none = ÐÐ¸ Ð¾Ð´Ð¸Ð½
report.normal = ÐÐ¾ÑÐ¼Ð°Ð»ÑÐ½ÑÐ¹
report.not.compliant = ÐÐ Ð¡ÐÐÐ¢ÐÐÐ¢Ð¡Ð¢ÐÐ£ÐÐ¢
report.note = Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ
report.not.found = ÐÐµ Ð½Ð°Ð¹Ð´ÐµÐ½
report.not.responding = ÐÐµ Ð¾ÑÐ²ÐµÑÐ°ÐµÑ
report.not.verified = ÐÐµ Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð¾
report.number.affected.hosts = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð·Ð°ÑÑÐ¾Ð½ÑÑÑÑ ÑÐ¾ÑÑÐ¾Ð²
report.nvd.override.comment = ÐÑÐ¸ÑÐ¸Ð½Ð° ÐºÐ¾ÑÑÐµÐºÑÐ¸Ð¸ ÑÐ¸ÑÑÐµÐ¼Ñ CVSS
report.open.issues = ÐÑÐºÑÑÑÑÐµ Ð²Ð¾Ð¿ÑÐ¾ÑÑ
report.option.filter.high = ÐÑÑÐ¾ÐºÐ¸Ð¹ ÑÐ¸ÑÐº
report.option.filter.low = ÐÐ¸Ð·ÐºÐ¸Ð¹ ÑÐ¸ÑÐº
report.option.filter.medium = Ð¡ÑÐµÐ´Ð½Ð¸Ð¹ ÑÑÐ¾Ð²ÐµÐ½Ñ ÑÐ¸ÑÐºÐ°
report.option.filter.other = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
report.option.type.compliance = Ð¡Ð¾Ð±Ð»ÑÐ´ÐµÐ½Ð¸Ðµ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.option.type.delta = ÐÐµÐ»ÑÑÐ° Ð¾ÑÑÐµÑ
report.option.type.discovery = ÐÐ½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ñ
report.option.type.groupdelta = ÐÑÑÐ¿Ð¿Ð¾Ð²ÑÐµ Ð´ÐµÐ»ÑÑÐ°-Ð¾ÑÑÐµÑÑ
report.option.type.grouptrending = Ð¢ÐµÐ½Ð´ÐµÐ½ÑÐ¸Ð¸ Ð³ÑÑÐ¿Ð¿Ñ
report.option.type.group.vulnerability = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÑ Ð³ÑÑÐ¿Ð¿Ñ 
report.option.type.pci = PCI
report.option.type.solution.detailed = ÐÐ¾Ð´ÑÐ¾Ð±Ð½ÑÐ¹ Ð¾ÑÑÐµÑ ÑÐµÑÐµÐ½Ð¸Ñ
report.option.type.solution.overview = ÐÐ±Ð·Ð¾Ñ Ð¾ÑÑÐµÑÐ° ÑÐµÑÐµÐ½Ð¸Ñ
report.option.type.solution.target = ÐÑÑÐµÑ Ð¿Ð¾ ÑÐµÐ»ÑÐ¼ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.option.type.solution.task = ÐÑÑÐµÑ Ð¿Ð¾ÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ Ð·Ð°Ð´Ð°Ñ
report.option.type.summary = ÐÐ±Ð·Ð¾Ñ Ð³ÑÑÐ¿Ð¿Ñ ÑÐµÐ»ÐµÐ¹
report.option.type.target.discovery = ÐÐ½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ñ ÑÐµÐ»ÐµÐ¹
report.option.type.topvulns = ÐÑÐ½Ð¾Ð²Ð½ÑÐµ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸
report.option.type.trend = Ð¢ÑÐµÐ½Ð´ 
report.option.type.vulnerability = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÑ
report.option.type.web.app.discovery = ÐÐ½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ñ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹
report.overall.compliance = ÐÐ¾Ð»Ð½Ð¾Ðµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ðµ
report.overall.status = ÐÐ±ÑÐ¸Ð¹ ÑÑÐ°ÑÑÑ
report.passed = Ð¡Ð¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ
report.patch.installed = ÐÐ°ÑÑ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½
report.patch.levels = Ð£ÑÐ¾Ð²Ð½Ð¸ Ð¿Ð°ÑÑÐ°
report.period = ÐÐ½ÑÐµÑÐ²Ð°Ð»
report.phone = Ð¢ÐµÐ»ÐµÑÐ¾Ð½
report.platform = ÐÐ»Ð°ÑÑÐ¾ÑÐ¼Ð°
report.policy = Ð ÐµÐ¶Ð¸Ð¼ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.ports = ÐÐ¾ÑÑÑ
report.ports.closed = ÐÐ¾ÑÑÑ Ð·Ð°ÐºÑÑÑÑ
report.ports.opened = ÐÐ¾ÑÑÑ Ð¾ÑÐºÑÑÑÑ
report.ports.unchanged = ÐÐ¾ÑÑÑ Ð±ÐµÐ· Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ
report.potential.false = ÐÐ¾ÑÐµÐ½ÑÐ¸Ð°Ð»ÑÐ½Ð¾ Ð½ÐµÐ²ÐµÑÐ½ÑÐ¹
report.previously.added = ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½Ð½ÑÐ¹ ÑÐ°Ð½ÐµÐµ
report.print.report.modifications.field.disabled = ÐÑÐºÐ»ÑÑÐµÐ½Ð½ÑÐµ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.print.report.modifications.field.enabled = ÐÐºÐ»ÑÑÐµÐ½Ð½ÑÐµ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.print.report.modifications.field.families = ÐÑÐºÐ»ÑÑÐµÐ½Ð½ÑÐµ ÑÐµÐ¼ÐµÐ¹ÑÑÐ²Ð°
report.print.report.modifications.field.settings = ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸
report.protocol = ÐÑÐ¾ÑÐ¾ÐºÐ¾Ð»
report.protocols = ÐÑÐ¾ÑÐ¾ÐºÐ¾Ð»Ñ
report.public.exploit.available = Ð¡ÑÑÐµÑÑÐ²ÑÐµÑ Ð¿ÑÐ±Ð»Ð¸ÑÐ½ÑÐ¹ ÑÐºÑÐ¿Ð»Ð¾Ð¹Ñ
report.quarter = ÐÐ²Ð°ÑÑÐ°Ð»
report.quarterly = ÐÐ¶ÐµÐºÐ²Ð°ÑÑÐ°Ð»ÑÐ½Ð¾
report.registry.keys = ÐÐ»ÑÑÐ¸ ÑÐµÐµÑÑÑÐ°
report.report = ÐÑÑÐµÑ
report.report.count = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¾ÑÑÐµÑÐ¾Ð²
report.report.date = ÐÐ°ÑÐ° Ð¾ÑÑÐµÑÐ°
report.reports = ÐÑÑÐµÑÑ
report.report.template = Ð¨Ð°Ð±Ð»Ð¾Ð½ Ð¾ÑÑÑÑÐ°
report.responding = ÐÑÐ²ÐµÑÐ°ÑÑÐ¸Ð¹\n\
\n\

report.responding.added = ÐÑÐ²ÐµÑÐ°ÑÑÐ¸Ð¹ Ð¸ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð½ÑÐ¹
report.risk.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ ÑÐ¸ÑÐºÐ°
report.risks.added = ÐÐ¾Ð±Ð°Ð²Ð»ÐµÐ½Ð½ÑÐµ ÑÐ¸ÑÐºÐ¸
report.risks.removed = Ð Ð¸ÑÐºÐ¸ ÑÐ´Ð°Ð»ÐµÐ½Ñ
report.scan.completed = ÐÐ°ÑÐ° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¸Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ\n\

report.scan.customer.company = Ð¡ÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ ÐºÐ»Ð¸ÐµÐ½ÑÑÐºÐ¾Ð¹ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ð¸
report.scan.customer.information = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð·Ð°ÐºÐ°Ð·ÑÐ¸ÐºÐ° ÑÐ¸ÑÑÐµÐ¼Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ.
report.scan.expiration = ÐÐºÐ¾Ð½ÑÐ°Ð½Ð¸Ðµ ÑÑÐ¾ÐºÐ° Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð´Ð°ÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ 
report.scan.information = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ð¸ 
report.scanner = Ð¡ÐºÐ°Ð½ÐµÑ
report.scanninginterval = ÐÐ½ÑÐµÑÐ²Ð°Ð» ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.scanresultsupdated = Ð ÐµÐ·ÑÐ»ÑÑÐ°ÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ñ
report.scan.status = Ð¡ÑÐ°ÑÑÑ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.scan.tracking.list = Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¸ÑÑÐ¾ÑÐ¸Ð¸ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.scheduled = Ð Ð°ÑÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ
report.scope = ÐÐ±ÑÐµÐ¼ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.service = Ð¡ÐµÑÐ²Ð¸Ñ
report.sn.description = ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ ÐºÐ»Ð¸ÐµÐ½ÑÐ°
report.sn.description.info = ÐÐ¿Ð¸ÑÐ°Ð½Ð¸Ðµ Ð·Ð°ÐºÐ°Ð·ÑÐ¸ÐºÐ¾Ð¼ ÑÐ¸ÑÑÐµÐ¼Ñ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¼ÐµÑ, Ð¿ÑÐµÐ´Ð¿ÑÐ¸Ð½ÑÑÑÑ ÑÑÐ¾Ð±Ñ: 1) ÑÐ´Ð°Ð»Ð¸ÑÑ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ðµ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ðµ Ð¸Ð»Ð¸ 2) Ð¾ÑÑÑÐµÑÑÐ²Ð»ÑÑÑ ÐºÐ¾Ð½ÑÑÐ¾Ð»Ñ Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾ÑÑÐ¸ ÐÐ
report.solution.0 = ÐÐµ ÐºÐ»Ð°ÑÑÐ¸ÑÐ¸ÑÐ¸ÑÑÐµÑÑÑ
report.solution.1 = ÐÐµÐ¸Ð·Ð²ÐµÑÑÐ½ÑÐ¹
report.solution.10 = ÐÐ°ÑÑÑÐ¾Ð¹ÑÐµ Ð°ÐºÐºÐ°ÑÐ½Ñ
report.solution.11 = ÐÑÐºÐ»ÑÑÐ¸ÑÐµ
report.solution.12 = Ð¤Ð¸Ð»ÑÑÑ
report.solution.13 = ÐÑÐµÐ´Ð¾Ð½Ð¾ÑÐ½Ð¾Ðµ ÐÐ
report.solution.2 = ÐÐµÑÐµÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ¸ÑÑÐ¹ÑÐµ
report.solution.3 = ÐÐ±ÑÐ¾Ð´Ð½Ð¾Ðµ ÑÐµÑÐµÐ½Ð¸Ðµ
report.solution.4 = Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¸ Ð¿Ð¾ ÑÑÑÑÐ°Ð½ÐµÐ½Ð¸Ñ Ð² Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐµ
report.solution.5 = Ð¡Ð²ÑÐ·Ð°ÑÑÑÑ Ñ Ð²ÐµÐ½Ð´Ð¾ÑÐ¾Ð¼
report.solution.6 = ÐÐ±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ
report.solution.7 = ÐÐ°ÑÑ
report.solution.8 = ÐÐµ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐ¶Ð´ÐµÐ½Ð¾
report.solution.9 = Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¸ Ð½Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ 
report.solution.summary = ÐÐ±Ð·Ð¾Ñ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¹
report.solution.targets = ÐÐ°Ð´Ð°ÑÐ¸ Ð¿Ð¾ ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸ÑÐ¼ Ð¿Ð¾ ÑÐµÐ»ÑÐ¼
report.solution.tasks = ÐÐ°Ð´Ð°ÑÐ¸ Ð¿Ð¾ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸ÑÐ¼
report.start = ÐÐ°ÑÐ°Ð»Ð¾
report.start.datetime = ÐÐ°ÑÐ°/Ð²ÑÐµÐ¼Ñ Ð½Ð°ÑÐ°Ð»Ð° ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.status = Ð¡ÑÐ°ÑÑÑ
report.summary = Ð ÐµÐ·ÑÐ¼Ðµ
report.target.activity.overview = ÐÐ±Ð·Ð¾Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¹ Ð¿Ð¾ ÑÐµÐ»Ð¸
report.target.discovery.details = ÐÐµÑÐ°Ð»Ð¸ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ ÑÐµÐ»Ð¸
report.target.discovery.summary = ÐÐ±Ð·Ð¾Ñ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ ÑÐµÐ»Ð¸
report.target.group.level = Ð£ÑÐ¾Ð²ÐµÐ½Ñ Ð³ÑÑÐ¿Ð¿Ñ ÑÐµÐ»ÐµÐ¹
report.target.group.overview = ÐÐ±Ð·Ð¾Ñ Ð³ÑÑÐ¿Ð¿Ñ ÑÐµÐ»ÐµÐ¹
report.target.group.summary = ÐÐ±Ð·Ð¾Ñ Ð³ÑÑÐ¿Ð¿Ñ ÑÐµÐ»ÐµÐ¹
report.targets = Ð¦ÐµÐ»Ð¸
report.targets.found = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð½ÑÐµ ÑÐµÐ»Ð¸
report.targets.found.alive = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð½ÑÐµ ÑÐ°Ð±Ð¾ÑÐ¸Ðµ ÑÐµÐ»Ð¸
report.targets.found.alive.added = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð½ÑÐµ ÑÐ°Ð±Ð¾ÑÐ¸Ðµ ÑÐµÐ»Ð¸ Ð±ÑÐ»Ð¸ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ñ
report.targets.not.found = Ð¦ÐµÐ»Ð¸ Ð½Ðµ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ñ
report.targets.not.responding = Ð¦ÐµÐ»Ð¸ Ð½Ðµ Ð¾ÑÐ²ÐµÑÐ°ÑÑ
report.targets.scanned = ÐÑÐ¾ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ ÑÐµÐ»Ð¸\n\

report.target.summary = ÐÐ±Ð·Ð¾Ñ ÑÐµÐ»Ð¸
report.template = Ð ÐµÐ¶Ð¸Ð¼ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.tickets = ÐÐ°Ð´Ð°ÑÐ¸
report.title.compliance.status = Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ñ ÑÑÐµÐ±Ð¾Ð²Ð°Ð½Ð¸Ð¹ Ð·Ð°ÐºÐ¾Ð½Ð¾Ð´Ð°ÑÐµÐ»ÑÑÑÐ²Ð° Ð¸ ÑÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ¾Ð²
report.to = Ð´Ð¾
report.top.risk.families = Ð¢Ð¾Ð¿ 10 ÑÐ¸ÑÐºÐ¾Ð² Ð¿Ð¾ ÑÐµÐ¼ÐµÐ¹ÑÑÐ²Ð°Ð¼ Ð¿ÑÐ¾Ð´ÑÐºÑÐ¾Ð² 
report.top.risk.families.text = ÐÐ±Ð·Ð¾Ñ ÑÐ¾Ð¿-10 ÑÐµÐ¼ÐµÐ¹ÑÑÐ² ÑÐ¸ÑÐºÐ°
report.top.solutions = Ð ÐµÐ·ÑÐ¼Ðµ Ð¾ÑÐ½Ð¾Ð²Ð½ÑÑ ÑÐµÑÐµÐ½Ð¸Ð¹
report.top.ten.findings = Ð¢Ð¾Ð¿ 10 Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ð¹
report.top.ten.solutions = Ð¢Ð¾Ð¿ 10 Ð ÐµÐºÐ¾Ð¼ÐµÐ½Ð´Ð°ÑÐ¸Ð¹
report.top.vulnerabilities = Ð¢Ð¾Ð¿ 10 ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.tracking = ÐÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð½Ð¸Ðµ
report.tracking.list = Ð¡Ð¿Ð¸ÑÐ¾Ðº Ð¸ÑÑÐ¾ÑÐ¸Ð¸ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
report.trackings = ÐÑÑÐ»ÐµÐ¶Ð¸Ð²Ð°Ð½Ð¸Ñ
report.trend.average.summary = Ð¡ÑÐ¼Ð¼Ð°ÑÐ½ÑÐ¹ ÑÑÐµÐ´Ð½Ð¸Ð¹ ÑÑÐµÐ½Ð´
report.trend.details = ÐÐµÑÐ°Ð»Ð¸ ÑÑÐµÐ½Ð´Ð°
report.trend.summary = Ð ÐµÐ·ÑÐ¼Ðµ ÑÑÐµÐ½Ð´Ð°
report.trigger = Ð¢ÑÐ¸Ð³Ð³ÐµÑ (Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¾Ñ)
report.type = ÐÐ¸Ð´
report.type.info = ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
report.type.port = ÐÐ¾ÑÑ
report.unknown = ÐÐµÐ¸Ð·Ð²ÐµÑÑÐ½ÑÐ¹
report.uri.discovered = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½ URI 
report.uri.discovered.external = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½ Ð²Ð½ÐµÑÐ½Ð¸Ð¹ URI 
report.uri.discovered.internal = ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½ Ð²Ð½ÑÑÑÐµÐ½Ð½Ð¸Ð¹ URI 
report.uri.overview = ÐÐ±Ð·Ð¾Ñ URI 
report.uris = URI
report.uri.scanned = ÐÑÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ URI
report.uris.scanned = ÐÑÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ URI
report.url = URL
report.verified = ÐÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐµÐ½
report.version = ÐÐµÑÑÐ¸Ñ
report.vhosts.discovered = ÐÐ¸ÑÑÑÐ°Ð»ÑÐ½ÑÐµ ÑÐ¾ÑÑÑ Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½Ñ
report.virtual.hosts = ÐÐ¸ÑÑÑÐ°Ð»ÑÐ½ÑÐµ ÑÐ¾ÑÑÑ
report.vulnerabilities = Ð£ÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÑ
report.vulnerability.details = ÐÐµÑÐ°Ð»Ð¸ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐ¸
report.was = WAS
report.webapp.discovery.details = ÐÐµÑÐ°Ð»ÑÐ½Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹
report.webapp.discovery.summary = ÐÐ±Ð·Ð¾Ñ Ð¸Ð½Ð²ÐµÐ½ÑÐ°ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹
report.web.application = ÐÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ
report.web.application.details = ÐÐµÑÐ°Ð»ÑÐ½ÑÐµ Ð´Ð°Ð½Ð½ÑÐµ Ð¾ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¸
report.web.application.scope = ÐÑÐ²Ð°Ñ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹
report.web.application.summary = ÐÐ±Ð·Ð¾Ñ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹
report.week = ÐÐµÐ´ÐµÐ»Ñ
report.weekly = ÐÐ¶ÐµÐ½ÐµÐ´ÐµÐ»ÑÐ½Ð¾
report.windows.password.policy = ÐÐ¾Ð»Ð¸ÑÐ¸ÐºÐ° Ð¿Ð°ÑÐ¾Ð»ÐµÐ¹ Windows
report.windows.services = Ð¡Ð»ÑÐ¶Ð±Ñ Windows
report.xml.core = ÐÐ²Ð¸Ð¶Ð¾Ðº ÑÐºÐ°Ð½ÐµÑÐ°
report.xml.framework = ÐÐµÐ½ÐµÑÐ°ÑÐ¾Ñ Ð¾ÑÑÐµÑÐ¾Ð²
report.xml.hardened = ÐÐ¾Ð²ÑÑÐµÐ½Ð° Ð±ÐµÐ·Ð¾Ð¿Ð°ÑÐ½Ð¾ÑÑÑ HIAB 
report.xml.rules = ÐÐ°Ð·Ð° Ð´Ð°Ð½Ð½ÑÑ Ð¿ÑÐ°Ð²Ð¸Ð»
report.xml.scripts = ÐÐ°Ð·Ð° Ð´Ð°Ð½Ð½ÑÑ ÑÑÐ·Ð²Ð¸Ð¼Ð¾ÑÑÐµÐ¹
report.xml.was = ÐÐ²Ð¸Ð¶Ð¾Ðº ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð²ÐµÐ±-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¹ 
report.yearly = ÐÐ¾Ð´Ð¾Ð²Ð¾Ð¹
report.yes = ÐÐ°
special.notes = ÐÑÐ¾Ð±ÑÐµ Ð¿ÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ
special.notes.for = ÐÑÐ¾Ð±ÑÐµ Ð¿ÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ Ðº
still.present = ÐÑÑ Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ
task.id = ID ÐÐ°Ð´Ð°ÑÐ¸ 
xreport.field.accepted.risks = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿ÑÐ¸Ð½Ð¸Ð¼Ð°ÐµÐ¼ÑÑ ÑÐ¸ÑÐºÐ¾Ð²
xreport.field.date = ÐÐ½ÑÐµÑÐ²Ð°Ð» Ð¾ÑÑÐµÑÐ½Ð¾ÑÑÐ¸
xreport.field.filter = Ð¤Ð¸Ð»ÑÑÑ
xreport.field.host = IP
xreport.field.reportcreator = ÐÑÑÐµÑ Ð±ÑÐ» ÑÐ¾Ð·Ð´Ð°Ð½
xreport.field.reportdate = ÐÐ°ÑÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ Ð¾ÑÑÐµÑÐ°
xreport.field.reportid = ID Ð¾ÑÑÐµÑÐ°
xreport.field.scandate = Ð¡ÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
xreport.field.schedulejob = ÐÐ¿ÑÐµÐ´ÐµÐ»Ð¸ÑÑ Ð·Ð°Ð´Ð°ÑÑ
xreport.field.test = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ð¹
xreport.field.threat = ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð½Ð°Ð¹Ð´ÐµÐ½Ð½ÑÑ ÑÐ¸ÑÐºÐ¾Ð²
xreport.field.timezone = Ð§Ð°ÑÐ¾Ð²Ð¾Ð¹ Ð¿Ð¾ÑÑ Ð´Ð»Ñ Ð´Ð°Ñ
xreport.field.type = ÐÐ¸Ð´ Ð¾ÑÑÐµÑÐ°
xreport.info.pcicompliant = Ð­ÑÐ¾Ñ Ð¾ÑÑÐµÑ Ð±ÑÐ» Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð»ÐµÐ½ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸ÐµÐ¹ Ð¸Ð¼ÐµÑÑÐµÐ¹ ÑÑÐ°ÑÑÑ PCI Approved Scanning Vendor, #COMPANY, Ð¾Ð±Ð»Ð°Ð´Ð°ÑÑÐµÐ¹ ÑÐµÑÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ð¼ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ #CERTIFICATE Ð² ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ð¸ Ñ Ð¿ÑÐ°Ð²Ð¸Ð»Ð°Ð¼Ð¸ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ñ Ð·Ð°ÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ PCI.
