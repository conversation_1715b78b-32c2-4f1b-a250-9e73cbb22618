server.port = -1

spring.datasource.url = *****************************************
spring.datasource.username = opsuser
spring.datasource.password = opsuser
spring.datasource.driverClassName = org.postgresql.Driver

spring.jpa.hibernate.ddl-auto = none
spring.jpa.show-sql = false
spring.jpa.format-sql = true
spring.jpa.hibernate.naming.physical-strategy = org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect

service.name = o24schedulingservice

management.server.port = 8081
management.server.ssl.enabled = false
management.endpoint.health.probes.enabled = true
management.endpoint.health.show-components = always
management.health.livenessState.enabled = true
management.health.readinessState.enabled = true
management.endpoint.health.group.readiness.include = readinessState,kafkaHealth
management.endpoint.health.group.liveness.include = livenessState,kafkaHealth
security.basic.enabled = false
