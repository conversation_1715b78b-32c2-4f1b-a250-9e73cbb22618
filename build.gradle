buildscript {
	repositories {
		mavenLocal()
		mavenCentral()
		flatDir dirs: './config'
		maven { url 'https://plugins.gradle.org/m2/' }
	}
	dependencies {
		classpath 'org.springframework.boot:spring-boot-gradle-plugin:2.7.18'
		classpath 'com.github.spotbugs.snom:spotbugs-gradle-plugin:6.0.25'
		classpath 'com.bmuschko:gradle-clover-plugin:3.0.7'
	}
}

plugins {
	id 'java'
	id 'maven-publish'
	id 'java-test-fixtures'
}

ext {
	// Get the project name
	def getProjectName = { Project project ->
		if (project.hasProperty('project.name')) {
			return project.getProperty('project.name')
		}
		return project.name
	}
	projectName = getProjectName(project)
	println("[** PROJECT NAME **]: " + projectName)

	// Get the project artifact version
	def getArtifactVersion = { Project project ->
		if (project.hasProperty('artifact.version')) {
			return project.getProperty('artifact.version')
		}
		else {    // Get the version from git
			try {
				def stdout = new ByteArrayOutputStream()
				project.exec {
					commandLine 'git', 'describe', '--tags', '--always', '--dirty'
					standardOutput = stdout
				}
				return stdout.toString().trim().substring(1) // substring(1) to remove the "v" from tags like 'v1.0.0...'
			} catch (Exception ignored) {
				return '0.0.0'
				// This happens in builds where no git binary is available, like in 'make lint' where docker build is used.
			}
		}
	}
	artifactVersion = getArtifactVersion(project)
	println("[** ARTIFACT VERSION **]: " + artifactVersion)

	// The git branch is passed in as a variable from the pipeline when building with docker otherwise it is fetched from git.
	def getGitBranch = { Project project ->
		if (project.hasProperty('git.branch')) {
			println("[** project.hasProperty('git.branch') **]: " + project.hasProperty('git.branch'))
			return project.getProperty('git.branch')
		}
		else {
			try {
				def stdout = new ByteArrayOutputStream()
				project.exec {
					commandLine 'git', 'rev-parse', '--abbrev-ref', 'HEAD'
					standardOutput = stdout
				}
				return stdout.toString().trim()
			} catch (Exception ignored) {
				return 'UNKNOWN_BRANCH'
			}
		}
	}
	gitBranch = getGitBranch(project)
	println("[** BUILDING ON BRANCH **]: " + gitBranch)

	def checkIfDockerContainerBuild = { Project project ->
		return (project.hasProperty("artifactory.url"))
	}
	isDockerContainerBuild = checkIfDockerContainerBuild(project)
	println("[** isDockerContainerBuild **]: " + isDockerContainerBuild)
}


if (hasProperty('artifactory.url') && hasProperty('artifact.version')) {
	publishing {
		repositories {
			maven {
				name = 'o24artifactory'
				url = property('artifactory.url')
				credentials {
					username System.getenv('ARTIFACTORY_USERNAME')
					password System.getenv('ARTIFACTORY_PASSWORD')
				}
			}
		}
		publications {
			maven(MavenPublication) {
				groupId 'com.outpost24'
				artifactId 'reportservice'
				version property('artifact.version')

				from components.java
			}
		}
	}
}
else {
	publishing {
		publications {
			maven(MavenPublication) {
				groupId = 'com.outpost24'
				artifactId = 'reportservice'
				version = artifactVersion

				from components.java
			}
		}
	}
}

if (project.file("../core/general.gradle").exists()) {
	apply from: '../core/general.gradle'
}

def currentJavaVersion = JavaVersion.current()
println("[** currentJavaVersion **]: " + currentJavaVersion)

tasks.withType(JavaCompile).configureEach {
	options.encoding = 'UTF-8'

	if (currentJavaVersion.isJava9Compatible()) {
		options.compilerArgs += [
				'--add-opens=java.base/java.net=ALL-UNNAMED',
				'--add-opens=java.base/java.time=ALL-UNNAMED',
				'--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED'
		]
	}
}

if (currentJavaVersion.isJava9Compatible()) {
	println("[** targetCompatibility **]: Java 17")
	sourceCompatibility = 17
	targetCompatibility = 17
}
else {
	println("[** targetCompatibility **]: Java 1.8]")
	sourceCompatibility = 1.8
	targetCompatibility = 1.8
}

compileTestJava.options.encoding = 'UTF-8'
test {
	systemProperty 'file.encoding', 'utf-8'
	maxHeapSize = '1G'
	maxParallelForks = 1

	// We use Java 8 for the rpm builds and 17 for the container builds.
	// Java 8 predates the Java Platform Module System (JPMS), which was introduced in Java 9.
	// Therefore, Java 8 code does not have any module-related encapsulation issues.
	// The --add-opens argument is specifically designed to work around JPMS encapsulation restrictions.
	// It allows code to access internal APIs that are normally hidden within modules.
	if (currentJavaVersion.isJava9Compatible()) {
		println("[** Adding --add-opens for JPMS encapsulation restrictions... **]")
		jvmArgs += [
				'--add-opens=java.base/java.net=ALL-UNNAMED',
				'--add-opens=java.base/java.time=ALL-UNNAMED',
				'--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED'
		]
	}
	println("[** JVM Args **]: ${jvmArgs}")

	testLogging {
		showStandardStreams true
		events 'started', 'passed'
	}

	reports {
		junitXml.required.set(true)
		html.required.set(true)
	}
}

if (isDockerContainerBuild) {
	apply plugin: 'jacoco'
	test.finalizedBy jacocoTestCoverageVerification, jacocoTestReport

	jacocoTestReport {
		reports {
			xml.required = true
			html.required = true
		}
	}
	jacocoTestCoverageVerification {
		dependsOn jacocoTestReport
		violationRules {
			rule {
				limit {
					minimum = 0.50
				}
			}
		}
	}
}
else if (gitBranch == "master") {
	apply plugin: 'com.bmuschko.clover'
	test.finalizedBy cloverGenerateReport

	dependencies {
		println("[** USING CLOVER dependency **]")
		clover 'org.openclover:clover:4.5.2'
	}

	clover {
		targetPercentage = '50%'
		report {
			html = true
			xml = true
		}
		additionalSourceSet {
			srcDirs = sourceSets.main.allJava.srcDirs
			classesDir = sourceSets.main.java.destinationDirectory.getAsFile().get()
		}
		additionalTestSourceSet {
			srcDirs = sourceSets.test.allJava.srcDirs
			classesDir = sourceSets.test.java.destinationDirectory.getAsFile().get()
		}
	}
}

apply plugin: 'com.github.spotbugs'
spotbugs {
	maxHeapSize = '2g'
	toolVersion = '4.8.6'
}
tasks.matching {
	task -> task.name.startsWith('spotbugs')
}.forEach {
	it.reports {
		html.enabled = true
		xml.enabled = true
	}
}

apply plugin: 'checkstyle'
checkstyle {
	toolVersion = "9.3"
	maxWarnings = 0
}
tasks.withType(Checkstyle).configureEach {
	configFile = file("config/checkstyle/checkstyle.xml")
	reports {
		xml.required = true
		html.required = true
	}
}

apply plugin: 'org.springframework.boot'
springBoot {
	buildInfo {
		properties {
			additional = [
					"version": System.properties["version"] != null ? System.properties["version"] : "unspecified"
			]
		}
	}
}

//For Eclipse IDE only
apply plugin: 'eclipse-wtp'
eclipse.classpath.file.beforeMerged {
	classpath -> classpath.entries.clear()
}
eclipse.classpath {
	file.whenMerged { classpath ->
		classpath.entries.findAll { entry -> entry instanceof org.gradle.plugins.ide.eclipse.model.ProjectDependency && entry.entryAttributes.test }
				.each { it.entryAttributes['test'] = 'false' }
	}
}

def o24ArtifactoryUrl = "https://artifactory.internal.outpost24.com:443/artifactory/"
def branchRepositorySuffix = "_gradle/"
def branchRepositoryUrl = "${o24ArtifactoryUrl}${gitBranch}${branchRepositorySuffix}"
repositories {
	mavenLocal()
	maven { url 'https://artifactory.internal.outpost24.com/artifactory/gradle_mirror' }
	maven { url "https://artifactory.internal.outpost24.com:443/artifactory/maven-argowf/" }
	maven { url 'https://artifactory.internal.outpost24.com/artifactory/master_gradle' }
	maven { url "https://artifactory.internal.outpost24.com:443/artifactory/tags_gradle/" }
	maven { url "https://build.shibboleth.net/nexus/content/repositories/releases" }
	maven { url "https://repository.jboss.org/nexus/content/repositories/thirdparty-releases/" }
	maven { url "https://splunk.jfrog.io/artifactory/ext-releases-local" }

	if (gitBranch != "master") {
		maven { url branchRepositoryUrl }
		println("[** Adding maven repository for branch **]: " + branchRepositoryUrl)
	}

	if (project.hasProperty("artifactory.url")) {
		maven {
			url project.getProperty("artifactory.url")
			println("[** Adding maven repository for Artifactory URL **]: " + project.getProperty("artifactory.url"))
		}
	}
	mavenCentral()
}

javadoc {
	options.overview = "overview.html"
}

jar {
	archiveBaseName = 'o24reportexport'
	duplicatesStrategy = DuplicatesStrategy.EXCLUDE
	archiveClassifier = ''
}

bootJar {
	archiveFileName = "o24reportservice.jar"
}

tasks.register('copyJar', Copy) {
	dependsOn jar
	from 'build/libs/'
	include '*.jar'
	into 'build/libs/'
	rename('.jar$', '-original.jar')
}

bootJar.dependsOn(copyJar)
compileTestJava.dependsOn('copyJar', 'bootJar')
testFixturesJar.dependsOn(copyJar)

configurations.configureEach {
	exclude group: "ch.qos.logback", module: "logback-classic"

	resolutionStrategy.eachDependency { DependencyResolveDetails details ->
		if (details.requested.group == 'org.apache.logging.log4j') {
			details.useVersion '2.23.1'
		}
	}
	resolutionStrategy {
		force 'org.bouncycastle:bcprov-jdk15on:1.70'
		force 'org.bouncycastle:bcprov-ext-jdk15on:1.70'
		force 'org.apache.tomcat:tomcat-catalina:9.0.96'
		force 'org.apache.tomcat:tomcat-servlet-api:9.0.96'
		force 'org.apache.tomcat:tomcat-coyote:9.0.96'
		force 'org.apache.tomcat:tomcat-embed-core:9.0.96'
		force 'org.apache.tomcat:tomcat-embed-el:9.0.96'
		force 'org.apache.tomcat:juli:9.0.96'
		force 'org.apache.tomcat:tomcat-embed-websocket:9.0.96'
		force 'org.postgresql:postgresql:42.7.4'
		force 'com.google.code.findbugs:jsr305:3.0.2'
		force 'com.google.guava:guava:33.4.8-jre'
		force 'org.reflections:reflections:0.9.12'
		force 'net.sf.saxon:Saxon-HE:10.3'
		force 'org.json:json:20240303'
		force 'commons-beanutils:commons-beanutils:1.9.4'
		force 'org.yaml:snakeyaml:2.3'
		force 'commons-codec:commons-codec:1.17.1'
		force 'org.apache.commons:commons-text:1.12.0'
		force 'com.fasterxml.woodstox:woodstox-core:7.1.0'
		force 'org.springframework:spring-expression:5.3.39'

		dependencySubstitution {
			substitute module('org.json:json') using module('org.json:json:20240303')
			substitute module('org.postgresql:postgresql') using module('org.postgresql:postgresql:42.7.4')
			substitute module('org.glassfish.jersey.core:jersey-client') using module('org.glassfish.jersey.core:jersey-client:2.45')
			substitute module('org.glassfish.jersey.core:jersey-common') using module('org.glassfish.jersey.core:jersey-common:2.45')
			substitute module('org.assertj:assertj-core') using module('org.assertj:assertj-core:3.26.3')
			substitute module('commons-beanutils:commons-beanutils') using module('commons-beanutils:commons-beanutils:1.9.4')
			substitute module('org.yaml:snakeyaml') using module('org.yaml:snakeyaml:2.3')
			substitute module('commons-codec:commons-codec') using module('commons-codec:commons-codec:1.17.1')
			substitute module('org.apache.httpcomponents:httpclient') using module('org.apache.httpcomponents:httpclient:4.5.13')
			substitute module('org.apache.httpcomponents:httpcore') using module('org.apache.httpcomponents:httpcore:4.4.16')
			substitute module('org.bouncycastle:bcprov-jdk18on') using module('org.bouncycastle:bcprov-jdk15on:1.70')
			substitute module('com.google.code.findbugs:annotations') using module('com.github.spotbugs:spotbugs-annotations:4.8.6')
		}
	}
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-undertow:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-actuator:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-security:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-mail:2.7.18'
	implementation 'org.springframework.boot:spring-boot-starter-log4j2:2.7.18'
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor:2.7.18'
	implementation 'org.springframework.kafka:spring-kafka:2.9.13'
	modules {
		module("org.springframework.boot:spring-boot-starter-logging") {
			replacedBy("org.springframework.boot:spring-boot-starter-log4j2", "Use Log4j2 instead of Logback")
		}
	}
	implementation 'org.slf4j:slf4j-nop:2.0.16'
	implementation group: 'org.apache.tomcat', name: 'tomcat-juli', version: '9.0.96'
	implementation 'org.springdoc:springdoc-openapi-ui:1.8.0'
	implementation 'org.eclipse.persistence:javax.persistence:2.1.1'
	implementation 'org.eclipse.persistence:org.eclipse.persistence.core:2.7.15'
	implementation 'org.eclipse.persistence:org.eclipse.persistence.moxy:2.7.15'
	implementation 'org.springframework.security:spring-security-core:5.8.15'
	implementation 'com.auth0:java-jwt:4.4.0'
	implementation 'org.postgresql:postgresql:42.7.4'
	implementation 'ch.mfrey.jackson:jackson-antpathfilter:1.0.2'
	implementation 'io.undertow:undertow-core:2.2.16.Final'
	implementation 'com.splunk:splunk:*******'
	implementation 'commons-io:commons-io:2.17.0'
	implementation 'org.projectlombok:lombok:1.18.34'
	implementation 'javax.validation:validation-api:2.0.1.Final'
	annotationProcessor 'org.projectlombok:lombok:1.18.34'
	implementation 'org.apache.poi:poi-ooxml:5.4.1'
	implementation 'org.apache.commons:commons-lang3:3.17.0'
	implementation 'org.jfree:jfreechart:1.5.5'
	implementation 'com.keypoint:png-encoder:1.5'
	implementation 'com.github.librepdf:openpdf:1.3.33'
	implementation 'com.github.librepdf:openpdf-fonts-extra:1.3.33'
	implementation 'org.json:json:20240303'
	implementation 'net.lingala.zip4j:zip4j:2.11.5'
	implementation 'javax.mail:mail:1.4.6'
	implementation 'org.bouncycastle:bcpg-jdk15on:1.70'
	implementation 'org.bouncycastle:bcpkix-jdk15on:1.70'

	implementation 'org.apache.logging.log4j:log4j-api:2.23.1'
	implementation 'org.apache.logging.log4j:log4j-core:2.23.1'
	implementation 'org.apache.logging.log4j:log4j-layout-template-json:2.23.1'

	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.18.0'
	implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.18.0'
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.18.0'
	implementation 'de.bwaldvogel:log4j-systemd-journal-appender:2.5.1'
	implementation 'org.pegdown:pegdown:1.6.0'
	implementation 'org.glassfish.jersey.core:jersey-client:2.45'
	implementation 'org.glassfish.jersey.inject:jersey-hk2:2.45'
	implementation 'io.swagger.core.v3:swagger-jaxrs2:2.2.25'
	implementation 'com.googlecode.java-ipv6:java-ipv6:0.16'
	implementation 'software.amazon.awssdk:s3:2.29.31'
	implementation 'com.vladsch.flexmark:flexmark:0.62.2'
	implementation 'com.vladmihalcea:hibernate-types-5:2.21.1'
	implementation 'io.dropwizard.metrics:metrics-graphite:4.2.28'
	implementation 'co.elastic.apm:apm-agent-attach:1.52.0'
	implementation 'co.elastic.apm:apm-agent-api:1.52.0'
	implementation 'co.elastic.apm:elastic-apm-agent:1.52.0'
	implementation 'com.google.guava:guava:33.4.8-jre'
	implementation 'org.glassfish:javax.json:1.1.4'
	implementation 'javax.json:javax.json-api:1.1.4'
	implementation 'org.apache.kafka:kafka-clients:3.2.3'
	implementation 'software.amazon.msk:aws-msk-iam-auth:2.2.0'
	implementation 'org.codelibs:jcifs:2.1.34'
	implementation 'com.github.spotbugs:spotbugs-annotations:4.8.6'
	implementation 'com.outpost24:o24jinja2renderer:0.0.4-v0.0.4'

	if (project.hasProperty('devbuild')) {
		implementation project(':core')
		testImplementation(testFixtures(project(':core')))
	}
	else if (isDockerContainerBuild) {
		implementation 'com.outpost24:core:+'
		testImplementation 'com.outpost24:core:+:test-fixtures'
	}
	else {
		implementation fileTree(dir: '/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/', include: ['o24core.jar'])
		testImplementation(testFixtures(fileTree(dir: '/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/', include: 'o24core-test-fixtures.jar')))
	}

	testAnnotationProcessor 'org.projectlombok:lombok:1.18.34'
	testImplementation('org.springframework.boot:spring-boot-starter-test:2.7.18') {
		exclude group: 'com.vaadin.external.google', module: 'android-json'
	}
	testImplementation 'org.springframework.boot:spring-boot-test:2.7.18'
	testImplementation 'org.springframework:spring-test:5.3.39'
	testImplementation 'org.awaitility:awaitility:4.2.2'
	testImplementation 'junit:junit:4.13.2'
	testImplementation 'com.github.spotbugs:spotbugs:4.8.2'
	testImplementation 'org.flywaydb:flyway-core:9.11.0'
	testImplementation 'org.assertj:assertj-core:3.26.3'
	testImplementation 'org.springframework.kafka:spring-kafka-test:2.9.13'
	testImplementation 'io.findify:s3mock_2.13:0.2.6'
}

// This will log the o24 dependencies versions that were used for the build - makes it easier to check in the log file
tasks.register('printDependencyVersionsUsed') {
	def targetGroup = 'com.outpost24'
	def targetNames = ['dblayer', 'core', 'ruleengine', 'scandataimportservice', 'eventservice']

	doLast {
		configurations.runtimeClasspath.resolvedConfiguration.lenientConfiguration.allModuleDependencies.each { dependency ->
			if (dependency.moduleGroup == targetGroup) { // Check group once
				targetNames.each { targetName ->
					if (dependency.moduleName == targetName) {
						logger.lifecycle("[** o24_VERSION_USED **] -> ${dependency.moduleGroup}:${dependency.moduleName}:${dependency.moduleVersion}")
					}
				}
			}
		}
	}
}

tasks.register('downloadDependencies') {
	doLast {
		configurations.configureEach {
			if (it.isCanBeResolved()) {
				it.resolve()
			}
		}
	}
}

tasks.withType(GenerateModuleMetadata).configureEach {
	enabled = false
}

configurations {
	customConfig.extendsFrom configurations.default
}

tasks.register('copyRuntimeLibs', Copy) {
	into "dependencies"
	from configurations.customConfig
}

processResources {
	println 'project.name: ' + projectName
	println 'project.version: ' + artifactVersion

	inputs.property 'appName', projectName
	inputs.property 'appVersion', artifactVersion

	filesMatching('**/applicationInfo.properties') {
		expand 'appName': projectName, 'appVersion': artifactVersion
	}
}
