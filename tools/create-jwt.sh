#!/bin/bash

expiresInSeconds=3600
userId=11
subUserId=null
consultancy=false
secret=secret
#keyFile=/var/updater-cache/client_key.pem

while getopts "u:s:ce:p:k:oh" opt; do
	case "$opt" in
		u)
			userId="$OPTARG"
			if [[ ! "$userId" =~ "^[0-9]+$" ]]; then
				echo "Value must be a number."
				exit 1
			fi
			;;
		s)
			subUserId="$OPTARG"
			if [[ ! "$subUserId" =~ "^[0-9]+$" ]]; then
				echo "Value must be a number."
				exit 1
			fi
			;;
		c) consultancy=true ;;
		e)
			expiresInSeconds="$OPTARG"
			if [[ ! "$expiresInSeconds" =~ "^[0-9]+$" ]]; then
				echo "Value must be a number."
				exit 1
			fi
			;;
		p) secret="$OPTARG" ;;
		k)
			keyFile="$OPTARG"
			if [ ! -f "$keyFile" ]; then
				echo "Key file does not exist."
				exit 1
			fi
			;;
		o) showOutput=true ;;
		h)
			echo "Valid arguments are:"
			echo "-u    user ID to create token for"
			echo "-s    subuser ID to create token for"
			echo "-c    create a consultancy token"
			echo "-e    in how many seconds the token should expire"
			echo "-p    a secret to use for signing with HS256"
			echo "-k    a PEM private key file to use for signing with RS256"
			echo "-o    show the decoded output and expiration date"
			echo "-h    show this help text"
			exit 1
			;;
		\?) exit 1 ;;
		:) exit 1 ;;
	esac
done

payload=$(printf '%s' "{\"iss\":\"Outpost24\",\"userId\":$userId,\"subUserId\":$subUserId,\"consultancy\":$consultancy,\"exp\":$(expr $(date +%s) + $expiresInSeconds)}" | base64 -w0 | sed -Ee 's/\+/-/g' -e 's/\//_/g' -e 's/=+$//g')
if [ -z "$keyFile" ]; then
	header=$(printf '%s' '{"typ":"JWT","alg":"HS256"}' | base64 -w0 | sed -Ee 's/\+/-/g' -e 's/\//_/g' -e 's/=+$//g')
	signature=$(printf '%s' "$header.$payload" | openssl dgst -binary -sha256 -hmac "$secret" | base64 -w0 | sed -Ee 's/\+/-/g' -e 's/\//_/g' -e 's/=+$//g')
else
	header=$(printf '%s' '{"typ":"JWT","alg":"RS256"}' | base64 -w0 | sed -Ee 's/\+/-/' -e 's/\//_/' -e 's/=+$//')
	signature=$(printf '%s' "$header.$payload" | openssl dgst -sha256 -sign "$keyFile" | base64 -w0 | sed -Ee 's/\+/-/g' -e 's/\//_/g' -e 's/=+$//g')
fi

token="$header.$payload.$signature"
echo "$token"

if [ -n "$showOutput" ]; then
	echo
	echo "$token" | cut -d '.' -f1 | base64 --decode 2>/dev/null | jq .
	echo "$token" | cut -d '.' -f2 | base64 --decode 2>/dev/null | jq .
	echo
	date -d "@$(echo "$token" | cut -d '.' -f2 | base64 --decode 2>/dev/null | jq .exp)"
fi
