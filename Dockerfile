FROM artifactory.internal.outpost24.com/docker/opibase:0.9.8 AS build
ARG GIT_BRANCH="master"
ARG ARTIFACT_VERSION=""
ARG PROJECT_NAME="xmlapi"
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com:443/artifactory"

WORKDIR /app
RUN mkdir -p /app/test-outcome
COPY src ./src
COPY config ./config
COPY gradle ./gradle
COPY build.gradle gradlew log4j-junit.properties ./

# Cache Gradle dependencies and avoid re-downloading
RUN ./gradlew dependencies --no-daemon --build-cache

RUN KUBERNETES_ENABLED=1 KUBERNETES_SERVICE_HOST=TEMPORARY \
    ./gradlew build jar copyRuntimeLibs printDependencyVersionsUsed -x test -x check --no-daemon --build-cache --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pproject.name="${PROJECT_NAME}" -Pgit.branch="${GIT_BRANCH}";

FROM build AS lint
ARG GIT_BRANCH=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"
ARG LINT_FAILED_FILENAME="lint_failed"

RUN ./gradlew checkstyleMain checkstyleTest spotbugsMain spotbugsTest --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}" \
    || touch /app/test-outcome/${LINT_FAILED_FILENAME};

FROM scratch AS lintresults
COPY --from=lint /app/test-outcome/* /
COPY --from=lint /app/build/reports/checkstyle/* /checkstyle/
COPY --from=lint /app/build/reports/spotbugs/* /spotbugs/

FROM build AS test
ARG GIT_BRANCH=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"
ARG TEST_FAILED_FILENAME="test_failed"
# Because there are tests that have the port number hard coded we force it 5432 - will need to be fixed in the future
# If this environment variable is not set, a random port will be used
ENV PSQL_PORT=5432

# Start the test database, get the port, update the test properties file with the port, run the tests, stop the test database
RUN /usr/bin/test-database.sh start && \
	port=$(cat /tmp/test-database-port) && \
	sed -i "s/127.0.0.1:[0-9]\+/127.0.0.1:${port}/" ./src/test/resources/application-test.properties && \
    KUBERNETES_ENABLED=1 KUBERNETES_SERVICE_HOST=TEMPORARY \
	./gradlew test --no-daemon --no-build-cache --info --stacktrace \
	   -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}" || touch /app/test-outcome/${TEST_FAILED_FILENAME} && \
	/usr/bin/test-database.sh stop

FROM scratch AS testresults
COPY --from=test /app/test-outcome/* /
COPY --from=test /app/build/test-results/test/*.xml /unit-tests/
COPY --from=test /app/build/reports/jacoco/test/*.xml /coverage/
COPY --from=test /app/build/reports/jacoco/test/html/* /coverage/html

FROM build AS publishjar
ARG GIT_BRANCH=""
ARG ARTIFACTORY_USERNAME=""
ARG ARTIFACTORY_PASSWORD=""
ARG ARTIFACT_VERSION=""
ARG PROJECT_NAME="xmlapi"
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"

RUN ./gradlew publish --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pproject.name="${PROJECT_NAME}" -Pgit.branch="${GIT_BRANCH}";

FROM artifactory.internal.outpost24.com/docker-mirror/tomcat:9.0.105-jre17 AS publish
ARG GIT_BRANCH=""
ARG GIT_URL=""
ARG GIT_COMMIT=""
ARG ARTIFACT_VERSION=""
ARG PROJECT_NAME="xmlapi"
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"

LABEL git-url=$GIT_URL
LABEL git-branch=$GIT_BRANCH
LABEL git-commit=$GIT_COMMIT
LABEL git-tag=$GIT_TAG
LABEL artifact-version=$ARTIFACT_VERSION
LABEL org.opencontainers.image.authors="outpost24.com"

RUN apt update && apt install libpcap0.8 -y && apt-get clean
COPY --from=build /app/build/libs/o24xml.jar /usr/local/tomcat/webapps/ROOT/WEB-INF/lib/o24xml.jar
COPY web/META-INF/ /usr/local/tomcat/webapps/ROOT/META-INF/
COPY web/WEB-INF/ /usr/local/tomcat/webapps/ROOT/WEB-INF/
COPY web/server.xml /usr/local/tomcat/conf/server.xml
COPY web/context.xml /usr/local/tomcat/conf/context.xml
COPY web/redisson.conf /usr/local/tomcat/redisson.conf
RUN mkdir /usr/local/tomcat/webapps/ROOT/META-INF/guides/
COPY guides /usr/local/tomcat/webapps/ROOT/META-INF/guides/
COPY --from=build /app/dependencies/* /usr/local/tomcat/webapps/ROOT/WEB-INF/lib/
COPY --from=build /app/dependencies/redisson* /app/dependencies/netty* /app/dependencies/cache-api* \
/app/dependencies/reactor-core* /app/dependencies/reactive-streams* /app/dependencies/rxjava* \
/app/dependencies/kryo* /app/dependencies/objenesis* /app/dependencies/minlog* \
/app/dependencies/byte-buddy* /app/dependencies/jodd-util* /app/dependencies/slf4j-api* \
/app/dependencies/jackson-annotations* /app/dependencies/jackson-dataformat-yaml* \
/app/dependencies/jackson-core* /app/dependencies/jackson-databind* /app/dependencies/snakeyaml* \
/usr/local/tomcat/lib/
RUN ls -l /usr/local/tomcat/lib

RUN echo -n "export CATALINA_OPTS=\"--add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED -javaagent:" > $CATALINA_HOME/bin/setenv.sh && \
    echo -n $(find /usr/local/tomcat/webapps/ROOT/WEB-INF/lib/ -name elastic-apm-agent-*.jar) >> $CATALINA_HOME/bin/setenv.sh && \
    sed -i 's/ALL/WARNING/g' $CATALINA_HOME/conf/logging.properties && \
    sed -i 's/INFO/WARNING/g' $CATALINA_HOME/conf/logging.properties && \
    echo " -Delastic.apm.service_name=\${ELASTIC_APM_SERVICE_NAME:-vm-api} -Delastic.apm.server_urls=\${ELASTIC_APM_SERVER_URL:-https://apm-server.monitoring.svc:8200} -Delastic.apm.application_packages=com.chilicoders  -Delastic.apm.enable_log_correlation=true -Delastic.apm.transaction_sample_rate=\${ELASTIC_APM_SAMPLE_RATE:-0.1}\"" >> $CATALINA_HOME/bin/setenv.sh && \
    chmod +x $CATALINA_HOME/bin/setenv.sh

# Run Tomcat
ENV ELASTIC_APM_LOG_FORMAT_SOUT=JSON
ENV JAVA_TOOL_OPTIONS="-XX:MaxRAMPercentage=80"
CMD ["catalina.sh", "run"]
