FROM artifactory.internal.outpost24.com/docker/opibase:0.9.8 AS build
ARG GIT_BRANCH=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"

WORKDIR /app
RUN mkdir -p /app/test-outcome

# Cache Gradle dependencies and avoid re-downloading
COPY gradle ./gradle
COPY build.gradle gradlew ./
RUN --mount=type=cache,target=/root/.gradle/ \
    ./gradlew dependencies --no-daemon --build-cache
COPY config/log4j2.properties ./src/main/resources/log4j2.properties
COPY config ./config
COPY src ./src

RUN --mount=type=cache,target=/root/.gradle/ \
    ./gradlew build printDependencyVersionsUsed -x test -x check copyRuntimeLibs --no-daemon --build-cache --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}";

FROM build AS lint
ARG GIT_BRANCH=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"
ARG LINT_FAILED_FILENAME="lint_failed"

RUN --mount=type=cache,target=/root/.gradle/ \
    ./gradlew checkstyleMain checkstyleTest spotbugsMain spotbugsTest --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}" \
    || touch /app/test-outcome/${LINT_FAILED_FILENAME};

FROM scratch AS lintresults
COPY --from=lint /app/test-outcome/* /
COPY --from=lint /app/build/reports/checkstyle/* /checkstyle/
COPY --from=lint /app/build/reports/spotbugs/* /spotbugs/

FROM build AS test
ARG GIT_BRANCH=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"
ARG TEST_FAILED_FILENAME="test_failed"

# Start the test database, get the port, update the test properties file with the port, run the tests, stop the test database
RUN --mount=type=cache,target=/root/.gradle/ \
    /usr/bin/test-database.sh start && \
	port=$(cat /tmp/test-database-port) && \
	sed -i "s/127.0.0.1:[0-9]\+/127.0.0.1:${port}/" ./src/test/resources/application-test.properties && \
	./gradlew test --info --stacktrace \
	   -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}" || touch /app/test-outcome/${TEST_FAILED_FILENAME} && \
	/usr/bin/test-database.sh stop

FROM scratch AS testresults
COPY --from=test /app/test-outcome/* /
COPY --from=test /app/build/test-results/test/*.xml /unit-tests/
COPY --from=test /app/build/reports/jacoco/test/*.xml /coverage/
COPY --from=test /app/build/reports/jacoco/test/html/* /coverage/html

FROM build AS publishjar
ARG GIT_BRANCH=""
ARG ARTIFACTORY_USERNAME=""
ARG ARTIFACTORY_PASSWORD=""
ARG ARTIFACT_VERSION=""
ARG ARTIFACTORY_URL="https://artifactory.internal.outpost24.com/artifactory"

RUN --mount=type=cache,target=/root/.gradle/ \
    ./gradlew publish --info --stacktrace \
    -Partifactory.url="${ARTIFACTORY_URL}"/"${GIT_BRANCH}"_gradle -Partifact.version="${ARTIFACT_VERSION}" -Pgit.branch="${GIT_BRANCH}";

FROM gcr.io/distroless/java17-debian11 AS publish
ARG GIT_BRANCH=""
ARG GIT_URL=""
ARG GIT_COMMIT=""
ARG GIT_TAG=""
ARG ARTIFACT_VERSION=""

LABEL git-url=$GIT_URL
LABEL git-branch=$GIT_BRANCH
LABEL git-commit=$GIT_COMMIT
LABEL git-tag=$GIT_TAG
LABEL artifact-version=$ARTIFACT_VERSION
LABEL org.opencontainers.image.authors="outpost24.com"

COPY --from=build /app/build/libs/o24scandataimport.jar /scandataimport/o24scandataimport.jar
COPY --from=build /app/dependencies/* /scandataimport/
ENV ELASTIC_APM_LOG_FORMAT_SOUT=JSON
ENV JAVA_TOOL_OPTIONS="-XX:MaxRAMPercentage=80"
ENTRYPOINT ["java", "-Dspring.config.location=/application.properties", "-cp", "/scandataimport/*", "com.chilicoders.ScanDataImporterApplication"]
