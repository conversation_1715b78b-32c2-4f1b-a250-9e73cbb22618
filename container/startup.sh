#!/bin/bash

# Ensure the host source directory has been mounted to `/o24/xmlapi`.
if [ ! -d "/o24/xmlapi/src" ]; then
  echo "ERROR: Source tree not mounted to /o24/xmlapi."
  exit 1
fi

# Symlink sudoers file.
if ! ln -sf /o24/xmlapi/web/META-INF/sudoers.d/* /etc/sudoers.d; then
  echo "ERROR: Could not symlink sudoers.d file"
  exit 1
fi

# If no command has been provided, default to usage instructions.
if [ -z "$1" ]; then
  set "usage"
fi

function setup_symlinks {
  echo "Creating configuration symlinks in container filesystem..."
  (cd /o24/xmlapi/container/rootdir && find . ! -path . -type d -exec sh -c 'mkdir -p "${0:1}"' {} \;)
  (cd /o24/xmlapi/container/rootdir && find . -type f -exec sh -c 'echo "* ${0:1}" && rm -f "${0:1}" && ln -s "$(pwd)${0:1}" "${0:1}"' {} \;)
  (cd /o24/xmlapi/container/rootdir && find . -type l -exec sh -c 'echo "* ${0:1}" && rm -f "${0:1}" && ln -s "$(pwd)${0:1}" "${0:1}"' {} \;)
  echo "* Copying compliance polices to /etc/opi/compliance"
  (ln -sf /var/lib/tomcats/opi/webapps/opi/META-INF/compliance/ /etc/opi/ && cp /etc/opi/compliance/cloudsec/* /etc/opi/compliance/)
}

function xmlapi_build {
  if [ $# -eq 0 ]; then
    echo "Building with default parameters..."
    # building xmlapi and submodules
    /o24/xmlapi/gradlew -Pdevbuild=1 clean build jar -x test -x compileTestJava -x checkstyleMain -x checkstyleTest -x spotbugsMain -x spotbugsTest -x spotbugsTestFixtures

    # building xmlapi and reportservice (assuming reportservice is cloned inside xmlapi)
    (cd /o24/xmlapi/reportservice && /o24/xmlapi/reportservice/gradlew -Pdevbuild=1 clean build jar -x test -x compileTestJava -x checkstyleMain -x checkstyleTest -x spotbugsMain -x spotbugsTest -x spotbugsTestFixtures)
  else
    echo -e "Building with custom parameters...\nCustom parameters: '$*'\n"
    /o24/xmlapi/gradlew -Pdevbuild=1 clean build jar $*
  fi

  /o24/xmlapi/gradlew --stop
}

function xmlapi_test {
  if [ $# -eq 0 ]; then
    echo "Running full test suite..."
    /o24/xmlapi/gradlew -Pdevbuild=1 test
  else
    echo -e "Running customized test suite...\nCustom parameters: '$*'\n"
    /o24/xmlapi/gradlew -Pdevbuild=1 test $*
  fi

  /o24/xmlapi/gradlew --stop
}

function xmlapi_lint {
  echo "Running linting tasks..."
  /o24/xmlapi/gradlew -Pdevbuild=1 checkstyleMain checkstyleTest
  /o24/xmlapi/gradlew --stop
}

function xmlapi_spotbugs {
	echo "Running spotbugs tasks..."
	/o24/xmlapi/gradlew -Pdevbuild=1 --stacktrace --info clean spotbugsMain spotbugsTest
	/o24/xmlapi/gradlew --stop
}

function xmlapi_gradle {
  echo -e "Running custom gradle command...\nCustom parameters: '$*'\n"
  /o24/xmlapi/gradlew -Pdevbuild=1 $*
  /o24/xmlapi/gradlew --stop
}

function xmlapi_serve {
  if [ ! -f "/o24/xmlapi/build/libs/opi.war" ]; then
    echo "No .war bundle in build directory; please run 'build' first."
    exit 1
  fi

  echo "Copying 'build/libs/opi.war' to tomcat webapps directory..."
  rm -rf /opt/outpost24/tomcat/webapps && mkdir -p /opt/outpost24/tomcat/webapps && mkdir /opt/outpost24/tomcat/temp
  cp /o24/xmlapi/build/libs/opi.war /opt/outpost24/tomcat/webapps/

  setup_symlinks

  echo "Starting scanjob server..."
  exec scanjob server &

  echo "Starting tomcat server..."
  exec /opt/outpost24/tomcat/bin/catalina.sh run
}

function reportservice_serve {
  if [ ! -f "/o24/xmlapi/reportservice/build/libs/o24reportservice.jar" ]; then
    echo "No .jar file in build directory; please run 'build' first."
    exit 1
  fi

  setup_symlinks

  echo "Starting reporting microservice..."
  ELASTIC_APM_ENABLED=false /usr/bin/java -agentlib:jdwp=transport=dt_socket,address=8902,server=y,suspend=n -Duser.timezone=GMT -jar /o24/xmlapi/reportservice/build/libs/o24reportservice.jar --spring.config.location=/etc/o24reportservice/application.properties
}

function eventservice_serve {
  if [ ! -f "/o24/xmlapi/eventservice/build/libs/eventservice.jar" ]; then
    echo "No .jar file in build directory; please run 'build' first."
    exit 1
  fi

  setup_symlinks

  echo "Starting event service microservice..."
  ELASTIC_APM_ENABLED=false /usr/bin/java -agentlib:jdwp=transport=dt_socket,address=8903,server=y,suspend=n -Duser.timezone=GMT -jar /o24/xmlapi/eventservice/build/libs/eventservice.jar --spring.config.location=/etc/o24eventservice/application.properties
}

case $1 in
  build )
    shift # Remove the 'build' string from parameters.
    xmlapi_build $* # Pass remaining params (if any) to function.
    ;;
  test )
    shift
    xmlapi_test $*
    ;;
  gradle )
    shift
    xmlapi_gradle $*
    ;;
  spotbugs )
    xmlapi_spotbugs
    ;;
  lint )
    xmlapi_lint
    ;;
  serve )
    xmlapi_serve
    ;;
  reportservice )
    reportservice_serve
    ;;
  eventservice )
    eventservice_serve
    ;;
  usage|help )
    echo -e "Outpost24 XMLAPI development container. Issue one of the following commands:\n"
    echo "  test [FLAGS]    Run the gradle test task, optionally providing custom gradle flags."
    echo "  build [FLAGS]   Build the project, optionally providing custom gradle flags."
    echo "  gradle [FLAGS]  Gradle invocation passthrough."
    echo "  spotbugs        Run the gradle spotbugs tasks."
    echo "  lint            Run the gradle checkstyle tasks."
    echo "  serve           Copy .war and config files, serve with tomcat."
    echo "  reportservice   Run the reporting microservice."
    ;;
  * )
    echo "Unsupported command: '$1', see 'usage' for help."
    exit 1
esac
