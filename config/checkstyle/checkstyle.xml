<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN" "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: Outpost24 8.7
    Description: none
-->
<module name="Checker">
	<property name="severity" value="warning"/>
	<property name="charset" value="UTF-8"/>
	<property name="fileExtensions" value="java, properties, xml"/>
	<property name="tabWidth" value="2"/>
	<module name="LineLength">
		<property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
		<property name="fileExtensions" value="java"/>
		<property name="max" value="250"/>
	</module>
	<module name="TreeWalker">
		<property name="tabWidth" value="2"/>
		<module name="WhitespaceAround"/>
		<module name="OuterTypeFilename"/>
		<module name="IllegalTokenText">
			<property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
			<property name="format" value="\\u00(08|09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
			<property name="message" value="Avoid using corresponding octal or Unicode escape."/>
		</module>
		<module name="AvoidEscapedUnicodeCharacters">
			<property name="severity" value="ignore"/>
			<property name="allowEscapesForControlCharacters" value="true"/>
			<property name="allowByTailComment" value="true"/>
			<property name="allowNonPrintableEscapes" value="true"/>
			<metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
		</module>
		<module name="AvoidStarImport"/>
		<module name="UnusedImports"/>
		<module name="OneTopLevelClass"/>
		<module name="NoLineWrap"/>
		<module name="EmptyBlock">
			<property name="option" value="TEXT"/>
			<property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
		</module>
		<module name="NeedBraces"/>
		<module name="LeftCurly"/>
		<module name="RightCurly">
			<property name="option" value="alone"/>
			<property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, STATIC_INIT, INSTANCE_INIT"/>
		</module>
		<module name="OneStatementPerLine"/>
		<module name="MultipleVariableDeclarations"/>
		<module name="ArrayTypeStyle"/>
		<module name="MissingSwitchDefault"/>
		<module name="FallThrough"/>
		<module name="UpperEll"/>
		<module name="ModifierOrder"/>
		<module name="EmptyLineSeparator">
			<property name="allowNoEmptyLineBetweenFields" value="true"/>
			<property name="allowMultipleEmptyLines" value="false"/>
		</module>
		<module name="SeparatorWrap">
			<property name="option" value="nl"/>
			<property name="tokens" value="DOT"/>
		</module>
		<module name="SeparatorWrap">
			<property name="option" value="EOL"/>
			<property name="tokens" value="COMMA"/>
		</module>
		<module name="FinalLocalVariableCheck">
			<property name="validateEnhancedForLoopVariable" value="true"/>
		</module>
		<module name="PackageName">
			<property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
			<message key="name.invalidPattern" value="Package name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="TypeName">
			<message key="name.invalidPattern" value="Type name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="MemberName">
			<property name="format" value="^[a-z][a-z0-9_][a-zA-Z0-9_]*$"/>
			<message key="name.invalidPattern" value="Member name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="ParameterName">
			<property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
			<message key="name.invalidPattern" value="Parameter name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="LocalVariableName">
			<property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
			<property name="allowOneCharVarInForLoop" value="true"/>
			<property name="tokens" value="VARIABLE_DEF"/>
			<message key="name.invalidPattern" value="Local variable name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="ClassTypeParameterName">
			<property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
			<message key="name.invalidPattern" value="Class type name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="MethodTypeParameterName">
			<property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
			<message key="name.invalidPattern" value="Method type name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="InterfaceTypeParameterName">
			<property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
			<message key="name.invalidPattern" value="Interface type name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="NoFinalizer">
			<property name="severity" value="ignore"/>
			<metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
		</module>
		<module name="GenericWhitespace">
			<message key="ws.notPreceded" value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
			<message key="ws.followed" value="GenericWhitespace ''{0}'' is followed by whitespace."/>
			<message key="ws.preceded" value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
			<message key="ws.illegalFollow" value="GenericWhitespace ''{0}'' should followed by whitespace."/>
		</module>
		<module name="Indentation">
			<property name="basicOffset" value="2"/>
			<property name="caseIndent" value="2"/>
			<property name="arrayInitIndent" value="2"/>
		</module>
		<module name="AbbreviationAsWordInName">
			<property name="allowedAbbreviationLength" value="6"/>
			<property name="ignoreFinal" value="false"/>
		</module>
		<module name="OverloadMethodsDeclarationOrder"/>
		<module name="VariableDeclarationUsageDistance"/>
		<module name="MethodParamPad"/>
		<module name="OperatorWrap">
			<property name="tokens" value="BAND,BOR,BSR,BXOR,DIV,EQUAL,GE,GT,LE,LITERAL_INSTANCEOF,LT,MINUS,MOD,NOT_EQUAL,QUESTION,SL,SR,STAR"/>
		</module>
		<module name="AnnotationLocation">
			<property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
		</module>
		<module name="AnnotationLocation">
			<property name="tokens" value="VARIABLE_DEF"/>
			<property name="allowSamelineMultipleAnnotations" value="true"/>
		</module>
		<module name="NonEmptyAtclauseDescription"/>
		<module name="JavadocTagContinuationIndentation">
			<property name="offset" value="2"/>
		</module>
		<module name="SummaryJavadoc">
			<property name="severity" value="ignore"/>
			<property name="forbiddenSummaryFragments" value="^@return the *|^This method returns |^A [{]@code [a-zA-Z0-9]+[}]( is a )"/>
			<metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
		</module>
		<module name="JavadocParagraph"/>
		<module name="AtclauseOrder">
			<property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
			<property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
		</module>
		<module name="JavadocMethod">
			<property name="allowedAnnotations" value="Override,Test"/>
		</module>
		<module name="MissingJavadocMethod">
			<property name="minLineCount" value="2"/>
			<property name="allowedAnnotations" value="Override,Test"/>
			<property name="scope" value="private"/>
			<property name="allowMissingPropertyJavadoc" value="true"/>
		</module>
		<module name="MethodName">
			<property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9_]*$"/>
			<message key="name.invalidPattern" value="Method name ''{0}'' must match pattern ''{1}''."/>
		</module>
		<module name="SingleLineJavadoc">
			<property name="ignoreInlineTags" value="false"/>
		</module>
		<module name="EmptyCatchBlock">
			<property name="exceptionVariableName" value="expected"/>
		</module>
		<module name="CommentsIndentation"/>
		<module name="FinalParameters">
			<property name="tokens" value="METHOD_DEF,CTOR_DEF,LITERAL_CATCH,FOR_EACH_CLAUSE"/>
		</module>
	</module>
	<module name="SuppressionFilter">
		<property name="file" value="${config_loc}/suppress.xml"/>
	</module>
</module>
