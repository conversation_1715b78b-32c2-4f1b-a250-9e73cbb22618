%global _hardened_build 1
%define debug_package %{nil}
%{!?_outputdir:%global _outputdir %{_tmppath}}
%global _build_date %(date '+%%y%%m%%d%%H%%M%%S')
%global _build_version %(/usr/bin/git describe --tags --always --dirty | sed -e 's|^v||' -e 's|-.*||')
%global _build_release %(/usr/bin/git describe --tags --always --dirty | sed -E -e 's|^v*[0-9.]*$|1|' -e 's|^[^-]*-||' -e 's|-|.|g' -e 's|([0-9]+.g[0-9a-f]{7,})|%{_build_date}.\\1|')%{?dist}%{?_suffix}

%global _postgres_version 14
%global _dblayer_version_low 1:2.31
%global _dblayer_version_high 1:3.0
%global _core_version_low 1:6.15
%global _core_version_high 1:7.0
%global _eventservice_version_low 12.30
%global _eventservice_version_high 13.0
%global _reportservice_version_low 1:5.9
%global _reportservice_version_high 1:6.0
%global _schedulinglib_version_low 1:5.5
%global _schedulinglib_version_high 1:6.0
%global _custom_package_version_low 1.9
%global _custom_package_version_high 2.0
%global _configuration_outscan_version_low 1.23
%global _configuration_outscan_version_high 1.24
%global _libellum_version_low 1.8.4
%global _libellum_version_high 2.0
%global _assetservice_version_low 2.0
%global _assetservice_version_high 3.0
%global _cloud_discovery_version_low 0.0.6
%global _cloud_discovery_version_high 1.0.0

%{?_selinux_policy_version:%global selinux_policyver %{_selinux_policy_version}}

%define __jar_repack 0

# Turn off the brp-python-bytecompile script
%global __os_install_post %(echo '%{__os_install_post}' | sed -e 's!/usr/lib[^[:space:]]*/brp-python-bytecompile[[:space:]].*$!!g')

Name: xmlapi
Version: %{_build_version}
Release: %{_build_release}
Summary: Main xmlapi

Vendor: Outpost24 AB
URL: https://www.outpost24.com/
License: Outpost24 License
Source0: xmlapi.tar.gz

%description
XMLAPI package

%prep
%setup -q

%build
chmod 777 /tmp
find ./web/META-INF/sudoers.d/ -type f -print0 | xargs -0 -n1 visudo -csf || FAIL=$?
checkmodule -M -m outpost24-xmlapi.te || FAIL=$?
make -f /usr/share/selinux/devel/Makefile o24opi.te clean all || FAIL=$?
make -f %{_datadir}/selinux/devel/Makefile clean all || FAIL=$?

if [ -x /usr/bin/test-database.sh ]; then
	function startDatabase() {
		PSQL_VERSION=%{_postgres_version} /usr/bin/test-database.sh start
		port=$(cat /tmp/test-database-port)
		echo -e "\njdbc.url=***************************:${port}/opsspace_junit\n1_jdbc.url=***************************:${port}/opsspace_junit\ntruststore=$(pwd)/web/WEB-INF/ssl/server.keystore\nclient.certificate=$(pwd)/web/WEB-INF/ssl/client.p12\n" > ./src/test/resources/application-test.properties
	}
	function stopDatabase() {
		PSQL_VERSION=%{_postgres_version} /usr/bin/test-database.sh stop
		rm ./src/test/resources/application-test.properties
	}
	trap stopDatabase EXIT
	startDatabase
fi

./gradlew --no-daemon --stacktrace --info -Partifact.version=%{_build_version}.%{_build_release} $GRADLE_FLAGS build || FAIL=$?

if [ -d build/test-results/test ]; then
	mkdir -p %{_outputdir}/junit
	cp build/test-results/test/*.xml %{_outputdir}/junit
fi
if [ -d build/reports/tests/test ]; then
	mv build/reports/tests/test %{_outputdir}/junit
fi
if [ -d build/reports/clover-root ]; then
	mv build/reports/clover-root/clover.xml %{_outputdir}/clover.xml
	mv build/reports/clover-root/html %{_outputdir}/coverage
fi
if [ -d build/reports/checkstyle ]; then
	mv build/reports/checkstyle %{_outputdir}/checkstyle
fi
if [ -d build/reports/spotbugs ]; then
	mv build/reports/spotbugs %{_outputdir}/spotbugs
fi

exit $FAIL

%install
mkdir -p \
	%{buildroot}/etc/opi.blacklist.d \
	%{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib \
	%{buildroot}/etc/logrotate.d \
	%{buildroot}%{_unitdir} \
	%{buildroot}/var/www/html \
	%{buildroot}/etc/opi \
	%{buildroot}/etc/sysconfig \
	%{buildroot}/etc/sudoers.d \
	%{buildroot}/etc/cron.daily \
	%{buildroot}/etc/cron.d \
	%{buildroot}/etc/systemd/system/opi.service.d \
	%{buildroot}/usr/share/selinux/packages \
	%{buildroot}/etc/nginx

cp -R ./web/WEB-INF %{buildroot}/var/lib/tomcats/opi/webapps/opi/
cp -R ./web/META-INF %{buildroot}/var/lib/tomcats/opi/webapps/opi/
mv ./build/dependencies/*.jar %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/
mv ./build/libs/o24xml.jar %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/nginx-maintenance.conf %{buildroot}/etc/nginx/
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/opi %{buildroot}/etc/logrotate.d/opi
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/opi.service %{buildroot}%{_unitdir}/opi.service
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/nginx-maintenance.service %{buildroot}%{_unitdir}/nginx-maintenance.service
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/tomcat.sysconfig %{buildroot}/etc/sysconfig/tomcat@opi
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/sudoers.d/opi-hiab %{buildroot}/etc/sudoers.d/opi-hiab
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/sudoers.d/opi-libellum %{buildroot}/etc/sudoers.d/opi-libellum
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/sudoers.d/opi-update-enrollment %{buildroot}/etc/sudoers.d/opi-update-enrollment
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/sudoers.d/opi-scanjob %{buildroot}/etc/sudoers.d/opi-scanjob
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/update-check %{buildroot}/etc/cron.d/update-check
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/remove_old_scans %{buildroot}/etc/cron.daily/remove_old_scans
mv %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/files/resize_memory_hiab.conf %{buildroot}/etc/systemd/system/opi.service.d/resize_memory_hiab.conf
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/createDebugReport.sh
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabpwddev.sh
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24dblayer.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24core.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24core-test-fixtures.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24reportexport.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24schedulinglib.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/event-service.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24scandataimport.jar
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24rule*
rm -rf %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/classes/*

echo TAG=%{_build_version}.%{_build_release} > %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/TAG

find %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib -name '*.jar' | grep -v o24dblayer | grep -v o24apibase | grep -v o24core | grep -v o24reportexport | grep -v o24scheduling | grep -v event-service | grep -v o24xml | grep -v o24rule | sed "s/$(echo %{buildroot} | sed 's/\//\\\//g')//" >> dependency-files
APM_JAR=$(find %{buildroot}/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib -name 'elastic-apm-agent-*.jar' -exec basename {} \;)
sed -i "s|elastic-apm-agent.jar|${APM_JAR}|" %{buildroot}/etc/sysconfig/tomcat@opi


cp -R ./web/* %{buildroot}/var/www/html/
rm -rf %{buildroot}/var/www/html/WEB-INF
rm -rf %{buildroot}/var/www/html/META-INF
rm -rf %{buildroot}/var/www/html/context.xml
rm -rf %{buildroot}/var/www/html/server.xml
rm -rf %{buildroot}/var/www/html/redisson.conf
touch %{buildroot}/etc/opi/hiabs.crl

cp *.pp %{buildroot}/usr/share/selinux/packages/
install -D outpost24-xmlapi.pp %{buildroot}%{_datadir}/selinux/packages/outpost24-xmlapi.pp
install -D outpost24-xmlapi.if %{buildroot}%{_datadir}/selinux/devel/include/contrib/outpost24-xmlapi.if

%postun
if [ $1 -eq 0 ]; then
	semodule -n -r probe
	semodule -n -r tomcatbind
	semodule -n -r readhtml
	semodule -n -r allowgpg
	semodule -n -r nisenabled
	semodule -n -r o24sudo
	semodule -n -r o24ifconfig
fi

%package attacker
Summary: xmlapi-attacker
Provides: xmlapi = %{_build_version}
Conflicts: core-scripts-osat < 2.189
Conflicts: xmlapi-hiab
Conflicts: xmlapi-frontend
Conflicts: xmlapi-backend
Conflicts: xmlapi-admin
Requires: o24-tomcat
Requires: haveged
Requires: postfix
Requires: sudo
Requires: createrepo
Requires: libpcap
Requires: postgresql >= %{_postgres_version}
Requires: postgresql-server >= %{_postgres_version}
Requires: policycoreutils-python
Requires: libellum >= %{_libellum_version_low}
Requires: libellum < %{_libellum_version_high}
Requires: clavem
Requires: scanjob >= 1.6, scanjob < 2.0
Requires: wasx-scan
Requires: teddy-salad-prepare
Requires: core-engine >= 1:4.0 core-engine < 1:6.0
Requires: cloudsec >= 1.11, cloudsec < 2.0
Requires: configuration-outscan-attacker >= %{_configuration_outscan_version_low}
Requires: configuration-outscan-attacker < %{_configuration_outscan_version_high}
Requires: xmlapi-scripts = %{_build_version}-%{_build_release}
Requires: xmlapi-dependencies = %{_build_version}-%{_build_release}
Requires: o24dblayer >= %{_dblayer_version_low}
Requires: o24dblayer < %{_dblayer_version_high}
Requires: o24core >= %{_core_version_low}
Requires: o24core < %{_core_version_high}
Requires: o24schedulinglib >= %{_schedulinglib_version_low}
Requires: o24schedulinglib < %{_schedulinglib_version_high}
Requires: outscan-custom >= %{_custom_package_version_low}
Requires: outscan-custom < %{_custom_package_version_high}
Requires: o24ruleengine
Requires: o24scandataimport
Requires: cloud-discovery >= %{_cloud_discovery_version_low}
Requires: cloud-discovery < %{_cloud_discovery_version_high}
Requires(post): scanjob >= 1.6, scanjob < 2.0
Requires(post): libellum
Obsoletes: o24schedulingservice <= 2.0
BuildRequires: vi
BuildRequires: libpcap
BuildRequires: openssl
BuildRequires: unzip
BuildRequires: java-1.8.0-openjdk-devel
BuildRequires: selinux-policy-devel
BuildRequires: sudo
BuildRequires: nmap-ncat
BuildRequires: postgresql%{_postgres_version}-server
BuildRequires: postgresql%{_postgres_version}-contrib
BuildRequires: libellum >= %{_libellum_version_low}
BuildRequires: libellum < %{_libellum_version_high}
BuildRequires: o24dblayer >= %{_dblayer_version_low}
BuildRequires: o24dblayer < %{_dblayer_version_high}
BuildRequires: o24core >= %{_core_version_low}
BuildRequires: o24core < %{_core_version_high}
BuildRequires: o24schedulinglib >= %{_schedulinglib_version_low}
BuildRequires: o24schedulinglib < %{_schedulinglib_version_high}
BuildRequires: o24eventservice >= %{_eventservice_version_low}
BuildRequires: o24eventservice < %{_eventservice_version_high}
BuildRequires: o24ruleengine-testfixture o24ruleengine o24scandataimport

%description attacker
Plain framework to be used on attackers.

%files attacker -f common-files
/etc/sysconfig/tomcat@opi
/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/exportrules.sh
%attr(755, root, root) /etc/cron.daily/remove_old_scans

%post attacker
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpminstall.sh $1

%postun attacker
if [ $1 -eq 0 ]; then
	semodule -n -r probe
fi

%posttrans attacker
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/private/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/private/

%package backend
Summary: xmlapi-backend
Provides: xmlapi = %{_build_version}
Conflicts: xmlapi-hiab
Conflicts: xmlapi-frontend
Conflicts: xmlapi-attacker
Conflicts: xmlapi-admin
Requires: o24-tomcat
Requires: haveged
Requires: postfix
Requires: sudo
Requires: createrepo
Requires: libpcap
Requires: postgresql%{_postgres_version}
Requires: policycoreutils-python
Requires: libellum >= %{_libellum_version_low}
Requires: libellum < %{_libellum_version_high}
Requires: clavem
Requires: configuration-outscan-osbe >= %{_configuration_outscan_version_low}
Requires: configuration-outscan-osbe < %{_configuration_outscan_version_high}
Requires: xmlapi-scripts = %{_build_version}-%{_build_release}
Requires: xmlapi-dependencies = %{_build_version}-%{_build_release}
Requires: o24dblayer >= %{_dblayer_version_low}
Requires: o24dblayer < %{_dblayer_version_high}
Requires: o24core >= %{_core_version_low}
Requires: o24core < %{_core_version_high}
Requires: o24schedulinglib >= %{_schedulinglib_version_low}
Requires: o24schedulinglib < %{_schedulinglib_version_high}
Requires: outscan-custom >= %{_custom_package_version_low}
Requires: outscan-custom < %{_custom_package_version_high}
Requires: o24ruleengine
Requires: o24scandataimport
Requires(post): libellum
Obsoletes: o24schedulingservice <= 2.0
BuildRequires: libpcap
BuildRequires: openssl
BuildRequires: unzip
BuildRequires: java-1.8.0-openjdk-devel
BuildRequires: selinux-policy-devel
BuildRequires: sudo
BuildRequires: nmap-ncat
BuildRequires: postgresql%{_postgres_version}-server
BuildRequires: postgresql%{_postgres_version}-contrib
BuildRequires: libellum >= %{_libellum_version_low}
BuildRequires: libellum < %{_libellum_version_high}
BuildRequires: o24dblayer >= %{_dblayer_version_low}
BuildRequires: o24dblayer < %{_dblayer_version_high}
BuildRequires: o24core >= %{_core_version_low}
BuildRequires: o24core < %{_core_version_high}
BuildRequires: o24schedulinglib >= %{_schedulinglib_version_low}
BuildRequires: o24schedulinglib < %{_schedulinglib_version_high}
BuildRequires: o24eventservice >= %{_eventservice_version_low}
BuildRequires: o24eventservice < %{_eventservice_version_high}

%description backend
Plain framework to be used on backend.

%files backend -f common-files
/etc/sysconfig/tomcat@opi
/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/exportrules.sh

%post backend
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpminstall.sh $1

%postun backend
if [ $1 -eq 0 ]; then
	semodule -n -r probe
fi

%posttrans backend
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/private/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/private/

%package admin
Summary: xmlapi-admin
Provides: xmlapi = %{_build_version}
Conflicts: xmlapi-hiab
Conflicts: xmlapi-frontend
Conflicts: xmlapi-backend
Conflicts: xmlapi-attacker
Requires: o24-tomcat
Requires: haveged
Requires: postfix
Requires: sudo
Requires: createrepo
Requires: libpcap
Requires: postgresql%{_postgres_version}
Requires: policycoreutils-python
Requires: nginx
Requires: python3-pyAesCrypt
Requires: compliance
Requires: monty-scripts-ssh-commands-datasets
Requires: portal-ui
Requires: admin-ui
Requires: libellum >= %{_libellum_version_low}
Requires: libellum < %{_libellum_version_high}
Requires: clavem
Requires: ui-admin
Requires: xmlapi-scripts = %{_build_version}-%{_build_release}
Requires: xmlapi-dependencies = %{_build_version}-%{_build_release}
# monty-script datasets needed for agent
Requires: monty-scripts-psh-commands-datasets
Requires: monty-scripts-remote-registry-datasets
Requires: configuration-outscan-osfa >= %{_configuration_outscan_version_low}
Requires: configuration-outscan-osfa < %{_configuration_outscan_version_high}
Requires: o24dblayer >= %{_dblayer_version_low}
Requires: o24dblayer < %{_dblayer_version_high}
Requires: o24core >= %{_core_version_low}
Requires: o24core < %{_core_version_high}
Requires: o24schedulinglib >= %{_schedulinglib_version_low}
Requires: o24schedulinglib < %{_schedulinglib_version_high}
Requires: outscan-custom >= %{_custom_package_version_low}
Requires: outscan-custom < %{_custom_package_version_high}
Requires: o24ruleengine
Requires: o24scandataimport
Requires(post): libellum
Obsoletes: o24schedulingservice <= 2.0
BuildRequires: libpcap
BuildRequires: openssl
BuildRequires: unzip
BuildRequires: java-1.8.0-openjdk-devel
BuildRequires: selinux-policy-devel
BuildRequires: sudo
BuildRequires: nmap-ncat
BuildRequires: postgresql%{_postgres_version}-server
BuildRequires: postgresql%{_postgres_version}-contrib
BuildRequires: libellum >= %{_libellum_version_low}
BuildRequires: libellum < %{_libellum_version_high}
BuildRequires: o24dblayer >= %{_dblayer_version_low}
BuildRequires: o24dblayer < %{_dblayer_version_high}
BuildRequires: o24core >= %{_core_version_low}
BuildRequires: o24core < %{_core_version_high}
BuildRequires: o24schedulinglib >= %{_schedulinglib_version_low}
BuildRequires: o24schedulinglib < %{_schedulinglib_version_high}
BuildRequires: o24eventservice >= %{_eventservice_version_low}
BuildRequires: o24eventservice < %{_eventservice_version_high}

%description admin
Plain framework to be used on admin.

%files admin -f common-files
/etc/sysconfig/tomcat@opi
%config(noreplace) %attr(660, root, tomcat) /etc/opi/hiabs.crl
/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/exportrules.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/installTranslations.sh

%post admin
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpminstall.sh $1

%postun admin
if [ $1 -eq 0 ]; then
	semodule -n -r probe
fi

%posttrans admin
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/private/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/private/

%package frontend
Summary: xmlapi-frontend
Provides: xmlapi = %{_build_version}
Conflicts: xmlapi-hiab
Conflicts: xmlapi-attacker
Conflicts: xmlapi-backend
Conflicts: xmlapi-admin
Requires: o24-tomcat
Requires: haveged
Requires: postfix
Requires: sudo
Requires: createrepo
Requires: libpcap
Requires: postgresql%{_postgres_version}
Requires: policycoreutils-python
Requires: yum-utils
Requires: nginx
Requires: compliance
Requires: monty-scripts-ssh-commands-datasets
Requires: portal-ui
Requires: libellum >= %{_libellum_version_low}
Requires: libellum < %{_libellum_version_high}
Requires: clavem
Requires: ui-outscan
Requires: xmlapi-scripts = %{_build_version}-%{_build_release}
Requires: xmlapi-dependencies = %{_build_version}-%{_build_release}
Requires: configuration-outscan-osfe >= %{_configuration_outscan_version_low}
Requires: configuration-outscan-osfe < %{_configuration_outscan_version_high}
Requires: o24dblayer >= %{_dblayer_version_low}
Requires: o24dblayer < %{_dblayer_version_high}
Requires: o24core >= %{_core_version_low}
Requires: o24core < %{_core_version_high}
Requires: o24schedulinglib >= %{_schedulinglib_version_low}
Requires: o24schedulinglib < %{_schedulinglib_version_high}
Requires: outscan-custom >= %{_custom_package_version_low}
Requires: outscan-custom < %{_custom_package_version_high}
Requires: o24scandataimport
Requires: o24ruleengine
Requires(post): libellum
Obsoletes: o24schedulingservice <= 2.0
BuildRequires: libpcap
BuildRequires: openssl
BuildRequires: unzip
BuildRequires: java-1.8.0-openjdk-devel
BuildRequires: selinux-policy-devel
BuildRequires: sudo
BuildRequires: nmap-ncat
BuildRequires: postgresql%{_postgres_version}-server
BuildRequires: postgresql%{_postgres_version}-contrib
BuildRequires: libellum >= %{_libellum_version_low}
BuildRequires: libellum < %{_libellum_version_high}
BuildRequires: o24dblayer >= %{_dblayer_version_low}
BuildRequires: o24dblayer < %{_dblayer_version_high}
BuildRequires: o24core >= %{_core_version_low}
BuildRequires: o24core < %{_core_version_high}
BuildRequires: o24schedulinglib >= %{_schedulinglib_version_low}
BuildRequires: o24schedulinglib < %{_schedulinglib_version_high}
BuildRequires: o24eventservice >= %{_eventservice_version_low}
BuildRequires: o24eventservice < %{_eventservice_version_high}

%description frontend
Plain framework to be used on frontend.

%files frontend -f common-files
/etc/sysconfig/tomcat@opi
%config(noreplace) %attr(660, root, tomcat) /etc/opi/hiabs.crl
/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/exportrules.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/installTranslations.sh
%attr(0440, root, root) /etc/sudoers.d/opi-update-enrollment

%post frontend
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpminstall.sh $1

%postun frontend
if [ $1 -eq 0 ]; then
	semodule -n -r probe
fi

%posttrans frontend
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -Rm user:tomcat:r-x,m:rwx /etc/pki/o24/private/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/certs/
setfacl -dRm user:tomcat:r-x,m:rwx /etc/pki/o24/private/

%package hiab
Summary: xmlapi-hiab
Provides: xmlapi = %{_build_version}
Conflicts: xmlapi-backend
Conflicts: xmlapi-frontend
Conflicts: xmlapi-attacker
Conflicts: xmlapi-admin
Conflicts: core-scripts-hiab < 2.189
Requires: o24-tomcat
Requires: haveged
Requires: postfix
Requires: sudo
Requires: createrepo
Requires: libpcap
Requires: postgresql%{_postgres_version}
Requires: postgresql%{_postgres_version}-server postgresql%{_postgres_version}-contrib
Requires: policycoreutils-python
Requires: cronie
Requires: PyYAML
Requires: compliance
Requires: scanjob
Requires: wasx-scan
Requires: libellum >= %{_libellum_version_low}
Requires: libellum < %{_libellum_version_high}
Requires: asset-service-cli >= %{_assetservice_version_low}
Requires: asset-service-cli < %{_assetservice_version_high}
Requires: portal-ui
Requires: teddy-salad-prepare
Requires: teddy-salad-scan
# monty-script datasets needed for agent
Requires: monty-scripts-psh-commands-datasets
Requires: monty-scripts-remote-registry-datasets
Requires: monty-scripts-ssh-commands-datasets
Requires: core-scripts-hiab
Requires: configuration-hiab >= 1.45
Requires: configuration-hiab < 1.46
Requires: core-engine >= 1:4.0
Requires: core-engine < 1:6.0
Requires: hiabconfig-console >= 1.17-1.0
Requires: xmlapi-hiab-scripts = %{_build_version}-%{_build_release}
Requires: xmlapi-dependencies = %{_build_version}-%{_build_release}
Requires: o24database
Requires: o24dblayer >= %{_dblayer_version_low}
Requires: o24dblayer < %{_dblayer_version_high}
Requires: o24core >= %{_core_version_low}
Requires: o24core < %{_core_version_high}
Requires: o24reportservice-hiab >= %{_reportservice_version_low}
Requires: o24reportservice-hiab < %{_reportservice_version_high}
Requires: o24reportservice-selinux >= %{_reportservice_version_low}
Requires: o24reportservice-selinux < %{_reportservice_version_high}
Requires: o24schedulinglib >= %{_schedulinglib_version_low}
Requires: o24schedulinglib < %{_schedulinglib_version_high}
Requires: hiab-custom >= %{_custom_package_version_low}
Requires: hiab-custom < %{_custom_package_version_high}
Requires: o24ruleengine
Requires: o24scandataimport
Requires: cloud-discovery >= %{_cloud_discovery_version_low}
Requires: cloud-discovery < %{_cloud_discovery_version_high}
#Requires: kafka zookeeper Commented out until we actually need it to be installed, i.e. when eventservice is released to HIABs
Requires(post): scanjob
Requires(post): libellum
Requires(post): asset-service-cli
Requires(post): o24database
Requires(post): postgresql%{_postgres_version}
Requires(post): postgresql%{_postgres_version}-server
Requires(post): postgresql%{_postgres_version}-contrib
Obsoletes: o24schedulingservice <= 2.0
BuildRequires: libpcap
BuildRequires: openssl
BuildRequires: unzip
BuildRequires: java-1.8.0-openjdk-devel
BuildRequires: selinux-policy-devel
BuildRequires: sudo
BuildRequires: nmap-ncat
BuildRequires: postgresql%{_postgres_version}-server
BuildRequires: postgresql%{_postgres_version}-contrib
BuildRequires: libellum >= %{_libellum_version_low}
BuildRequires: libellum < %{_libellum_version_high}
BuildRequires: o24dblayer >= %{_dblayer_version_low}
BuildRequires: o24dblayer < %{_dblayer_version_high}
BuildRequires: o24core >= %{_core_version_low}
BuildRequires: o24core < %{_core_version_high}
BuildRequires: o24schedulinglib >= %{_schedulinglib_version_low}
BuildRequires: o24schedulinglib < %{_schedulinglib_version_high}
BuildRequires: o24eventservice >= %{_eventservice_version_low}
BuildRequires: o24eventservice < %{_eventservice_version_high}

%description hiab
Plain framework to be used on hiabs.

%files hiab -f common-files
%attr(0440, root, root) /etc/sudoers.d/opi-hiab
%attr(644, root, root) /etc/cron.d/update-check
%attr(755, root, root) /etc/cron.daily/remove_old_scans
/etc/sysconfig/tomcat@opi
%attr(644,root,root) /etc/systemd/system/opi.service.d/resize_memory_hiab.conf
/var/lib/tomcats/opi/webapps/opi/WEB-INF/lib/o24xml.jar
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/restartrequired.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/psql14.sh

%post hiab
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpminstall.sh $1
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/psql14.sh
touch /etc/opi/hiabsetup.properties
chmod 644 /etc/opi/hiabsetup.properties
chown tomcat:tomcat /etc/opi/hiabsetup.properties

perl -p -i -e "s/Xmx\d+m/Xmx$(free -m | awk '/Mem:/ {print int($2/2)}')m/" /etc/sysconfig/tomcat\@opi
mkdir -p /etc/opi.properties.d/
chown tomcat:tomcat /etc/opi.properties.d/
if [ -f /etc/custom.properties ]; then
	mv /etc/custom.properties /etc/opi.properties.d/20-secret.properties
fi
systemctl enable crond
systemctl start crond

%posttrans hiab
setfacl -Rm user:tomcat:rwx,m:rwx /etc/pki/tls/certs/
setfacl -Rm user:tomcat:rwx,m:rwx /etc/pki/tls/private/
setfacl -Rm user:tomcat:rwx,m:rwx /etc/pki/o24/certs/
setfacl -Rm user:tomcat:rwx,m:rwx /etc/pki/o24/private/
setfacl -dRm user:tomcat:rwx,m:rwx /etc/pki/o24/certs/
setfacl -dRm user:tomcat:rwx,m:rwx /etc/pki/o24/private/

%package scripts
Summary: xmlapi-scripts
Conflicts: xmlapi-hiab-scripts
Requires: xmlapi = %{_build_version}-%{_build_release}
Requires: python3
Requires: python3-lxml
Requires: python3-urllib3

%description scripts
Scripts for outscan

%files scripts
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/createdb.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/dumpSchema.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/getVersion.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabpackage_centos.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiab_offline_packages_needed.py
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/info.sh
%attr(755, root, root) /var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpm-tidy
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/ruleengine.sh

%package hiab-scripts
Summary: xmlapi-hiab-scripts
Conflicts: xmlapi-scripts
Requires: xmlapi = %{_build_version}-%{_build_release}

%description hiab-scripts
Scripts for hiab

%files hiab-scripts
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/checkupdate.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/createdb.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/dumpData.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/dumpSchema.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/getVersion.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiablogs.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabpackage_centos.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/test-repo-connectivity.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/exportrules.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabenroll_centos.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabpwd.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiabupdate_centos.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/impData.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/impData.sql
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/info.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/maintenancemode.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/scheduleronly.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/scanner_offline_packages_needed.py
%attr(755, root, root) /var/lib/tomcats/opi/webapps/opi/META-INF/scripts/rpm-tidy
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/ruleengine.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/skybox/outpost24.pl
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiab_list_packages.py
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/fetch_rpm_db.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/pod-scan.sh
/var/lib/tomcats/opi/webapps/opi/META-INF/scripts/hiab_resize_memory_upon_start.sh

%package dependencies
Summary: xmlapi-dependencies
Requires: xmlapi-selinux = %{_build_version}-%{_build_release}

Obsoletes: o24reportexport < 1:5.2.0

%description dependencies
All libraries needed by xmlapi.

%files dependencies -f dependency-files

%package selinux
Summary:	Selinux policy for xmlapi
Requires: selinux-policy-base >= %{selinux_policyver}
Requires: policycoreutils
Requires: libselinux-utils
Requires(post): selinux-policy-base >= %{selinux_policyver}
Requires(post): policycoreutils
Requires(post): libselinux-utils
Requires(postun): selinux-policy-base >= %{selinux_policyver}
Requires(postun): policycoreutils
Requires(postun): libselinux-utils
BuildRequires: selinux-policy-base >= %{selinux_policyver}
BuildRequires: selinux-policy-devel
BuildRequires: checkpolicy

%description selinux
This package provides an selinux policy for xmlapi.

%files selinux
%attr(0600,root,root) %{_datadir}/selinux/packages/outpost24-xmlapi.pp
%attr(0600,root,root) %{_datadir}/selinux/devel/include/contrib/outpost24-xmlapi.if

%post selinux
if semodule --list-modules=full | grep outpost24-xmlapi; then
	semodule -n -r outpost24-xmlapi
fi
semodule -n -i %{_datadir}/selinux/packages/outpost24-xmlapi.pp
if /usr/sbin/selinuxenabled ; then
    /usr/sbin/load_policy
fi

%postun selinux
if [ $1 -eq 0 ]; then
	semodule -n -r outpost24-xmlapi
	if /usr/sbin/selinuxenabled ; then
		/usr/sbin/load_policy
	fi
fi
